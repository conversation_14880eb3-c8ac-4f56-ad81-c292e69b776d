{"name": "postcss-normalize-charset", "version": "4.0.1", "description": "Add necessary or remove extra charset with PostCSS", "keywords": ["postcss", "css", "postcss-plugin", "charset"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "files": ["dist"], "license": "MIT", "repository": "cssnano/cssnano", "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "homepage": "https://github.com/cssnano/cssnano", "dependencies": {"postcss": "^7.0.0"}, "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0", "postcss-devtools": "^1.0.0"}, "main": "dist/index.js", "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "engines": {"node": ">=6.9.0"}}