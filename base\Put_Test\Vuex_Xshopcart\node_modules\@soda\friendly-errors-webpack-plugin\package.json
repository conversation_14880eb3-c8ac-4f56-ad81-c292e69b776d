{"name": "@soda/friendly-errors-webpack-plugin", "version": "1.7.1", "description": "Recognizes certain classes of webpack errors and cleans, aggregates and prioritizes them to provide a better Developer Experience", "main": "index.js", "scripts": {"test": "eslint --ignore-pattern test/* && jest"}, "files": ["src", "index.js"], "keywords": ["friendly", "errors", "webpack", "plugin"], "author": "<PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/sodatea/friendly-errors-webpack-plugin.git"}, "bugs": {"url": "https://github.com/sodatea/friendly-errors-webpack-plugin/issues"}, "license": "MIT", "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0 || ^4.0.0"}, "devDependencies": {"babel-core": "^6.23.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.3.0", "babel-plugin-transform-async-to-generator": "^6.22.0", "babel-preset-react": "^6.23.0", "eslint": "^3.16.1", "eslint-loader": "^1.6.1", "expect": "^1.20.2", "jest": "^18.1.0", "memory-fs": "^0.4.1", "webpack": "^2.2.1"}, "dependencies": {"chalk": "^1.1.3", "error-stack-parser": "^2.0.0", "string-width": "^2.0.0"}}