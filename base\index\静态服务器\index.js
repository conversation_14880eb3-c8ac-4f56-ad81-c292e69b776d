const http = require('http')
const fs = require('fs')
const path = require('path')

let server = http.createServer()

server.on('request', (request, response)=>{
    let url = request.url
    if(url === '/'){
        url = '/login.html'
    }
    let fullPath = path.join(__dirname, 'www', url)
    try {
        let res = fs.readFileSync(fullPath)
        response.end(res)
    } catch(err) {
        response.statusCode = 404
        response.end('404 not found!')
    }
})

server.listen(8995, ()=>{
    console.log('服务器已开启，端口号8995===>>>>', __dirname)
})