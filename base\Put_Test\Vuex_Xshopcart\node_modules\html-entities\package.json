{"name": "html-entities", "version": "1.3.1", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.2.11", "@types/mocha": "^7.0.2", "@types/node": "^13.11.1", "chai": "^1.9.1", "coveralls": "^2.11.2", "entities": "*", "mocha": "^1.21.4", "node-html-encoder": "*", "ts-node": "^8.8.2", "typescript": "^3.8.3"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index", "types": "./lib/index", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "benchmark": "ts-node benchmark/benchmark", "travis": "yarn test", "build": "tsc", "prepublishOnly": "yarn build"}, "files": ["index.js", "lib", "LICENSE"], "license": "MIT"}