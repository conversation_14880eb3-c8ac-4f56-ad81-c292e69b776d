<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <style>
     .name{
        color:red;
        padding: 10px 5px;
      }
    </style>
</head>

<body>
    <div id="app">
       <!-- <div class="name" v-for="(item,index) in list" :key="index" >
            <span>{{item.a}} || </span>
            <br/>
            <span>{{item.b}} || </span>
       </div>
       <button @click="go()">点击</button> -->

       <input 
            tyle="text"
            :placeholder="palcehoder2"
            :value="value"
       >
    </div>
    <script src="https://cdn.staticfile.org/vue/2.2.2/vue.min.js"></script>
    <script>
        const vm = new Vue({
           el:'#app',
           data() {
               return {
                //   list: [
                //       {a:1,b:2},
                //       {a:2,b:4},
                //       {a:3,b:5},
                //       {a:4,b:6}
                //   ],
                //   list2:[
                //       {f:1}
                //     ]
                value:'',
                palcehoder: '请输入文字'
               }
           },
           methods:{
            //    go(){
            //        console.log('$data', this.$data)
            //    }
            
           },
           computed:{
                palcehoder2(){
                    return this.value ? this.palcehoder : '输入文字完毕'

                }
           }
        });
    </script>
</body>

</html>