{"name": "@types/tapable", "version": "1.0.6", "description": "TypeScript definitions for tapable", "license": "MIT", "contributors": [{"name": "e-cloud", "url": "https://github.com/e-cloud", "githubUsername": "e-cloud"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/tapable"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "788018beac10366820eb56bbd9d711b5e26e353300932f7c925a8d87eadaee46", "typeScriptVersion": "3.0"}