{"name": "@types/qs", "version": "6.9.4", "description": "TypeScript definitions for qs", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/RWander", "githubUsername": "R<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/leonyu", "githubUsername": "leonyu"}, {"name": "<PERSON>", "url": "https://github.com/tehbelinda", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/zyml", "githubUsername": "zyml"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/artursvonda", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dpsmith3", "githubUsername": "dpsmith3"}, {"name": "<PERSON>", "url": "https://github.com/hperrin", "githubUsername": "<PERSON><PERSON>rin"}, {"name": "<PERSON>", "url": "https://github.com/ljharb", "githubUsername": "lj<PERSON>b"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/qs"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "439bfa44155bda6199294078b4c73c6e38042c2b583f470b75d740a50e4c7125", "typeScriptVersion": "3.0"}