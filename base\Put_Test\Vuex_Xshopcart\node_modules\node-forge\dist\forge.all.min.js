!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.forge=t():e.forge=t()}("undefined"!=typeof self?self:this,function(){return function(e){function t(n){if(r[n])return r[n].exports;var a=r[n]={i:n,l:!1,exports:{}};return e[n].call(a.exports,a,a.exports,t),a.l=!0,a.exports}var r={};return t.m=e,t.c=r,t.d=function(e,r,n){t.o(e,r)||Object.defineProperty(e,r,{configurable:!1,enumerable:!0,get:n})},t.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,"a",r),r},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=36)}([function(e,t){e.exports={options:{usePureJavaScript:!1}}},function(e,t,r){(function(t){function n(e){if(8!==e&&16!==e&&24!==e&&32!==e)throw new Error("Only 8, 16, 24, or 32 bits supported: "+e)}function a(e){if(this.data="",this.read=0,"string"==typeof e)this.data=e;else if(c.isArrayBuffer(e)||c.isArrayBufferView(e))if("undefined"!=typeof Buffer&&e instanceof Buffer)this.data=e.toString("binary");else{var t=new Uint8Array(e);try{this.data=String.fromCharCode.apply(null,t)}catch(e){for(var r=0;r<t.length;++r)this.putByte(t[r])}}else(e instanceof a||"object"==typeof e&&"string"==typeof e.data&&"number"==typeof e.read)&&(this.data=e.data,this.read=e.read);this._constructedStringLength=0}function i(e,t){t=t||{},this.read=t.readOffset||0,this.growSize=t.growSize||1024;var r=c.isArrayBuffer(e),n=c.isArrayBufferView(e);if(r||n)return this.data=r?new DataView(e):new DataView(e.buffer,e.byteOffset,e.byteLength),void(this.write="writeOffset"in t?t.writeOffset:this.data.byteLength);this.data=new DataView(new ArrayBuffer(0)),this.write=0,null!==e&&void 0!==e&&this.putBytes(e),"writeOffset"in t&&(this.write=t.writeOffset)}var s=r(0),o=r(40),c=e.exports=s.util=s.util||{};!function(){function e(e){if(e.source===window&&e.data===t){e.stopPropagation();var n=r.slice();r.length=0,n.forEach(function(e){e()})}}if("undefined"!=typeof process&&process.nextTick&&!process.browser)return c.nextTick=process.nextTick,void("function"==typeof setImmediate?c.setImmediate=setImmediate:c.setImmediate=c.nextTick);if("function"==typeof setImmediate)return c.setImmediate=function(){return setImmediate.apply(void 0,arguments)},void(c.nextTick=function(e){return setImmediate(e)});if(c.setImmediate=function(e){setTimeout(e,0)},"undefined"!=typeof window&&"function"==typeof window.postMessage){var t="forge.setImmediate",r=[];c.setImmediate=function(e){r.push(e),1===r.length&&window.postMessage(t,"*")},window.addEventListener("message",e,!0)}if("undefined"!=typeof MutationObserver){var n=Date.now(),a=!0,i=document.createElement("div"),r=[];new MutationObserver(function(){var e=r.slice();r.length=0,e.forEach(function(e){e()})}).observe(i,{attributes:!0});var s=c.setImmediate;c.setImmediate=function(e){Date.now()-n>15?(n=Date.now(),s(e)):(r.push(e),1===r.length&&i.setAttribute("a",a=!a))}}c.nextTick=c.setImmediate}(),c.isNodejs="undefined"!=typeof process&&process.versions&&process.versions.node,c.globalScope=function(){return c.isNodejs?t:"undefined"==typeof self?window:self}(),c.isArray=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},c.isArrayBuffer=function(e){return"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer},c.isArrayBufferView=function(e){return e&&c.isArrayBuffer(e.buffer)&&void 0!==e.byteLength},c.ByteBuffer=a,c.ByteStringBuffer=a;c.ByteStringBuffer.prototype._optimizeConstructedString=function(e){this._constructedStringLength+=e,this._constructedStringLength>4096&&(this.data.substr(0,1),this._constructedStringLength=0)},c.ByteStringBuffer.prototype.length=function(){return this.data.length-this.read},c.ByteStringBuffer.prototype.isEmpty=function(){return this.length()<=0},c.ByteStringBuffer.prototype.putByte=function(e){return this.putBytes(String.fromCharCode(e))},c.ByteStringBuffer.prototype.fillWithByte=function(e,t){e=String.fromCharCode(e);for(var r=this.data;t>0;)1&t&&(r+=e),(t>>>=1)>0&&(e+=e);return this.data=r,this._optimizeConstructedString(t),this},c.ByteStringBuffer.prototype.putBytes=function(e){return this.data+=e,this._optimizeConstructedString(e.length),this},c.ByteStringBuffer.prototype.putString=function(e){return this.putBytes(c.encodeUtf8(e))},c.ByteStringBuffer.prototype.putInt16=function(e){return this.putBytes(String.fromCharCode(e>>8&255)+String.fromCharCode(255&e))},c.ByteStringBuffer.prototype.putInt24=function(e){return this.putBytes(String.fromCharCode(e>>16&255)+String.fromCharCode(e>>8&255)+String.fromCharCode(255&e))},c.ByteStringBuffer.prototype.putInt32=function(e){return this.putBytes(String.fromCharCode(e>>24&255)+String.fromCharCode(e>>16&255)+String.fromCharCode(e>>8&255)+String.fromCharCode(255&e))},c.ByteStringBuffer.prototype.putInt16Le=function(e){return this.putBytes(String.fromCharCode(255&e)+String.fromCharCode(e>>8&255))},c.ByteStringBuffer.prototype.putInt24Le=function(e){return this.putBytes(String.fromCharCode(255&e)+String.fromCharCode(e>>8&255)+String.fromCharCode(e>>16&255))},c.ByteStringBuffer.prototype.putInt32Le=function(e){return this.putBytes(String.fromCharCode(255&e)+String.fromCharCode(e>>8&255)+String.fromCharCode(e>>16&255)+String.fromCharCode(e>>24&255))},c.ByteStringBuffer.prototype.putInt=function(e,t){n(t);var r="";do{t-=8,r+=String.fromCharCode(e>>t&255)}while(t>0);return this.putBytes(r)},c.ByteStringBuffer.prototype.putSignedInt=function(e,t){return e<0&&(e+=2<<t-1),this.putInt(e,t)},c.ByteStringBuffer.prototype.putBuffer=function(e){return this.putBytes(e.getBytes())},c.ByteStringBuffer.prototype.getByte=function(){return this.data.charCodeAt(this.read++)},c.ByteStringBuffer.prototype.getInt16=function(){var e=this.data.charCodeAt(this.read)<<8^this.data.charCodeAt(this.read+1);return this.read+=2,e},c.ByteStringBuffer.prototype.getInt24=function(){var e=this.data.charCodeAt(this.read)<<16^this.data.charCodeAt(this.read+1)<<8^this.data.charCodeAt(this.read+2);return this.read+=3,e},c.ByteStringBuffer.prototype.getInt32=function(){var e=this.data.charCodeAt(this.read)<<24^this.data.charCodeAt(this.read+1)<<16^this.data.charCodeAt(this.read+2)<<8^this.data.charCodeAt(this.read+3);return this.read+=4,e},c.ByteStringBuffer.prototype.getInt16Le=function(){var e=this.data.charCodeAt(this.read)^this.data.charCodeAt(this.read+1)<<8;return this.read+=2,e},c.ByteStringBuffer.prototype.getInt24Le=function(){var e=this.data.charCodeAt(this.read)^this.data.charCodeAt(this.read+1)<<8^this.data.charCodeAt(this.read+2)<<16;return this.read+=3,e},c.ByteStringBuffer.prototype.getInt32Le=function(){var e=this.data.charCodeAt(this.read)^this.data.charCodeAt(this.read+1)<<8^this.data.charCodeAt(this.read+2)<<16^this.data.charCodeAt(this.read+3)<<24;return this.read+=4,e},c.ByteStringBuffer.prototype.getInt=function(e){n(e);var t=0;do{t=(t<<8)+this.data.charCodeAt(this.read++),e-=8}while(e>0);return t},c.ByteStringBuffer.prototype.getSignedInt=function(e){var t=this.getInt(e),r=2<<e-2;return t>=r&&(t-=r<<1),t},c.ByteStringBuffer.prototype.getBytes=function(e){var t;return e?(e=Math.min(this.length(),e),t=this.data.slice(this.read,this.read+e),this.read+=e):0===e?t="":(t=0===this.read?this.data:this.data.slice(this.read),this.clear()),t},c.ByteStringBuffer.prototype.bytes=function(e){return void 0===e?this.data.slice(this.read):this.data.slice(this.read,this.read+e)},c.ByteStringBuffer.prototype.at=function(e){return this.data.charCodeAt(this.read+e)},c.ByteStringBuffer.prototype.setAt=function(e,t){return this.data=this.data.substr(0,this.read+e)+String.fromCharCode(t)+this.data.substr(this.read+e+1),this},c.ByteStringBuffer.prototype.last=function(){return this.data.charCodeAt(this.data.length-1)},c.ByteStringBuffer.prototype.copy=function(){var e=c.createBuffer(this.data);return e.read=this.read,e},c.ByteStringBuffer.prototype.compact=function(){return this.read>0&&(this.data=this.data.slice(this.read),this.read=0),this},c.ByteStringBuffer.prototype.clear=function(){return this.data="",this.read=0,this},c.ByteStringBuffer.prototype.truncate=function(e){var t=Math.max(0,this.length()-e);return this.data=this.data.substr(this.read,t),this.read=0,this},c.ByteStringBuffer.prototype.toHex=function(){for(var e="",t=this.read;t<this.data.length;++t){var r=this.data.charCodeAt(t);r<16&&(e+="0"),e+=r.toString(16)}return e},c.ByteStringBuffer.prototype.toString=function(){return c.decodeUtf8(this.bytes())},c.DataBuffer=i,c.DataBuffer.prototype.length=function(){return this.write-this.read},c.DataBuffer.prototype.isEmpty=function(){return this.length()<=0},c.DataBuffer.prototype.accommodate=function(e,t){if(this.length()>=e)return this;t=Math.max(t||this.growSize,e);var r=new Uint8Array(this.data.buffer,this.data.byteOffset,this.data.byteLength),n=new Uint8Array(this.length()+t);return n.set(r),this.data=new DataView(n.buffer),this},c.DataBuffer.prototype.putByte=function(e){return this.accommodate(1),this.data.setUint8(this.write++,e),this},c.DataBuffer.prototype.fillWithByte=function(e,t){this.accommodate(t);for(var r=0;r<t;++r)this.data.setUint8(e);return this},c.DataBuffer.prototype.putBytes=function(e,t){if(c.isArrayBufferView(e)){var r=new Uint8Array(e.buffer,e.byteOffset,e.byteLength),n=r.byteLength-r.byteOffset;this.accommodate(n);var a=new Uint8Array(this.data.buffer,this.write);return a.set(r),this.write+=n,this}if(c.isArrayBuffer(e)){var r=new Uint8Array(e);this.accommodate(r.byteLength);var a=new Uint8Array(this.data.buffer);return a.set(r,this.write),this.write+=r.byteLength,this}if(e instanceof c.DataBuffer||"object"==typeof e&&"number"==typeof e.read&&"number"==typeof e.write&&c.isArrayBufferView(e.data)){var r=new Uint8Array(e.data.byteLength,e.read,e.length());this.accommodate(r.byteLength);var a=new Uint8Array(e.data.byteLength,this.write);return a.set(r),this.write+=r.byteLength,this}if(e instanceof c.ByteStringBuffer&&(e=e.data,t="binary"),t=t||"binary","string"==typeof e){var i;if("hex"===t)return this.accommodate(Math.ceil(e.length/2)),i=new Uint8Array(this.data.buffer,this.write),this.write+=c.binary.hex.decode(e,i,this.write),this;if("base64"===t)return this.accommodate(3*Math.ceil(e.length/4)),i=new Uint8Array(this.data.buffer,this.write),this.write+=c.binary.base64.decode(e,i,this.write),this;if("utf8"===t&&(e=c.encodeUtf8(e),t="binary"),"binary"===t||"raw"===t)return this.accommodate(e.length),i=new Uint8Array(this.data.buffer,this.write),this.write+=c.binary.raw.decode(i),this;if("utf16"===t)return this.accommodate(2*e.length),i=new Uint16Array(this.data.buffer,this.write),this.write+=c.text.utf16.encode(i),this;throw new Error("Invalid encoding: "+t)}throw Error("Invalid parameter: "+e)},c.DataBuffer.prototype.putBuffer=function(e){return this.putBytes(e),e.clear(),this},c.DataBuffer.prototype.putString=function(e){return this.putBytes(e,"utf16")},c.DataBuffer.prototype.putInt16=function(e){return this.accommodate(2),this.data.setInt16(this.write,e),this.write+=2,this},c.DataBuffer.prototype.putInt24=function(e){return this.accommodate(3),this.data.setInt16(this.write,e>>8&65535),this.data.setInt8(this.write,e>>16&255),this.write+=3,this},c.DataBuffer.prototype.putInt32=function(e){return this.accommodate(4),this.data.setInt32(this.write,e),this.write+=4,this},c.DataBuffer.prototype.putInt16Le=function(e){return this.accommodate(2),this.data.setInt16(this.write,e,!0),this.write+=2,this},c.DataBuffer.prototype.putInt24Le=function(e){return this.accommodate(3),this.data.setInt8(this.write,e>>16&255),this.data.setInt16(this.write,e>>8&65535,!0),this.write+=3,this},c.DataBuffer.prototype.putInt32Le=function(e){return this.accommodate(4),this.data.setInt32(this.write,e,!0),this.write+=4,this},c.DataBuffer.prototype.putInt=function(e,t){n(t),this.accommodate(t/8);do{t-=8,this.data.setInt8(this.write++,e>>t&255)}while(t>0);return this},c.DataBuffer.prototype.putSignedInt=function(e,t){return n(t),this.accommodate(t/8),e<0&&(e+=2<<t-1),this.putInt(e,t)},c.DataBuffer.prototype.getByte=function(){return this.data.getInt8(this.read++)},c.DataBuffer.prototype.getInt16=function(){var e=this.data.getInt16(this.read);return this.read+=2,e},c.DataBuffer.prototype.getInt24=function(){var e=this.data.getInt16(this.read)<<8^this.data.getInt8(this.read+2);return this.read+=3,e},c.DataBuffer.prototype.getInt32=function(){var e=this.data.getInt32(this.read);return this.read+=4,e},c.DataBuffer.prototype.getInt16Le=function(){var e=this.data.getInt16(this.read,!0);return this.read+=2,e},c.DataBuffer.prototype.getInt24Le=function(){var e=this.data.getInt8(this.read)^this.data.getInt16(this.read+1,!0)<<8;return this.read+=3,e},c.DataBuffer.prototype.getInt32Le=function(){var e=this.data.getInt32(this.read,!0);return this.read+=4,e},c.DataBuffer.prototype.getInt=function(e){n(e);var t=0;do{t=(t<<8)+this.data.getInt8(this.read++),e-=8}while(e>0);return t},c.DataBuffer.prototype.getSignedInt=function(e){var t=this.getInt(e),r=2<<e-2;return t>=r&&(t-=r<<1),t},c.DataBuffer.prototype.getBytes=function(e){var t;return e?(e=Math.min(this.length(),e),t=this.data.slice(this.read,this.read+e),this.read+=e):0===e?t="":(t=0===this.read?this.data:this.data.slice(this.read),this.clear()),t},c.DataBuffer.prototype.bytes=function(e){return void 0===e?this.data.slice(this.read):this.data.slice(this.read,this.read+e)},c.DataBuffer.prototype.at=function(e){return this.data.getUint8(this.read+e)},c.DataBuffer.prototype.setAt=function(e,t){return this.data.setUint8(e,t),this},c.DataBuffer.prototype.last=function(){return this.data.getUint8(this.write-1)},c.DataBuffer.prototype.copy=function(){return new c.DataBuffer(this)},c.DataBuffer.prototype.compact=function(){if(this.read>0){var e=new Uint8Array(this.data.buffer,this.read),t=new Uint8Array(e.byteLength);t.set(e),this.data=new DataView(t),this.write-=this.read,this.read=0}return this},c.DataBuffer.prototype.clear=function(){return this.data=new DataView(new ArrayBuffer(0)),this.read=this.write=0,this},c.DataBuffer.prototype.truncate=function(e){return this.write=Math.max(0,this.length()-e),this.read=Math.min(this.read,this.write),this},c.DataBuffer.prototype.toHex=function(){for(var e="",t=this.read;t<this.data.byteLength;++t){var r=this.data.getUint8(t);r<16&&(e+="0"),e+=r.toString(16)}return e},c.DataBuffer.prototype.toString=function(e){var t=new Uint8Array(this.data,this.read,this.length());if("binary"===(e=e||"utf8")||"raw"===e)return c.binary.raw.encode(t);if("hex"===e)return c.binary.hex.encode(t);if("base64"===e)return c.binary.base64.encode(t);if("utf8"===e)return c.text.utf8.decode(t);if("utf16"===e)return c.text.utf16.decode(t);throw new Error("Invalid encoding: "+e)},c.createBuffer=function(e,t){return t=t||"raw",void 0!==e&&"utf8"===t&&(e=c.encodeUtf8(e)),new c.ByteBuffer(e)},c.fillString=function(e,t){for(var r="";t>0;)1&t&&(r+=e),(t>>>=1)>0&&(e+=e);return r},c.xorBytes=function(e,t,r){for(var n="",a="",i="",s=0,o=0;r>0;--r,++s)a=e.charCodeAt(s)^t.charCodeAt(s),o>=10&&(n+=i,i="",o=0),i+=String.fromCharCode(a),++o;return n+=i},c.hexToBytes=function(e){var t="",r=0;for(!0&e.length&&(r=1,t+=String.fromCharCode(parseInt(e[0],16)));r<e.length;r+=2)t+=String.fromCharCode(parseInt(e.substr(r,2),16));return t},c.bytesToHex=function(e){return c.createBuffer(e).toHex()},c.int32ToBytes=function(e){return String.fromCharCode(e>>24&255)+String.fromCharCode(e>>16&255)+String.fromCharCode(e>>8&255)+String.fromCharCode(255&e)};var u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",l=[62,-1,-1,-1,63,52,53,54,55,56,57,58,59,60,61,-1,-1,-1,64,-1,-1,-1,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,-1,-1,-1,-1,-1,-1,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51],p="123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";c.encode64=function(e,t){for(var r,n,a,i="",s="",o=0;o<e.length;)r=e.charCodeAt(o++),n=e.charCodeAt(o++),a=e.charCodeAt(o++),i+=u.charAt(r>>2),i+=u.charAt((3&r)<<4|n>>4),isNaN(n)?i+="==":(i+=u.charAt((15&n)<<2|a>>6),i+=isNaN(a)?"=":u.charAt(63&a)),t&&i.length>t&&(s+=i.substr(0,t)+"\r\n",i=i.substr(t));return s+=i},c.decode64=function(e){e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"");for(var t,r,n,a,i="",s=0;s<e.length;)t=l[e.charCodeAt(s++)-43],r=l[e.charCodeAt(s++)-43],n=l[e.charCodeAt(s++)-43],a=l[e.charCodeAt(s++)-43],i+=String.fromCharCode(t<<2|r>>4),64!==n&&(i+=String.fromCharCode((15&r)<<4|n>>2),64!==a&&(i+=String.fromCharCode((3&n)<<6|a)));return i},c.encodeUtf8=function(e){return unescape(encodeURIComponent(e))},c.decodeUtf8=function(e){return decodeURIComponent(escape(e))},c.binary={raw:{},hex:{},base64:{},base58:{},baseN:{encode:o.encode,decode:o.decode}},c.binary.raw.encode=function(e){return String.fromCharCode.apply(null,e)},c.binary.raw.decode=function(e,t,r){var n=t;n||(n=new Uint8Array(e.length)),r=r||0;for(var a=r,i=0;i<e.length;++i)n[a++]=e.charCodeAt(i);return t?a-r:n},c.binary.hex.encode=c.bytesToHex,c.binary.hex.decode=function(e,t,r){var n=t;n||(n=new Uint8Array(Math.ceil(e.length/2))),r=r||0;var a=0,i=r;for(1&e.length&&(a=1,n[i++]=parseInt(e[0],16));a<e.length;a+=2)n[i++]=parseInt(e.substr(a,2),16);return t?i-r:n},c.binary.base64.encode=function(e,t){for(var r,n,a,i="",s="",o=0;o<e.byteLength;)r=e[o++],n=e[o++],a=e[o++],i+=u.charAt(r>>2),i+=u.charAt((3&r)<<4|n>>4),isNaN(n)?i+="==":(i+=u.charAt((15&n)<<2|a>>6),i+=isNaN(a)?"=":u.charAt(63&a)),t&&i.length>t&&(s+=i.substr(0,t)+"\r\n",i=i.substr(t));return s+=i},c.binary.base64.decode=function(e,t,r){var n=t;n||(n=new Uint8Array(3*Math.ceil(e.length/4))),e=e.replace(/[^A-Za-z0-9\+\/\=]/g,""),r=r||0;for(var a,i,s,o,c=0,u=r;c<e.length;)a=l[e.charCodeAt(c++)-43],i=l[e.charCodeAt(c++)-43],s=l[e.charCodeAt(c++)-43],o=l[e.charCodeAt(c++)-43],n[u++]=a<<2|i>>4,64!==s&&(n[u++]=(15&i)<<4|s>>2,64!==o&&(n[u++]=(3&s)<<6|o));return t?u-r:n.subarray(0,u)},c.binary.base58.encode=function(e,t){return c.binary.baseN.encode(e,p,t)},c.binary.base58.decode=function(e,t){return c.binary.baseN.decode(e,p,t)},c.text={utf8:{},utf16:{}},c.text.utf8.encode=function(e,t,r){e=c.encodeUtf8(e);var n=t;n||(n=new Uint8Array(e.length)),r=r||0;for(var a=r,i=0;i<e.length;++i)n[a++]=e.charCodeAt(i);return t?a-r:n},c.text.utf8.decode=function(e){return c.decodeUtf8(String.fromCharCode.apply(null,e))},c.text.utf16.encode=function(e,t,r){var n=t;n||(n=new Uint8Array(2*e.length));var a=new Uint16Array(n.buffer);r=r||0;for(var i=r,s=r,o=0;o<e.length;++o)a[s++]=e.charCodeAt(o),i+=2;return t?i-r:n},c.text.utf16.decode=function(e){return String.fromCharCode.apply(null,new Uint16Array(e.buffer))},c.deflate=function(e,t,r){if(t=c.decode64(e.deflate(c.encode64(t)).rval),r){var n=2;32&t.charCodeAt(1)&&(n=6),t=t.substring(n,t.length-4)}return t},c.inflate=function(e,t,r){var n=e.inflate(c.encode64(t)).rval;return null===n?null:c.decode64(n)};var f=function(e,t,r){if(!e)throw new Error("WebStorage not available.");var n;if(null===r?n=e.removeItem(t):(r=c.encode64(JSON.stringify(r)),n=e.setItem(t,r)),void 0!==n&&!0!==n.rval){var a=new Error(n.error.message);throw a.id=n.error.id,a.name=n.error.name,a}},h=function(e,t){if(!e)throw new Error("WebStorage not available.");var r=e.getItem(t);if(e.init)if(null===r.rval){if(r.error){var n=new Error(r.error.message);throw n.id=r.error.id,n.name=r.error.name,n}r=null}else r=r.rval;return null!==r&&(r=JSON.parse(c.decode64(r))),r},d=function(e,t,r,n){var a=h(e,t);null===a&&(a={}),a[r]=n,f(e,t,a)},y=function(e,t,r){var n=h(e,t);return null!==n&&(n=r in n?n[r]:null),n},g=function(e,t,r){var n=h(e,t);if(null!==n&&r in n){delete n[r];var a=!0;for(var i in n){a=!1;break}a&&(n=null),f(e,t,n)}},v=function(e,t){f(e,t,null)},m=function(e,t,r){var n=null;void 0===r&&(r=["web","flash"]);var a,i=!1,s=null;for(var o in r){a=r[o];try{if("flash"===a||"both"===a){if(null===t[0])throw new Error("Flash local storage not available.");n=e.apply(this,t),i="flash"===a}"web"!==a&&"both"!==a||(t[0]=localStorage,n=e.apply(this,t),i=!0)}catch(e){s=e}if(i)break}if(!i)throw s;return n};c.setItem=function(e,t,r,n,a){m(d,arguments,a)},c.getItem=function(e,t,r,n){return m(y,arguments,n)},c.removeItem=function(e,t,r,n){m(g,arguments,n)},c.clearItems=function(e,t,r){m(v,arguments,r)},c.parseUrl=function(e){var t=/^(https?):\/\/([^:&^\/]*):?(\d*)(.*)$/g;t.lastIndex=0;var r=t.exec(e),n=null===r?null:{full:e,scheme:r[1],host:r[2],port:r[3],path:r[4]};return n&&(n.fullHost=n.host,n.port?80!==n.port&&"http"===n.scheme?n.fullHost+=":"+n.port:443!==n.port&&"https"===n.scheme&&(n.fullHost+=":"+n.port):"http"===n.scheme?n.port=80:"https"===n.scheme&&(n.port=443),n.full=n.scheme+"://"+n.fullHost),n};var C=null;c.getQueryVariables=function(e){var t,r=function(e){for(var t={},r=e.split("&"),n=0;n<r.length;n++){var a,i,s=r[n].indexOf("=");s>0?(a=r[n].substring(0,s),i=r[n].substring(s+1)):(a=r[n],i=null),a in t||(t[a]=[]),a in Object.prototype||null===i||t[a].push(unescape(i))}return t};return void 0===e?(null===C&&(C="undefined"!=typeof window&&window.location&&window.location.search?r(window.location.search.substring(1)):{}),t=C):t=r(e),t},c.parseFragment=function(e){var t=e,r="",n=e.indexOf("?");n>0&&(t=e.substring(0,n),r=e.substring(n+1));var a=t.split("/");return a.length>0&&""===a[0]&&a.shift(),{pathString:t,queryString:r,path:a,query:""===r?{}:c.getQueryVariables(r)}},c.makeRequest=function(e){var t=c.parseFragment(e),r={path:t.pathString,query:t.queryString,getPath:function(e){return void 0===e?t.path:t.path[e]},getQuery:function(e,r){var n;return void 0===e?n=t.query:(n=t.query[e])&&void 0!==r&&(n=n[r]),n},getQueryLast:function(e,t){var n=r.getQuery(e);return n?n[n.length-1]:t}};return r},c.makeLink=function(e,t,r){e=jQuery.isArray(e)?e.join("/"):e;var n=jQuery.param(t||{});return r=r||"",e+(n.length>0?"?"+n:"")+(r.length>0?"#"+r:"")},c.setPath=function(e,t,r){if("object"==typeof e&&null!==e)for(var n=0,a=t.length;n<a;){var i=t[n++];if(n==a)e[i]=r;else{var s=i in e;(!s||s&&"object"!=typeof e[i]||s&&null===e[i])&&(e[i]={}),e=e[i]}}},c.getPath=function(e,t,r){for(var n=0,a=t.length,i=!0;i&&n<a&&"object"==typeof e&&null!==e;){var s=t[n++];i=s in e,i&&(e=e[s])}return i?e:r},c.deletePath=function(e,t){if("object"==typeof e&&null!==e)for(var r=0,n=t.length;r<n;){var a=t[r++];if(r==n)delete e[a];else{if(!(a in e)||"object"!=typeof e[a]||null===e[a])break;e=e[a]}}},c.isEmpty=function(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0},c.format=function(e){for(var t,r,n=/%./g,a=0,i=[],s=0;t=n.exec(e);){r=e.substring(s,n.lastIndex-2),r.length>0&&i.push(r),s=n.lastIndex;var o=t[0][1];switch(o){case"s":case"o":a<arguments.length?i.push(arguments[1+a++]):i.push("<?>");break;case"%":i.push("%");break;default:i.push("<%"+o+"?>")}}return i.push(e.substring(s)),i.join("")},c.formatNumber=function(e,t,r,n){var a=e,i=isNaN(t=Math.abs(t))?2:t,s=void 0===r?",":r,o=void 0===n?".":n,c=a<0?"-":"",u=parseInt(a=Math.abs(+a||0).toFixed(i),10)+"",l=u.length>3?u.length%3:0;return c+(l?u.substr(0,l)+o:"")+u.substr(l).replace(/(\d{3})(?=\d)/g,"$1"+o)+(i?s+Math.abs(a-u).toFixed(i).slice(2):"")},c.formatSize=function(e){return e=e>=1073741824?c.formatNumber(e/1073741824,2,".","")+" GiB":e>=1048576?c.formatNumber(e/1048576,2,".","")+" MiB":e>=1024?c.formatNumber(e/1024,0)+" KiB":c.formatNumber(e,0)+" bytes"},c.bytesFromIP=function(e){return-1!==e.indexOf(".")?c.bytesFromIPv4(e):-1!==e.indexOf(":")?c.bytesFromIPv6(e):null},c.bytesFromIPv4=function(e){if(e=e.split("."),4!==e.length)return null;for(var t=c.createBuffer(),r=0;r<e.length;++r){var n=parseInt(e[r],10);if(isNaN(n))return null;t.putByte(n)}return t.getBytes()},c.bytesFromIPv6=function(e){var t=0;e=e.split(":").filter(function(e){return 0===e.length&&++t,!0});for(var r=2*(8-e.length+t),n=c.createBuffer(),a=0;a<8;++a)if(e[a]&&0!==e[a].length){var i=c.hexToBytes(e[a]);i.length<2&&n.putByte(0),n.putBytes(i)}else n.fillWithByte(0,r),r=0;return n.getBytes()},c.bytesToIP=function(e){return 4===e.length?c.bytesToIPv4(e):16===e.length?c.bytesToIPv6(e):null},c.bytesToIPv4=function(e){if(4!==e.length)return null;for(var t=[],r=0;r<e.length;++r)t.push(e.charCodeAt(r));return t.join(".")},c.bytesToIPv6=function(e){if(16!==e.length)return null;for(var t=[],r=[],n=0,a=0;a<e.length;a+=2){for(var i=c.bytesToHex(e[a]+e[a+1]);"0"===i[0]&&"0"!==i;)i=i.substr(1);if("0"===i){var s=r[r.length-1],o=t.length;s&&o===s.end+1?(s.end=o,s.end-s.start>r[n].end-r[n].start&&(n=r.length-1)):r.push({start:o,end:o})}t.push(i)}if(r.length>0){var u=r[n];u.end-u.start>0&&(t.splice(u.start,u.end-u.start+1,""),0===u.start&&t.unshift(""),7===u.end&&t.push(""))}return t.join(":")},c.estimateCores=function(e,t){function r(e,s,o){if(0===s){var u=Math.floor(e.reduce(function(e,t){return e+t},0)/e.length);return c.cores=Math.max(1,u),URL.revokeObjectURL(i),t(null,c.cores)}n(o,function(t,n){e.push(a(o,n)),r(e,s-1,o)})}function n(e,t){for(var r=[],n=[],a=0;a<e;++a){var s=new Worker(i);s.addEventListener("message",function(a){if(n.push(a.data),n.length===e){for(var i=0;i<e;++i)r[i].terminate();t(null,n)}}),r.push(s)}for(var a=0;a<e;++a)r[a].postMessage(a)}function a(e,t){for(var r=[],n=0;n<e;++n)for(var a=t[n],i=r[n]=[],s=0;s<e;++s)if(n!==s){var o=t[s];(a.st>o.st&&a.st<o.et||o.st>a.st&&o.st<a.et)&&i.push(s)}return r.reduce(function(e,t){return Math.max(e,t.length)},0)}if("function"==typeof e&&(t=e,e={}),e=e||{},"cores"in c&&!e.update)return t(null,c.cores);if("undefined"!=typeof navigator&&"hardwareConcurrency"in navigator&&navigator.hardwareConcurrency>0)return c.cores=navigator.hardwareConcurrency,t(null,c.cores);if("undefined"==typeof Worker)return c.cores=1,t(null,c.cores);if("undefined"==typeof Blob)return c.cores=2,t(null,c.cores);var i=URL.createObjectURL(new Blob(["(",function(){self.addEventListener("message",function(e){for(var t=Date.now(),r=t+4;Date.now()<r;);self.postMessage({st:t,et:r})})}.toString(),")()"],{type:"application/javascript"}));r([],5,16)}}).call(t,r(39))},function(e,t,r){var n=r(0);r(5),r(24),r(25),r(1),function(){if(n.random&&n.random.getBytes)return void(e.exports=n.random);!function(t){function r(){var e=n.prng.create(a);return e.getBytes=function(t,r){return e.generate(t,r)},e.getBytesSync=function(t){return e.generate(t)},e}var a={},i=new Array(4),s=n.util.createBuffer();a.formatKey=function(e){var t=n.util.createBuffer(e);return e=new Array(4),e[0]=t.getInt32(),e[1]=t.getInt32(),e[2]=t.getInt32(),e[3]=t.getInt32(),n.aes._expandKey(e,!1)},a.formatSeed=function(e){var t=n.util.createBuffer(e);return e=new Array(4),e[0]=t.getInt32(),e[1]=t.getInt32(),e[2]=t.getInt32(),e[3]=t.getInt32(),e},a.cipher=function(e,t){return n.aes._updateBlock(e,t,i,!1),s.putInt32(i[0]),s.putInt32(i[1]),s.putInt32(i[2]),s.putInt32(i[3]),s.getBytes()},a.increment=function(e){return++e[3],e},a.md=n.md.sha256;var o=r(),c=null,u=n.util.globalScope,l=u.crypto||u.msCrypto;if(l&&l.getRandomValues&&(c=function(e){return l.getRandomValues(e)}),n.options.usePureJavaScript||!n.util.isNodejs&&!c){if("undefined"==typeof window||window.document,o.collectInt(+new Date,32),"undefined"!=typeof navigator){var p="";for(var f in navigator)try{"string"==typeof navigator[f]&&(p+=navigator[f])}catch(e){}o.collect(p),p=null}t&&(t().mousemove(function(e){o.collectInt(e.clientX,16),o.collectInt(e.clientY,16)}),t().keypress(function(e){o.collectInt(e.charCode,8)}))}if(n.random)for(var f in o)n.random[f]=o[f];else n.random=o;n.random.createInstance=r,e.exports=n.random}("undefined"!=typeof jQuery?jQuery:null)}()},function(e,t,r){function n(e,t,r){if(r>t){var n=new Error("Too few bytes to parse DER.");throw n.available=e.length(),n.remaining=t,n.requested=r,n}}function a(e,t,r,i){var c;n(e,t,2);var u=e.getByte();t--;var l=192&u,p=31&u;c=e.length();var f=o(e,t);if(t-=c-e.length(),void 0!==f&&f>t){if(i.strict){var h=new Error("Too few bytes to read ASN.1 value.");throw h.available=e.length(),h.remaining=t,h.requested=f,h}f=t}var d,y,g=32==(32&u);if(g)if(d=[],void 0===f)for(;;){if(n(e,t,2),e.bytes(2)===String.fromCharCode(0,0)){e.getBytes(2),t-=2;break}c=e.length(),d.push(a(e,t,r+1,i)),t-=c-e.length()}else for(;f>0;)c=e.length(),d.push(a(e,f,r+1,i)),t-=c-e.length(),f-=c-e.length();if(void 0===d&&l===s.Class.UNIVERSAL&&p===s.Type.BITSTRING&&(y=e.bytes(f)),void 0===d&&i.decodeBitStrings&&l===s.Class.UNIVERSAL&&p===s.Type.BITSTRING&&f>1){var v=e.read,m=t,C=0;if(p===s.Type.BITSTRING&&(n(e,t,1),C=e.getByte(),t--),0===C)try{c=e.length();var E={verbose:i.verbose,strict:!0,decodeBitStrings:!0},S=a(e,t,r+1,E),T=c-e.length();t-=T,p==s.Type.BITSTRING&&T++;var b=S.tagClass;T!==f||b!==s.Class.UNIVERSAL&&b!==s.Class.CONTEXT_SPECIFIC||(d=[S])}catch(e){}void 0===d&&(e.read=v,t=m)}if(void 0===d){if(void 0===f){if(i.strict)throw new Error("Non-constructed ASN.1 object of indefinite length.");f=t}if(p===s.Type.BMPSTRING)for(d="";f>0;f-=2)n(e,t,2),d+=String.fromCharCode(e.getInt16()),t-=2;else d=e.getBytes(f)}var I=void 0===y?null:{bitStringContents:y};return s.create(l,p,g,d,I)}var i=r(0);r(1),r(6);var s=e.exports=i.asn1=i.asn1||{};s.Class={UNIVERSAL:0,APPLICATION:64,CONTEXT_SPECIFIC:128,PRIVATE:192},s.Type={NONE:0,BOOLEAN:1,INTEGER:2,BITSTRING:3,OCTETSTRING:4,NULL:5,OID:6,ODESC:7,EXTERNAL:8,REAL:9,ENUMERATED:10,EMBEDDED:11,UTF8:12,ROID:13,SEQUENCE:16,SET:17,PRINTABLESTRING:19,IA5STRING:22,UTCTIME:23,GENERALIZEDTIME:24,BMPSTRING:30},s.create=function(e,t,r,n,a){if(i.util.isArray(n)){for(var o=[],c=0;c<n.length;++c)void 0!==n[c]&&o.push(n[c]);n=o}var u={tagClass:e,type:t,constructed:r,composed:r||i.util.isArray(n),value:n};return a&&"bitStringContents"in a&&(u.bitStringContents=a.bitStringContents,u.original=s.copy(u)),u},s.copy=function(e,t){var r;if(i.util.isArray(e)){r=[];for(var n=0;n<e.length;++n)r.push(s.copy(e[n],t));return r}return"string"==typeof e?e:(r={tagClass:e.tagClass,type:e.type,constructed:e.constructed,composed:e.composed,value:s.copy(e.value,t)},t&&!t.excludeBitStringContents&&(r.bitStringContents=e.bitStringContents),r)},s.equals=function(e,t,r){if(i.util.isArray(e)){if(!i.util.isArray(t))return!1;if(e.length!==t.length)return!1;for(var n=0;n<e.length;++n)if(!s.equals(e[n],t[n]))return!1;return!0}if(typeof e!=typeof t)return!1;if("string"==typeof e)return e===t;var a=e.tagClass===t.tagClass&&e.type===t.type&&e.constructed===t.constructed&&e.composed===t.composed&&s.equals(e.value,t.value);return r&&r.includeBitStringContents&&(a=a&&e.bitStringContents===t.bitStringContents),a},s.getBerValueLength=function(e){var t=e.getByte();if(128!==t){return 128&t?e.getInt((127&t)<<3):t}};var o=function(e,t){var r=e.getByte();if(t--,128!==r){var a;if(128&r){var i=127&r;n(e,t,i),a=e.getInt(i<<3)}else a=r;if(a<0)throw new Error("Negative length: "+a);return a}};s.fromDer=function(e,t){return void 0===t&&(t={strict:!0,decodeBitStrings:!0}),"boolean"==typeof t&&(t={strict:t,decodeBitStrings:!0}),"strict"in t||(t.strict=!0),"decodeBitStrings"in t||(t.decodeBitStrings=!0),"string"==typeof e&&(e=i.util.createBuffer(e)),a(e,e.length(),0,t)},s.toDer=function(e){var t=i.util.createBuffer(),r=e.tagClass|e.type,n=i.util.createBuffer(),a=!1;if("bitStringContents"in e&&(a=!0,e.original&&(a=s.equals(e,e.original))),a)n.putBytes(e.bitStringContents);else if(e.composed){e.constructed?r|=32:n.putByte(0);for(var o=0;o<e.value.length;++o)void 0!==e.value[o]&&n.putBuffer(s.toDer(e.value[o]))}else if(e.type===s.Type.BMPSTRING)for(var o=0;o<e.value.length;++o)n.putInt16(e.value.charCodeAt(o));else e.type===s.Type.INTEGER&&e.value.length>1&&(0===e.value.charCodeAt(0)&&0==(128&e.value.charCodeAt(1))||255===e.value.charCodeAt(0)&&128==(128&e.value.charCodeAt(1)))?n.putBytes(e.value.substr(1)):n.putBytes(e.value);if(t.putByte(r),n.length()<=127)t.putByte(127&n.length());else{var c=n.length(),u="";do{u+=String.fromCharCode(255&c),c>>>=8}while(c>0);t.putByte(128|u.length);for(var o=u.length-1;o>=0;--o)t.putByte(u.charCodeAt(o))}return t.putBuffer(n),t},s.oidToDer=function(e){var t=e.split("."),r=i.util.createBuffer();r.putByte(40*parseInt(t[0],10)+parseInt(t[1],10));for(var n,a,s,o,c=2;c<t.length;++c){n=!0,a=[],s=parseInt(t[c],10);do{o=127&s,s>>>=7,n||(o|=128),a.push(o),n=!1}while(s>0);for(var u=a.length-1;u>=0;--u)r.putByte(a[u])}return r},s.derToOid=function(e){var t;"string"==typeof e&&(e=i.util.createBuffer(e));var r=e.getByte();t=Math.floor(r/40)+"."+r%40;for(var n=0;e.length()>0;)r=e.getByte(),n<<=7,128&r?n+=127&r:(t+="."+(n+r),n=0);return t},s.utcTimeToDate=function(e){var t=new Date,r=parseInt(e.substr(0,2),10);r=r>=50?1900+r:2e3+r;var n=parseInt(e.substr(2,2),10)-1,a=parseInt(e.substr(4,2),10),i=parseInt(e.substr(6,2),10),s=parseInt(e.substr(8,2),10),o=0;if(e.length>11){var c=e.charAt(10),u=10;"+"!==c&&"-"!==c&&(o=parseInt(e.substr(10,2),10),u+=2)}if(t.setUTCFullYear(r,n,a),t.setUTCHours(i,s,o,0),u&&("+"===(c=e.charAt(u))||"-"===c)){var l=parseInt(e.substr(u+1,2),10),p=parseInt(e.substr(u+4,2),10),f=60*l+p;f*=6e4,"+"===c?t.setTime(+t-f):t.setTime(+t+f)}return t},s.generalizedTimeToDate=function(e){var t=new Date,r=parseInt(e.substr(0,4),10),n=parseInt(e.substr(4,2),10)-1,a=parseInt(e.substr(6,2),10),i=parseInt(e.substr(8,2),10),s=parseInt(e.substr(10,2),10),o=parseInt(e.substr(12,2),10),c=0,u=0,l=!1;"Z"===e.charAt(e.length-1)&&(l=!0);var p=e.length-5,f=e.charAt(p);if("+"===f||"-"===f){u=60*parseInt(e.substr(p+1,2),10)+parseInt(e.substr(p+4,2),10),u*=6e4,"+"===f&&(u*=-1),l=!0}return"."===e.charAt(14)&&(c=1e3*parseFloat(e.substr(14),10)),l?(t.setUTCFullYear(r,n,a),t.setUTCHours(i,s,o,c),t.setTime(+t+u)):(t.setFullYear(r,n,a),t.setHours(i,s,o,c)),t},s.dateToUtcTime=function(e){if("string"==typeof e)return e;var t="",r=[];r.push((""+e.getUTCFullYear()).substr(2)),r.push(""+(e.getUTCMonth()+1)),r.push(""+e.getUTCDate()),r.push(""+e.getUTCHours()),r.push(""+e.getUTCMinutes()),r.push(""+e.getUTCSeconds());for(var n=0;n<r.length;++n)r[n].length<2&&(t+="0"),t+=r[n];return t+="Z"},s.dateToGeneralizedTime=function(e){if("string"==typeof e)return e;var t="",r=[];r.push(""+e.getUTCFullYear()),r.push(""+(e.getUTCMonth()+1)),r.push(""+e.getUTCDate()),r.push(""+e.getUTCHours()),r.push(""+e.getUTCMinutes()),r.push(""+e.getUTCSeconds());for(var n=0;n<r.length;++n)r[n].length<2&&(t+="0"),t+=r[n];return t+="Z"},s.integerToDer=function(e){var t=i.util.createBuffer();if(e>=-128&&e<128)return t.putSignedInt(e,8);if(e>=-32768&&e<32768)return t.putSignedInt(e,16);if(e>=-8388608&&e<8388608)return t.putSignedInt(e,24);if(e>=-2147483648&&e<2147483648)return t.putSignedInt(e,32);var r=new Error("Integer too large; max is 32-bits.");throw r.integer=e,r},s.derToInteger=function(e){"string"==typeof e&&(e=i.util.createBuffer(e));var t=8*e.length();if(t>32)throw new Error("Integer too large; max is 32-bits.");return e.getSignedInt(t)},s.validate=function(e,t,r,n){var a=!1;if(e.tagClass!==t.tagClass&&void 0!==t.tagClass||e.type!==t.type&&void 0!==t.type)n&&(e.tagClass!==t.tagClass&&n.push("["+t.name+'] Expected tag class "'+t.tagClass+'", got "'+e.tagClass+'"'),e.type!==t.type&&n.push("["+t.name+'] Expected type "'+t.type+'", got "'+e.type+'"'));else if(e.constructed===t.constructed||void 0===t.constructed){if(a=!0,t.value&&i.util.isArray(t.value))for(var o=0,c=0;a&&c<t.value.length;++c)a=t.value[c].optional||!1,e.value[o]&&(a=s.validate(e.value[o],t.value[c],r,n),a?++o:t.value[c].optional&&(a=!0)),!a&&n&&n.push("["+t.name+'] Tag class "'+t.tagClass+'", type "'+t.type+'" expected value length "'+t.value.length+'", got "'+e.value.length+'"');if(a&&r&&(t.capture&&(r[t.capture]=e.value),t.captureAsn1&&(r[t.captureAsn1]=e),t.captureBitStringContents&&"bitStringContents"in e&&(r[t.captureBitStringContents]=e.bitStringContents),t.captureBitStringValue&&"bitStringContents"in e)){if(e.bitStringContents.length<2)r[t.captureBitStringValue]="";else{var u=e.bitStringContents.charCodeAt(0);if(0!==u)throw new Error("captureBitStringValue only supported for zero unused bits");r[t.captureBitStringValue]=e.bitStringContents.slice(1)}}}else n&&n.push("["+t.name+'] Expected constructed "'+t.constructed+'", got "'+e.constructed+'"');return a};var c=/[^\\u0000-\\u00ff]/;s.prettyPrint=function(e,t,r){var n="";t=t||0,r=r||2,t>0&&(n+="\n");for(var a="",o=0;o<t*r;++o)a+=" ";switch(n+=a+"Tag: ",e.tagClass){case s.Class.UNIVERSAL:n+="Universal:";break;case s.Class.APPLICATION:n+="Application:";break;case s.Class.CONTEXT_SPECIFIC:n+="Context-Specific:";break;case s.Class.PRIVATE:n+="Private:"}if(e.tagClass===s.Class.UNIVERSAL)switch(n+=e.type,e.type){case s.Type.NONE:n+=" (None)";break;case s.Type.BOOLEAN:n+=" (Boolean)";break;case s.Type.INTEGER:n+=" (Integer)";break;case s.Type.BITSTRING:n+=" (Bit string)";break;case s.Type.OCTETSTRING:n+=" (Octet string)";break;case s.Type.NULL:n+=" (Null)";break;case s.Type.OID:n+=" (Object Identifier)";break;case s.Type.ODESC:n+=" (Object Descriptor)";break;case s.Type.EXTERNAL:n+=" (External or Instance of)";break;case s.Type.REAL:n+=" (Real)";break;case s.Type.ENUMERATED:n+=" (Enumerated)";break;case s.Type.EMBEDDED:n+=" (Embedded PDV)";break;case s.Type.UTF8:n+=" (UTF8)";break;case s.Type.ROID:n+=" (Relative Object Identifier)";break;case s.Type.SEQUENCE:n+=" (Sequence)";break;case s.Type.SET:n+=" (Set)";break;case s.Type.PRINTABLESTRING:n+=" (Printable String)";break;case s.Type.IA5String:n+=" (IA5String (ASCII))";break;case s.Type.UTCTIME:n+=" (UTC time)";break;case s.Type.GENERALIZEDTIME:n+=" (Generalized time)";break;case s.Type.BMPSTRING:n+=" (BMP String)"}else n+=e.type;if(n+="\n",n+=a+"Constructed: "+e.constructed+"\n",e.composed){for(var u=0,l="",o=0;o<e.value.length;++o)void 0!==e.value[o]&&(u+=1,l+=s.prettyPrint(e.value[o],t+1,r),o+1<e.value.length&&(l+=","));n+=a+"Sub values: "+u+l}else{if(n+=a+"Value: ",e.type===s.Type.OID){var p=s.derToOid(e.value);n+=p,i.pki&&i.pki.oids&&p in i.pki.oids&&(n+=" ("+i.pki.oids[p]+") ")}if(e.type===s.Type.INTEGER)try{n+=s.derToInteger(e.value)}catch(t){n+="0x"+i.util.bytesToHex(e.value)}else if(e.type===s.Type.BITSTRING){if(e.value.length>1?n+="0x"+i.util.bytesToHex(e.value.slice(1)):n+="(none)",e.value.length>0){var f=e.value.charCodeAt(0);1==f?n+=" (1 unused bit shown)":f>1&&(n+=" ("+f+" unused bits shown)")}}else e.type===s.Type.OCTETSTRING?(c.test(e.value)||(n+="("+e.value+") "),n+="0x"+i.util.bytesToHex(e.value)):e.type===s.Type.UTF8?n+=i.util.decodeUtf8(e.value):e.type===s.Type.PRINTABLESTRING||e.type===s.Type.IA5String?n+=e.value:c.test(e.value)?n+="0x"+i.util.bytesToHex(e.value):0===e.value.length?n+="[null]":n+=e.value}return n}},function(e,t,r){var n=r(0);e.exports=n.md=n.md||{},n.md.algorithms=n.md.algorithms||{}},function(e,t,r){function n(e,t){var r=function(){return new c.aes.Algorithm(e,t)};c.cipher.registerAlgorithm(e,r)}function a(){d=!0,p=[0,1,2,4,8,16,32,64,128,27,54];for(var e=new Array(256),t=0;t<128;++t)e[t]=t<<1,e[t+128]=t+128<<1^283;u=new Array(256),l=new Array(256),f=new Array(4),h=new Array(4);for(var t=0;t<4;++t)f[t]=new Array(256),h[t]=new Array(256);for(var r,n,a,i,s,o,c,y=0,g=0,t=0;t<256;++t){i=g^g<<1^g<<2^g<<3^g<<4,i=i>>8^255&i^99,u[y]=i,l[i]=y,s=e[i],r=e[y],n=e[r],a=e[n],o=s<<24^i<<16^i<<8^i^s,c=(r^n^a)<<24^(y^a)<<16^(y^n^a)<<8^y^r^a;for(var v=0;v<4;++v)f[v][y]=o,h[v][i]=c,o=o<<24|o>>>8,c=c<<24|c>>>8;0===y?y=g=1:(y=r^e[e[e[r^a]]],g^=e[e[g]])}}function i(e,t){for(var r,n=e.slice(0),a=1,i=n.length,s=i+6+1,o=y*s,c=i;c<o;++c)r=n[c-1],c%i==0?(r=u[r>>>16&255]<<24^u[r>>>8&255]<<16^u[255&r]<<8^u[r>>>24]^p[a]<<24,a++):i>6&&c%i==4&&(r=u[r>>>24]<<24^u[r>>>16&255]<<16^u[r>>>8&255]<<8^u[255&r]),n[c]=n[c-i]^r;if(t){var l,f=h[0],d=h[1],g=h[2],v=h[3],m=n.slice(0);o=n.length;for(var c=0,C=o-y;c<o;c+=y,C-=y)if(0===c||c===o-y)m[c]=n[C],m[c+1]=n[C+3],m[c+2]=n[C+2],m[c+3]=n[C+1];else for(var E=0;E<y;++E)l=n[C+E],m[c+(3&-E)]=f[u[l>>>24]]^d[u[l>>>16&255]]^g[u[l>>>8&255]]^v[u[255&l]];n=m}return n}function s(e,t,r,n){var a,i,s,o,c,p=e.length/4-1;n?(a=h[0],i=h[1],s=h[2],o=h[3],c=l):(a=f[0],i=f[1],s=f[2],o=f[3],c=u);var d,y,g,v,m,C,E;d=t[0]^e[0],y=t[n?3:1]^e[1],g=t[2]^e[2],v=t[n?1:3]^e[3];for(var S=3,T=1;T<p;++T)m=a[d>>>24]^i[y>>>16&255]^s[g>>>8&255]^o[255&v]^e[++S],C=a[y>>>24]^i[g>>>16&255]^s[v>>>8&255]^o[255&d]^e[++S],E=a[g>>>24]^i[v>>>16&255]^s[d>>>8&255]^o[255&y]^e[++S],v=a[v>>>24]^i[d>>>16&255]^s[y>>>8&255]^o[255&g]^e[++S],d=m,y=C,g=E;r[0]=c[d>>>24]<<24^c[y>>>16&255]<<16^c[g>>>8&255]<<8^c[255&v]^e[++S],r[n?3:1]=c[y>>>24]<<24^c[g>>>16&255]<<16^c[v>>>8&255]<<8^c[255&d]^e[++S],r[2]=c[g>>>24]<<24^c[v>>>16&255]<<16^c[d>>>8&255]<<8^c[255&y]^e[++S],r[n?1:3]=c[v>>>24]<<24^c[d>>>16&255]<<16^c[y>>>8&255]<<8^c[255&g]^e[++S]}function o(e){e=e||{};var t,r=(e.mode||"CBC").toUpperCase(),n="AES-"+r;t=e.decrypt?c.cipher.createDecipher(n,e.key):c.cipher.createCipher(n,e.key);var a=t.start;return t.start=function(e,r){var n=null;r instanceof c.util.ByteBuffer&&(n=r,r={}),r=r||{},r.output=n,r.iv=e,a.call(t,r)},t}var c=r(0);r(14),r(21),r(1),e.exports=c.aes=c.aes||{},c.aes.startEncrypting=function(e,t,r,n){var a=o({key:e,output:r,decrypt:!1,mode:n});return a.start(t),a},c.aes.createEncryptionCipher=function(e,t){return o({key:e,output:null,decrypt:!1,mode:t})},c.aes.startDecrypting=function(e,t,r,n){var a=o({key:e,output:r,decrypt:!0,mode:n});return a.start(t),a},c.aes.createDecryptionCipher=function(e,t){return o({key:e,output:null,decrypt:!0,mode:t})},c.aes.Algorithm=function(e,t){d||a();var r=this;r.name=e,r.mode=new t({blockSize:16,cipher:{encrypt:function(e,t){return s(r._w,e,t,!1)},decrypt:function(e,t){return s(r._w,e,t,!0)}}}),r._init=!1},c.aes.Algorithm.prototype.initialize=function(e){if(!this._init){var t,r=e.key;if("string"!=typeof r||16!==r.length&&24!==r.length&&32!==r.length){if(c.util.isArray(r)&&(16===r.length||24===r.length||32===r.length)){t=r,r=c.util.createBuffer();for(var n=0;n<t.length;++n)r.putByte(t[n])}}else r=c.util.createBuffer(r);if(!c.util.isArray(r)){t=r,r=[];var a=t.length();if(16===a||24===a||32===a){a>>>=2;for(var n=0;n<a;++n)r.push(t.getInt32())}}if(!c.util.isArray(r)||4!==r.length&&6!==r.length&&8!==r.length)throw new Error("Invalid key parameter.");var s=this.mode.name,o=-1!==["CFB","OFB","CTR","GCM"].indexOf(s);this._w=i(r,e.decrypt&&!o),this._init=!0}},c.aes._expandKey=function(e,t){return d||a(),i(e,t)},c.aes._updateBlock=s,n("AES-ECB",c.cipher.modes.ecb),n("AES-CBC",c.cipher.modes.cbc),n("AES-CFB",c.cipher.modes.cfb),n("AES-OFB",c.cipher.modes.ofb),n("AES-CTR",c.cipher.modes.ctr),n("AES-GCM",c.cipher.modes.gcm);var u,l,p,f,h,d=!1,y=4},function(e,t,r){function n(e,t){s[e]=t,s[t]=e}function a(e,t){s[e]=t}var i=r(0);i.pki=i.pki||{};var s=e.exports=i.pki.oids=i.oids=i.oids||{};n("1.2.840.113549.1.1.1","rsaEncryption"),n("1.2.840.113549.1.1.4","md5WithRSAEncryption"),n("1.2.840.113549.1.1.5","sha1WithRSAEncryption"),n("1.2.840.113549.1.1.7","RSAES-OAEP"),n("1.2.840.113549.1.1.8","mgf1"),n("1.2.840.113549.1.1.9","pSpecified"),n("1.2.840.113549.1.1.10","RSASSA-PSS"),n("1.2.840.113549.1.1.11","sha256WithRSAEncryption"),n("1.2.840.113549.1.1.12","sha384WithRSAEncryption"),n("1.2.840.113549.1.1.13","sha512WithRSAEncryption"),n("***********","EdDSA25519"),n("1.2.840.10040.4.3","dsa-with-sha1"),n("********.2.7","desCBC"),n("********.2.26","sha1"),n("2.16.840.*********.2.1","sha256"),n("2.16.840.*********.2.2","sha384"),n("2.16.840.*********.2.3","sha512"),n("1.2.840.113549.2.5","md5"),n("1.2.840.113549.1.7.1","data"),n("1.2.840.113549.1.7.2","signedData"),n("1.2.840.113549.1.7.3","envelopedData"),n("1.2.840.113549.1.7.4","signedAndEnvelopedData"),n("1.2.840.113549.1.7.5","digestedData"),n("1.2.840.113549.1.7.6","encryptedData"),n("1.2.840.113549.1.9.1","emailAddress"),n("1.2.840.113549.1.9.2","unstructuredName"),n("1.2.840.113549.1.9.3","contentType"),n("1.2.840.113549.1.9.4","messageDigest"),n("1.2.840.113549.1.9.5","signingTime"),n("1.2.840.113549.1.9.6","counterSignature"),n("1.2.840.113549.1.9.7","challengePassword"),n("1.2.840.113549.1.9.8","unstructuredAddress"),n("1.2.840.113549.1.9.14","extensionRequest"),n("1.2.840.113549.1.9.20","friendlyName"),n("1.2.840.113549.1.9.21","localKeyId"),n("1.2.840.113549.********","x509Certificate"),n("1.2.840.113549.*********.1","keyBag"),n("1.2.840.113549.*********.2","pkcs8ShroudedKeyBag"),n("1.2.840.113549.*********.3","certBag"),n("1.2.840.113549.*********.4","crlBag"),n("1.2.840.113549.*********.5","secretBag"),n("1.2.840.113549.*********.6","safeContentsBag"),n("1.2.840.113549.1.5.13","pkcs5PBES2"),n("1.2.840.113549.1.5.12","pkcs5PBKDF2"),n("1.2.840.113549.********","pbeWithSHAAnd128BitRC4"),n("1.2.840.113549.********","pbeWithSHAAnd40BitRC4"),n("1.2.840.113549.********","pbeWithSHAAnd3-KeyTripleDES-CBC"),n("1.2.840.113549.********","pbeWithSHAAnd2-KeyTripleDES-CBC"),n("1.2.840.113549.********","pbeWithSHAAnd128BitRC2-CBC"),n("1.2.840.113549.********","pbewithSHAAnd40BitRC2-CBC"),n("1.2.840.113549.2.7","hmacWithSHA1"),n("1.2.840.113549.2.8","hmacWithSHA224"),n("1.2.840.113549.2.9","hmacWithSHA256"),n("1.2.840.113549.2.10","hmacWithSHA384"),n("1.2.840.113549.2.11","hmacWithSHA512"),n("1.2.840.113549.3.7","des-EDE3-CBC"),n("2.16.840.*********.1.2","aes128-CBC"),n("2.16.840.*********.1.22","aes192-CBC"),n("2.16.840.*********.1.42","aes256-CBC"),n("*******","commonName"),n("*******","serialName"),n("*******","countryName"),n("*******","localityName"),n("*******","stateOrProvinceName"),n("*******","streetAddress"),n("********","organizationName"),n("********","organizationalUnitName"),n("********","description"),n("********","businessCategory"),n("********","postalCode"),n("*******.4.1.311.********","jurisdictionOfIncorporationStateOrProvinceName"),n("*******.4.1.311.********","jurisdictionOfIncorporationCountryName"),n("2.16.840.1.113730.1.1","nsCertType"),n("2.16.840.1.113730.1.13","nsComment"),a("********","authorityKeyIdentifier"),a("********","keyAttributes"),a("********","certificatePolicies"),a("********","keyUsageRestriction"),a("********","policyMapping"),a("********","subtreesConstraint"),a("********","subjectAltName"),a("********","issuerAltName"),a("********","subjectDirectoryAttributes"),a("********0","basicConstraints"),a("********1","nameConstraints"),a("*********","policyConstraints"),a("********3","basicConstraints"),n("*********","subjectKeyIdentifier"),n("*********","keyUsage"),a("*********","privateKeyUsagePeriod"),n("*********","subjectAltName"),n("********8","issuerAltName"),n("********9","basicConstraints"),a("*********","cRLNumber"),a("*********","cRLReason"),a("********2","expirationDate"),a("********3","instructionCode"),a("********4","invalidityDate"),a("********5","cRLDistributionPoints"),a("********6","issuingDistributionPoint"),a("********7","deltaCRLIndicator"),a("********8","issuingDistributionPoint"),a("********9","certificateIssuer"),a("********0","nameConstraints"),n("********1","cRLDistributionPoints"),n("********2","certificatePolicies"),a("********3","policyMappings"),a("********4","policyConstraints"),n("********5","authorityKeyIdentifier"),a("********6","policyConstraints"),n("********7","extKeyUsage"),a("********6","freshestCRL"),a("********4","inhibitAnyPolicy"),n("*******.4.1.11129.2.4.2","timestampList"),n("*******.5.5.7.1.1","authorityInfoAccess"),n("*******.5.5.7.3.1","serverAuth"),n("*******.5.5.7.3.2","clientAuth"),n("*******.5.5.7.3.3","codeSigning"),n("*******.5.5.7.3.4","emailProtection"),n("*******.5.5.7.3.8","timeStamping")},function(e,t,r){function n(e){for(var t=e.name+": ",r=[],n=function(e,t){return" "+t},a=0;a<e.values.length;++a)r.push(e.values[a].replace(/^(\S+\r\n)/,n));t+=r.join(",")+"\r\n";for(var i=0,s=-1,a=0;a<t.length;++a,++i)if(i>65&&-1!==s){var o=t[s];","===o?(++s,t=t.substr(0,s)+"\r\n "+t.substr(s)):t=t.substr(0,s)+"\r\n"+o+t.substr(s+1),i=a-s-1,s=-1,++a}else" "!==t[a]&&"\t"!==t[a]&&","!==t[a]||(s=a);return t}function a(e){return e.replace(/^\s+/,"")}var i=r(0);r(1);var s=e.exports=i.pem=i.pem||{};s.encode=function(e,t){t=t||{};var r,a="-----BEGIN "+e.type+"-----\r\n";if(e.procType&&(r={name:"Proc-Type",values:[String(e.procType.version),e.procType.type]},a+=n(r)),e.contentDomain&&(r={name:"Content-Domain",values:[e.contentDomain]},a+=n(r)),e.dekInfo&&(r={name:"DEK-Info",values:[e.dekInfo.algorithm]},e.dekInfo.parameters&&r.values.push(e.dekInfo.parameters),a+=n(r)),e.headers)for(var s=0;s<e.headers.length;++s)a+=n(e.headers[s]);return e.procType&&(a+="\r\n"),a+=i.util.encode64(e.body,t.maxline||64)+"\r\n",a+="-----END "+e.type+"-----\r\n"},s.decode=function(e){for(var t,r=[],n=/\s*-----BEGIN ([A-Z0-9- ]+)-----\r?\n?([\x21-\x7e\s]+?(?:\r?\n\r?\n))?([:A-Za-z0-9+\/=\s]+?)-----END \1-----/g,s=/([\x21-\x7e]+):\s*([\x21-\x7e\s^:]+)/,o=/\r?\n/;;){if(!(t=n.exec(e)))break;var c={type:t[1],procType:null,contentDomain:null,dekInfo:null,headers:[],body:i.util.decode64(t[3])};if(r.push(c),t[2]){for(var u=t[2].split(o),l=0;t&&l<u.length;){for(var p=u[l].replace(/\s+$/,""),f=l+1;f<u.length;++f){var h=u[f];if(!/\s/.test(h[0]))break;p+=h,l=f}if(t=p.match(s)){for(var d={name:t[1],values:[]},y=t[2].split(","),g=0;g<y.length;++g)d.values.push(a(y[g]));if(c.procType)if(c.contentDomain||"Content-Domain"!==d.name)if(c.dekInfo||"DEK-Info"!==d.name)c.headers.push(d);else{if(0===d.values.length)throw new Error('Invalid PEM formatted message. The "DEK-Info" header must have at least one subfield.');c.dekInfo={algorithm:y[0],parameters:y[1]||null}}else c.contentDomain=y[0]||"";else{if("Proc-Type"!==d.name)throw new Error('Invalid PEM formatted message. The first encapsulated header must be "Proc-Type".');if(2!==d.values.length)throw new Error('Invalid PEM formatted message. The "Proc-Type" header must have two subfields.');c.procType={version:y[0],type:y[1]}}}++l}if("ENCRYPTED"===c.procType&&!c.dekInfo)throw new Error('Invalid PEM formatted message. The "DEK-Info" header must be present if "Proc-Type" is "ENCRYPTED".')}}if(0===r.length)throw new Error("Invalid PEM formatted message.");return r}},function(e,t,r){var n=r(0);r(4),r(1),(e.exports=n.hmac=n.hmac||{}).create=function(){var e=null,t=null,r=null,a=null,i={};return i.start=function(i,s){if(null!==i)if("string"==typeof i){if(!((i=i.toLowerCase())in n.md.algorithms))throw new Error('Unknown hash algorithm "'+i+'"');t=n.md.algorithms[i].create()}else t=i;if(null===s)s=e;else{if("string"==typeof s)s=n.util.createBuffer(s);else if(n.util.isArray(s)){var o=s;s=n.util.createBuffer();for(var c=0;c<o.length;++c)s.putByte(o[c])}var u=s.length();u>t.blockLength&&(t.start(),t.update(s.bytes()),s=t.digest()),r=n.util.createBuffer(),a=n.util.createBuffer(),u=s.length();for(var c=0;c<u;++c){var o=s.at(c);r.putByte(54^o),a.putByte(92^o)}if(u<t.blockLength)for(var o=t.blockLength-u,c=0;c<o;++c)r.putByte(54),a.putByte(92);e=s,r=r.bytes(),a=a.bytes()}t.start(),t.update(r)},i.update=function(e){t.update(e)},i.getMac=function(){var e=t.digest().bytes();return t.start(),t.update(a),t.update(e),t.digest()},i.digest=i.getMac,i}},function(e,t,r){function n(){o=String.fromCharCode(128),o+=i.util.fillString(String.fromCharCode(0),64),c=!0}function a(e,t,r){for(var n,a,i,s,o,c,u,l,p=r.length();p>=64;){for(a=e.h0,i=e.h1,s=e.h2,o=e.h3,c=e.h4,l=0;l<16;++l)n=r.getInt32(),t[l]=n,u=o^i&(s^o),n=(a<<5|a>>>27)+u+c+1518500249+n,c=o,o=s,s=(i<<30|i>>>2)>>>0,i=a,a=n;for(;l<20;++l)n=t[l-3]^t[l-8]^t[l-14]^t[l-16],n=n<<1|n>>>31,t[l]=n,u=o^i&(s^o),n=(a<<5|a>>>27)+u+c+1518500249+n,c=o,o=s,s=(i<<30|i>>>2)>>>0,i=a,a=n;for(;l<32;++l)n=t[l-3]^t[l-8]^t[l-14]^t[l-16],n=n<<1|n>>>31,t[l]=n,u=i^s^o,n=(a<<5|a>>>27)+u+c+1859775393+n,c=o,o=s,s=(i<<30|i>>>2)>>>0,i=a,a=n;for(;l<40;++l)n=t[l-6]^t[l-16]^t[l-28]^t[l-32],n=n<<2|n>>>30,t[l]=n,u=i^s^o,n=(a<<5|a>>>27)+u+c+1859775393+n,c=o,o=s,s=(i<<30|i>>>2)>>>0,i=a,a=n;for(;l<60;++l)n=t[l-6]^t[l-16]^t[l-28]^t[l-32],n=n<<2|n>>>30,t[l]=n,u=i&s|o&(i^s),n=(a<<5|a>>>27)+u+c+2400959708+n,c=o,o=s,s=(i<<30|i>>>2)>>>0,i=a,a=n;for(;l<80;++l)n=t[l-6]^t[l-16]^t[l-28]^t[l-32],n=n<<2|n>>>30,t[l]=n,u=i^s^o,n=(a<<5|a>>>27)+u+c+3395469782+n,c=o,o=s,s=(i<<30|i>>>2)>>>0,i=a,a=n;e.h0=e.h0+a|0,e.h1=e.h1+i|0,e.h2=e.h2+s|0,e.h3=e.h3+o|0,e.h4=e.h4+c|0,p-=64}}var i=r(0);r(4),r(1);var s=e.exports=i.sha1=i.sha1||{};i.md.sha1=i.md.algorithms.sha1=s,s.create=function(){c||n();var e=null,t=i.util.createBuffer(),r=new Array(80),s={algorithm:"sha1",blockLength:64,digestLength:20,messageLength:0,fullMessageLength:null,messageLengthSize:8};return s.start=function(){s.messageLength=0,s.fullMessageLength=s.messageLength64=[];for(var r=s.messageLengthSize/4,n=0;n<r;++n)s.fullMessageLength.push(0);return t=i.util.createBuffer(),e={h0:1732584193,h1:4023233417,h2:2562383102,h3:271733878,h4:3285377520},s},s.start(),s.update=function(n,o){"utf8"===o&&(n=i.util.encodeUtf8(n));var c=n.length;s.messageLength+=c,c=[c/4294967296>>>0,c>>>0];for(var u=s.fullMessageLength.length-1;u>=0;--u)s.fullMessageLength[u]+=c[1],c[1]=c[0]+(s.fullMessageLength[u]/4294967296>>>0),s.fullMessageLength[u]=s.fullMessageLength[u]>>>0,c[0]=c[1]/4294967296>>>0;return t.putBytes(n),a(e,r,t),(t.read>2048||0===t.length())&&t.compact(),s},s.digest=function(){var n=i.util.createBuffer();n.putBytes(t.bytes());var c=s.fullMessageLength[s.fullMessageLength.length-1]+s.messageLengthSize,u=c&s.blockLength-1;n.putBytes(o.substr(0,s.blockLength-u));for(var l,p,f=8*s.fullMessageLength[0],h=0;h<s.fullMessageLength.length-1;++h)l=8*s.fullMessageLength[h+1],p=l/4294967296>>>0,f+=p,n.putInt32(f>>>0),f=l>>>0;n.putInt32(f);var d={h0:e.h0,h1:e.h1,h2:e.h2,h3:e.h3,h4:e.h4};a(d,r,n);var y=i.util.createBuffer();return y.putInt32(d.h0),y.putInt32(d.h1),y.putInt32(d.h2),y.putInt32(d.h3),y.putInt32(d.h4),y},s};var o=null,c=!1},function(e,t,r){var n=r(0);r(3),r(8),r(15),r(7),r(22),r(2),r(9),r(1);var a=function(e,t,r,a){var i=n.util.createBuffer(),s=e.length>>1,o=s+(1&e.length),c=e.substr(0,o),u=e.substr(s,o),l=n.util.createBuffer(),p=n.hmac.create();r=t+r;var f=Math.ceil(a/16),h=Math.ceil(a/20);p.start("MD5",c);var d=n.util.createBuffer();l.putBytes(r);for(var y=0;y<f;++y)p.start(null,null),p.update(l.getBytes()),l.putBuffer(p.digest()),p.start(null,null),p.update(l.bytes()+r),d.putBuffer(p.digest());p.start("SHA1",u);var g=n.util.createBuffer();l.clear(),l.putBytes(r);for(var y=0;y<h;++y)p.start(null,null),p.update(l.getBytes()),l.putBuffer(p.digest()),p.start(null,null),p.update(l.bytes()+r),g.putBuffer(p.digest());return i.putBytes(n.util.xorBytes(d.getBytes(),g.getBytes(),a)),i},i=function(e,t,r){var a=n.hmac.create();a.start("SHA1",e);var i=n.util.createBuffer();return i.putInt32(t[0]),i.putInt32(t[1]),i.putByte(r.type),i.putByte(r.version.major),i.putByte(r.version.minor),i.putInt16(r.length),i.putBytes(r.fragment.bytes()),a.update(i.getBytes()),a.digest().getBytes()},s=function(e,t,r){var a=!1;try{var i=e.deflate(t.fragment.getBytes());t.fragment=n.util.createBuffer(i),t.length=i.length,a=!0}catch(e){}return a},o=function(e,t,r){var a=!1;try{var i=e.inflate(t.fragment.getBytes());t.fragment=n.util.createBuffer(i),t.length=i.length,a=!0}catch(e){}return a},c=function(e,t){var r=0;switch(t){case 1:r=e.getByte();break;case 2:r=e.getInt16();break;case 3:r=e.getInt24();break;case 4:r=e.getInt32()}return n.util.createBuffer(e.getBytes(r))},u=function(e,t,r){e.putInt(r.length(),t<<3),e.putBuffer(r)},l={};l.Versions={TLS_1_0:{major:3,minor:1},TLS_1_1:{major:3,minor:2},TLS_1_2:{major:3,minor:3}},l.SupportedVersions=[l.Versions.TLS_1_1,l.Versions.TLS_1_0],l.Version=l.SupportedVersions[0],l.MaxFragment=15360,l.ConnectionEnd={server:0,client:1},l.PRFAlgorithm={tls_prf_sha256:0},l.BulkCipherAlgorithm={none:null,rc4:0,des3:1,aes:2},l.CipherType={stream:0,block:1,aead:2},l.MACAlgorithm={none:null,hmac_md5:0,hmac_sha1:1,hmac_sha256:2,hmac_sha384:3,hmac_sha512:4},l.CompressionMethod={none:0,deflate:1},l.ContentType={change_cipher_spec:20,alert:21,handshake:22,application_data:23,heartbeat:24},l.HandshakeType={hello_request:0,client_hello:1,server_hello:2,certificate:11,server_key_exchange:12,certificate_request:13,server_hello_done:14,certificate_verify:15,client_key_exchange:16,finished:20},l.Alert={},l.Alert.Level={warning:1,fatal:2},l.Alert.Description={close_notify:0,unexpected_message:10,bad_record_mac:20,decryption_failed:21,record_overflow:22,decompression_failure:30,handshake_failure:40,bad_certificate:42,unsupported_certificate:43,certificate_revoked:44,certificate_expired:45,certificate_unknown:46,illegal_parameter:47,unknown_ca:48,access_denied:49,decode_error:50,decrypt_error:51,export_restriction:60,protocol_version:70,insufficient_security:71,internal_error:80,user_canceled:90,no_renegotiation:100},l.HeartbeatMessageType={heartbeat_request:1,heartbeat_response:2},l.CipherSuites={},l.getCipherSuite=function(e){var t=null;for(var r in l.CipherSuites){var n=l.CipherSuites[r];if(n.id[0]===e.charCodeAt(0)&&n.id[1]===e.charCodeAt(1)){t=n;break}}return t},l.handleUnexpected=function(e,t){!e.open&&e.entity===l.ConnectionEnd.client||e.error(e,{message:"Unexpected message. Received TLS record out of order.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.unexpected_message}})},l.handleHelloRequest=function(e,t,r){!e.handshaking&&e.handshakes>0&&(l.queue(e,l.createAlert(e,{level:l.Alert.Level.warning,description:l.Alert.Description.no_renegotiation})),l.flush(e)),e.process()},l.parseHelloMessage=function(e,t,r){var a=null,i=e.entity===l.ConnectionEnd.client;if(r<38)e.error(e,{message:i?"Invalid ServerHello message. Message too short.":"Invalid ClientHello message. Message too short.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.illegal_parameter}});else{var s=t.fragment,o=s.length();if(a={version:{major:s.getByte(),minor:s.getByte()},random:n.util.createBuffer(s.getBytes(32)),session_id:c(s,1),extensions:[]},i?(a.cipher_suite=s.getBytes(2),a.compression_method=s.getByte()):(a.cipher_suites=c(s,2),a.compression_methods=c(s,1)),(o=r-(o-s.length()))>0){for(var u=c(s,2);u.length()>0;)a.extensions.push({type:[u.getByte(),u.getByte()],data:c(u,2)});if(!i)for(var p=0;p<a.extensions.length;++p){var f=a.extensions[p];if(0===f.type[0]&&0===f.type[1])for(var h=c(f.data,2);h.length()>0;){var d=h.getByte();if(0!==d)break;e.session.extensions.server_name.serverNameList.push(c(h,2).getBytes())}}}if(e.session.version&&(a.version.major!==e.session.version.major||a.version.minor!==e.session.version.minor))return e.error(e,{message:"TLS version change is disallowed during renegotiation.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.protocol_version}});if(i)e.session.cipherSuite=l.getCipherSuite(a.cipher_suite);else for(var y=n.util.createBuffer(a.cipher_suites.bytes());y.length()>0&&(e.session.cipherSuite=l.getCipherSuite(y.getBytes(2)),null===e.session.cipherSuite););if(null===e.session.cipherSuite)return e.error(e,{message:"No cipher suites in common.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.handshake_failure},cipherSuite:n.util.bytesToHex(a.cipher_suite)});e.session.compressionMethod=i?a.compression_method:l.CompressionMethod.none}return a},l.createSecurityParameters=function(e,t){var r=e.entity===l.ConnectionEnd.client,n=t.random.bytes(),a=r?e.session.sp.client_random:n,i=r?n:l.createRandom().getBytes();e.session.sp={entity:e.entity,prf_algorithm:l.PRFAlgorithm.tls_prf_sha256,bulk_cipher_algorithm:null,cipher_type:null,enc_key_length:null,block_length:null,fixed_iv_length:null,record_iv_length:null,mac_algorithm:null,mac_length:null,mac_key_length:null,compression_algorithm:e.session.compressionMethod,pre_master_secret:null,master_secret:null,client_random:a,server_random:i}},l.handleServerHello=function(e,t,r){var n=l.parseHelloMessage(e,t,r);if(!e.fail){if(!(n.version.minor<=e.version.minor))return e.error(e,{message:"Incompatible TLS version.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.protocol_version}});e.version.minor=n.version.minor,e.session.version=e.version;var a=n.session_id.bytes();a.length>0&&a===e.session.id?(e.expect=y,e.session.resuming=!0,e.session.sp.server_random=n.random.bytes()):(e.expect=p,e.session.resuming=!1,l.createSecurityParameters(e,n)),e.session.id=a,e.process()}},l.handleClientHello=function(e,t,r){var a=l.parseHelloMessage(e,t,r);if(!e.fail){var i=a.session_id.bytes(),s=null;if(e.sessionCache&&(s=e.sessionCache.getSession(i),null===s?i="":(s.version.major!==a.version.major||s.version.minor>a.version.minor)&&(s=null,i="")),0===i.length&&(i=n.random.getBytes(32)),e.session.id=i,e.session.clientHelloVersion=a.version,e.session.sp={},s)e.version=e.session.version=s.version,e.session.sp=s.sp;else{for(var o,c=1;c<l.SupportedVersions.length&&(o=l.SupportedVersions[c],!(o.minor<=a.version.minor));++c);e.version={major:o.major,minor:o.minor},e.session.version=e.version}null!==s?(e.expect=T,e.session.resuming=!0,e.session.sp.client_random=a.random.bytes()):(e.expect=!1!==e.verifyClient?C:E,e.session.resuming=!1,l.createSecurityParameters(e,a)),e.open=!0,l.queue(e,l.createRecord(e,{type:l.ContentType.handshake,data:l.createServerHello(e)})),e.session.resuming?(l.queue(e,l.createRecord(e,{type:l.ContentType.change_cipher_spec,data:l.createChangeCipherSpec()})),e.state.pending=l.createConnectionState(e),e.state.current.write=e.state.pending.write,l.queue(e,l.createRecord(e,{type:l.ContentType.handshake,data:l.createFinished(e)}))):(l.queue(e,l.createRecord(e,{type:l.ContentType.handshake,data:l.createCertificate(e)})),e.fail||(l.queue(e,l.createRecord(e,{type:l.ContentType.handshake,data:l.createServerKeyExchange(e)})),!1!==e.verifyClient&&l.queue(e,l.createRecord(e,{type:l.ContentType.handshake,data:l.createCertificateRequest(e)})),l.queue(e,l.createRecord(e,{type:l.ContentType.handshake,data:l.createServerHelloDone(e)})))),l.flush(e),e.process()}},l.handleCertificate=function(e,t,r){if(r<3)return e.error(e,{message:"Invalid Certificate message. Message too short.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.illegal_parameter}});var a,i,s=t.fragment,o={certificate_list:c(s,3)},u=[];try{for(;o.certificate_list.length()>0;)a=c(o.certificate_list,3),i=n.asn1.fromDer(a),a=n.pki.certificateFromAsn1(i,!0),u.push(a)}catch(t){return e.error(e,{message:"Could not parse certificate list.",cause:t,send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.bad_certificate}})}var p=e.entity===l.ConnectionEnd.client;!p&&!0!==e.verifyClient||0!==u.length?0===u.length?e.expect=p?f:E:(p?e.session.serverCertificate=u[0]:e.session.clientCertificate=u[0],l.verifyCertificateChain(e,u)&&(e.expect=p?f:E)):e.error(e,{message:p?"No server certificate provided.":"No client certificate provided.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.illegal_parameter}}),e.process()},l.handleServerKeyExchange=function(e,t,r){if(r>0)return e.error(e,{message:"Invalid key parameters. Only RSA is supported.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.unsupported_certificate}});e.expect=h,e.process()},l.handleClientKeyExchange=function(e,t,r){if(r<48)return e.error(e,{message:"Invalid key parameters. Only RSA is supported.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.unsupported_certificate}});var a=t.fragment,i={enc_pre_master_secret:c(a,2).getBytes()},s=null;if(e.getPrivateKey)try{s=e.getPrivateKey(e,e.session.serverCertificate),s=n.pki.privateKeyFromPem(s)}catch(t){e.error(e,{message:"Could not get private key.",cause:t,send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.internal_error}})}if(null===s)return e.error(e,{message:"No private key set.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.internal_error}});try{var o=e.session.sp;o.pre_master_secret=s.decrypt(i.enc_pre_master_secret);var u=e.session.clientHelloVersion;if(u.major!==o.pre_master_secret.charCodeAt(0)||u.minor!==o.pre_master_secret.charCodeAt(1))throw new Error("TLS version rollback attack detected.")}catch(e){o.pre_master_secret=n.random.getBytes(48)}e.expect=T,null!==e.session.clientCertificate&&(e.expect=S),e.process()},l.handleCertificateRequest=function(e,t,r){if(r<3)return e.error(e,{message:"Invalid CertificateRequest. Message too short.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.illegal_parameter}});var n=t.fragment,a={certificate_types:c(n,1),certificate_authorities:c(n,2)};e.session.certificateRequest=a,e.expect=d,e.process()},l.handleCertificateVerify=function(e,t,r){if(r<2)return e.error(e,{message:"Invalid CertificateVerify. Message too short.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.illegal_parameter}});var a=t.fragment;a.read-=4;var i=a.bytes();a.read+=4;var s={signature:c(a,2).getBytes()},o=n.util.createBuffer();o.putBuffer(e.session.md5.digest()),o.putBuffer(e.session.sha1.digest()),o=o.getBytes();try{if(!e.session.clientCertificate.publicKey.verify(o,s.signature,"NONE"))throw new Error("CertificateVerify signature does not match.");e.session.md5.update(i),e.session.sha1.update(i)}catch(t){return e.error(e,{message:"Bad signature in CertificateVerify.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.handshake_failure}})}e.expect=T,e.process()},l.handleServerHelloDone=function(e,t,r){if(r>0)return e.error(e,{message:"Invalid ServerHelloDone message. Invalid length.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.record_overflow}});if(null===e.serverCertificate){var a={message:"No server certificate provided. Not enough security.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.insufficient_security}},i=e.verify(e,a.alert.description,0,[]);if(!0!==i)return(i||0===i)&&("object"!=typeof i||n.util.isArray(i)?"number"==typeof i&&(a.alert.description=i):(i.message&&(a.message=i.message),i.alert&&(a.alert.description=i.alert))),e.error(e,a)}null!==e.session.certificateRequest&&(t=l.createRecord(e,{type:l.ContentType.handshake,data:l.createCertificate(e)}),l.queue(e,t)),t=l.createRecord(e,{type:l.ContentType.handshake,data:l.createClientKeyExchange(e)}),l.queue(e,t),e.expect=m;var s=function(e,t){null!==e.session.certificateRequest&&null!==e.session.clientCertificate&&l.queue(e,l.createRecord(e,{type:l.ContentType.handshake,data:l.createCertificateVerify(e,t)})),l.queue(e,l.createRecord(e,{type:l.ContentType.change_cipher_spec,data:l.createChangeCipherSpec()})),e.state.pending=l.createConnectionState(e),e.state.current.write=e.state.pending.write,l.queue(e,l.createRecord(e,{type:l.ContentType.handshake,data:l.createFinished(e)})),e.expect=y,l.flush(e),e.process()};if(null===e.session.certificateRequest||null===e.session.clientCertificate)return s(e,null);l.getClientSignature(e,s)},l.handleChangeCipherSpec=function(e,t){if(1!==t.fragment.getByte())return e.error(e,{message:"Invalid ChangeCipherSpec message received.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.illegal_parameter}});var r=e.entity===l.ConnectionEnd.client;(e.session.resuming&&r||!e.session.resuming&&!r)&&(e.state.pending=l.createConnectionState(e)),e.state.current.read=e.state.pending.read,(!e.session.resuming&&r||e.session.resuming&&!r)&&(e.state.pending=null),e.expect=r?g:b,e.process()},l.handleFinished=function(e,t,r){var i=t.fragment;i.read-=4;var s=i.bytes();i.read+=4;var o=t.fragment.getBytes();i=n.util.createBuffer(),i.putBuffer(e.session.md5.digest()),i.putBuffer(e.session.sha1.digest());var c=e.entity===l.ConnectionEnd.client,u=c?"server finished":"client finished",p=e.session.sp;if(i=a(p.master_secret,u,i.getBytes(),12),i.getBytes()!==o)return e.error(e,{message:"Invalid verify_data in Finished message.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.decrypt_error}});e.session.md5.update(s),e.session.sha1.update(s),(e.session.resuming&&c||!e.session.resuming&&!c)&&(l.queue(e,l.createRecord(e,{type:l.ContentType.change_cipher_spec,data:l.createChangeCipherSpec()})),e.state.current.write=e.state.pending.write,e.state.pending=null,l.queue(e,l.createRecord(e,{type:l.ContentType.handshake,data:l.createFinished(e)}))),e.expect=c?v:I,e.handshaking=!1,++e.handshakes,e.peerCertificate=c?e.session.serverCertificate:e.session.clientCertificate,l.flush(e),e.isConnected=!0,e.connected(e),e.process()},l.handleAlert=function(e,t){var r,n=t.fragment,a={level:n.getByte(),description:n.getByte()};switch(a.description){case l.Alert.Description.close_notify:r="Connection closed.";break;case l.Alert.Description.unexpected_message:r="Unexpected message.";break;case l.Alert.Description.bad_record_mac:r="Bad record MAC.";break;case l.Alert.Description.decryption_failed:r="Decryption failed.";break;case l.Alert.Description.record_overflow:r="Record overflow.";break;case l.Alert.Description.decompression_failure:r="Decompression failed.";break;case l.Alert.Description.handshake_failure:r="Handshake failure.";break;case l.Alert.Description.bad_certificate:r="Bad certificate.";break;case l.Alert.Description.unsupported_certificate:r="Unsupported certificate.";break;case l.Alert.Description.certificate_revoked:r="Certificate revoked.";break;case l.Alert.Description.certificate_expired:r="Certificate expired.";break;case l.Alert.Description.certificate_unknown:r="Certificate unknown.";break;case l.Alert.Description.illegal_parameter:r="Illegal parameter.";break;case l.Alert.Description.unknown_ca:r="Unknown certificate authority.";break;case l.Alert.Description.access_denied:r="Access denied.";break;case l.Alert.Description.decode_error:r="Decode error.";break;case l.Alert.Description.decrypt_error:r="Decrypt error.";break;case l.Alert.Description.export_restriction:r="Export restriction.";break;case l.Alert.Description.protocol_version:r="Unsupported protocol version.";break;case l.Alert.Description.insufficient_security:r="Insufficient security.";break;case l.Alert.Description.internal_error:r="Internal error.";break;case l.Alert.Description.user_canceled:r="User canceled.";break;case l.Alert.Description.no_renegotiation:r="Renegotiation not supported.";break;default:r="Unknown error."}if(a.description===l.Alert.Description.close_notify)return e.close();e.error(e,{message:r,send:!1,origin:e.entity===l.ConnectionEnd.client?"server":"client",alert:a}),e.process()},l.handleHandshake=function(e,t){var r=t.fragment,a=r.getByte(),i=r.getInt24();if(i>r.length())return e.fragmented=t,t.fragment=n.util.createBuffer(),r.read-=4,e.process();e.fragmented=null,r.read-=4;var s=r.bytes(i+4);r.read+=4,a in K[e.entity][e.expect]?(e.entity!==l.ConnectionEnd.server||e.open||e.fail||(e.handshaking=!0,e.session={version:null,extensions:{server_name:{serverNameList:[]}},cipherSuite:null,compressionMethod:null,serverCertificate:null,clientCertificate:null,md5:n.md.md5.create(),sha1:n.md.sha1.create()}),a!==l.HandshakeType.hello_request&&a!==l.HandshakeType.certificate_verify&&a!==l.HandshakeType.finished&&(e.session.md5.update(s),e.session.sha1.update(s)),K[e.entity][e.expect][a](e,t,i)):l.handleUnexpected(e,t)},l.handleApplicationData=function(e,t){e.data.putBuffer(t.fragment),e.dataReady(e),e.process()},l.handleHeartbeat=function(e,t){var r=t.fragment,a=r.getByte(),i=r.getInt16(),s=r.getBytes(i);if(a===l.HeartbeatMessageType.heartbeat_request){if(e.handshaking||i>s.length)return e.process();l.queue(e,l.createRecord(e,{type:l.ContentType.heartbeat,data:l.createHeartbeat(l.HeartbeatMessageType.heartbeat_response,s)})),l.flush(e)}else if(a===l.HeartbeatMessageType.heartbeat_response){if(s!==e.expectedHeartbeatPayload)return e.process();e.heartbeatReceived&&e.heartbeatReceived(e,n.util.createBuffer(s))}e.process()};var p=1,f=2,h=3,d=4,y=5,g=6,v=7,m=8,C=1,E=2,S=3,T=4,b=5,I=6,A=l.handleUnexpected,B=l.handleChangeCipherSpec,k=l.handleAlert,N=l.handleHandshake,w=l.handleApplicationData,R=l.handleHeartbeat,L=[];L[l.ConnectionEnd.client]=[[A,k,N,A,R],[A,k,N,A,R],[A,k,N,A,R],[A,k,N,A,R],[A,k,N,A,R],[B,k,A,A,R],[A,k,N,A,R],[A,k,N,w,R],[A,k,N,A,R]],L[l.ConnectionEnd.server]=[[A,k,N,A,R],[A,k,N,A,R],[A,k,N,A,R],[A,k,N,A,R],[B,k,A,A,R],[A,k,N,A,R],[A,k,N,w,R],[A,k,N,A,R]];var _=l.handleHelloRequest,U=l.handleServerHello,D=l.handleCertificate,P=l.handleServerKeyExchange,O=l.handleCertificateRequest,V=l.handleServerHelloDone,x=l.handleFinished,K=[];K[l.ConnectionEnd.client]=[[A,A,U,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A],[_,A,A,A,A,A,A,A,A,A,A,D,P,O,V,A,A,A,A,A,A],[_,A,A,A,A,A,A,A,A,A,A,A,P,O,V,A,A,A,A,A,A],[_,A,A,A,A,A,A,A,A,A,A,A,A,O,V,A,A,A,A,A,A],[_,A,A,A,A,A,A,A,A,A,A,A,A,A,V,A,A,A,A,A,A],[_,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A],[_,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,x],[_,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A],[_,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A]];var M=l.handleClientHello,F=l.handleClientKeyExchange,q=l.handleCertificateVerify;K[l.ConnectionEnd.server]=[[A,M,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A],[A,A,A,A,A,A,A,A,A,A,A,D,A,A,A,A,A,A,A,A,A],[A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,F,A,A,A,A],[A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,q,A,A,A,A,A],[A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A],[A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,x],[A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A],[A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A,A]],l.generateKeys=function(e,t){var r=a,n=t.client_random+t.server_random;e.session.resuming||(t.master_secret=r(t.pre_master_secret,"master secret",n,48).bytes(),t.pre_master_secret=null),n=t.server_random+t.client_random;var i=2*t.mac_key_length+2*t.enc_key_length,s=e.version.major===l.Versions.TLS_1_0.major&&e.version.minor===l.Versions.TLS_1_0.minor;s&&(i+=2*t.fixed_iv_length);var o=r(t.master_secret,"key expansion",n,i),c={client_write_MAC_key:o.getBytes(t.mac_key_length),server_write_MAC_key:o.getBytes(t.mac_key_length),client_write_key:o.getBytes(t.enc_key_length),server_write_key:o.getBytes(t.enc_key_length)};return s&&(c.client_write_IV=o.getBytes(t.fixed_iv_length),c.server_write_IV=o.getBytes(t.fixed_iv_length)),c},l.createConnectionState=function(e){var t=e.entity===l.ConnectionEnd.client,r=function(){var e={sequenceNumber:[0,0],macKey:null,macLength:0,macFunction:null,cipherState:null,cipherFunction:function(e){return!0},compressionState:null,compressFunction:function(e){return!0},updateSequenceNumber:function(){4294967295===e.sequenceNumber[1]?(e.sequenceNumber[1]=0,++e.sequenceNumber[0]):++e.sequenceNumber[1]}};return e},n={read:r(),write:r()};if(n.read.update=function(e,t){return n.read.cipherFunction(t,n.read)?n.read.compressFunction(e,t,n.read)||e.error(e,{message:"Could not decompress record.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.decompression_failure}}):e.error(e,{message:"Could not decrypt record or bad MAC.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.bad_record_mac}}),!e.fail},n.write.update=function(e,t){return n.write.compressFunction(e,t,n.write)?n.write.cipherFunction(t,n.write)||e.error(e,{message:"Could not encrypt record.",send:!1,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.internal_error}}):e.error(e,{message:"Could not compress record.",send:!1,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.internal_error}}),!e.fail},e.session){var a=e.session.sp;switch(e.session.cipherSuite.initSecurityParameters(a),a.keys=l.generateKeys(e,a),n.read.macKey=t?a.keys.server_write_MAC_key:a.keys.client_write_MAC_key,n.write.macKey=t?a.keys.client_write_MAC_key:a.keys.server_write_MAC_key,e.session.cipherSuite.initConnectionState(n,e,a),a.compression_algorithm){case l.CompressionMethod.none:break;case l.CompressionMethod.deflate:n.read.compressFunction=o,n.write.compressFunction=s;break;default:throw new Error("Unsupported compression algorithm.")}}return n},l.createRandom=function(){var e=new Date,t=+e+6e4*e.getTimezoneOffset(),r=n.util.createBuffer();return r.putInt32(t),r.putBytes(n.random.getBytes(28)),r},l.createRecord=function(e,t){return t.data?{type:t.type,version:{major:e.version.major,minor:e.version.minor},length:t.data.length(),fragment:t.data}:null},l.createAlert=function(e,t){var r=n.util.createBuffer();return r.putByte(t.level),r.putByte(t.description),l.createRecord(e,{type:l.ContentType.alert,data:r})},l.createClientHello=function(e){e.session.clientHelloVersion={major:e.version.major,minor:e.version.minor};for(var t=n.util.createBuffer(),r=0;r<e.cipherSuites.length;++r){var a=e.cipherSuites[r];t.putByte(a.id[0]),t.putByte(a.id[1])}var i=t.length(),s=n.util.createBuffer();s.putByte(l.CompressionMethod.none);var o=s.length(),c=n.util.createBuffer();if(e.virtualHost){var p=n.util.createBuffer();p.putByte(0),p.putByte(0);var f=n.util.createBuffer();f.putByte(0),u(f,2,n.util.createBuffer(e.virtualHost));var h=n.util.createBuffer();u(h,2,f),u(p,2,h),c.putBuffer(p)}var d=c.length();d>0&&(d+=2);var y=e.session.id,g=y.length+1+2+4+28+2+i+1+o+d,v=n.util.createBuffer();return v.putByte(l.HandshakeType.client_hello),v.putInt24(g),v.putByte(e.version.major),v.putByte(e.version.minor),v.putBytes(e.session.sp.client_random),u(v,1,n.util.createBuffer(y)),u(v,2,t),u(v,1,s),d>0&&u(v,2,c),v},l.createServerHello=function(e){var t=e.session.id,r=t.length+1+2+4+28+2+1,a=n.util.createBuffer();return a.putByte(l.HandshakeType.server_hello),a.putInt24(r),a.putByte(e.version.major),a.putByte(e.version.minor),a.putBytes(e.session.sp.server_random),u(a,1,n.util.createBuffer(t)),a.putByte(e.session.cipherSuite.id[0]),a.putByte(e.session.cipherSuite.id[1]),a.putByte(e.session.compressionMethod),a},l.createCertificate=function(e){var t=e.entity===l.ConnectionEnd.client,r=null;if(e.getCertificate){var a;a=t?e.session.certificateRequest:e.session.extensions.server_name.serverNameList,r=e.getCertificate(e,a)}var i=n.util.createBuffer();if(null!==r)try{n.util.isArray(r)||(r=[r]);for(var s=null,o=0;o<r.length;++o){var c=n.pem.decode(r[o])[0];if("CERTIFICATE"!==c.type&&"X509 CERTIFICATE"!==c.type&&"TRUSTED CERTIFICATE"!==c.type){var p=new Error('Could not convert certificate from PEM; PEM header type is not "CERTIFICATE", "X509 CERTIFICATE", or "TRUSTED CERTIFICATE".');throw p.headerType=c.type,p}if(c.procType&&"ENCRYPTED"===c.procType.type)throw new Error("Could not convert certificate from PEM; PEM is encrypted.");var f=n.util.createBuffer(c.body);null===s&&(s=n.asn1.fromDer(f.bytes(),!1));var h=n.util.createBuffer();u(h,3,f),i.putBuffer(h)}r=n.pki.certificateFromAsn1(s),t?e.session.clientCertificate=r:e.session.serverCertificate=r}catch(t){return e.error(e,{message:"Could not send certificate list.",cause:t,send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.bad_certificate}})}var d=3+i.length(),y=n.util.createBuffer();return y.putByte(l.HandshakeType.certificate),y.putInt24(d),u(y,3,i),y},l.createClientKeyExchange=function(e){var t=n.util.createBuffer();t.putByte(e.session.clientHelloVersion.major),t.putByte(e.session.clientHelloVersion.minor),t.putBytes(n.random.getBytes(46));var r=e.session.sp;r.pre_master_secret=t.getBytes(),t=e.session.serverCertificate.publicKey.encrypt(r.pre_master_secret);var a=t.length+2,i=n.util.createBuffer();return i.putByte(l.HandshakeType.client_key_exchange),i.putInt24(a),i.putInt16(t.length),i.putBytes(t),i},l.createServerKeyExchange=function(e){var t=n.util.createBuffer();return t},l.getClientSignature=function(e,t){var r=n.util.createBuffer();r.putBuffer(e.session.md5.digest()),r.putBuffer(e.session.sha1.digest()),r=r.getBytes(),e.getSignature=e.getSignature||function(e,t,r){var a=null;if(e.getPrivateKey)try{a=e.getPrivateKey(e,e.session.clientCertificate),a=n.pki.privateKeyFromPem(a)}catch(t){e.error(e,{message:"Could not get private key.",cause:t,send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.internal_error}})}null===a?e.error(e,{message:"No private key set.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.internal_error}}):t=a.sign(t,null),r(e,t)},e.getSignature(e,r,t)},l.createCertificateVerify=function(e,t){var r=t.length+2,a=n.util.createBuffer();return a.putByte(l.HandshakeType.certificate_verify),a.putInt24(r),a.putInt16(t.length),a.putBytes(t),a},l.createCertificateRequest=function(e){var t=n.util.createBuffer();t.putByte(1);var r=n.util.createBuffer();for(var a in e.caStore.certs){var i=e.caStore.certs[a],s=n.pki.distinguishedNameToAsn1(i.subject),o=n.asn1.toDer(s);r.putInt16(o.length()),r.putBuffer(o)}var c=1+t.length()+2+r.length(),p=n.util.createBuffer();return p.putByte(l.HandshakeType.certificate_request),p.putInt24(c),u(p,1,t),u(p,2,r),p},l.createServerHelloDone=function(e){var t=n.util.createBuffer();return t.putByte(l.HandshakeType.server_hello_done),t.putInt24(0),t},l.createChangeCipherSpec=function(){var e=n.util.createBuffer();return e.putByte(1),e},l.createFinished=function(e){var t=n.util.createBuffer();t.putBuffer(e.session.md5.digest()),t.putBuffer(e.session.sha1.digest());var r=e.entity===l.ConnectionEnd.client,i=e.session.sp,s=a,o=r?"client finished":"server finished";t=s(i.master_secret,o,t.getBytes(),12);var c=n.util.createBuffer();return c.putByte(l.HandshakeType.finished),c.putInt24(t.length()),c.putBuffer(t),c},l.createHeartbeat=function(e,t,r){void 0===r&&(r=t.length);var a=n.util.createBuffer();a.putByte(e),a.putInt16(r),a.putBytes(t);var i=a.length(),s=Math.max(16,i-r-3);return a.putBytes(n.random.getBytes(s)),a},l.queue=function(e,t){if(t&&(0!==t.fragment.length()||t.type!==l.ContentType.handshake&&t.type!==l.ContentType.alert&&t.type!==l.ContentType.change_cipher_spec)){if(t.type===l.ContentType.handshake){var r=t.fragment.bytes();e.session.md5.update(r),e.session.sha1.update(r),r=null}var a;if(t.fragment.length()<=l.MaxFragment)a=[t];else{a=[];for(var i=t.fragment.bytes();i.length>l.MaxFragment;)a.push(l.createRecord(e,{type:t.type,data:n.util.createBuffer(i.slice(0,l.MaxFragment))})),i=i.slice(l.MaxFragment);i.length>0&&a.push(l.createRecord(e,{type:t.type,data:n.util.createBuffer(i)}))}for(var s=0;s<a.length&&!e.fail;++s){var o=a[s];e.state.current.write.update(e,o)&&e.records.push(o)}}},l.flush=function(e){for(var t=0;t<e.records.length;++t){var r=e.records[t];e.tlsData.putByte(r.type),e.tlsData.putByte(r.version.major),e.tlsData.putByte(r.version.minor),e.tlsData.putInt16(r.fragment.length()),e.tlsData.putBuffer(e.records[t].fragment)}return e.records=[],e.tlsDataReady(e)};var H=function(e){switch(e){case!0:return!0;case n.pki.certificateError.bad_certificate:return l.Alert.Description.bad_certificate;case n.pki.certificateError.unsupported_certificate:return l.Alert.Description.unsupported_certificate;case n.pki.certificateError.certificate_revoked:return l.Alert.Description.certificate_revoked;case n.pki.certificateError.certificate_expired:return l.Alert.Description.certificate_expired;case n.pki.certificateError.certificate_unknown:return l.Alert.Description.certificate_unknown;case n.pki.certificateError.unknown_ca:return l.Alert.Description.unknown_ca;default:return l.Alert.Description.bad_certificate}},j=function(e){switch(e){case!0:return!0;case l.Alert.Description.bad_certificate:return n.pki.certificateError.bad_certificate;case l.Alert.Description.unsupported_certificate:return n.pki.certificateError.unsupported_certificate;case l.Alert.Description.certificate_revoked:return n.pki.certificateError.certificate_revoked;case l.Alert.Description.certificate_expired:return n.pki.certificateError.certificate_expired;case l.Alert.Description.certificate_unknown:return n.pki.certificateError.certificate_unknown;case l.Alert.Description.unknown_ca:return n.pki.certificateError.unknown_ca;default:return n.pki.certificateError.bad_certificate}};l.verifyCertificateChain=function(e,t){try{var r={};for(var a in e.verifyOptions)r[a]=e.verifyOptions[a];r.verify=function(t,r,a){var i=(H(t),e.verify(e,t,r,a));if(!0!==i){if("object"==typeof i&&!n.util.isArray(i)){var s=new Error("The application rejected the certificate.");throw s.send=!0,s.alert={level:l.Alert.Level.fatal,description:l.Alert.Description.bad_certificate},i.message&&(s.message=i.message),i.alert&&(s.alert.description=i.alert),s}i!==t&&(i=j(i))}return i},n.pki.verifyCertificateChain(e.caStore,t,r)}catch(t){var i=t;("object"!=typeof i||n.util.isArray(i))&&(i={send:!0,alert:{level:l.Alert.Level.fatal,description:H(t)}}),"send"in i||(i.send=!0),"alert"in i||(i.alert={level:l.Alert.Level.fatal,description:H(i.error)}),e.error(e,i)}return!e.fail},l.createSessionCache=function(e,t){var r=null;if(e&&e.getSession&&e.setSession&&e.order)r=e;else{r={},r.cache=e||{},r.capacity=Math.max(t||100,1),r.order=[];for(var a in e)r.order.length<=t?r.order.push(a):delete e[a];r.getSession=function(e){var t=null,a=null;if(e?a=n.util.bytesToHex(e):r.order.length>0&&(a=r.order[0]),null!==a&&a in r.cache){t=r.cache[a],delete r.cache[a];for(var i in r.order)if(r.order[i]===a){r.order.splice(i,1);break}}return t},r.setSession=function(e,t){if(r.order.length===r.capacity){var a=r.order.shift();delete r.cache[a]}var a=n.util.bytesToHex(e);r.order.push(a),r.cache[a]=t}}return r},l.createConnection=function(e){var t=null;t=e.caStore?n.util.isArray(e.caStore)?n.pki.createCaStore(e.caStore):e.caStore:n.pki.createCaStore();var r=e.cipherSuites||null;if(null===r){r=[];for(var a in l.CipherSuites)r.push(l.CipherSuites[a])}var i=e.server?l.ConnectionEnd.server:l.ConnectionEnd.client,s=e.sessionCache?l.createSessionCache(e.sessionCache):null,o={version:{major:l.Version.major,minor:l.Version.minor},entity:i,sessionId:e.sessionId,caStore:t,sessionCache:s,cipherSuites:r,connected:e.connected,virtualHost:e.virtualHost||null,verifyClient:e.verifyClient||!1,verify:e.verify||function(e,t,r,n){return t},verifyOptions:e.verifyOptions||{},getCertificate:e.getCertificate||null,getPrivateKey:e.getPrivateKey||null,getSignature:e.getSignature||null,input:n.util.createBuffer(),tlsData:n.util.createBuffer(),data:n.util.createBuffer(),tlsDataReady:e.tlsDataReady,dataReady:e.dataReady,heartbeatReceived:e.heartbeatReceived,closed:e.closed,error:function(t,r){r.origin=r.origin||(t.entity===l.ConnectionEnd.client?"client":"server"),r.send&&(l.queue(t,l.createAlert(t,r.alert)),l.flush(t));var n=!1!==r.fatal;n&&(t.fail=!0),e.error(t,r),n&&t.close(!1)},deflate:e.deflate||null,inflate:e.inflate||null};o.reset=function(e){o.version={major:l.Version.major,minor:l.Version.minor},o.record=null,o.session=null,o.peerCertificate=null,o.state={pending:null,current:null},o.expect=(o.entity,l.ConnectionEnd.client,0),o.fragmented=null,o.records=[],o.open=!1,o.handshakes=0,o.handshaking=!1,o.isConnected=!1,o.fail=!(e||void 0===e),o.input.clear(),o.tlsData.clear(),o.data.clear(),o.state.current=l.createConnectionState(o)},o.reset();var c=function(e,t){var r=t.type-l.ContentType.change_cipher_spec,n=L[e.entity][e.expect];r in n?n[r](e,t):l.handleUnexpected(e,t)},u=function(e){var t=0,r=e.input,a=r.length();if(a<5)t=5-a;else{e.record={type:r.getByte(),version:{major:r.getByte(),minor:r.getByte()},length:r.getInt16(),fragment:n.util.createBuffer(),ready:!1};var i=e.record.version.major===e.version.major;i&&e.session&&e.session.version&&(i=e.record.version.minor===e.version.minor),i||e.error(e,{message:"Incompatible TLS version.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.protocol_version}})}return t},p=function(e){var t=0,r=e.input,n=r.length();if(n<e.record.length)t=e.record.length-n;else{e.record.fragment.putBytes(r.getBytes(e.record.length)),r.compact();e.state.current.read.update(e,e.record)&&(null!==e.fragmented&&(e.fragmented.type===e.record.type?(e.fragmented.fragment.putBuffer(e.record.fragment),e.record=e.fragmented):e.error(e,{message:"Invalid fragmented record.",send:!0,alert:{level:l.Alert.Level.fatal,description:l.Alert.Description.unexpected_message}})),e.record.ready=!0)}return t};return o.handshake=function(e){if(o.entity!==l.ConnectionEnd.client)o.error(o,{message:"Cannot initiate handshake as a server.",fatal:!1});else if(o.handshaking)o.error(o,{message:"Handshake already in progress.",fatal:!1});else{o.fail&&!o.open&&0===o.handshakes&&(o.fail=!1),o.handshaking=!0,e=e||"";var t=null;e.length>0&&(o.sessionCache&&(t=o.sessionCache.getSession(e)),null===t&&(e="")),0===e.length&&o.sessionCache&&null!==(t=o.sessionCache.getSession())&&(e=t.id),o.session={id:e,version:null,cipherSuite:null,compressionMethod:null,serverCertificate:null,certificateRequest:null,clientCertificate:null,sp:{},md5:n.md.md5.create(),sha1:n.md.sha1.create()},t&&(o.version=t.version,o.session.sp=t.sp),o.session.sp.client_random=l.createRandom().getBytes(),o.open=!0,l.queue(o,l.createRecord(o,{type:l.ContentType.handshake,data:l.createClientHello(o)})),l.flush(o)}},o.process=function(e){var t=0;return e&&o.input.putBytes(e),o.fail||(null!==o.record&&o.record.ready&&o.record.fragment.isEmpty()&&(o.record=null),null===o.record&&(t=u(o)),o.fail||null===o.record||o.record.ready||(t=p(o)),!o.fail&&null!==o.record&&o.record.ready&&c(o,o.record)),t},o.prepare=function(e){return l.queue(o,l.createRecord(o,{type:l.ContentType.application_data,data:n.util.createBuffer(e)})),l.flush(o)},o.prepareHeartbeatRequest=function(e,t){return e instanceof n.util.ByteBuffer&&(e=e.bytes()),void 0===t&&(t=e.length),o.expectedHeartbeatPayload=e,l.queue(o,l.createRecord(o,{type:l.ContentType.heartbeat,data:l.createHeartbeat(l.HeartbeatMessageType.heartbeat_request,e,t)})),l.flush(o)},o.close=function(e){if(!o.fail&&o.sessionCache&&o.session){var t={id:o.session.id,version:o.session.version,sp:o.session.sp};t.sp.keys=null,o.sessionCache.setSession(t.id,t)}o.open&&(o.open=!1,o.input.clear(),(o.isConnected||o.handshaking)&&(o.isConnected=o.handshaking=!1,l.queue(o,l.createAlert(o,{level:l.Alert.Level.warning,description:l.Alert.Description.close_notify})),l.flush(o)),o.closed(o)),o.reset(e)},o},e.exports=n.tls=n.tls||{};for(var G in l)"function"!=typeof l[G]&&(n.tls[G]=l[G]);n.tls.prf_tls1=a,n.tls.hmac_sha1=i,n.tls.createSessionCache=l.createSessionCache,n.tls.createConnection=l.createConnection},function(e,t,r){function n(e,t){var r=function(){return new o.des.Algorithm(e,t)};o.cipher.registerAlgorithm(e,r)}function a(e){for(var t,r=[0,4,536870912,536870916,65536,65540,536936448,536936452,512,516,536871424,536871428,66048,66052,536936960,536936964],n=[0,1,1048576,1048577,67108864,67108865,68157440,68157441,256,257,1048832,1048833,67109120,67109121,68157696,68157697],a=[0,8,2048,2056,16777216,16777224,16779264,16779272,0,8,2048,2056,16777216,16777224,16779264,16779272],i=[0,2097152,134217728,136314880,8192,2105344,134225920,136323072,131072,2228224,134348800,136445952,139264,2236416,134356992,136454144],s=[0,262144,16,262160,0,262144,16,262160,4096,266240,4112,266256,4096,266240,4112,266256],o=[0,1024,32,1056,0,1024,32,1056,33554432,33555456,33554464,33555488,33554432,33555456,33554464,33555488],c=[0,268435456,524288,268959744,2,268435458,524290,268959746,0,268435456,524288,268959744,2,268435458,524290,268959746],u=[0,65536,2048,67584,536870912,536936448,536872960,536938496,131072,196608,133120,198656,537001984,537067520,537004032,537069568],l=[0,262144,0,262144,2,262146,2,262146,33554432,33816576,33554432,33816576,33554434,33816578,33554434,33816578],p=[0,268435456,8,268435464,0,268435456,8,268435464,1024,268436480,1032,268436488,1024,268436480,1032,268436488],f=[0,32,0,32,1048576,1048608,1048576,1048608,8192,8224,8192,8224,1056768,1056800,1056768,1056800],h=[0,16777216,512,16777728,2097152,18874368,2097664,18874880,67108864,83886080,67109376,83886592,69206016,85983232,69206528,85983744],d=[0,4096,134217728,134221824,524288,528384,134742016,134746112,16,4112,134217744,134221840,524304,528400,134742032,134746128],y=[0,4,256,260,0,4,256,260,1,5,257,261,1,5,257,261],g=e.length()>8?3:1,v=[],m=[0,0,1,1,1,1,1,1,0,1,1,1,1,1,1,0],C=0,E=0;E<g;E++){var S=e.getInt32(),T=e.getInt32();t=252645135&(S>>>4^T),T^=t,S^=t<<4,t=65535&(T>>>-16^S),S^=t,T^=t<<-16,t=858993459&(S>>>2^T),T^=t,S^=t<<2,t=65535&(T>>>-16^S),S^=t,T^=t<<-16,t=1431655765&(S>>>1^T),T^=t,S^=t<<1,t=16711935&(T>>>8^S),S^=t,T^=t<<8,t=1431655765&(S>>>1^T),T^=t,S^=t<<1,t=S<<8|T>>>20&240,S=T<<24|T<<8&16711680|T>>>8&65280|T>>>24&240,T=t;for(var b=0;b<m.length;++b){m[b]?(S=S<<2|S>>>26,T=T<<2|T>>>26):(S=S<<1|S>>>27,T=T<<1|T>>>27),S&=-15,T&=-15;var I=r[S>>>28]|n[S>>>24&15]|a[S>>>20&15]|i[S>>>16&15]|s[S>>>12&15]|o[S>>>8&15]|c[S>>>4&15],A=u[T>>>28]|l[T>>>24&15]|p[T>>>20&15]|f[T>>>16&15]|h[T>>>12&15]|d[T>>>8&15]|y[T>>>4&15];t=65535&(A>>>16^I),v[C++]=I^t,v[C++]=A^t<<16}}return v}function i(e,t,r,n){var a,i=32===e.length?3:9;a=3===i?n?[30,-2,-2]:[0,32,2]:n?[94,62,-2,32,64,2,30,-2,-2]:[0,32,2,62,30,-2,64,96,2];var s,o=t[0],g=t[1];s=252645135&(o>>>4^g),g^=s,o^=s<<4,s=65535&(o>>>16^g),g^=s,o^=s<<16,s=858993459&(g>>>2^o),o^=s,g^=s<<2,s=16711935&(g>>>8^o),o^=s,g^=s<<8,s=1431655765&(o>>>1^g),g^=s,o^=s<<1,o=o<<1|o>>>31,g=g<<1|g>>>31;for(var v=0;v<i;v+=3){for(var m=a[v+1],C=a[v+2],E=a[v];E!=m;E+=C){var S=g^e[E],T=(g>>>4|g<<28)^e[E+1];s=o,o=g,g=s^(u[S>>>24&63]|p[S>>>16&63]|h[S>>>8&63]|y[63&S]|c[T>>>24&63]|l[T>>>16&63]|f[T>>>8&63]|d[63&T])}s=o,o=g,g=s}o=o>>>1|o<<31,g=g>>>1|g<<31,s=1431655765&(o>>>1^g),g^=s,o^=s<<1,s=16711935&(g>>>8^o),o^=s,g^=s<<8,s=858993459&(g>>>2^o),o^=s,g^=s<<2,s=65535&(o>>>16^g),g^=s,o^=s<<16,s=252645135&(o>>>4^g),g^=s,o^=s<<4,r[0]=o,r[1]=g}function s(e){e=e||{};var t,r=(e.mode||"CBC").toUpperCase(),n="DES-"+r;t=e.decrypt?o.cipher.createDecipher(n,e.key):o.cipher.createCipher(n,e.key);var a=t.start;return t.start=function(e,r){var n=null;r instanceof o.util.ByteBuffer&&(n=r,r={}),r=r||{},r.output=n,r.iv=e,a.call(t,r)},t}var o=r(0);r(14),r(21),r(1),e.exports=o.des=o.des||{},o.des.startEncrypting=function(e,t,r,n){var a=s({key:e,output:r,decrypt:!1,mode:n||(null===t?"ECB":"CBC")});return a.start(t),a},o.des.createEncryptionCipher=function(e,t){return s({key:e,output:null,decrypt:!1,mode:t})},o.des.startDecrypting=function(e,t,r,n){var a=s({key:e,output:r,decrypt:!0,mode:n||(null===t?"ECB":"CBC")});return a.start(t),a},o.des.createDecryptionCipher=function(e,t){return s({key:e,output:null,decrypt:!0,mode:t})},o.des.Algorithm=function(e,t){var r=this;r.name=e,r.mode=new t({blockSize:8,cipher:{encrypt:function(e,t){return i(r._keys,e,t,!1)},decrypt:function(e,t){return i(r._keys,e,t,!0)}}}),r._init=!1},o.des.Algorithm.prototype.initialize=function(e){if(!this._init){var t=o.util.createBuffer(e.key);if(0===this.name.indexOf("3DES")&&24!==t.length())throw new Error("Invalid Triple-DES key size: "+8*t.length());this._keys=a(t),this._init=!0}},n("DES-ECB",o.cipher.modes.ecb),n("DES-CBC",o.cipher.modes.cbc),n("DES-CFB",o.cipher.modes.cfb),n("DES-OFB",o.cipher.modes.ofb),n("DES-CTR",o.cipher.modes.ctr),n("3DES-ECB",o.cipher.modes.ecb),n("3DES-CBC",o.cipher.modes.cbc),n("3DES-CFB",o.cipher.modes.cfb),n("3DES-OFB",o.cipher.modes.ofb),n("3DES-CTR",o.cipher.modes.ctr);var c=[16843776,0,65536,16843780,16842756,66564,4,65536,1024,16843776,16843780,1024,16778244,16842756,16777216,4,1028,16778240,16778240,66560,66560,16842752,16842752,16778244,65540,16777220,16777220,65540,0,1028,66564,16777216,65536,16843780,4,16842752,16843776,16777216,16777216,1024,16842756,65536,66560,16777220,1024,4,16778244,66564,16843780,65540,16842752,16778244,16777220,1028,66564,16843776,1028,16778240,16778240,0,65540,66560,0,16842756],u=[-2146402272,-2147450880,32768,1081376,1048576,32,-2146435040,-2147450848,-2147483616,-2146402272,-2146402304,-2147483648,-2147450880,1048576,32,-2146435040,1081344,1048608,-2147450848,0,-2147483648,32768,1081376,-2146435072,1048608,-2147483616,0,1081344,32800,-2146402304,-2146435072,32800,0,1081376,-2146435040,1048576,-2147450848,-2146435072,-2146402304,32768,-2146435072,-2147450880,32,-2146402272,1081376,32,32768,-2147483648,32800,-2146402304,1048576,-2147483616,1048608,-2147450848,-2147483616,1048608,1081344,0,-2147450880,32800,-2147483648,-2146435040,-2146402272,1081344],l=[520,134349312,0,134348808,134218240,0,131592,134218240,131080,134217736,134217736,131072,134349320,131080,134348800,520,134217728,8,134349312,512,131584,134348800,134348808,131592,134218248,131584,131072,134218248,8,134349320,512,134217728,134349312,134217728,131080,520,131072,134349312,134218240,0,512,131080,134349320,134218240,134217736,512,0,134348808,134218248,131072,134217728,134349320,8,131592,131584,134217736,134348800,134218248,520,134348800,131592,8,134348808,131584],p=[8396801,8321,8321,128,8396928,8388737,8388609,8193,0,8396800,8396800,8396929,129,0,8388736,8388609,1,8192,8388608,8396801,128,8388608,8193,8320,8388737,1,8320,8388736,8192,8396928,8396929,129,8388736,8388609,8396800,8396929,129,0,0,8396800,8320,8388736,8388737,1,8396801,8321,8321,128,8396929,129,1,8192,8388609,8193,8396928,8388737,8193,8320,8388608,8396801,128,8388608,8192,8396928],f=[256,34078976,34078720,1107296512,524288,256,1073741824,34078720,1074266368,524288,33554688,1074266368,1107296512,1107820544,524544,1073741824,33554432,1074266112,1074266112,0,1073742080,1107820800,1107820800,33554688,1107820544,1073742080,0,1107296256,34078976,33554432,1107296256,524544,524288,1107296512,256,33554432,1073741824,34078720,1107296512,1074266368,33554688,1073741824,1107820544,34078976,1074266368,256,33554432,1107820544,1107820800,524544,1107296256,1107820800,34078720,0,1074266112,1107296256,524544,33554688,1073742080,524288,0,1074266112,34078976,1073742080],h=[536870928,541065216,16384,541081616,541065216,16,541081616,4194304,536887296,4210704,4194304,536870928,4194320,536887296,536870912,16400,0,4194320,536887312,16384,4210688,536887312,16,541065232,541065232,0,4210704,541081600,16400,4210688,541081600,536870912,536887296,16,541065232,4210688,541081616,4194304,16400,536870928,4194304,536887296,536870912,16400,536870928,541081616,4210688,541065216,4210704,541081600,0,541065232,16,16384,541065216,4210704,16384,4194320,536887312,0,541081600,536870912,4194320,536887312],d=[2097152,69206018,67110914,0,2048,67110914,2099202,69208064,69208066,2097152,0,67108866,2,67108864,69206018,2050,67110912,2099202,2097154,67110912,67108866,69206016,69208064,2097154,69206016,2048,2050,69208066,2099200,2,67108864,2099200,67108864,2099200,2097152,67110914,67110914,69206018,69206018,2,2097154,67108864,67110912,2097152,69208064,2050,2099202,69208064,2050,67108866,69208066,69206016,2099200,0,2,69208066,0,2099202,69206016,2048,67108866,67110912,2048,2097154],y=[268439616,4096,262144,268701760,268435456,268439616,64,268435456,262208,268697600,268701760,266240,268701696,266304,4096,64,268697600,268435520,268439552,4160,266240,262208,268697664,268701696,4160,0,0,268697664,268435520,268439552,266304,262144,266304,262144,268701696,4096,64,268697664,4096,266304,268439552,64,268435520,268697600,268697664,268435456,262144,268439616,0,268701760,262208,268435520,268697600,268439552,268439616,0,268701760,266240,266240,4160,4160,262208,268435456,268701696]},function(e,t,r){function n(e,t,r){var n=f.util.createBuffer(),a=Math.ceil(t.n.bitLength()/8);if(e.length>a-11){var i=new Error("Message is too long for PKCS#1 v1.5 padding.");throw i.length=e.length,i.max=a-11,i}n.putByte(0),n.putByte(r);var s,o=a-3-e.length;if(0===r||1===r){s=0===r?0:255;for(var c=0;c<o;++c)n.putByte(s)}else for(;o>0;){for(var u=0,l=f.random.getBytes(o),c=0;c<o;++c)s=l.charCodeAt(c),0===s?++u:n.putByte(s);o=u}return n.putByte(0),n.putBytes(e),n}function a(e,t,r,n){var a=Math.ceil(t.n.bitLength()/8),i=f.util.createBuffer(e),s=i.getByte(),o=i.getByte();if(0!==s||r&&0!==o&&1!==o||!r&&2!=o||r&&0===o&&void 0===n)throw new Error("Encryption block is invalid.");var c=0;if(0===o){c=a-3-n;for(var u=0;u<c;++u)if(0!==i.getByte())throw new Error("Encryption block is invalid.")}else if(1===o)for(c=0;i.length()>1;){if(255!==i.getByte()){--i.read;break}++c}else if(2===o)for(c=0;i.length()>1;){if(0===i.getByte()){--i.read;break}++c}if(0!==i.getByte()||c!==a-3-i.length())throw new Error("Encryption block is invalid.");return i.getBytes()}function i(e,t,r){function n(){a(e.pBits,function(t,n){return t?r(t):(e.p=n,null!==e.q?i(t,e.q):void a(e.qBits,i))})}function a(e,t){f.prime.generateProbablePrime(e,s,t)}function i(t,s){if(t)return r(t);if(e.q=s,e.p.compareTo(e.q)<0){var o=e.p;e.p=e.q,e.q=o}if(0!==e.p.subtract(h.ONE).gcd(e.e).compareTo(h.ONE))return e.p=null,void n();if(0!==e.q.subtract(h.ONE).gcd(e.e).compareTo(h.ONE))return e.q=null,void a(e.qBits,i);if(e.p1=e.p.subtract(h.ONE),e.q1=e.q.subtract(h.ONE),e.phi=e.p1.multiply(e.q1),0!==e.phi.gcd(e.e).compareTo(h.ONE))return e.p=e.q=null,void n();if(e.n=e.p.multiply(e.q),e.n.bitLength()!==e.bits)return e.q=null,void a(e.qBits,i);var c=e.e.modInverse(e.phi);e.keys={privateKey:v.rsa.setPrivateKey(e.n,e.e,c,e.p,e.q,c.mod(e.p1),c.mod(e.q1),e.q.modInverse(e.p)),publicKey:v.rsa.setPublicKey(e.n,e.e)},r(null,e.keys)}"function"==typeof t&&(r=t,t={}),t=t||{};var s={algorithm:{name:t.algorithm||"PRIMEINC",options:{workers:t.workers||2,workLoad:t.workLoad||100,workerScript:t.workerScript}}};"prng"in t&&(s.prng=t.prng),n()}function s(e){var t=e.toString(16);t[0]>="8"&&(t="00"+t);var r=f.util.hexToBytes(t);return r.length>1&&(0===r.charCodeAt(0)&&0==(128&r.charCodeAt(1))||255===r.charCodeAt(0)&&128==(128&r.charCodeAt(1)))?r.substr(1):r}function o(e){return e<=100?27:e<=150?18:e<=200?15:e<=250?12:e<=300?9:e<=350?8:e<=400?7:e<=500?6:e<=600?5:e<=800?4:e<=1250?3:2}function c(e){return f.util.isNodejs&&"function"==typeof d[e]}function u(e){return void 0!==g.globalScope&&"object"==typeof g.globalScope.crypto&&"object"==typeof g.globalScope.crypto.subtle&&"function"==typeof g.globalScope.crypto.subtle[e]}function l(e){return void 0!==g.globalScope&&"object"==typeof g.globalScope.msCrypto&&"object"==typeof g.globalScope.msCrypto.subtle&&"function"==typeof g.globalScope.msCrypto.subtle[e]}function p(e){for(var t=f.util.hexToBytes(e.toString(16)),r=new Uint8Array(t.length),n=0;n<t.length;++n)r[n]=t.charCodeAt(n);return r}var f=r(0);if(r(3),r(13),r(6),r(27),r(28),r(2),r(1),void 0===h)var h=f.jsbn.BigInteger;var d=f.util.isNodejs?r(17):null,y=f.asn1,g=f.util;f.pki=f.pki||{},e.exports=f.pki.rsa=f.rsa=f.rsa||{};var v=f.pki,m=[6,4,2,4,2,4,6,2],C={name:"PrivateKeyInfo",tagClass:y.Class.UNIVERSAL,type:y.Type.SEQUENCE,constructed:!0,value:[{name:"PrivateKeyInfo.version",tagClass:y.Class.UNIVERSAL,type:y.Type.INTEGER,constructed:!1,capture:"privateKeyVersion"},{name:"PrivateKeyInfo.privateKeyAlgorithm",tagClass:y.Class.UNIVERSAL,type:y.Type.SEQUENCE,constructed:!0,value:[{name:"AlgorithmIdentifier.algorithm",tagClass:y.Class.UNIVERSAL,type:y.Type.OID,constructed:!1,capture:"privateKeyOid"}]},{name:"PrivateKeyInfo",tagClass:y.Class.UNIVERSAL,type:y.Type.OCTETSTRING,constructed:!1,capture:"privateKey"}]},E={name:"RSAPrivateKey",tagClass:y.Class.UNIVERSAL,type:y.Type.SEQUENCE,constructed:!0,value:[{name:"RSAPrivateKey.version",tagClass:y.Class.UNIVERSAL,type:y.Type.INTEGER,constructed:!1,capture:"privateKeyVersion"},{name:"RSAPrivateKey.modulus",tagClass:y.Class.UNIVERSAL,type:y.Type.INTEGER,constructed:!1,capture:"privateKeyModulus"},{name:"RSAPrivateKey.publicExponent",tagClass:y.Class.UNIVERSAL,type:y.Type.INTEGER,constructed:!1,capture:"privateKeyPublicExponent"},{name:"RSAPrivateKey.privateExponent",tagClass:y.Class.UNIVERSAL,type:y.Type.INTEGER,constructed:!1,capture:"privateKeyPrivateExponent"},{name:"RSAPrivateKey.prime1",tagClass:y.Class.UNIVERSAL,type:y.Type.INTEGER,constructed:!1,capture:"privateKeyPrime1"},{name:"RSAPrivateKey.prime2",tagClass:y.Class.UNIVERSAL,type:y.Type.INTEGER,constructed:!1,capture:"privateKeyPrime2"},{name:"RSAPrivateKey.exponent1",tagClass:y.Class.UNIVERSAL,type:y.Type.INTEGER,constructed:!1,capture:"privateKeyExponent1"},{name:"RSAPrivateKey.exponent2",tagClass:y.Class.UNIVERSAL,type:y.Type.INTEGER,constructed:!1,capture:"privateKeyExponent2"},{name:"RSAPrivateKey.coefficient",tagClass:y.Class.UNIVERSAL,type:y.Type.INTEGER,constructed:!1,capture:"privateKeyCoefficient"}]},S={name:"RSAPublicKey",tagClass:y.Class.UNIVERSAL,type:y.Type.SEQUENCE,constructed:!0,value:[{name:"RSAPublicKey.modulus",tagClass:y.Class.UNIVERSAL,type:y.Type.INTEGER,constructed:!1,capture:"publicKeyModulus"},{name:"RSAPublicKey.exponent",tagClass:y.Class.UNIVERSAL,type:y.Type.INTEGER,constructed:!1,capture:"publicKeyExponent"}]},T=f.pki.rsa.publicKeyValidator={name:"SubjectPublicKeyInfo",tagClass:y.Class.UNIVERSAL,type:y.Type.SEQUENCE,constructed:!0,captureAsn1:"subjectPublicKeyInfo",value:[{name:"SubjectPublicKeyInfo.AlgorithmIdentifier",tagClass:y.Class.UNIVERSAL,type:y.Type.SEQUENCE,constructed:!0,value:[{name:"AlgorithmIdentifier.algorithm",tagClass:y.Class.UNIVERSAL,type:y.Type.OID,constructed:!1,capture:"publicKeyOid"}]},{name:"SubjectPublicKeyInfo.subjectPublicKey",tagClass:y.Class.UNIVERSAL,type:y.Type.BITSTRING,constructed:!1,value:[{name:"SubjectPublicKeyInfo.subjectPublicKey.RSAPublicKey",tagClass:y.Class.UNIVERSAL,type:y.Type.SEQUENCE,constructed:!0,optional:!0,captureAsn1:"rsaPublicKey"}]}]},b=function(e){var t;if(!(e.algorithm in v.oids)){var r=new Error("Unknown message digest algorithm.");throw r.algorithm=e.algorithm,r}t=v.oids[e.algorithm];var n=y.oidToDer(t).getBytes(),a=y.create(y.Class.UNIVERSAL,y.Type.SEQUENCE,!0,[]),i=y.create(y.Class.UNIVERSAL,y.Type.SEQUENCE,!0,[]);i.value.push(y.create(y.Class.UNIVERSAL,y.Type.OID,!1,n)),i.value.push(y.create(y.Class.UNIVERSAL,y.Type.NULL,!1,""));var s=y.create(y.Class.UNIVERSAL,y.Type.OCTETSTRING,!1,e.digest().getBytes());return a.value.push(i),a.value.push(s),y.toDer(a).getBytes()},I=function(e,t,r){if(r)return e.modPow(t.e,t.n);if(!t.p||!t.q)return e.modPow(t.d,t.n);t.dP||(t.dP=t.d.mod(t.p.subtract(h.ONE))),t.dQ||(t.dQ=t.d.mod(t.q.subtract(h.ONE))),t.qInv||(t.qInv=t.q.modInverse(t.p));var n;do{n=new h(f.util.bytesToHex(f.random.getBytes(t.n.bitLength()/8)),16)}while(n.compareTo(t.n)>=0||!n.gcd(t.n).equals(h.ONE));e=e.multiply(n.modPow(t.e,t.n)).mod(t.n);for(var a=e.mod(t.p).modPow(t.dP,t.p),i=e.mod(t.q).modPow(t.dQ,t.q);a.compareTo(i)<0;)a=a.add(t.p);var s=a.subtract(i).multiply(t.qInv).mod(t.p).multiply(t.q).add(i);return s=s.multiply(n.modInverse(t.n)).mod(t.n)};v.rsa.encrypt=function(e,t,r){var a,i=r,s=Math.ceil(t.n.bitLength()/8);!1!==r&&!0!==r?(i=2===r,a=n(e,t,r)):(a=f.util.createBuffer(),a.putBytes(e));for(var o=new h(a.toHex(),16),c=I(o,t,i),u=c.toString(16),l=f.util.createBuffer(),p=s-Math.ceil(u.length/2);p>0;)l.putByte(0),--p;return l.putBytes(f.util.hexToBytes(u)),l.getBytes()},v.rsa.decrypt=function(e,t,r,n){var i=Math.ceil(t.n.bitLength()/8);if(e.length!==i){var s=new Error("Encrypted message length is invalid.");throw s.length=e.length,s.expected=i,s}var o=new h(f.util.createBuffer(e).toHex(),16);if(o.compareTo(t.n)>=0)throw new Error("Encrypted message is invalid.");for(var c=I(o,t,r),u=c.toString(16),l=f.util.createBuffer(),p=i-Math.ceil(u.length/2);p>0;)l.putByte(0),--p;return l.putBytes(f.util.hexToBytes(u)),!1!==n?a(l.getBytes(),t,r):l.getBytes()},v.rsa.createKeyPairGenerationState=function(e,t,r){"string"==typeof e&&(e=parseInt(e,10)),e=e||2048,r=r||{};var n,a=r.prng||f.random,i={nextBytes:function(e){for(var t=a.getBytesSync(e.length),r=0;r<e.length;++r)e[r]=t.charCodeAt(r)}},s=r.algorithm||"PRIMEINC";if("PRIMEINC"!==s)throw new Error("Invalid key generation algorithm: "+s);return n={algorithm:s,state:0,bits:e,rng:i,eInt:t||65537,e:new h(null),p:null,q:null,qBits:e>>1,pBits:e-(e>>1),pqState:0,num:null,keys:null},n.e.fromInt(n.eInt),n},v.rsa.stepKeyPairGenerationState=function(e,t){"algorithm"in e||(e.algorithm="PRIMEINC");var r=new h(null);r.fromInt(30);for(var n,a=0,i=function(e,t){return e|t},s=+new Date,c=0;null===e.keys&&(t<=0||c<t);){if(0===e.state){var u=null===e.p?e.pBits:e.qBits,l=u-1;0===e.pqState?(e.num=new h(u,e.rng),e.num.testBit(l)||e.num.bitwiseTo(h.ONE.shiftLeft(l),i,e.num),e.num.dAddOffset(31-e.num.mod(r).byteValue(),0),a=0,++e.pqState):1===e.pqState?e.num.bitLength()>u?e.pqState=0:e.num.isProbablePrime(o(e.num.bitLength()))?++e.pqState:e.num.dAddOffset(m[a++%8],0):2===e.pqState?e.pqState=0===e.num.subtract(h.ONE).gcd(e.e).compareTo(h.ONE)?3:0:3===e.pqState&&(e.pqState=0,null===e.p?e.p=e.num:e.q=e.num,null!==e.p&&null!==e.q&&++e.state,e.num=null)}else if(1===e.state)e.p.compareTo(e.q)<0&&(e.num=e.p,e.p=e.q,e.q=e.num),++e.state;else if(2===e.state)e.p1=e.p.subtract(h.ONE),e.q1=e.q.subtract(h.ONE),e.phi=e.p1.multiply(e.q1),++e.state;else if(3===e.state)0===e.phi.gcd(e.e).compareTo(h.ONE)?++e.state:(e.p=null,e.q=null,e.state=0);else if(4===e.state)e.n=e.p.multiply(e.q),e.n.bitLength()===e.bits?++e.state:(e.q=null,e.state=0);else if(5===e.state){var p=e.e.modInverse(e.phi);e.keys={privateKey:v.rsa.setPrivateKey(e.n,e.e,p,e.p,e.q,p.mod(e.p1),p.mod(e.q1),e.q.modInverse(e.p)),publicKey:v.rsa.setPublicKey(e.n,e.e)}}n=+new Date,c+=n-s,s=n}return null!==e.keys},v.rsa.generateKeyPair=function(e,t,r,n){if(1===arguments.length?"object"==typeof e?(r=e,e=void 0):"function"==typeof e&&(n=e,e=void 0):2===arguments.length?"number"==typeof e?"function"==typeof t?(n=t,t=void 0):"number"!=typeof t&&(r=t,t=void 0):(r=e,n=t,e=void 0,t=void 0):3===arguments.length&&("number"==typeof t?"function"==typeof r&&(n=r,r=void 0):(n=r,r=t,t=void 0)),r=r||{},void 0===e&&(e=r.bits||2048),void 0===t&&(t=r.e||65537),!f.options.usePureJavaScript&&!r.prng&&e>=256&&e<=16384&&(65537===t||3===t))if(n){if(c("generateKeyPair"))return d.generateKeyPair("rsa",{modulusLength:e,publicExponent:t,publicKeyEncoding:{type:"spki",format:"pem"},privateKeyEncoding:{type:"pkcs8",format:"pem"}},function(e,t,r){if(e)return n(e);n(null,{privateKey:v.privateKeyFromPem(r),publicKey:v.publicKeyFromPem(t)})});if(u("generateKey")&&u("exportKey"))return g.globalScope.crypto.subtle.generateKey({name:"RSASSA-PKCS1-v1_5",modulusLength:e,publicExponent:p(t),hash:{name:"SHA-256"}},!0,["sign","verify"]).then(function(e){return g.globalScope.crypto.subtle.exportKey("pkcs8",e.privateKey)}).then(void 0,function(e){n(e)}).then(function(e){if(e){var t=v.privateKeyFromAsn1(y.fromDer(f.util.createBuffer(e)));n(null,{privateKey:t,publicKey:v.setRsaPublicKey(t.n,t.e)})}});if(l("generateKey")&&l("exportKey")){var a=g.globalScope.msCrypto.subtle.generateKey({name:"RSASSA-PKCS1-v1_5",modulusLength:e,publicExponent:p(t),hash:{name:"SHA-256"}},!0,["sign","verify"]);return a.oncomplete=function(e){var t=e.target.result,r=g.globalScope.msCrypto.subtle.exportKey("pkcs8",t.privateKey);r.oncomplete=function(e){var t=e.target.result,r=v.privateKeyFromAsn1(y.fromDer(f.util.createBuffer(t)));n(null,{privateKey:r,publicKey:v.setRsaPublicKey(r.n,r.e)})},r.onerror=function(e){n(e)}},void(a.onerror=function(e){n(e)})}}else if(c("generateKeyPairSync")){var s=d.generateKeyPairSync("rsa",{modulusLength:e,publicExponent:t,publicKeyEncoding:{type:"spki",format:"pem"},privateKeyEncoding:{type:"pkcs8",format:"pem"}});return{privateKey:v.privateKeyFromPem(s.privateKey),publicKey:v.publicKeyFromPem(s.publicKey)}}var o=v.rsa.createKeyPairGenerationState(e,t,r);if(!n)return v.rsa.stepKeyPairGenerationState(o,0),o.keys;i(o,r,n)},v.setRsaPublicKey=v.rsa.setPublicKey=function(e,t){var r={n:e,e:t};return r.encrypt=function(e,t,a){if("string"==typeof t?t=t.toUpperCase():void 0===t&&(t="RSAES-PKCS1-V1_5"),"RSAES-PKCS1-V1_5"===t)t={encode:function(e,t,r){return n(e,t,2).getBytes()}};else if("RSA-OAEP"===t||"RSAES-OAEP"===t)t={encode:function(e,t){return f.pkcs1.encode_rsa_oaep(t,e,a)}};else if(-1!==["RAW","NONE","NULL",null].indexOf(t))t={encode:function(e){return e}};else if("string"==typeof t)throw new Error('Unsupported encryption scheme: "'+t+'".');var i=t.encode(e,r,!0);return v.rsa.encrypt(i,r,!0)},r.verify=function(e,t,n){"string"==typeof n?n=n.toUpperCase():void 0===n&&(n="RSASSA-PKCS1-V1_5"),"RSASSA-PKCS1-V1_5"===n?n={verify:function(e,t){return t=a(t,r,!0),e===y.fromDer(t).value[1].value}}:"NONE"!==n&&"NULL"!==n&&null!==n||(n={verify:function(e,t){return t=a(t,r,!0),e===t}});var i=v.rsa.decrypt(t,r,!0,!1);return n.verify(e,i,r.n.bitLength())},r},v.setRsaPrivateKey=v.rsa.setPrivateKey=function(e,t,r,n,i,s,o,c){var u={n:e,e:t,d:r,p:n,q:i,dP:s,dQ:o,qInv:c};return u.decrypt=function(e,t,r){"string"==typeof t?t=t.toUpperCase():void 0===t&&(t="RSAES-PKCS1-V1_5");var n=v.rsa.decrypt(e,u,!1,!1);if("RSAES-PKCS1-V1_5"===t)t={decode:a};else if("RSA-OAEP"===t||"RSAES-OAEP"===t)t={decode:function(e,t){return f.pkcs1.decode_rsa_oaep(t,e,r)}};else{if(-1===["RAW","NONE","NULL",null].indexOf(t))throw new Error('Unsupported encryption scheme: "'+t+'".');t={decode:function(e){return e}}}return t.decode(n,u,!1)},u.sign=function(e,t){var r=!1;"string"==typeof t&&(t=t.toUpperCase()),void 0===t||"RSASSA-PKCS1-V1_5"===t?(t={encode:b},r=1):"NONE"!==t&&"NULL"!==t&&null!==t||(t={encode:function(){return e}},r=1);var n=t.encode(e,u.n.bitLength());return v.rsa.encrypt(n,u,r)},u},v.wrapRsaPrivateKey=function(e){return y.create(y.Class.UNIVERSAL,y.Type.SEQUENCE,!0,[y.create(y.Class.UNIVERSAL,y.Type.INTEGER,!1,y.integerToDer(0).getBytes()),y.create(y.Class.UNIVERSAL,y.Type.SEQUENCE,!0,[y.create(y.Class.UNIVERSAL,y.Type.OID,!1,y.oidToDer(v.oids.rsaEncryption).getBytes()),y.create(y.Class.UNIVERSAL,y.Type.NULL,!1,"")]),y.create(y.Class.UNIVERSAL,y.Type.OCTETSTRING,!1,y.toDer(e).getBytes())])},v.privateKeyFromAsn1=function(e){var t={},r=[];if(y.validate(e,C,t,r)&&(e=y.fromDer(f.util.createBuffer(t.privateKey))),t={},r=[],!y.validate(e,E,t,r)){var n=new Error("Cannot read private key. ASN.1 object does not contain an RSAPrivateKey.");throw n.errors=r,n}var a,i,s,o,c,u,l,p;return a=f.util.createBuffer(t.privateKeyModulus).toHex(),i=f.util.createBuffer(t.privateKeyPublicExponent).toHex(),s=f.util.createBuffer(t.privateKeyPrivateExponent).toHex(),o=f.util.createBuffer(t.privateKeyPrime1).toHex(),c=f.util.createBuffer(t.privateKeyPrime2).toHex(),u=f.util.createBuffer(t.privateKeyExponent1).toHex(),l=f.util.createBuffer(t.privateKeyExponent2).toHex(),p=f.util.createBuffer(t.privateKeyCoefficient).toHex(),v.setRsaPrivateKey(new h(a,16),new h(i,16),new h(s,16),new h(o,16),new h(c,16),new h(u,16),new h(l,16),new h(p,16))},v.privateKeyToAsn1=v.privateKeyToRSAPrivateKey=function(e){return y.create(y.Class.UNIVERSAL,y.Type.SEQUENCE,!0,[y.create(y.Class.UNIVERSAL,y.Type.INTEGER,!1,y.integerToDer(0).getBytes()),y.create(y.Class.UNIVERSAL,y.Type.INTEGER,!1,s(e.n)),y.create(y.Class.UNIVERSAL,y.Type.INTEGER,!1,s(e.e)),y.create(y.Class.UNIVERSAL,y.Type.INTEGER,!1,s(e.d)),y.create(y.Class.UNIVERSAL,y.Type.INTEGER,!1,s(e.p)),y.create(y.Class.UNIVERSAL,y.Type.INTEGER,!1,s(e.q)),y.create(y.Class.UNIVERSAL,y.Type.INTEGER,!1,s(e.dP)),y.create(y.Class.UNIVERSAL,y.Type.INTEGER,!1,s(e.dQ)),y.create(y.Class.UNIVERSAL,y.Type.INTEGER,!1,s(e.qInv))])},v.publicKeyFromAsn1=function(e){var t={},r=[];if(y.validate(e,T,t,r)){var n=y.derToOid(t.publicKeyOid);if(n!==v.oids.rsaEncryption){var a=new Error("Cannot read public key. Unknown OID.");throw a.oid=n,a}e=t.rsaPublicKey}if(r=[],!y.validate(e,S,t,r)){var a=new Error("Cannot read public key. ASN.1 object does not contain an RSAPublicKey.");throw a.errors=r,a}var i=f.util.createBuffer(t.publicKeyModulus).toHex(),s=f.util.createBuffer(t.publicKeyExponent).toHex();return v.setRsaPublicKey(new h(i,16),new h(s,16))},v.publicKeyToAsn1=v.publicKeyToSubjectPublicKeyInfo=function(e){return y.create(y.Class.UNIVERSAL,y.Type.SEQUENCE,!0,[y.create(y.Class.UNIVERSAL,y.Type.SEQUENCE,!0,[y.create(y.Class.UNIVERSAL,y.Type.OID,!1,y.oidToDer(v.oids.rsaEncryption).getBytes()),y.create(y.Class.UNIVERSAL,y.Type.NULL,!1,"")]),y.create(y.Class.UNIVERSAL,y.Type.BITSTRING,!1,[v.publicKeyToRSAPublicKey(e)])])},v.publicKeyToRSAPublicKey=function(e){return y.create(y.Class.UNIVERSAL,y.Type.SEQUENCE,!0,[y.create(y.Class.UNIVERSAL,y.Type.INTEGER,!1,s(e.n)),y.create(y.Class.UNIVERSAL,y.Type.INTEGER,!1,s(e.e))])}},function(e,t,r){function n(e,t,r){this.data=[],null!=e&&("number"==typeof e?this.fromNumber(e,t,r):null==t&&"string"!=typeof e?this.fromString(e,256):this.fromString(e,t))}function a(){return new n(null)}function i(e,t,r,n,a,i){for(;--i>=0;){var s=t*this.data[e++]+r.data[n]+a;a=Math.floor(s/67108864),r.data[n++]=67108863&s}return a}function s(e,t,r,n,a,i){for(var s=32767&t,o=t>>15;--i>=0;){var c=32767&this.data[e],u=this.data[e++]>>15,l=o*c+u*s;c=s*c+((32767&l)<<15)+r.data[n]+(1073741823&a),a=(c>>>30)+(l>>>15)+o*u+(a>>>30),r.data[n++]=1073741823&c}return a}function o(e,t,r,n,a,i){for(var s=16383&t,o=t>>14;--i>=0;){var c=16383&this.data[e],u=this.data[e++]>>14,l=o*c+u*s;c=s*c+((16383&l)<<14)+r.data[n]+a,a=(c>>28)+(l>>14)+o*u,r.data[n++]=268435455&c}return a}function c(e){return it.charAt(e)}function u(e,t){var r=st[e.charCodeAt(t)];return null==r?-1:r}function l(e){for(var t=this.t-1;t>=0;--t)e.data[t]=this.data[t];e.t=this.t,e.s=this.s}function p(e){this.t=1,this.s=e<0?-1:0,e>0?this.data[0]=e:e<-1?this.data[0]=e+this.DV:this.t=0}function f(e){var t=a();return t.fromInt(e),t}function h(e,t){var r;if(16==t)r=4;else if(8==t)r=3;else if(256==t)r=8;else if(2==t)r=1;else if(32==t)r=5;else{if(4!=t)return void this.fromRadix(e,t);r=2}this.t=0,this.s=0;for(var a=e.length,i=!1,s=0;--a>=0;){var o=8==r?255&e[a]:u(e,a);o<0?"-"==e.charAt(a)&&(i=!0):(i=!1,0==s?this.data[this.t++]=o:s+r>this.DB?(this.data[this.t-1]|=(o&(1<<this.DB-s)-1)<<s,this.data[this.t++]=o>>this.DB-s):this.data[this.t-1]|=o<<s,(s+=r)>=this.DB&&(s-=this.DB))}8==r&&0!=(128&e[0])&&(this.s=-1,s>0&&(this.data[this.t-1]|=(1<<this.DB-s)-1<<s)),this.clamp(),i&&n.ZERO.subTo(this,this)}function d(){for(var e=this.s&this.DM;this.t>0&&this.data[this.t-1]==e;)--this.t}function y(e){if(this.s<0)return"-"+this.negate().toString(e);var t;if(16==e)t=4;else if(8==e)t=3;else if(2==e)t=1;else if(32==e)t=5;else{if(4!=e)return this.toRadix(e);t=2}var r,n=(1<<t)-1,a=!1,i="",s=this.t,o=this.DB-s*this.DB%t;if(s-- >0)for(o<this.DB&&(r=this.data[s]>>o)>0&&(a=!0,i=c(r));s>=0;)o<t?(r=(this.data[s]&(1<<o)-1)<<t-o,r|=this.data[--s]>>(o+=this.DB-t)):(r=this.data[s]>>(o-=t)&n,o<=0&&(o+=this.DB,--s)),r>0&&(a=!0),a&&(i+=c(r));return a?i:"0"}function g(){var e=a();return n.ZERO.subTo(this,e),e}function v(){return this.s<0?this.negate():this}function m(e){var t=this.s-e.s;if(0!=t)return t;var r=this.t;if(0!=(t=r-e.t))return this.s<0?-t:t;for(;--r>=0;)if(0!=(t=this.data[r]-e.data[r]))return t;return 0}function C(e){var t,r=1;return 0!=(t=e>>>16)&&(e=t,r+=16),0!=(t=e>>8)&&(e=t,r+=8),0!=(t=e>>4)&&(e=t,r+=4),0!=(t=e>>2)&&(e=t,r+=2),0!=(t=e>>1)&&(e=t,r+=1),r}function E(){return this.t<=0?0:this.DB*(this.t-1)+C(this.data[this.t-1]^this.s&this.DM)}function S(e,t){var r;for(r=this.t-1;r>=0;--r)t.data[r+e]=this.data[r];for(r=e-1;r>=0;--r)t.data[r]=0;t.t=this.t+e,t.s=this.s}function T(e,t){for(var r=e;r<this.t;++r)t.data[r-e]=this.data[r];t.t=Math.max(this.t-e,0),t.s=this.s}function b(e,t){var r,n=e%this.DB,a=this.DB-n,i=(1<<a)-1,s=Math.floor(e/this.DB),o=this.s<<n&this.DM;for(r=this.t-1;r>=0;--r)t.data[r+s+1]=this.data[r]>>a|o,o=(this.data[r]&i)<<n;for(r=s-1;r>=0;--r)t.data[r]=0;t.data[s]=o,t.t=this.t+s+1,t.s=this.s,t.clamp()}function I(e,t){t.s=this.s;var r=Math.floor(e/this.DB);if(r>=this.t)return void(t.t=0);var n=e%this.DB,a=this.DB-n,i=(1<<n)-1;t.data[0]=this.data[r]>>n;for(var s=r+1;s<this.t;++s)t.data[s-r-1]|=(this.data[s]&i)<<a,t.data[s-r]=this.data[s]>>n;n>0&&(t.data[this.t-r-1]|=(this.s&i)<<a),t.t=this.t-r,t.clamp()}function A(e,t){for(var r=0,n=0,a=Math.min(e.t,this.t);r<a;)n+=this.data[r]-e.data[r],t.data[r++]=n&this.DM,n>>=this.DB;if(e.t<this.t){for(n-=e.s;r<this.t;)n+=this.data[r],t.data[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<e.t;)n-=e.data[r],t.data[r++]=n&this.DM,n>>=this.DB;n-=e.s}t.s=n<0?-1:0,n<-1?t.data[r++]=this.DV+n:n>0&&(t.data[r++]=n),t.t=r,t.clamp()}function B(e,t){var r=this.abs(),a=e.abs(),i=r.t;for(t.t=i+a.t;--i>=0;)t.data[i]=0;for(i=0;i<a.t;++i)t.data[i+r.t]=r.am(0,a.data[i],t,i,0,r.t);t.s=0,t.clamp(),this.s!=e.s&&n.ZERO.subTo(t,t)}function k(e){for(var t=this.abs(),r=e.t=2*t.t;--r>=0;)e.data[r]=0;for(r=0;r<t.t-1;++r){var n=t.am(r,t.data[r],e,2*r,0,1);(e.data[r+t.t]+=t.am(r+1,2*t.data[r],e,2*r+1,n,t.t-r-1))>=t.DV&&(e.data[r+t.t]-=t.DV,e.data[r+t.t+1]=1)}e.t>0&&(e.data[e.t-1]+=t.am(r,t.data[r],e,2*r,0,1)),e.s=0,e.clamp()}function N(e,t,r){var i=e.abs();if(!(i.t<=0)){var s=this.abs();if(s.t<i.t)return null!=t&&t.fromInt(0),void(null!=r&&this.copyTo(r));null==r&&(r=a());var o=a(),c=this.s,u=e.s,l=this.DB-C(i.data[i.t-1]);l>0?(i.lShiftTo(l,o),s.lShiftTo(l,r)):(i.copyTo(o),s.copyTo(r));var p=o.t,f=o.data[p-1];if(0!=f){var h=f*(1<<this.F1)+(p>1?o.data[p-2]>>this.F2:0),d=this.FV/h,y=(1<<this.F1)/h,g=1<<this.F2,v=r.t,m=v-p,E=null==t?a():t;for(o.dlShiftTo(m,E),r.compareTo(E)>=0&&(r.data[r.t++]=1,r.subTo(E,r)),n.ONE.dlShiftTo(p,E),E.subTo(o,o);o.t<p;)o.data[o.t++]=0;for(;--m>=0;){var S=r.data[--v]==f?this.DM:Math.floor(r.data[v]*d+(r.data[v-1]+g)*y);if((r.data[v]+=o.am(0,S,r,m,0,p))<S)for(o.dlShiftTo(m,E),r.subTo(E,r);r.data[v]<--S;)r.subTo(E,r)}null!=t&&(r.drShiftTo(p,t),c!=u&&n.ZERO.subTo(t,t)),r.t=p,r.clamp(),l>0&&r.rShiftTo(l,r),c<0&&n.ZERO.subTo(r,r)}}}function w(e){var t=a();return this.abs().divRemTo(e,null,t),this.s<0&&t.compareTo(n.ZERO)>0&&e.subTo(t,t),t}function R(e){this.m=e}function L(e){return e.s<0||e.compareTo(this.m)>=0?e.mod(this.m):e}function _(e){return e}function U(e){e.divRemTo(this.m,null,e)}function D(e,t,r){e.multiplyTo(t,r),this.reduce(r)}function P(e,t){e.squareTo(t),this.reduce(t)}function O(){if(this.t<1)return 0;var e=this.data[0];if(0==(1&e))return 0;var t=3&e;return t=t*(2-(15&e)*t)&15,t=t*(2-(255&e)*t)&255,t=t*(2-((65535&e)*t&65535))&65535,t=t*(2-e*t%this.DV)%this.DV,t>0?this.DV-t:-t}function V(e){this.m=e,this.mp=e.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<e.DB-15)-1,this.mt2=2*e.t}function x(e){var t=a();return e.abs().dlShiftTo(this.m.t,t),t.divRemTo(this.m,null,t),e.s<0&&t.compareTo(n.ZERO)>0&&this.m.subTo(t,t),t}function K(e){var t=a();return e.copyTo(t),this.reduce(t),t}function M(e){for(;e.t<=this.mt2;)e.data[e.t++]=0;for(var t=0;t<this.m.t;++t){var r=32767&e.data[t],n=r*this.mpl+((r*this.mph+(e.data[t]>>15)*this.mpl&this.um)<<15)&e.DM;for(r=t+this.m.t,e.data[r]+=this.m.am(0,n,e,t,0,this.m.t);e.data[r]>=e.DV;)e.data[r]-=e.DV,e.data[++r]++}e.clamp(),e.drShiftTo(this.m.t,e),e.compareTo(this.m)>=0&&e.subTo(this.m,e)}function F(e,t){e.squareTo(t),this.reduce(t)}function q(e,t,r){e.multiplyTo(t,r),this.reduce(r)}function H(){return 0==(this.t>0?1&this.data[0]:this.s)}function j(e,t){if(e>4294967295||e<1)return n.ONE;var r=a(),i=a(),s=t.convert(this),o=C(e)-1;for(s.copyTo(r);--o>=0;)if(t.sqrTo(r,i),(e&1<<o)>0)t.mulTo(i,s,r);else{var c=r;r=i,i=c}return t.revert(r)}function G(e,t){var r;return r=e<256||t.isEven()?new R(t):new V(t),this.exp(e,r)}function Q(){var e=a();return this.copyTo(e),e}function z(){if(this.s<0){if(1==this.t)return this.data[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this.data[0];if(0==this.t)return 0}return(this.data[1]&(1<<32-this.DB)-1)<<this.DB|this.data[0]}function W(){return 0==this.t?this.s:this.data[0]<<24>>24}function X(){return 0==this.t?this.s:this.data[0]<<16>>16}function Y(e){return Math.floor(Math.LN2*this.DB/Math.log(e))}function Z(){return this.s<0?-1:this.t<=0||1==this.t&&this.data[0]<=0?0:1}function J(e){if(null==e&&(e=10),0==this.signum()||e<2||e>36)return"0";var t=this.chunkSize(e),r=Math.pow(e,t),n=f(r),i=a(),s=a(),o="";for(this.divRemTo(n,i,s);i.signum()>0;)o=(r+s.intValue()).toString(e).substr(1)+o,i.divRemTo(n,i,s);return s.intValue().toString(e)+o}function $(e,t){this.fromInt(0),null==t&&(t=10);for(var r=this.chunkSize(t),a=Math.pow(t,r),i=!1,s=0,o=0,c=0;c<e.length;++c){var l=u(e,c);l<0?"-"==e.charAt(c)&&0==this.signum()&&(i=!0):(o=t*o+l,++s>=r&&(this.dMultiply(a),this.dAddOffset(o,0),s=0,o=0))}s>0&&(this.dMultiply(Math.pow(t,s)),this.dAddOffset(o,0)),i&&n.ZERO.subTo(this,this)}function ee(e,t,r){if("number"==typeof t)if(e<2)this.fromInt(1);else for(this.fromNumber(e,r),this.testBit(e-1)||this.bitwiseTo(n.ONE.shiftLeft(e-1),ce,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(t);)this.dAddOffset(2,0),this.bitLength()>e&&this.subTo(n.ONE.shiftLeft(e-1),this);else{var a=new Array,i=7&e;a.length=1+(e>>3),t.nextBytes(a),i>0?a[0]&=(1<<i)-1:a[0]=0,this.fromString(a,256)}}function te(){var e=this.t,t=new Array;t[0]=this.s;var r,n=this.DB-e*this.DB%8,a=0;if(e-- >0)for(n<this.DB&&(r=this.data[e]>>n)!=(this.s&this.DM)>>n&&(t[a++]=r|this.s<<this.DB-n);e>=0;)n<8?(r=(this.data[e]&(1<<n)-1)<<8-n,r|=this.data[--e]>>(n+=this.DB-8)):(r=this.data[e]>>(n-=8)&255,n<=0&&(n+=this.DB,--e)),0!=(128&r)&&(r|=-256),0==a&&(128&this.s)!=(128&r)&&++a,(a>0||r!=this.s)&&(t[a++]=r);return t}function re(e){return 0==this.compareTo(e)}function ne(e){return this.compareTo(e)<0?this:e}function ae(e){return this.compareTo(e)>0?this:e}function ie(e,t,r){var n,a,i=Math.min(e.t,this.t);for(n=0;n<i;++n)r.data[n]=t(this.data[n],e.data[n]);if(e.t<this.t){for(a=e.s&this.DM,n=i;n<this.t;++n)r.data[n]=t(this.data[n],a);r.t=this.t}else{for(a=this.s&this.DM,n=i;n<e.t;++n)r.data[n]=t(a,e.data[n]);r.t=e.t}r.s=t(this.s,e.s),r.clamp()}function se(e,t){return e&t}function oe(e){var t=a();return this.bitwiseTo(e,se,t),t}function ce(e,t){return e|t}function ue(e){var t=a();return this.bitwiseTo(e,ce,t),t}function le(e,t){return e^t}function pe(e){var t=a();return this.bitwiseTo(e,le,t),t}function fe(e,t){return e&~t}function he(e){var t=a();return this.bitwiseTo(e,fe,t),t}function de(){for(var e=a(),t=0;t<this.t;++t)e.data[t]=this.DM&~this.data[t];return e.t=this.t,e.s=~this.s,e}function ye(e){var t=a();return e<0?this.rShiftTo(-e,t):this.lShiftTo(e,t),t}function ge(e){var t=a();return e<0?this.lShiftTo(-e,t):this.rShiftTo(e,t),t}function ve(e){if(0==e)return-1;var t=0;return 0==(65535&e)&&(e>>=16,t+=16),0==(255&e)&&(e>>=8,t+=8),0==(15&e)&&(e>>=4,t+=4),0==(3&e)&&(e>>=2,t+=2),0==(1&e)&&++t,t}function me(){for(var e=0;e<this.t;++e)if(0!=this.data[e])return e*this.DB+ve(this.data[e]);return this.s<0?this.t*this.DB:-1}function Ce(e){for(var t=0;0!=e;)e&=e-1,++t;return t}function Ee(){for(var e=0,t=this.s&this.DM,r=0;r<this.t;++r)e+=Ce(this.data[r]^t);return e}function Se(e){var t=Math.floor(e/this.DB);return t>=this.t?0!=this.s:0!=(this.data[t]&1<<e%this.DB)}function Te(e,t){var r=n.ONE.shiftLeft(e);return this.bitwiseTo(r,t,r),r}function be(e){return this.changeBit(e,ce)}function Ie(e){return this.changeBit(e,fe)}function Ae(e){return this.changeBit(e,le)}function Be(e,t){for(var r=0,n=0,a=Math.min(e.t,this.t);r<a;)n+=this.data[r]+e.data[r],t.data[r++]=n&this.DM,n>>=this.DB;if(e.t<this.t){for(n+=e.s;r<this.t;)n+=this.data[r],t.data[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<e.t;)n+=e.data[r],t.data[r++]=n&this.DM,n>>=this.DB;n+=e.s}t.s=n<0?-1:0,n>0?t.data[r++]=n:n<-1&&(t.data[r++]=this.DV+n),t.t=r,t.clamp()}function ke(e){var t=a();return this.addTo(e,t),t}function Ne(e){var t=a();return this.subTo(e,t),t}function we(e){var t=a();return this.multiplyTo(e,t),t}function Re(e){var t=a();return this.divRemTo(e,t,null),t}function Le(e){var t=a();return this.divRemTo(e,null,t),t}function _e(e){var t=a(),r=a();return this.divRemTo(e,t,r),new Array(t,r)}function Ue(e){this.data[this.t]=this.am(0,e-1,this,0,0,this.t),++this.t,this.clamp()}function De(e,t){if(0!=e){for(;this.t<=t;)this.data[this.t++]=0;for(this.data[t]+=e;this.data[t]>=this.DV;)this.data[t]-=this.DV,++t>=this.t&&(this.data[this.t++]=0),++this.data[t]}}function Pe(){}function Oe(e){return e}function Ve(e,t,r){e.multiplyTo(t,r)}function xe(e,t){e.squareTo(t)}function Ke(e){return this.exp(e,new Pe)}function Me(e,t,r){var n=Math.min(this.t+e.t,t);for(r.s=0,r.t=n;n>0;)r.data[--n]=0;var a;for(a=r.t-this.t;n<a;++n)r.data[n+this.t]=this.am(0,e.data[n],r,n,0,this.t);for(a=Math.min(e.t,t);n<a;++n)this.am(0,e.data[n],r,n,0,t-n);r.clamp()}function Fe(e,t,r){--t;var n=r.t=this.t+e.t-t;for(r.s=0;--n>=0;)r.data[n]=0;for(n=Math.max(t-this.t,0);n<e.t;++n)r.data[this.t+n-t]=this.am(t-n,e.data[n],r,0,0,this.t+n-t);r.clamp(),r.drShiftTo(1,r)}function qe(e){this.r2=a(),this.q3=a(),n.ONE.dlShiftTo(2*e.t,this.r2),this.mu=this.r2.divide(e),this.m=e}function He(e){if(e.s<0||e.t>2*this.m.t)return e.mod(this.m);if(e.compareTo(this.m)<0)return e;var t=a();return e.copyTo(t),this.reduce(t),t}function je(e){return e}function Ge(e){for(e.drShiftTo(this.m.t-1,this.r2),e.t>this.m.t+1&&(e.t=this.m.t+1,e.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);e.compareTo(this.r2)<0;)e.dAddOffset(1,this.m.t+1);for(e.subTo(this.r2,e);e.compareTo(this.m)>=0;)e.subTo(this.m,e)}function Qe(e,t){e.squareTo(t),this.reduce(t)}function ze(e,t,r){e.multiplyTo(t,r),this.reduce(r)}function We(e,t){var r,n,i=e.bitLength(),s=f(1);if(i<=0)return s;r=i<18?1:i<48?3:i<144?4:i<768?5:6,n=i<8?new R(t):t.isEven()?new qe(t):new V(t);var o=new Array,c=3,u=r-1,l=(1<<r)-1;if(o[1]=n.convert(this),r>1){var p=a();for(n.sqrTo(o[1],p);c<=l;)o[c]=a(),n.mulTo(p,o[c-2],o[c]),c+=2}var h,d,y=e.t-1,g=!0,v=a();for(i=C(e.data[y])-1;y>=0;){for(i>=u?h=e.data[y]>>i-u&l:(h=(e.data[y]&(1<<i+1)-1)<<u-i,y>0&&(h|=e.data[y-1]>>this.DB+i-u)),c=r;0==(1&h);)h>>=1,--c;if((i-=c)<0&&(i+=this.DB,--y),g)o[h].copyTo(s),g=!1;else{for(;c>1;)n.sqrTo(s,v),n.sqrTo(v,s),c-=2;c>0?n.sqrTo(s,v):(d=s,s=v,v=d),n.mulTo(v,o[h],s)}for(;y>=0&&0==(e.data[y]&1<<i);)n.sqrTo(s,v),d=s,s=v,v=d,--i<0&&(i=this.DB-1,--y)}return n.revert(s)}function Xe(e){var t=this.s<0?this.negate():this.clone(),r=e.s<0?e.negate():e.clone();if(t.compareTo(r)<0){var n=t;t=r,r=n}var a=t.getLowestSetBit(),i=r.getLowestSetBit();if(i<0)return t;for(a<i&&(i=a),i>0&&(t.rShiftTo(i,t),r.rShiftTo(i,r));t.signum()>0;)(a=t.getLowestSetBit())>0&&t.rShiftTo(a,t),(a=r.getLowestSetBit())>0&&r.rShiftTo(a,r),t.compareTo(r)>=0?(t.subTo(r,t),t.rShiftTo(1,t)):(r.subTo(t,r),r.rShiftTo(1,r));return i>0&&r.lShiftTo(i,r),r}function Ye(e){if(e<=0)return 0;var t=this.DV%e,r=this.s<0?e-1:0;if(this.t>0)if(0==t)r=this.data[0]%e;else for(var n=this.t-1;n>=0;--n)r=(t*r+this.data[n])%e;return r}function Ze(e){var t=e.isEven();if(this.isEven()&&t||0==e.signum())return n.ZERO;for(var r=e.clone(),a=this.clone(),i=f(1),s=f(0),o=f(0),c=f(1);0!=r.signum();){for(;r.isEven();)r.rShiftTo(1,r),t?(i.isEven()&&s.isEven()||(i.addTo(this,i),s.subTo(e,s)),i.rShiftTo(1,i)):s.isEven()||s.subTo(e,s),s.rShiftTo(1,s);for(;a.isEven();)a.rShiftTo(1,a),t?(o.isEven()&&c.isEven()||(o.addTo(this,o),c.subTo(e,c)),o.rShiftTo(1,o)):c.isEven()||c.subTo(e,c),c.rShiftTo(1,c);r.compareTo(a)>=0?(r.subTo(a,r),t&&i.subTo(o,i),s.subTo(c,s)):(a.subTo(r,a),t&&o.subTo(i,o),c.subTo(s,c))}return 0!=a.compareTo(n.ONE)?n.ZERO:c.compareTo(e)>=0?c.subtract(e):c.signum()<0?(c.addTo(e,c),c.signum()<0?c.add(e):c):c}function Je(e){var t,r=this.abs();if(1==r.t&&r.data[0]<=ot[ot.length-1]){for(t=0;t<ot.length;++t)if(r.data[0]==ot[t])return!0;return!1}if(r.isEven())return!1;for(t=1;t<ot.length;){for(var n=ot[t],a=t+1;a<ot.length&&n<ct;)n*=ot[a++];for(n=r.modInt(n);t<a;)if(n%ot[t++]==0)return!1}return r.millerRabin(e)}function $e(e){var t=this.subtract(n.ONE),r=t.getLowestSetBit();if(r<=0)return!1;for(var a,i=t.shiftRight(r),s=et(),o=0;o<e;++o){do{a=new n(this.bitLength(),s)}while(a.compareTo(n.ONE)<=0||a.compareTo(t)>=0);var c=a.modPow(i,this);if(0!=c.compareTo(n.ONE)&&0!=c.compareTo(t)){for(var u=1;u++<r&&0!=c.compareTo(t);)if(c=c.modPowInt(2,this),0==c.compareTo(n.ONE))return!1;if(0!=c.compareTo(t))return!1}}return!0}function et(){return{nextBytes:function(e){for(var t=0;t<e.length;++t)e[t]=Math.floor(256*Math.random())}}}var tt=r(0);e.exports=tt.jsbn=tt.jsbn||{};var rt;tt.jsbn.BigInteger=n,"undefined"==typeof navigator?(n.prototype.am=o,rt=28):"Microsoft Internet Explorer"==navigator.appName?(n.prototype.am=s,rt=30):"Netscape"!=navigator.appName?(n.prototype.am=i,rt=26):(n.prototype.am=o,rt=28),n.prototype.DB=rt,n.prototype.DM=(1<<rt)-1,n.prototype.DV=1<<rt;n.prototype.FV=Math.pow(2,52),n.prototype.F1=52-rt,n.prototype.F2=2*rt-52;var nt,at,it="0123456789abcdefghijklmnopqrstuvwxyz",st=new Array;for(nt="0".charCodeAt(0),at=0;at<=9;++at)st[nt++]=at;for(nt="a".charCodeAt(0),at=10;at<36;++at)st[nt++]=at;for(nt="A".charCodeAt(0),at=10;at<36;++at)st[nt++]=at;R.prototype.convert=L,R.prototype.revert=_,R.prototype.reduce=U,R.prototype.mulTo=D,R.prototype.sqrTo=P,V.prototype.convert=x,V.prototype.revert=K,V.prototype.reduce=M,V.prototype.mulTo=q,V.prototype.sqrTo=F,n.prototype.copyTo=l,n.prototype.fromInt=p,n.prototype.fromString=h,n.prototype.clamp=d,n.prototype.dlShiftTo=S,n.prototype.drShiftTo=T,n.prototype.lShiftTo=b,n.prototype.rShiftTo=I,n.prototype.subTo=A,n.prototype.multiplyTo=B,n.prototype.squareTo=k,n.prototype.divRemTo=N,n.prototype.invDigit=O,n.prototype.isEven=H,n.prototype.exp=j,n.prototype.toString=y,n.prototype.negate=g,n.prototype.abs=v,n.prototype.compareTo=m,n.prototype.bitLength=E,n.prototype.mod=w,n.prototype.modPowInt=G,n.ZERO=f(0),n.ONE=f(1),Pe.prototype.convert=Oe,Pe.prototype.revert=Oe,Pe.prototype.mulTo=Ve,Pe.prototype.sqrTo=xe,qe.prototype.convert=He,qe.prototype.revert=je,qe.prototype.reduce=Ge,qe.prototype.mulTo=ze,qe.prototype.sqrTo=Qe;var ot=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509],ct=(1<<26)/ot[ot.length-1];n.prototype.chunkSize=Y,n.prototype.toRadix=J,n.prototype.fromRadix=$,n.prototype.fromNumber=ee,n.prototype.bitwiseTo=ie,n.prototype.changeBit=Te,n.prototype.addTo=Be,n.prototype.dMultiply=Ue,n.prototype.dAddOffset=De,n.prototype.multiplyLowerTo=Me,n.prototype.multiplyUpperTo=Fe,n.prototype.modInt=Ye,n.prototype.millerRabin=$e,n.prototype.clone=Q,n.prototype.intValue=z,n.prototype.byteValue=W,n.prototype.shortValue=X,n.prototype.signum=Z,n.prototype.toByteArray=te,n.prototype.equals=re,n.prototype.min=ne,n.prototype.max=ae,n.prototype.and=oe,n.prototype.or=ue,n.prototype.xor=pe,n.prototype.andNot=he,n.prototype.not=de,n.prototype.shiftLeft=ye,n.prototype.shiftRight=ge,n.prototype.getLowestSetBit=me,n.prototype.bitCount=Ee,n.prototype.testBit=Se,n.prototype.setBit=be,n.prototype.clearBit=Ie,n.prototype.flipBit=Ae,n.prototype.add=ke,n.prototype.subtract=Ne,n.prototype.multiply=we,n.prototype.divide=Re,n.prototype.remainder=Le,n.prototype.divideAndRemainder=_e,n.prototype.modPow=We,n.prototype.modInverse=Ze,n.prototype.pow=Ke,n.prototype.gcd=Xe,n.prototype.isProbablePrime=Je},function(e,t,r){var n=r(0);r(1),e.exports=n.cipher=n.cipher||{},n.cipher.algorithms=n.cipher.algorithms||{},n.cipher.createCipher=function(e,t){var r=e;if("string"==typeof r&&(r=n.cipher.getAlgorithm(r))&&(r=r()),!r)throw new Error("Unsupported algorithm: "+e);return new n.cipher.BlockCipher({algorithm:r,key:t,decrypt:!1})},n.cipher.createDecipher=function(e,t){var r=e;if("string"==typeof r&&(r=n.cipher.getAlgorithm(r))&&(r=r()),!r)throw new Error("Unsupported algorithm: "+e);return new n.cipher.BlockCipher({algorithm:r,key:t,decrypt:!0})},n.cipher.registerAlgorithm=function(e,t){e=e.toUpperCase(),n.cipher.algorithms[e]=t},n.cipher.getAlgorithm=function(e){return e=e.toUpperCase(),e in n.cipher.algorithms?n.cipher.algorithms[e]:null};var a=n.cipher.BlockCipher=function(e){this.algorithm=e.algorithm,this.mode=this.algorithm.mode,this.blockSize=this.mode.blockSize,this._finish=!1,this._input=null,this.output=null,this._op=e.decrypt?this.mode.decrypt:this.mode.encrypt,this._decrypt=e.decrypt,this.algorithm.initialize(e)};a.prototype.start=function(e){e=e||{};var t={};for(var r in e)t[r]=e[r];t.decrypt=this._decrypt,this._finish=!1,this._input=n.util.createBuffer(),this.output=e.output||n.util.createBuffer(),this.mode.start(t)},a.prototype.update=function(e){for(e&&this._input.putBuffer(e);!this._op.call(this.mode,this._input,this.output,this._finish)&&!this._finish;);this._input.compact()},a.prototype.finish=function(e){!e||"ECB"!==this.mode.name&&"CBC"!==this.mode.name||(this.mode.pad=function(t){return e(this.blockSize,t,!1)},this.mode.unpad=function(t){return e(this.blockSize,t,!0)});var t={};return t.decrypt=this._decrypt,t.overflow=this._input.length()%this.blockSize,!(!this._decrypt&&this.mode.pad&&!this.mode.pad(this._input,t))&&(this._finish=!0,this.update(),!(this._decrypt&&this.mode.unpad&&!this.mode.unpad(this.output,t))&&!(this.mode.afterFinish&&!this.mode.afterFinish(this.output,t)))}},function(e,t,r){function n(){o=String.fromCharCode(128),o+=i.util.fillString(String.fromCharCode(0),64),c=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,1,6,11,0,5,10,15,4,9,14,3,8,13,2,7,12,5,8,11,14,1,4,7,10,13,0,3,6,9,12,15,2,0,7,14,5,12,3,10,1,8,15,6,13,4,11,2,9],u=[7,12,17,22,7,12,17,22,7,12,17,22,7,12,17,22,5,9,14,20,5,9,14,20,5,9,14,20,5,9,14,20,4,11,16,23,4,11,16,23,4,11,16,23,4,11,16,23,6,10,15,21,6,10,15,21,6,10,15,21,6,10,15,21],l=new Array(64);for(var e=0;e<64;++e)l[e]=Math.floor(4294967296*Math.abs(Math.sin(e+1)));p=!0}function a(e,t,r){for(var n,a,i,s,o,p,f,h,d=r.length();d>=64;){for(a=e.h0,i=e.h1,s=e.h2,o=e.h3,h=0;h<16;++h)t[h]=r.getInt32Le(),p=o^i&(s^o),n=a+p+l[h]+t[h],f=u[h],a=o,o=s,s=i,i+=n<<f|n>>>32-f;for(;h<32;++h)p=s^o&(i^s),n=a+p+l[h]+t[c[h]],f=u[h],a=o,o=s,s=i,i+=n<<f|n>>>32-f;for(;h<48;++h)p=i^s^o,n=a+p+l[h]+t[c[h]],f=u[h],a=o,o=s,s=i,i+=n<<f|n>>>32-f;for(;h<64;++h)p=s^(i|~o),n=a+p+l[h]+t[c[h]],f=u[h],a=o,o=s,s=i,i+=n<<f|n>>>32-f;e.h0=e.h0+a|0,e.h1=e.h1+i|0,e.h2=e.h2+s|0,e.h3=e.h3+o|0,d-=64}}var i=r(0);r(4),r(1);var s=e.exports=i.md5=i.md5||{};i.md.md5=i.md.algorithms.md5=s,s.create=function(){p||n();var e=null,t=i.util.createBuffer(),r=new Array(16),s={algorithm:"md5",blockLength:64,digestLength:16,messageLength:0,fullMessageLength:null,messageLengthSize:8};return s.start=function(){s.messageLength=0,s.fullMessageLength=s.messageLength64=[];for(var r=s.messageLengthSize/4,n=0;n<r;++n)s.fullMessageLength.push(0);return t=i.util.createBuffer(),e={h0:1732584193,h1:4023233417,h2:2562383102,h3:271733878},s},s.start(),s.update=function(n,o){"utf8"===o&&(n=i.util.encodeUtf8(n));var c=n.length;s.messageLength+=c,c=[c/4294967296>>>0,c>>>0];for(var u=s.fullMessageLength.length-1;u>=0;--u)s.fullMessageLength[u]+=c[1],c[1]=c[0]+(s.fullMessageLength[u]/4294967296>>>0),s.fullMessageLength[u]=s.fullMessageLength[u]>>>0,c[0]=c[1]/4294967296>>>0;return t.putBytes(n),a(e,r,t),(t.read>2048||0===t.length())&&t.compact(),s},s.digest=function(){var n=i.util.createBuffer();n.putBytes(t.bytes());var c=s.fullMessageLength[s.fullMessageLength.length-1]+s.messageLengthSize,u=c&s.blockLength-1;n.putBytes(o.substr(0,s.blockLength-u));for(var l,p=0,f=s.fullMessageLength.length-1;f>=0;--f)l=8*s.fullMessageLength[f]+p,p=l/4294967296>>>0,n.putInt32Le(l>>>0);var h={h0:e.h0,h1:e.h1,h2:e.h2,h3:e.h3};a(h,r,n);var d=i.util.createBuffer();return d.putInt32Le(h.h0),d.putInt32Le(h.h1),d.putInt32Le(h.h2),d.putInt32Le(h.h3),d},s};var o=null,c=null,u=null,l=null,p=!1},function(e,t,r){var n=r(0);r(8),r(4),r(1);var a,i=n.pkcs5=n.pkcs5||{};n.util.isNodejs&&!n.options.usePureJavaScript&&(a=r(17)),e.exports=n.pbkdf2=i.pbkdf2=function(e,t,r,i,s,o){function c(){if(C>f)return o(null,m);d.start(null,null),d.update(t),d.update(n.util.int32ToBytes(C)),y=v=d.digest().getBytes(),E=2,u()}function u(){if(E<=r)return d.start(null,null),d.update(v),g=d.digest().getBytes(),y=n.util.xorBytes(y,g,l),v=g,++E,n.util.setImmediate(u);m+=C<f?y:y.substr(0,h),++C,c()}if("function"==typeof s&&(o=s,s=null),n.util.isNodejs&&!n.options.usePureJavaScript&&a.pbkdf2&&(null===s||"object"!=typeof s)&&(a.pbkdf2Sync.length>4||!s||"sha1"===s))return"string"!=typeof s&&(s="sha1"),e=Buffer.from(e,"binary"),t=Buffer.from(t,"binary"),o?4===a.pbkdf2Sync.length?a.pbkdf2(e,t,r,i,function(e,t){if(e)return o(e);o(null,t.toString("binary"))}):a.pbkdf2(e,t,r,i,s,function(e,t){if(e)return o(e);o(null,t.toString("binary"))}):4===a.pbkdf2Sync.length?a.pbkdf2Sync(e,t,r,i).toString("binary"):a.pbkdf2Sync(e,t,r,i,s).toString("binary");if(void 0!==s&&null!==s||(s="sha1"),"string"==typeof s){if(!(s in n.md.algorithms))throw new Error("Unknown hash algorithm: "+s);s=n.md[s].create()}var l=s.digestLength;if(i>4294967295*l){var p=new Error("Derived key is too long.");if(o)return o(p);throw p}var f=Math.ceil(i/l),h=i-(f-1)*l,d=n.hmac.create();d.start(s,e);var y,g,v,m="";if(!o){for(var C=1;C<=f;++C){d.start(null,null),d.update(t),d.update(n.util.int32ToBytes(C)),y=v=d.digest().getBytes();for(var E=2;E<=r;++E)d.start(null,null),d.update(v),g=d.digest().getBytes(),y=n.util.xorBytes(y,g,l),v=g;m+=C<f?y:y.substr(0,h)}return m}var E,C=1;c()}},function(e,t){},function(e,t,r){function n(e,t){"string"==typeof t&&(t={shortName:t});for(var r,n=null,a=0;null===n&&a<e.attributes.length;++a)r=e.attributes[a],t.type&&t.type===r.type?n=r:t.name&&t.name===r.name?n=r:t.shortName&&t.shortName===r.shortName&&(n=r);return n}function a(e){for(var t,r,n=p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[]),a=e.attributes,i=0;i<a.length;++i){t=a[i];var s=t.value,o=p.Type.PRINTABLESTRING;"valueTagClass"in t&&(o=t.valueTagClass)===p.Type.UTF8&&(s=l.util.encodeUtf8(s)),r=p.create(p.Class.UNIVERSAL,p.Type.SET,!0,[p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[p.create(p.Class.UNIVERSAL,p.Type.OID,!1,p.oidToDer(t.type).getBytes()),p.create(p.Class.UNIVERSAL,o,!1,s)])]),n.value.push(r)}return n}function i(e){for(var t,r=0;r<e.length;++r){if(t=e[r],void 0===t.name&&(t.type&&t.type in f.oids?t.name=f.oids[t.type]:t.shortName&&t.shortName in d&&(t.name=f.oids[d[t.shortName]])),void 0===t.type){if(!(t.name&&t.name in f.oids)){var n=new Error("Attribute type not specified.");throw n.attribute=t,n}t.type=f.oids[t.name]}if(void 0===t.shortName&&t.name&&t.name in d&&(t.shortName=d[t.name]),t.type===h.extensionRequest&&(t.valueConstructed=!0,t.valueTagClass=p.Type.SEQUENCE,!t.value&&t.extensions)){t.value=[];for(var a=0;a<t.extensions.length;++a)t.value.push(f.certificateExtensionToAsn1(s(t.extensions[a])))}if(void 0===t.value){var n=new Error("Attribute value not specified.");throw n.attribute=t,n}}}function s(e,t){if(t=t||{},void 0===e.name&&e.id&&e.id in f.oids&&(e.name=f.oids[e.id]),void 0===e.id){if(!(e.name&&e.name in f.oids)){var r=new Error("Extension ID not specified.");throw r.extension=e,r}e.id=f.oids[e.name]}if(void 0!==e.value)return e;if("keyUsage"===e.name){var n=0,i=0,s=0;e.digitalSignature&&(i|=128,n=7),e.nonRepudiation&&(i|=64,n=6),e.keyEncipherment&&(i|=32,n=5),e.dataEncipherment&&(i|=16,n=4),e.keyAgreement&&(i|=8,n=3),e.keyCertSign&&(i|=4,n=2),e.cRLSign&&(i|=2,n=1),e.encipherOnly&&(i|=1,n=0),e.decipherOnly&&(s|=128,n=7);var o=String.fromCharCode(n);0!==s?o+=String.fromCharCode(i)+String.fromCharCode(s):0!==i&&(o+=String.fromCharCode(i)),e.value=p.create(p.Class.UNIVERSAL,p.Type.BITSTRING,!1,o)}else if("basicConstraints"===e.name)e.value=p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[]),e.cA&&e.value.value.push(p.create(p.Class.UNIVERSAL,p.Type.BOOLEAN,!1,String.fromCharCode(255))),"pathLenConstraint"in e&&e.value.value.push(p.create(p.Class.UNIVERSAL,p.Type.INTEGER,!1,p.integerToDer(e.pathLenConstraint).getBytes()));else if("extKeyUsage"===e.name){e.value=p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[]);var c=e.value.value;for(var u in e)!0===e[u]&&(u in h?c.push(p.create(p.Class.UNIVERSAL,p.Type.OID,!1,p.oidToDer(h[u]).getBytes())):-1!==u.indexOf(".")&&c.push(p.create(p.Class.UNIVERSAL,p.Type.OID,!1,p.oidToDer(u).getBytes())))}else if("nsCertType"===e.name){var n=0,i=0;e.client&&(i|=128,n=7),e.server&&(i|=64,n=6),e.email&&(i|=32,n=5),e.objsign&&(i|=16,n=4),e.reserved&&(i|=8,n=3),e.sslCA&&(i|=4,n=2),e.emailCA&&(i|=2,n=1),e.objCA&&(i|=1,n=0);var o=String.fromCharCode(n);0!==i&&(o+=String.fromCharCode(i)),e.value=p.create(p.Class.UNIVERSAL,p.Type.BITSTRING,!1,o)}else if("subjectAltName"===e.name||"issuerAltName"===e.name){e.value=p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[]);for(var d,y=0;y<e.altNames.length;++y){d=e.altNames[y];var o=d.value;if(7===d.type&&d.ip){if(null===(o=l.util.bytesFromIP(d.ip))){var r=new Error('Extension "ip" value is not a valid IPv4 or IPv6 address.');throw r.extension=e,r}}else 8===d.type&&(o=d.oid?p.oidToDer(p.oidToDer(d.oid)):p.oidToDer(o));e.value.value.push(p.create(p.Class.CONTEXT_SPECIFIC,d.type,!1,o))}}else if("nsComment"===e.name&&t.cert){if(!/^[\x00-\x7F]*$/.test(e.comment)||e.comment.length<1||e.comment.length>128)throw new Error('Invalid "nsComment" content.');e.value=p.create(p.Class.UNIVERSAL,p.Type.IA5STRING,!1,e.comment)}else if("subjectKeyIdentifier"===e.name&&t.cert){var g=t.cert.generateSubjectKeyIdentifier();e.subjectKeyIdentifier=g.toHex(),e.value=p.create(p.Class.UNIVERSAL,p.Type.OCTETSTRING,!1,g.getBytes())}else if("authorityKeyIdentifier"===e.name&&t.cert){e.value=p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[]);var c=e.value.value;if(e.keyIdentifier){var v=!0===e.keyIdentifier?t.cert.generateSubjectKeyIdentifier().getBytes():e.keyIdentifier;c.push(p.create(p.Class.CONTEXT_SPECIFIC,0,!1,v))}if(e.authorityCertIssuer){var m=[p.create(p.Class.CONTEXT_SPECIFIC,4,!0,[a(!0===e.authorityCertIssuer?t.cert.issuer:e.authorityCertIssuer)])];c.push(p.create(p.Class.CONTEXT_SPECIFIC,1,!0,m))}if(e.serialNumber){var C=l.util.hexToBytes(!0===e.serialNumber?t.cert.serialNumber:e.serialNumber);c.push(p.create(p.Class.CONTEXT_SPECIFIC,2,!1,C))}}else if("cRLDistributionPoints"===e.name){e.value=p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[]);for(var d,c=e.value.value,E=p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[]),S=p.create(p.Class.CONTEXT_SPECIFIC,0,!0,[]),y=0;y<e.altNames.length;++y){d=e.altNames[y];var o=d.value;if(7===d.type&&d.ip){if(null===(o=l.util.bytesFromIP(d.ip))){var r=new Error('Extension "ip" value is not a valid IPv4 or IPv6 address.');throw r.extension=e,r}}else 8===d.type&&(o=d.oid?p.oidToDer(p.oidToDer(d.oid)):p.oidToDer(o));S.value.push(p.create(p.Class.CONTEXT_SPECIFIC,d.type,!1,o))}E.value.push(p.create(p.Class.CONTEXT_SPECIFIC,0,!0,[S])),c.push(E)}if(void 0===e.value){var r=new Error("Extension value not specified.");throw r.extension=e,r}return e}function o(e,t){switch(e){case h["RSASSA-PSS"]:var r=[];return void 0!==t.hash.algorithmOid&&r.push(p.create(p.Class.CONTEXT_SPECIFIC,0,!0,[p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[p.create(p.Class.UNIVERSAL,p.Type.OID,!1,p.oidToDer(t.hash.algorithmOid).getBytes()),p.create(p.Class.UNIVERSAL,p.Type.NULL,!1,"")])])),void 0!==t.mgf.algorithmOid&&r.push(p.create(p.Class.CONTEXT_SPECIFIC,1,!0,[p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[p.create(p.Class.UNIVERSAL,p.Type.OID,!1,p.oidToDer(t.mgf.algorithmOid).getBytes()),p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[p.create(p.Class.UNIVERSAL,p.Type.OID,!1,p.oidToDer(t.mgf.hash.algorithmOid).getBytes()),p.create(p.Class.UNIVERSAL,p.Type.NULL,!1,"")])])])),void 0!==t.saltLength&&r.push(p.create(p.Class.CONTEXT_SPECIFIC,2,!0,[p.create(p.Class.UNIVERSAL,p.Type.INTEGER,!1,p.integerToDer(t.saltLength).getBytes())])),p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,r);default:return p.create(p.Class.UNIVERSAL,p.Type.NULL,!1,"")}}function c(e){var t=p.create(p.Class.CONTEXT_SPECIFIC,0,!0,[]);if(0===e.attributes.length)return t;for(var r=e.attributes,n=0;n<r.length;++n){var a=r[n],i=a.value,s=p.Type.UTF8;"valueTagClass"in a&&(s=a.valueTagClass),s===p.Type.UTF8&&(i=l.util.encodeUtf8(i));var o=!1;"valueConstructed"in a&&(o=a.valueConstructed);var c=p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[p.create(p.Class.UNIVERSAL,p.Type.OID,!1,p.oidToDer(a.type).getBytes()),p.create(p.Class.UNIVERSAL,p.Type.SET,!0,[p.create(p.Class.UNIVERSAL,s,o,i)])]);t.value.push(c)}return t}function u(e){return e>=S&&e<T?p.create(p.Class.UNIVERSAL,p.Type.UTCTIME,!1,p.dateToUtcTime(e)):p.create(p.Class.UNIVERSAL,p.Type.GENERALIZEDTIME,!1,p.dateToGeneralizedTime(e))}var l=r(0);r(5),r(3),r(11),r(4),r(42),r(6),r(7),r(19),r(12),r(1);var p=l.asn1,f=e.exports=l.pki=l.pki||{},h=f.oids,d={};d.CN=h.commonName,d.commonName="CN",d.C=h.countryName,d.countryName="C",d.L=h.localityName,d.localityName="L",d.ST=h.stateOrProvinceName,d.stateOrProvinceName="ST",d.O=h.organizationName,d.organizationName="O",d.OU=h.organizationalUnitName,d.organizationalUnitName="OU",d.E=h.emailAddress,d.emailAddress="E";var y=l.pki.rsa.publicKeyValidator,g={name:"Certificate",tagClass:p.Class.UNIVERSAL,type:p.Type.SEQUENCE,constructed:!0,value:[{name:"Certificate.TBSCertificate",tagClass:p.Class.UNIVERSAL,type:p.Type.SEQUENCE,constructed:!0,captureAsn1:"tbsCertificate",value:[{name:"Certificate.TBSCertificate.version",tagClass:p.Class.CONTEXT_SPECIFIC,type:0,constructed:!0,optional:!0,value:[{name:"Certificate.TBSCertificate.version.integer",tagClass:p.Class.UNIVERSAL,type:p.Type.INTEGER,constructed:!1,capture:"certVersion"}]},{name:"Certificate.TBSCertificate.serialNumber",tagClass:p.Class.UNIVERSAL,type:p.Type.INTEGER,constructed:!1,capture:"certSerialNumber"},{name:"Certificate.TBSCertificate.signature",tagClass:p.Class.UNIVERSAL,type:p.Type.SEQUENCE,constructed:!0,value:[{name:"Certificate.TBSCertificate.signature.algorithm",tagClass:p.Class.UNIVERSAL,type:p.Type.OID,constructed:!1,capture:"certinfoSignatureOid"},{name:"Certificate.TBSCertificate.signature.parameters",tagClass:p.Class.UNIVERSAL,optional:!0,captureAsn1:"certinfoSignatureParams"}]},{name:"Certificate.TBSCertificate.issuer",tagClass:p.Class.UNIVERSAL,type:p.Type.SEQUENCE,constructed:!0,captureAsn1:"certIssuer"},{name:"Certificate.TBSCertificate.validity",tagClass:p.Class.UNIVERSAL,type:p.Type.SEQUENCE,constructed:!0,value:[{name:"Certificate.TBSCertificate.validity.notBefore (utc)",tagClass:p.Class.UNIVERSAL,type:p.Type.UTCTIME,constructed:!1,optional:!0,capture:"certValidity1UTCTime"},{name:"Certificate.TBSCertificate.validity.notBefore (generalized)",tagClass:p.Class.UNIVERSAL,type:p.Type.GENERALIZEDTIME,constructed:!1,optional:!0,capture:"certValidity2GeneralizedTime"},{name:"Certificate.TBSCertificate.validity.notAfter (utc)",tagClass:p.Class.UNIVERSAL,type:p.Type.UTCTIME,constructed:!1,optional:!0,capture:"certValidity3UTCTime"},{name:"Certificate.TBSCertificate.validity.notAfter (generalized)",tagClass:p.Class.UNIVERSAL,type:p.Type.GENERALIZEDTIME,constructed:!1,optional:!0,capture:"certValidity4GeneralizedTime"}]},{name:"Certificate.TBSCertificate.subject",tagClass:p.Class.UNIVERSAL,type:p.Type.SEQUENCE,constructed:!0,captureAsn1:"certSubject"},y,{name:"Certificate.TBSCertificate.issuerUniqueID",tagClass:p.Class.CONTEXT_SPECIFIC,type:1,constructed:!0,optional:!0,value:[{name:"Certificate.TBSCertificate.issuerUniqueID.id",tagClass:p.Class.UNIVERSAL,type:p.Type.BITSTRING,constructed:!1,captureBitStringValue:"certIssuerUniqueId"}]},{name:"Certificate.TBSCertificate.subjectUniqueID",tagClass:p.Class.CONTEXT_SPECIFIC,type:2,constructed:!0,optional:!0,value:[{name:"Certificate.TBSCertificate.subjectUniqueID.id",tagClass:p.Class.UNIVERSAL,type:p.Type.BITSTRING,constructed:!1,captureBitStringValue:"certSubjectUniqueId"}]},{name:"Certificate.TBSCertificate.extensions",tagClass:p.Class.CONTEXT_SPECIFIC,type:3,constructed:!0,captureAsn1:"certExtensions",optional:!0}]},{name:"Certificate.signatureAlgorithm",tagClass:p.Class.UNIVERSAL,type:p.Type.SEQUENCE,constructed:!0,value:[{name:"Certificate.signatureAlgorithm.algorithm",tagClass:p.Class.UNIVERSAL,type:p.Type.OID,constructed:!1,capture:"certSignatureOid"},{name:"Certificate.TBSCertificate.signature.parameters",tagClass:p.Class.UNIVERSAL,optional:!0,captureAsn1:"certSignatureParams"}]},{name:"Certificate.signatureValue",tagClass:p.Class.UNIVERSAL,type:p.Type.BITSTRING,constructed:!1,captureBitStringValue:"certSignature"}]},v={name:"rsapss",tagClass:p.Class.UNIVERSAL,type:p.Type.SEQUENCE,constructed:!0,value:[{name:"rsapss.hashAlgorithm",tagClass:p.Class.CONTEXT_SPECIFIC,type:0,constructed:!0,value:[{name:"rsapss.hashAlgorithm.AlgorithmIdentifier",tagClass:p.Class.UNIVERSAL,type:p.Class.SEQUENCE,constructed:!0,optional:!0,value:[{name:"rsapss.hashAlgorithm.AlgorithmIdentifier.algorithm",tagClass:p.Class.UNIVERSAL,type:p.Type.OID,constructed:!1,capture:"hashOid"}]}]},{name:"rsapss.maskGenAlgorithm",tagClass:p.Class.CONTEXT_SPECIFIC,type:1,constructed:!0,value:[{name:"rsapss.maskGenAlgorithm.AlgorithmIdentifier",tagClass:p.Class.UNIVERSAL,type:p.Class.SEQUENCE,constructed:!0,optional:!0,value:[{name:"rsapss.maskGenAlgorithm.AlgorithmIdentifier.algorithm",tagClass:p.Class.UNIVERSAL,type:p.Type.OID,constructed:!1,capture:"maskGenOid"},{name:"rsapss.maskGenAlgorithm.AlgorithmIdentifier.params",tagClass:p.Class.UNIVERSAL,type:p.Type.SEQUENCE,constructed:!0,value:[{name:"rsapss.maskGenAlgorithm.AlgorithmIdentifier.params.algorithm",tagClass:p.Class.UNIVERSAL,type:p.Type.OID,constructed:!1,capture:"maskGenHashOid"}]}]}]},{name:"rsapss.saltLength",tagClass:p.Class.CONTEXT_SPECIFIC,type:2,optional:!0,value:[{name:"rsapss.saltLength.saltLength",tagClass:p.Class.UNIVERSAL,type:p.Class.INTEGER,constructed:!1,capture:"saltLength"}]},{name:"rsapss.trailerField",tagClass:p.Class.CONTEXT_SPECIFIC,type:3,optional:!0,value:[{name:"rsapss.trailer.trailer",tagClass:p.Class.UNIVERSAL,type:p.Class.INTEGER,constructed:!1,capture:"trailer"}]}]},m={name:"CertificationRequestInfo",tagClass:p.Class.UNIVERSAL,type:p.Type.SEQUENCE,constructed:!0,captureAsn1:"certificationRequestInfo",value:[{name:"CertificationRequestInfo.integer",tagClass:p.Class.UNIVERSAL,type:p.Type.INTEGER,constructed:!1,capture:"certificationRequestInfoVersion"},{name:"CertificationRequestInfo.subject",tagClass:p.Class.UNIVERSAL,type:p.Type.SEQUENCE,constructed:!0,captureAsn1:"certificationRequestInfoSubject"},y,{name:"CertificationRequestInfo.attributes",tagClass:p.Class.CONTEXT_SPECIFIC,type:0,constructed:!0,optional:!0,capture:"certificationRequestInfoAttributes",value:[{name:"CertificationRequestInfo.attributes",tagClass:p.Class.UNIVERSAL,type:p.Type.SEQUENCE,constructed:!0,value:[{name:"CertificationRequestInfo.attributes.type",tagClass:p.Class.UNIVERSAL,type:p.Type.OID,constructed:!1},{name:"CertificationRequestInfo.attributes.value",tagClass:p.Class.UNIVERSAL,type:p.Type.SET,constructed:!0}]}]}]},C={name:"CertificationRequest",tagClass:p.Class.UNIVERSAL,type:p.Type.SEQUENCE,constructed:!0,captureAsn1:"csr",value:[m,{name:"CertificationRequest.signatureAlgorithm",tagClass:p.Class.UNIVERSAL,type:p.Type.SEQUENCE,constructed:!0,value:[{name:"CertificationRequest.signatureAlgorithm.algorithm",tagClass:p.Class.UNIVERSAL,type:p.Type.OID,constructed:!1,capture:"csrSignatureOid"},{name:"CertificationRequest.signatureAlgorithm.parameters",tagClass:p.Class.UNIVERSAL,optional:!0,captureAsn1:"csrSignatureParams"}]},{name:"CertificationRequest.signature",tagClass:p.Class.UNIVERSAL,type:p.Type.BITSTRING,constructed:!1,captureBitStringValue:"csrSignature"}]};f.RDNAttributesAsArray=function(e,t){for(var r,n,a,i=[],s=0;s<e.value.length;++s){r=e.value[s];for(var o=0;o<r.value.length;++o)a={},n=r.value[o],a.type=p.derToOid(n.value[0].value),a.value=n.value[1].value,a.valueTagClass=n.value[1].type,a.type in h&&(a.name=h[a.type],a.name in d&&(a.shortName=d[a.name])),t&&(t.update(a.type),t.update(a.value)),i.push(a)}return i},f.CRIAttributesAsArray=function(e){for(var t=[],r=0;r<e.length;++r)for(var n=e[r],a=p.derToOid(n.value[0].value),i=n.value[1].value,s=0;s<i.length;++s){var o={};if(o.type=a,o.value=i[s].value,o.valueTagClass=i[s].type,o.type in h&&(o.name=h[o.type],o.name in d&&(o.shortName=d[o.name])),o.type===h.extensionRequest){o.extensions=[];for(var c=0;c<o.value.length;++c)o.extensions.push(f.certificateExtensionFromAsn1(o.value[c]))}t.push(o)}return t};var E=function(e,t,r){var n={};if(e!==h["RSASSA-PSS"])return n;r&&(n={hash:{algorithmOid:h.sha1},mgf:{algorithmOid:h.mgf1,hash:{algorithmOid:h.sha1}},saltLength:20});var a={},i=[];if(!p.validate(t,v,a,i)){var s=new Error("Cannot read RSASSA-PSS parameter block.");throw s.errors=i,s}return void 0!==a.hashOid&&(n.hash=n.hash||{},n.hash.algorithmOid=p.derToOid(a.hashOid)),void 0!==a.maskGenOid&&(n.mgf=n.mgf||{},n.mgf.algorithmOid=p.derToOid(a.maskGenOid),n.mgf.hash=n.mgf.hash||{},n.mgf.hash.algorithmOid=p.derToOid(a.maskGenHashOid)),void 0!==a.saltLength&&(n.saltLength=a.saltLength.charCodeAt(0)),n};f.certificateFromPem=function(e,t,r){var n=l.pem.decode(e)[0];if("CERTIFICATE"!==n.type&&"X509 CERTIFICATE"!==n.type&&"TRUSTED CERTIFICATE"!==n.type){var a=new Error('Could not convert certificate from PEM; PEM header type is not "CERTIFICATE", "X509 CERTIFICATE", or "TRUSTED CERTIFICATE".');throw a.headerType=n.type,a}if(n.procType&&"ENCRYPTED"===n.procType.type)throw new Error("Could not convert certificate from PEM; PEM is encrypted.");var i=p.fromDer(n.body,r);return f.certificateFromAsn1(i,t)},f.certificateToPem=function(e,t){var r={type:"CERTIFICATE",body:p.toDer(f.certificateToAsn1(e)).getBytes()};return l.pem.encode(r,{maxline:t})},f.publicKeyFromPem=function(e){var t=l.pem.decode(e)[0];if("PUBLIC KEY"!==t.type&&"RSA PUBLIC KEY"!==t.type){var r=new Error('Could not convert public key from PEM; PEM header type is not "PUBLIC KEY" or "RSA PUBLIC KEY".');throw r.headerType=t.type,r}if(t.procType&&"ENCRYPTED"===t.procType.type)throw new Error("Could not convert public key from PEM; PEM is encrypted.");var n=p.fromDer(t.body);return f.publicKeyFromAsn1(n)},f.publicKeyToPem=function(e,t){var r={type:"PUBLIC KEY",body:p.toDer(f.publicKeyToAsn1(e)).getBytes()};return l.pem.encode(r,{maxline:t})},f.publicKeyToRSAPublicKeyPem=function(e,t){var r={type:"RSA PUBLIC KEY",body:p.toDer(f.publicKeyToRSAPublicKey(e)).getBytes()};return l.pem.encode(r,{maxline:t})},f.getPublicKeyFingerprint=function(e,t){t=t||{};var r,n=t.md||l.md.sha1.create(),a=t.type||"RSAPublicKey";switch(a){case"RSAPublicKey":r=p.toDer(f.publicKeyToRSAPublicKey(e)).getBytes();break;case"SubjectPublicKeyInfo":r=p.toDer(f.publicKeyToAsn1(e)).getBytes();break;default:throw new Error('Unknown fingerprint type "'+t.type+'".')}n.start(),n.update(r);var i=n.digest();if("hex"===t.encoding){var s=i.toHex();return t.delimiter?s.match(/.{2}/g).join(t.delimiter):s}if("binary"===t.encoding)return i.getBytes();if(t.encoding)throw new Error('Unknown encoding "'+t.encoding+'".');return i},f.certificationRequestFromPem=function(e,t,r){var n=l.pem.decode(e)[0];if("CERTIFICATE REQUEST"!==n.type){var a=new Error('Could not convert certification request from PEM; PEM header type is not "CERTIFICATE REQUEST".');throw a.headerType=n.type,a}if(n.procType&&"ENCRYPTED"===n.procType.type)throw new Error("Could not convert certification request from PEM; PEM is encrypted.");var i=p.fromDer(n.body,r);return f.certificationRequestFromAsn1(i,t)},f.certificationRequestToPem=function(e,t){var r={type:"CERTIFICATE REQUEST",body:p.toDer(f.certificationRequestToAsn1(e)).getBytes()};return l.pem.encode(r,{maxline:t})},f.createCertificate=function(){var e={};return e.version=2,e.serialNumber="00",e.signatureOid=null,e.signature=null,e.siginfo={},e.siginfo.algorithmOid=null,e.validity={},e.validity.notBefore=new Date,e.validity.notAfter=new Date,e.issuer={},e.issuer.getField=function(t){return n(e.issuer,t)},e.issuer.addField=function(t){i([t]),e.issuer.attributes.push(t)},e.issuer.attributes=[],e.issuer.hash=null,e.subject={},e.subject.getField=function(t){return n(e.subject,t)},e.subject.addField=function(t){i([t]),e.subject.attributes.push(t)},e.subject.attributes=[],e.subject.hash=null,e.extensions=[],e.publicKey=null,e.md=null,e.setSubject=function(t,r){i(t),e.subject.attributes=t,delete e.subject.uniqueId,r&&(e.subject.uniqueId=r),e.subject.hash=null},e.setIssuer=function(t,r){i(t),e.issuer.attributes=t,delete e.issuer.uniqueId,r&&(e.issuer.uniqueId=r),e.issuer.hash=null},e.setExtensions=function(t){for(var r=0;r<t.length;++r)s(t[r],{cert:e});e.extensions=t},e.getExtension=function(t){"string"==typeof t&&(t={name:t});for(var r,n=null,a=0;null===n&&a<e.extensions.length;++a)r=e.extensions[a],t.id&&r.id===t.id?n=r:t.name&&r.name===t.name&&(n=r);return n},e.sign=function(t,r){e.md=r||l.md.sha1.create();var n=h[e.md.algorithm+"WithRSAEncryption"];if(!n){var a=new Error("Could not compute certificate digest. Unknown message digest algorithm OID.");throw a.algorithm=e.md.algorithm,a}e.signatureOid=e.siginfo.algorithmOid=n,e.tbsCertificate=f.getTBSCertificate(e);var i=p.toDer(e.tbsCertificate);e.md.update(i.getBytes()),e.signature=t.sign(e.md)},e.verify=function(t){var r=!1;if(!e.issued(t)){var n=t.issuer,a=e.subject,i=new Error("The parent certificate did not issue the given child certificate; the child certificate's issuer does not match the parent's subject.");throw i.expectedIssuer=n.attributes,i.actualIssuer=a.attributes,i}var s=t.md;if(null===s){if(t.signatureOid in h){switch(h[t.signatureOid]){case"sha1WithRSAEncryption":s=l.md.sha1.create();break;case"md5WithRSAEncryption":s=l.md.md5.create();break;case"sha256WithRSAEncryption":s=l.md.sha256.create();break;case"sha384WithRSAEncryption":s=l.md.sha384.create();break;case"sha512WithRSAEncryption":s=l.md.sha512.create();break;case"RSASSA-PSS":s=l.md.sha256.create()}}if(null===s){var i=new Error("Could not compute certificate digest. Unknown signature OID.");throw i.signatureOid=t.signatureOid,i}var o=t.tbsCertificate||f.getTBSCertificate(t),c=p.toDer(o);s.update(c.getBytes())}if(null!==s){var u;switch(t.signatureOid){case h.sha1WithRSAEncryption:u=void 0;break;case h["RSASSA-PSS"]:var d,y;if(void 0===(d=h[t.signatureParameters.mgf.hash.algorithmOid])||void 0===l.md[d]){var i=new Error("Unsupported MGF hash function.");throw i.oid=t.signatureParameters.mgf.hash.algorithmOid,i.name=d,i}if(void 0===(y=h[t.signatureParameters.mgf.algorithmOid])||void 0===l.mgf[y]){var i=new Error("Unsupported MGF function.");throw i.oid=t.signatureParameters.mgf.algorithmOid,i.name=y,i}if(y=l.mgf[y].create(l.md[d].create()),void 0===(d=h[t.signatureParameters.hash.algorithmOid])||void 0===l.md[d])throw{message:"Unsupported RSASSA-PSS hash function.",oid:t.signatureParameters.hash.algorithmOid,name:d};u=l.pss.create(l.md[d].create(),y,t.signatureParameters.saltLength)}r=e.publicKey.verify(s.digest().getBytes(),t.signature,u)}return r},e.isIssuer=function(t){var r=!1,n=e.issuer,a=t.subject;if(n.hash&&a.hash)r=n.hash===a.hash;else if(n.attributes.length===a.attributes.length){r=!0;for(var i,s,o=0;r&&o<n.attributes.length;++o)i=n.attributes[o],s=a.attributes[o],i.type===s.type&&i.value===s.value||(r=!1)}return r},e.issued=function(t){return t.isIssuer(e)},e.generateSubjectKeyIdentifier=function(){return f.getPublicKeyFingerprint(e.publicKey,{type:"RSAPublicKey"})},e.verifySubjectKeyIdentifier=function(){for(var t=h.subjectKeyIdentifier,r=0;r<e.extensions.length;++r){var n=e.extensions[r];if(n.id===t){var a=e.generateSubjectKeyIdentifier().getBytes();return l.util.hexToBytes(n.subjectKeyIdentifier)===a}}return!1},e},f.certificateFromAsn1=function(e,t){var r={},a=[];if(!p.validate(e,g,r,a)){var s=new Error("Cannot read X.509 certificate. ASN.1 object is not an X509v3 Certificate.");throw s.errors=a,s}var o=p.derToOid(r.publicKeyOid);if(o!==f.oids.rsaEncryption)throw new Error("Cannot read public key. OID is not RSA.");var c=f.createCertificate();c.version=r.certVersion?r.certVersion.charCodeAt(0):0;var u=l.util.createBuffer(r.certSerialNumber);c.serialNumber=u.toHex(),c.signatureOid=l.asn1.derToOid(r.certSignatureOid),c.signatureParameters=E(c.signatureOid,r.certSignatureParams,!0),c.siginfo.algorithmOid=l.asn1.derToOid(r.certinfoSignatureOid),c.siginfo.parameters=E(c.siginfo.algorithmOid,r.certinfoSignatureParams,!1),c.signature=r.certSignature;var d=[];if(void 0!==r.certValidity1UTCTime&&d.push(p.utcTimeToDate(r.certValidity1UTCTime)),void 0!==r.certValidity2GeneralizedTime&&d.push(p.generalizedTimeToDate(r.certValidity2GeneralizedTime)),void 0!==r.certValidity3UTCTime&&d.push(p.utcTimeToDate(r.certValidity3UTCTime)),void 0!==r.certValidity4GeneralizedTime&&d.push(p.generalizedTimeToDate(r.certValidity4GeneralizedTime)),d.length>2)throw new Error("Cannot read notBefore/notAfter validity times; more than two times were provided in the certificate.");if(d.length<2)throw new Error("Cannot read notBefore/notAfter validity times; they were not provided as either UTCTime or GeneralizedTime.");if(c.validity.notBefore=d[0],c.validity.notAfter=d[1],c.tbsCertificate=r.tbsCertificate,t){if(c.md=null,c.signatureOid in h){var o=h[c.signatureOid];switch(o){case"sha1WithRSAEncryption":c.md=l.md.sha1.create();break;case"md5WithRSAEncryption":c.md=l.md.md5.create();break;case"sha256WithRSAEncryption":c.md=l.md.sha256.create();break;case"sha384WithRSAEncryption":c.md=l.md.sha384.create();break;case"sha512WithRSAEncryption":c.md=l.md.sha512.create();break;case"RSASSA-PSS":c.md=l.md.sha256.create()}}if(null===c.md){var s=new Error("Could not compute certificate digest. Unknown signature OID.");throw s.signatureOid=c.signatureOid,s}var y=p.toDer(c.tbsCertificate);c.md.update(y.getBytes())}var v=l.md.sha1.create();c.issuer.getField=function(e){return n(c.issuer,e)},c.issuer.addField=function(e){i([e]),c.issuer.attributes.push(e)},c.issuer.attributes=f.RDNAttributesAsArray(r.certIssuer,v),r.certIssuerUniqueId&&(c.issuer.uniqueId=r.certIssuerUniqueId),c.issuer.hash=v.digest().toHex();var m=l.md.sha1.create();return c.subject.getField=function(e){return n(c.subject,e)},c.subject.addField=function(e){i([e]),c.subject.attributes.push(e)},c.subject.attributes=f.RDNAttributesAsArray(r.certSubject,m),r.certSubjectUniqueId&&(c.subject.uniqueId=r.certSubjectUniqueId),c.subject.hash=m.digest().toHex(),r.certExtensions?c.extensions=f.certificateExtensionsFromAsn1(r.certExtensions):c.extensions=[],c.publicKey=f.publicKeyFromAsn1(r.subjectPublicKeyInfo),c},f.certificateExtensionsFromAsn1=function(e){for(var t=[],r=0;r<e.value.length;++r)for(var n=e.value[r],a=0;a<n.value.length;++a)t.push(f.certificateExtensionFromAsn1(n.value[a]));return t},f.certificateExtensionFromAsn1=function(e){var t={};if(t.id=p.derToOid(e.value[0].value),t.critical=!1,e.value[1].type===p.Type.BOOLEAN?(t.critical=0!==e.value[1].value.charCodeAt(0),t.value=e.value[2].value):t.value=e.value[1].value,t.id in h)if(t.name=h[t.id],"keyUsage"===t.name){var r=p.fromDer(t.value),n=0,a=0;r.value.length>1&&(n=r.value.charCodeAt(1),a=r.value.length>2?r.value.charCodeAt(2):0),t.digitalSignature=128==(128&n),t.nonRepudiation=64==(64&n),t.keyEncipherment=32==(32&n),t.dataEncipherment=16==(16&n),t.keyAgreement=8==(8&n),t.keyCertSign=4==(4&n),t.cRLSign=2==(2&n),t.encipherOnly=1==(1&n),t.decipherOnly=128==(128&a)}else if("basicConstraints"===t.name){var r=p.fromDer(t.value);r.value.length>0&&r.value[0].type===p.Type.BOOLEAN?t.cA=0!==r.value[0].value.charCodeAt(0):t.cA=!1;var i=null;r.value.length>0&&r.value[0].type===p.Type.INTEGER?i=r.value[0].value:r.value.length>1&&(i=r.value[1].value),null!==i&&(t.pathLenConstraint=p.derToInteger(i))}else if("extKeyUsage"===t.name)for(var r=p.fromDer(t.value),s=0;s<r.value.length;++s){var o=p.derToOid(r.value[s].value);o in h?t[h[o]]=!0:t[o]=!0}else if("nsCertType"===t.name){var r=p.fromDer(t.value),n=0;r.value.length>1&&(n=r.value.charCodeAt(1)),t.client=128==(128&n),t.server=64==(64&n),t.email=32==(32&n),t.objsign=16==(16&n),t.reserved=8==(8&n),t.sslCA=4==(4&n),t.emailCA=2==(2&n),t.objCA=1==(1&n)}else if("subjectAltName"===t.name||"issuerAltName"===t.name){t.altNames=[];for(var c,r=p.fromDer(t.value),u=0;u<r.value.length;++u){c=r.value[u];var f={type:c.type,value:c.value};switch(t.altNames.push(f),c.type){case 1:case 2:case 6:break;case 7:f.ip=l.util.bytesToIP(c.value);break;case 8:f.oid=p.derToOid(c.value)}}}else if("subjectKeyIdentifier"===t.name){var r=p.fromDer(t.value);t.subjectKeyIdentifier=l.util.bytesToHex(r.value)}return t},f.certificationRequestFromAsn1=function(e,t){var r={},a=[];if(!p.validate(e,C,r,a)){var s=new Error("Cannot read PKCS#10 certificate request. ASN.1 object is not a PKCS#10 CertificationRequest.");throw s.errors=a,s}var o=p.derToOid(r.publicKeyOid);if(o!==f.oids.rsaEncryption)throw new Error("Cannot read public key. OID is not RSA.");var c=f.createCertificationRequest();if(c.version=r.csrVersion?r.csrVersion.charCodeAt(0):0,c.signatureOid=l.asn1.derToOid(r.csrSignatureOid),c.signatureParameters=E(c.signatureOid,r.csrSignatureParams,!0),c.siginfo.algorithmOid=l.asn1.derToOid(r.csrSignatureOid),c.siginfo.parameters=E(c.siginfo.algorithmOid,r.csrSignatureParams,!1),c.signature=r.csrSignature,c.certificationRequestInfo=r.certificationRequestInfo,t){if(c.md=null,c.signatureOid in h){var o=h[c.signatureOid];switch(o){case"sha1WithRSAEncryption":c.md=l.md.sha1.create();break;case"md5WithRSAEncryption":c.md=l.md.md5.create();break;case"sha256WithRSAEncryption":c.md=l.md.sha256.create();break;case"sha384WithRSAEncryption":c.md=l.md.sha384.create();break;case"sha512WithRSAEncryption":c.md=l.md.sha512.create();break;case"RSASSA-PSS":c.md=l.md.sha256.create()}}if(null===c.md){var s=new Error("Could not compute certification request digest. Unknown signature OID.");throw s.signatureOid=c.signatureOid,s}var u=p.toDer(c.certificationRequestInfo);c.md.update(u.getBytes())}var d=l.md.sha1.create();return c.subject.getField=function(e){return n(c.subject,e)},c.subject.addField=function(e){i([e]),c.subject.attributes.push(e)},c.subject.attributes=f.RDNAttributesAsArray(r.certificationRequestInfoSubject,d),c.subject.hash=d.digest().toHex(),c.publicKey=f.publicKeyFromAsn1(r.subjectPublicKeyInfo),c.getAttribute=function(e){return n(c,e)},c.addAttribute=function(e){i([e]),c.attributes.push(e)},c.attributes=f.CRIAttributesAsArray(r.certificationRequestInfoAttributes||[]),c},f.createCertificationRequest=function(){var e={};return e.version=0,e.signatureOid=null,e.signature=null,e.siginfo={},e.siginfo.algorithmOid=null,e.subject={},e.subject.getField=function(t){return n(e.subject,t)},e.subject.addField=function(t){i([t]),e.subject.attributes.push(t)},e.subject.attributes=[],e.subject.hash=null,e.publicKey=null,e.attributes=[],e.getAttribute=function(t){return n(e,t)},e.addAttribute=function(t){i([t]),e.attributes.push(t)},e.md=null,e.setSubject=function(t){i(t),e.subject.attributes=t,e.subject.hash=null},e.setAttributes=function(t){i(t),e.attributes=t},e.sign=function(t,r){e.md=r||l.md.sha1.create();var n=h[e.md.algorithm+"WithRSAEncryption"];if(!n){var a=new Error("Could not compute certification request digest. Unknown message digest algorithm OID.");throw a.algorithm=e.md.algorithm,a}e.signatureOid=e.siginfo.algorithmOid=n,e.certificationRequestInfo=f.getCertificationRequestInfo(e);var i=p.toDer(e.certificationRequestInfo);e.md.update(i.getBytes()),e.signature=t.sign(e.md)},e.verify=function(){var t=!1,r=e.md;if(null===r){if(e.signatureOid in h){switch(h[e.signatureOid]){case"sha1WithRSAEncryption":r=l.md.sha1.create();break;case"md5WithRSAEncryption":r=l.md.md5.create();break;case"sha256WithRSAEncryption":r=l.md.sha256.create();break;case"sha384WithRSAEncryption":r=l.md.sha384.create();break;case"sha512WithRSAEncryption":r=l.md.sha512.create();break;case"RSASSA-PSS":r=l.md.sha256.create()}}if(null===r){var n=new Error("Could not compute certification request digest. Unknown signature OID.");throw n.signatureOid=e.signatureOid,n}var a=e.certificationRequestInfo||f.getCertificationRequestInfo(e),i=p.toDer(a);r.update(i.getBytes())}if(null!==r){var s;switch(e.signatureOid){case h.sha1WithRSAEncryption:break;case h["RSASSA-PSS"]:var o,c;if(void 0===(o=h[e.signatureParameters.mgf.hash.algorithmOid])||void 0===l.md[o]){var n=new Error("Unsupported MGF hash function.");throw n.oid=e.signatureParameters.mgf.hash.algorithmOid,n.name=o,n}if(void 0===(c=h[e.signatureParameters.mgf.algorithmOid])||void 0===l.mgf[c]){var n=new Error("Unsupported MGF function.");throw n.oid=e.signatureParameters.mgf.algorithmOid,n.name=c,n}if(c=l.mgf[c].create(l.md[o].create()),void 0===(o=h[e.signatureParameters.hash.algorithmOid])||void 0===l.md[o]){var n=new Error("Unsupported RSASSA-PSS hash function.");throw n.oid=e.signatureParameters.hash.algorithmOid,n.name=o,n}s=l.pss.create(l.md[o].create(),c,e.signatureParameters.saltLength)}t=e.publicKey.verify(r.digest().getBytes(),e.signature,s)}return t},e};var S=new Date("1950-01-01T00:00:00Z"),T=new Date("2050-01-01T00:00:00Z");f.getTBSCertificate=function(e){var t=u(e.validity.notBefore),r=u(e.validity.notAfter),n=p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[p.create(p.Class.CONTEXT_SPECIFIC,0,!0,[p.create(p.Class.UNIVERSAL,p.Type.INTEGER,!1,p.integerToDer(e.version).getBytes())]),p.create(p.Class.UNIVERSAL,p.Type.INTEGER,!1,l.util.hexToBytes(e.serialNumber)),p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[p.create(p.Class.UNIVERSAL,p.Type.OID,!1,p.oidToDer(e.siginfo.algorithmOid).getBytes()),o(e.siginfo.algorithmOid,e.siginfo.parameters)]),a(e.issuer),p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[t,r]),a(e.subject),f.publicKeyToAsn1(e.publicKey)]);return e.issuer.uniqueId&&n.value.push(p.create(p.Class.CONTEXT_SPECIFIC,1,!0,[p.create(p.Class.UNIVERSAL,p.Type.BITSTRING,!1,String.fromCharCode(0)+e.issuer.uniqueId)])),e.subject.uniqueId&&n.value.push(p.create(p.Class.CONTEXT_SPECIFIC,2,!0,[p.create(p.Class.UNIVERSAL,p.Type.BITSTRING,!1,String.fromCharCode(0)+e.subject.uniqueId)])),e.extensions.length>0&&n.value.push(f.certificateExtensionsToAsn1(e.extensions)),n},f.getCertificationRequestInfo=function(e){return p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[p.create(p.Class.UNIVERSAL,p.Type.INTEGER,!1,p.integerToDer(e.version).getBytes()),a(e.subject),f.publicKeyToAsn1(e.publicKey),c(e)])},f.distinguishedNameToAsn1=function(e){return a(e)},f.certificateToAsn1=function(e){var t=e.tbsCertificate||f.getTBSCertificate(e);return p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[t,p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[p.create(p.Class.UNIVERSAL,p.Type.OID,!1,p.oidToDer(e.signatureOid).getBytes()),o(e.signatureOid,e.signatureParameters)]),p.create(p.Class.UNIVERSAL,p.Type.BITSTRING,!1,String.fromCharCode(0)+e.signature)])},f.certificateExtensionsToAsn1=function(e){var t=p.create(p.Class.CONTEXT_SPECIFIC,3,!0,[]),r=p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[]);t.value.push(r);for(var n=0;n<e.length;++n)r.value.push(f.certificateExtensionToAsn1(e[n]));return t},f.certificateExtensionToAsn1=function(e){var t=p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[]);t.value.push(p.create(p.Class.UNIVERSAL,p.Type.OID,!1,p.oidToDer(e.id).getBytes())),e.critical&&t.value.push(p.create(p.Class.UNIVERSAL,p.Type.BOOLEAN,!1,String.fromCharCode(255)));var r=e.value;return"string"!=typeof e.value&&(r=p.toDer(r).getBytes()),t.value.push(p.create(p.Class.UNIVERSAL,p.Type.OCTETSTRING,!1,r)),t},f.certificationRequestToAsn1=function(e){var t=e.certificationRequestInfo||f.getCertificationRequestInfo(e);return p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[t,p.create(p.Class.UNIVERSAL,p.Type.SEQUENCE,!0,[p.create(p.Class.UNIVERSAL,p.Type.OID,!1,p.oidToDer(e.signatureOid).getBytes()),o(e.signatureOid,e.signatureParameters)]),p.create(p.Class.UNIVERSAL,p.Type.BITSTRING,!1,String.fromCharCode(0)+e.signature)])},f.createCaStore=function(e){function t(e){return r(e),n.certs[e.hash]||null}function r(e){if(!e.hash){var t=l.md.sha1.create();e.attributes=f.RDNAttributesAsArray(a(e),t),e.hash=t.digest().toHex()}}var n={certs:{}};if(n.getIssuer=function(e){return t(e.issuer)},n.addCertificate=function(e){if("string"==typeof e&&(e=l.pki.certificateFromPem(e)),r(e.subject),!n.hasCertificate(e))if(e.subject.hash in n.certs){var t=n.certs[e.subject.hash];l.util.isArray(t)||(t=[t]),t.push(e),n.certs[e.subject.hash]=t}else n.certs[e.subject.hash]=e},n.hasCertificate=function(e){"string"==typeof e&&(e=l.pki.certificateFromPem(e));var r=t(e.subject);if(!r)return!1;l.util.isArray(r)||(r=[r]);for(var n=p.toDer(f.certificateToAsn1(e)).getBytes(),a=0;a<r.length;++a){if(n===p.toDer(f.certificateToAsn1(r[a])).getBytes())return!0}return!1},n.listAllCertificates=function(){var e=[];for(var t in n.certs)if(n.certs.hasOwnProperty(t)){var r=n.certs[t];if(l.util.isArray(r))for(var a=0;a<r.length;++a)e.push(r[a]);else e.push(r)}return e},n.removeCertificate=function(e){var a;if("string"==typeof e&&(e=l.pki.certificateFromPem(e)),r(e.subject),!n.hasCertificate(e))return null;var i=t(e.subject);if(!l.util.isArray(i))return a=n.certs[e.subject.hash],delete n.certs[e.subject.hash],a;for(var s=p.toDer(f.certificateToAsn1(e)).getBytes(),o=0;o<i.length;++o){s===p.toDer(f.certificateToAsn1(i[o])).getBytes()&&(a=i[o],i.splice(o,1))}return 0===i.length&&delete n.certs[e.subject.hash],a},e)for(var i=0;i<e.length;++i){var s=e[i];n.addCertificate(s)}return n},f.certificateError={bad_certificate:"forge.pki.BadCertificate",unsupported_certificate:"forge.pki.UnsupportedCertificate",certificate_revoked:"forge.pki.CertificateRevoked",certificate_expired:"forge.pki.CertificateExpired",certificate_unknown:"forge.pki.CertificateUnknown",unknown_ca:"forge.pki.UnknownCertificateAuthority"},f.verifyCertificateChain=function(e,t,r){"function"==typeof r&&(r={verify:r}),r=r||{},t=t.slice(0);var n=t.slice(0),a=r.validityCheckDate;void 0===a&&(a=new Date);var i=!0,s=null,o=0;do{var c=t.shift(),u=null,p=!1;if(a&&(a<c.validity.notBefore||a>c.validity.notAfter)&&(s={message:"Certificate is not valid yet or has expired.",error:f.certificateError.certificate_expired,notBefore:c.validity.notBefore,notAfter:c.validity.notAfter,now:a}),null===s){if(u=t[0]||e.getIssuer(c),null===u&&c.isIssuer(c)&&(p=!0,u=c),u){var h=u;l.util.isArray(h)||(h=[h]);for(var d=!1;!d&&h.length>0;){u=h.shift();try{d=u.verify(c)}catch(e){}}d||(s={message:"Certificate signature is invalid.",error:f.certificateError.bad_certificate})}null!==s||u&&!p||e.hasCertificate(c)||(s={message:"Certificate is not trusted.",error:f.certificateError.unknown_ca})}if(null===s&&u&&!c.isIssuer(u)&&(s={message:"Certificate issuer is invalid.",error:f.certificateError.bad_certificate}),null===s)for(var y={keyUsage:!0,basicConstraints:!0},g=0;null===s&&g<c.extensions.length;++g){var v=c.extensions[g];!v.critical||v.name in y||(s={message:"Certificate has an unsupported critical extension.",error:f.certificateError.unsupported_certificate})}if(null===s&&(!i||0===t.length&&(!u||p))){var m=c.getExtension("basicConstraints"),C=c.getExtension("keyUsage");if(null!==C&&(C.keyCertSign&&null!==m||(s={message:"Certificate keyUsage or basicConstraints conflict or indicate that the certificate is not a CA. If the certificate is the only one in the chain or isn't the first then the certificate must be a valid CA.",error:f.certificateError.bad_certificate})),null!==s||null===m||m.cA||(s={message:"Certificate basicConstraints indicates the certificate is not a CA.",error:f.certificateError.bad_certificate}),null===s&&null!==C&&"pathLenConstraint"in m){o-1>m.pathLenConstraint&&(s={message:"Certificate basicConstraints pathLenConstraint violated.",error:f.certificateError.bad_certificate})}}var E=null===s||s.error,S=r.verify?r.verify(E,o,n):E;if(!0!==S)throw!0===E&&(s={message:"The application rejected the certificate.",error:f.certificateError.bad_certificate}),(S||0===S)&&("object"!=typeof S||l.util.isArray(S)?"string"==typeof S&&(s.error=S):(S.message&&(s.message=S.message),S.error&&(s.error=S.error))),s;s=null,i=!1,++o}while(t.length>0);return!0}},function(e,t,r){var n=r(0);r(2),r(1),(e.exports=n.pss=n.pss||{}).create=function(e){3===arguments.length&&(e={md:arguments[0],mgf:arguments[1],saltLength:arguments[2]});var t=e.md,r=e.mgf,a=t.digestLength,i=e.salt||null;"string"==typeof i&&(i=n.util.createBuffer(i));var s;if("saltLength"in e)s=e.saltLength;else{if(null===i)throw new Error("Salt length not specified or specific salt not given.");s=i.length()}if(null!==i&&i.length()!==s)throw new Error("Given salt length does not match length of given salt.");var o=e.prng||n.random,c={};return c.encode=function(e,c){var u,l=c-1,p=Math.ceil(l/8),f=e.digest().getBytes();if(p<a+s+2)throw new Error("Message is too long to encrypt.");var h;h=null===i?o.getBytesSync(s):i.bytes();var d=new n.util.ByteBuffer;d.fillWithByte(0,8),d.putBytes(f),d.putBytes(h),t.start(),t.update(d.getBytes());var y=t.digest().getBytes(),g=new n.util.ByteBuffer;g.fillWithByte(0,p-s-a-2),g.putByte(1),g.putBytes(h);var v=g.getBytes(),m=p-a-1,C=r.generate(y,m),E="";for(u=0;u<m;u++)E+=String.fromCharCode(v.charCodeAt(u)^C.charCodeAt(u));var S=65280>>8*p-l&255;return(E=String.fromCharCode(E.charCodeAt(0)&~S)+E.substr(1))+y+String.fromCharCode(188)},c.verify=function(e,i,o){var c,u=o-1,l=Math.ceil(u/8);if(i=i.substr(-l),l<a+s+2)throw new Error("Inconsistent parameters to PSS signature verification.");if(188!==i.charCodeAt(l-1))throw new Error("Encoded message does not end in 0xBC.");var p=l-a-1,f=i.substr(0,p),h=i.substr(p,a),d=65280>>8*l-u&255;if(0!=(f.charCodeAt(0)&d))throw new Error("Bits beyond keysize not zero as expected.");var y=r.generate(h,p),g="";for(c=0;c<p;c++)g+=String.fromCharCode(f.charCodeAt(c)^y.charCodeAt(c));g=String.fromCharCode(g.charCodeAt(0)&~d)+g.substr(1);var v=l-a-s-2;for(c=0;c<v;c++)if(0!==g.charCodeAt(c))throw new Error("Leftmost octets not zero as expected");if(1!==g.charCodeAt(v))throw new Error("Inconsistent PSS signature, 0x01 marker not found");var m=g.substr(-s),C=new n.util.ByteBuffer;return C.fillWithByte(0,8),C.putBytes(e),C.putBytes(m),t.start(),t.update(C.getBytes()),h===t.digest().getBytes()},c}},function(e,t,r){var n=r(0);e.exports=n.debug=n.debug||{},n.debug.storage={},n.debug.get=function(e,t){var r;return void 0===e?r=n.debug.storage:e in n.debug.storage&&(r=void 0===t?n.debug.storage[e]:n.debug.storage[e][t]),r},n.debug.set=function(e,t,r){e in n.debug.storage||(n.debug.storage[e]={}),n.debug.storage[e][t]=r},n.debug.clear=function(e,t){void 0===e?n.debug.storage={}:e in n.debug.storage&&(void 0===t?delete n.debug.storage[e]:delete n.debug.storage[e][t])}},function(e,t,r){function n(e){if("string"==typeof e&&(e=s.util.createBuffer(e)),s.util.isArray(e)&&e.length>4){var t=e;e=s.util.createBuffer();for(var r=0;r<t.length;++r)e.putByte(t[r])}return s.util.isArray(e)||(e=[e.getInt32(),e.getInt32(),e.getInt32(),e.getInt32()]),e}function a(e){e[e.length-1]=e[e.length-1]+1&4294967295}function i(e){return[e/4294967296|0,4294967295&e]}var s=r(0);r(1),s.cipher=s.cipher||{};var o=e.exports=s.cipher.modes=s.cipher.modes||{};o.ecb=function(e){e=e||{},this.name="ECB",this.cipher=e.cipher,this.blockSize=e.blockSize||16,this._ints=this.blockSize/4,this._inBlock=new Array(this._ints),this._outBlock=new Array(this._ints)},o.ecb.prototype.start=function(e){},o.ecb.prototype.encrypt=function(e,t,r){if(e.length()<this.blockSize&&!(r&&e.length()>0))return!0;for(var n=0;n<this._ints;++n)this._inBlock[n]=e.getInt32();this.cipher.encrypt(this._inBlock,this._outBlock);for(var n=0;n<this._ints;++n)t.putInt32(this._outBlock[n])},o.ecb.prototype.decrypt=function(e,t,r){if(e.length()<this.blockSize&&!(r&&e.length()>0))return!0;for(var n=0;n<this._ints;++n)this._inBlock[n]=e.getInt32();this.cipher.decrypt(this._inBlock,this._outBlock);for(var n=0;n<this._ints;++n)t.putInt32(this._outBlock[n])},o.ecb.prototype.pad=function(e,t){var r=e.length()===this.blockSize?this.blockSize:this.blockSize-e.length();return e.fillWithByte(r,r),!0},o.ecb.prototype.unpad=function(e,t){if(t.overflow>0)return!1;var r=e.length(),n=e.at(r-1);return!(n>this.blockSize<<2)&&(e.truncate(n),!0)},o.cbc=function(e){e=e||{},this.name="CBC",this.cipher=e.cipher,this.blockSize=e.blockSize||16,this._ints=this.blockSize/4,this._inBlock=new Array(this._ints),this._outBlock=new Array(this._ints)},o.cbc.prototype.start=function(e){if(null===e.iv){if(!this._prev)throw new Error("Invalid IV parameter.");this._iv=this._prev.slice(0)}else{if(!("iv"in e))throw new Error("Invalid IV parameter.");this._iv=n(e.iv),this._prev=this._iv.slice(0)}},o.cbc.prototype.encrypt=function(e,t,r){if(e.length()<this.blockSize&&!(r&&e.length()>0))return!0;for(var n=0;n<this._ints;++n)this._inBlock[n]=this._prev[n]^e.getInt32();this.cipher.encrypt(this._inBlock,this._outBlock);for(var n=0;n<this._ints;++n)t.putInt32(this._outBlock[n]);this._prev=this._outBlock},o.cbc.prototype.decrypt=function(e,t,r){if(e.length()<this.blockSize&&!(r&&e.length()>0))return!0;for(var n=0;n<this._ints;++n)this._inBlock[n]=e.getInt32();this.cipher.decrypt(this._inBlock,this._outBlock);for(var n=0;n<this._ints;++n)t.putInt32(this._prev[n]^this._outBlock[n]);this._prev=this._inBlock.slice(0)},o.cbc.prototype.pad=function(e,t){var r=e.length()===this.blockSize?this.blockSize:this.blockSize-e.length();return e.fillWithByte(r,r),!0},o.cbc.prototype.unpad=function(e,t){if(t.overflow>0)return!1;var r=e.length(),n=e.at(r-1);return!(n>this.blockSize<<2)&&(e.truncate(n),!0)},o.cfb=function(e){e=e||{},this.name="CFB",this.cipher=e.cipher,this.blockSize=e.blockSize||16,this._ints=this.blockSize/4,this._inBlock=null,this._outBlock=new Array(this._ints),this._partialBlock=new Array(this._ints),this._partialOutput=s.util.createBuffer(),this._partialBytes=0},o.cfb.prototype.start=function(e){if(!("iv"in e))throw new Error("Invalid IV parameter.");this._iv=n(e.iv),this._inBlock=this._iv.slice(0),this._partialBytes=0},o.cfb.prototype.encrypt=function(e,t,r){var n=e.length();if(0===n)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),0===this._partialBytes&&n>=this.blockSize)for(var a=0;a<this._ints;++a)this._inBlock[a]=e.getInt32()^this._outBlock[a],t.putInt32(this._inBlock[a]);else{var i=(this.blockSize-n)%this.blockSize;i>0&&(i=this.blockSize-i),this._partialOutput.clear();for(var a=0;a<this._ints;++a)this._partialBlock[a]=e.getInt32()^this._outBlock[a],this._partialOutput.putInt32(this._partialBlock[a]);if(i>0)e.read-=this.blockSize;else for(var a=0;a<this._ints;++a)this._inBlock[a]=this._partialBlock[a];if(this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),i>0&&!r)return t.putBytes(this._partialOutput.getBytes(i-this._partialBytes)),this._partialBytes=i,!0;t.putBytes(this._partialOutput.getBytes(n-this._partialBytes)),this._partialBytes=0}},o.cfb.prototype.decrypt=function(e,t,r){var n=e.length();if(0===n)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),0===this._partialBytes&&n>=this.blockSize)for(var a=0;a<this._ints;++a)this._inBlock[a]=e.getInt32(),t.putInt32(this._inBlock[a]^this._outBlock[a]);else{var i=(this.blockSize-n)%this.blockSize;i>0&&(i=this.blockSize-i),this._partialOutput.clear();for(var a=0;a<this._ints;++a)this._partialBlock[a]=e.getInt32(),this._partialOutput.putInt32(this._partialBlock[a]^this._outBlock[a]);if(i>0)e.read-=this.blockSize;else for(var a=0;a<this._ints;++a)this._inBlock[a]=this._partialBlock[a];if(this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),i>0&&!r)return t.putBytes(this._partialOutput.getBytes(i-this._partialBytes)),this._partialBytes=i,!0;t.putBytes(this._partialOutput.getBytes(n-this._partialBytes)),this._partialBytes=0}},o.ofb=function(e){e=e||{},this.name="OFB",this.cipher=e.cipher,this.blockSize=e.blockSize||16,this._ints=this.blockSize/4,this._inBlock=null,this._outBlock=new Array(this._ints),this._partialOutput=s.util.createBuffer(),this._partialBytes=0},o.ofb.prototype.start=function(e){if(!("iv"in e))throw new Error("Invalid IV parameter.");this._iv=n(e.iv),this._inBlock=this._iv.slice(0),this._partialBytes=0},o.ofb.prototype.encrypt=function(e,t,r){var n=e.length();if(0===e.length())return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),0===this._partialBytes&&n>=this.blockSize)for(var a=0;a<this._ints;++a)t.putInt32(e.getInt32()^this._outBlock[a]),this._inBlock[a]=this._outBlock[a];else{var i=(this.blockSize-n)%this.blockSize;i>0&&(i=this.blockSize-i),this._partialOutput.clear();for(var a=0;a<this._ints;++a)this._partialOutput.putInt32(e.getInt32()^this._outBlock[a]);if(i>0)e.read-=this.blockSize;else for(var a=0;a<this._ints;++a)this._inBlock[a]=this._outBlock[a];if(this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),i>0&&!r)return t.putBytes(this._partialOutput.getBytes(i-this._partialBytes)),this._partialBytes=i,!0;t.putBytes(this._partialOutput.getBytes(n-this._partialBytes)),this._partialBytes=0}},o.ofb.prototype.decrypt=o.ofb.prototype.encrypt,o.ctr=function(e){e=e||{},this.name="CTR",this.cipher=e.cipher,this.blockSize=e.blockSize||16,this._ints=this.blockSize/4,this._inBlock=null,this._outBlock=new Array(this._ints),this._partialOutput=s.util.createBuffer(),this._partialBytes=0},o.ctr.prototype.start=function(e){if(!("iv"in e))throw new Error("Invalid IV parameter.");this._iv=n(e.iv),this._inBlock=this._iv.slice(0),this._partialBytes=0},o.ctr.prototype.encrypt=function(e,t,r){var n=e.length();if(0===n)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),0===this._partialBytes&&n>=this.blockSize)for(var i=0;i<this._ints;++i)t.putInt32(e.getInt32()^this._outBlock[i]);else{var s=(this.blockSize-n)%this.blockSize;s>0&&(s=this.blockSize-s),this._partialOutput.clear();for(var i=0;i<this._ints;++i)this._partialOutput.putInt32(e.getInt32()^this._outBlock[i]);if(s>0&&(e.read-=this.blockSize),this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),s>0&&!r)return t.putBytes(this._partialOutput.getBytes(s-this._partialBytes)),this._partialBytes=s,!0;t.putBytes(this._partialOutput.getBytes(n-this._partialBytes)),this._partialBytes=0}a(this._inBlock)},o.ctr.prototype.decrypt=o.ctr.prototype.encrypt,o.gcm=function(e){e=e||{},this.name="GCM",this.cipher=e.cipher,this.blockSize=e.blockSize||16,this._ints=this.blockSize/4,this._inBlock=new Array(this._ints),this._outBlock=new Array(this._ints),this._partialOutput=s.util.createBuffer(),this._partialBytes=0,this._R=3774873600},o.gcm.prototype.start=function(e){if(!("iv"in e))throw new Error("Invalid IV parameter.");var t=s.util.createBuffer(e.iv);this._cipherLength=0;var r;if(r="additionalData"in e?s.util.createBuffer(e.additionalData):s.util.createBuffer(),this._tagLength="tagLength"in e?e.tagLength:128,this._tag=null,e.decrypt&&(this._tag=s.util.createBuffer(e.tag).getBytes(),this._tag.length!==this._tagLength/8))throw new Error("Authentication tag does not match tag length.");this._hashBlock=new Array(this._ints),this.tag=null,this._hashSubkey=new Array(this._ints),this.cipher.encrypt([0,0,0,0],this._hashSubkey),this.componentBits=4,this._m=this.generateHashTable(this._hashSubkey,this.componentBits);var n=t.length();if(12===n)this._j0=[t.getInt32(),t.getInt32(),t.getInt32(),1];else{for(this._j0=[0,0,0,0];t.length()>0;)this._j0=this.ghash(this._hashSubkey,this._j0,[t.getInt32(),t.getInt32(),t.getInt32(),t.getInt32()]);this._j0=this.ghash(this._hashSubkey,this._j0,[0,0].concat(i(8*n)))}this._inBlock=this._j0.slice(0),a(this._inBlock),this._partialBytes=0,r=s.util.createBuffer(r),this._aDataLength=i(8*r.length());var o=r.length()%this.blockSize;for(o&&r.fillWithByte(0,this.blockSize-o),this._s=[0,0,0,0];r.length()>0;)this._s=this.ghash(this._hashSubkey,this._s,[r.getInt32(),r.getInt32(),r.getInt32(),r.getInt32()])},o.gcm.prototype.encrypt=function(e,t,r){var n=e.length();if(0===n)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),0===this._partialBytes&&n>=this.blockSize){for(var i=0;i<this._ints;++i)t.putInt32(this._outBlock[i]^=e.getInt32());this._cipherLength+=this.blockSize}else{var s=(this.blockSize-n)%this.blockSize;s>0&&(s=this.blockSize-s),this._partialOutput.clear();for(var i=0;i<this._ints;++i)this._partialOutput.putInt32(e.getInt32()^this._outBlock[i]);if(s<=0||r){if(r){var o=n%this.blockSize;this._cipherLength+=o,this._partialOutput.truncate(this.blockSize-o)}else this._cipherLength+=this.blockSize;for(var i=0;i<this._ints;++i)this._outBlock[i]=this._partialOutput.getInt32();this._partialOutput.read-=this.blockSize}if(this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),s>0&&!r)return e.read-=this.blockSize,t.putBytes(this._partialOutput.getBytes(s-this._partialBytes)),this._partialBytes=s,!0;t.putBytes(this._partialOutput.getBytes(n-this._partialBytes)),this._partialBytes=0}this._s=this.ghash(this._hashSubkey,this._s,this._outBlock),a(this._inBlock)},o.gcm.prototype.decrypt=function(e,t,r){var n=e.length();if(n<this.blockSize&&!(r&&n>0))return!0;this.cipher.encrypt(this._inBlock,this._outBlock),a(this._inBlock),this._hashBlock[0]=e.getInt32(),this._hashBlock[1]=e.getInt32(),this._hashBlock[2]=e.getInt32(),this._hashBlock[3]=e.getInt32(),this._s=this.ghash(this._hashSubkey,this._s,this._hashBlock);for(var i=0;i<this._ints;++i)t.putInt32(this._outBlock[i]^this._hashBlock[i]);n<this.blockSize?this._cipherLength+=n%this.blockSize:this._cipherLength+=this.blockSize},o.gcm.prototype.afterFinish=function(e,t){var r=!0;t.decrypt&&t.overflow&&e.truncate(this.blockSize-t.overflow),this.tag=s.util.createBuffer();var n=this._aDataLength.concat(i(8*this._cipherLength));this._s=this.ghash(this._hashSubkey,this._s,n);var a=[];this.cipher.encrypt(this._j0,a);for(var o=0;o<this._ints;++o)this.tag.putInt32(this._s[o]^a[o]);return this.tag.truncate(this.tag.length()%(this._tagLength/8)),t.decrypt&&this.tag.bytes()!==this._tag&&(r=!1),r},o.gcm.prototype.multiply=function(e,t){for(var r=[0,0,0,0],n=t.slice(0),a=0;a<128;++a){e[a/32|0]&1<<31-a%32&&(r[0]^=n[0],r[1]^=n[1],r[2]^=n[2],r[3]^=n[3]),this.pow(n,n)}return r},o.gcm.prototype.pow=function(e,t){for(var r=1&e[3],n=3;n>0;--n)t[n]=e[n]>>>1|(1&e[n-1])<<31;t[0]=e[0]>>>1,r&&(t[0]^=this._R)},o.gcm.prototype.tableMultiply=function(e){for(var t=[0,0,0,0],r=0;r<32;++r){var n=r/8|0,a=e[n]>>>4*(7-r%8)&15,i=this._m[r][a];t[0]^=i[0],t[1]^=i[1],t[2]^=i[2],t[3]^=i[3]}return t},o.gcm.prototype.ghash=function(e,t,r){return t[0]^=r[0],t[1]^=r[1],t[2]^=r[2],t[3]^=r[3],this.tableMultiply(t)},o.gcm.prototype.generateHashTable=function(e,t){for(var r=8/t,n=4*r,a=16*r,i=new Array(a),s=0;s<a;++s){var o=[0,0,0,0],c=s/n|0,u=(n-1-s%n)*t;o[c]=1<<t-1<<u,i[s]=this.generateSubHashTable(this.multiply(o,e),t)}return i},o.gcm.prototype.generateSubHashTable=function(e,t){var r=1<<t,n=r>>>1,a=new Array(r);a[n]=e.slice(0);for(var i=n>>>1;i>0;)this.pow(a[2*i],a[i]=[]),i>>=1;for(i=2;i<n;){for(var s=1;s<i;++s){var o=a[i],c=a[s];a[i+s]=[o[0]^c[0],o[1]^c[1],o[2]^c[2],o[3]^c[3]]}i*=2}for(a[0]=[0,0,0,0],i=n+1;i<r;++i){var u=a[i^n];a[i]=[e[0]^u[0],e[1]^u[1],e[2]^u[2],e[3]^u[3]]}return a}},function(e,t,r){var n=r(0);r(3),r(6),r(23),r(7),r(16),r(29),r(19),r(12),r(1),r(18);var a=n.asn1,i=e.exports=n.pki=n.pki||{};i.pemToDer=function(e){var t=n.pem.decode(e)[0];if(t.procType&&"ENCRYPTED"===t.procType.type)throw new Error("Could not convert PEM to DER; PEM is encrypted.");return n.util.createBuffer(t.body)},i.privateKeyFromPem=function(e){var t=n.pem.decode(e)[0];if("PRIVATE KEY"!==t.type&&"RSA PRIVATE KEY"!==t.type){var r=new Error('Could not convert private key from PEM; PEM header type is not "PRIVATE KEY" or "RSA PRIVATE KEY".');throw r.headerType=t.type,r}if(t.procType&&"ENCRYPTED"===t.procType.type)throw new Error("Could not convert private key from PEM; PEM is encrypted.");var s=a.fromDer(t.body);return i.privateKeyFromAsn1(s)},i.privateKeyToPem=function(e,t){var r={type:"RSA PRIVATE KEY",body:a.toDer(i.privateKeyToAsn1(e)).getBytes()};return n.pem.encode(r,{maxline:t})},i.privateKeyInfoToPem=function(e,t){var r={type:"PRIVATE KEY",body:a.toDer(e).getBytes()};return n.pem.encode(r,{maxline:t})}},function(e,t,r){function n(e,t){return e.start().update(t).digest().getBytes()}function a(e){var t;if(e){if(!(t=l.oids[u.derToOid(e)])){var r=new Error("Unsupported PRF OID.");throw r.oid=e,r.supported=["hmacWithSHA1","hmacWithSHA224","hmacWithSHA256","hmacWithSHA384","hmacWithSHA512"],r}}else t="hmacWithSHA1";return i(t)}function i(e){var t=o.md;switch(e){case"hmacWithSHA224":t=o.md.sha512;case"hmacWithSHA1":case"hmacWithSHA256":case"hmacWithSHA384":case"hmacWithSHA512":e=e.substr(8).toLowerCase();break;default:var r=new Error("Unsupported PRF algorithm.");throw r.algorithm=e,r.supported=["hmacWithSHA1","hmacWithSHA224","hmacWithSHA256","hmacWithSHA384","hmacWithSHA512"],r}if(!(t&&e in t))throw new Error("Unknown hash algorithm: "+e);return t[e].create()}function s(e,t,r,n){var a=u.create(u.Class.UNIVERSAL,u.Type.SEQUENCE,!0,[u.create(u.Class.UNIVERSAL,u.Type.OCTETSTRING,!1,e),u.create(u.Class.UNIVERSAL,u.Type.INTEGER,!1,t.getBytes())]);return"hmacWithSHA1"!==n&&a.value.push(u.create(u.Class.UNIVERSAL,u.Type.INTEGER,!1,o.util.hexToBytes(r.toString(16))),u.create(u.Class.UNIVERSAL,u.Type.SEQUENCE,!0,[u.create(u.Class.UNIVERSAL,u.Type.OID,!1,u.oidToDer(l.oids[n]).getBytes()),u.create(u.Class.UNIVERSAL,u.Type.NULL,!1,"")])),a}var o=r(0);if(r(5),r(3),r(11),r(4),r(6),r(16),r(7),r(2),r(26),r(12),r(1),void 0===c)var c=o.jsbn.BigInteger;var u=o.asn1,l=o.pki=o.pki||{};e.exports=l.pbe=o.pbe=o.pbe||{};var p=l.oids,f={name:"EncryptedPrivateKeyInfo",tagClass:u.Class.UNIVERSAL,type:u.Type.SEQUENCE,constructed:!0,value:[{name:"EncryptedPrivateKeyInfo.encryptionAlgorithm",tagClass:u.Class.UNIVERSAL,type:u.Type.SEQUENCE,constructed:!0,value:[{name:"AlgorithmIdentifier.algorithm",tagClass:u.Class.UNIVERSAL,type:u.Type.OID,constructed:!1,capture:"encryptionOid"},{name:"AlgorithmIdentifier.parameters",tagClass:u.Class.UNIVERSAL,type:u.Type.SEQUENCE,constructed:!0,captureAsn1:"encryptionParams"}]},{name:"EncryptedPrivateKeyInfo.encryptedData",tagClass:u.Class.UNIVERSAL,type:u.Type.OCTETSTRING,constructed:!1,capture:"encryptedData"}]},h={name:"PBES2Algorithms",tagClass:u.Class.UNIVERSAL,type:u.Type.SEQUENCE,constructed:!0,value:[{name:"PBES2Algorithms.keyDerivationFunc",tagClass:u.Class.UNIVERSAL,type:u.Type.SEQUENCE,constructed:!0,value:[{name:"PBES2Algorithms.keyDerivationFunc.oid",tagClass:u.Class.UNIVERSAL,type:u.Type.OID,constructed:!1,capture:"kdfOid"},{name:"PBES2Algorithms.params",tagClass:u.Class.UNIVERSAL,type:u.Type.SEQUENCE,constructed:!0,value:[{name:"PBES2Algorithms.params.salt",tagClass:u.Class.UNIVERSAL,type:u.Type.OCTETSTRING,constructed:!1,capture:"kdfSalt"},{name:"PBES2Algorithms.params.iterationCount",tagClass:u.Class.UNIVERSAL,type:u.Type.INTEGER,constructed:!1,capture:"kdfIterationCount"},{name:"PBES2Algorithms.params.keyLength",tagClass:u.Class.UNIVERSAL,type:u.Type.INTEGER,constructed:!1,optional:!0,capture:"keyLength"},{name:"PBES2Algorithms.params.prf",tagClass:u.Class.UNIVERSAL,type:u.Type.SEQUENCE,constructed:!0,optional:!0,value:[{name:"PBES2Algorithms.params.prf.algorithm",tagClass:u.Class.UNIVERSAL,type:u.Type.OID,constructed:!1,capture:"prfOid"}]}]}]},{name:"PBES2Algorithms.encryptionScheme",tagClass:u.Class.UNIVERSAL,type:u.Type.SEQUENCE,constructed:!0,value:[{name:"PBES2Algorithms.encryptionScheme.oid",tagClass:u.Class.UNIVERSAL,type:u.Type.OID,constructed:!1,capture:"encOid"},{name:"PBES2Algorithms.encryptionScheme.iv",tagClass:u.Class.UNIVERSAL,type:u.Type.OCTETSTRING,constructed:!1,capture:"encIv"}]}]},d={name:"pkcs-12PbeParams",tagClass:u.Class.UNIVERSAL,type:u.Type.SEQUENCE,constructed:!0,value:[{name:"pkcs-12PbeParams.salt",tagClass:u.Class.UNIVERSAL,type:u.Type.OCTETSTRING,constructed:!1,capture:"salt"},{name:"pkcs-12PbeParams.iterations",tagClass:u.Class.UNIVERSAL,type:u.Type.INTEGER,constructed:!1,capture:"iterations"}]};l.encryptPrivateKeyInfo=function(e,t,r){r=r||{},r.saltSize=r.saltSize||8,r.count=r.count||2048,r.algorithm=r.algorithm||"aes128",r.prfAlgorithm=r.prfAlgorithm||"sha1";var n,a,c,f=o.random.getBytesSync(r.saltSize),h=r.count,d=u.integerToDer(h);if(0===r.algorithm.indexOf("aes")||"des"===r.algorithm){var y,g,v;switch(r.algorithm){case"aes128":n=16,y=16,g=p["aes128-CBC"],v=o.aes.createEncryptionCipher;break;case"aes192":n=24,y=16,g=p["aes192-CBC"],v=o.aes.createEncryptionCipher;break;case"aes256":n=32,y=16,g=p["aes256-CBC"],v=o.aes.createEncryptionCipher;break;case"des":n=8,y=8,g=p.desCBC,v=o.des.createEncryptionCipher;break;default:var m=new Error("Cannot encrypt private key. Unknown encryption algorithm.");throw m.algorithm=r.algorithm,m}var C="hmacWith"+r.prfAlgorithm.toUpperCase(),E=i(C),S=o.pkcs5.pbkdf2(t,f,h,n,E),T=o.random.getBytesSync(y),b=v(S);b.start(T),b.update(u.toDer(e)),b.finish(),c=b.output.getBytes();var I=s(f,d,n,C);a=u.create(u.Class.UNIVERSAL,u.Type.SEQUENCE,!0,[u.create(u.Class.UNIVERSAL,u.Type.OID,!1,u.oidToDer(p.pkcs5PBES2).getBytes()),u.create(u.Class.UNIVERSAL,u.Type.SEQUENCE,!0,[u.create(u.Class.UNIVERSAL,u.Type.SEQUENCE,!0,[u.create(u.Class.UNIVERSAL,u.Type.OID,!1,u.oidToDer(p.pkcs5PBKDF2).getBytes()),I]),u.create(u.Class.UNIVERSAL,u.Type.SEQUENCE,!0,[u.create(u.Class.UNIVERSAL,u.Type.OID,!1,u.oidToDer(g).getBytes()),u.create(u.Class.UNIVERSAL,u.Type.OCTETSTRING,!1,T)])])])}else{if("3des"!==r.algorithm){var m=new Error("Cannot encrypt private key. Unknown encryption algorithm.");throw m.algorithm=r.algorithm,m}n=24;var A=new o.util.ByteBuffer(f),S=l.pbe.generatePkcs12Key(t,A,1,h,n),T=l.pbe.generatePkcs12Key(t,A,2,h,n),b=o.des.createEncryptionCipher(S);b.start(T),b.update(u.toDer(e)),b.finish(),c=b.output.getBytes(),a=u.create(u.Class.UNIVERSAL,u.Type.SEQUENCE,!0,[u.create(u.Class.UNIVERSAL,u.Type.OID,!1,u.oidToDer(p["pbeWithSHAAnd3-KeyTripleDES-CBC"]).getBytes()),u.create(u.Class.UNIVERSAL,u.Type.SEQUENCE,!0,[u.create(u.Class.UNIVERSAL,u.Type.OCTETSTRING,!1,f),u.create(u.Class.UNIVERSAL,u.Type.INTEGER,!1,d.getBytes())])])}return u.create(u.Class.UNIVERSAL,u.Type.SEQUENCE,!0,[a,u.create(u.Class.UNIVERSAL,u.Type.OCTETSTRING,!1,c)])},l.decryptPrivateKeyInfo=function(e,t){var r=null,n={},a=[];if(!u.validate(e,f,n,a)){var i=new Error("Cannot read encrypted private key. ASN.1 object is not a supported EncryptedPrivateKeyInfo.");throw i.errors=a,i}var s=u.derToOid(n.encryptionOid),c=l.pbe.getCipher(s,n.encryptionParams,t),p=o.util.createBuffer(n.encryptedData);return c.update(p),c.finish()&&(r=u.fromDer(c.output)),r},l.encryptedPrivateKeyToPem=function(e,t){var r={type:"ENCRYPTED PRIVATE KEY",body:u.toDer(e).getBytes()};return o.pem.encode(r,{maxline:t})},l.encryptedPrivateKeyFromPem=function(e){var t=o.pem.decode(e)[0];if("ENCRYPTED PRIVATE KEY"!==t.type){var r=new Error('Could not convert encrypted private key from PEM; PEM header type is "ENCRYPTED PRIVATE KEY".');throw r.headerType=t.type,r}if(t.procType&&"ENCRYPTED"===t.procType.type)throw new Error("Could not convert encrypted private key from PEM; PEM is encrypted.");return u.fromDer(t.body)},l.encryptRsaPrivateKey=function(e,t,r){if(r=r||{},!r.legacy){var n=l.wrapRsaPrivateKey(l.privateKeyToAsn1(e));return n=l.encryptPrivateKeyInfo(n,t,r),l.encryptedPrivateKeyToPem(n)}var a,i,s,c;switch(r.algorithm){case"aes128":a="AES-128-CBC",s=16,i=o.random.getBytesSync(16),c=o.aes.createEncryptionCipher;break;case"aes192":a="AES-192-CBC",s=24,i=o.random.getBytesSync(16),c=o.aes.createEncryptionCipher;break;case"aes256":a="AES-256-CBC",s=32,i=o.random.getBytesSync(16),c=o.aes.createEncryptionCipher;break;case"3des":a="DES-EDE3-CBC",s=24,i=o.random.getBytesSync(8),c=o.des.createEncryptionCipher;break;case"des":a="DES-CBC",s=8,i=o.random.getBytesSync(8),c=o.des.createEncryptionCipher;break;default:var p=new Error('Could not encrypt RSA private key; unsupported encryption algorithm "'+r.algorithm+'".');throw p.algorithm=r.algorithm,p}var f=o.pbe.opensslDeriveBytes(t,i.substr(0,8),s),h=c(f);h.start(i),h.update(u.toDer(l.privateKeyToAsn1(e))),h.finish();var d={type:"RSA PRIVATE KEY",procType:{version:"4",type:"ENCRYPTED"},dekInfo:{algorithm:a,parameters:o.util.bytesToHex(i).toUpperCase()},body:h.output.getBytes()};return o.pem.encode(d)},l.decryptRsaPrivateKey=function(e,t){var r=null,n=o.pem.decode(e)[0];if("ENCRYPTED PRIVATE KEY"!==n.type&&"PRIVATE KEY"!==n.type&&"RSA PRIVATE KEY"!==n.type){var a=new Error('Could not convert private key from PEM; PEM header type is not "ENCRYPTED PRIVATE KEY", "PRIVATE KEY", or "RSA PRIVATE KEY".');throw a.headerType=a,a}if(n.procType&&"ENCRYPTED"===n.procType.type){var i,s;switch(n.dekInfo.algorithm){case"DES-CBC":i=8,s=o.des.createDecryptionCipher;break;case"DES-EDE3-CBC":i=24,s=o.des.createDecryptionCipher;break;case"AES-128-CBC":i=16,s=o.aes.createDecryptionCipher;break;case"AES-192-CBC":i=24,s=o.aes.createDecryptionCipher;break;case"AES-256-CBC":i=32,s=o.aes.createDecryptionCipher;break;case"RC2-40-CBC":i=5,s=function(e){return o.rc2.createDecryptionCipher(e,40)};break;case"RC2-64-CBC":i=8,s=function(e){return o.rc2.createDecryptionCipher(e,64)};break;case"RC2-128-CBC":i=16,s=function(e){return o.rc2.createDecryptionCipher(e,128)};break;default:var a=new Error('Could not decrypt private key; unsupported encryption algorithm "'+n.dekInfo.algorithm+'".');throw a.algorithm=n.dekInfo.algorithm,a}var c=o.util.hexToBytes(n.dekInfo.parameters),p=o.pbe.opensslDeriveBytes(t,c.substr(0,8),i),f=s(p);if(f.start(c),f.update(o.util.createBuffer(n.body)),!f.finish())return r;r=f.output.getBytes()}else r=n.body;return r="ENCRYPTED PRIVATE KEY"===n.type?l.decryptPrivateKeyInfo(u.fromDer(r),t):u.fromDer(r),null!==r&&(r=l.privateKeyFromAsn1(r)),r},l.pbe.generatePkcs12Key=function(e,t,r,n,a,i){var s,c;if(void 0===i||null===i){if(!("sha1"in o.md))throw new Error('"sha1" hash algorithm unavailable.');i=o.md.sha1.create()}var u=i.digestLength,l=i.blockLength,p=new o.util.ByteBuffer,f=new o.util.ByteBuffer;if(null!==e&&void 0!==e){for(c=0;c<e.length;c++)f.putInt16(e.charCodeAt(c));f.putInt16(0)}var h=f.length(),d=t.length(),y=new o.util.ByteBuffer;y.fillWithByte(r,l);var g=l*Math.ceil(d/l),v=new o.util.ByteBuffer;for(c=0;c<g;c++)v.putByte(t.at(c%d));var m=l*Math.ceil(h/l),C=new o.util.ByteBuffer;for(c=0;c<m;c++)C.putByte(f.at(c%h));var E=v;E.putBuffer(C);for(var S=Math.ceil(a/u),T=1;T<=S;T++){var b=new o.util.ByteBuffer;b.putBytes(y.bytes()),b.putBytes(E.bytes());for(var I=0;I<n;I++)i.start(),i.update(b.getBytes()),b=i.digest();var A=new o.util.ByteBuffer;for(c=0;c<l;c++)A.putByte(b.at(c%u));var B=Math.ceil(d/l)+Math.ceil(h/l),k=new o.util.ByteBuffer;for(s=0;s<B;s++){var N=new o.util.ByteBuffer(E.getBytes(l)),w=511;for(c=A.length()-1;c>=0;c--)w>>=8,w+=A.at(c)+N.at(c),N.setAt(c,255&w);k.putBuffer(N)}E=k,p.putBuffer(b)}return p.truncate(p.length()-a),p},l.pbe.getCipher=function(e,t,r){switch(e){case l.oids.pkcs5PBES2:return l.pbe.getCipherForPBES2(e,t,r);case l.oids["pbeWithSHAAnd3-KeyTripleDES-CBC"]:case l.oids["pbewithSHAAnd40BitRC2-CBC"]:return l.pbe.getCipherForPKCS12PBE(e,t,r);default:var n=new Error("Cannot read encrypted PBE data block. Unsupported OID.");throw n.oid=e,n.supportedOids=["pkcs5PBES2","pbeWithSHAAnd3-KeyTripleDES-CBC","pbewithSHAAnd40BitRC2-CBC"],n}},l.pbe.getCipherForPBES2=function(e,t,r){var n={},i=[];if(!u.validate(t,h,n,i)){var s=new Error("Cannot read password-based-encryption algorithm parameters. ASN.1 object is not a supported EncryptedPrivateKeyInfo.");throw s.errors=i,s}if((e=u.derToOid(n.kdfOid))!==l.oids.pkcs5PBKDF2){var s=new Error("Cannot read encrypted private key. Unsupported key derivation function OID.");throw s.oid=e,s.supportedOids=["pkcs5PBKDF2"],s}if((e=u.derToOid(n.encOid))!==l.oids["aes128-CBC"]&&e!==l.oids["aes192-CBC"]&&e!==l.oids["aes256-CBC"]&&e!==l.oids["des-EDE3-CBC"]&&e!==l.oids.desCBC){var s=new Error("Cannot read encrypted private key. Unsupported encryption scheme OID.");throw s.oid=e,s.supportedOids=["aes128-CBC","aes192-CBC","aes256-CBC","des-EDE3-CBC","desCBC"],s}var c=n.kdfSalt,p=o.util.createBuffer(n.kdfIterationCount);p=p.getInt(p.length()<<3);var f,d;switch(l.oids[e]){case"aes128-CBC":f=16,d=o.aes.createDecryptionCipher;break;case"aes192-CBC":f=24,d=o.aes.createDecryptionCipher;break;case"aes256-CBC":f=32,d=o.aes.createDecryptionCipher;break;case"des-EDE3-CBC":f=24,d=o.des.createDecryptionCipher;break;case"desCBC":f=8,d=o.des.createDecryptionCipher}var y=a(n.prfOid),g=o.pkcs5.pbkdf2(r,c,p,f,y),v=n.encIv,m=d(g);return m.start(v),m},l.pbe.getCipherForPKCS12PBE=function(e,t,r){var n={},i=[];if(!u.validate(t,d,n,i)){var s=new Error("Cannot read password-based-encryption algorithm parameters. ASN.1 object is not a supported EncryptedPrivateKeyInfo.");throw s.errors=i,s}var c=o.util.createBuffer(n.salt),p=o.util.createBuffer(n.iterations);p=p.getInt(p.length()<<3);var f,h,y;switch(e){case l.oids["pbeWithSHAAnd3-KeyTripleDES-CBC"]:f=24,h=8,y=o.des.startDecrypting;break;case l.oids["pbewithSHAAnd40BitRC2-CBC"]:f=5,h=8,y=function(e,t){var r=o.rc2.createDecryptionCipher(e,40);return r.start(t,null),r};break;default:var s=new Error("Cannot read PKCS #12 PBE data block. Unsupported OID.");throw s.oid=e,s}var g=a(n.prfOid),v=l.pbe.generatePkcs12Key(r,c,1,p,f,g);return g.start(),y(v,l.pbe.generatePkcs12Key(r,c,2,p,h,g))},l.pbe.opensslDeriveBytes=function(e,t,r,a){if(void 0===a||null===a){if(!("md5"in o.md))throw new Error('"md5" hash algorithm unavailable.');a=o.md.md5.create()}null===t&&(t="");for(var i=[n(a,e+t)],s=16,c=1;s<r;++c,s+=16)i.push(n(a,i[c-1]+e+t));return i.join("").substr(0,r)}},function(e,t,r){function n(){o=String.fromCharCode(128),o+=i.util.fillString(String.fromCharCode(0),64),u=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],c=!0}function a(e,t,r){for(var n,a,i,s,o,c,l,p,f,h,d,y,g,v,m,C=r.length();C>=64;){for(l=0;l<16;++l)t[l]=r.getInt32();for(;l<64;++l)n=t[l-2],n=(n>>>17|n<<15)^(n>>>19|n<<13)^n>>>10,a=t[l-15],a=(a>>>7|a<<25)^(a>>>18|a<<14)^a>>>3,t[l]=n+t[l-7]+a+t[l-16]|0;for(p=e.h0,f=e.h1,h=e.h2,d=e.h3,y=e.h4,g=e.h5,v=e.h6,m=e.h7,l=0;l<64;++l)s=(y>>>6|y<<26)^(y>>>11|y<<21)^(y>>>25|y<<7),o=v^y&(g^v),i=(p>>>2|p<<30)^(p>>>13|p<<19)^(p>>>22|p<<10),c=p&f|h&(p^f),n=m+s+o+u[l]+t[l],a=i+c,m=v,v=g,g=y,y=d+n>>>0,d=h,h=f,f=p,p=n+a>>>0;e.h0=e.h0+p|0,e.h1=e.h1+f|0,e.h2=e.h2+h|0,e.h3=e.h3+d|0,e.h4=e.h4+y|0,e.h5=e.h5+g|0,e.h6=e.h6+v|0,e.h7=e.h7+m|0,C-=64}}var i=r(0);r(4),r(1);var s=e.exports=i.sha256=i.sha256||{};i.md.sha256=i.md.algorithms.sha256=s,s.create=function(){c||n();var e=null,t=i.util.createBuffer(),r=new Array(64),s={algorithm:"sha256",blockLength:64,digestLength:32,messageLength:0,fullMessageLength:null,messageLengthSize:8};return s.start=function(){s.messageLength=0,s.fullMessageLength=s.messageLength64=[];for(var r=s.messageLengthSize/4,n=0;n<r;++n)s.fullMessageLength.push(0);return t=i.util.createBuffer(),e={h0:1779033703,h1:3144134277,h2:1013904242,h3:2773480762,h4:1359893119,h5:2600822924,h6:528734635,h7:1541459225},s},s.start(),s.update=function(n,o){"utf8"===o&&(n=i.util.encodeUtf8(n));var c=n.length;s.messageLength+=c,c=[c/4294967296>>>0,c>>>0];for(var u=s.fullMessageLength.length-1;u>=0;--u)s.fullMessageLength[u]+=c[1],c[1]=c[0]+(s.fullMessageLength[u]/4294967296>>>0),s.fullMessageLength[u]=s.fullMessageLength[u]>>>0,c[0]=c[1]/4294967296>>>0;return t.putBytes(n),a(e,r,t),(t.read>2048||0===t.length())&&t.compact(),s},s.digest=function(){var n=i.util.createBuffer();n.putBytes(t.bytes());var c=s.fullMessageLength[s.fullMessageLength.length-1]+s.messageLengthSize,u=c&s.blockLength-1;n.putBytes(o.substr(0,s.blockLength-u));for(var l,p,f=8*s.fullMessageLength[0],h=0;h<s.fullMessageLength.length-1;++h)l=8*s.fullMessageLength[h+1],p=l/4294967296>>>0,f+=p,n.putInt32(f>>>0),f=l>>>0;n.putInt32(f);var d={h0:e.h0,h1:e.h1,h2:e.h2,h3:e.h3,h4:e.h4,h5:e.h5,h6:e.h6,h7:e.h7};a(d,r,n);var y=i.util.createBuffer();return y.putInt32(d.h0),y.putInt32(d.h1),y.putInt32(d.h2),y.putInt32(d.h3),y.putInt32(d.h4),y.putInt32(d.h5),y.putInt32(d.h6),y.putInt32(d.h7),y},s};var o=null,c=!1,u=null},function(e,t,r){var n=r(0);r(1);var a=null;!n.util.isNodejs||n.options.usePureJavaScript||process.versions["node-webkit"]||(a=r(17)),(e.exports=n.prng=n.prng||{}).create=function(e){function t(e){if(o.pools[0].messageLength>=32)return i(),e();var t=32-o.pools[0].messageLength<<5;o.seedFile(t,function(t,r){if(t)return e(t);o.collect(r),i(),e()})}function r(){if(o.pools[0].messageLength>=32)return i();var e=32-o.pools[0].messageLength<<5;o.collect(o.seedFileSync(e)),i()}function i(){o.reseeds=4294967295===o.reseeds?0:o.reseeds+1;var e=o.plugin.md.create();e.update(o.keyBytes);for(var t=1,r=0;r<32;++r)o.reseeds%t==0&&(e.update(o.pools[r].digest().getBytes()),o.pools[r].start()),t<<=1;o.keyBytes=e.digest().getBytes(),e.start(),e.update(o.keyBytes);var n=e.digest().getBytes();o.key=o.plugin.formatKey(o.keyBytes),o.seed=o.plugin.formatSeed(n),o.generated=0}function s(e){var t=null,r=n.util.globalScope,a=r.crypto||r.msCrypto;a&&a.getRandomValues&&(t=function(e){return a.getRandomValues(e)});var i=n.util.createBuffer();if(t)for(;i.length()<e;){var s=Math.max(1,Math.min(e-i.length(),65536)/4),o=new Uint32Array(Math.floor(s));try{t(o);for(var c=0;c<o.length;++c)i.putInt32(o[c])}catch(e){if(!("undefined"!=typeof QuotaExceededError&&e instanceof QuotaExceededError))throw e}}if(i.length()<e)for(var u,l,p,f=Math.floor(65536*Math.random());i.length()<e;){l=16807*(65535&f),u=16807*(f>>16),l+=(32767&u)<<16,l+=u>>15,l=(2147483647&l)+(l>>31),f=4294967295&l;for(var c=0;c<3;++c)p=f>>>(c<<3),p^=Math.floor(256*Math.random()),i.putByte(String.fromCharCode(255&p))}return i.getBytes(e)}for(var o={plugin:e,key:null,seed:null,time:null,reseeds:0,generated:0,keyBytes:""},c=e.md,u=new Array(32),l=0;l<32;++l)u[l]=c.create();return o.pools=u,o.pool=0,o.generate=function(e,r){function a(p){if(p)return r(p);if(l.length()>=e)return r(null,l.getBytes(e));if(o.generated>1048575&&(o.key=null),null===o.key)return n.util.nextTick(function(){t(a)});var f=i(o.key,o.seed);o.generated+=f.length,l.putBytes(f),o.key=c(i(o.key,s(o.seed))),o.seed=u(i(o.key,o.seed)),n.util.setImmediate(a)}if(!r)return o.generateSync(e);var i=o.plugin.cipher,s=o.plugin.increment,c=o.plugin.formatKey,u=o.plugin.formatSeed,l=n.util.createBuffer();o.key=null,a()},o.generateSync=function(e){var t=o.plugin.cipher,a=o.plugin.increment,i=o.plugin.formatKey,s=o.plugin.formatSeed;o.key=null;for(var c=n.util.createBuffer();c.length()<e;){o.generated>1048575&&(o.key=null),null===o.key&&r();var u=t(o.key,o.seed);o.generated+=u.length,c.putBytes(u),o.key=i(t(o.key,a(o.seed))),o.seed=s(t(o.key,o.seed))}return c.getBytes(e)},a?(o.seedFile=function(e,t){a.randomBytes(e,function(e,r){if(e)return t(e);t(null,r.toString())})},o.seedFileSync=function(e){return a.randomBytes(e).toString()}):(o.seedFile=function(e,t){try{t(null,s(e))}catch(e){t(e)}},o.seedFileSync=s),o.collect=function(e){for(var t=e.length,r=0;r<t;++r)o.pools[o.pool].update(e.substr(r,1)),o.pool=31===o.pool?0:o.pool+1},o.collectInt=function(e,t){for(var r="",n=0;n<t;n+=8)r+=String.fromCharCode(e>>n&255);o.collect(r)},o.registerWorker=function(e){if(e===self)o.seedFile=function(e,t){function r(e){var n=e.data;n.forge&&n.forge.prng&&(self.removeEventListener("message",r),t(n.forge.prng.err,n.forge.prng.bytes))}self.addEventListener("message",r),self.postMessage({forge:{prng:{needed:e}}})};else{var t=function(t){var r=t.data;r.forge&&r.forge.prng&&o.seedFile(r.forge.prng.needed,function(t,r){e.postMessage({forge:{prng:{err:t,bytes:r}}})})};e.addEventListener("message",t)}},o}},function(e,t,r){var n=r(0);r(1);var a=[217,120,249,196,25,221,181,237,40,233,253,121,74,160,216,157,198,126,55,131,43,118,83,142,98,76,100,136,68,139,251,162,23,154,89,245,135,179,79,19,97,69,109,141,9,129,125,50,189,143,64,235,134,183,123,11,240,149,33,34,92,107,78,130,84,214,101,147,206,96,178,28,115,86,192,20,167,140,241,220,18,117,202,31,59,190,228,209,66,61,212,48,163,60,182,38,111,191,14,218,70,105,7,87,39,242,29,155,188,148,67,3,248,17,199,246,144,239,62,231,6,195,213,47,200,102,30,215,8,232,234,222,128,82,238,247,132,170,114,172,53,77,106,42,150,26,210,113,90,21,73,116,75,159,208,94,4,24,164,236,194,224,65,110,15,81,203,204,36,145,175,80,161,244,112,57,153,124,58,133,35,184,180,122,252,2,54,91,37,85,151,49,45,93,250,152,227,138,146,174,5,223,41,16,103,108,186,201,211,0,230,207,225,158,168,44,99,22,1,63,88,226,137,169,13,56,52,27,171,51,255,176,187,72,12,95,185,177,205,46,197,243,219,71,229,165,156,119,10,166,32,104,254,127,193,173],i=[1,2,3,5],s=function(e,t){return e<<t&65535|(65535&e)>>16-t},o=function(e,t){return(65535&e)>>t|e<<16-t&65535};e.exports=n.rc2=n.rc2||{},n.rc2.expandKey=function(e,t){"string"==typeof e&&(e=n.util.createBuffer(e)),t=t||128;var r,i=e,s=e.length(),o=t,c=Math.ceil(o/8),u=255>>(7&o);for(r=s;r<128;r++)i.putByte(a[i.at(r-1)+i.at(r-s)&255]);for(i.setAt(128-c,a[i.at(128-c)&u]),r=127-c;r>=0;r--)i.setAt(r,a[i.at(r+1)^i.at(r+c)]);return i};var c=function(e,t,r){var a,c,u,l,p=!1,f=null,h=null,d=null,y=[];for(e=n.rc2.expandKey(e,t),u=0;u<64;u++)y.push(e.getInt16Le());r?(a=function(e){for(u=0;u<4;u++)e[u]+=y[l]+(e[(u+3)%4]&e[(u+2)%4])+(~e[(u+3)%4]&e[(u+1)%4]),e[u]=s(e[u],i[u]),l++},c=function(e){for(u=0;u<4;u++)e[u]+=y[63&e[(u+3)%4]]}):(a=function(e){for(u=3;u>=0;u--)e[u]=o(e[u],i[u]),e[u]-=y[l]+(e[(u+3)%4]&e[(u+2)%4])+(~e[(u+3)%4]&e[(u+1)%4]),l--},c=function(e){for(u=3;u>=0;u--)e[u]-=y[63&e[(u+3)%4]]});var g=function(e){var t=[];for(u=0;u<4;u++){var n=f.getInt16Le();null!==d&&(r?n^=d.getInt16Le():d.putInt16Le(n)),t.push(65535&n)}l=r?0:63;for(var a=0;a<e.length;a++)for(var i=0;i<e[a][0];i++)e[a][1](t);for(u=0;u<4;u++)null!==d&&(r?d.putInt16Le(t[u]):t[u]^=d.getInt16Le()),h.putInt16Le(t[u])},v=null;return v={start:function(e,t){e&&"string"==typeof e&&(e=n.util.createBuffer(e)),p=!1,f=n.util.createBuffer(),h=t||new n.util.createBuffer,d=e,v.output=h},update:function(e){for(p||f.putBuffer(e);f.length()>=8;)g([[5,a],[1,c],[6,a],[1,c],[5,a]])},finish:function(e){var t=!0;if(r)if(e)t=e(8,f,!r);else{var n=8===f.length()?8:8-f.length();f.fillWithByte(n,n)}if(t&&(p=!0,v.update()),!r&&(t=0===f.length()))if(e)t=e(8,h,!r);else{var a=h.length(),i=h.at(a-1);i>a?t=!1:h.truncate(i)}return t}}};n.rc2.startEncrypting=function(e,t,r){var a=n.rc2.createEncryptionCipher(e,128);return a.start(t,r),a},n.rc2.createEncryptionCipher=function(e,t){return c(e,t,!0)},n.rc2.startDecrypting=function(e,t,r){var a=n.rc2.createDecryptionCipher(e,128);return a.start(t,r),a},n.rc2.createDecryptionCipher=function(e,t){return c(e,t,!1)}},function(e,t,r){function n(e,t,r){r||(r=a.md.sha1.create());for(var n="",i=Math.ceil(t/r.digestLength),s=0;s<i;++s){var o=String.fromCharCode(s>>24&255,s>>16&255,s>>8&255,255&s);r.start(),r.update(e+o),n+=r.digest().getBytes()}return n.substring(0,t)}var a=r(0);r(1),r(2),r(9);var i=e.exports=a.pkcs1=a.pkcs1||{};i.encode_rsa_oaep=function(e,t,r){var i,s,o,c;"string"==typeof r?(i=r,s=arguments[3]||void 0,o=arguments[4]||void 0):r&&(i=r.label||void 0,s=r.seed||void 0,o=r.md||void 0,r.mgf1&&r.mgf1.md&&(c=r.mgf1.md)),o?o.start():o=a.md.sha1.create(),c||(c=o);var u=Math.ceil(e.n.bitLength()/8),l=u-2*o.digestLength-2;if(t.length>l){var p=new Error("RSAES-OAEP input message length is too long.");throw p.length=t.length,p.maxLength=l,p}i||(i=""),o.update(i,"raw");for(var f=o.digest(),h="",d=l-t.length,y=0;y<d;y++)h+="\0";var g=f.getBytes()+h+""+t;if(s){if(s.length!==o.digestLength){var p=new Error("Invalid RSAES-OAEP seed. The seed length must match the digest length.");throw p.seedLength=s.length,p.digestLength=o.digestLength,p}}else s=a.random.getBytes(o.digestLength);var v=n(s,u-o.digestLength-1,c),m=a.util.xorBytes(g,v,g.length),C=n(m,o.digestLength,c);return"\0"+a.util.xorBytes(s,C,s.length)+m},i.decode_rsa_oaep=function(e,t,r){var i,s,o;"string"==typeof r?(i=r,s=arguments[3]||void 0):r&&(i=r.label||void 0,s=r.md||void 0,r.mgf1&&r.mgf1.md&&(o=r.mgf1.md));var c=Math.ceil(e.n.bitLength()/8);if(t.length!==c){var u=new Error("RSAES-OAEP encoded message length is invalid.");throw u.length=t.length,u.expectedLength=c,u}if(void 0===s?s=a.md.sha1.create():s.start(),o||(o=s),c<2*s.digestLength+2)throw new Error("RSAES-OAEP key is too short for the hash function.");i||(i=""),s.update(i,"raw");for(var l=s.digest().getBytes(),p=t.charAt(0),f=t.substring(1,s.digestLength+1),h=t.substring(1+s.digestLength),d=n(h,s.digestLength,o),y=a.util.xorBytes(f,d,f.length),g=n(y,c-s.digestLength-1,o),v=a.util.xorBytes(h,g,h.length),m=v.substring(0,s.digestLength),u="\0"!==p,C=0;C<s.digestLength;++C)u|=l.charAt(C)!==m.charAt(C);for(var E=1,S=s.digestLength,T=s.digestLength;T<v.length;T++){var b=v.charCodeAt(T),I=1&b^1;u|=b&(E?65534:0),E&=I,S+=E}if(u||1!==v.charCodeAt(S))throw new Error("Invalid RSAES-OAEP padding.");return v.substring(S+1)}},function(e,t,r){var n=r(0);r(1),r(13),r(2),function(){function t(e,t,n,a){return"workers"in n?i(e,t,n,a):r(e,t,n,a)}function r(e,t,r,n){var i=s(e,t),c=o(i.bitLength());"millerRabinTests"in r&&(c=r.millerRabinTests);var u=10;"maxBlockTime"in r&&(u=r.maxBlockTime),a(i,e,t,0,c,u,n)}function a(e,t,r,i,o,c,u){var p=+new Date;do{if(e.bitLength()>t&&(e=s(t,r)),e.isProbablePrime(o))return u(null,e);e.dAddOffset(l[i++%8],0)}while(c<0||+new Date-p<c);n.util.setImmediate(function(){a(e,t,r,i,o,c,u)})}function i(e,t,a,i){function o(){function r(r){if(!d){--o;var a=r.data;if(a.found){for(var l=0;l<n.length;++l)n[l].terminate();return d=!0,i(null,new u(a.prime,16))}c.bitLength()>e&&(c=s(e,t));var h=c.toString(16);r.target.postMessage({hex:h,workLoad:p}),c.dAddOffset(f,0)}}l=Math.max(1,l);for(var n=[],a=0;a<l;++a)n[a]=new Worker(h);for(var o=l,a=0;a<l;++a)n[a].addEventListener("message",r);var d=!1}if("undefined"==typeof Worker)return r(e,t,a,i);var c=s(e,t),l=a.workers,p=a.workLoad||100,f=30*p/8,h=a.workerScript||"forge/prime.worker.js";if(-1===l)return n.util.estimateCores(function(e,t){e&&(t=2),l=t-1,o()});o()}function s(e,t){var r=new u(e,t),n=e-1;return r.testBit(n)||r.bitwiseTo(u.ONE.shiftLeft(n),f,r),r.dAddOffset(31-r.mod(p).byteValue(),0),r}function o(e){return e<=100?27:e<=150?18:e<=200?15:e<=250?12:e<=300?9:e<=350?8:e<=400?7:e<=500?6:e<=600?5:e<=800?4:e<=1250?3:2}if(n.prime)return void(e.exports=n.prime);var c=e.exports=n.prime=n.prime||{},u=n.jsbn.BigInteger,l=[6,4,2,4,2,4,6,2],p=new u(null);p.fromInt(30);var f=function(e,t){return e|t};c.generateProbablePrime=function(e,r,a){"function"==typeof r&&(a=r,r={}),r=r||{};var i=r.algorithm||"PRIMEINC";"string"==typeof i&&(i={name:i}),i.options=i.options||{};var s=r.prng||n.random,o={nextBytes:function(e){for(var t=s.getBytesSync(e.length),r=0;r<e.length;++r)e[r]=t.charCodeAt(r)}};if("PRIMEINC"===i.name)return t(e,o,i.options,a);throw new Error("Invalid prime generation algorithm: "+i.name)}}()},function(e,t,r){function n(e,t,r,n){for(var a=[],i=0;i<e.length;i++)for(var s=0;s<e[i].safeBags.length;s++){var o=e[i].safeBags[s];void 0!==n&&o.type!==n||(null!==t?void 0!==o.attributes[t]&&o.attributes[t].indexOf(r)>=0&&a.push(o):a.push(o))}return a}function a(e){if(e.composed||e.constructed){for(var t=u.util.createBuffer(),r=0;r<e.value.length;++r)t.putBytes(e.value[r].value);e.composed=e.constructed=!1,e.value=t.getBytes()}return e}function i(e,t,r,n){if(t=l.fromDer(t,r),t.tagClass!==l.Class.UNIVERSAL||t.type!==l.Type.SEQUENCE||!0!==t.constructed)throw new Error("PKCS#12 AuthenticatedSafe expected to be a SEQUENCE OF ContentInfo");for(var i=0;i<t.value.length;i++){var c=t.value[i],u={},f=[];if(!l.validate(c,h,u,f)){var d=new Error("Cannot read ContentInfo.");throw d.errors=f,d}var y={encrypted:!1},g=null,v=u.content.value[0];switch(l.derToOid(u.contentType)){case p.oids.data:if(v.tagClass!==l.Class.UNIVERSAL||v.type!==l.Type.OCTETSTRING)throw new Error("PKCS#12 SafeContents Data is not an OCTET STRING.");g=a(v).value;break;case p.oids.encryptedData:g=s(v,n),y.encrypted=!0;break;default:var d=new Error("Unsupported PKCS#12 contentType.");throw d.contentType=l.derToOid(u.contentType),d}y.safeBags=o(g,r,n),e.safeContents.push(y)}}function s(e,t){var r={},n=[];if(!l.validate(e,u.pkcs7.asn1.encryptedDataValidator,r,n)){var i=new Error("Cannot read EncryptedContentInfo.");throw i.errors=n,i}var s=l.derToOid(r.contentType);if(s!==p.oids.data){var i=new Error("PKCS#12 EncryptedContentInfo ContentType is not Data.");throw i.oid=s,i}s=l.derToOid(r.encAlgorithm);var o=p.pbe.getCipher(s,r.encParameter,t),c=a(r.encryptedContentAsn1),f=u.util.createBuffer(c.value);if(o.update(f),!o.finish())throw new Error("Failed to decrypt PKCS#12 SafeContents.");return o.output.getBytes()}function o(e,t,r){if(!t&&0===e.length)return[];if(e=l.fromDer(e,t),e.tagClass!==l.Class.UNIVERSAL||e.type!==l.Type.SEQUENCE||!0!==e.constructed)throw new Error("PKCS#12 SafeContents expected to be a SEQUENCE OF SafeBag.");for(var n=[],a=0;a<e.value.length;a++){var i=e.value[a],s={},o=[];if(!l.validate(i,y,s,o)){var u=new Error("Cannot read SafeBag.");throw u.errors=o,u}var f={type:l.derToOid(s.bagId),attributes:c(s.bagAttributes)};n.push(f);var h,d,g=s.bagValue.value[0];switch(f.type){case p.oids.pkcs8ShroudedKeyBag:if(null===(g=p.decryptPrivateKeyInfo(g,r)))throw new Error("Unable to decrypt PKCS#8 ShroudedKeyBag, wrong password?");case p.oids.keyBag:try{f.key=p.privateKeyFromAsn1(g)}catch(e){f.key=null,f.asn1=g}continue;case p.oids.certBag:h=v,d=function(){if(l.derToOid(s.certId)!==p.oids.x509Certificate){var e=new Error("Unsupported certificate type, only X.509 supported.");throw e.oid=l.derToOid(s.certId),e}var r=l.fromDer(s.cert,t);try{f.cert=p.certificateFromAsn1(r,!0)}catch(e){f.cert=null,f.asn1=r}};break;default:var u=new Error("Unsupported PKCS#12 SafeBag type.");throw u.oid=f.type,u}if(void 0!==h&&!l.validate(g,h,s,o)){var u=new Error("Cannot read PKCS#12 "+h.name);throw u.errors=o,u}d()}return n}function c(e){var t={};if(void 0!==e)for(var r=0;r<e.length;++r){var n={},a=[];if(!l.validate(e[r],g,n,a)){var i=new Error("Cannot read PKCS#12 BagAttribute.");throw i.errors=a,i}var s=l.derToOid(n.oid);if(void 0!==p.oids[s]){t[p.oids[s]]=[];for(var o=0;o<n.values.length;++o)t[p.oids[s]].push(n.values[o].value)}}return t}var u=r(0);r(3),r(8),r(6),r(30),r(23),r(2),r(12),r(9),r(1),r(18);var l=u.asn1,p=u.pki,f=e.exports=u.pkcs12=u.pkcs12||{},h={name:"ContentInfo",tagClass:l.Class.UNIVERSAL,type:l.Type.SEQUENCE,constructed:!0,value:[{name:"ContentInfo.contentType",tagClass:l.Class.UNIVERSAL,type:l.Type.OID,constructed:!1,capture:"contentType"},{name:"ContentInfo.content",tagClass:l.Class.CONTEXT_SPECIFIC,constructed:!0,captureAsn1:"content"}]},d={name:"PFX",tagClass:l.Class.UNIVERSAL,type:l.Type.SEQUENCE,constructed:!0,value:[{name:"PFX.version",tagClass:l.Class.UNIVERSAL,type:l.Type.INTEGER,constructed:!1,capture:"version"},h,{name:"PFX.macData",tagClass:l.Class.UNIVERSAL,type:l.Type.SEQUENCE,constructed:!0,optional:!0,captureAsn1:"mac",value:[{name:"PFX.macData.mac",tagClass:l.Class.UNIVERSAL,type:l.Type.SEQUENCE,constructed:!0,value:[{name:"PFX.macData.mac.digestAlgorithm",tagClass:l.Class.UNIVERSAL,type:l.Type.SEQUENCE,constructed:!0,value:[{name:"PFX.macData.mac.digestAlgorithm.algorithm",tagClass:l.Class.UNIVERSAL,type:l.Type.OID,constructed:!1,capture:"macAlgorithm"},{name:"PFX.macData.mac.digestAlgorithm.parameters",tagClass:l.Class.UNIVERSAL,captureAsn1:"macAlgorithmParameters"}]},{name:"PFX.macData.mac.digest",tagClass:l.Class.UNIVERSAL,type:l.Type.OCTETSTRING,constructed:!1,capture:"macDigest"}]},{name:"PFX.macData.macSalt",tagClass:l.Class.UNIVERSAL,type:l.Type.OCTETSTRING,constructed:!1,capture:"macSalt"},{name:"PFX.macData.iterations",tagClass:l.Class.UNIVERSAL,type:l.Type.INTEGER,constructed:!1,optional:!0,capture:"macIterations"}]}]},y={name:"SafeBag",tagClass:l.Class.UNIVERSAL,type:l.Type.SEQUENCE,constructed:!0,value:[{name:"SafeBag.bagId",tagClass:l.Class.UNIVERSAL,type:l.Type.OID,constructed:!1,capture:"bagId"},{name:"SafeBag.bagValue",tagClass:l.Class.CONTEXT_SPECIFIC,constructed:!0,captureAsn1:"bagValue"},{name:"SafeBag.bagAttributes",tagClass:l.Class.UNIVERSAL,type:l.Type.SET,constructed:!0,optional:!0,capture:"bagAttributes"}]},g={name:"Attribute",tagClass:l.Class.UNIVERSAL,type:l.Type.SEQUENCE,constructed:!0,value:[{name:"Attribute.attrId",tagClass:l.Class.UNIVERSAL,type:l.Type.OID,constructed:!1,capture:"oid"},{name:"Attribute.attrValues",tagClass:l.Class.UNIVERSAL,type:l.Type.SET,constructed:!0,capture:"values"}]},v={name:"CertBag",tagClass:l.Class.UNIVERSAL,type:l.Type.SEQUENCE,constructed:!0,value:[{name:"CertBag.certId",tagClass:l.Class.UNIVERSAL,type:l.Type.OID,constructed:!1,capture:"certId"},{name:"CertBag.certValue",tagClass:l.Class.CONTEXT_SPECIFIC,constructed:!0,value:[{name:"CertBag.certValue[0]",tagClass:l.Class.UNIVERSAL,type:l.Class.OCTETSTRING,constructed:!1,capture:"cert"}]}]};f.pkcs12FromAsn1=function(e,t,r){"string"==typeof t?(r=t,t=!0):void 0===t&&(t=!0);var s={},o=[];if(!l.validate(e,d,s,o)){var c=new Error("Cannot read PKCS#12 PFX. ASN.1 object is not an PKCS#12 PFX.");throw c.errors=c,c}var h={version:s.version.charCodeAt(0),safeContents:[],getBags:function(e){var t,r={};return"localKeyId"in e?t=e.localKeyId:"localKeyIdHex"in e&&(t=u.util.hexToBytes(e.localKeyIdHex)),void 0===t&&!("friendlyName"in e)&&"bagType"in e&&(r[e.bagType]=n(h.safeContents,null,null,e.bagType)),void 0!==t&&(r.localKeyId=n(h.safeContents,"localKeyId",t,e.bagType)),"friendlyName"in e&&(r.friendlyName=n(h.safeContents,"friendlyName",e.friendlyName,e.bagType)),r},getBagsByFriendlyName:function(e,t){return n(h.safeContents,"friendlyName",e,t)},getBagsByLocalKeyId:function(e,t){return n(h.safeContents,"localKeyId",e,t)}};if(3!==s.version.charCodeAt(0)){var c=new Error("PKCS#12 PFX of version other than 3 not supported.");throw c.version=s.version.charCodeAt(0),c}if(l.derToOid(s.contentType)!==p.oids.data){var c=new Error("Only PKCS#12 PFX in password integrity mode supported.");throw c.oid=l.derToOid(s.contentType),c}var y=s.content.value[0];if(y.tagClass!==l.Class.UNIVERSAL||y.type!==l.Type.OCTETSTRING)throw new Error("PKCS#12 authSafe content data is not an OCTET STRING.");if(y=a(y),s.mac){var g=null,v=0,m=l.derToOid(s.macAlgorithm);switch(m){case p.oids.sha1:g=u.md.sha1.create(),v=20;break;case p.oids.sha256:g=u.md.sha256.create(),v=32;break;case p.oids.sha384:g=u.md.sha384.create(),v=48;break;case p.oids.sha512:g=u.md.sha512.create(),v=64;break;case p.oids.md5:g=u.md.md5.create(),v=16}if(null===g)throw new Error("PKCS#12 uses unsupported MAC algorithm: "+m);var C=new u.util.ByteBuffer(s.macSalt),E="macIterations"in s?parseInt(u.util.bytesToHex(s.macIterations),16):1,S=f.generateKey(r,C,3,E,v,g),T=u.hmac.create();T.start(g,S),T.update(y.value);if(T.getMac().getBytes()!==s.macDigest)throw new Error("PKCS#12 MAC could not be verified. Invalid password?")}return i(h,y.value,t,r),h},f.toPkcs12Asn1=function(e,t,r,n){n=n||{},n.saltSize=n.saltSize||8,n.count=n.count||2048,n.algorithm=n.algorithm||n.encAlgorithm||"aes128","useMac"in n||(n.useMac=!0),"localKeyId"in n||(n.localKeyId=null),"generateLocalKeyId"in n||(n.generateLocalKeyId=!0);var a,i=n.localKeyId;if(null!==i)i=u.util.hexToBytes(i);else if(n.generateLocalKeyId)if(t){var s=u.util.isArray(t)?t[0]:t;"string"==typeof s&&(s=p.certificateFromPem(s));var o=u.md.sha1.create();o.update(l.toDer(p.certificateToAsn1(s)).getBytes()),i=o.digest().getBytes()}else i=u.random.getBytes(20);var c=[];null!==i&&c.push(l.create(l.Class.UNIVERSAL,l.Type.SEQUENCE,!0,[l.create(l.Class.UNIVERSAL,l.Type.OID,!1,l.oidToDer(p.oids.localKeyId).getBytes()),l.create(l.Class.UNIVERSAL,l.Type.SET,!0,[l.create(l.Class.UNIVERSAL,l.Type.OCTETSTRING,!1,i)])])),"friendlyName"in n&&c.push(l.create(l.Class.UNIVERSAL,l.Type.SEQUENCE,!0,[l.create(l.Class.UNIVERSAL,l.Type.OID,!1,l.oidToDer(p.oids.friendlyName).getBytes()),l.create(l.Class.UNIVERSAL,l.Type.SET,!0,[l.create(l.Class.UNIVERSAL,l.Type.BMPSTRING,!1,n.friendlyName)])])),c.length>0&&(a=l.create(l.Class.UNIVERSAL,l.Type.SET,!0,c));var h=[],d=[];null!==t&&(d=u.util.isArray(t)?t:[t]);for(var y=[],g=0;g<d.length;++g){t=d[g],"string"==typeof t&&(t=p.certificateFromPem(t));var v=0===g?a:void 0,m=p.certificateToAsn1(t),C=l.create(l.Class.UNIVERSAL,l.Type.SEQUENCE,!0,[l.create(l.Class.UNIVERSAL,l.Type.OID,!1,l.oidToDer(p.oids.certBag).getBytes()),l.create(l.Class.CONTEXT_SPECIFIC,0,!0,[l.create(l.Class.UNIVERSAL,l.Type.SEQUENCE,!0,[l.create(l.Class.UNIVERSAL,l.Type.OID,!1,l.oidToDer(p.oids.x509Certificate).getBytes()),l.create(l.Class.CONTEXT_SPECIFIC,0,!0,[l.create(l.Class.UNIVERSAL,l.Type.OCTETSTRING,!1,l.toDer(m).getBytes())])])]),v]);y.push(C)}if(y.length>0){var E=l.create(l.Class.UNIVERSAL,l.Type.SEQUENCE,!0,y),S=l.create(l.Class.UNIVERSAL,l.Type.SEQUENCE,!0,[l.create(l.Class.UNIVERSAL,l.Type.OID,!1,l.oidToDer(p.oids.data).getBytes()),l.create(l.Class.CONTEXT_SPECIFIC,0,!0,[l.create(l.Class.UNIVERSAL,l.Type.OCTETSTRING,!1,l.toDer(E).getBytes())])]);h.push(S)}var T=null;if(null!==e){var b=p.wrapRsaPrivateKey(p.privateKeyToAsn1(e));T=null===r?l.create(l.Class.UNIVERSAL,l.Type.SEQUENCE,!0,[l.create(l.Class.UNIVERSAL,l.Type.OID,!1,l.oidToDer(p.oids.keyBag).getBytes()),l.create(l.Class.CONTEXT_SPECIFIC,0,!0,[b]),a]):l.create(l.Class.UNIVERSAL,l.Type.SEQUENCE,!0,[l.create(l.Class.UNIVERSAL,l.Type.OID,!1,l.oidToDer(p.oids.pkcs8ShroudedKeyBag).getBytes()),l.create(l.Class.CONTEXT_SPECIFIC,0,!0,[p.encryptPrivateKeyInfo(b,r,n)]),a]);var I=l.create(l.Class.UNIVERSAL,l.Type.SEQUENCE,!0,[T]),A=l.create(l.Class.UNIVERSAL,l.Type.SEQUENCE,!0,[l.create(l.Class.UNIVERSAL,l.Type.OID,!1,l.oidToDer(p.oids.data).getBytes()),l.create(l.Class.CONTEXT_SPECIFIC,0,!0,[l.create(l.Class.UNIVERSAL,l.Type.OCTETSTRING,!1,l.toDer(I).getBytes())])]);h.push(A)}var B,k=l.create(l.Class.UNIVERSAL,l.Type.SEQUENCE,!0,h);if(n.useMac){var o=u.md.sha1.create(),N=new u.util.ByteBuffer(u.random.getBytes(n.saltSize)),w=n.count,e=f.generateKey(r,N,3,w,20),R=u.hmac.create();R.start(o,e),R.update(l.toDer(k).getBytes());var L=R.getMac();B=l.create(l.Class.UNIVERSAL,l.Type.SEQUENCE,!0,[l.create(l.Class.UNIVERSAL,l.Type.SEQUENCE,!0,[l.create(l.Class.UNIVERSAL,l.Type.SEQUENCE,!0,[l.create(l.Class.UNIVERSAL,l.Type.OID,!1,l.oidToDer(p.oids.sha1).getBytes()),l.create(l.Class.UNIVERSAL,l.Type.NULL,!1,"")]),l.create(l.Class.UNIVERSAL,l.Type.OCTETSTRING,!1,L.getBytes())]),l.create(l.Class.UNIVERSAL,l.Type.OCTETSTRING,!1,N.getBytes()),l.create(l.Class.UNIVERSAL,l.Type.INTEGER,!1,l.integerToDer(w).getBytes())])}return l.create(l.Class.UNIVERSAL,l.Type.SEQUENCE,!0,[l.create(l.Class.UNIVERSAL,l.Type.INTEGER,!1,l.integerToDer(3).getBytes()),l.create(l.Class.UNIVERSAL,l.Type.SEQUENCE,!0,[l.create(l.Class.UNIVERSAL,l.Type.OID,!1,l.oidToDer(p.oids.data).getBytes()),l.create(l.Class.CONTEXT_SPECIFIC,0,!0,[l.create(l.Class.UNIVERSAL,l.Type.OCTETSTRING,!1,l.toDer(k).getBytes())])]),B])},f.generateKey=u.pbe.generatePkcs12Key},function(e,t,r){var n=r(0);r(3),r(1);var a=n.asn1,i=e.exports=n.pkcs7asn1=n.pkcs7asn1||{};n.pkcs7=n.pkcs7||{},n.pkcs7.asn1=i;var s={name:"ContentInfo",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"ContentInfo.ContentType",tagClass:a.Class.UNIVERSAL,type:a.Type.OID,constructed:!1,capture:"contentType"},{name:"ContentInfo.content",tagClass:a.Class.CONTEXT_SPECIFIC,type:0,constructed:!0,optional:!0,captureAsn1:"content"}]};i.contentInfoValidator=s;var o={name:"EncryptedContentInfo",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"EncryptedContentInfo.contentType",tagClass:a.Class.UNIVERSAL,type:a.Type.OID,constructed:!1,capture:"contentType"},{name:"EncryptedContentInfo.contentEncryptionAlgorithm",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"EncryptedContentInfo.contentEncryptionAlgorithm.algorithm",tagClass:a.Class.UNIVERSAL,type:a.Type.OID,constructed:!1,capture:"encAlgorithm"},{name:"EncryptedContentInfo.contentEncryptionAlgorithm.parameter",tagClass:a.Class.UNIVERSAL,captureAsn1:"encParameter"}]},{name:"EncryptedContentInfo.encryptedContent",tagClass:a.Class.CONTEXT_SPECIFIC,type:0,capture:"encryptedContent",captureAsn1:"encryptedContentAsn1"}]};i.envelopedDataValidator={name:"EnvelopedData",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"EnvelopedData.Version",tagClass:a.Class.UNIVERSAL,type:a.Type.INTEGER,constructed:!1,capture:"version"},{name:"EnvelopedData.RecipientInfos",tagClass:a.Class.UNIVERSAL,type:a.Type.SET,constructed:!0,captureAsn1:"recipientInfos"}].concat(o)},i.encryptedDataValidator={name:"EncryptedData",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"EncryptedData.Version",tagClass:a.Class.UNIVERSAL,type:a.Type.INTEGER,constructed:!1,capture:"version"}].concat(o)};var c={name:"SignerInfo",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"SignerInfo.version",tagClass:a.Class.UNIVERSAL,type:a.Type.INTEGER,constructed:!1},{name:"SignerInfo.issuerAndSerialNumber",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"SignerInfo.issuerAndSerialNumber.issuer",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,captureAsn1:"issuer"},{name:"SignerInfo.issuerAndSerialNumber.serialNumber",tagClass:a.Class.UNIVERSAL,type:a.Type.INTEGER,constructed:!1,capture:"serial"}]},{name:"SignerInfo.digestAlgorithm",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"SignerInfo.digestAlgorithm.algorithm",tagClass:a.Class.UNIVERSAL,type:a.Type.OID,constructed:!1,capture:"digestAlgorithm"},{name:"SignerInfo.digestAlgorithm.parameter",tagClass:a.Class.UNIVERSAL,constructed:!1,captureAsn1:"digestParameter",optional:!0}]},{name:"SignerInfo.authenticatedAttributes",tagClass:a.Class.CONTEXT_SPECIFIC,type:0,constructed:!0,optional:!0,capture:"authenticatedAttributes"},{name:"SignerInfo.digestEncryptionAlgorithm",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,capture:"signatureAlgorithm"},{name:"SignerInfo.encryptedDigest",tagClass:a.Class.UNIVERSAL,type:a.Type.OCTETSTRING,constructed:!1,capture:"signature"},{name:"SignerInfo.unauthenticatedAttributes",tagClass:a.Class.CONTEXT_SPECIFIC,type:1,constructed:!0,optional:!0,capture:"unauthenticatedAttributes"}]};i.signedDataValidator={name:"SignedData",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"SignedData.Version",tagClass:a.Class.UNIVERSAL,type:a.Type.INTEGER,constructed:!1,capture:"version"},{name:"SignedData.DigestAlgorithms",tagClass:a.Class.UNIVERSAL,type:a.Type.SET,constructed:!0,captureAsn1:"digestAlgorithms"},s,{name:"SignedData.Certificates",tagClass:a.Class.CONTEXT_SPECIFIC,type:0,optional:!0,captureAsn1:"certificates"},{name:"SignedData.CertificateRevocationLists",tagClass:a.Class.CONTEXT_SPECIFIC,type:1,optional:!0,captureAsn1:"crls"},{name:"SignedData.SignerInfos",tagClass:a.Class.UNIVERSAL,type:a.Type.SET,capture:"signerInfos",optional:!0,value:[c]}]},i.recipientInfoValidator={name:"RecipientInfo",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"RecipientInfo.version",tagClass:a.Class.UNIVERSAL,type:a.Type.INTEGER,constructed:!1,capture:"version"},{name:"RecipientInfo.issuerAndSerial",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"RecipientInfo.issuerAndSerial.issuer",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,captureAsn1:"issuer"},{name:"RecipientInfo.issuerAndSerial.serialNumber",tagClass:a.Class.UNIVERSAL,type:a.Type.INTEGER,constructed:!1,capture:"serial"}]},{name:"RecipientInfo.keyEncryptionAlgorithm",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"RecipientInfo.keyEncryptionAlgorithm.algorithm",tagClass:a.Class.UNIVERSAL,type:a.Type.OID,constructed:!1,capture:"encAlgorithm"},{name:"RecipientInfo.keyEncryptionAlgorithm.parameter",tagClass:a.Class.UNIVERSAL,constructed:!1,captureAsn1:"encParameter"}]},{name:"RecipientInfo.encryptedKey",tagClass:a.Class.UNIVERSAL,type:a.Type.OCTETSTRING,constructed:!1,capture:"encKey"}]}},function(e,t,r){var n=r(0);r(1),n.mgf=n.mgf||{},(e.exports=n.mgf.mgf1=n.mgf1=n.mgf1||{}).create=function(e){return{generate:function(t,r){for(var a=new n.util.ByteBuffer,i=Math.ceil(r/e.digestLength),s=0;s<i;s++){var o=new n.util.ByteBuffer;o.putInt32(s),e.start(),e.update(t+o.getBytes()),a.putBuffer(e.digest())}return a.truncate(a.length()-r),a.getBytes()}}}},function(e,t,r){function n(){c=String.fromCharCode(128),c+=i.util.fillString(String.fromCharCode(0),128),l=[[1116352408,3609767458],[1899447441,602891725],[3049323471,3964484399],[3921009573,2173295548],[961987163,4081628472],[1508970993,3053834265],[2453635748,2937671579],[2870763221,3664609560],[3624381080,2734883394],[310598401,1164996542],[607225278,1323610764],[1426881987,3590304994],[1925078388,4068182383],[2162078206,991336113],[2614888103,633803317],[3248222580,3479774868],[3835390401,2666613458],[4022224774,944711139],[264347078,2341262773],[604807628,2007800933],[770255983,1495990901],[1249150122,1856431235],[1555081692,3175218132],[1996064986,2198950837],[2554220882,3999719339],[2821834349,766784016],[2952996808,2566594879],[3210313671,3203337956],[3336571891,1034457026],[3584528711,2466948901],[113926993,3758326383],[338241895,168717936],[666307205,1188179964],[773529912,1546045734],[1294757372,1522805485],[1396182291,2643833823],[1695183700,2343527390],[1986661051,1014477480],[2177026350,1206759142],[2456956037,344077627],[2730485921,1290863460],[2820302411,3158454273],[3259730800,3505952657],[3345764771,106217008],[3516065817,3606008344],[3600352804,1432725776],[4094571909,1467031594],[275423344,851169720],[430227734,3100823752],[506948616,1363258195],[659060556,3750685593],[883997877,3785050280],[958139571,3318307427],[1322822218,3812723403],[1537002063,2003034995],[1747873779,3602036899],[1955562222,1575990012],[2024104815,1125592928],[2227730452,2716904306],[2361852424,442776044],[2428436474,593698344],[2756734187,3733110249],[3204031479,2999351573],[3329325298,3815920427],[3391569614,3928383900],[3515267271,566280711],[3940187606,3454069534],[4118630271,4000239992],[116418474,1914138554],[174292421,2731055270],[289380356,3203993006],[460393269,320620315],[685471733,587496836],[852142971,1086792851],[1017036298,365543100],[1126000580,2618297676],[1288033470,3409855158],[1501505948,4234509866],[1607167915,987167468],[1816402316,1246189591]],p={},p["SHA-512"]=[[1779033703,4089235720],[3144134277,2227873595],[1013904242,4271175723],[2773480762,1595750129],[1359893119,2917565137],[2600822924,725511199],[528734635,4215389547],[1541459225,327033209]],p["SHA-384"]=[[3418070365,3238371032],[1654270250,914150663],[2438529370,812702999],[355462360,4144912697],[1731405415,4290775857],[2394180231,1750603025],[3675008525,1694076839],[1203062813,3204075428]],p["SHA-512/256"]=[[573645204,4230739756],[2673172387,3360449730],[596883563,1867755857],[2520282905,1497426621],[2519219938,2827943907],[3193839141,1401305490],[721525244,746961066],[246885852,2177182882]],p["SHA-512/224"]=[[2352822216,424955298],[1944164710,2312950998],[502970286,855612546],[1738396948,1479516111],[258812777,2077511080],[2011393907,79989058],[1067287976,1780299464],[286451373,2446758561]],u=!0}function a(e,t,r){for(var n,a,i,s,o,c,u,p,f,h,d,y,g,v,m,C,E,S,T,b,I,A,B,k,N,w,R,L,_,U,D,P,O,V,x,K=r.length();K>=128;){for(_=0;_<16;++_)t[_][0]=r.getInt32()>>>0,t[_][1]=r.getInt32()>>>0;for(;_<80;++_)P=t[_-2],U=P[0],D=P[1],n=((U>>>19|D<<13)^(D>>>29|U<<3)^U>>>6)>>>0,a=((U<<13|D>>>19)^(D<<3|U>>>29)^(U<<26|D>>>6))>>>0,V=t[_-15],U=V[0],D=V[1],i=((U>>>1|D<<31)^(U>>>8|D<<24)^U>>>7)>>>0,s=((U<<31|D>>>1)^(U<<24|D>>>8)^(U<<25|D>>>7))>>>0,O=t[_-7],x=t[_-16],D=a+O[1]+s+x[1],t[_][0]=n+O[0]+i+x[0]+(D/4294967296>>>0)>>>0,t[_][1]=D>>>0;for(g=e[0][0],v=e[0][1],m=e[1][0],C=e[1][1],E=e[2][0],S=e[2][1],T=e[3][0],b=e[3][1],I=e[4][0],A=e[4][1],B=e[5][0],k=e[5][1],N=e[6][0],w=e[6][1],R=e[7][0],L=e[7][1],_=0;_<80;++_)u=((I>>>14|A<<18)^(I>>>18|A<<14)^(A>>>9|I<<23))>>>0,p=((I<<18|A>>>14)^(I<<14|A>>>18)^(A<<23|I>>>9))>>>0,f=(N^I&(B^N))>>>0,h=(w^A&(k^w))>>>0,o=((g>>>28|v<<4)^(v>>>2|g<<30)^(v>>>7|g<<25))>>>0,c=((g<<4|v>>>28)^(v<<30|g>>>2)^(v<<25|g>>>7))>>>0,d=(g&m|E&(g^m))>>>0,y=(v&C|S&(v^C))>>>0,D=L+p+h+l[_][1]+t[_][1],n=R+u+f+l[_][0]+t[_][0]+(D/4294967296>>>0)>>>0,a=D>>>0,D=c+y,i=o+d+(D/4294967296>>>0)>>>0,s=D>>>0,R=N,L=w,N=B,w=k,B=I,k=A,D=b+a,I=T+n+(D/4294967296>>>0)>>>0,A=D>>>0,T=E,b=S,E=m,S=C,m=g,C=v,D=a+s,g=n+i+(D/4294967296>>>0)>>>0,v=D>>>0;D=e[0][1]+v,e[0][0]=e[0][0]+g+(D/4294967296>>>0)>>>0,e[0][1]=D>>>0,D=e[1][1]+C,e[1][0]=e[1][0]+m+(D/4294967296>>>0)>>>0,e[1][1]=D>>>0,D=e[2][1]+S,e[2][0]=e[2][0]+E+(D/4294967296>>>0)>>>0,e[2][1]=D>>>0,D=e[3][1]+b,e[3][0]=e[3][0]+T+(D/4294967296>>>0)>>>0,e[3][1]=D>>>0,D=e[4][1]+A,e[4][0]=e[4][0]+I+(D/4294967296>>>0)>>>0,e[4][1]=D>>>0,D=e[5][1]+k,e[5][0]=e[5][0]+B+(D/4294967296>>>0)>>>0,e[5][1]=D>>>0,D=e[6][1]+w,e[6][0]=e[6][0]+N+(D/4294967296>>>0)>>>0,e[6][1]=D>>>0,D=e[7][1]+L,e[7][0]=e[7][0]+R+(D/4294967296>>>0)>>>0,e[7][1]=D>>>0,K-=128}}var i=r(0);r(4),r(1);var s=e.exports=i.sha512=i.sha512||{};i.md.sha512=i.md.algorithms.sha512=s;var o=i.sha384=i.sha512.sha384=i.sha512.sha384||{};o.create=function(){return s.create("SHA-384")},i.md.sha384=i.md.algorithms.sha384=o,i.sha512.sha256=i.sha512.sha256||{create:function(){return s.create("SHA-512/256")}},i.md["sha512/256"]=i.md.algorithms["sha512/256"]=i.sha512.sha256,i.sha512.sha224=i.sha512.sha224||{create:function(){return s.create("SHA-512/224")}},i.md["sha512/224"]=i.md.algorithms["sha512/224"]=i.sha512.sha224,s.create=function(e){if(u||n(),void 0===e&&(e="SHA-512"),!(e in p))throw new Error("Invalid SHA-512 algorithm: "+e);for(var t=p[e],r=null,s=i.util.createBuffer(),o=new Array(80),l=0;l<80;++l)o[l]=new Array(2);var f=64;switch(e){case"SHA-384":f=48;break;case"SHA-512/256":f=32;break;case"SHA-512/224":f=28}var h={algorithm:e.replace("-","").toLowerCase(),blockLength:128,digestLength:f,messageLength:0,fullMessageLength:null,messageLengthSize:16};return h.start=function(){h.messageLength=0,h.fullMessageLength=h.messageLength128=[];for(var e=h.messageLengthSize/4,n=0;n<e;++n)h.fullMessageLength.push(0);s=i.util.createBuffer(),r=new Array(t.length);for(var n=0;n<t.length;++n)r[n]=t[n].slice(0);return h},h.start(),h.update=function(e,t){"utf8"===t&&(e=i.util.encodeUtf8(e));var n=e.length;h.messageLength+=n,n=[n/4294967296>>>0,n>>>0];for(var c=h.fullMessageLength.length-1;c>=0;--c)h.fullMessageLength[c]+=n[1],n[1]=n[0]+(h.fullMessageLength[c]/4294967296>>>0),h.fullMessageLength[c]=h.fullMessageLength[c]>>>0,n[0]=n[1]/4294967296>>>0;return s.putBytes(e),a(r,o,s),(s.read>2048||0===s.length())&&s.compact(),h},h.digest=function(){var t=i.util.createBuffer();t.putBytes(s.bytes());var n=h.fullMessageLength[h.fullMessageLength.length-1]+h.messageLengthSize,u=n&h.blockLength-1;t.putBytes(c.substr(0,h.blockLength-u));for(var l,p,f=8*h.fullMessageLength[0],d=0;d<h.fullMessageLength.length-1;++d)l=8*h.fullMessageLength[d+1],p=l/4294967296>>>0,f+=p,t.putInt32(f>>>0),f=l>>>0;t.putInt32(f);for(var y=new Array(r.length),d=0;d<r.length;++d)y[d]=r[d].slice(0);a(y,o,t);var g,v=i.util.createBuffer();g="SHA-512"===e?y.length:"SHA-384"===e?y.length-2:y.length-4;for(var d=0;d<g;++d)v.putInt32(y[d][0]),d===g-1&&"SHA-512/224"===e||v.putInt32(y[d][1]);return v},h};var c=null,u=!1,l=null,p=null},function(e,t,r){var n=r(0);r(1),e.exports=n.log=n.log||{},n.log.levels=["none","error","warning","info","debug","verbose","max"];var a={},i=[],s=null;n.log.LEVEL_LOCKED=2,n.log.NO_LEVEL_CHECK=4,n.log.INTERPOLATE=8;for(var o=0;o<n.log.levels.length;++o){var c=n.log.levels[o];a[c]={index:o,name:c.toUpperCase()}}n.log.logMessage=function(e){for(var t=a[e.level].index,r=0;r<i.length;++r){var s=i[r];if(s.flags&n.log.NO_LEVEL_CHECK)s.f(e);else{t<=a[s.level].index&&s.f(s,e)}}},n.log.prepareStandard=function(e){"standard"in e||(e.standard=a[e.level].name+" ["+e.category+"] "+e.message)},n.log.prepareFull=function(e){if(!("full"in e)){var t=[e.message];t=t.concat([]||e.arguments),e.full=n.util.format.apply(this,t)}},n.log.prepareStandardFull=function(e){"standardFull"in e||(n.log.prepareStandard(e),e.standardFull=e.standard)};for(var u=["error","warning","info","debug","verbose"],o=0;o<u.length;++o)!function(e){n.log[e]=function(t,r){var a=Array.prototype.slice.call(arguments).slice(2),i={timestamp:new Date,level:e,category:t,message:r,arguments:a};n.log.logMessage(i)}}(u[o]);if(n.log.makeLogger=function(e){var t={flags:0,f:e};return n.log.setLevel(t,"none"),t},n.log.setLevel=function(e,t){var r=!1;if(e&&!(e.flags&n.log.LEVEL_LOCKED))for(var a=0;a<n.log.levels.length;++a){var i=n.log.levels[a];if(t==i){e.level=t,r=!0;break}}return r},n.log.lock=function(e,t){void 0===t||t?e.flags|=n.log.LEVEL_LOCKED:e.flags&=~n.log.LEVEL_LOCKED},n.log.addLogger=function(e){i.push(e)},"undefined"!=typeof console&&"log"in console){var l;if(console.error&&console.warn&&console.info&&console.debug){var p={error:console.error,warning:console.warn,info:console.info,debug:console.debug,verbose:console.debug},f=function(e,t){n.log.prepareStandard(t);var r=p[t.level],a=[t.standard];a=a.concat(t.arguments.slice()),r.apply(console,a)};l=n.log.makeLogger(f)}else{var f=function(e,t){n.log.prepareStandardFull(t),console.log(t.standardFull)};l=n.log.makeLogger(f)}n.log.setLevel(l,"debug"),n.log.addLogger(l),s=l}else console={log:function(){}};if(null!==s){var h=n.util.getQueryVariables();if("console.level"in h&&n.log.setLevel(s,h["console.level"].slice(-1)[0]),"console.lock"in h){"true"==h["console.lock"].slice(-1)[0]&&n.log.lock(s)}}n.log.consoleLogger=s},function(e,t,r){var n=r(0);r(1);var a=e.exports=n.net=n.net||{};a.socketPools={},a.createSocketPool=function(e){e.msie=e.msie||!1;var t=e.flashId,r=document.getElementById(t);r.init({marshallExceptions:!e.msie});var i={id:t,flashApi:r,sockets:{},policyPort:e.policyPort||0,policyUrl:e.policyUrl||null};a.socketPools[t]=i,!0===e.msie?i.handler=function(e){if(e.id in i.sockets){var t;switch(e.type){case"connect":t="connected";break;case"close":t="closed";break;case"socketData":t="data";break;default:t="error"}setTimeout(function(){i.sockets[e.id][t](e)},0)}}:i.handler=function(e){if(e.id in i.sockets){var t;switch(e.type){case"connect":t="connected";break;case"close":t="closed";break;case"socketData":t="data";break;default:t="error"}i.sockets[e.id][t](e)}};var s="forge.net.socketPools['"+t+"'].handler";return r.subscribe("connect",s),r.subscribe("close",s),r.subscribe("socketData",s),r.subscribe("ioError",s),r.subscribe("securityError",s),i.destroy=function(){delete a.socketPools[e.flashId];for(var t in i.sockets)i.sockets[t].destroy();i.sockets={},r.cleanup()},i.createSocket=function(e){e=e||{};var t=r.create(),a={id:t,connected:e.connected||function(e){},closed:e.closed||function(e){},data:e.data||function(e){},error:e.error||function(e){}};return a.destroy=function(){r.destroy(t),delete i.sockets[t]},a.connect=function(e){var n=e.policyUrl||null,a=0;null===n&&0!==e.policyPort&&(a=e.policyPort||i.policyPort),r.connect(t,e.host,e.port,a,n)},a.close=function(){r.close(t),a.closed({id:a.id,type:"close",bytesAvailable:0})},a.isConnected=function(){return r.isConnected(t)},a.send=function(e){return r.send(t,n.util.encode64(e))},a.receive=function(e){var a=r.receive(t,e).rval;return null===a?null:n.util.decode64(a)},a.bytesAvailable=function(){return r.getBytesAvailable(t)},i.sockets[t]=a,a},i},a.destroySocketPool=function(e){if(e.flashId in a.socketPools){a.socketPools[e.flashId].destroy()}},a.createSocket=function(e){var t=null;if(e.flashId in a.socketPools){t=a.socketPools[e.flashId].createSocket(e)}return t}},function(e,t,r){var n=r(0);r(20),r(10),r(1);var a=e.exports=n.http=n.http||{};n.debug&&n.debug.set("forge.http","clients",[]);var i=function(e){return e.toLowerCase().replace(/(^.)|(-.)/g,function(e){return e.toUpperCase()})},s=function(e){return"forge.http."+e.url.scheme+"."+e.url.host+"."+e.url.port},o=function(e){if(e.persistCookies)try{var t=n.util.getItem(e.socketPool.flashApi,s(e),"cookies");e.cookies=t||{}}catch(e){}},c=function(e){if(e.persistCookies)try{n.util.setItem(e.socketPool.flashApi,s(e),"cookies",e.cookies)}catch(e){}o(e)},u=function(e){if(e.persistCookies)try{n.util.clearItems(e.socketPool.flashApi,s(e))}catch(e){}},l=function(e,t){t.isConnected()?(t.options.request.connectTime=+new Date,t.connected({type:"connect",id:t.id})):(t.options.request.connectTime=+new Date,t.connect({host:e.url.host,port:e.url.port,policyPort:e.policyPort,policyUrl:e.policyUrl}))},p=function(e,t){t.buffer.clear();for(var r=null;null===r&&e.requests.length>0;)r=e.requests.shift(),r.request.aborted&&(r=null);null===r?(null!==t.options&&(t.options=null),e.idle.push(t)):(t.retries=1,t.options=r,l(e,t))},f=function(e,t,r){t.options=null,t.connected=function(r){if(null===t.options)p(e,t);else{var n=t.options.request;if(n.connectTime=+new Date-n.connectTime,r.socket=t,t.options.connected(r),n.aborted)t.close();else{var a=n.toString();n.body&&(a+=n.body),n.time=+new Date,t.send(a),n.time=+new Date-n.time,t.options.response.time=+new Date,t.sending=!0}}},t.closed=function(r){if(t.sending)t.sending=!1,t.retries>0?(--t.retries,l(e,t)):t.error({id:t.id,type:"ioError",message:"Connection closed during send. Broken pipe.",bytesAvailable:0});else{var n=t.options.response;n.readBodyUntilClose&&(n.time=+new Date-n.time,n.bodyReceived=!0,t.options.bodyReady({request:t.options.request,response:n,socket:t})),t.options.closed(r),p(e,t)}},t.data=function(r){if(t.sending=!1,t.options.request.aborted)t.close();else{var n=t.options.response,a=t.receive(r.bytesAvailable);null!==a&&(t.buffer.putBytes(a),n.headerReceived||(n.readHeader(t.buffer),n.headerReceived&&t.options.headerReady({request:t.options.request,response:n,socket:t})),n.headerReceived&&!n.bodyReceived&&n.readBody(t.buffer),n.bodyReceived)&&(t.options.bodyReady({request:t.options.request,response:n,socket:t}),-1!=(n.getField("Connection")||"").indexOf("close")||"HTTP/1.0"===n.version&&null===n.getField("Keep-Alive")?t.close():p(e,t))}},t.error=function(e){t.options.error({type:e.type,message:e.message,request:t.options.request,response:t.options.response,socket:t}),t.close()},r?(t=n.tls.wrapSocket({sessionId:null,sessionCache:{},caStore:r.caStore,cipherSuites:r.cipherSuites,socket:t,virtualHost:r.virtualHost,verify:r.verify,getCertificate:r.getCertificate,getPrivateKey:r.getPrivateKey,getSignature:r.getSignature,deflate:r.deflate||null,inflate:r.inflate||null}),t.options=null,t.buffer=n.util.createBuffer(),e.sockets.push(t),r.prime?t.connect({host:e.url.host,port:e.url.port,policyPort:e.policyPort,policyUrl:e.policyUrl}):e.idle.push(t)):(t.buffer=n.util.createBuffer(),e.sockets.push(t),e.idle.push(t))},h=function(e){var t=!1;if(-1!==e.maxAge){var r=m(new Date);e.created+e.maxAge<=r&&(t=!0)}return t},d=function(e,t){var r=[],n=(e.url,e.cookies);for(var a in n){var i=n[a];for(var s in i){var o=i[s];h(o)?r.push(o):0===t.path.indexOf(o.path)&&t.addCookie(o)}}for(var c=0;c<r.length;++c){var o=r[c];e.removeCookie(o.name,o.path)}},y=function(e,t){for(var r=t.getCookies(),n=0;n<r.length;++n)try{e.setCookie(r[n])}catch(e){}};a.createClient=function(e){var t=null;e.caCerts&&(t=n.pki.createCaStore(e.caCerts)),e.url=e.url||window.location.protocol+"//"+window.location.host;var r=a.parseUrl(e.url);if(!r){var i=new Error("Invalid url.");throw i.details={url:e.url},i}e.connections=e.connections||1;var s=e.socketPool,p={url:r,socketPool:s,policyPort:e.policyPort,policyUrl:e.policyUrl,requests:[],sockets:[],idle:[],secure:"https"===r.scheme,cookies:{},persistCookies:void 0===e.persistCookies||e.persistCookies};n.debug&&n.debug.get("forge.http","clients").push(p),o(p);var h=function(e,t,r,n){if(0===r&&!0===t){var a=n[r].subject.getField("CN");null!==a&&p.url.host===a.value||(t={message:"Certificate common name does not match url host."})}return t},g=null;p.secure&&(g={caStore:t,cipherSuites:e.cipherSuites||null,virtualHost:e.virtualHost||r.host,verify:e.verify||h,getCertificate:e.getCertificate||null,getPrivateKey:e.getPrivateKey||null,getSignature:e.getSignature||null,prime:e.primeTlsSockets||!1},null!==s.flashApi&&(g.deflate=function(e){return n.util.deflate(s.flashApi,e,!0)},g.inflate=function(e){return n.util.inflate(s.flashApi,e,!0)}));for(var v=0;v<e.connections;++v)f(p,s.createSocket(),g);return p.send=function(e){null===e.request.getField("Host")&&e.request.setField("Host",p.url.fullHost);var t={};if(t.request=e.request,t.connected=e.connected||function(){},t.closed=e.close||function(){},t.headerReady=function(t){y(p,t.response),e.headerReady&&e.headerReady(t)},t.bodyReady=e.bodyReady||function(){},t.error=e.error||function(){},t.response=a.createResponse(),t.response.time=0,t.response.flashApi=p.socketPool.flashApi,t.request.flashApi=p.socketPool.flashApi,t.request.abort=function(){t.request.aborted=!0,t.connected=function(){},t.closed=function(){},t.headerReady=function(){},t.bodyReady=function(){},t.error=function(){}},d(p,t.request),0===p.idle.length)p.requests.push(t);else{for(var r=null,n=p.idle.length,i=0;null===r&&i<n;++i)r=p.idle[i],r.isConnected()?p.idle.splice(i,1):r=null;null===r&&(r=p.idle.pop()),r.options=t,l(p,r)}},p.destroy=function(){p.requests=[];for(var e=0;e<p.sockets.length;++e)p.sockets[e].close(),p.sockets[e].destroy();p.socketPool=null,p.sockets=[],p.idle=[]},p.setCookie=function(e){var t;if(void 0!==e.name)if(null===e.value||void 0===e.value||""===e.value)t=p.removeCookie(e.name,e.path);else{if(e.comment=e.comment||"",e.maxAge=e.maxAge||0,e.secure=void 0===e.secure||e.secure,e.httpOnly=e.httpOnly||!0,e.path=e.path||"/",e.domain=e.domain||null,e.version=e.version||null,e.created=m(new Date),e.secure!==p.secure){var r=new Error("Http client url scheme is incompatible with cookie secure flag.");throw r.url=p.url,r.cookie=e,r}if(!a.withinCookieDomain(p.url,e)){var r=new Error("Http client url scheme is incompatible with cookie secure flag.");throw r.url=p.url,r.cookie=e,r}e.name in p.cookies||(p.cookies[e.name]={}),p.cookies[e.name][e.path]=e,t=!0,c(p)}return t},p.getCookie=function(e,t){var r=null;if(e in p.cookies){var n=p.cookies[e];if(t)t in n&&(r=n[t]);else for(var a in n){r=n[a];break}}return r},p.removeCookie=function(e,t){var r=!1;if(e in p.cookies)if(t){var n=p.cookies[e];if(t in n){r=!0,delete p.cookies[e][t];var a=!0;for(var i in p.cookies[e]){a=!1;break}a&&delete p.cookies[e]}}else r=!0,delete p.cookies[e];return r&&c(p),r},p.clearCookies=function(){p.cookies={},u(p)},n.log&&n.log.debug("forge.http","created client",e),p};var g=function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")},v=function(){var e={fields:{},setField:function(t,r){e.fields[i(t)]=[g(""+r)]},appendField:function(t,r){t=i(t),t in e.fields||(e.fields[t]=[]),e.fields[t].push(g(""+r))},getField:function(t,r){var n=null;return t=i(t),t in e.fields&&(r=r||0,n=e.fields[t][r]),n}};return e},m=function(e){e.getTimezoneOffset();return Math.floor(+new Date/1e3)};a.createRequest=function(e){e=e||{};var t=v();t.version=e.version||"HTTP/1.1",t.method=e.method||null,t.path=e.path||null,t.body=e.body||null,t.bodyDeflated=!1,t.flashApi=null;var r=e.headers||[];n.util.isArray(r)||(r=[r]);for(var a=0;a<r.length;++a)for(var i in r[a])t.appendField(i,r[a][i]);return t.addCookie=function(e){var r="",n=t.getField("Cookie");null!==n&&(r=n+"; ");m(new Date);r+=e.name+"="+e.value,t.setField("Cookie",r)},t.toString=function(){null===t.getField("User-Agent")&&t.setField("User-Agent","forge.http 1.0"),null===t.getField("Accept")&&t.setField("Accept","*/*"),null===t.getField("Connection")&&(t.setField("Connection","keep-alive"),t.setField("Keep-Alive","115")),null!==t.flashApi&&null===t.getField("Accept-Encoding")&&t.setField("Accept-Encoding","deflate"),null!==t.flashApi&&null!==t.body&&null===t.getField("Content-Encoding")&&!t.bodyDeflated&&t.body.length>100?(t.body=n.util.deflate(t.flashApi,t.body),t.bodyDeflated=!0,t.setField("Content-Encoding","deflate"),t.setField("Content-Length",t.body.length)):null!==t.body&&t.setField("Content-Length",t.body.length);var e=t.method.toUpperCase()+" "+t.path+" "+t.version+"\r\n";for(var r in t.fields)for(var a=t.fields[r],i=0;i<a.length;++i)e+=r+": "+a[i]+"\r\n";return e+="\r\n"},t},a.createResponse=function(){var e=!0,t=0,r=!1,a=v();a.version=null,a.code=0,a.message=null,a.body=null,a.headerReceived=!1,a.bodyReceived=!1,a.flashApi=null;var i=function(e){var t=null,r=e.data.indexOf("\r\n",e.read);return-1!=r&&(t=e.getBytes(r-e.read),e.getBytes(2)),t},s=function(e){var t=e.indexOf(":"),r=e.substring(0,t++);a.appendField(r,t<e.length?e.substring(t):"")};a.readHeader=function(t){for(var r="";!a.headerReceived&&null!==r;)if(null!==(r=i(t)))if(e){e=!1;var n=r.split(" ");if(!(n.length>=3)){var o=new Error("Invalid http response header.");throw o.details={line:r},o}a.version=n[0],a.code=parseInt(n[1],10),a.message=n.slice(2).join(" ")}else 0===r.length?a.headerReceived=!0:s(r);return a.headerReceived};var o=function(e){for(var n="";null!==n&&e.length()>0;)if(t>0){if(t+2>e.length())break;a.body+=e.getBytes(t),e.getBytes(2),t=0}else if(r)for(n=i(e);null!==n;)n.length>0?(s(n),n=i(e)):(a.bodyReceived=!0,n=null);else null!==(n=i(e))&&(t=parseInt(n.split(";",1)[0],16),r=0===t);return a.bodyReceived};return a.readBody=function(e){var t=a.getField("Content-Length"),r=a.getField("Transfer-Encoding");if(null!==t&&(t=parseInt(t)),null!==t&&t>=0)a.body=a.body||"",a.body+=e.getBytes(t),a.bodyReceived=a.body.length===t;else if(null!==r){if(-1==r.indexOf("chunked")){var i=new Error("Unknown Transfer-Encoding.");throw i.details={transferEncoding:r},i}a.body=a.body||"",o(e)}else null!==t&&t<0||null===t&&null!==a.getField("Content-Type")?(a.body=a.body||"",a.body+=e.getBytes(),a.readBodyUntilClose=!0):(a.body=null,a.bodyReceived=!0);return a.bodyReceived&&(a.time=+new Date-a.time),null!==a.flashApi&&a.bodyReceived&&null!==a.body&&"deflate"===a.getField("Content-Encoding")&&(a.body=n.util.inflate(a.flashApi,a.body)),a.bodyReceived},a.getCookies=function(){var e=[];if("Set-Cookie"in a.fields)for(var t=a.fields["Set-Cookie"],r=+new Date/1e3,n=/\s*([^=]*)=?([^;]*)(;|$)/g,i=0;i<t.length;++i){var s,o=t[i];n.lastIndex=0;var c=!0,u={};do{if(null!==(s=n.exec(o))){var l=g(s[1]),p=g(s[2]);if(c)u.name=l,u.value=p,c=!1;else switch(l=l.toLowerCase()){case"expires":p=p.replace(/-/g," ");var f=Date.parse(p)/1e3;u.maxAge=Math.max(0,f-r);break;case"max-age":u.maxAge=parseInt(p,10);break;case"secure":u.secure=!0;break;case"httponly":u.httpOnly=!0;break;default:""!==l&&(u[l]=p)}}}while(null!==s&&""!==s[0]);e.push(u)}return e},a.toString=function(){var e=a.version+" "+a.code+" "+a.message+"\r\n";for(var t in a.fields)for(var r=a.fields[t],n=0;n<r.length;++n)e+=t+": "+r[n]+"\r\n";return e+="\r\n"},a},a.parseUrl=n.util.parseUrl,a.withinCookieDomain=function(e,t){var r=!1,n=null===t||"string"==typeof t?t:t.domain;if(null===n)r=!0;else if("."===n.charAt(0)){"string"==typeof e&&(e=a.parseUrl(e));var i="."+e.host,s=i.lastIndexOf(n);-1!==s&&s+n.length===i.length&&(r=!0)}return r}},function(e,t,r){e.exports=r(37)},function(e,t,r){e.exports=r(0),r(38),r(50),r(34),r(51),r(35),r(52)},function(e,t,r){e.exports=r(0),r(5),r(41),r(3),r(14),r(20),r(11),r(43),r(8),r(45),r(33),r(46),r(31),r(16),r(7),r(27),r(29),r(47),r(22),r(28),r(25),r(19),r(2),r(26),r(48),r(49),r(10),r(1)},function(e,t){var r;r=function(){return this}();try{r=r||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(r=window)}e.exports=r},function(e,t){function r(e,t){var r=0,n=t.length,a=t.charAt(0),i=[0];for(r=0;r<e.length();++r){for(var s=0,o=e.at(r);s<i.length;++s)o+=i[s]<<8,i[s]=o%n,o=o/n|0;for(;o>0;)i.push(o%n),o=o/n|0}var c="";for(r=0;0===e.at(r)&&r<e.length()-1;++r)c+=a;for(r=i.length-1;r>=0;--r)c+=t[i[r]];return c}var n={};e.exports=n;var a={};n.encode=function(e,t,n){if("string"!=typeof t)throw new TypeError('"alphabet" must be a string.');if(void 0!==n&&"number"!=typeof n)throw new TypeError('"maxline" must be a number.');var a="";if(e instanceof Uint8Array){var i=0,s=t.length,o=t.charAt(0),c=[0];for(i=0;i<e.length;++i){for(var u=0,l=e[i];u<c.length;++u)l+=c[u]<<8,c[u]=l%s,l=l/s|0;for(;l>0;)c.push(l%s),l=l/s|0}for(i=0;0===e[i]&&i<e.length-1;++i)a+=o;for(i=c.length-1;i>=0;--i)a+=t[c[i]]}else a=r(e,t);if(n){var p=new RegExp(".{1,"+n+"}","g");a=a.match(p).join("\r\n")}return a},n.decode=function(e,t){if("string"!=typeof e)throw new TypeError('"input" must be a string.');if("string"!=typeof t)throw new TypeError('"alphabet" must be a string.');var r=a[t];if(!r){r=a[t]=[];for(var n=0;n<t.length;++n)r[t.charCodeAt(n)]=n}e=e.replace(/\s/g,"");for(var i=t.length,s=t.charAt(0),o=[0],n=0;n<e.length;n++){var c=r[e.charCodeAt(n)];if(void 0===c)return;for(var u=0,l=c;u<o.length;++u)l+=o[u]*i,o[u]=255&l,l>>=8;for(;l>0;)o.push(255&l),l>>=8}for(var p=0;e[p]===s&&p<e.length-1;++p)o.push(0);return"undefined"!=typeof Buffer?Buffer.from(o.reverse()):new Uint8Array(o.reverse())}},function(e,t,r){function n(e,t,r){var n=t.entity===u.tls.ConnectionEnd.client;e.read.cipherState={init:!1,cipher:u.cipher.createDecipher("AES-CBC",n?r.keys.server_write_key:r.keys.client_write_key),iv:n?r.keys.server_write_IV:r.keys.client_write_IV},e.write.cipherState={init:!1,cipher:u.cipher.createCipher("AES-CBC",n?r.keys.client_write_key:r.keys.server_write_key),iv:n?r.keys.client_write_IV:r.keys.server_write_IV},e.read.cipherFunction=o,e.write.cipherFunction=a,e.read.macLength=e.write.macLength=r.mac_length,e.read.macFunction=e.write.macFunction=l.hmac_sha1}function a(e,t){var r=!1,n=t.macFunction(t.macKey,t.sequenceNumber,e);e.fragment.putBytes(n),t.updateSequenceNumber();var a;a=e.version.minor===l.Versions.TLS_1_0.minor?t.cipherState.init?null:t.cipherState.iv:u.random.getBytesSync(16),t.cipherState.init=!0;var s=t.cipherState.cipher;return s.start({iv:a}),e.version.minor>=l.Versions.TLS_1_1.minor&&s.output.putBytes(a),s.update(e.fragment),s.finish(i)&&(e.fragment=s.output,e.length=e.fragment.length(),r=!0),r}function i(e,t,r){if(!r){var n=e-t.length()%e;t.fillWithByte(n-1,n)}return!0}function s(e,t,r){var n=!0;if(r){for(var a=t.length(),i=t.last(),s=a-1-i;s<a-1;++s)n=n&&t.at(s)==i;n&&t.truncate(i+1)}return n}function o(e,t){var r,n=!1;r=e.version.minor===l.Versions.TLS_1_0.minor?t.cipherState.init?null:t.cipherState.iv:e.fragment.getBytes(16),t.cipherState.init=!0;var a=t.cipherState.cipher;a.start({iv:r}),a.update(e.fragment),n=a.finish(s);var i=t.macLength,o=u.random.getBytesSync(i),p=a.output.length();p>=i?(e.fragment=a.output.getBytes(p-i),o=a.output.getBytes(i)):e.fragment=a.output.getBytes(),e.fragment=u.util.createBuffer(e.fragment),e.length=e.fragment.length();var f=t.macFunction(t.macKey,t.sequenceNumber,e);return t.updateSequenceNumber(),n=c(t.macKey,o,f)&&n}function c(e,t,r){var n=u.hmac.create();return n.start("SHA1",e),n.update(t),t=n.digest().getBytes(),n.start(null,null),n.update(r),r=n.digest().getBytes(),t===r}var u=r(0);r(5),r(10);var l=e.exports=u.tls;l.CipherSuites.TLS_RSA_WITH_AES_128_CBC_SHA={id:[0,47],name:"TLS_RSA_WITH_AES_128_CBC_SHA",initSecurityParameters:function(e){e.bulk_cipher_algorithm=l.BulkCipherAlgorithm.aes,e.cipher_type=l.CipherType.block,e.enc_key_length=16,e.block_length=16,e.fixed_iv_length=16,e.record_iv_length=16,e.mac_algorithm=l.MACAlgorithm.hmac_sha1,e.mac_length=20,e.mac_key_length=20},initConnectionState:n},l.CipherSuites.TLS_RSA_WITH_AES_256_CBC_SHA={id:[0,53],name:"TLS_RSA_WITH_AES_256_CBC_SHA",initSecurityParameters:function(e){e.bulk_cipher_algorithm=l.BulkCipherAlgorithm.aes,e.cipher_type=l.CipherType.block,e.enc_key_length=32,e.block_length=16,e.fixed_iv_length=16,e.record_iv_length=16,e.mac_algorithm=l.MACAlgorithm.hmac_sha1,e.mac_length=20,e.mac_key_length=20},initConnectionState:n}},function(e,t,r){var n=r(0);r(31),e.exports=n.mgf=n.mgf||{},n.mgf.mgf1=n.mgf1},function(e,t,r){function n(e){var t=e.message;if(t instanceof Uint8Array||t instanceof x)return t;var r=e.encoding;if(void 0===t){if(!e.md)throw new TypeError('"options.message" or "options.md" not specified.');t=e.md.digest().getBytes(),r="binary"}if("string"==typeof t&&!r)throw new TypeError('"options.encoding" must be "binary" or "utf8".');if("string"==typeof t){if("undefined"!=typeof Buffer)return Buffer.from(t,r);t=new V(t,r)}else if(!(t instanceof V))throw new TypeError('"options.message" must be a node.js Buffer, a Uint8Array, a forge ByteBuffer, or a string with "options.encoding" specifying its encoding.');for(var n=new x(t.length()),a=0;a<n.length;++a)n[a]=t.at(a);return n}function a(e,t){var r=_.md.sha512.create(),n=new V(e);r.update(n.getBytes(t),"binary");var a=r.digest().getBytes();if("undefined"!=typeof Buffer)return Buffer.from(a,"binary");for(var i=new x(K.constants.HASH_BYTE_LENGTH),s=0;s<64;++s)i[s]=a.charCodeAt(s);return i}function i(e,t){var r,n=[k(),k(),k(),k()],i=a(t,32);for(i[0]&=248,i[31]&=127,i[31]|=64,T(n,i),f(e,n),r=0;r<32;++r)t[r+32]=e[r];return 0}function s(e,t,r,n){var i,s,o=new Float64Array(64),l=[k(),k(),k(),k()],p=a(n,32);p[0]&=248,p[31]&=127,p[31]|=64;var h=r+64;for(i=0;i<r;++i)e[64+i]=t[i];for(i=0;i<32;++i)e[32+i]=p[32+i];var d=a(e.subarray(32),r+32);for(u(d),T(l,d),f(e,l),i=32;i<64;++i)e[i]=n[i];var y=a(e,r+64);for(u(y),i=32;i<64;++i)o[i]=0;for(i=0;i<32;++i)o[i]=d[i];for(i=0;i<32;++i)for(s=0;s<32;s++)o[i+s]+=y[i]*p[s];return c(e.subarray(32),o),h}function o(e,t,r,n){var i,s=new x(32),o=[k(),k(),k(),k()],c=[k(),k(),k(),k()];if(-1,r<64)return-1;if(d(c,n))return-1;for(i=0;i<r;++i)e[i]=t[i];for(i=0;i<32;++i)e[i+32]=n[i];var p=a(e,r);if(u(p),S(o,c,p),T(c,t.subarray(32)),l(o,c),f(s,o),r-=64,m(t,0,s,0)){for(i=0;i<r;++i)e[i]=0;return-1}for(i=0;i<r;++i)e[i]=t[i+64];return r}function c(e,t){var r,n,a,i;for(n=63;n>=32;--n){for(r=0,a=n-32,i=n-12;a<i;++a)t[a]+=r-16*t[n]*Q[a-(n-32)],r=t[a]+128>>8,t[a]-=256*r;t[a]+=r,t[n]=0}for(r=0,a=0;a<32;++a)t[a]+=r-(t[31]>>4)*Q[a],r=t[a]>>8,t[a]&=255;for(a=0;a<32;++a)t[a]-=r*Q[a];for(n=0;n<32;++n)t[n+1]+=t[n]>>8,e[n]=255&t[n]}function u(e){for(var t=new Float64Array(64),r=0;r<64;++r)t[r]=e[r],e[r]=0;c(e,t)}function l(e,t){var r=k(),n=k(),a=k(),i=k(),s=k(),o=k(),c=k(),u=k(),l=k();w(r,e[1],e[0]),w(l,t[1],t[0]),L(r,r,l),N(n,e[0],e[1]),N(l,t[0],t[1]),L(n,n,l),L(a,e[3],t[3]),L(a,a,H),L(i,e[2],t[2]),N(i,i,i),w(s,n,r),w(o,i,a),N(c,i,a),N(u,n,r),L(e[0],s,o),L(e[1],u,c),L(e[2],c,o),L(e[3],s,u)}function p(e,t,r){for(var n=0;n<4;++n)B(e[n],t[n],r)}function f(e,t){var r=k(),n=k(),a=k();I(a,t[2]),L(r,t[0],a),L(n,t[1],a),h(e,n),e[31]^=E(r)<<7}function h(e,t){var r,n,a,i=k(),s=k();for(r=0;r<16;++r)s[r]=t[r];for(A(s),A(s),A(s),n=0;n<2;++n){for(i[0]=s[0]-65517,r=1;r<15;++r)i[r]=s[r]-65535-(i[r-1]>>16&1),i[r-1]&=65535;i[15]=s[15]-32767-(i[14]>>16&1),a=i[15]>>16&1,i[14]&=65535,B(s,i,1-a)}for(r=0;r<16;r++)e[2*r]=255&s[r],e[2*r+1]=s[r]>>8}function d(e,t){var r=k(),n=k(),a=k(),i=k(),s=k(),o=k(),c=k();return b(e[2],F),y(e[1],t),R(a,e[1]),L(i,a,q),w(a,a,e[2]),N(i,e[2],i),R(s,i),R(o,s),L(c,o,s),L(r,c,a),L(r,r,i),g(r,r),L(r,r,a),L(r,r,i),L(r,r,i),L(e[0],r,i),R(n,e[0]),L(n,n,i),v(n,a)&&L(e[0],e[0],z),R(n,e[0]),L(n,n,i),v(n,a)?-1:(E(e[0])===t[31]>>7&&w(e[0],M,e[0]),L(e[3],e[0],e[1]),0)}function y(e,t){var r;for(r=0;r<16;++r)e[r]=t[2*r]+(t[2*r+1]<<8);e[15]&=32767}function g(e,t){var r,n=k();for(r=0;r<16;++r)n[r]=t[r];for(r=250;r>=0;--r)R(n,n),1!==r&&L(n,n,t);for(r=0;r<16;++r)e[r]=n[r]}function v(e,t){var r=new x(32),n=new x(32);return h(r,e),h(n,t),m(r,0,n,0)}function m(e,t,r,n){return C(e,t,r,n,32)}function C(e,t,r,n,a){var i,s=0;for(i=0;i<a;++i)s|=e[t+i]^r[n+i];return(1&s-1>>>8)-1}function E(e){var t=new x(32);return h(t,e),1&t[0]}function S(e,t,r){var n,a;for(b(e[0],M),b(e[1],F),b(e[2],F),b(e[3],M),a=255;a>=0;--a)n=r[a/8|0]>>(7&a)&1,p(e,t,n),l(t,e),l(e,e),p(e,t,n)}function T(e,t){var r=[k(),k(),k(),k()];b(r[0],j),b(r[1],G),b(r[2],F),L(r[3],j,G),S(e,r,t)}function b(e,t){var r;for(r=0;r<16;r++)e[r]=0|t[r]}function I(e,t){var r,n=k();for(r=0;r<16;++r)n[r]=t[r];for(r=253;r>=0;--r)R(n,n),2!==r&&4!==r&&L(n,n,t);for(r=0;r<16;++r)e[r]=n[r]}function A(e){var t,r,n=1;for(t=0;t<16;++t)r=e[t]+n+65535,n=Math.floor(r/65536),e[t]=r-65536*n;e[0]+=n-1+37*(n-1)}function B(e,t,r){for(var n,a=~(r-1),i=0;i<16;++i)n=a&(e[i]^t[i]),e[i]^=n,t[i]^=n}function k(e){var t,r=new Float64Array(16);if(e)for(t=0;t<e.length;++t)r[t]=e[t];return r}function N(e,t,r){for(var n=0;n<16;++n)e[n]=t[n]+r[n]}function w(e,t,r){for(var n=0;n<16;++n)e[n]=t[n]-r[n]}function R(e,t){L(e,t,t)}function L(e,t,r){var n,a,i=0,s=0,o=0,c=0,u=0,l=0,p=0,f=0,h=0,d=0,y=0,g=0,v=0,m=0,C=0,E=0,S=0,T=0,b=0,I=0,A=0,B=0,k=0,N=0,w=0,R=0,L=0,_=0,U=0,D=0,P=0,O=r[0],V=r[1],x=r[2],K=r[3],M=r[4],F=r[5],q=r[6],H=r[7],j=r[8],G=r[9],Q=r[10],z=r[11],W=r[12],X=r[13],Y=r[14],Z=r[15];n=t[0],i+=n*O,s+=n*V,o+=n*x,c+=n*K,u+=n*M,l+=n*F,p+=n*q,f+=n*H,h+=n*j,d+=n*G,y+=n*Q,g+=n*z,v+=n*W,m+=n*X,C+=n*Y,E+=n*Z,n=t[1],s+=n*O,o+=n*V,c+=n*x,u+=n*K,l+=n*M,p+=n*F,f+=n*q,h+=n*H,d+=n*j,y+=n*G,g+=n*Q,v+=n*z,m+=n*W,C+=n*X,E+=n*Y,S+=n*Z,n=t[2],o+=n*O,c+=n*V,u+=n*x,l+=n*K,p+=n*M,f+=n*F,h+=n*q,d+=n*H,y+=n*j,g+=n*G,v+=n*Q,m+=n*z,C+=n*W,E+=n*X,S+=n*Y,T+=n*Z,n=t[3],c+=n*O,u+=n*V,l+=n*x,p+=n*K,f+=n*M,h+=n*F,d+=n*q,y+=n*H,g+=n*j,v+=n*G,m+=n*Q,C+=n*z,E+=n*W,S+=n*X,T+=n*Y,b+=n*Z,n=t[4],u+=n*O,l+=n*V,p+=n*x,f+=n*K,h+=n*M,d+=n*F,y+=n*q,g+=n*H,v+=n*j,m+=n*G,C+=n*Q,E+=n*z,S+=n*W,T+=n*X,b+=n*Y,I+=n*Z,n=t[5],l+=n*O,p+=n*V,f+=n*x,h+=n*K,d+=n*M,y+=n*F,g+=n*q,v+=n*H,m+=n*j,C+=n*G,E+=n*Q,S+=n*z,T+=n*W,b+=n*X,I+=n*Y,A+=n*Z,n=t[6],p+=n*O,f+=n*V,h+=n*x,d+=n*K,y+=n*M,g+=n*F,v+=n*q,m+=n*H,C+=n*j,E+=n*G,S+=n*Q,T+=n*z,b+=n*W,I+=n*X,A+=n*Y,B+=n*Z,n=t[7],f+=n*O,h+=n*V,d+=n*x,y+=n*K,g+=n*M,v+=n*F,m+=n*q,C+=n*H,E+=n*j,S+=n*G,T+=n*Q,b+=n*z,I+=n*W,A+=n*X,B+=n*Y,k+=n*Z,n=t[8],h+=n*O,d+=n*V,y+=n*x,g+=n*K,v+=n*M,m+=n*F,C+=n*q,E+=n*H,S+=n*j,T+=n*G,b+=n*Q,I+=n*z,A+=n*W,B+=n*X,k+=n*Y,N+=n*Z,n=t[9],d+=n*O,y+=n*V,g+=n*x,v+=n*K,m+=n*M,C+=n*F,E+=n*q,S+=n*H,T+=n*j,b+=n*G,I+=n*Q,A+=n*z,B+=n*W,k+=n*X,N+=n*Y,w+=n*Z,n=t[10],y+=n*O,g+=n*V,v+=n*x,m+=n*K,C+=n*M,E+=n*F,S+=n*q,T+=n*H,b+=n*j,I+=n*G,A+=n*Q,B+=n*z,k+=n*W,N+=n*X,w+=n*Y,R+=n*Z,n=t[11],g+=n*O,v+=n*V,m+=n*x,C+=n*K,E+=n*M,S+=n*F,T+=n*q,b+=n*H,I+=n*j,A+=n*G,B+=n*Q,k+=n*z;N+=n*W,w+=n*X,R+=n*Y,L+=n*Z,n=t[12],v+=n*O,m+=n*V,C+=n*x,E+=n*K,S+=n*M,T+=n*F,b+=n*q,I+=n*H,A+=n*j,B+=n*G,k+=n*Q,N+=n*z,w+=n*W,R+=n*X,L+=n*Y,_+=n*Z,n=t[13],m+=n*O,C+=n*V,E+=n*x,S+=n*K,T+=n*M,b+=n*F,I+=n*q,A+=n*H,B+=n*j,k+=n*G,N+=n*Q,w+=n*z,R+=n*W,L+=n*X,_+=n*Y,U+=n*Z,n=t[14],C+=n*O,E+=n*V,S+=n*x,T+=n*K,b+=n*M,I+=n*F,A+=n*q,B+=n*H,k+=n*j,N+=n*G,w+=n*Q,R+=n*z,L+=n*W,_+=n*X,U+=n*Y,D+=n*Z,n=t[15],E+=n*O,S+=n*V,T+=n*x,b+=n*K,I+=n*M,A+=n*F,B+=n*q,k+=n*H,N+=n*j,w+=n*G,R+=n*Q,L+=n*z,_+=n*W,U+=n*X,D+=n*Y,P+=n*Z,i+=38*S,s+=38*T,o+=38*b,c+=38*I,u+=38*A,l+=38*B,p+=38*k,f+=38*N,h+=38*w,d+=38*R,y+=38*L,g+=38*_,v+=38*U,m+=38*D,C+=38*P,a=1,n=i+a+65535,a=Math.floor(n/65536),i=n-65536*a,n=s+a+65535,a=Math.floor(n/65536),s=n-65536*a,n=o+a+65535,a=Math.floor(n/65536),o=n-65536*a,n=c+a+65535,a=Math.floor(n/65536),c=n-65536*a,n=u+a+65535,a=Math.floor(n/65536),u=n-65536*a,n=l+a+65535,a=Math.floor(n/65536),l=n-65536*a,n=p+a+65535,a=Math.floor(n/65536),p=n-65536*a,n=f+a+65535,a=Math.floor(n/65536),f=n-65536*a,n=h+a+65535,a=Math.floor(n/65536),h=n-65536*a,n=d+a+65535,a=Math.floor(n/65536),d=n-65536*a,n=y+a+65535,a=Math.floor(n/65536),y=n-65536*a,n=g+a+65535,a=Math.floor(n/65536),g=n-65536*a,n=v+a+65535,a=Math.floor(n/65536),v=n-65536*a,n=m+a+65535,a=Math.floor(n/65536),m=n-65536*a,n=C+a+65535,a=Math.floor(n/65536),C=n-65536*a,n=E+a+65535,a=Math.floor(n/65536),E=n-65536*a,i+=a-1+37*(a-1),a=1,n=i+a+65535,a=Math.floor(n/65536),i=n-65536*a,n=s+a+65535,a=Math.floor(n/65536),s=n-65536*a,n=o+a+65535,a=Math.floor(n/65536),o=n-65536*a,n=c+a+65535,a=Math.floor(n/65536),c=n-65536*a,n=u+a+65535,a=Math.floor(n/65536),u=n-65536*a,n=l+a+65535,a=Math.floor(n/65536),l=n-65536*a,n=p+a+65535,a=Math.floor(n/65536),p=n-65536*a,n=f+a+65535,a=Math.floor(n/65536),f=n-65536*a,n=h+a+65535,a=Math.floor(n/65536),h=n-65536*a,n=d+a+65535,a=Math.floor(n/65536),d=n-65536*a,n=y+a+65535,a=Math.floor(n/65536),y=n-65536*a,n=g+a+65535,a=Math.floor(n/65536),g=n-65536*a,n=v+a+65535,a=Math.floor(n/65536),v=n-65536*a,n=m+a+65535,a=Math.floor(n/65536),m=n-65536*a,n=C+a+65535,a=Math.floor(n/65536),C=n-65536*a,n=E+a+65535,a=Math.floor(n/65536),E=n-65536*a,i+=a-1+37*(a-1),e[0]=i,e[1]=s,e[2]=o,e[3]=c,e[4]=u,e[5]=l,e[6]=p,e[7]=f,e[8]=h,e[9]=d,e[10]=y,e[11]=g,e[12]=v;e[13]=m,e[14]=C,e[15]=E}var _=r(0);r(13),r(2),r(32),r(1);var U=r(44),D=U.publicKeyValidator,P=U.privateKeyValidator;if(void 0===O)var O=_.jsbn.BigInteger;var V=_.util.ByteBuffer,x="undefined"==typeof Buffer?Uint8Array:Buffer;_.pki=_.pki||{},e.exports=_.pki.ed25519=_.ed25519=_.ed25519||{};var K=_.ed25519;K.constants={},K.constants.PUBLIC_KEY_BYTE_LENGTH=32,K.constants.PRIVATE_KEY_BYTE_LENGTH=64,K.constants.SEED_BYTE_LENGTH=32,K.constants.SIGN_BYTE_LENGTH=64,K.constants.HASH_BYTE_LENGTH=64,K.generateKeyPair=function(e){e=e||{};var t=e.seed;if(void 0===t)t=_.random.getBytesSync(K.constants.SEED_BYTE_LENGTH);else if("string"==typeof t){if(t.length!==K.constants.SEED_BYTE_LENGTH)throw new TypeError('"seed" must be '+K.constants.SEED_BYTE_LENGTH+" bytes in length.")}else if(!(t instanceof Uint8Array))throw new TypeError('"seed" must be a node.js Buffer, Uint8Array, or a binary string.');t=n({message:t,encoding:"binary"});for(var r=new x(K.constants.PUBLIC_KEY_BYTE_LENGTH),a=new x(K.constants.PRIVATE_KEY_BYTE_LENGTH),s=0;s<32;++s)a[s]=t[s];return i(r,a),{publicKey:r,privateKey:a}},K.privateKeyFromAsn1=function(e){var t={},r=[];if(!_.asn1.validate(e,P,t,r)){var a=new Error("Invalid Key.");throw a.errors=r,a}var i=_.asn1.derToOid(t.privateKeyOid),s=_.oids.EdDSA25519;if(i!==s)throw new Error('Invalid OID "'+i+'"; OID must be "'+s+'".');var o=t.privateKey;return{privateKeyBytes:n({message:_.asn1.fromDer(o).value,encoding:"binary"})}},K.publicKeyFromAsn1=function(e){var t={},r=[];if(!_.asn1.validate(e,D,t,r)){var a=new Error("Invalid Key.");throw a.errors=r,a}var i=_.asn1.derToOid(t.publicKeyOid),s=_.oids.EdDSA25519;if(i!==s)throw new Error('Invalid OID "'+i+'"; OID must be "'+s+'".');var o=t.ed25519PublicKey;if(o.length!==K.constants.PUBLIC_KEY_BYTE_LENGTH)throw new Error("Key length is invalid.");return n({message:o,encoding:"binary"})},K.publicKeyFromPrivateKey=function(e){e=e||{};var t=n({message:e.privateKey,encoding:"binary"});if(t.length!==K.constants.PRIVATE_KEY_BYTE_LENGTH)throw new TypeError('"options.privateKey" must have a byte length of '+K.constants.PRIVATE_KEY_BYTE_LENGTH);for(var r=new x(K.constants.PUBLIC_KEY_BYTE_LENGTH),a=0;a<r.length;++a)r[a]=t[32+a];return r},K.sign=function(e){e=e||{};var t=n(e),r=n({message:e.privateKey,encoding:"binary"});if(r.length===K.constants.SEED_BYTE_LENGTH){r=K.generateKeyPair({seed:r}).privateKey}else if(r.length!==K.constants.PRIVATE_KEY_BYTE_LENGTH)throw new TypeError('"options.privateKey" must have a byte length of '+K.constants.SEED_BYTE_LENGTH+" or "+K.constants.PRIVATE_KEY_BYTE_LENGTH);var a=new x(K.constants.SIGN_BYTE_LENGTH+t.length);s(a,t,t.length,r);for(var i=new x(K.constants.SIGN_BYTE_LENGTH),o=0;o<i.length;++o)i[o]=a[o];return i},K.verify=function(e){e=e||{};var t=n(e);if(void 0===e.signature)throw new TypeError('"options.signature" must be a node.js Buffer, a Uint8Array, a forge ByteBuffer, or a binary string.');var r=n({message:e.signature,encoding:"binary"});if(r.length!==K.constants.SIGN_BYTE_LENGTH)throw new TypeError('"options.signature" must have a byte length of '+K.constants.SIGN_BYTE_LENGTH);var a=n({message:e.publicKey,encoding:"binary"});if(a.length!==K.constants.PUBLIC_KEY_BYTE_LENGTH)throw new TypeError('"options.publicKey" must have a byte length of '+K.constants.PUBLIC_KEY_BYTE_LENGTH);var i,s=new x(K.constants.SIGN_BYTE_LENGTH+t.length),c=new x(K.constants.SIGN_BYTE_LENGTH+t.length);for(i=0;i<K.constants.SIGN_BYTE_LENGTH;++i)s[i]=r[i];for(i=0;i<t.length;++i)s[i+K.constants.SIGN_BYTE_LENGTH]=t[i];return o(c,s,s.length,a)>=0};var M=k(),F=k([1]),q=k([30883,4953,19914,30187,55467,16705,2637,112,59544,30585,16505,36039,65139,11119,27886,20995]),H=k([61785,9906,39828,60374,45398,33411,5274,224,53552,61171,33010,6542,64743,22239,55772,9222]),j=k([54554,36645,11616,51542,42930,38181,51040,26924,56412,64982,57905,49316,21502,52590,14035,8553]),G=k([26200,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214]),Q=new Float64Array([237,211,245,92,26,99,18,88,214,156,247,162,222,249,222,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16]),z=k([41136,18958,6951,50414,58488,44335,6150,12099,55207,15867,153,11085,57099,20417,9344,11139])},function(e,t,r){var n=r(0);r(3);var a=n.asn1;t.privateKeyValidator={name:"PrivateKeyInfo",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"PrivateKeyInfo.version",tagClass:a.Class.UNIVERSAL,type:a.Type.INTEGER,constructed:!1,capture:"privateKeyVersion"},{name:"PrivateKeyInfo.privateKeyAlgorithm",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"AlgorithmIdentifier.algorithm",tagClass:a.Class.UNIVERSAL,type:a.Type.OID,constructed:!1,capture:"privateKeyOid"}]},{name:"PrivateKeyInfo",tagClass:a.Class.UNIVERSAL,type:a.Type.OCTETSTRING,constructed:!1,capture:"privateKey"}]},t.publicKeyValidator={name:"SubjectPublicKeyInfo",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,captureAsn1:"subjectPublicKeyInfo",value:[{name:"SubjectPublicKeyInfo.AlgorithmIdentifier",tagClass:a.Class.UNIVERSAL,type:a.Type.SEQUENCE,constructed:!0,value:[{name:"AlgorithmIdentifier.algorithm",tagClass:a.Class.UNIVERSAL,type:a.Type.OID,constructed:!1,capture:"publicKeyOid"}]},{tagClass:a.Class.UNIVERSAL,type:a.Type.BITSTRING,constructed:!1,composed:!0,captureBitStringValue:"ed25519PublicKey"}]}},function(e,t,r){function n(e,t,r,n){e.generate=function(e,i){for(var s=new a.util.ByteBuffer,o=Math.ceil(i/n)+r,c=new a.util.ByteBuffer,u=r;u<o;++u){c.putInt32(u),t.start(),t.update(e+c.getBytes());var l=t.digest();s.putBytes(l.getBytes(n))}return s.truncate(s.length()-i),s.getBytes()}}var a=r(0);r(1),r(2),r(13),e.exports=a.kem=a.kem||{};var i=a.jsbn.BigInteger;a.kem.rsa={},a.kem.rsa.create=function(e,t){t=t||{};var r=t.prng||a.random,n={};return n.encrypt=function(t,n){var s,o=Math.ceil(t.n.bitLength()/8);do{s=new i(a.util.bytesToHex(r.getBytesSync(o)),16).mod(t.n)}while(s.compareTo(i.ONE)<=0);s=a.util.hexToBytes(s.toString(16));var c=o-s.length;return c>0&&(s=a.util.fillString(String.fromCharCode(0),c)+s),{encapsulation:t.encrypt(s,"NONE"),key:e.generate(s,n)}},n.decrypt=function(t,r,n){var a=t.decrypt(r,"NONE");return e.generate(a,n)},n},a.kem.kdf1=function(e,t){n(this,e,0,t||e.digestLength)},a.kem.kdf2=function(e,t){n(this,e,1,t||e.digestLength)}},function(e,t,r){e.exports=r(4),r(15),r(9),r(24),r(32)},function(e,t,r){function n(e){var t={},r=[];if(!d.validate(e,y.asn1.recipientInfoValidator,t,r)){var n=new Error("Cannot read PKCS#7 RecipientInfo. ASN.1 object is not an PKCS#7 RecipientInfo.");throw n.errors=r,n}return{version:t.version.charCodeAt(0),issuer:h.pki.RDNAttributesAsArray(t.issuer),serialNumber:h.util.createBuffer(t.serial).toHex(),encryptedContent:{algorithm:d.derToOid(t.encAlgorithm),parameter:t.encParameter.value,content:t.encKey}}}function a(e){return d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.INTEGER,!1,d.integerToDer(e.version).getBytes()),d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[h.pki.distinguishedNameToAsn1({attributes:e.issuer}),d.create(d.Class.UNIVERSAL,d.Type.INTEGER,!1,h.util.hexToBytes(e.serialNumber))]),d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.OID,!1,d.oidToDer(e.encryptedContent.algorithm).getBytes()),d.create(d.Class.UNIVERSAL,d.Type.NULL,!1,"")]),d.create(d.Class.UNIVERSAL,d.Type.OCTETSTRING,!1,e.encryptedContent.content)])}function i(e){for(var t=[],r=0;r<e.length;++r)t.push(n(e[r]));return t}function s(e){for(var t=[],r=0;r<e.length;++r)t.push(a(e[r]));return t}function o(e){var t=d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.INTEGER,!1,d.integerToDer(e.version).getBytes()),d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[h.pki.distinguishedNameToAsn1({attributes:e.issuer}),d.create(d.Class.UNIVERSAL,d.Type.INTEGER,!1,h.util.hexToBytes(e.serialNumber))]),d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.OID,!1,d.oidToDer(e.digestAlgorithm).getBytes()),d.create(d.Class.UNIVERSAL,d.Type.NULL,!1,"")])]);if(e.authenticatedAttributesAsn1&&t.value.push(e.authenticatedAttributesAsn1),t.value.push(d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.OID,!1,d.oidToDer(e.signatureAlgorithm).getBytes()),d.create(d.Class.UNIVERSAL,d.Type.NULL,!1,"")])),t.value.push(d.create(d.Class.UNIVERSAL,d.Type.OCTETSTRING,!1,e.signature)),e.unauthenticatedAttributes.length>0){for(var r=d.create(d.Class.CONTEXT_SPECIFIC,1,!0,[]),n=0;n<e.unauthenticatedAttributes.length;++n){var a=e.unauthenticatedAttributes[n];r.values.push(u(a))}t.value.push(r)}return t}function c(e){for(var t=[],r=0;r<e.length;++r)t.push(o(e[r]));return t}function u(e){var t;if(e.type===h.pki.oids.contentType)t=d.create(d.Class.UNIVERSAL,d.Type.OID,!1,d.oidToDer(e.value).getBytes());else if(e.type===h.pki.oids.messageDigest)t=d.create(d.Class.UNIVERSAL,d.Type.OCTETSTRING,!1,e.value.bytes());else if(e.type===h.pki.oids.signingTime){var r=new Date("1950-01-01T00:00:00Z"),n=new Date("2050-01-01T00:00:00Z"),a=e.value;if("string"==typeof a){var i=Date.parse(a);a=isNaN(i)?13===a.length?d.utcTimeToDate(a):d.generalizedTimeToDate(a):new Date(i)}t=a>=r&&a<n?d.create(d.Class.UNIVERSAL,d.Type.UTCTIME,!1,d.dateToUtcTime(a)):d.create(d.Class.UNIVERSAL,d.Type.GENERALIZEDTIME,!1,d.dateToGeneralizedTime(a))}return d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.OID,!1,d.oidToDer(e.type).getBytes()),d.create(d.Class.UNIVERSAL,d.Type.SET,!0,[t])])}function l(e){return[d.create(d.Class.UNIVERSAL,d.Type.OID,!1,d.oidToDer(h.pki.oids.data).getBytes()),d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.OID,!1,d.oidToDer(e.algorithm).getBytes()),d.create(d.Class.UNIVERSAL,d.Type.OCTETSTRING,!1,e.parameter.getBytes())]),d.create(d.Class.CONTEXT_SPECIFIC,0,!0,[d.create(d.Class.UNIVERSAL,d.Type.OCTETSTRING,!1,e.content.getBytes())])]}function p(e,t,r){var n={},a=[];if(!d.validate(t,r,n,a)){var i=new Error("Cannot read PKCS#7 message. ASN.1 object is not a supported PKCS#7 message.");throw i.errors=i,i}if(d.derToOid(n.contentType)!==h.pki.oids.data)throw new Error("Unsupported PKCS#7 message. Only wrapped ContentType Data supported.");if(n.encryptedContent){var s="";if(h.util.isArray(n.encryptedContent))for(var o=0;o<n.encryptedContent.length;++o){if(n.encryptedContent[o].type!==d.Type.OCTETSTRING)throw new Error("Malformed PKCS#7 message, expecting encrypted content constructed of only OCTET STRING objects.");s+=n.encryptedContent[o].value}else s=n.encryptedContent;e.encryptedContent={algorithm:d.derToOid(n.encAlgorithm),parameter:h.util.createBuffer(n.encParameter.value),content:h.util.createBuffer(s)}}if(n.content){var s="";if(h.util.isArray(n.content))for(var o=0;o<n.content.length;++o){if(n.content[o].type!==d.Type.OCTETSTRING)throw new Error("Malformed PKCS#7 message, expecting content constructed of only OCTET STRING objects.");s+=n.content[o].value}else s=n.content;e.content=h.util.createBuffer(s)}return e.version=n.version.charCodeAt(0),e.rawCapture=n,n}function f(e){if(void 0===e.encryptedContent.key)throw new Error("Symmetric key not available.");if(void 0===e.content){var t;switch(e.encryptedContent.algorithm){case h.pki.oids["aes128-CBC"]:case h.pki.oids["aes192-CBC"]:case h.pki.oids["aes256-CBC"]:t=h.aes.createDecryptionCipher(e.encryptedContent.key);break;case h.pki.oids.desCBC:case h.pki.oids["des-EDE3-CBC"]:t=h.des.createDecryptionCipher(e.encryptedContent.key);break;default:throw new Error("Unsupported symmetric cipher, OID "+e.encryptedContent.algorithm)}if(t.start(e.encryptedContent.parameter),t.update(e.encryptedContent.content),!t.finish())throw new Error("Symmetric decryption failed.");e.content=t.output}}var h=r(0);r(5),r(3),r(11),r(6),r(7),r(30),r(2),r(1),r(18);var d=h.asn1,y=e.exports=h.pkcs7=h.pkcs7||{};y.messageFromPem=function(e){var t=h.pem.decode(e)[0];if("PKCS7"!==t.type){var r=new Error('Could not convert PKCS#7 message from PEM; PEM header type is not "PKCS#7".');throw r.headerType=t.type,r}if(t.procType&&"ENCRYPTED"===t.procType.type)throw new Error("Could not convert PKCS#7 message from PEM; PEM is encrypted.");var n=d.fromDer(t.body);return y.messageFromAsn1(n)},y.messageToPem=function(e,t){var r={type:"PKCS7",body:d.toDer(e.toAsn1()).getBytes()};return h.pem.encode(r,{maxline:t})},y.messageFromAsn1=function(e){var t={},r=[];if(!d.validate(e,y.asn1.contentInfoValidator,t,r)){var n=new Error("Cannot read PKCS#7 message. ASN.1 object is not an PKCS#7 ContentInfo.");throw n.errors=r,n}var a,i=d.derToOid(t.contentType);switch(i){case h.pki.oids.envelopedData:a=y.createEnvelopedData();break;case h.pki.oids.encryptedData:a=y.createEncryptedData();break;case h.pki.oids.signedData:a=y.createSignedData();break;default:throw new Error("Cannot read PKCS#7 message. ContentType with OID "+i+" is not (yet) supported.")}return a.fromAsn1(t.content.value[0]),a},y.createSignedData=function(){function e(){for(var e={},t=0;t<r.signers.length;++t){var n=r.signers[t],a=n.digestAlgorithm;a in e||(e[a]=h.md[h.pki.oids[a]].create()),0===n.authenticatedAttributes.length?n.md=e[a]:n.md=h.md[h.pki.oids[a]].create()}r.digestAlgorithmIdentifiers=[];for(var a in e)r.digestAlgorithmIdentifiers.push(d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.OID,!1,d.oidToDer(a).getBytes()),d.create(d.Class.UNIVERSAL,d.Type.NULL,!1,"")]));return e}function t(e){var t;if(r.detachedContent?t=r.detachedContent:(t=r.contentInfo.value[1],t=t.value[0]),!t)throw new Error("Could not sign PKCS#7 message; there is no content to sign.");var n=d.derToOid(r.contentInfo.value[0].value),a=d.toDer(t);a.getByte(),d.getBerValueLength(a),a=a.getBytes();for(var i in e)e[i].start().update(a);for(var s=new Date,o=0;o<r.signers.length;++o){var l=r.signers[o];if(0===l.authenticatedAttributes.length){if(n!==h.pki.oids.data)throw new Error("Invalid signer; authenticatedAttributes must be present when the ContentInfo content type is not PKCS#7 Data.")}else{l.authenticatedAttributesAsn1=d.create(d.Class.CONTEXT_SPECIFIC,0,!0,[]);for(var p=d.create(d.Class.UNIVERSAL,d.Type.SET,!0,[]),f=0;f<l.authenticatedAttributes.length;++f){var y=l.authenticatedAttributes[f];y.type===h.pki.oids.messageDigest?y.value=e[l.digestAlgorithm].digest():y.type===h.pki.oids.signingTime&&(y.value||(y.value=s)),p.value.push(u(y)),l.authenticatedAttributesAsn1.value.push(u(y))}a=d.toDer(p).getBytes(),l.md.start().update(a)}l.signature=l.key.sign(l.md,"RSASSA-PKCS1-V1_5")}r.signerInfos=c(r.signers)}var r=null;return r={type:h.pki.oids.signedData,version:1,certificates:[],crls:[],signers:[],digestAlgorithmIdentifiers:[],contentInfo:null,signerInfos:[],fromAsn1:function(e){if(p(r,e,y.asn1.signedDataValidator),r.certificates=[],r.crls=[],r.digestAlgorithmIdentifiers=[],r.contentInfo=null,r.signerInfos=[],r.rawCapture.certificates)for(var t=r.rawCapture.certificates.value,n=0;n<t.length;++n)r.certificates.push(h.pki.certificateFromAsn1(t[n]))},toAsn1:function(){r.contentInfo||r.sign();for(var e=[],t=0;t<r.certificates.length;++t)e.push(h.pki.certificateToAsn1(r.certificates[t]));var n=[],a=d.create(d.Class.CONTEXT_SPECIFIC,0,!0,[d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.INTEGER,!1,d.integerToDer(r.version).getBytes()),d.create(d.Class.UNIVERSAL,d.Type.SET,!0,r.digestAlgorithmIdentifiers),r.contentInfo])]);return e.length>0&&a.value[0].value.push(d.create(d.Class.CONTEXT_SPECIFIC,0,!0,e)),n.length>0&&a.value[0].value.push(d.create(d.Class.CONTEXT_SPECIFIC,1,!0,n)),a.value[0].value.push(d.create(d.Class.UNIVERSAL,d.Type.SET,!0,r.signerInfos)),d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.OID,!1,d.oidToDer(r.type).getBytes()),a])},addSigner:function(e){var t=e.issuer,n=e.serialNumber;if(e.certificate){var a=e.certificate;"string"==typeof a&&(a=h.pki.certificateFromPem(a)),t=a.issuer.attributes,n=a.serialNumber}var i=e.key;if(!i)throw new Error("Could not add PKCS#7 signer; no private key specified.");"string"==typeof i&&(i=h.pki.privateKeyFromPem(i));var s=e.digestAlgorithm||h.pki.oids.sha1;switch(s){case h.pki.oids.sha1:case h.pki.oids.sha256:case h.pki.oids.sha384:case h.pki.oids.sha512:case h.pki.oids.md5:break;default:throw new Error("Could not add PKCS#7 signer; unknown message digest algorithm: "+s)}var o=e.authenticatedAttributes||[];if(o.length>0){for(var c=!1,u=!1,l=0;l<o.length;++l){var p=o[l];if(c||p.type!==h.pki.oids.contentType){if(u||p.type!==h.pki.oids.messageDigest);else if(u=!0,c)break}else if(c=!0,u)break}if(!c||!u)throw new Error("Invalid signer.authenticatedAttributes. If signer.authenticatedAttributes is specified, then it must contain at least two attributes, PKCS #9 content-type and PKCS #9 message-digest.")}r.signers.push({key:i,version:1,issuer:t,serialNumber:n,digestAlgorithm:s,signatureAlgorithm:h.pki.oids.rsaEncryption,signature:null,authenticatedAttributes:o,unauthenticatedAttributes:[]})},sign:function(n){if(n=n||{},("object"!=typeof r.content||null===r.contentInfo)&&(r.contentInfo=d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.OID,!1,d.oidToDer(h.pki.oids.data).getBytes())]),"content"in r)){var a;r.content instanceof h.util.ByteBuffer?a=r.content.bytes():"string"==typeof r.content&&(a=h.util.encodeUtf8(r.content)),n.detached?r.detachedContent=d.create(d.Class.UNIVERSAL,d.Type.OCTETSTRING,!1,a):r.contentInfo.value.push(d.create(d.Class.CONTEXT_SPECIFIC,0,!0,[d.create(d.Class.UNIVERSAL,d.Type.OCTETSTRING,!1,a)]))}if(0!==r.signers.length){t(e())}},verify:function(){throw new Error("PKCS#7 signature verification not yet implemented.")},addCertificate:function(e){"string"==typeof e&&(e=h.pki.certificateFromPem(e)),r.certificates.push(e)},addCertificateRevokationList:function(e){throw new Error("PKCS#7 CRL support not yet implemented.")}}},y.createEncryptedData=function(){var e=null;return e={type:h.pki.oids.encryptedData,version:0,encryptedContent:{algorithm:h.pki.oids["aes256-CBC"]},fromAsn1:function(t){p(e,t,y.asn1.encryptedDataValidator)},decrypt:function(t){void 0!==t&&(e.encryptedContent.key=t),f(e)}}},y.createEnvelopedData=function(){var e=null;return e={type:h.pki.oids.envelopedData,version:0,recipients:[],encryptedContent:{algorithm:h.pki.oids["aes256-CBC"]},fromAsn1:function(t){var r=p(e,t,y.asn1.envelopedDataValidator);e.recipients=i(r.recipientInfos.value)},toAsn1:function(){return d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.OID,!1,d.oidToDer(e.type).getBytes()),d.create(d.Class.CONTEXT_SPECIFIC,0,!0,[d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,[d.create(d.Class.UNIVERSAL,d.Type.INTEGER,!1,d.integerToDer(e.version).getBytes()),d.create(d.Class.UNIVERSAL,d.Type.SET,!0,s(e.recipients)),d.create(d.Class.UNIVERSAL,d.Type.SEQUENCE,!0,l(e.encryptedContent))])])])},findRecipient:function(t){for(var r=t.issuer.attributes,n=0;n<e.recipients.length;++n){var a=e.recipients[n],i=a.issuer;if(a.serialNumber===t.serialNumber&&i.length===r.length){for(var s=!0,o=0;o<r.length;++o)if(i[o].type!==r[o].type||i[o].value!==r[o].value){s=!1;break}if(s)return a}}return null},decrypt:function(t,r){if(void 0===e.encryptedContent.key&&void 0!==t&&void 0!==r)switch(t.encryptedContent.algorithm){case h.pki.oids.rsaEncryption:case h.pki.oids.desCBC:var n=r.decrypt(t.encryptedContent.content);e.encryptedContent.key=h.util.createBuffer(n);break;default:throw new Error("Unsupported asymmetric cipher, OID "+t.encryptedContent.algorithm)}f(e)},addRecipient:function(t){e.recipients.push({version:0,issuer:t.issuer.attributes,serialNumber:t.serialNumber,encryptedContent:{algorithm:h.pki.oids.rsaEncryption,key:t.publicKey}})},encrypt:function(t,r){if(void 0===e.encryptedContent.content){r=r||e.encryptedContent.algorithm,t=t||e.encryptedContent.key;var n,a,i;switch(r){case h.pki.oids["aes128-CBC"]:n=16,a=16,i=h.aes.createEncryptionCipher;break;case h.pki.oids["aes192-CBC"]:n=24,a=16,i=h.aes.createEncryptionCipher;break;case h.pki.oids["aes256-CBC"]:n=32,a=16,i=h.aes.createEncryptionCipher;break;case h.pki.oids["des-EDE3-CBC"]:n=24,a=8,i=h.des.createEncryptionCipher;break;default:throw new Error("Unsupported symmetric cipher, OID "+r)}if(void 0===t)t=h.util.createBuffer(h.random.getBytes(n));else if(t.length()!=n)throw new Error("Symmetric key has wrong length; got "+t.length()+" bytes, expected "+n+".");e.encryptedContent.algorithm=r,e.encryptedContent.key=t,e.encryptedContent.parameter=h.util.createBuffer(h.random.getBytes(a));var s=i(t);if(s.start(e.encryptedContent.parameter.copy()),s.update(e.content),!s.finish())throw new Error("Symmetric encryption failed.");e.encryptedContent.content=s.output}for(var o=0;o<e.recipients.length;++o){var c=e.recipients[o];if(void 0===c.encryptedContent.content)switch(c.encryptedContent.algorithm){case h.pki.oids.rsaEncryption:c.encryptedContent.content=c.encryptedContent.key.encrypt(e.encryptedContent.key.data);break;default:throw new Error("Unsupported asymmetric cipher, OID "+c.encryptedContent.algorithm)}}}}}},function(e,t,r){function n(e,t){var r=t.toString(16);r[0]>="8"&&(r="00"+r);var n=s.util.hexToBytes(r);e.putInt32(n.length),e.putBytes(n)}function a(e,t){e.putInt32(t.length),e.putString(t)}function i(){for(var e=s.md.sha1.create(),t=arguments.length,r=0;r<t;++r)e.update(arguments[r]);return e.digest()}var s=r(0);r(5),r(8),r(15),r(9),r(1);var o=e.exports=s.ssh=s.ssh||{};o.privateKeyToPutty=function(e,t,r){r=r||"",t=t||"";var o=""===t?"none":"aes256-cbc",c="PuTTY-User-Key-File-2: ssh-rsa\r\n";c+="Encryption: "+o+"\r\n",c+="Comment: "+r+"\r\n";var u=s.util.createBuffer();a(u,"ssh-rsa"),n(u,e.e),n(u,e.n);var l=s.util.encode64(u.bytes(),64),p=Math.floor(l.length/66)+1;c+="Public-Lines: "+p+"\r\n",c+=l;var f=s.util.createBuffer();n(f,e.d),n(f,e.p),n(f,e.q),n(f,e.qInv);var h;if(t){var d=f.length()+16-1;d-=d%16;var y=i(f.bytes());y.truncate(y.length()-d+f.length()),f.putBuffer(y);var g=s.util.createBuffer();g.putBuffer(i("\0\0\0\0",t)),g.putBuffer(i("\0\0\0",t));var v=s.aes.createEncryptionCipher(g.truncate(8),"CBC");v.start(s.util.createBuffer().fillWithByte(0,16)),v.update(f.copy()),v.finish();var m=v.output;m.truncate(16),h=s.util.encode64(m.bytes(),64)}else h=s.util.encode64(f.bytes(),64);p=Math.floor(h.length/66)+1,c+="\r\nPrivate-Lines: "+p+"\r\n",c+=h;var C=i("putty-private-key-file-mac-key",t),E=s.util.createBuffer();a(E,"ssh-rsa"),a(E,o),a(E,r),E.putInt32(u.length()),E.putBuffer(u),E.putInt32(f.length()),E.putBuffer(f);var S=s.hmac.create();return S.start("sha1",C),S.update(E.bytes()),c+="\r\nPrivate-MAC: "+S.digest().toHex()+"\r\n"},o.publicKeyToOpenSSH=function(e,t){t=t||"";var r=s.util.createBuffer();return a(r,"ssh-rsa"),n(r,e.e),n(r,e.n),"ssh-rsa "+s.util.encode64(r.bytes())+" "+t},o.privateKeyToOpenSSH=function(e,t){return t?s.pki.encryptRsaPrivateKey(e,t,{legacy:!0,algorithm:"aes128"}):s.pki.privateKeyToPem(e)},o.getPublicKeyFingerprint=function(e,t){t=t||{};var r=t.md||s.md.md5.create(),i=s.util.createBuffer();a(i,"ssh-rsa"),n(i,e.e),n(i,e.n),r.start(),r.update(i.getBytes());var o=r.digest();if("hex"===t.encoding){var c=o.toHex();return t.delimiter?c.match(/.{2}/g).join(t.delimiter):c}if("binary"===t.encoding)return o.getBytes();if(t.encoding)throw new Error('Unknown encoding "'+t.encoding+'".');return o}},function(e,t,r){var n=r(0);r(20),r(33),r(1);var a="forge.task",i={},s=0;n.debug.set(a,"tasks",i);var o={};n.debug.set(a,"queues",o);var c="ready",u="running",l="blocked",p="sleeping",f="done",h="error",d="stop",y="start",g={};g[c]={},g[c][d]=c,g[c][y]=u,g[c].cancel=f,g[c].fail=h,g[u]={},g[u][d]=c,g[u][y]=u,g[u].block=l,g[u].unblock=u,g[u].sleep=p,g[u].wakeup=u,g[u].cancel=f,g[u].fail=h,g[l]={},g[l][d]=l,g[l][y]=l,g[l].block=l,g[l].unblock=l,g[l].sleep=l,g[l].wakeup=l,g[l].cancel=f,g[l].fail=h,g[p]={},g[p][d]=p,g[p][y]=p,g[p].block=p,g[p].unblock=p,g[p].sleep=p,g[p].wakeup=p,g[p].cancel=f,g[p].fail=h,g[f]={},g[f][d]=f,g[f][y]=f,g[f].block=f,g[f].unblock=f,g[f].sleep=f,g[f].wakeup=f,g[f].cancel=f,g[f].fail=h,g[h]={},g[h][d]=h,g[h][y]=h,g[h].block=h,g[h].unblock=h,g[h].sleep=h,g[h].wakeup=h,g[h].cancel=h,g[h].fail=h;var v=function(e){this.id=-1,this.name=e.name||"?",this.parent=e.parent||null,this.run=e.run,this.subtasks=[],this.error=!1,this.state=c,this.blocks=0,this.timeoutId=null,this.swapTime=null,this.userData=null,this.id=s++,i[this.id]=this};v.prototype.debug=function(e){e=e||"",n.log.debug(a,e,"[%s][%s] task:",this.id,this.name,this,"subtasks:",this.subtasks.length,"queue:",o)},v.prototype.next=function(e,t){"function"==typeof e&&(t=e,e=this.name);var r=new v({run:t,name:e,parent:this});return r.state=u,r.type=this.type,r.successCallback=this.successCallback||null,r.failureCallback=this.failureCallback||null,this.subtasks.push(r),this},v.prototype.parallel=function(e,t){return n.util.isArray(e)&&(t=e,e=this.name),this.next(e,function(r){var a=r;a.block(t.length);for(var i=0;i<t.length;i++){var s=e+"__parallel-"+r.id+"-"+i,o=i;!function(e,r){n.task.start({type:e,run:function(e){t[r](e)},success:function(e){a.unblock()},failure:function(e){a.unblock()}})}(s,o)}})},v.prototype.stop=function(){this.state=g[this.state][d]},v.prototype.start=function(){this.error=!1,this.state=g[this.state][y],this.state===u&&(this.start=new Date,this.run(this),C(this,0))},v.prototype.block=function(e){e=void 0===e?1:e,this.blocks+=e,this.blocks>0&&(this.state=g[this.state].block)},v.prototype.unblock=function(e){return e=void 0===e?1:e,this.blocks-=e,0===this.blocks&&this.state!==f&&(this.state=u,C(this,0)),this.blocks},v.prototype.sleep=function(e){e=void 0===e?0:e,this.state=g[this.state].sleep;var t=this;this.timeoutId=setTimeout(function(){t.timeoutId=null,t.state=u,C(t,0)},e)},v.prototype.wait=function(e){e.wait(this)},v.prototype.wakeup=function(){this.state===p&&(cancelTimeout(this.timeoutId),this.timeoutId=null,this.state=u,C(this,0))},v.prototype.cancel=function(){this.state=g[this.state].cancel,this.permitsNeeded=0,null!==this.timeoutId&&(cancelTimeout(this.timeoutId),this.timeoutId=null),this.subtasks=[]},v.prototype.fail=function(e){if(this.error=!0,E(this,!0),e)e.error=this.error,e.swapTime=this.swapTime,e.userData=this.userData,C(e,0);else{if(null!==this.parent){for(var t=this.parent;null!==t.parent;)t.error=this.error,t.swapTime=this.swapTime,t.userData=this.userData,t=t.parent;E(t,!0)}this.failureCallback&&this.failureCallback(this)}};var m=function(e){e.error=!1,e.state=g[e.state][y],setTimeout(function(){e.state===u&&(e.swapTime=+new Date,e.run(e),C(e,0))},0)},C=function(e,t){var r=t>30||+new Date-e.swapTime>20,n=function(t){if(t++,e.state===u)if(r&&(e.swapTime=+new Date),e.subtasks.length>0){var n=e.subtasks.shift();n.error=e.error,n.swapTime=e.swapTime,n.userData=e.userData,n.run(n),n.error||C(n,t)}else E(e),e.error||null!==e.parent&&(e.parent.error=e.error,e.parent.swapTime=e.swapTime,e.parent.userData=e.userData,C(e.parent,t))};r?setTimeout(n,0):n(t)},E=function(e,t){e.state=f,delete i[e.id],null===e.parent&&(e.type in o?0===o[e.type].length?n.log.error(a,"[%s][%s] task queue empty [%s]",e.id,e.name,e.type):o[e.type][0]!==e?n.log.error(a,"[%s][%s] task not first in queue [%s]",e.id,e.name,e.type):(o[e.type].shift(),0===o[e.type].length?delete o[e.type]:o[e.type][0].start()):n.log.error(a,"[%s][%s] task queue missing [%s]",e.id,e.name,e.type),t||(e.error&&e.failureCallback?e.failureCallback(e):!e.error&&e.successCallback&&e.successCallback(e)))};e.exports=n.task=n.task||{},n.task.start=function(e){var t=new v({run:e.run,name:e.name||"?"});t.type=e.type,t.successCallback=e.success||null,t.failureCallback=e.failure||null,t.type in o?o[e.type].push(t):(o[t.type]=[t],m(t))},n.task.cancel=function(e){e in o&&(o[e]=[o[e][0]])},n.task.createCondition=function(){var e={tasks:{}};return e.wait=function(t){t.id in e.tasks||(t.block(),e.tasks[t.id]=t)},e.notify=function(){var t=e.tasks;e.tasks={};for(var r in t)t[r].unblock()},e}},function(e,t,r){var n=r(0),a=e.exports=n.form=n.form||{};!function(e){var t=/([^\[]*?)\[(.*?)\]/g,r=function(e){for(var r,n=[];r=t.exec(e);)r[1].length>0&&n.push(r[1]),r.length>=2&&n.push(r[2]);return 0===n.length&&n.push(e),n},n=function(t,n,a,i){for(var s=[],o=0;o<n.length;++o){var c=n[o];if(-1!==c.indexOf("[")&&-1===c.indexOf("]")&&o<n.length-1)do{c+="."+n[++o]}while(o<n.length-1&&-1===n[o].indexOf("]"));s.push(c)}n=s;var s=[];e.each(n,function(e,t){s=s.concat(r(t))}),n=s,e.each(n,function(r,s){if(i&&0!==s.length&&s in i&&(s=i[s]),0===s.length&&(s=t.length),t[s])r==n.length-1?(e.isArray(t[s])||(t[s]=[t[s]]),t[s].push(a)):t=t[s];else if(r==n.length-1)t[s]=a;else{var o=n[r+1];if(0===o.length)t[s]=[];else{var c=o-0==o&&o.length>0;t[s]=c?[]:{}}t=t[s]}})};a.serialize=function(t,r,a){var i={};return r=r||".",e.each(t.serializeArray(),function(){n(i,this.name.split(r),this.value||"",a)}),i}}(jQuery)},function(e,t,r){var n=r(0);r(10),n.tls.wrapSocket=function(e){var t=e.socket,r={id:t.id,connected:t.connected||function(e){},closed:t.closed||function(e){},data:t.data||function(e){},error:t.error||function(e){}},a=n.tls.createConnection({server:!1,sessionId:e.sessionId||null,caStore:e.caStore||[],sessionCache:e.sessionCache||null,cipherSuites:e.cipherSuites||null,virtualHost:e.virtualHost,verify:e.verify,getCertificate:e.getCertificate,getPrivateKey:e.getPrivateKey,getSignature:e.getSignature,deflate:e.deflate,inflate:e.inflate,connected:function(e){1===e.handshakes&&r.connected({id:t.id,type:"connect",bytesAvailable:e.data.length()})},tlsDataReady:function(e){return t.send(e.tlsData.getBytes())},dataReady:function(e){r.data({id:t.id,type:"socketData",bytesAvailable:e.data.length()})},closed:function(e){t.close()},error:function(e,n){r.error({id:t.id,type:"tlsError",message:n.message,bytesAvailable:0,error:n}),t.close()}});t.connected=function(t){a.handshake(e.sessionId)},t.closed=function(e){a.open&&a.handshaking&&r.error({id:t.id,type:"ioError",message:"Connection closed during handshake.",bytesAvailable:0}),a.close(),r.closed({id:t.id,type:"close",bytesAvailable:0})},t.error=function(e){r.error({id:t.id,type:e.type,message:e.message,bytesAvailable:0}),a.close()};var i=0;return t.data=function(e){if(a.open){if(e.bytesAvailable>=i){var r=Math.max(e.bytesAvailable,i),n=t.receive(r);null!==n&&(i=a.process(n))}}else t.receive(e.bytesAvailable)},r.destroy=function(){t.destroy()},r.setSessionCache=function(e){a.sessionCache=tls.createSessionCache(e)},r.connect=function(e){t.connect(e)},r.close=function(){a.close()},r.isConnected=function(){return a.isConnected&&t.isConnected()},r.send=function(e){return a.prepare(e)},r.receive=function(e){return a.data.getBytes(e)},r.bytesAvailable=function(){return a.data.length()},r}},function(e,t,r){var n=r(0);r(34),r(35);var a=e.exports=n.xhr=n.xhr||{};!function(e){var t="forge.xhr",r=null,i=0,s=null,o=null,c={},u=10,l=n.net,p=n.http;a.init=function(e){n.log.debug(t,"initializing",e),i=e.policyPort||i,s=e.policyUrl||s,u=e.connections||u,r=l.createSocketPool({flashId:e.flashId,policyPort:i,policyUrl:s,msie:e.msie||!1}),o=p.createClient({url:e.url||window.location.protocol+"//"+window.location.host,socketPool:r,policyPort:i,policyUrl:s,connections:e.connections||u,caCerts:e.caCerts,cipherSuites:e.cipherSuites,persistCookies:e.persistCookies||!0,primeTlsSockets:e.primeTlsSockets||!1,verify:e.verify,getCertificate:e.getCertificate,getPrivateKey:e.getPrivateKey,getSignature:e.getSignature}),c[o.url.full]=o,n.log.debug(t,"ready")},a.cleanup=function(){for(var e in c)c[e].destroy();c={},o=null,r.destroy(),r=null},a.setCookie=function(e){if(e.maxAge=e.maxAge||-1,e.domain)for(var t in c){var r=c[t];p.withinCookieDomain(r.url,e)&&r.secure===e.secure&&r.setCookie(e)}else o.setCookie(e)},a.getCookie=function(e,t,r){var a=null;if(r)for(var i in c){var s=c[i];if(p.withinCookieDomain(s.url,r)){var u=s.getCookie(e,t);null!==u&&(null===a?a=u:n.util.isArray(a)?a.push(u):a=[a,u])}}else a=o.getCookie(e,t);return a},a.removeCookie=function(e,t,r){var n=!1;if(r)for(var a in c){var i=c[a];p.withinCookieDomain(i.url,r)&&i.removeCookie(e,t)&&(n=!0)}else n=o.removeCookie(e,t);return n},a.create=function(a){a=e.extend({logWarningOnError:!0,verbose:!1,logError:function(){},logWarning:function(){},logDebug:function(){},logVerbose:function(){},url:null},a||{});var l={client:null,request:null,response:null,asynchronous:!0,sendFlag:!1,errorFlag:!1},f={error:a.logError||n.log.error,warning:a.logWarning||n.log.warning,debug:a.logDebug||n.log.debug,verbose:a.logVerbose||n.log.verbose},h={onreadystatechange:null,readyState:0,responseText:"",responseXML:null,status:0,statusText:""};if(null===a.url)l.client=o;else{var d=p.parseUrl(a.url);if(!d){new Error("Invalid url.").details={url:a.url}}d.full in c?l.client=c[d.full]:(l.client=p.createClient({url:a.url,socketPool:r,policyPort:a.policyPort||i,policyUrl:a.policyUrl||s,connections:a.connections||u,caCerts:a.caCerts,cipherSuites:a.cipherSuites,persistCookies:a.persistCookies||!0,primeTlsSockets:a.primeTlsSockets||!1,verify:a.verify,getCertificate:a.getCertificate,getPrivateKey:a.getPrivateKey,getSignature:a.getSignature}),c[d.full]=l.client)}return h.open=function(e,t,r,n,a){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"PATCH":case"POST":case"PUT":break;case"CONNECT":case"TRACE":case"TRACK":throw new Error("CONNECT, TRACE and TRACK methods are disallowed");default:throw new Error("Invalid method: "+e)}l.sendFlag=!1,h.responseText="",h.responseXML=null,h.status=0,h.statusText="",l.request=p.createRequest({method:e,path:t}),h.readyState=1,h.onreadystatechange&&h.onreadystatechange()},h.setRequestHeader=function(e,t){if(1!=h.readyState||l.sendFlag)throw new Error("XHR not open or sending");l.request.setField(e,t)},h.send=function(e){if(1!=h.readyState||l.sendFlag)throw new Error("XHR not open or sending");if(e&&"GET"!==l.request.method&&"HEAD"!==l.request.method)if("undefined"!=typeof XMLSerializer)if(e instanceof Document){var r=new XMLSerializer;l.request.body=r.serializeToString(e)}else l.request.body=e;else void 0!==e.xml?l.request.body=e.xml:l.request.body=e;l.errorFlag=!1,l.sendFlag=!0,h.onreadystatechange&&h.onreadystatechange();var n={};n.request=l.request,n.headerReady=function(e){h.cookies=l.client.cookies,h.readyState=2,h.status=e.response.code,h.statusText=e.response.message,l.response=e.response,h.onreadystatechange&&h.onreadystatechange(),l.response.aborted||(h.readyState=3,h.onreadystatechange&&h.onreadystatechange())},n.bodyReady=function(e){h.readyState=4;var r=e.response.getField("Content-Type");if(r&&(0===r.indexOf("text/xml")||0===r.indexOf("application/xml")||-1!==r.indexOf("+xml")))try{var a=new ActiveXObject("MicrosoftXMLDOM");a.async=!1,a.loadXML(e.response.body),h.responseXML=a}catch(e){var i=new DOMParser;h.responseXML=i.parseFromString(e.body,"text/xml")}var s=0;null!==e.response.body&&(h.responseText=e.response.body,s=e.response.body.length);var o=l.request,c=o.method+" "+o.path+" "+h.status+" "+h.statusText+" "+s+"B "+(e.request.connectTime+e.request.time+e.response.time)+"ms";n.verbose?(h.status>=400&&n.logWarningOnError?f.warning:f.verbose)(t,c,e,e.response.body?"\n"+e.response.body:"\nNo content"):(h.status>=400&&n.logWarningOnError?f.warning:f.debug)(t,c),h.onreadystatechange&&h.onreadystatechange()},n.error=function(e){var r=l.request;f.error(t,r.method+" "+r.path,e),h.responseText="",h.responseXML=null,l.errorFlag=!0,h.status=0,h.statusText="",h.readyState=4,h.onreadystatechange&&h.onreadystatechange()},l.client.send(n)},h.abort=function(){l.request.abort(),h.responseText="",h.responseXML=null,l.errorFlag=!0,h.status=0,h.statusText="",l.request=null,l.response=null,4===h.readyState||0===h.readyState||1===h.readyState&&!l.sendFlag?h.readyState=0:(h.readyState=4,l.sendFlag=!1,h.onreadystatechange&&h.onreadystatechange(),h.readyState=0)},h.getAllResponseHeaders=function(){var t="";if(null!==l.response){var r=l.response.fields;e.each(r,function(r,n){e.each(n,function(e,n){t+=r+": "+n+"\r\n"})})}return t},h.getResponseHeader=function(e){var t=null;return null!==l.response&&e in l.response.fields&&(t=l.response.fields[e],n.util.isArray(t)&&(t=t.join())),t},h}}(jQuery)}])});
//# sourceMappingURL=forge.all.min.js.map