# babel-extract-comments [![NPM version](https://img.shields.io/npm/v/babel-extract-comments.svg?style=flat)](https://www.npmjs.com/package/babel-extract-comments) [![NPM monthly downloads](https://img.shields.io/npm/dm/babel-extract-comments.svg?style=flat)](https://npmjs.org/package/babel-extract-comments) [![NPM total downloads](https://img.shields.io/npm/dt/babel-extract-comments.svg?style=flat)](https://npmjs.org/package/babel-extract-comments) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/babel-extract-comments.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/babel-extract-comments)

> Uses babel (babylon) to extract JavaScript code comments from a JavaScript string or file.

Please consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save babel-extract-comments
```

## Usage

Add to your Node.js/JavaScript project with the following line of code:

```js
const extract = require('babel-extract-comments');
```

## API

### [extract](index.js#L32)

Extract code comments from the given `string`.

**Params**

* `string` **{String}**: String of javascript
* `returns` **{Array}**: Array of code comment objects.

**Example**

```js
var extract = require('babel-extract-comments');
console.log(extract('// this is a code comment'));
// [{ type: 'CommentBlock',
//  value: '!\n * babel-extract-comments <https://github.com/jonschlinkert/babel-extract-comments>\n *\n *
// Copyright (c) 2014-2018, Jon Schlinkert.\n * Released under the MIT License.\n ',
//   start: 0,
//   end: 173,
//   loc: SourceLocation { start: [Position], end: [Position] } }]
```

### [.file](index.js#L53)

Extract code comments from a JavaScript file.

**Params**

* `file` **{String}**: Filepath to the file to parse.
* `options` **{Object}**: Options to pass to [esprima](http://esprima.org).
* `returns` **{Array}**: Array of code comment objects.

**Example**

```js
console.log(extract.file('some-file.js'), { cwd: 'some/path' });
// [ { type: 'Line',
//     value: ' this is a line comment',
//     range: [ 0, 25 ],
//     loc: { start: { line: 1, column: 0 }, end: { line: 1, column: 25 } } } ]
```

## About

<details>
<summary><strong>Contributing</strong></summary>

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

</details>

<details>
<summary><strong>Running Tests</strong></summary>

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

</details>

<details>
<summary><strong>Building docs</strong></summary>

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

</details>

### Related projects

You might also be interested in these projects:

* [esprima-extract-comments](https://www.npmjs.com/package/esprima-extract-comments): Extract code comments from string or from a glob of files using esprima. | [homepage](https://github.com/jonschlinkert/esprima-extract-comments "Extract code comments from string or from a glob of files using esprima.")
* [extract-comments](https://www.npmjs.com/package/extract-comments): Uses esprima to extract line and block comments from a string of JavaScript. Also optionally… [more](https://github.com/jonschlinkert/extract-comments) | [homepage](https://github.com/jonschlinkert/extract-comments "Uses esprima to extract line and block comments from a string of JavaScript. Also optionally parses code context (the next line of code after a comment).")
* [js-comments](https://www.npmjs.com/package/js-comments): Parse JavaScript code comments and generate API documentation. | [homepage](https://github.com/jonschlinkert/js-comments "Parse JavaScript code comments and generate API documentation.")
* [parse-comments](https://www.npmjs.com/package/parse-comments): Parse code comments from JavaScript or any language that uses the same format. | [homepage](https://github.com/jonschlinkert/parse-comments "Parse code comments from JavaScript or any language that uses the same format.")

### Contributors

| **Commits** | **Contributor** | 
| --- | --- |
| 4 | [jonschlinkert](https://github.com/jonschlinkert) |
| 1 | [eventualbuddha](https://github.com/eventualbuddha) |

### Author

**Jon Schlinkert**

* [linkedin/in/jonschlinkert](https://linkedin.com/in/jonschlinkert)
* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on February 12, 2018._