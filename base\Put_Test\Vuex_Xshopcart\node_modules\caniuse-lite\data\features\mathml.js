module.exports={A:{A:{"2":"D A B kB","8":"I F E"},B:{"2":"C O P H J K L","8":"M V N WB KB"},C:{"1":"0 1 2 3 4 5 6 7 8 9 G W I F E D A B C O P H J K L X Y Z a b c d e f g h i j k l m n o p q r s t u v w x Q z AB YB CB JB EB FB GB HB DB BB y T S LB MB NB OB PB QB IB SB M V N iB","129":"tB RB jB rB"},D:{"1":"c","8":"0 1 2 3 4 5 6 7 8 9 G W I F E D A B C O P H J K L X Y Z a b d e f g h i j k l m n o p q r s t u v w x Q z AB YB CB JB EB FB GB HB DB BB y T S LB MB NB OB PB QB IB SB M V N WB KB TB uB aB bB"},E:{"1":"A B C O P VB R U lB mB","260":"G W I F E D cB UB eB fB gB hB"},F:{"2":"D","4":"B C nB oB pB qB R XB sB U","8":"0 1 2 3 4 5 6 7 8 9 H J K L X Y Z a b c d e f g h i j k l m n o p q r s t u v w x Q z AB CB EB FB GB HB DB BB y T S"},G:{"1":"E vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC","8":"UB TC ZB"},H:{"8":"CC"},I:{"8":"RB G N DC EC FC GC ZB HC IC"},J:{"1":"A","8":"F"},K:{"8":"A B C Q R XB U"},L:{"8":"TB"},M:{"1":"M"},N:{"2":"A B"},O:{"4":"JC"},P:{"8":"G KC LC MC NC OC VB PC QC"},Q:{"8":"RC"},R:{"8":"SC"},S:{"1":"dB"}},B:2,C:"MathML"};
