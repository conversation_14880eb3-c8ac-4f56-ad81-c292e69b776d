module.exports={A:{A:{"2":"I F E kB","36":"D A B"},B:{"1":"H J K L M V N WB KB","36":"C O P"},C:{"1":"0 1 2 3 4 5 6 7 8 9 m n o p q r s t u v w x Q z AB YB CB JB EB FB GB HB DB BB y T S LB MB NB OB PB QB IB SB M V N iB","2":"tB RB jB","36":"G W I F E D A B C O P H J K L X Y Z a b c d e f g h i j k l rB"},D:{"1":"0 1 2 3 4 5 6 7 8 9 m n o p q r s t u v w x Q z AB YB CB JB EB FB GB HB DB BB y T S LB MB NB OB PB QB IB SB M V N WB KB TB uB aB bB","36":"G W I F E D A B C O P H J K L X Y Z a b c d e f g h i j k l"},E:{"1":"E D A B C O P gB hB VB R U lB mB","2":"G cB UB","36":"W I F eB fB"},F:{"1":"0 1 2 3 4 5 6 7 8 9 Z a b c d e f g h i j k l m n o p q r s t u v w x Q z AB CB EB FB GB HB DB BB y T S","2":"D B nB oB pB qB R","36":"C H J K L X Y XB sB U"},G:{"1":"E yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC","2":"UB","36":"TC ZB vB wB xB"},H:{"2":"CC"},I:{"1":"N","2":"DC","36":"RB G EC FC GC ZB HC IC"},J:{"36":"F A"},K:{"1":"Q","2":"A B","36":"C R XB U"},L:{"1":"TB"},M:{"1":"M"},N:{"36":"A B"},O:{"1":"JC"},P:{"1":"KC LC MC NC OC VB PC QC","36":"G"},Q:{"1":"RC"},R:{"1":"SC"},S:{"1":"dB"}},B:1,C:"matches() DOM method"};
