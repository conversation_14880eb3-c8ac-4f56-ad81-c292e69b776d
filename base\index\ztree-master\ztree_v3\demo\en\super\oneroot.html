<!DOCTYPE html>
<HTML>
<HEAD>
	<TITLE> ZTREE DEMO - one root</TITLE>
	<meta http-equiv="content-type" content="text/html; charset=UTF-8">
	<link rel="stylesheet" href="../../../css/demo.css" type="text/css">
	<link rel="stylesheet" href="../../../css/zTreeStyle/zTreeStyle.css" type="text/css">
	<script type="text/javascript" src="../../../js/jquery-1.4.4.min.js"></script>
	<script type="text/javascript" src="../../../js/jquery.ztree.core-3.5.js"></script>
	<!--  <script type="text/javascript" src="../../../js/jquery.ztree.excheck-3.5.js"></script>
	  <script type="text/javascript" src="../../../js/jquery.ztree.exedit-3.5.js"></script>-->
	<SCRIPT type="text/javascript">
		<!--
		var setting = {
			view: {
				dblClickExpand: dblClickExpand
			},
			data: {
				simpleData: {
					enable: true
				}
			}
		};

		var zNodes =[
			{ id:1, pId:0, name:"Root", open:true},
			{ id:11, pId:1, name:"Parent Node 1-1", open:true},
			{ id:111, pId:11, name:"Leaf Node 1-1-1"},
			{ id:112, pId:11, name:"Leaf Node 1-1-2"},
			{ id:113, pId:11, name:"Leaf Node 1-1-3"},
			{ id:114, pId:11, name:"Leaf Node 1-1-4"},
			{ id:12, pId:1, name:"Parent Node 1-2", open:true},
			{ id:121, pId:12, name:"Leaf Node 1-2-1"},
			{ id:122, pId:12, name:"Leaf Node 1-2-2"},
			{ id:123, pId:12, name:"Leaf Node 1-2-3"},
			{ id:124, pId:12, name:"Leaf Node 1-2-4"},
			{ id:13, pId:1, name:"Parent Node 1-3", open:true},
			{ id:131, pId:13, name:"Leaf Node 1-3-1"},
			{ id:132, pId:13, name:"Leaf Node 1-3-2"},
			{ id:133, pId:13, name:"Leaf Node 1-3-3"},
			{ id:134, pId:13, name:"Leaf Node 1-3-4"}
		];

		function dblClickExpand(treeId, treeNode) {
			return treeNode.level > 0;
		}

		$(document).ready(function(){
			$.fn.zTree.init($("#treeDemo"), setting, zNodes);
		});
		//-->
	</SCRIPT>
	<style type="text/css">
.ztree li span.button.switch.level0 {visibility:hidden; width:1px;}
.ztree li ul.level0 {padding:0; background:none;}
	</style>
 </HEAD>

<BODY>
<h1>Freeze the Root Node</h1>
<h6>[ File Path: demo/super/oneroot.html ]</h6>
<div class="content_wrap">
	<div class="zTreeDemoBackground left">
		<ul id="treeDemo" class="ztree"></ul>
	</div>
	<div class="right">
		<ul class="info">
			<li class="title"><h2>Explanation of implementation method</h2>
				<ul class="list">
				<li>For only one root, and do not show +/- switch needs need to modify the css, and set the setting.</li>
				<li class="highlight_red">zTree v3.x can be for a given level, set the style, check out the page source, see the css.</li>
				<li class="highlight_red">Set setting.view.dblClickExpand to Function, you can turn off double-clicking for expand the root node.</li>
				</ul>
			</li>
		</ul>
	</div>
</div>
</BODY>
</HTML>