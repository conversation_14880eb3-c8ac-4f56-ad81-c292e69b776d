{"name": "globby", "version": "9.2.0", "description": "Extends `glob` with support for multiple patterns and exposes a Promise API", "license": "MIT", "repository": "sindresorhus/globby", "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"bench": "npm update glob-stream fast-glob && matcha bench.js", "test": "xo && ava && tsd"}, "files": ["index.js", "gitignore.js", "index.d.ts"], "keywords": ["all", "array", "directories", "dirs", "expand", "files", "filesystem", "filter", "find", "fnmatch", "folders", "fs", "glob", "globbing", "globs", "gulpfriendly", "match", "matcher", "minimatch", "multi", "multiple", "paths", "pattern", "patterns", "traverse", "util", "utility", "wildcard", "wildcards", "promise", "gitignore", "git"], "dependencies": {"@types/glob": "^7.1.1", "array-union": "^1.0.2", "dir-glob": "^2.2.2", "fast-glob": "^2.2.6", "glob": "^7.1.3", "ignore": "^4.0.3", "pify": "^4.0.1", "slash": "^2.0.0"}, "devDependencies": {"ava": "^1.4.1", "glob-stream": "^6.1.0", "globby": "sindresorhus/globby#master", "matcha": "^0.7.0", "rimraf": "^2.6.3", "tsd": "^0.7.1", "xo": "^0.24.0"}, "xo": {"ignores": ["fixtures"]}}