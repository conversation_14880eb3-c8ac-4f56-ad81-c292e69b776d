{"version": 3, "file": "scan.js", "sources": ["../../src/internal/operators/scan.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAEA,4CAA2C;AAoD3C,SAAgB,IAAI,CAAO,WAAmD,EAAE,IAAY;IAC1F,IAAI,OAAO,GAAG,KAAK,CAAC;IAMpB,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE;QACzB,OAAO,GAAG,IAAI,CAAC;KAChB;IAED,OAAO,SAAS,oBAAoB,CAAC,MAAqB;QACxD,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;IACnE,CAAC,CAAC;AACJ,CAAC;AAdD,oBAcC;AAED;IACE,sBAAoB,WAAmD,EAAU,IAAY,EAAU,OAAwB;QAAxB,wBAAA,EAAA,eAAwB;QAA3G,gBAAW,GAAX,WAAW,CAAwC;QAAU,SAAI,GAAJ,IAAI,CAAQ;QAAU,YAAO,GAAP,OAAO,CAAiB;IAAG,CAAC;IAEnI,2BAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,cAAc,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACrG,CAAC;IACH,mBAAC;AAAD,CAAC,AAND,IAMC;AAOD;IAAmC,kCAAa;IAY9C,wBAAY,WAA0B,EAAU,WAAmD,EAAU,KAAY,EACrG,OAAgB;QADpC,YAEE,kBAAM,WAAW,CAAC,SACnB;QAH+C,iBAAW,GAAX,WAAW,CAAwC;QAAU,WAAK,GAAL,KAAK,CAAO;QACrG,aAAO,GAAP,OAAO,CAAS;QAZ5B,WAAK,GAAW,CAAC,CAAC;;IAc1B,CAAC;IAZD,sBAAI,gCAAI;aAAR;YACE,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,CAAC;aAED,UAAS,KAAY;YACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACrB,CAAC;;;OALA;IAYS,8BAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;YAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC9B;aAAM;YACL,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SAC7B;IACH,CAAC;IAEO,iCAAQ,GAAhB,UAAiB,KAAQ;QACvB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,MAAW,CAAC;QAChB,IAAI;YACF,MAAM,GAAG,IAAI,CAAC,WAAW,CAAI,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;SACvD;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SAC7B;QACD,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;QACnB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IACH,qBAAC;AAAD,CAAC,AArCD,CAAmC,uBAAU,GAqC5C"}