# lodash.templatesettings v4.2.0

The [Lodash](https://lodash.com/) method `_.templateSettings` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.templatesettings
```

In Node.js:
```js
var templateSettings = require('lodash.templatesettings');
```

See the [documentation](https://lodash.com/docs#templateSettings) or [package source](https://github.com/lodash/lodash/blob/4.2.0-npm-packages/lodash.templatesettings) for more details.
