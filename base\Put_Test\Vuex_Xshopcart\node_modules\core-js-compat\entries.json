{"core-js": ["es.symbol", "es.symbol.description", "es.symbol.async-iterator", "es.symbol.has-instance", "es.symbol.is-concat-spreadable", "es.symbol.iterator", "es.symbol.match", "es.symbol.match-all", "es.symbol.replace", "es.symbol.search", "es.symbol.species", "es.symbol.split", "es.symbol.to-primitive", "es.symbol.to-string-tag", "es.symbol.unscopables", "es.array.concat", "es.array.copy-within", "es.array.every", "es.array.fill", "es.array.filter", "es.array.find", "es.array.find-index", "es.array.flat", "es.array.flat-map", "es.array.for-each", "es.array.from", "es.array.includes", "es.array.index-of", "es.array.is-array", "es.array.iterator", "es.array.join", "es.array.last-index-of", "es.array.map", "es.array.of", "es.array.reduce", "es.array.reduce-right", "es.array.reverse", "es.array.slice", "es.array.some", "es.array.sort", "es.array.species", "es.array.splice", "es.array.unscopables.flat", "es.array.unscopables.flat-map", "es.array-buffer.constructor", "es.array-buffer.is-view", "es.array-buffer.slice", "es.data-view", "es.date.now", "es.date.to-iso-string", "es.date.to-json", "es.date.to-primitive", "es.date.to-string", "es.function.bind", "es.function.has-instance", "es.function.name", "es.global-this", "es.json.stringify", "es.json.to-string-tag", "es.map", "es.math.acosh", "es.math.asinh", "es.math.atanh", "es.math.cbrt", "es.math.clz32", "es.math.cosh", "es.math.expm1", "es.math.fround", "es.math.hypot", "es.math.imul", "es.math.log10", "es.math.log1p", "es.math.log2", "es.math.sign", "es.math.sinh", "es.math.tanh", "es.math.to-string-tag", "es.math.trunc", "es.number.constructor", "es.number.epsilon", "es.number.is-finite", "es.number.is-integer", "es.number.is-nan", "es.number.is-safe-integer", "es.number.max-safe-integer", "es.number.min-safe-integer", "es.number.parse-float", "es.number.parse-int", "es.number.to-fixed", "es.number.to-precision", "es.object.assign", "es.object.create", "es.object.define-getter", "es.object.define-properties", "es.object.define-property", "es.object.define-setter", "es.object.entries", "es.object.freeze", "es.object.from-entries", "es.object.get-own-property-descriptor", "es.object.get-own-property-descriptors", "es.object.get-own-property-names", "es.object.get-prototype-of", "es.object.is", "es.object.is-extensible", "es.object.is-frozen", "es.object.is-sealed", "es.object.keys", "es.object.lookup-getter", "es.object.lookup-setter", "es.object.prevent-extensions", "es.object.seal", "es.object.set-prototype-of", "es.object.to-string", "es.object.values", "es.parse-float", "es.parse-int", "es.promise", "es.promise.all-settled", "es.promise.finally", "es.reflect.apply", "es.reflect.construct", "es.reflect.define-property", "es.reflect.delete-property", "es.reflect.get", "es.reflect.get-own-property-descriptor", "es.reflect.get-prototype-of", "es.reflect.has", "es.reflect.is-extensible", "es.reflect.own-keys", "es.reflect.prevent-extensions", "es.reflect.set", "es.reflect.set-prototype-of", "es.regexp.constructor", "es.regexp.exec", "es.regexp.flags", "es.regexp.sticky", "es.regexp.test", "es.regexp.to-string", "es.set", "es.string.code-point-at", "es.string.ends-with", "es.string.from-code-point", "es.string.includes", "es.string.iterator", "es.string.match", "es.string.match-all", "es.string.pad-end", "es.string.pad-start", "es.string.raw", "es.string.repeat", "es.string.replace", "es.string.search", "es.string.split", "es.string.starts-with", "es.string.trim", "es.string.trim-end", "es.string.trim-start", "es.string.anchor", "es.string.big", "es.string.blink", "es.string.bold", "es.string.fixed", "es.string.fontcolor", "es.string.fontsize", "es.string.italics", "es.string.link", "es.string.small", "es.string.strike", "es.string.sub", "es.string.sup", "es.typed-array.float32-array", "es.typed-array.float64-array", "es.typed-array.int8-array", "es.typed-array.int16-array", "es.typed-array.int32-array", "es.typed-array.uint8-array", "es.typed-array.uint8-clamped-array", "es.typed-array.uint16-array", "es.typed-array.uint32-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string", "es.weak-map", "es.weak-set", "esnext.aggregate-error", "esnext.array.is-template-object", "esnext.array.last-index", "esnext.array.last-item", "esnext.async-iterator.constructor", "esnext.async-iterator.as-indexed-pairs", "esnext.async-iterator.drop", "esnext.async-iterator.every", "esnext.async-iterator.filter", "esnext.async-iterator.find", "esnext.async-iterator.flat-map", "esnext.async-iterator.for-each", "esnext.async-iterator.from", "esnext.async-iterator.map", "esnext.async-iterator.reduce", "esnext.async-iterator.some", "esnext.async-iterator.take", "esnext.async-iterator.to-array", "esnext.composite-key", "esnext.composite-symbol", "esnext.global-this", "esnext.iterator.constructor", "esnext.iterator.as-indexed-pairs", "esnext.iterator.drop", "esnext.iterator.every", "esnext.iterator.filter", "esnext.iterator.find", "esnext.iterator.flat-map", "esnext.iterator.for-each", "esnext.iterator.from", "esnext.iterator.map", "esnext.iterator.reduce", "esnext.iterator.some", "esnext.iterator.take", "esnext.iterator.to-array", "esnext.map.delete-all", "esnext.map.every", "esnext.map.filter", "esnext.map.find", "esnext.map.find-key", "esnext.map.from", "esnext.map.group-by", "esnext.map.includes", "esnext.map.key-by", "esnext.map.key-of", "esnext.map.map-keys", "esnext.map.map-values", "esnext.map.merge", "esnext.map.of", "esnext.map.reduce", "esnext.map.some", "esnext.map.update", "esnext.map.update-or-insert", "esnext.map.upsert", "esnext.math.clamp", "esnext.math.deg-per-rad", "esnext.math.degrees", "esnext.math.fscale", "esnext.math.iaddh", "esnext.math.imulh", "esnext.math.isubh", "esnext.math.rad-per-deg", "esnext.math.radians", "esnext.math.scale", "esnext.math.seeded-prng", "esnext.math.signbit", "esnext.math.umulh", "esnext.number.from-string", "esnext.object.iterate-entries", "esnext.object.iterate-keys", "esnext.object.iterate-values", "esnext.observable", "esnext.promise.all-settled", "esnext.promise.any", "esnext.promise.try", "esnext.reflect.define-metadata", "esnext.reflect.delete-metadata", "esnext.reflect.get-metadata", "esnext.reflect.get-metadata-keys", "esnext.reflect.get-own-metadata", "esnext.reflect.get-own-metadata-keys", "esnext.reflect.has-metadata", "esnext.reflect.has-own-metadata", "esnext.reflect.metadata", "esnext.set.add-all", "esnext.set.delete-all", "esnext.set.difference", "esnext.set.every", "esnext.set.filter", "esnext.set.find", "esnext.set.from", "esnext.set.intersection", "esnext.set.is-disjoint-from", "esnext.set.is-subset-of", "esnext.set.is-superset-of", "esnext.set.join", "esnext.set.map", "esnext.set.of", "esnext.set.reduce", "esnext.set.some", "esnext.set.symmetric-difference", "esnext.set.union", "esnext.string.at", "esnext.string.code-points", "esnext.string.match-all", "esnext.string.replace-all", "esnext.symbol.async-dispose", "esnext.symbol.dispose", "esnext.symbol.observable", "esnext.symbol.pattern-match", "esnext.symbol.replace-all", "esnext.weak-map.delete-all", "esnext.weak-map.from", "esnext.weak-map.of", "esnext.weak-map.upsert", "esnext.weak-set.add-all", "esnext.weak-set.delete-all", "esnext.weak-set.from", "esnext.weak-set.of", "web.dom-collections.for-each", "web.dom-collections.iterator", "web.immediate", "web.queue-microtask", "web.timers", "web.url", "web.url.to-json", "web.url-search-params"], "core-js/es": ["es.symbol", "es.symbol.description", "es.symbol.async-iterator", "es.symbol.has-instance", "es.symbol.is-concat-spreadable", "es.symbol.iterator", "es.symbol.match", "es.symbol.match-all", "es.symbol.replace", "es.symbol.search", "es.symbol.species", "es.symbol.split", "es.symbol.to-primitive", "es.symbol.to-string-tag", "es.symbol.unscopables", "es.array.concat", "es.array.copy-within", "es.array.every", "es.array.fill", "es.array.filter", "es.array.find", "es.array.find-index", "es.array.flat", "es.array.flat-map", "es.array.for-each", "es.array.from", "es.array.includes", "es.array.index-of", "es.array.is-array", "es.array.iterator", "es.array.join", "es.array.last-index-of", "es.array.map", "es.array.of", "es.array.reduce", "es.array.reduce-right", "es.array.reverse", "es.array.slice", "es.array.some", "es.array.sort", "es.array.species", "es.array.splice", "es.array.unscopables.flat", "es.array.unscopables.flat-map", "es.array-buffer.constructor", "es.array-buffer.is-view", "es.array-buffer.slice", "es.data-view", "es.date.now", "es.date.to-iso-string", "es.date.to-json", "es.date.to-primitive", "es.date.to-string", "es.function.bind", "es.function.has-instance", "es.function.name", "es.global-this", "es.json.stringify", "es.json.to-string-tag", "es.map", "es.math.acosh", "es.math.asinh", "es.math.atanh", "es.math.cbrt", "es.math.clz32", "es.math.cosh", "es.math.expm1", "es.math.fround", "es.math.hypot", "es.math.imul", "es.math.log10", "es.math.log1p", "es.math.log2", "es.math.sign", "es.math.sinh", "es.math.tanh", "es.math.to-string-tag", "es.math.trunc", "es.number.constructor", "es.number.epsilon", "es.number.is-finite", "es.number.is-integer", "es.number.is-nan", "es.number.is-safe-integer", "es.number.max-safe-integer", "es.number.min-safe-integer", "es.number.parse-float", "es.number.parse-int", "es.number.to-fixed", "es.number.to-precision", "es.object.assign", "es.object.create", "es.object.define-getter", "es.object.define-properties", "es.object.define-property", "es.object.define-setter", "es.object.entries", "es.object.freeze", "es.object.from-entries", "es.object.get-own-property-descriptor", "es.object.get-own-property-descriptors", "es.object.get-own-property-names", "es.object.get-prototype-of", "es.object.is", "es.object.is-extensible", "es.object.is-frozen", "es.object.is-sealed", "es.object.keys", "es.object.lookup-getter", "es.object.lookup-setter", "es.object.prevent-extensions", "es.object.seal", "es.object.set-prototype-of", "es.object.to-string", "es.object.values", "es.parse-float", "es.parse-int", "es.promise", "es.promise.all-settled", "es.promise.finally", "es.reflect.apply", "es.reflect.construct", "es.reflect.define-property", "es.reflect.delete-property", "es.reflect.get", "es.reflect.get-own-property-descriptor", "es.reflect.get-prototype-of", "es.reflect.has", "es.reflect.is-extensible", "es.reflect.own-keys", "es.reflect.prevent-extensions", "es.reflect.set", "es.reflect.set-prototype-of", "es.regexp.constructor", "es.regexp.exec", "es.regexp.flags", "es.regexp.sticky", "es.regexp.test", "es.regexp.to-string", "es.set", "es.string.code-point-at", "es.string.ends-with", "es.string.from-code-point", "es.string.includes", "es.string.iterator", "es.string.match", "es.string.match-all", "es.string.pad-end", "es.string.pad-start", "es.string.raw", "es.string.repeat", "es.string.replace", "es.string.search", "es.string.split", "es.string.starts-with", "es.string.trim", "es.string.trim-end", "es.string.trim-start", "es.string.anchor", "es.string.big", "es.string.blink", "es.string.bold", "es.string.fixed", "es.string.fontcolor", "es.string.fontsize", "es.string.italics", "es.string.link", "es.string.small", "es.string.strike", "es.string.sub", "es.string.sup", "es.typed-array.float32-array", "es.typed-array.float64-array", "es.typed-array.int8-array", "es.typed-array.int16-array", "es.typed-array.int32-array", "es.typed-array.uint8-array", "es.typed-array.uint8-clamped-array", "es.typed-array.uint16-array", "es.typed-array.uint32-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string", "es.weak-map", "es.weak-set"], "core-js/es/array": ["es.array.concat", "es.array.copy-within", "es.array.every", "es.array.fill", "es.array.filter", "es.array.find", "es.array.find-index", "es.array.flat", "es.array.flat-map", "es.array.for-each", "es.array.from", "es.array.includes", "es.array.index-of", "es.array.is-array", "es.array.iterator", "es.array.join", "es.array.last-index-of", "es.array.map", "es.array.of", "es.array.reduce", "es.array.reduce-right", "es.array.reverse", "es.array.slice", "es.array.some", "es.array.sort", "es.array.species", "es.array.splice", "es.array.unscopables.flat", "es.array.unscopables.flat-map", "es.string.iterator"], "core-js/es/array-buffer": ["es.array-buffer.constructor", "es.array-buffer.is-view", "es.array-buffer.slice", "es.object.to-string"], "core-js/es/array-buffer/constructor": ["es.array-buffer.constructor", "es.object.to-string"], "core-js/es/array-buffer/is-view": ["es.array-buffer.is-view"], "core-js/es/array-buffer/slice": ["es.array-buffer.slice"], "core-js/es/array/concat": ["es.array.concat"], "core-js/es/array/copy-within": ["es.array.copy-within"], "core-js/es/array/entries": ["es.array.iterator"], "core-js/es/array/every": ["es.array.every"], "core-js/es/array/fill": ["es.array.fill"], "core-js/es/array/filter": ["es.array.filter"], "core-js/es/array/find": ["es.array.find"], "core-js/es/array/find-index": ["es.array.find-index"], "core-js/es/array/flat": ["es.array.flat", "es.array.unscopables.flat"], "core-js/es/array/flat-map": ["es.array.flat-map", "es.array.unscopables.flat-map"], "core-js/es/array/for-each": ["es.array.for-each"], "core-js/es/array/from": ["es.array.from", "es.string.iterator"], "core-js/es/array/includes": ["es.array.includes"], "core-js/es/array/index-of": ["es.array.index-of"], "core-js/es/array/is-array": ["es.array.is-array"], "core-js/es/array/iterator": ["es.array.iterator"], "core-js/es/array/join": ["es.array.join"], "core-js/es/array/keys": ["es.array.iterator"], "core-js/es/array/last-index-of": ["es.array.last-index-of"], "core-js/es/array/map": ["es.array.map"], "core-js/es/array/of": ["es.array.of"], "core-js/es/array/reduce": ["es.array.reduce"], "core-js/es/array/reduce-right": ["es.array.reduce-right"], "core-js/es/array/reverse": ["es.array.reverse"], "core-js/es/array/slice": ["es.array.slice"], "core-js/es/array/some": ["es.array.some"], "core-js/es/array/sort": ["es.array.sort"], "core-js/es/array/splice": ["es.array.splice"], "core-js/es/array/values": ["es.array.iterator"], "core-js/es/array/virtual": ["es.array.concat", "es.array.copy-within", "es.array.every", "es.array.fill", "es.array.filter", "es.array.find", "es.array.find-index", "es.array.flat", "es.array.flat-map", "es.array.for-each", "es.array.includes", "es.array.index-of", "es.array.iterator", "es.array.join", "es.array.last-index-of", "es.array.map", "es.array.reduce", "es.array.reduce-right", "es.array.reverse", "es.array.slice", "es.array.some", "es.array.sort", "es.array.species", "es.array.splice", "es.array.unscopables.flat", "es.array.unscopables.flat-map"], "core-js/es/array/virtual/concat": ["es.array.concat"], "core-js/es/array/virtual/copy-within": ["es.array.copy-within"], "core-js/es/array/virtual/entries": ["es.array.iterator"], "core-js/es/array/virtual/every": ["es.array.every"], "core-js/es/array/virtual/fill": ["es.array.fill"], "core-js/es/array/virtual/filter": ["es.array.filter"], "core-js/es/array/virtual/find": ["es.array.find"], "core-js/es/array/virtual/find-index": ["es.array.find-index"], "core-js/es/array/virtual/flat": ["es.array.flat", "es.array.unscopables.flat"], "core-js/es/array/virtual/flat-map": ["es.array.flat-map", "es.array.unscopables.flat-map"], "core-js/es/array/virtual/for-each": ["es.array.for-each"], "core-js/es/array/virtual/includes": ["es.array.includes"], "core-js/es/array/virtual/index-of": ["es.array.index-of"], "core-js/es/array/virtual/iterator": ["es.array.iterator"], "core-js/es/array/virtual/join": ["es.array.join"], "core-js/es/array/virtual/keys": ["es.array.iterator"], "core-js/es/array/virtual/last-index-of": ["es.array.last-index-of"], "core-js/es/array/virtual/map": ["es.array.map"], "core-js/es/array/virtual/reduce": ["es.array.reduce"], "core-js/es/array/virtual/reduce-right": ["es.array.reduce-right"], "core-js/es/array/virtual/reverse": ["es.array.reverse"], "core-js/es/array/virtual/slice": ["es.array.slice"], "core-js/es/array/virtual/some": ["es.array.some"], "core-js/es/array/virtual/sort": ["es.array.sort"], "core-js/es/array/virtual/splice": ["es.array.splice"], "core-js/es/array/virtual/values": ["es.array.iterator"], "core-js/es/data-view": ["es.data-view", "es.object.to-string"], "core-js/es/date": ["es.date.now", "es.date.to-iso-string", "es.date.to-json", "es.date.to-primitive", "es.date.to-string"], "core-js/es/date/now": ["es.date.now"], "core-js/es/date/to-iso-string": ["es.date.to-iso-string", "es.date.to-json"], "core-js/es/date/to-json": ["es.date.to-json"], "core-js/es/date/to-primitive": ["es.date.to-primitive"], "core-js/es/date/to-string": ["es.date.to-string"], "core-js/es/function": ["es.function.bind", "es.function.has-instance", "es.function.name"], "core-js/es/function/bind": ["es.function.bind"], "core-js/es/function/has-instance": ["es.function.has-instance"], "core-js/es/function/name": ["es.function.name"], "core-js/es/function/virtual": ["es.function.bind"], "core-js/es/function/virtual/bind": ["es.function.bind"], "core-js/es/global-this": ["es.global-this"], "core-js/es/instance/bind": ["es.function.bind"], "core-js/es/instance/code-point-at": ["es.string.code-point-at"], "core-js/es/instance/concat": ["es.array.concat"], "core-js/es/instance/copy-within": ["es.array.copy-within"], "core-js/es/instance/ends-with": ["es.string.ends-with"], "core-js/es/instance/entries": ["es.array.iterator"], "core-js/es/instance/every": ["es.array.every"], "core-js/es/instance/fill": ["es.array.fill"], "core-js/es/instance/filter": ["es.array.filter"], "core-js/es/instance/find": ["es.array.find"], "core-js/es/instance/find-index": ["es.array.find-index"], "core-js/es/instance/flags": ["es.regexp.flags"], "core-js/es/instance/flat": ["es.array.flat", "es.array.unscopables.flat"], "core-js/es/instance/flat-map": ["es.array.flat-map", "es.array.unscopables.flat-map"], "core-js/es/instance/for-each": ["es.array.for-each"], "core-js/es/instance/includes": ["es.array.includes", "es.string.includes"], "core-js/es/instance/index-of": ["es.array.index-of"], "core-js/es/instance/keys": ["es.array.iterator"], "core-js/es/instance/last-index-of": ["es.array.last-index-of"], "core-js/es/instance/map": ["es.array.map"], "core-js/es/instance/match-all": ["es.string.match-all"], "core-js/es/instance/pad-end": ["es.string.pad-end"], "core-js/es/instance/pad-start": ["es.string.pad-start"], "core-js/es/instance/reduce": ["es.array.reduce"], "core-js/es/instance/reduce-right": ["es.array.reduce-right"], "core-js/es/instance/repeat": ["es.string.repeat"], "core-js/es/instance/reverse": ["es.array.reverse"], "core-js/es/instance/slice": ["es.array.slice"], "core-js/es/instance/some": ["es.array.some"], "core-js/es/instance/sort": ["es.array.sort"], "core-js/es/instance/splice": ["es.array.splice"], "core-js/es/instance/starts-with": ["es.string.starts-with"], "core-js/es/instance/trim": ["es.string.trim"], "core-js/es/instance/trim-end": ["es.string.trim-end"], "core-js/es/instance/trim-left": ["es.string.trim-start"], "core-js/es/instance/trim-right": ["es.string.trim-end"], "core-js/es/instance/trim-start": ["es.string.trim-start"], "core-js/es/instance/values": ["es.array.iterator"], "core-js/es/json": ["es.json.stringify", "es.json.to-string-tag"], "core-js/es/json/stringify": ["es.json.stringify"], "core-js/es/json/to-string-tag": ["es.json.to-string-tag"], "core-js/es/map": ["es.map", "es.object.to-string", "es.string.iterator", "web.dom-collections.iterator"], "core-js/es/math": ["es.math.acosh", "es.math.asinh", "es.math.atanh", "es.math.cbrt", "es.math.clz32", "es.math.cosh", "es.math.expm1", "es.math.fround", "es.math.hypot", "es.math.imul", "es.math.log10", "es.math.log1p", "es.math.log2", "es.math.sign", "es.math.sinh", "es.math.tanh", "es.math.to-string-tag", "es.math.trunc"], "core-js/es/math/acosh": ["es.math.acosh"], "core-js/es/math/asinh": ["es.math.asinh"], "core-js/es/math/atanh": ["es.math.atanh"], "core-js/es/math/cbrt": ["es.math.cbrt"], "core-js/es/math/clz32": ["es.math.clz32"], "core-js/es/math/cosh": ["es.math.cosh"], "core-js/es/math/expm1": ["es.math.expm1"], "core-js/es/math/fround": ["es.math.fround"], "core-js/es/math/hypot": ["es.math.hypot"], "core-js/es/math/imul": ["es.math.imul"], "core-js/es/math/log10": ["es.math.log10"], "core-js/es/math/log1p": ["es.math.log1p"], "core-js/es/math/log2": ["es.math.log2"], "core-js/es/math/sign": ["es.math.sign"], "core-js/es/math/sinh": ["es.math.sinh"], "core-js/es/math/tanh": ["es.math.tanh"], "core-js/es/math/to-string-tag": ["es.math.to-string-tag"], "core-js/es/math/trunc": ["es.math.trunc"], "core-js/es/number": ["es.number.constructor", "es.number.epsilon", "es.number.is-finite", "es.number.is-integer", "es.number.is-nan", "es.number.is-safe-integer", "es.number.max-safe-integer", "es.number.min-safe-integer", "es.number.parse-float", "es.number.parse-int", "es.number.to-fixed", "es.number.to-precision"], "core-js/es/number/constructor": ["es.number.constructor"], "core-js/es/number/epsilon": ["es.number.epsilon"], "core-js/es/number/is-finite": ["es.number.is-finite"], "core-js/es/number/is-integer": ["es.number.is-integer"], "core-js/es/number/is-nan": ["es.number.is-nan"], "core-js/es/number/is-safe-integer": ["es.number.is-safe-integer"], "core-js/es/number/max-safe-integer": ["es.number.max-safe-integer"], "core-js/es/number/min-safe-integer": ["es.number.min-safe-integer"], "core-js/es/number/parse-float": ["es.number.parse-float"], "core-js/es/number/parse-int": ["es.number.parse-int"], "core-js/es/number/to-fixed": ["es.number.to-fixed"], "core-js/es/number/to-precision": ["es.number.to-precision"], "core-js/es/number/virtual": ["es.number.to-fixed", "es.number.to-precision"], "core-js/es/number/virtual/to-fixed": ["es.number.to-fixed"], "core-js/es/number/virtual/to-precision": ["es.number.to-precision"], "core-js/es/object": ["es.symbol", "es.json.to-string-tag", "es.math.to-string-tag", "es.object.assign", "es.object.create", "es.object.define-getter", "es.object.define-properties", "es.object.define-property", "es.object.define-setter", "es.object.entries", "es.object.freeze", "es.object.from-entries", "es.object.get-own-property-descriptor", "es.object.get-own-property-descriptors", "es.object.get-own-property-names", "es.object.get-prototype-of", "es.object.is", "es.object.is-extensible", "es.object.is-frozen", "es.object.is-sealed", "es.object.keys", "es.object.lookup-getter", "es.object.lookup-setter", "es.object.prevent-extensions", "es.object.seal", "es.object.set-prototype-of", "es.object.to-string", "es.object.values"], "core-js/es/object/assign": ["es.object.assign"], "core-js/es/object/create": ["es.object.create"], "core-js/es/object/define-getter": ["es.object.define-getter"], "core-js/es/object/define-properties": ["es.object.define-properties"], "core-js/es/object/define-property": ["es.object.define-property"], "core-js/es/object/define-setter": ["es.object.define-setter"], "core-js/es/object/entries": ["es.object.entries"], "core-js/es/object/freeze": ["es.object.freeze"], "core-js/es/object/from-entries": ["es.array.iterator", "es.object.from-entries"], "core-js/es/object/get-own-property-descriptor": ["es.object.get-own-property-descriptor"], "core-js/es/object/get-own-property-descriptors": ["es.object.get-own-property-descriptors"], "core-js/es/object/get-own-property-names": ["es.object.get-own-property-names"], "core-js/es/object/get-own-property-symbols": ["es.symbol"], "core-js/es/object/get-prototype-of": ["es.object.get-prototype-of"], "core-js/es/object/is": ["es.object.is"], "core-js/es/object/is-extensible": ["es.object.is-extensible"], "core-js/es/object/is-frozen": ["es.object.is-frozen"], "core-js/es/object/is-sealed": ["es.object.is-sealed"], "core-js/es/object/keys": ["es.object.keys"], "core-js/es/object/lookup-getter": ["es.object.lookup-setter"], "core-js/es/object/lookup-setter": ["es.object.lookup-setter"], "core-js/es/object/prevent-extensions": ["es.object.prevent-extensions"], "core-js/es/object/seal": ["es.object.seal"], "core-js/es/object/set-prototype-of": ["es.object.set-prototype-of"], "core-js/es/object/to-string": ["es.json.to-string-tag", "es.math.to-string-tag", "es.object.to-string"], "core-js/es/object/values": ["es.object.values"], "core-js/es/parse-float": ["es.parse-float"], "core-js/es/parse-int": ["es.parse-int"], "core-js/es/promise": ["es.object.to-string", "es.promise", "es.promise.all-settled", "es.promise.finally", "es.string.iterator", "web.dom-collections.iterator"], "core-js/es/promise/all-settled": ["es.promise", "es.promise.all-settled"], "core-js/es/promise/finally": ["es.promise", "es.promise.finally"], "core-js/es/reflect": ["es.reflect.apply", "es.reflect.construct", "es.reflect.define-property", "es.reflect.delete-property", "es.reflect.get", "es.reflect.get-own-property-descriptor", "es.reflect.get-prototype-of", "es.reflect.has", "es.reflect.is-extensible", "es.reflect.own-keys", "es.reflect.prevent-extensions", "es.reflect.set", "es.reflect.set-prototype-of"], "core-js/es/reflect/apply": ["es.reflect.apply"], "core-js/es/reflect/construct": ["es.reflect.construct"], "core-js/es/reflect/define-property": ["es.reflect.define-property"], "core-js/es/reflect/delete-property": ["es.reflect.delete-property"], "core-js/es/reflect/get": ["es.reflect.get"], "core-js/es/reflect/get-own-property-descriptor": ["es.reflect.get-own-property-descriptor"], "core-js/es/reflect/get-prototype-of": ["es.reflect.get-prototype-of"], "core-js/es/reflect/has": ["es.reflect.has"], "core-js/es/reflect/is-extensible": ["es.reflect.is-extensible"], "core-js/es/reflect/own-keys": ["es.reflect.own-keys"], "core-js/es/reflect/prevent-extensions": ["es.reflect.prevent-extensions"], "core-js/es/reflect/set": ["es.reflect.set"], "core-js/es/reflect/set-prototype-of": ["es.reflect.set-prototype-of"], "core-js/es/regexp": ["es.regexp.constructor", "es.regexp.exec", "es.regexp.flags", "es.regexp.sticky", "es.regexp.test", "es.regexp.to-string", "es.string.match", "es.string.replace", "es.string.search", "es.string.split"], "core-js/es/regexp/constructor": ["es.regexp.constructor"], "core-js/es/regexp/flags": ["es.regexp.flags"], "core-js/es/regexp/match": ["es.string.match"], "core-js/es/regexp/replace": ["es.string.replace"], "core-js/es/regexp/search": ["es.string.search"], "core-js/es/regexp/split": ["es.string.split"], "core-js/es/regexp/sticky": ["es.regexp.sticky"], "core-js/es/regexp/test": ["es.regexp.exec", "es.regexp.test"], "core-js/es/regexp/to-string": ["es.regexp.to-string"], "core-js/es/set": ["es.object.to-string", "es.set", "es.string.iterator", "web.dom-collections.iterator"], "core-js/es/string": ["es.regexp.exec", "es.string.code-point-at", "es.string.ends-with", "es.string.from-code-point", "es.string.includes", "es.string.iterator", "es.string.match", "es.string.match-all", "es.string.pad-end", "es.string.pad-start", "es.string.raw", "es.string.repeat", "es.string.replace", "es.string.search", "es.string.split", "es.string.starts-with", "es.string.trim", "es.string.trim-end", "es.string.trim-start", "es.string.anchor", "es.string.big", "es.string.blink", "es.string.bold", "es.string.fixed", "es.string.fontcolor", "es.string.fontsize", "es.string.italics", "es.string.link", "es.string.small", "es.string.strike", "es.string.sub", "es.string.sup"], "core-js/es/string/anchor": ["es.string.anchor"], "core-js/es/string/big": ["es.string.big"], "core-js/es/string/blink": ["es.string.blink"], "core-js/es/string/bold": ["es.string.bold"], "core-js/es/string/code-point-at": ["es.string.code-point-at"], "core-js/es/string/ends-with": ["es.string.ends-with"], "core-js/es/string/fixed": ["es.string.fixed"], "core-js/es/string/fontcolor": ["es.string.fontcolor"], "core-js/es/string/fontsize": ["es.string.fontsize"], "core-js/es/string/from-code-point": ["es.string.from-code-point"], "core-js/es/string/includes": ["es.string.includes"], "core-js/es/string/italics": ["es.string.italics"], "core-js/es/string/iterator": ["es.string.iterator"], "core-js/es/string/link": ["es.string.link"], "core-js/es/string/match": ["es.regexp.exec", "es.string.match"], "core-js/es/string/match-all": ["es.string.match-all"], "core-js/es/string/pad-end": ["es.string.pad-end"], "core-js/es/string/pad-start": ["es.string.pad-start"], "core-js/es/string/raw": ["es.string.raw"], "core-js/es/string/repeat": ["es.string.repeat"], "core-js/es/string/replace": ["es.regexp.exec", "es.string.replace"], "core-js/es/string/search": ["es.regexp.exec", "es.string.search"], "core-js/es/string/small": ["es.string.small"], "core-js/es/string/split": ["es.regexp.exec", "es.string.split"], "core-js/es/string/starts-with": ["es.string.starts-with"], "core-js/es/string/strike": ["es.string.strike"], "core-js/es/string/sub": ["es.string.sub"], "core-js/es/string/sup": ["es.string.sup"], "core-js/es/string/trim": ["es.string.trim"], "core-js/es/string/trim-end": ["es.string.trim-end"], "core-js/es/string/trim-left": ["es.string.trim-start"], "core-js/es/string/trim-right": ["es.string.trim-end"], "core-js/es/string/trim-start": ["es.string.trim-start"], "core-js/es/string/virtual": ["es.string.code-point-at", "es.string.ends-with", "es.string.includes", "es.string.iterator", "es.string.match", "es.string.match-all", "es.string.pad-end", "es.string.pad-start", "es.string.repeat", "es.string.replace", "es.string.search", "es.string.split", "es.string.starts-with", "es.string.trim", "es.string.trim-end", "es.string.trim-start", "es.string.anchor", "es.string.big", "es.string.blink", "es.string.bold", "es.string.fixed", "es.string.fontcolor", "es.string.fontsize", "es.string.italics", "es.string.link", "es.string.small", "es.string.strike", "es.string.sub", "es.string.sup"], "core-js/es/string/virtual/anchor": ["es.string.anchor"], "core-js/es/string/virtual/big": ["es.string.big"], "core-js/es/string/virtual/blink": ["es.string.blink"], "core-js/es/string/virtual/bold": ["es.string.bold"], "core-js/es/string/virtual/code-point-at": ["es.string.code-point-at"], "core-js/es/string/virtual/ends-with": ["es.string.ends-with"], "core-js/es/string/virtual/fixed": ["es.string.fixed"], "core-js/es/string/virtual/fontcolor": ["es.string.fontcolor"], "core-js/es/string/virtual/fontsize": ["es.string.fontsize"], "core-js/es/string/virtual/includes": ["es.string.includes"], "core-js/es/string/virtual/italics": ["es.string.italics"], "core-js/es/string/virtual/iterator": ["es.string.iterator"], "core-js/es/string/virtual/link": ["es.string.link"], "core-js/es/string/virtual/match-all": ["es.string.match-all"], "core-js/es/string/virtual/pad-end": ["es.string.pad-end"], "core-js/es/string/virtual/pad-start": ["es.string.pad-start"], "core-js/es/string/virtual/repeat": ["es.string.repeat"], "core-js/es/string/virtual/small": ["es.string.small"], "core-js/es/string/virtual/starts-with": ["es.string.starts-with"], "core-js/es/string/virtual/strike": ["es.string.strike"], "core-js/es/string/virtual/sub": ["es.string.sub"], "core-js/es/string/virtual/sup": ["es.string.sup"], "core-js/es/string/virtual/trim": ["es.string.trim"], "core-js/es/string/virtual/trim-end": ["es.string.trim-end"], "core-js/es/string/virtual/trim-left": ["es.string.trim-start"], "core-js/es/string/virtual/trim-right": ["es.string.trim-end"], "core-js/es/string/virtual/trim-start": ["es.string.trim-start"], "core-js/es/symbol": ["es.symbol", "es.symbol.description", "es.symbol.async-iterator", "es.symbol.has-instance", "es.symbol.is-concat-spreadable", "es.symbol.iterator", "es.symbol.match", "es.symbol.match-all", "es.symbol.replace", "es.symbol.search", "es.symbol.species", "es.symbol.split", "es.symbol.to-primitive", "es.symbol.to-string-tag", "es.symbol.unscopables", "es.array.concat", "es.json.to-string-tag", "es.math.to-string-tag", "es.object.to-string"], "core-js/es/symbol/async-iterator": ["es.symbol.async-iterator"], "core-js/es/symbol/description": ["es.symbol.description"], "core-js/es/symbol/for": ["es.symbol"], "core-js/es/symbol/has-instance": ["es.symbol.has-instance", "es.function.has-instance"], "core-js/es/symbol/is-concat-spreadable": ["es.symbol.is-concat-spreadable", "es.array.concat"], "core-js/es/symbol/iterator": ["es.symbol.iterator", "es.string.iterator", "web.dom-collections.iterator"], "core-js/es/symbol/key-for": ["es.symbol"], "core-js/es/symbol/match": ["es.symbol.match", "es.string.match"], "core-js/es/symbol/match-all": ["es.symbol.match-all", "es.string.match-all"], "core-js/es/symbol/replace": ["es.symbol.replace", "es.string.replace"], "core-js/es/symbol/search": ["es.symbol.search", "es.string.search"], "core-js/es/symbol/species": ["es.symbol.species"], "core-js/es/symbol/split": ["es.symbol.split", "es.string.split"], "core-js/es/symbol/to-primitive": ["es.symbol.to-primitive"], "core-js/es/symbol/to-string-tag": ["es.symbol.to-string-tag", "es.json.to-string-tag", "es.math.to-string-tag", "es.object.to-string"], "core-js/es/symbol/unscopables": ["es.symbol.unscopables"], "core-js/es/typed-array": ["es.object.to-string", "es.typed-array.float32-array", "es.typed-array.float64-array", "es.typed-array.int8-array", "es.typed-array.int16-array", "es.typed-array.int32-array", "es.typed-array.uint8-array", "es.typed-array.uint8-clamped-array", "es.typed-array.uint16-array", "es.typed-array.uint32-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/es/typed-array/copy-within": ["es.typed-array.copy-within"], "core-js/es/typed-array/entries": ["es.typed-array.iterator"], "core-js/es/typed-array/every": ["es.typed-array.every"], "core-js/es/typed-array/fill": ["es.typed-array.fill"], "core-js/es/typed-array/filter": ["es.typed-array.filter"], "core-js/es/typed-array/find": ["es.typed-array.find"], "core-js/es/typed-array/find-index": ["es.typed-array.find-index"], "core-js/es/typed-array/float32-array": ["es.object.to-string", "es.typed-array.float32-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/es/typed-array/float64-array": ["es.object.to-string", "es.typed-array.float64-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/es/typed-array/for-each": ["es.typed-array.for-each"], "core-js/es/typed-array/from": ["es.typed-array.from"], "core-js/es/typed-array/includes": ["es.typed-array.includes"], "core-js/es/typed-array/index-of": ["es.typed-array.index-of"], "core-js/es/typed-array/int16-array": ["es.object.to-string", "es.typed-array.int16-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/es/typed-array/int32-array": ["es.object.to-string", "es.typed-array.int32-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/es/typed-array/int8-array": ["es.object.to-string", "es.typed-array.int8-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/es/typed-array/iterator": ["es.typed-array.iterator"], "core-js/es/typed-array/join": ["es.typed-array.join"], "core-js/es/typed-array/keys": ["es.typed-array.iterator"], "core-js/es/typed-array/last-index-of": ["es.typed-array.last-index-of"], "core-js/es/typed-array/map": ["es.typed-array.map"], "core-js/es/typed-array/methods": ["es.object.to-string", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/es/typed-array/of": ["es.typed-array.of"], "core-js/es/typed-array/reduce": ["es.typed-array.reduce"], "core-js/es/typed-array/reduce-right": ["es.typed-array.reduce-right"], "core-js/es/typed-array/reverse": ["es.typed-array.reverse"], "core-js/es/typed-array/set": ["es.typed-array.set"], "core-js/es/typed-array/slice": ["es.typed-array.slice"], "core-js/es/typed-array/some": ["es.typed-array.some"], "core-js/es/typed-array/sort": ["es.typed-array.sort"], "core-js/es/typed-array/subarray": ["es.typed-array.subarray"], "core-js/es/typed-array/to-locale-string": ["es.typed-array.to-locale-string"], "core-js/es/typed-array/to-string": ["es.typed-array.to-string"], "core-js/es/typed-array/uint16-array": ["es.object.to-string", "es.typed-array.uint16-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/es/typed-array/uint32-array": ["es.object.to-string", "es.typed-array.uint32-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/es/typed-array/uint8-array": ["es.object.to-string", "es.typed-array.uint8-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/es/typed-array/uint8-clamped-array": ["es.object.to-string", "es.typed-array.uint8-clamped-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/es/typed-array/values": ["es.typed-array.iterator"], "core-js/es/weak-map": ["es.object.to-string", "es.weak-map", "web.dom-collections.iterator"], "core-js/es/weak-set": ["es.object.to-string", "es.weak-set", "web.dom-collections.iterator"], "core-js/features": ["es.symbol", "es.symbol.description", "es.symbol.async-iterator", "es.symbol.has-instance", "es.symbol.is-concat-spreadable", "es.symbol.iterator", "es.symbol.match", "es.symbol.match-all", "es.symbol.replace", "es.symbol.search", "es.symbol.species", "es.symbol.split", "es.symbol.to-primitive", "es.symbol.to-string-tag", "es.symbol.unscopables", "es.array.concat", "es.array.copy-within", "es.array.every", "es.array.fill", "es.array.filter", "es.array.find", "es.array.find-index", "es.array.flat", "es.array.flat-map", "es.array.for-each", "es.array.from", "es.array.includes", "es.array.index-of", "es.array.is-array", "es.array.iterator", "es.array.join", "es.array.last-index-of", "es.array.map", "es.array.of", "es.array.reduce", "es.array.reduce-right", "es.array.reverse", "es.array.slice", "es.array.some", "es.array.sort", "es.array.species", "es.array.splice", "es.array.unscopables.flat", "es.array.unscopables.flat-map", "es.array-buffer.constructor", "es.array-buffer.is-view", "es.array-buffer.slice", "es.data-view", "es.date.now", "es.date.to-iso-string", "es.date.to-json", "es.date.to-primitive", "es.date.to-string", "es.function.bind", "es.function.has-instance", "es.function.name", "es.global-this", "es.json.stringify", "es.json.to-string-tag", "es.map", "es.math.acosh", "es.math.asinh", "es.math.atanh", "es.math.cbrt", "es.math.clz32", "es.math.cosh", "es.math.expm1", "es.math.fround", "es.math.hypot", "es.math.imul", "es.math.log10", "es.math.log1p", "es.math.log2", "es.math.sign", "es.math.sinh", "es.math.tanh", "es.math.to-string-tag", "es.math.trunc", "es.number.constructor", "es.number.epsilon", "es.number.is-finite", "es.number.is-integer", "es.number.is-nan", "es.number.is-safe-integer", "es.number.max-safe-integer", "es.number.min-safe-integer", "es.number.parse-float", "es.number.parse-int", "es.number.to-fixed", "es.number.to-precision", "es.object.assign", "es.object.create", "es.object.define-getter", "es.object.define-properties", "es.object.define-property", "es.object.define-setter", "es.object.entries", "es.object.freeze", "es.object.from-entries", "es.object.get-own-property-descriptor", "es.object.get-own-property-descriptors", "es.object.get-own-property-names", "es.object.get-prototype-of", "es.object.is", "es.object.is-extensible", "es.object.is-frozen", "es.object.is-sealed", "es.object.keys", "es.object.lookup-getter", "es.object.lookup-setter", "es.object.prevent-extensions", "es.object.seal", "es.object.set-prototype-of", "es.object.to-string", "es.object.values", "es.parse-float", "es.parse-int", "es.promise", "es.promise.all-settled", "es.promise.finally", "es.reflect.apply", "es.reflect.construct", "es.reflect.define-property", "es.reflect.delete-property", "es.reflect.get", "es.reflect.get-own-property-descriptor", "es.reflect.get-prototype-of", "es.reflect.has", "es.reflect.is-extensible", "es.reflect.own-keys", "es.reflect.prevent-extensions", "es.reflect.set", "es.reflect.set-prototype-of", "es.regexp.constructor", "es.regexp.exec", "es.regexp.flags", "es.regexp.sticky", "es.regexp.test", "es.regexp.to-string", "es.set", "es.string.code-point-at", "es.string.ends-with", "es.string.from-code-point", "es.string.includes", "es.string.iterator", "es.string.match", "es.string.match-all", "es.string.pad-end", "es.string.pad-start", "es.string.raw", "es.string.repeat", "es.string.replace", "es.string.search", "es.string.split", "es.string.starts-with", "es.string.trim", "es.string.trim-end", "es.string.trim-start", "es.string.anchor", "es.string.big", "es.string.blink", "es.string.bold", "es.string.fixed", "es.string.fontcolor", "es.string.fontsize", "es.string.italics", "es.string.link", "es.string.small", "es.string.strike", "es.string.sub", "es.string.sup", "es.typed-array.float32-array", "es.typed-array.float64-array", "es.typed-array.int8-array", "es.typed-array.int16-array", "es.typed-array.int32-array", "es.typed-array.uint8-array", "es.typed-array.uint8-clamped-array", "es.typed-array.uint16-array", "es.typed-array.uint32-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string", "es.weak-map", "es.weak-set", "esnext.aggregate-error", "esnext.array.is-template-object", "esnext.array.last-index", "esnext.array.last-item", "esnext.async-iterator.constructor", "esnext.async-iterator.as-indexed-pairs", "esnext.async-iterator.drop", "esnext.async-iterator.every", "esnext.async-iterator.filter", "esnext.async-iterator.find", "esnext.async-iterator.flat-map", "esnext.async-iterator.for-each", "esnext.async-iterator.from", "esnext.async-iterator.map", "esnext.async-iterator.reduce", "esnext.async-iterator.some", "esnext.async-iterator.take", "esnext.async-iterator.to-array", "esnext.composite-key", "esnext.composite-symbol", "esnext.global-this", "esnext.iterator.constructor", "esnext.iterator.as-indexed-pairs", "esnext.iterator.drop", "esnext.iterator.every", "esnext.iterator.filter", "esnext.iterator.find", "esnext.iterator.flat-map", "esnext.iterator.for-each", "esnext.iterator.from", "esnext.iterator.map", "esnext.iterator.reduce", "esnext.iterator.some", "esnext.iterator.take", "esnext.iterator.to-array", "esnext.map.delete-all", "esnext.map.every", "esnext.map.filter", "esnext.map.find", "esnext.map.find-key", "esnext.map.from", "esnext.map.group-by", "esnext.map.includes", "esnext.map.key-by", "esnext.map.key-of", "esnext.map.map-keys", "esnext.map.map-values", "esnext.map.merge", "esnext.map.of", "esnext.map.reduce", "esnext.map.some", "esnext.map.update", "esnext.map.update-or-insert", "esnext.map.upsert", "esnext.math.clamp", "esnext.math.deg-per-rad", "esnext.math.degrees", "esnext.math.fscale", "esnext.math.iaddh", "esnext.math.imulh", "esnext.math.isubh", "esnext.math.rad-per-deg", "esnext.math.radians", "esnext.math.scale", "esnext.math.seeded-prng", "esnext.math.signbit", "esnext.math.umulh", "esnext.number.from-string", "esnext.object.iterate-entries", "esnext.object.iterate-keys", "esnext.object.iterate-values", "esnext.observable", "esnext.promise.all-settled", "esnext.promise.any", "esnext.promise.try", "esnext.reflect.define-metadata", "esnext.reflect.delete-metadata", "esnext.reflect.get-metadata", "esnext.reflect.get-metadata-keys", "esnext.reflect.get-own-metadata", "esnext.reflect.get-own-metadata-keys", "esnext.reflect.has-metadata", "esnext.reflect.has-own-metadata", "esnext.reflect.metadata", "esnext.set.add-all", "esnext.set.delete-all", "esnext.set.difference", "esnext.set.every", "esnext.set.filter", "esnext.set.find", "esnext.set.from", "esnext.set.intersection", "esnext.set.is-disjoint-from", "esnext.set.is-subset-of", "esnext.set.is-superset-of", "esnext.set.join", "esnext.set.map", "esnext.set.of", "esnext.set.reduce", "esnext.set.some", "esnext.set.symmetric-difference", "esnext.set.union", "esnext.string.at", "esnext.string.code-points", "esnext.string.match-all", "esnext.string.replace-all", "esnext.symbol.async-dispose", "esnext.symbol.dispose", "esnext.symbol.observable", "esnext.symbol.pattern-match", "esnext.symbol.replace-all", "esnext.weak-map.delete-all", "esnext.weak-map.from", "esnext.weak-map.of", "esnext.weak-map.upsert", "esnext.weak-set.add-all", "esnext.weak-set.delete-all", "esnext.weak-set.from", "esnext.weak-set.of", "web.dom-collections.for-each", "web.dom-collections.iterator", "web.immediate", "web.queue-microtask", "web.timers", "web.url", "web.url.to-json", "web.url-search-params"], "core-js/features/aggregate-error": ["es.string.iterator", "esnext.aggregate-error", "web.dom-collections.iterator"], "core-js/features/array": ["es.array.concat", "es.array.copy-within", "es.array.every", "es.array.fill", "es.array.filter", "es.array.find", "es.array.find-index", "es.array.flat", "es.array.flat-map", "es.array.for-each", "es.array.from", "es.array.includes", "es.array.index-of", "es.array.is-array", "es.array.iterator", "es.array.join", "es.array.last-index-of", "es.array.map", "es.array.of", "es.array.reduce", "es.array.reduce-right", "es.array.reverse", "es.array.slice", "es.array.some", "es.array.sort", "es.array.species", "es.array.splice", "es.array.unscopables.flat", "es.array.unscopables.flat-map", "es.string.iterator", "esnext.array.is-template-object", "esnext.array.last-index", "esnext.array.last-item"], "core-js/features/array-buffer": ["es.array-buffer.constructor", "es.array-buffer.is-view", "es.array-buffer.slice", "es.object.to-string"], "core-js/features/array-buffer/constructor": ["es.array-buffer.constructor", "es.object.to-string"], "core-js/features/array-buffer/is-view": ["es.array-buffer.is-view"], "core-js/features/array-buffer/slice": ["es.array-buffer.slice"], "core-js/features/array/concat": ["es.array.concat"], "core-js/features/array/copy-within": ["es.array.copy-within"], "core-js/features/array/entries": ["es.array.iterator"], "core-js/features/array/every": ["es.array.every"], "core-js/features/array/fill": ["es.array.fill"], "core-js/features/array/filter": ["es.array.filter"], "core-js/features/array/find": ["es.array.find"], "core-js/features/array/find-index": ["es.array.find-index"], "core-js/features/array/flat": ["es.array.flat", "es.array.unscopables.flat"], "core-js/features/array/flat-map": ["es.array.flat-map", "es.array.unscopables.flat-map"], "core-js/features/array/for-each": ["es.array.for-each"], "core-js/features/array/from": ["es.array.from", "es.string.iterator"], "core-js/features/array/includes": ["es.array.includes"], "core-js/features/array/index-of": ["es.array.index-of"], "core-js/features/array/is-array": ["es.array.is-array"], "core-js/features/array/is-template-object": ["esnext.array.is-template-object"], "core-js/features/array/iterator": ["es.array.iterator"], "core-js/features/array/join": ["es.array.join"], "core-js/features/array/keys": ["es.array.iterator"], "core-js/features/array/last-index": ["esnext.array.last-index"], "core-js/features/array/last-index-of": ["es.array.last-index-of"], "core-js/features/array/last-item": ["esnext.array.last-item"], "core-js/features/array/map": ["es.array.map"], "core-js/features/array/of": ["es.array.of"], "core-js/features/array/reduce": ["es.array.reduce"], "core-js/features/array/reduce-right": ["es.array.reduce-right"], "core-js/features/array/reverse": ["es.array.reverse"], "core-js/features/array/slice": ["es.array.slice"], "core-js/features/array/some": ["es.array.some"], "core-js/features/array/sort": ["es.array.sort"], "core-js/features/array/splice": ["es.array.splice"], "core-js/features/array/values": ["es.array.iterator"], "core-js/features/array/virtual": ["es.array.concat", "es.array.copy-within", "es.array.every", "es.array.fill", "es.array.filter", "es.array.find", "es.array.find-index", "es.array.flat", "es.array.flat-map", "es.array.for-each", "es.array.includes", "es.array.index-of", "es.array.iterator", "es.array.join", "es.array.last-index-of", "es.array.map", "es.array.reduce", "es.array.reduce-right", "es.array.reverse", "es.array.slice", "es.array.some", "es.array.sort", "es.array.species", "es.array.splice", "es.array.unscopables.flat", "es.array.unscopables.flat-map"], "core-js/features/array/virtual/concat": ["es.array.concat"], "core-js/features/array/virtual/copy-within": ["es.array.copy-within"], "core-js/features/array/virtual/entries": ["es.array.iterator"], "core-js/features/array/virtual/every": ["es.array.every"], "core-js/features/array/virtual/fill": ["es.array.fill"], "core-js/features/array/virtual/filter": ["es.array.filter"], "core-js/features/array/virtual/find": ["es.array.find"], "core-js/features/array/virtual/find-index": ["es.array.find-index"], "core-js/features/array/virtual/flat": ["es.array.flat", "es.array.unscopables.flat"], "core-js/features/array/virtual/flat-map": ["es.array.flat-map", "es.array.unscopables.flat-map"], "core-js/features/array/virtual/for-each": ["es.array.for-each"], "core-js/features/array/virtual/includes": ["es.array.includes"], "core-js/features/array/virtual/index-of": ["es.array.index-of"], "core-js/features/array/virtual/iterator": ["es.array.iterator"], "core-js/features/array/virtual/join": ["es.array.join"], "core-js/features/array/virtual/keys": ["es.array.iterator"], "core-js/features/array/virtual/last-index-of": ["es.array.last-index-of"], "core-js/features/array/virtual/map": ["es.array.map"], "core-js/features/array/virtual/reduce": ["es.array.reduce"], "core-js/features/array/virtual/reduce-right": ["es.array.reduce-right"], "core-js/features/array/virtual/reverse": ["es.array.reverse"], "core-js/features/array/virtual/slice": ["es.array.slice"], "core-js/features/array/virtual/some": ["es.array.some"], "core-js/features/array/virtual/sort": ["es.array.sort"], "core-js/features/array/virtual/splice": ["es.array.splice"], "core-js/features/array/virtual/values": ["es.array.iterator"], "core-js/features/async-iterator": ["es.object.to-string", "es.promise", "es.string.iterator", "esnext.async-iterator.constructor", "esnext.async-iterator.as-indexed-pairs", "esnext.async-iterator.drop", "esnext.async-iterator.every", "esnext.async-iterator.filter", "esnext.async-iterator.find", "esnext.async-iterator.flat-map", "esnext.async-iterator.for-each", "esnext.async-iterator.from", "esnext.async-iterator.map", "esnext.async-iterator.reduce", "esnext.async-iterator.some", "esnext.async-iterator.take", "esnext.async-iterator.to-array", "web.dom-collections.iterator"], "core-js/features/async-iterator/as-indexed-pairs": ["es.object.to-string", "es.promise", "es.string.iterator", "esnext.async-iterator.constructor", "esnext.async-iterator.as-indexed-pairs", "web.dom-collections.iterator"], "core-js/features/async-iterator/drop": ["es.object.to-string", "es.promise", "es.string.iterator", "esnext.async-iterator.constructor", "esnext.async-iterator.drop", "web.dom-collections.iterator"], "core-js/features/async-iterator/every": ["es.object.to-string", "es.promise", "es.string.iterator", "esnext.async-iterator.constructor", "esnext.async-iterator.every", "web.dom-collections.iterator"], "core-js/features/async-iterator/filter": ["es.object.to-string", "es.promise", "es.string.iterator", "esnext.async-iterator.constructor", "esnext.async-iterator.filter", "web.dom-collections.iterator"], "core-js/features/async-iterator/find": ["es.object.to-string", "es.promise", "es.string.iterator", "esnext.async-iterator.constructor", "esnext.async-iterator.find", "web.dom-collections.iterator"], "core-js/features/async-iterator/flat-map": ["es.object.to-string", "es.promise", "es.string.iterator", "esnext.async-iterator.constructor", "esnext.async-iterator.flat-map", "web.dom-collections.iterator"], "core-js/features/async-iterator/for-each": ["es.object.to-string", "es.promise", "es.string.iterator", "esnext.async-iterator.constructor", "esnext.async-iterator.for-each", "web.dom-collections.iterator"], "core-js/features/async-iterator/from": ["es.object.to-string", "es.promise", "es.string.iterator", "esnext.async-iterator.constructor", "esnext.async-iterator.from", "web.dom-collections.iterator"], "core-js/features/async-iterator/map": ["es.object.to-string", "es.promise", "es.string.iterator", "esnext.async-iterator.constructor", "esnext.async-iterator.map", "web.dom-collections.iterator"], "core-js/features/async-iterator/reduce": ["es.object.to-string", "es.promise", "es.string.iterator", "esnext.async-iterator.constructor", "esnext.async-iterator.reduce", "web.dom-collections.iterator"], "core-js/features/async-iterator/some": ["es.object.to-string", "es.promise", "es.string.iterator", "esnext.async-iterator.constructor", "esnext.async-iterator.some", "web.dom-collections.iterator"], "core-js/features/async-iterator/take": ["es.object.to-string", "es.promise", "es.string.iterator", "esnext.async-iterator.constructor", "esnext.async-iterator.take", "web.dom-collections.iterator"], "core-js/features/async-iterator/to-array": ["es.object.to-string", "es.promise", "es.string.iterator", "esnext.async-iterator.constructor", "esnext.async-iterator.to-array", "web.dom-collections.iterator"], "core-js/features/clear-immediate": ["web.immediate"], "core-js/features/composite-key": ["esnext.composite-key"], "core-js/features/composite-symbol": ["es.symbol", "esnext.composite-symbol"], "core-js/features/data-view": ["es.data-view", "es.object.to-string"], "core-js/features/date": ["es.date.now", "es.date.to-iso-string", "es.date.to-json", "es.date.to-primitive", "es.date.to-string"], "core-js/features/date/now": ["es.date.now"], "core-js/features/date/to-iso-string": ["es.date.to-iso-string", "es.date.to-json"], "core-js/features/date/to-json": ["es.date.to-json"], "core-js/features/date/to-primitive": ["es.date.to-primitive"], "core-js/features/date/to-string": ["es.date.to-string"], "core-js/features/dom-collections": ["es.array.iterator", "web.dom-collections.for-each", "web.dom-collections.iterator"], "core-js/features/dom-collections/for-each": ["web.dom-collections.for-each"], "core-js/features/dom-collections/iterator": ["web.dom-collections.iterator"], "core-js/features/function": ["es.function.bind", "es.function.has-instance", "es.function.name"], "core-js/features/function/bind": ["es.function.bind"], "core-js/features/function/has-instance": ["es.function.has-instance"], "core-js/features/function/name": ["es.function.name"], "core-js/features/function/virtual": ["es.function.bind"], "core-js/features/function/virtual/bind": ["es.function.bind"], "core-js/features/get-iterator": ["es.string.iterator", "web.dom-collections.iterator"], "core-js/features/get-iterator-method": ["es.string.iterator", "web.dom-collections.iterator"], "core-js/features/global-this": ["es.global-this", "esnext.global-this"], "core-js/features/instance/at": ["esnext.string.at"], "core-js/features/instance/bind": ["es.function.bind"], "core-js/features/instance/code-point-at": ["es.string.code-point-at"], "core-js/features/instance/code-points": ["esnext.string.code-points"], "core-js/features/instance/concat": ["es.array.concat"], "core-js/features/instance/copy-within": ["es.array.copy-within"], "core-js/features/instance/ends-with": ["es.string.ends-with"], "core-js/features/instance/entries": ["es.array.iterator", "web.dom-collections.iterator"], "core-js/features/instance/every": ["es.array.every"], "core-js/features/instance/fill": ["es.array.fill"], "core-js/features/instance/filter": ["es.array.filter"], "core-js/features/instance/find": ["es.array.find"], "core-js/features/instance/find-index": ["es.array.find-index"], "core-js/features/instance/flags": ["es.regexp.flags"], "core-js/features/instance/flat": ["es.array.flat", "es.array.unscopables.flat"], "core-js/features/instance/flat-map": ["es.array.flat-map", "es.array.unscopables.flat-map"], "core-js/features/instance/for-each": ["es.array.for-each", "web.dom-collections.iterator"], "core-js/features/instance/includes": ["es.array.includes", "es.string.includes"], "core-js/features/instance/index-of": ["es.array.index-of"], "core-js/features/instance/keys": ["es.array.iterator", "web.dom-collections.iterator"], "core-js/features/instance/last-index-of": ["es.array.last-index-of"], "core-js/features/instance/map": ["es.array.map"], "core-js/features/instance/match-all": ["es.string.match-all", "esnext.string.match-all"], "core-js/features/instance/pad-end": ["es.string.pad-end"], "core-js/features/instance/pad-start": ["es.string.pad-start"], "core-js/features/instance/reduce": ["es.array.reduce"], "core-js/features/instance/reduce-right": ["es.array.reduce-right"], "core-js/features/instance/repeat": ["es.string.repeat"], "core-js/features/instance/replace-all": ["esnext.string.replace-all"], "core-js/features/instance/reverse": ["es.array.reverse"], "core-js/features/instance/slice": ["es.array.slice"], "core-js/features/instance/some": ["es.array.some"], "core-js/features/instance/sort": ["es.array.sort"], "core-js/features/instance/splice": ["es.array.splice"], "core-js/features/instance/starts-with": ["es.string.starts-with"], "core-js/features/instance/trim": ["es.string.trim"], "core-js/features/instance/trim-end": ["es.string.trim-end"], "core-js/features/instance/trim-left": ["es.string.trim-start"], "core-js/features/instance/trim-right": ["es.string.trim-end"], "core-js/features/instance/trim-start": ["es.string.trim-start"], "core-js/features/instance/values": ["es.array.iterator", "web.dom-collections.iterator"], "core-js/features/is-iterable": ["es.string.iterator", "web.dom-collections.iterator"], "core-js/features/iterator": ["es.object.to-string", "es.string.iterator", "esnext.iterator.constructor", "esnext.iterator.as-indexed-pairs", "esnext.iterator.drop", "esnext.iterator.every", "esnext.iterator.filter", "esnext.iterator.find", "esnext.iterator.flat-map", "esnext.iterator.for-each", "esnext.iterator.from", "esnext.iterator.map", "esnext.iterator.reduce", "esnext.iterator.some", "esnext.iterator.take", "esnext.iterator.to-array", "web.dom-collections.iterator"], "core-js/features/iterator/as-indexed-pairs": ["es.object.to-string", "es.string.iterator", "esnext.iterator.constructor", "esnext.iterator.as-indexed-pairs", "web.dom-collections.iterator"], "core-js/features/iterator/drop": ["es.object.to-string", "es.string.iterator", "esnext.iterator.constructor", "esnext.iterator.drop", "web.dom-collections.iterator"], "core-js/features/iterator/every": ["es.object.to-string", "es.string.iterator", "esnext.iterator.constructor", "esnext.iterator.every", "web.dom-collections.iterator"], "core-js/features/iterator/filter": ["es.object.to-string", "es.string.iterator", "esnext.iterator.constructor", "esnext.iterator.filter", "web.dom-collections.iterator"], "core-js/features/iterator/find": ["es.object.to-string", "es.string.iterator", "esnext.iterator.constructor", "esnext.iterator.find", "web.dom-collections.iterator"], "core-js/features/iterator/flat-map": ["es.object.to-string", "es.string.iterator", "esnext.iterator.constructor", "esnext.iterator.flat-map", "web.dom-collections.iterator"], "core-js/features/iterator/for-each": ["es.object.to-string", "es.string.iterator", "esnext.iterator.constructor", "esnext.iterator.for-each", "web.dom-collections.iterator"], "core-js/features/iterator/from": ["es.object.to-string", "es.string.iterator", "esnext.iterator.constructor", "esnext.iterator.from", "web.dom-collections.iterator"], "core-js/features/iterator/map": ["es.object.to-string", "es.string.iterator", "esnext.iterator.constructor", "esnext.iterator.map", "web.dom-collections.iterator"], "core-js/features/iterator/reduce": ["es.object.to-string", "es.string.iterator", "esnext.iterator.constructor", "esnext.iterator.reduce", "web.dom-collections.iterator"], "core-js/features/iterator/some": ["es.object.to-string", "es.string.iterator", "esnext.iterator.constructor", "esnext.iterator.some", "web.dom-collections.iterator"], "core-js/features/iterator/take": ["es.object.to-string", "es.string.iterator", "esnext.iterator.constructor", "esnext.iterator.take", "web.dom-collections.iterator"], "core-js/features/iterator/to-array": ["es.object.to-string", "es.string.iterator", "esnext.iterator.constructor", "esnext.iterator.to-array", "web.dom-collections.iterator"], "core-js/features/json": ["es.json.stringify", "es.json.to-string-tag"], "core-js/features/json/stringify": ["es.json.stringify"], "core-js/features/json/to-string-tag": ["es.json.to-string-tag"], "core-js/features/map": ["es.map", "es.object.to-string", "es.string.iterator", "esnext.map.delete-all", "esnext.map.every", "esnext.map.filter", "esnext.map.find", "esnext.map.find-key", "esnext.map.from", "esnext.map.group-by", "esnext.map.includes", "esnext.map.key-by", "esnext.map.key-of", "esnext.map.map-keys", "esnext.map.map-values", "esnext.map.merge", "esnext.map.of", "esnext.map.reduce", "esnext.map.some", "esnext.map.update", "esnext.map.update-or-insert", "esnext.map.upsert", "web.dom-collections.iterator"], "core-js/features/map/delete-all": ["es.map", "esnext.map.delete-all"], "core-js/features/map/every": ["es.map", "esnext.map.every"], "core-js/features/map/filter": ["es.map", "esnext.map.filter"], "core-js/features/map/find": ["es.map", "esnext.map.find"], "core-js/features/map/find-key": ["es.map", "esnext.map.find-key"], "core-js/features/map/from": ["es.map", "es.string.iterator", "esnext.map.from", "web.dom-collections.iterator"], "core-js/features/map/group-by": ["es.map", "esnext.map.group-by"], "core-js/features/map/includes": ["es.map", "esnext.map.includes"], "core-js/features/map/key-by": ["es.map", "esnext.map.key-by"], "core-js/features/map/key-of": ["es.map", "esnext.map.key-of"], "core-js/features/map/map-keys": ["es.map", "esnext.map.map-keys"], "core-js/features/map/map-values": ["es.map", "esnext.map.map-values"], "core-js/features/map/merge": ["es.map", "esnext.map.merge"], "core-js/features/map/of": ["es.map", "es.string.iterator", "esnext.map.of", "web.dom-collections.iterator"], "core-js/features/map/reduce": ["es.map", "esnext.map.reduce"], "core-js/features/map/some": ["es.map", "esnext.map.some"], "core-js/features/map/update": ["es.map", "esnext.map.update"], "core-js/features/map/update-or-insert": ["es.map", "esnext.map.update-or-insert"], "core-js/features/map/upsert": ["es.map", "esnext.map.upsert"], "core-js/features/math": ["es.math.acosh", "es.math.asinh", "es.math.atanh", "es.math.cbrt", "es.math.clz32", "es.math.cosh", "es.math.expm1", "es.math.fround", "es.math.hypot", "es.math.imul", "es.math.log10", "es.math.log1p", "es.math.log2", "es.math.sign", "es.math.sinh", "es.math.tanh", "es.math.to-string-tag", "es.math.trunc", "esnext.math.clamp", "esnext.math.deg-per-rad", "esnext.math.degrees", "esnext.math.fscale", "esnext.math.iaddh", "esnext.math.imulh", "esnext.math.isubh", "esnext.math.rad-per-deg", "esnext.math.radians", "esnext.math.scale", "esnext.math.seeded-prng", "esnext.math.signbit", "esnext.math.umulh"], "core-js/features/math/acosh": ["es.math.acosh"], "core-js/features/math/asinh": ["es.math.asinh"], "core-js/features/math/atanh": ["es.math.atanh"], "core-js/features/math/cbrt": ["es.math.cbrt"], "core-js/features/math/clamp": ["esnext.math.clamp"], "core-js/features/math/clz32": ["es.math.clz32"], "core-js/features/math/cosh": ["es.math.cosh"], "core-js/features/math/deg-per-rad": ["esnext.math.deg-per-rad"], "core-js/features/math/degrees": ["esnext.math.degrees"], "core-js/features/math/expm1": ["es.math.expm1"], "core-js/features/math/fround": ["es.math.fround"], "core-js/features/math/fscale": ["esnext.math.fscale"], "core-js/features/math/hypot": ["es.math.hypot"], "core-js/features/math/iaddh": ["esnext.math.iaddh"], "core-js/features/math/imul": ["es.math.imul"], "core-js/features/math/imulh": ["esnext.math.imulh"], "core-js/features/math/isubh": ["esnext.math.isubh"], "core-js/features/math/log10": ["es.math.log10"], "core-js/features/math/log1p": ["es.math.log1p"], "core-js/features/math/log2": ["es.math.log2"], "core-js/features/math/rad-per-deg": ["esnext.math.rad-per-deg"], "core-js/features/math/radians": ["esnext.math.radians"], "core-js/features/math/scale": ["esnext.math.scale"], "core-js/features/math/seeded-prng": ["esnext.math.seeded-prng"], "core-js/features/math/sign": ["es.math.sign"], "core-js/features/math/signbit": ["esnext.math.signbit"], "core-js/features/math/sinh": ["es.math.sinh"], "core-js/features/math/tanh": ["es.math.tanh"], "core-js/features/math/to-string-tag": ["es.math.to-string-tag"], "core-js/features/math/trunc": ["es.math.trunc"], "core-js/features/math/umulh": ["esnext.math.umulh"], "core-js/features/number": ["es.number.constructor", "es.number.epsilon", "es.number.is-finite", "es.number.is-integer", "es.number.is-nan", "es.number.is-safe-integer", "es.number.max-safe-integer", "es.number.min-safe-integer", "es.number.parse-float", "es.number.parse-int", "es.number.to-fixed", "es.number.to-precision", "esnext.number.from-string"], "core-js/features/number/constructor": ["es.number.constructor"], "core-js/features/number/epsilon": ["es.number.epsilon"], "core-js/features/number/from-string": ["esnext.number.from-string"], "core-js/features/number/is-finite": ["es.number.is-finite"], "core-js/features/number/is-integer": ["es.number.is-integer"], "core-js/features/number/is-nan": ["es.number.is-nan"], "core-js/features/number/is-safe-integer": ["es.number.is-safe-integer"], "core-js/features/number/max-safe-integer": ["es.number.max-safe-integer"], "core-js/features/number/min-safe-integer": ["es.number.min-safe-integer"], "core-js/features/number/parse-float": ["es.number.parse-float"], "core-js/features/number/parse-int": ["es.number.parse-int"], "core-js/features/number/to-fixed": ["es.number.to-fixed"], "core-js/features/number/to-precision": ["es.number.to-precision"], "core-js/features/number/virtual": ["es.number.to-fixed", "es.number.to-precision"], "core-js/features/number/virtual/to-fixed": ["es.number.to-fixed"], "core-js/features/number/virtual/to-precision": ["es.number.to-precision"], "core-js/features/object": ["es.symbol", "es.json.to-string-tag", "es.math.to-string-tag", "es.object.assign", "es.object.create", "es.object.define-getter", "es.object.define-properties", "es.object.define-property", "es.object.define-setter", "es.object.entries", "es.object.freeze", "es.object.from-entries", "es.object.get-own-property-descriptor", "es.object.get-own-property-descriptors", "es.object.get-own-property-names", "es.object.get-prototype-of", "es.object.is", "es.object.is-extensible", "es.object.is-frozen", "es.object.is-sealed", "es.object.keys", "es.object.lookup-getter", "es.object.lookup-setter", "es.object.prevent-extensions", "es.object.seal", "es.object.set-prototype-of", "es.object.to-string", "es.object.values", "esnext.object.iterate-entries", "esnext.object.iterate-keys", "esnext.object.iterate-values"], "core-js/features/object/assign": ["es.object.assign"], "core-js/features/object/create": ["es.object.create"], "core-js/features/object/define-getter": ["es.object.define-getter"], "core-js/features/object/define-properties": ["es.object.define-properties"], "core-js/features/object/define-property": ["es.object.define-property"], "core-js/features/object/define-setter": ["es.object.define-setter"], "core-js/features/object/entries": ["es.object.entries"], "core-js/features/object/freeze": ["es.object.freeze"], "core-js/features/object/from-entries": ["es.array.iterator", "es.object.from-entries"], "core-js/features/object/get-own-property-descriptor": ["es.object.get-own-property-descriptor"], "core-js/features/object/get-own-property-descriptors": ["es.object.get-own-property-descriptors"], "core-js/features/object/get-own-property-names": ["es.object.get-own-property-names"], "core-js/features/object/get-own-property-symbols": ["es.symbol"], "core-js/features/object/get-prototype-of": ["es.object.get-prototype-of"], "core-js/features/object/is": ["es.object.is"], "core-js/features/object/is-extensible": ["es.object.is-extensible"], "core-js/features/object/is-frozen": ["es.object.is-frozen"], "core-js/features/object/is-sealed": ["es.object.is-sealed"], "core-js/features/object/iterate-entries": ["esnext.object.iterate-entries"], "core-js/features/object/iterate-keys": ["esnext.object.iterate-keys"], "core-js/features/object/iterate-values": ["esnext.object.iterate-values"], "core-js/features/object/keys": ["es.object.keys"], "core-js/features/object/lookup-getter": ["es.object.lookup-setter"], "core-js/features/object/lookup-setter": ["es.object.lookup-setter"], "core-js/features/object/prevent-extensions": ["es.object.prevent-extensions"], "core-js/features/object/seal": ["es.object.seal"], "core-js/features/object/set-prototype-of": ["es.object.set-prototype-of"], "core-js/features/object/to-string": ["es.json.to-string-tag", "es.math.to-string-tag", "es.object.to-string"], "core-js/features/object/values": ["es.object.values"], "core-js/features/observable": ["es.object.to-string", "es.string.iterator", "esnext.observable", "esnext.symbol.observable", "web.dom-collections.iterator"], "core-js/features/parse-float": ["es.parse-float"], "core-js/features/parse-int": ["es.parse-int"], "core-js/features/promise": ["es.object.to-string", "es.promise", "es.promise.all-settled", "es.promise.finally", "es.string.iterator", "esnext.aggregate-error", "esnext.promise.all-settled", "esnext.promise.any", "esnext.promise.try", "web.dom-collections.iterator"], "core-js/features/promise/all-settled": ["es.promise", "es.promise.all-settled", "esnext.promise.all-settled"], "core-js/features/promise/any": ["es.promise", "esnext.aggregate-error", "esnext.promise.any"], "core-js/features/promise/finally": ["es.promise", "es.promise.finally"], "core-js/features/promise/try": ["es.promise", "esnext.promise.try"], "core-js/features/queue-microtask": ["web.queue-microtask"], "core-js/features/reflect": ["es.reflect.apply", "es.reflect.construct", "es.reflect.define-property", "es.reflect.delete-property", "es.reflect.get", "es.reflect.get-own-property-descriptor", "es.reflect.get-prototype-of", "es.reflect.has", "es.reflect.is-extensible", "es.reflect.own-keys", "es.reflect.prevent-extensions", "es.reflect.set", "es.reflect.set-prototype-of", "esnext.reflect.define-metadata", "esnext.reflect.delete-metadata", "esnext.reflect.get-metadata", "esnext.reflect.get-metadata-keys", "esnext.reflect.get-own-metadata", "esnext.reflect.get-own-metadata-keys", "esnext.reflect.has-metadata", "esnext.reflect.has-own-metadata", "esnext.reflect.metadata"], "core-js/features/reflect/apply": ["es.reflect.apply"], "core-js/features/reflect/construct": ["es.reflect.construct"], "core-js/features/reflect/define-metadata": ["esnext.reflect.define-metadata"], "core-js/features/reflect/define-property": ["es.reflect.define-property"], "core-js/features/reflect/delete-metadata": ["esnext.reflect.delete-metadata"], "core-js/features/reflect/delete-property": ["es.reflect.delete-property"], "core-js/features/reflect/get": ["es.reflect.get"], "core-js/features/reflect/get-metadata": ["esnext.reflect.get-metadata"], "core-js/features/reflect/get-metadata-keys": ["esnext.reflect.get-metadata-keys"], "core-js/features/reflect/get-own-metadata": ["esnext.reflect.get-own-metadata"], "core-js/features/reflect/get-own-metadata-keys": ["esnext.reflect.get-own-metadata-keys"], "core-js/features/reflect/get-own-property-descriptor": ["es.reflect.get-own-property-descriptor"], "core-js/features/reflect/get-prototype-of": ["es.reflect.get-prototype-of"], "core-js/features/reflect/has": ["es.reflect.has"], "core-js/features/reflect/has-metadata": ["esnext.reflect.has-metadata"], "core-js/features/reflect/has-own-metadata": ["esnext.reflect.has-own-metadata"], "core-js/features/reflect/is-extensible": ["es.reflect.is-extensible"], "core-js/features/reflect/metadata": ["esnext.reflect.metadata"], "core-js/features/reflect/own-keys": ["es.reflect.own-keys"], "core-js/features/reflect/prevent-extensions": ["es.reflect.prevent-extensions"], "core-js/features/reflect/set": ["es.reflect.set"], "core-js/features/reflect/set-prototype-of": ["es.reflect.set-prototype-of"], "core-js/features/regexp": ["es.regexp.constructor", "es.regexp.exec", "es.regexp.flags", "es.regexp.sticky", "es.regexp.test", "es.regexp.to-string", "es.string.match", "es.string.replace", "es.string.search", "es.string.split"], "core-js/features/regexp/constructor": ["es.regexp.constructor"], "core-js/features/regexp/flags": ["es.regexp.flags"], "core-js/features/regexp/match": ["es.string.match"], "core-js/features/regexp/replace": ["es.string.replace"], "core-js/features/regexp/search": ["es.string.search"], "core-js/features/regexp/split": ["es.string.split"], "core-js/features/regexp/sticky": ["es.regexp.sticky"], "core-js/features/regexp/test": ["es.regexp.exec", "es.regexp.test"], "core-js/features/regexp/to-string": ["es.regexp.to-string"], "core-js/features/set": ["es.object.to-string", "es.set", "es.string.iterator", "esnext.set.add-all", "esnext.set.delete-all", "esnext.set.difference", "esnext.set.every", "esnext.set.filter", "esnext.set.find", "esnext.set.from", "esnext.set.intersection", "esnext.set.is-disjoint-from", "esnext.set.is-subset-of", "esnext.set.is-superset-of", "esnext.set.join", "esnext.set.map", "esnext.set.of", "esnext.set.reduce", "esnext.set.some", "esnext.set.symmetric-difference", "esnext.set.union", "web.dom-collections.iterator"], "core-js/features/set-immediate": ["web.immediate"], "core-js/features/set-interval": ["web.timers"], "core-js/features/set-timeout": ["web.timers"], "core-js/features/set/add-all": ["es.set", "esnext.set.add-all"], "core-js/features/set/delete-all": ["es.set", "esnext.set.delete-all"], "core-js/features/set/difference": ["es.set", "es.string.iterator", "esnext.set.difference", "web.dom-collections.iterator"], "core-js/features/set/every": ["es.set", "esnext.set.every"], "core-js/features/set/filter": ["es.set", "esnext.set.filter"], "core-js/features/set/find": ["es.set", "esnext.set.find"], "core-js/features/set/from": ["es.set", "es.string.iterator", "esnext.set.from", "web.dom-collections.iterator"], "core-js/features/set/intersection": ["es.set", "esnext.set.intersection"], "core-js/features/set/is-disjoint-from": ["es.set", "esnext.set.is-disjoint-from"], "core-js/features/set/is-subset-of": ["es.set", "es.string.iterator", "esnext.set.is-subset-of", "web.dom-collections.iterator"], "core-js/features/set/is-superset-of": ["es.set", "esnext.set.is-superset-of"], "core-js/features/set/join": ["es.set", "esnext.set.join"], "core-js/features/set/map": ["es.set", "esnext.set.map"], "core-js/features/set/of": ["es.set", "es.string.iterator", "esnext.set.of", "web.dom-collections.iterator"], "core-js/features/set/reduce": ["es.set", "esnext.set.reduce"], "core-js/features/set/some": ["es.set", "esnext.set.some"], "core-js/features/set/symmetric-difference": ["es.set", "es.string.iterator", "esnext.set.symmetric-difference", "web.dom-collections.iterator"], "core-js/features/set/union": ["es.set", "es.string.iterator", "esnext.set.union", "web.dom-collections.iterator"], "core-js/features/string": ["es.regexp.exec", "es.string.code-point-at", "es.string.ends-with", "es.string.from-code-point", "es.string.includes", "es.string.iterator", "es.string.match", "es.string.match-all", "es.string.pad-end", "es.string.pad-start", "es.string.raw", "es.string.repeat", "es.string.replace", "es.string.search", "es.string.split", "es.string.starts-with", "es.string.trim", "es.string.trim-end", "es.string.trim-start", "es.string.anchor", "es.string.big", "es.string.blink", "es.string.bold", "es.string.fixed", "es.string.fontcolor", "es.string.fontsize", "es.string.italics", "es.string.link", "es.string.small", "es.string.strike", "es.string.sub", "es.string.sup", "esnext.string.at", "esnext.string.code-points", "esnext.string.match-all", "esnext.string.replace-all"], "core-js/features/string/anchor": ["es.string.anchor"], "core-js/features/string/at": ["esnext.string.at"], "core-js/features/string/big": ["es.string.big"], "core-js/features/string/blink": ["es.string.blink"], "core-js/features/string/bold": ["es.string.bold"], "core-js/features/string/code-point-at": ["es.string.code-point-at"], "core-js/features/string/code-points": ["esnext.string.code-points"], "core-js/features/string/ends-with": ["es.string.ends-with"], "core-js/features/string/fixed": ["es.string.fixed"], "core-js/features/string/fontcolor": ["es.string.fontcolor"], "core-js/features/string/fontsize": ["es.string.fontsize"], "core-js/features/string/from-code-point": ["es.string.from-code-point"], "core-js/features/string/includes": ["es.string.includes"], "core-js/features/string/italics": ["es.string.italics"], "core-js/features/string/iterator": ["es.string.iterator"], "core-js/features/string/link": ["es.string.link"], "core-js/features/string/match": ["es.regexp.exec", "es.string.match"], "core-js/features/string/match-all": ["es.string.match-all", "esnext.string.match-all"], "core-js/features/string/pad-end": ["es.string.pad-end"], "core-js/features/string/pad-start": ["es.string.pad-start"], "core-js/features/string/raw": ["es.string.raw"], "core-js/features/string/repeat": ["es.string.repeat"], "core-js/features/string/replace": ["es.regexp.exec", "es.string.replace"], "core-js/features/string/replace-all": ["esnext.string.replace-all"], "core-js/features/string/search": ["es.regexp.exec", "es.string.search"], "core-js/features/string/small": ["es.string.small"], "core-js/features/string/split": ["es.regexp.exec", "es.string.split"], "core-js/features/string/starts-with": ["es.string.starts-with"], "core-js/features/string/strike": ["es.string.strike"], "core-js/features/string/sub": ["es.string.sub"], "core-js/features/string/sup": ["es.string.sup"], "core-js/features/string/trim": ["es.string.trim"], "core-js/features/string/trim-end": ["es.string.trim-end"], "core-js/features/string/trim-left": ["es.string.trim-start"], "core-js/features/string/trim-right": ["es.string.trim-end"], "core-js/features/string/trim-start": ["es.string.trim-start"], "core-js/features/string/virtual": ["es.string.code-point-at", "es.string.ends-with", "es.string.includes", "es.string.iterator", "es.string.match", "es.string.match-all", "es.string.pad-end", "es.string.pad-start", "es.string.repeat", "es.string.replace", "es.string.search", "es.string.split", "es.string.starts-with", "es.string.trim", "es.string.trim-end", "es.string.trim-start", "es.string.anchor", "es.string.big", "es.string.blink", "es.string.bold", "es.string.fixed", "es.string.fontcolor", "es.string.fontsize", "es.string.italics", "es.string.link", "es.string.small", "es.string.strike", "es.string.sub", "es.string.sup", "esnext.string.at", "esnext.string.code-points", "esnext.string.match-all", "esnext.string.replace-all"], "core-js/features/string/virtual/anchor": ["es.string.anchor"], "core-js/features/string/virtual/at": ["esnext.string.at"], "core-js/features/string/virtual/big": ["es.string.big"], "core-js/features/string/virtual/blink": ["es.string.blink"], "core-js/features/string/virtual/bold": ["es.string.bold"], "core-js/features/string/virtual/code-point-at": ["es.string.code-point-at"], "core-js/features/string/virtual/code-points": ["esnext.string.code-points"], "core-js/features/string/virtual/ends-with": ["es.string.ends-with"], "core-js/features/string/virtual/fixed": ["es.string.fixed"], "core-js/features/string/virtual/fontcolor": ["es.string.fontcolor"], "core-js/features/string/virtual/fontsize": ["es.string.fontsize"], "core-js/features/string/virtual/includes": ["es.string.includes"], "core-js/features/string/virtual/italics": ["es.string.italics"], "core-js/features/string/virtual/iterator": ["es.string.iterator"], "core-js/features/string/virtual/link": ["es.string.link"], "core-js/features/string/virtual/match-all": ["es.string.match-all", "esnext.string.match-all"], "core-js/features/string/virtual/pad-end": ["es.string.pad-end"], "core-js/features/string/virtual/pad-start": ["es.string.pad-start"], "core-js/features/string/virtual/repeat": ["es.string.repeat"], "core-js/features/string/virtual/replace-all": ["esnext.string.replace-all"], "core-js/features/string/virtual/small": ["es.string.small"], "core-js/features/string/virtual/starts-with": ["es.string.starts-with"], "core-js/features/string/virtual/strike": ["es.string.strike"], "core-js/features/string/virtual/sub": ["es.string.sub"], "core-js/features/string/virtual/sup": ["es.string.sup"], "core-js/features/string/virtual/trim": ["es.string.trim"], "core-js/features/string/virtual/trim-end": ["es.string.trim-end"], "core-js/features/string/virtual/trim-left": ["es.string.trim-start"], "core-js/features/string/virtual/trim-right": ["es.string.trim-end"], "core-js/features/string/virtual/trim-start": ["es.string.trim-start"], "core-js/features/symbol": ["es.symbol", "es.symbol.description", "es.symbol.async-iterator", "es.symbol.has-instance", "es.symbol.is-concat-spreadable", "es.symbol.iterator", "es.symbol.match", "es.symbol.match-all", "es.symbol.replace", "es.symbol.search", "es.symbol.species", "es.symbol.split", "es.symbol.to-primitive", "es.symbol.to-string-tag", "es.symbol.unscopables", "es.array.concat", "es.json.to-string-tag", "es.math.to-string-tag", "es.object.to-string", "esnext.symbol.async-dispose", "esnext.symbol.dispose", "esnext.symbol.observable", "esnext.symbol.pattern-match", "esnext.symbol.replace-all"], "core-js/features/symbol/async-dispose": ["esnext.symbol.async-dispose"], "core-js/features/symbol/async-iterator": ["es.symbol.async-iterator"], "core-js/features/symbol/description": ["es.symbol.description"], "core-js/features/symbol/dispose": ["esnext.symbol.dispose"], "core-js/features/symbol/for": ["es.symbol"], "core-js/features/symbol/has-instance": ["es.symbol.has-instance", "es.function.has-instance"], "core-js/features/symbol/is-concat-spreadable": ["es.symbol.is-concat-spreadable", "es.array.concat"], "core-js/features/symbol/iterator": ["es.symbol.iterator", "es.string.iterator", "web.dom-collections.iterator"], "core-js/features/symbol/key-for": ["es.symbol"], "core-js/features/symbol/match": ["es.symbol.match", "es.string.match"], "core-js/features/symbol/match-all": ["es.symbol.match-all", "es.string.match-all"], "core-js/features/symbol/observable": ["esnext.symbol.observable"], "core-js/features/symbol/pattern-match": ["esnext.symbol.pattern-match"], "core-js/features/symbol/replace": ["es.symbol.replace", "es.string.replace"], "core-js/features/symbol/replace-all": ["esnext.symbol.replace-all"], "core-js/features/symbol/search": ["es.symbol.search", "es.string.search"], "core-js/features/symbol/species": ["es.symbol.species"], "core-js/features/symbol/split": ["es.symbol.split", "es.string.split"], "core-js/features/symbol/to-primitive": ["es.symbol.to-primitive"], "core-js/features/symbol/to-string-tag": ["es.symbol.to-string-tag", "es.json.to-string-tag", "es.math.to-string-tag", "es.object.to-string"], "core-js/features/symbol/unscopables": ["es.symbol.unscopables"], "core-js/features/typed-array": ["es.object.to-string", "es.typed-array.float32-array", "es.typed-array.float64-array", "es.typed-array.int8-array", "es.typed-array.int16-array", "es.typed-array.int32-array", "es.typed-array.uint8-array", "es.typed-array.uint8-clamped-array", "es.typed-array.uint16-array", "es.typed-array.uint32-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/features/typed-array/copy-within": ["es.typed-array.copy-within"], "core-js/features/typed-array/entries": ["es.typed-array.iterator"], "core-js/features/typed-array/every": ["es.typed-array.every"], "core-js/features/typed-array/fill": ["es.typed-array.fill"], "core-js/features/typed-array/filter": ["es.typed-array.filter"], "core-js/features/typed-array/find": ["es.typed-array.find"], "core-js/features/typed-array/find-index": ["es.typed-array.find-index"], "core-js/features/typed-array/float32-array": ["es.object.to-string", "es.typed-array.float32-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/features/typed-array/float64-array": ["es.object.to-string", "es.typed-array.float64-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/features/typed-array/for-each": ["es.typed-array.for-each"], "core-js/features/typed-array/from": ["es.typed-array.from"], "core-js/features/typed-array/includes": ["es.typed-array.includes"], "core-js/features/typed-array/index-of": ["es.typed-array.index-of"], "core-js/features/typed-array/int16-array": ["es.object.to-string", "es.typed-array.int16-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/features/typed-array/int32-array": ["es.object.to-string", "es.typed-array.int32-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/features/typed-array/int8-array": ["es.object.to-string", "es.typed-array.int8-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/features/typed-array/iterator": ["es.typed-array.iterator"], "core-js/features/typed-array/join": ["es.typed-array.join"], "core-js/features/typed-array/keys": ["es.typed-array.iterator"], "core-js/features/typed-array/last-index-of": ["es.typed-array.last-index-of"], "core-js/features/typed-array/map": ["es.typed-array.map"], "core-js/features/typed-array/of": ["es.typed-array.of"], "core-js/features/typed-array/reduce": ["es.typed-array.reduce"], "core-js/features/typed-array/reduce-right": ["es.typed-array.reduce-right"], "core-js/features/typed-array/reverse": ["es.typed-array.reverse"], "core-js/features/typed-array/set": ["es.typed-array.set"], "core-js/features/typed-array/slice": ["es.typed-array.slice"], "core-js/features/typed-array/some": ["es.typed-array.some"], "core-js/features/typed-array/sort": ["es.typed-array.sort"], "core-js/features/typed-array/subarray": ["es.typed-array.subarray"], "core-js/features/typed-array/to-locale-string": ["es.typed-array.to-locale-string"], "core-js/features/typed-array/to-string": ["es.typed-array.to-string"], "core-js/features/typed-array/uint16-array": ["es.object.to-string", "es.typed-array.uint16-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/features/typed-array/uint32-array": ["es.object.to-string", "es.typed-array.uint32-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/features/typed-array/uint8-array": ["es.object.to-string", "es.typed-array.uint8-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/features/typed-array/uint8-clamped-array": ["es.object.to-string", "es.typed-array.uint8-clamped-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/features/typed-array/values": ["es.typed-array.iterator"], "core-js/features/url": ["web.url", "web.url.to-json", "web.url-search-params"], "core-js/features/url-search-params": ["web.url-search-params"], "core-js/features/url/to-json": ["web.url.to-json"], "core-js/features/weak-map": ["es.object.to-string", "es.weak-map", "esnext.weak-map.delete-all", "esnext.weak-map.from", "esnext.weak-map.of", "esnext.weak-map.upsert", "web.dom-collections.iterator"], "core-js/features/weak-map/delete-all": ["es.weak-map", "esnext.weak-map.delete-all"], "core-js/features/weak-map/from": ["es.string.iterator", "es.weak-map", "esnext.weak-map.from", "web.dom-collections.iterator"], "core-js/features/weak-map/of": ["es.string.iterator", "es.weak-map", "esnext.weak-map.of", "web.dom-collections.iterator"], "core-js/features/weak-map/upsert": ["es.weak-map", "esnext.weak-map.upsert"], "core-js/features/weak-set": ["es.object.to-string", "es.weak-set", "esnext.weak-set.add-all", "esnext.weak-set.delete-all", "esnext.weak-set.from", "esnext.weak-set.of", "web.dom-collections.iterator"], "core-js/features/weak-set/add-all": ["es.weak-set", "esnext.weak-set.add-all"], "core-js/features/weak-set/delete-all": ["es.weak-set", "esnext.weak-set.delete-all"], "core-js/features/weak-set/from": ["es.string.iterator", "es.weak-set", "esnext.weak-set.from", "web.dom-collections.iterator"], "core-js/features/weak-set/of": ["es.string.iterator", "es.weak-set", "esnext.weak-set.of", "web.dom-collections.iterator"], "core-js/modules/es.array-buffer.constructor": ["es.array-buffer.constructor"], "core-js/modules/es.array-buffer.is-view": ["es.array-buffer.is-view"], "core-js/modules/es.array-buffer.slice": ["es.array-buffer.slice"], "core-js/modules/es.array.concat": ["es.array.concat"], "core-js/modules/es.array.copy-within": ["es.array.copy-within"], "core-js/modules/es.array.every": ["es.array.every"], "core-js/modules/es.array.fill": ["es.array.fill"], "core-js/modules/es.array.filter": ["es.array.filter"], "core-js/modules/es.array.find": ["es.array.find"], "core-js/modules/es.array.find-index": ["es.array.find-index"], "core-js/modules/es.array.flat": ["es.array.flat"], "core-js/modules/es.array.flat-map": ["es.array.flat-map"], "core-js/modules/es.array.for-each": ["es.array.for-each"], "core-js/modules/es.array.from": ["es.array.from"], "core-js/modules/es.array.includes": ["es.array.includes"], "core-js/modules/es.array.index-of": ["es.array.index-of"], "core-js/modules/es.array.is-array": ["es.array.is-array"], "core-js/modules/es.array.iterator": ["es.array.iterator"], "core-js/modules/es.array.join": ["es.array.join"], "core-js/modules/es.array.last-index-of": ["es.array.last-index-of"], "core-js/modules/es.array.map": ["es.array.map"], "core-js/modules/es.array.of": ["es.array.of"], "core-js/modules/es.array.reduce": ["es.array.reduce"], "core-js/modules/es.array.reduce-right": ["es.array.reduce-right"], "core-js/modules/es.array.reverse": ["es.array.reverse"], "core-js/modules/es.array.slice": ["es.array.slice"], "core-js/modules/es.array.some": ["es.array.some"], "core-js/modules/es.array.sort": ["es.array.sort"], "core-js/modules/es.array.species": ["es.array.species"], "core-js/modules/es.array.splice": ["es.array.splice"], "core-js/modules/es.array.unscopables.flat": ["es.array.unscopables.flat"], "core-js/modules/es.array.unscopables.flat-map": ["es.array.unscopables.flat-map"], "core-js/modules/es.data-view": ["es.data-view"], "core-js/modules/es.date.now": ["es.date.now"], "core-js/modules/es.date.to-iso-string": ["es.date.to-iso-string"], "core-js/modules/es.date.to-json": ["es.date.to-json"], "core-js/modules/es.date.to-primitive": ["es.date.to-primitive"], "core-js/modules/es.date.to-string": ["es.date.to-string"], "core-js/modules/es.function.bind": ["es.function.bind"], "core-js/modules/es.function.has-instance": ["es.function.has-instance"], "core-js/modules/es.function.name": ["es.function.name"], "core-js/modules/es.global-this": ["es.global-this"], "core-js/modules/es.json.stringify": ["es.json.stringify"], "core-js/modules/es.json.to-string-tag": ["es.json.to-string-tag"], "core-js/modules/es.map": ["es.map"], "core-js/modules/es.math.acosh": ["es.math.acosh"], "core-js/modules/es.math.asinh": ["es.math.asinh"], "core-js/modules/es.math.atanh": ["es.math.atanh"], "core-js/modules/es.math.cbrt": ["es.math.cbrt"], "core-js/modules/es.math.clz32": ["es.math.clz32"], "core-js/modules/es.math.cosh": ["es.math.cosh"], "core-js/modules/es.math.expm1": ["es.math.expm1"], "core-js/modules/es.math.fround": ["es.math.fround"], "core-js/modules/es.math.hypot": ["es.math.hypot"], "core-js/modules/es.math.imul": ["es.math.imul"], "core-js/modules/es.math.log10": ["es.math.log10"], "core-js/modules/es.math.log1p": ["es.math.log1p"], "core-js/modules/es.math.log2": ["es.math.log2"], "core-js/modules/es.math.sign": ["es.math.sign"], "core-js/modules/es.math.sinh": ["es.math.sinh"], "core-js/modules/es.math.tanh": ["es.math.tanh"], "core-js/modules/es.math.to-string-tag": ["es.math.to-string-tag"], "core-js/modules/es.math.trunc": ["es.math.trunc"], "core-js/modules/es.number.constructor": ["es.number.constructor"], "core-js/modules/es.number.epsilon": ["es.number.epsilon"], "core-js/modules/es.number.is-finite": ["es.number.is-finite"], "core-js/modules/es.number.is-integer": ["es.number.is-integer"], "core-js/modules/es.number.is-nan": ["es.number.is-nan"], "core-js/modules/es.number.is-safe-integer": ["es.number.is-safe-integer"], "core-js/modules/es.number.max-safe-integer": ["es.number.max-safe-integer"], "core-js/modules/es.number.min-safe-integer": ["es.number.min-safe-integer"], "core-js/modules/es.number.parse-float": ["es.number.parse-float"], "core-js/modules/es.number.parse-int": ["es.number.parse-int"], "core-js/modules/es.number.to-fixed": ["es.number.to-fixed"], "core-js/modules/es.number.to-precision": ["es.number.to-precision"], "core-js/modules/es.object.assign": ["es.object.assign"], "core-js/modules/es.object.create": ["es.object.create"], "core-js/modules/es.object.define-getter": ["es.object.define-getter"], "core-js/modules/es.object.define-properties": ["es.object.define-properties"], "core-js/modules/es.object.define-property": ["es.object.define-property"], "core-js/modules/es.object.define-setter": ["es.object.define-setter"], "core-js/modules/es.object.entries": ["es.object.entries"], "core-js/modules/es.object.freeze": ["es.object.freeze"], "core-js/modules/es.object.from-entries": ["es.object.from-entries"], "core-js/modules/es.object.get-own-property-descriptor": ["es.object.get-own-property-descriptor"], "core-js/modules/es.object.get-own-property-descriptors": ["es.object.get-own-property-descriptors"], "core-js/modules/es.object.get-own-property-names": ["es.object.get-own-property-names"], "core-js/modules/es.object.get-prototype-of": ["es.object.get-prototype-of"], "core-js/modules/es.object.is": ["es.object.is"], "core-js/modules/es.object.is-extensible": ["es.object.is-extensible"], "core-js/modules/es.object.is-frozen": ["es.object.is-frozen"], "core-js/modules/es.object.is-sealed": ["es.object.is-sealed"], "core-js/modules/es.object.keys": ["es.object.keys"], "core-js/modules/es.object.lookup-getter": ["es.object.lookup-getter"], "core-js/modules/es.object.lookup-setter": ["es.object.lookup-setter"], "core-js/modules/es.object.prevent-extensions": ["es.object.prevent-extensions"], "core-js/modules/es.object.seal": ["es.object.seal"], "core-js/modules/es.object.set-prototype-of": ["es.object.set-prototype-of"], "core-js/modules/es.object.to-string": ["es.object.to-string"], "core-js/modules/es.object.values": ["es.object.values"], "core-js/modules/es.parse-float": ["es.parse-float"], "core-js/modules/es.parse-int": ["es.parse-int"], "core-js/modules/es.promise": ["es.promise"], "core-js/modules/es.promise.all-settled": ["es.promise.all-settled"], "core-js/modules/es.promise.finally": ["es.promise.finally"], "core-js/modules/es.reflect.apply": ["es.reflect.apply"], "core-js/modules/es.reflect.construct": ["es.reflect.construct"], "core-js/modules/es.reflect.define-property": ["es.reflect.define-property"], "core-js/modules/es.reflect.delete-property": ["es.reflect.delete-property"], "core-js/modules/es.reflect.get": ["es.reflect.get"], "core-js/modules/es.reflect.get-own-property-descriptor": ["es.reflect.get-own-property-descriptor"], "core-js/modules/es.reflect.get-prototype-of": ["es.reflect.get-prototype-of"], "core-js/modules/es.reflect.has": ["es.reflect.has"], "core-js/modules/es.reflect.is-extensible": ["es.reflect.is-extensible"], "core-js/modules/es.reflect.own-keys": ["es.reflect.own-keys"], "core-js/modules/es.reflect.prevent-extensions": ["es.reflect.prevent-extensions"], "core-js/modules/es.reflect.set": ["es.reflect.set"], "core-js/modules/es.reflect.set-prototype-of": ["es.reflect.set-prototype-of"], "core-js/modules/es.regexp.constructor": ["es.regexp.constructor"], "core-js/modules/es.regexp.exec": ["es.regexp.exec"], "core-js/modules/es.regexp.flags": ["es.regexp.flags"], "core-js/modules/es.regexp.sticky": ["es.regexp.sticky"], "core-js/modules/es.regexp.test": ["es.regexp.test"], "core-js/modules/es.regexp.to-string": ["es.regexp.to-string"], "core-js/modules/es.set": ["es.set"], "core-js/modules/es.string.anchor": ["es.string.anchor"], "core-js/modules/es.string.big": ["es.string.big"], "core-js/modules/es.string.blink": ["es.string.blink"], "core-js/modules/es.string.bold": ["es.string.bold"], "core-js/modules/es.string.code-point-at": ["es.string.code-point-at"], "core-js/modules/es.string.ends-with": ["es.string.ends-with"], "core-js/modules/es.string.fixed": ["es.string.fixed"], "core-js/modules/es.string.fontcolor": ["es.string.fontcolor"], "core-js/modules/es.string.fontsize": ["es.string.fontsize"], "core-js/modules/es.string.from-code-point": ["es.string.from-code-point"], "core-js/modules/es.string.includes": ["es.string.includes"], "core-js/modules/es.string.italics": ["es.string.italics"], "core-js/modules/es.string.iterator": ["es.string.iterator"], "core-js/modules/es.string.link": ["es.string.link"], "core-js/modules/es.string.match": ["es.string.match"], "core-js/modules/es.string.match-all": ["es.string.match-all"], "core-js/modules/es.string.pad-end": ["es.string.pad-end"], "core-js/modules/es.string.pad-start": ["es.string.pad-start"], "core-js/modules/es.string.raw": ["es.string.raw"], "core-js/modules/es.string.repeat": ["es.string.repeat"], "core-js/modules/es.string.replace": ["es.string.replace"], "core-js/modules/es.string.search": ["es.string.search"], "core-js/modules/es.string.small": ["es.string.small"], "core-js/modules/es.string.split": ["es.string.split"], "core-js/modules/es.string.starts-with": ["es.string.starts-with"], "core-js/modules/es.string.strike": ["es.string.strike"], "core-js/modules/es.string.sub": ["es.string.sub"], "core-js/modules/es.string.sup": ["es.string.sup"], "core-js/modules/es.string.trim": ["es.string.trim"], "core-js/modules/es.string.trim-end": ["es.string.trim-end"], "core-js/modules/es.string.trim-start": ["es.string.trim-start"], "core-js/modules/es.symbol": ["es.symbol"], "core-js/modules/es.symbol.async-iterator": ["es.symbol.async-iterator"], "core-js/modules/es.symbol.description": ["es.symbol.description"], "core-js/modules/es.symbol.has-instance": ["es.symbol.has-instance"], "core-js/modules/es.symbol.is-concat-spreadable": ["es.symbol.is-concat-spreadable"], "core-js/modules/es.symbol.iterator": ["es.symbol.iterator"], "core-js/modules/es.symbol.match": ["es.symbol.match"], "core-js/modules/es.symbol.match-all": ["es.symbol.match-all"], "core-js/modules/es.symbol.replace": ["es.symbol.replace"], "core-js/modules/es.symbol.search": ["es.symbol.search"], "core-js/modules/es.symbol.species": ["es.symbol.species"], "core-js/modules/es.symbol.split": ["es.symbol.split"], "core-js/modules/es.symbol.to-primitive": ["es.symbol.to-primitive"], "core-js/modules/es.symbol.to-string-tag": ["es.symbol.to-string-tag"], "core-js/modules/es.symbol.unscopables": ["es.symbol.unscopables"], "core-js/modules/es.typed-array.copy-within": ["es.typed-array.copy-within"], "core-js/modules/es.typed-array.every": ["es.typed-array.every"], "core-js/modules/es.typed-array.fill": ["es.typed-array.fill"], "core-js/modules/es.typed-array.filter": ["es.typed-array.filter"], "core-js/modules/es.typed-array.find": ["es.typed-array.find"], "core-js/modules/es.typed-array.find-index": ["es.typed-array.find-index"], "core-js/modules/es.typed-array.float32-array": ["es.typed-array.float32-array"], "core-js/modules/es.typed-array.float64-array": ["es.typed-array.float64-array"], "core-js/modules/es.typed-array.for-each": ["es.typed-array.for-each"], "core-js/modules/es.typed-array.from": ["es.typed-array.from"], "core-js/modules/es.typed-array.includes": ["es.typed-array.includes"], "core-js/modules/es.typed-array.index-of": ["es.typed-array.index-of"], "core-js/modules/es.typed-array.int16-array": ["es.typed-array.int16-array"], "core-js/modules/es.typed-array.int32-array": ["es.typed-array.int32-array"], "core-js/modules/es.typed-array.int8-array": ["es.typed-array.int8-array"], "core-js/modules/es.typed-array.iterator": ["es.typed-array.iterator"], "core-js/modules/es.typed-array.join": ["es.typed-array.join"], "core-js/modules/es.typed-array.last-index-of": ["es.typed-array.last-index-of"], "core-js/modules/es.typed-array.map": ["es.typed-array.map"], "core-js/modules/es.typed-array.of": ["es.typed-array.of"], "core-js/modules/es.typed-array.reduce": ["es.typed-array.reduce"], "core-js/modules/es.typed-array.reduce-right": ["es.typed-array.reduce-right"], "core-js/modules/es.typed-array.reverse": ["es.typed-array.reverse"], "core-js/modules/es.typed-array.set": ["es.typed-array.set"], "core-js/modules/es.typed-array.slice": ["es.typed-array.slice"], "core-js/modules/es.typed-array.some": ["es.typed-array.some"], "core-js/modules/es.typed-array.sort": ["es.typed-array.sort"], "core-js/modules/es.typed-array.subarray": ["es.typed-array.subarray"], "core-js/modules/es.typed-array.to-locale-string": ["es.typed-array.to-locale-string"], "core-js/modules/es.typed-array.to-string": ["es.typed-array.to-string"], "core-js/modules/es.typed-array.uint16-array": ["es.typed-array.uint16-array"], "core-js/modules/es.typed-array.uint32-array": ["es.typed-array.uint32-array"], "core-js/modules/es.typed-array.uint8-array": ["es.typed-array.uint8-array"], "core-js/modules/es.typed-array.uint8-clamped-array": ["es.typed-array.uint8-clamped-array"], "core-js/modules/es.weak-map": ["es.weak-map"], "core-js/modules/es.weak-set": ["es.weak-set"], "core-js/modules/esnext.aggregate-error": ["esnext.aggregate-error"], "core-js/modules/esnext.array.is-template-object": ["esnext.array.is-template-object"], "core-js/modules/esnext.array.last-index": ["esnext.array.last-index"], "core-js/modules/esnext.array.last-item": ["esnext.array.last-item"], "core-js/modules/esnext.async-iterator.as-indexed-pairs": ["esnext.async-iterator.as-indexed-pairs"], "core-js/modules/esnext.async-iterator.constructor": ["esnext.async-iterator.constructor"], "core-js/modules/esnext.async-iterator.drop": ["esnext.async-iterator.drop"], "core-js/modules/esnext.async-iterator.every": ["esnext.async-iterator.every"], "core-js/modules/esnext.async-iterator.filter": ["esnext.async-iterator.filter"], "core-js/modules/esnext.async-iterator.find": ["esnext.async-iterator.find"], "core-js/modules/esnext.async-iterator.flat-map": ["esnext.async-iterator.flat-map"], "core-js/modules/esnext.async-iterator.for-each": ["esnext.async-iterator.for-each"], "core-js/modules/esnext.async-iterator.from": ["esnext.async-iterator.from"], "core-js/modules/esnext.async-iterator.map": ["esnext.async-iterator.map"], "core-js/modules/esnext.async-iterator.reduce": ["esnext.async-iterator.reduce"], "core-js/modules/esnext.async-iterator.some": ["esnext.async-iterator.some"], "core-js/modules/esnext.async-iterator.take": ["esnext.async-iterator.take"], "core-js/modules/esnext.async-iterator.to-array": ["esnext.async-iterator.to-array"], "core-js/modules/esnext.composite-key": ["esnext.composite-key"], "core-js/modules/esnext.composite-symbol": ["esnext.composite-symbol"], "core-js/modules/esnext.global-this": ["esnext.global-this"], "core-js/modules/esnext.iterator.as-indexed-pairs": ["esnext.iterator.as-indexed-pairs"], "core-js/modules/esnext.iterator.constructor": ["esnext.iterator.constructor"], "core-js/modules/esnext.iterator.drop": ["esnext.iterator.drop"], "core-js/modules/esnext.iterator.every": ["esnext.iterator.every"], "core-js/modules/esnext.iterator.filter": ["esnext.iterator.filter"], "core-js/modules/esnext.iterator.find": ["esnext.iterator.find"], "core-js/modules/esnext.iterator.flat-map": ["esnext.iterator.flat-map"], "core-js/modules/esnext.iterator.for-each": ["esnext.iterator.for-each"], "core-js/modules/esnext.iterator.from": ["esnext.iterator.from"], "core-js/modules/esnext.iterator.map": ["esnext.iterator.map"], "core-js/modules/esnext.iterator.reduce": ["esnext.iterator.reduce"], "core-js/modules/esnext.iterator.some": ["esnext.iterator.some"], "core-js/modules/esnext.iterator.take": ["esnext.iterator.take"], "core-js/modules/esnext.iterator.to-array": ["esnext.iterator.to-array"], "core-js/modules/esnext.map.delete-all": ["esnext.map.delete-all"], "core-js/modules/esnext.map.every": ["esnext.map.every"], "core-js/modules/esnext.map.filter": ["esnext.map.filter"], "core-js/modules/esnext.map.find": ["esnext.map.find"], "core-js/modules/esnext.map.find-key": ["esnext.map.find-key"], "core-js/modules/esnext.map.from": ["esnext.map.from"], "core-js/modules/esnext.map.group-by": ["esnext.map.group-by"], "core-js/modules/esnext.map.includes": ["esnext.map.includes"], "core-js/modules/esnext.map.key-by": ["esnext.map.key-by"], "core-js/modules/esnext.map.key-of": ["esnext.map.key-of"], "core-js/modules/esnext.map.map-keys": ["esnext.map.map-keys"], "core-js/modules/esnext.map.map-values": ["esnext.map.map-values"], "core-js/modules/esnext.map.merge": ["esnext.map.merge"], "core-js/modules/esnext.map.of": ["esnext.map.of"], "core-js/modules/esnext.map.reduce": ["esnext.map.reduce"], "core-js/modules/esnext.map.some": ["esnext.map.some"], "core-js/modules/esnext.map.update": ["esnext.map.update"], "core-js/modules/esnext.map.update-or-insert": ["esnext.map.update-or-insert"], "core-js/modules/esnext.map.upsert": ["esnext.map.upsert"], "core-js/modules/esnext.math.clamp": ["esnext.math.clamp"], "core-js/modules/esnext.math.deg-per-rad": ["esnext.math.deg-per-rad"], "core-js/modules/esnext.math.degrees": ["esnext.math.degrees"], "core-js/modules/esnext.math.fscale": ["esnext.math.fscale"], "core-js/modules/esnext.math.iaddh": ["esnext.math.iaddh"], "core-js/modules/esnext.math.imulh": ["esnext.math.imulh"], "core-js/modules/esnext.math.isubh": ["esnext.math.isubh"], "core-js/modules/esnext.math.rad-per-deg": ["esnext.math.rad-per-deg"], "core-js/modules/esnext.math.radians": ["esnext.math.radians"], "core-js/modules/esnext.math.scale": ["esnext.math.scale"], "core-js/modules/esnext.math.seeded-prng": ["esnext.math.seeded-prng"], "core-js/modules/esnext.math.signbit": ["esnext.math.signbit"], "core-js/modules/esnext.math.umulh": ["esnext.math.umulh"], "core-js/modules/esnext.number.from-string": ["esnext.number.from-string"], "core-js/modules/esnext.object.iterate-entries": ["esnext.object.iterate-entries"], "core-js/modules/esnext.object.iterate-keys": ["esnext.object.iterate-keys"], "core-js/modules/esnext.object.iterate-values": ["esnext.object.iterate-values"], "core-js/modules/esnext.observable": ["esnext.observable"], "core-js/modules/esnext.promise.all-settled": ["esnext.promise.all-settled"], "core-js/modules/esnext.promise.any": ["esnext.promise.any"], "core-js/modules/esnext.promise.try": ["esnext.promise.try"], "core-js/modules/esnext.reflect.define-metadata": ["esnext.reflect.define-metadata"], "core-js/modules/esnext.reflect.delete-metadata": ["esnext.reflect.delete-metadata"], "core-js/modules/esnext.reflect.get-metadata": ["esnext.reflect.get-metadata"], "core-js/modules/esnext.reflect.get-metadata-keys": ["esnext.reflect.get-metadata-keys"], "core-js/modules/esnext.reflect.get-own-metadata": ["esnext.reflect.get-own-metadata"], "core-js/modules/esnext.reflect.get-own-metadata-keys": ["esnext.reflect.get-own-metadata-keys"], "core-js/modules/esnext.reflect.has-metadata": ["esnext.reflect.has-metadata"], "core-js/modules/esnext.reflect.has-own-metadata": ["esnext.reflect.has-own-metadata"], "core-js/modules/esnext.reflect.metadata": ["esnext.reflect.metadata"], "core-js/modules/esnext.set.add-all": ["esnext.set.add-all"], "core-js/modules/esnext.set.delete-all": ["esnext.set.delete-all"], "core-js/modules/esnext.set.difference": ["esnext.set.difference"], "core-js/modules/esnext.set.every": ["esnext.set.every"], "core-js/modules/esnext.set.filter": ["esnext.set.filter"], "core-js/modules/esnext.set.find": ["esnext.set.find"], "core-js/modules/esnext.set.from": ["esnext.set.from"], "core-js/modules/esnext.set.intersection": ["esnext.set.intersection"], "core-js/modules/esnext.set.is-disjoint-from": ["esnext.set.is-disjoint-from"], "core-js/modules/esnext.set.is-subset-of": ["esnext.set.is-subset-of"], "core-js/modules/esnext.set.is-superset-of": ["esnext.set.is-superset-of"], "core-js/modules/esnext.set.join": ["esnext.set.join"], "core-js/modules/esnext.set.map": ["esnext.set.map"], "core-js/modules/esnext.set.of": ["esnext.set.of"], "core-js/modules/esnext.set.reduce": ["esnext.set.reduce"], "core-js/modules/esnext.set.some": ["esnext.set.some"], "core-js/modules/esnext.set.symmetric-difference": ["esnext.set.symmetric-difference"], "core-js/modules/esnext.set.union": ["esnext.set.union"], "core-js/modules/esnext.string.at": ["esnext.string.at"], "core-js/modules/esnext.string.code-points": ["esnext.string.code-points"], "core-js/modules/esnext.string.match-all": ["esnext.string.match-all"], "core-js/modules/esnext.string.replace-all": ["esnext.string.replace-all"], "core-js/modules/esnext.symbol.async-dispose": ["esnext.symbol.async-dispose"], "core-js/modules/esnext.symbol.dispose": ["esnext.symbol.dispose"], "core-js/modules/esnext.symbol.observable": ["esnext.symbol.observable"], "core-js/modules/esnext.symbol.pattern-match": ["esnext.symbol.pattern-match"], "core-js/modules/esnext.symbol.replace-all": ["esnext.symbol.replace-all"], "core-js/modules/esnext.weak-map.delete-all": ["esnext.weak-map.delete-all"], "core-js/modules/esnext.weak-map.from": ["esnext.weak-map.from"], "core-js/modules/esnext.weak-map.of": ["esnext.weak-map.of"], "core-js/modules/esnext.weak-map.upsert": ["esnext.weak-map.upsert"], "core-js/modules/esnext.weak-set.add-all": ["esnext.weak-set.add-all"], "core-js/modules/esnext.weak-set.delete-all": ["esnext.weak-set.delete-all"], "core-js/modules/esnext.weak-set.from": ["esnext.weak-set.from"], "core-js/modules/esnext.weak-set.of": ["esnext.weak-set.of"], "core-js/modules/web.dom-collections.for-each": ["web.dom-collections.for-each"], "core-js/modules/web.dom-collections.iterator": ["web.dom-collections.iterator"], "core-js/modules/web.immediate": ["web.immediate"], "core-js/modules/web.queue-microtask": ["web.queue-microtask"], "core-js/modules/web.timers": ["web.timers"], "core-js/modules/web.url": ["web.url"], "core-js/modules/web.url-search-params": ["web.url-search-params"], "core-js/modules/web.url.to-json": ["web.url.to-json"], "core-js/proposals": ["esnext.aggregate-error", "esnext.array.is-template-object", "esnext.array.last-index", "esnext.array.last-item", "esnext.async-iterator.constructor", "esnext.async-iterator.as-indexed-pairs", "esnext.async-iterator.drop", "esnext.async-iterator.every", "esnext.async-iterator.filter", "esnext.async-iterator.find", "esnext.async-iterator.flat-map", "esnext.async-iterator.for-each", "esnext.async-iterator.from", "esnext.async-iterator.map", "esnext.async-iterator.reduce", "esnext.async-iterator.some", "esnext.async-iterator.take", "esnext.async-iterator.to-array", "esnext.composite-key", "esnext.composite-symbol", "esnext.global-this", "esnext.iterator.constructor", "esnext.iterator.as-indexed-pairs", "esnext.iterator.drop", "esnext.iterator.every", "esnext.iterator.filter", "esnext.iterator.find", "esnext.iterator.flat-map", "esnext.iterator.for-each", "esnext.iterator.from", "esnext.iterator.map", "esnext.iterator.reduce", "esnext.iterator.some", "esnext.iterator.take", "esnext.iterator.to-array", "esnext.map.delete-all", "esnext.map.every", "esnext.map.filter", "esnext.map.find", "esnext.map.find-key", "esnext.map.from", "esnext.map.group-by", "esnext.map.includes", "esnext.map.key-by", "esnext.map.key-of", "esnext.map.map-keys", "esnext.map.map-values", "esnext.map.merge", "esnext.map.of", "esnext.map.reduce", "esnext.map.some", "esnext.map.update", "esnext.map.update-or-insert", "esnext.map.upsert", "esnext.math.clamp", "esnext.math.deg-per-rad", "esnext.math.degrees", "esnext.math.fscale", "esnext.math.iaddh", "esnext.math.imulh", "esnext.math.isubh", "esnext.math.rad-per-deg", "esnext.math.radians", "esnext.math.scale", "esnext.math.seeded-prng", "esnext.math.signbit", "esnext.math.umulh", "esnext.number.from-string", "esnext.object.iterate-entries", "esnext.object.iterate-keys", "esnext.object.iterate-values", "esnext.observable", "esnext.promise.all-settled", "esnext.promise.any", "esnext.promise.try", "esnext.reflect.define-metadata", "esnext.reflect.delete-metadata", "esnext.reflect.get-metadata", "esnext.reflect.get-metadata-keys", "esnext.reflect.get-own-metadata", "esnext.reflect.get-own-metadata-keys", "esnext.reflect.has-metadata", "esnext.reflect.has-own-metadata", "esnext.reflect.metadata", "esnext.set.add-all", "esnext.set.delete-all", "esnext.set.difference", "esnext.set.every", "esnext.set.filter", "esnext.set.find", "esnext.set.from", "esnext.set.intersection", "esnext.set.is-disjoint-from", "esnext.set.is-subset-of", "esnext.set.is-superset-of", "esnext.set.join", "esnext.set.map", "esnext.set.of", "esnext.set.reduce", "esnext.set.some", "esnext.set.symmetric-difference", "esnext.set.union", "esnext.string.at", "esnext.string.code-points", "esnext.string.match-all", "esnext.string.replace-all", "esnext.symbol.async-dispose", "esnext.symbol.dispose", "esnext.symbol.observable", "esnext.symbol.pattern-match", "esnext.symbol.replace-all", "esnext.weak-map.delete-all", "esnext.weak-map.from", "esnext.weak-map.of", "esnext.weak-map.upsert", "esnext.weak-set.add-all", "esnext.weak-set.delete-all", "esnext.weak-set.from", "esnext.weak-set.of", "web.url", "web.url.to-json", "web.url-search-params"], "core-js/proposals/array-is-template-object": ["esnext.array.is-template-object"], "core-js/proposals/array-last": ["esnext.array.last-index", "esnext.array.last-item"], "core-js/proposals/collection-methods": ["esnext.map.delete-all", "esnext.map.every", "esnext.map.filter", "esnext.map.find", "esnext.map.find-key", "esnext.map.group-by", "esnext.map.includes", "esnext.map.key-by", "esnext.map.key-of", "esnext.map.map-keys", "esnext.map.map-values", "esnext.map.merge", "esnext.map.reduce", "esnext.map.some", "esnext.map.update", "esnext.set.add-all", "esnext.set.delete-all", "esnext.set.every", "esnext.set.filter", "esnext.set.find", "esnext.set.join", "esnext.set.map", "esnext.set.reduce", "esnext.set.some", "esnext.weak-map.delete-all", "esnext.weak-set.add-all", "esnext.weak-set.delete-all"], "core-js/proposals/collection-of-from": ["esnext.map.from", "esnext.map.of", "esnext.set.from", "esnext.set.of", "esnext.weak-map.from", "esnext.weak-map.of", "esnext.weak-set.from", "esnext.weak-set.of"], "core-js/proposals/efficient-64-bit-arithmetic": ["esnext.math.iaddh", "esnext.math.imulh", "esnext.math.isubh", "esnext.math.umulh"], "core-js/proposals/global-this": ["esnext.global-this"], "core-js/proposals/iterator-helpers": ["esnext.async-iterator.constructor", "esnext.async-iterator.as-indexed-pairs", "esnext.async-iterator.drop", "esnext.async-iterator.every", "esnext.async-iterator.filter", "esnext.async-iterator.find", "esnext.async-iterator.flat-map", "esnext.async-iterator.for-each", "esnext.async-iterator.from", "esnext.async-iterator.map", "esnext.async-iterator.reduce", "esnext.async-iterator.some", "esnext.async-iterator.take", "esnext.async-iterator.to-array", "esnext.iterator.constructor", "esnext.iterator.as-indexed-pairs", "esnext.iterator.drop", "esnext.iterator.every", "esnext.iterator.filter", "esnext.iterator.find", "esnext.iterator.flat-map", "esnext.iterator.for-each", "esnext.iterator.from", "esnext.iterator.map", "esnext.iterator.reduce", "esnext.iterator.some", "esnext.iterator.take", "esnext.iterator.to-array"], "core-js/proposals/keys-composition": ["esnext.composite-key", "esnext.composite-symbol"], "core-js/proposals/map-update-or-insert": ["esnext.map.update-or-insert", "esnext.map.upsert", "esnext.weak-map.upsert"], "core-js/proposals/map-upsert": ["esnext.map.update-or-insert", "esnext.map.upsert", "esnext.weak-map.upsert"], "core-js/proposals/math-extensions": ["esnext.math.clamp", "esnext.math.deg-per-rad", "esnext.math.degrees", "esnext.math.fscale", "esnext.math.rad-per-deg", "esnext.math.radians", "esnext.math.scale"], "core-js/proposals/math-signbit": ["esnext.math.signbit"], "core-js/proposals/number-from-string": ["esnext.number.from-string"], "core-js/proposals/object-iteration": ["esnext.object.iterate-entries", "esnext.object.iterate-keys", "esnext.object.iterate-values"], "core-js/proposals/observable": ["esnext.observable", "esnext.symbol.observable"], "core-js/proposals/pattern-matching": ["esnext.symbol.pattern-match"], "core-js/proposals/promise-all-settled": ["esnext.promise.all-settled"], "core-js/proposals/promise-any": ["esnext.aggregate-error", "esnext.promise.any"], "core-js/proposals/promise-try": ["esnext.promise.try"], "core-js/proposals/reflect-metadata": ["esnext.reflect.define-metadata", "esnext.reflect.delete-metadata", "esnext.reflect.get-metadata", "esnext.reflect.get-metadata-keys", "esnext.reflect.get-own-metadata", "esnext.reflect.get-own-metadata-keys", "esnext.reflect.has-metadata", "esnext.reflect.has-own-metadata", "esnext.reflect.metadata"], "core-js/proposals/seeded-random": ["esnext.math.seeded-prng"], "core-js/proposals/set-methods": ["esnext.set.difference", "esnext.set.intersection", "esnext.set.is-disjoint-from", "esnext.set.is-subset-of", "esnext.set.is-superset-of", "esnext.set.symmetric-difference", "esnext.set.union"], "core-js/proposals/string-at": ["esnext.string.at"], "core-js/proposals/string-code-points": ["esnext.string.code-points"], "core-js/proposals/string-match-all": ["esnext.string.match-all"], "core-js/proposals/string-replace-all": ["esnext.string.replace-all", "esnext.symbol.replace-all"], "core-js/proposals/url": ["web.url", "web.url.to-json", "web.url-search-params"], "core-js/proposals/using-statement": ["esnext.symbol.async-dispose", "esnext.symbol.dispose"], "core-js/stable": ["es.symbol", "es.symbol.description", "es.symbol.async-iterator", "es.symbol.has-instance", "es.symbol.is-concat-spreadable", "es.symbol.iterator", "es.symbol.match", "es.symbol.match-all", "es.symbol.replace", "es.symbol.search", "es.symbol.species", "es.symbol.split", "es.symbol.to-primitive", "es.symbol.to-string-tag", "es.symbol.unscopables", "es.array.concat", "es.array.copy-within", "es.array.every", "es.array.fill", "es.array.filter", "es.array.find", "es.array.find-index", "es.array.flat", "es.array.flat-map", "es.array.for-each", "es.array.from", "es.array.includes", "es.array.index-of", "es.array.is-array", "es.array.iterator", "es.array.join", "es.array.last-index-of", "es.array.map", "es.array.of", "es.array.reduce", "es.array.reduce-right", "es.array.reverse", "es.array.slice", "es.array.some", "es.array.sort", "es.array.species", "es.array.splice", "es.array.unscopables.flat", "es.array.unscopables.flat-map", "es.array-buffer.constructor", "es.array-buffer.is-view", "es.array-buffer.slice", "es.data-view", "es.date.now", "es.date.to-iso-string", "es.date.to-json", "es.date.to-primitive", "es.date.to-string", "es.function.bind", "es.function.has-instance", "es.function.name", "es.global-this", "es.json.stringify", "es.json.to-string-tag", "es.map", "es.math.acosh", "es.math.asinh", "es.math.atanh", "es.math.cbrt", "es.math.clz32", "es.math.cosh", "es.math.expm1", "es.math.fround", "es.math.hypot", "es.math.imul", "es.math.log10", "es.math.log1p", "es.math.log2", "es.math.sign", "es.math.sinh", "es.math.tanh", "es.math.to-string-tag", "es.math.trunc", "es.number.constructor", "es.number.epsilon", "es.number.is-finite", "es.number.is-integer", "es.number.is-nan", "es.number.is-safe-integer", "es.number.max-safe-integer", "es.number.min-safe-integer", "es.number.parse-float", "es.number.parse-int", "es.number.to-fixed", "es.number.to-precision", "es.object.assign", "es.object.create", "es.object.define-getter", "es.object.define-properties", "es.object.define-property", "es.object.define-setter", "es.object.entries", "es.object.freeze", "es.object.from-entries", "es.object.get-own-property-descriptor", "es.object.get-own-property-descriptors", "es.object.get-own-property-names", "es.object.get-prototype-of", "es.object.is", "es.object.is-extensible", "es.object.is-frozen", "es.object.is-sealed", "es.object.keys", "es.object.lookup-getter", "es.object.lookup-setter", "es.object.prevent-extensions", "es.object.seal", "es.object.set-prototype-of", "es.object.to-string", "es.object.values", "es.parse-float", "es.parse-int", "es.promise", "es.promise.all-settled", "es.promise.finally", "es.reflect.apply", "es.reflect.construct", "es.reflect.define-property", "es.reflect.delete-property", "es.reflect.get", "es.reflect.get-own-property-descriptor", "es.reflect.get-prototype-of", "es.reflect.has", "es.reflect.is-extensible", "es.reflect.own-keys", "es.reflect.prevent-extensions", "es.reflect.set", "es.reflect.set-prototype-of", "es.regexp.constructor", "es.regexp.exec", "es.regexp.flags", "es.regexp.sticky", "es.regexp.test", "es.regexp.to-string", "es.set", "es.string.code-point-at", "es.string.ends-with", "es.string.from-code-point", "es.string.includes", "es.string.iterator", "es.string.match", "es.string.match-all", "es.string.pad-end", "es.string.pad-start", "es.string.raw", "es.string.repeat", "es.string.replace", "es.string.search", "es.string.split", "es.string.starts-with", "es.string.trim", "es.string.trim-end", "es.string.trim-start", "es.string.anchor", "es.string.big", "es.string.blink", "es.string.bold", "es.string.fixed", "es.string.fontcolor", "es.string.fontsize", "es.string.italics", "es.string.link", "es.string.small", "es.string.strike", "es.string.sub", "es.string.sup", "es.typed-array.float32-array", "es.typed-array.float64-array", "es.typed-array.int8-array", "es.typed-array.int16-array", "es.typed-array.int32-array", "es.typed-array.uint8-array", "es.typed-array.uint8-clamped-array", "es.typed-array.uint16-array", "es.typed-array.uint32-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string", "es.weak-map", "es.weak-set", "web.dom-collections.for-each", "web.dom-collections.iterator", "web.immediate", "web.queue-microtask", "web.timers", "web.url", "web.url.to-json", "web.url-search-params"], "core-js/stable/array": ["es.array.concat", "es.array.copy-within", "es.array.every", "es.array.fill", "es.array.filter", "es.array.find", "es.array.find-index", "es.array.flat", "es.array.flat-map", "es.array.for-each", "es.array.from", "es.array.includes", "es.array.index-of", "es.array.is-array", "es.array.iterator", "es.array.join", "es.array.last-index-of", "es.array.map", "es.array.of", "es.array.reduce", "es.array.reduce-right", "es.array.reverse", "es.array.slice", "es.array.some", "es.array.sort", "es.array.species", "es.array.splice", "es.array.unscopables.flat", "es.array.unscopables.flat-map", "es.string.iterator"], "core-js/stable/array-buffer": ["es.array-buffer.constructor", "es.array-buffer.is-view", "es.array-buffer.slice", "es.object.to-string"], "core-js/stable/array-buffer/constructor": ["es.array-buffer.constructor", "es.object.to-string"], "core-js/stable/array-buffer/is-view": ["es.array-buffer.is-view"], "core-js/stable/array-buffer/slice": ["es.array-buffer.slice"], "core-js/stable/array/concat": ["es.array.concat"], "core-js/stable/array/copy-within": ["es.array.copy-within"], "core-js/stable/array/entries": ["es.array.iterator"], "core-js/stable/array/every": ["es.array.every"], "core-js/stable/array/fill": ["es.array.fill"], "core-js/stable/array/filter": ["es.array.filter"], "core-js/stable/array/find": ["es.array.find"], "core-js/stable/array/find-index": ["es.array.find-index"], "core-js/stable/array/flat": ["es.array.flat", "es.array.unscopables.flat"], "core-js/stable/array/flat-map": ["es.array.flat-map", "es.array.unscopables.flat-map"], "core-js/stable/array/for-each": ["es.array.for-each"], "core-js/stable/array/from": ["es.array.from", "es.string.iterator"], "core-js/stable/array/includes": ["es.array.includes"], "core-js/stable/array/index-of": ["es.array.index-of"], "core-js/stable/array/is-array": ["es.array.is-array"], "core-js/stable/array/iterator": ["es.array.iterator"], "core-js/stable/array/join": ["es.array.join"], "core-js/stable/array/keys": ["es.array.iterator"], "core-js/stable/array/last-index-of": ["es.array.last-index-of"], "core-js/stable/array/map": ["es.array.map"], "core-js/stable/array/of": ["es.array.of"], "core-js/stable/array/reduce": ["es.array.reduce"], "core-js/stable/array/reduce-right": ["es.array.reduce-right"], "core-js/stable/array/reverse": ["es.array.reverse"], "core-js/stable/array/slice": ["es.array.slice"], "core-js/stable/array/some": ["es.array.some"], "core-js/stable/array/sort": ["es.array.sort"], "core-js/stable/array/splice": ["es.array.splice"], "core-js/stable/array/values": ["es.array.iterator"], "core-js/stable/array/virtual": ["es.array.concat", "es.array.copy-within", "es.array.every", "es.array.fill", "es.array.filter", "es.array.find", "es.array.find-index", "es.array.flat", "es.array.flat-map", "es.array.for-each", "es.array.includes", "es.array.index-of", "es.array.iterator", "es.array.join", "es.array.last-index-of", "es.array.map", "es.array.reduce", "es.array.reduce-right", "es.array.reverse", "es.array.slice", "es.array.some", "es.array.sort", "es.array.species", "es.array.splice", "es.array.unscopables.flat", "es.array.unscopables.flat-map"], "core-js/stable/array/virtual/concat": ["es.array.concat"], "core-js/stable/array/virtual/copy-within": ["es.array.copy-within"], "core-js/stable/array/virtual/entries": ["es.array.iterator"], "core-js/stable/array/virtual/every": ["es.array.every"], "core-js/stable/array/virtual/fill": ["es.array.fill"], "core-js/stable/array/virtual/filter": ["es.array.filter"], "core-js/stable/array/virtual/find": ["es.array.find"], "core-js/stable/array/virtual/find-index": ["es.array.find-index"], "core-js/stable/array/virtual/flat": ["es.array.flat", "es.array.unscopables.flat"], "core-js/stable/array/virtual/flat-map": ["es.array.flat-map", "es.array.unscopables.flat-map"], "core-js/stable/array/virtual/for-each": ["es.array.for-each"], "core-js/stable/array/virtual/includes": ["es.array.includes"], "core-js/stable/array/virtual/index-of": ["es.array.index-of"], "core-js/stable/array/virtual/iterator": ["es.array.iterator"], "core-js/stable/array/virtual/join": ["es.array.join"], "core-js/stable/array/virtual/keys": ["es.array.iterator"], "core-js/stable/array/virtual/last-index-of": ["es.array.last-index-of"], "core-js/stable/array/virtual/map": ["es.array.map"], "core-js/stable/array/virtual/reduce": ["es.array.reduce"], "core-js/stable/array/virtual/reduce-right": ["es.array.reduce-right"], "core-js/stable/array/virtual/reverse": ["es.array.reverse"], "core-js/stable/array/virtual/slice": ["es.array.slice"], "core-js/stable/array/virtual/some": ["es.array.some"], "core-js/stable/array/virtual/sort": ["es.array.sort"], "core-js/stable/array/virtual/splice": ["es.array.splice"], "core-js/stable/array/virtual/values": ["es.array.iterator"], "core-js/stable/clear-immediate": ["web.immediate"], "core-js/stable/data-view": ["es.data-view", "es.object.to-string"], "core-js/stable/date": ["es.date.now", "es.date.to-iso-string", "es.date.to-json", "es.date.to-primitive", "es.date.to-string"], "core-js/stable/date/now": ["es.date.now"], "core-js/stable/date/to-iso-string": ["es.date.to-iso-string", "es.date.to-json"], "core-js/stable/date/to-json": ["es.date.to-json"], "core-js/stable/date/to-primitive": ["es.date.to-primitive"], "core-js/stable/date/to-string": ["es.date.to-string"], "core-js/stable/dom-collections": ["es.array.iterator", "web.dom-collections.for-each", "web.dom-collections.iterator"], "core-js/stable/dom-collections/for-each": ["web.dom-collections.for-each"], "core-js/stable/dom-collections/iterator": ["web.dom-collections.iterator"], "core-js/stable/function": ["es.function.bind", "es.function.has-instance", "es.function.name"], "core-js/stable/function/bind": ["es.function.bind"], "core-js/stable/function/has-instance": ["es.function.has-instance"], "core-js/stable/function/name": ["es.function.name"], "core-js/stable/function/virtual": ["es.function.bind"], "core-js/stable/function/virtual/bind": ["es.function.bind"], "core-js/stable/global-this": ["es.global-this"], "core-js/stable/instance/bind": ["es.function.bind"], "core-js/stable/instance/code-point-at": ["es.string.code-point-at"], "core-js/stable/instance/concat": ["es.array.concat"], "core-js/stable/instance/copy-within": ["es.array.copy-within"], "core-js/stable/instance/ends-with": ["es.string.ends-with"], "core-js/stable/instance/entries": ["es.array.iterator", "web.dom-collections.iterator"], "core-js/stable/instance/every": ["es.array.every"], "core-js/stable/instance/fill": ["es.array.fill"], "core-js/stable/instance/filter": ["es.array.filter"], "core-js/stable/instance/find": ["es.array.find"], "core-js/stable/instance/find-index": ["es.array.find-index"], "core-js/stable/instance/flags": ["es.regexp.flags"], "core-js/stable/instance/flat": ["es.array.flat", "es.array.unscopables.flat"], "core-js/stable/instance/flat-map": ["es.array.flat-map", "es.array.unscopables.flat-map"], "core-js/stable/instance/for-each": ["es.array.for-each", "web.dom-collections.iterator"], "core-js/stable/instance/includes": ["es.array.includes", "es.string.includes"], "core-js/stable/instance/index-of": ["es.array.index-of"], "core-js/stable/instance/keys": ["es.array.iterator", "web.dom-collections.iterator"], "core-js/stable/instance/last-index-of": ["es.array.last-index-of"], "core-js/stable/instance/map": ["es.array.map"], "core-js/stable/instance/match-all": ["es.string.match-all"], "core-js/stable/instance/pad-end": ["es.string.pad-end"], "core-js/stable/instance/pad-start": ["es.string.pad-start"], "core-js/stable/instance/reduce": ["es.array.reduce"], "core-js/stable/instance/reduce-right": ["es.array.reduce-right"], "core-js/stable/instance/repeat": ["es.string.repeat"], "core-js/stable/instance/reverse": ["es.array.reverse"], "core-js/stable/instance/slice": ["es.array.slice"], "core-js/stable/instance/some": ["es.array.some"], "core-js/stable/instance/sort": ["es.array.sort"], "core-js/stable/instance/splice": ["es.array.splice"], "core-js/stable/instance/starts-with": ["es.string.starts-with"], "core-js/stable/instance/trim": ["es.string.trim"], "core-js/stable/instance/trim-end": ["es.string.trim-end"], "core-js/stable/instance/trim-left": ["es.string.trim-start"], "core-js/stable/instance/trim-right": ["es.string.trim-end"], "core-js/stable/instance/trim-start": ["es.string.trim-start"], "core-js/stable/instance/values": ["es.array.iterator", "web.dom-collections.iterator"], "core-js/stable/json": ["es.json.stringify", "es.json.to-string-tag"], "core-js/stable/json/stringify": ["es.json.stringify"], "core-js/stable/json/to-string-tag": ["es.json.to-string-tag"], "core-js/stable/map": ["es.map", "es.object.to-string", "es.string.iterator", "web.dom-collections.iterator"], "core-js/stable/math": ["es.math.acosh", "es.math.asinh", "es.math.atanh", "es.math.cbrt", "es.math.clz32", "es.math.cosh", "es.math.expm1", "es.math.fround", "es.math.hypot", "es.math.imul", "es.math.log10", "es.math.log1p", "es.math.log2", "es.math.sign", "es.math.sinh", "es.math.tanh", "es.math.to-string-tag", "es.math.trunc"], "core-js/stable/math/acosh": ["es.math.acosh"], "core-js/stable/math/asinh": ["es.math.asinh"], "core-js/stable/math/atanh": ["es.math.atanh"], "core-js/stable/math/cbrt": ["es.math.cbrt"], "core-js/stable/math/clz32": ["es.math.clz32"], "core-js/stable/math/cosh": ["es.math.cosh"], "core-js/stable/math/expm1": ["es.math.expm1"], "core-js/stable/math/fround": ["es.math.fround"], "core-js/stable/math/hypot": ["es.math.hypot"], "core-js/stable/math/imul": ["es.math.imul"], "core-js/stable/math/log10": ["es.math.log10"], "core-js/stable/math/log1p": ["es.math.log1p"], "core-js/stable/math/log2": ["es.math.log2"], "core-js/stable/math/sign": ["es.math.sign"], "core-js/stable/math/sinh": ["es.math.sinh"], "core-js/stable/math/tanh": ["es.math.tanh"], "core-js/stable/math/to-string-tag": ["es.math.to-string-tag"], "core-js/stable/math/trunc": ["es.math.trunc"], "core-js/stable/number": ["es.number.constructor", "es.number.epsilon", "es.number.is-finite", "es.number.is-integer", "es.number.is-nan", "es.number.is-safe-integer", "es.number.max-safe-integer", "es.number.min-safe-integer", "es.number.parse-float", "es.number.parse-int", "es.number.to-fixed", "es.number.to-precision"], "core-js/stable/number/constructor": ["es.number.constructor"], "core-js/stable/number/epsilon": ["es.number.epsilon"], "core-js/stable/number/is-finite": ["es.number.is-finite"], "core-js/stable/number/is-integer": ["es.number.is-integer"], "core-js/stable/number/is-nan": ["es.number.is-nan"], "core-js/stable/number/is-safe-integer": ["es.number.is-safe-integer"], "core-js/stable/number/max-safe-integer": ["es.number.max-safe-integer"], "core-js/stable/number/min-safe-integer": ["es.number.min-safe-integer"], "core-js/stable/number/parse-float": ["es.number.parse-float"], "core-js/stable/number/parse-int": ["es.number.parse-int"], "core-js/stable/number/to-fixed": ["es.number.to-fixed"], "core-js/stable/number/to-precision": ["es.number.to-precision"], "core-js/stable/number/virtual": ["es.number.to-fixed", "es.number.to-precision"], "core-js/stable/number/virtual/to-fixed": ["es.number.to-fixed"], "core-js/stable/number/virtual/to-precision": ["es.number.to-precision"], "core-js/stable/object": ["es.symbol", "es.json.to-string-tag", "es.math.to-string-tag", "es.object.assign", "es.object.create", "es.object.define-getter", "es.object.define-properties", "es.object.define-property", "es.object.define-setter", "es.object.entries", "es.object.freeze", "es.object.from-entries", "es.object.get-own-property-descriptor", "es.object.get-own-property-descriptors", "es.object.get-own-property-names", "es.object.get-prototype-of", "es.object.is", "es.object.is-extensible", "es.object.is-frozen", "es.object.is-sealed", "es.object.keys", "es.object.lookup-getter", "es.object.lookup-setter", "es.object.prevent-extensions", "es.object.seal", "es.object.set-prototype-of", "es.object.to-string", "es.object.values"], "core-js/stable/object/assign": ["es.object.assign"], "core-js/stable/object/create": ["es.object.create"], "core-js/stable/object/define-getter": ["es.object.define-getter"], "core-js/stable/object/define-properties": ["es.object.define-properties"], "core-js/stable/object/define-property": ["es.object.define-property"], "core-js/stable/object/define-setter": ["es.object.define-setter"], "core-js/stable/object/entries": ["es.object.entries"], "core-js/stable/object/freeze": ["es.object.freeze"], "core-js/stable/object/from-entries": ["es.array.iterator", "es.object.from-entries"], "core-js/stable/object/get-own-property-descriptor": ["es.object.get-own-property-descriptor"], "core-js/stable/object/get-own-property-descriptors": ["es.object.get-own-property-descriptors"], "core-js/stable/object/get-own-property-names": ["es.object.get-own-property-names"], "core-js/stable/object/get-own-property-symbols": ["es.symbol"], "core-js/stable/object/get-prototype-of": ["es.object.get-prototype-of"], "core-js/stable/object/is": ["es.object.is"], "core-js/stable/object/is-extensible": ["es.object.is-extensible"], "core-js/stable/object/is-frozen": ["es.object.is-frozen"], "core-js/stable/object/is-sealed": ["es.object.is-sealed"], "core-js/stable/object/keys": ["es.object.keys"], "core-js/stable/object/lookup-getter": ["es.object.lookup-setter"], "core-js/stable/object/lookup-setter": ["es.object.lookup-setter"], "core-js/stable/object/prevent-extensions": ["es.object.prevent-extensions"], "core-js/stable/object/seal": ["es.object.seal"], "core-js/stable/object/set-prototype-of": ["es.object.set-prototype-of"], "core-js/stable/object/to-string": ["es.json.to-string-tag", "es.math.to-string-tag", "es.object.to-string"], "core-js/stable/object/values": ["es.object.values"], "core-js/stable/parse-float": ["es.parse-float"], "core-js/stable/parse-int": ["es.parse-int"], "core-js/stable/promise": ["es.object.to-string", "es.promise", "es.promise.all-settled", "es.promise.finally", "es.string.iterator", "web.dom-collections.iterator"], "core-js/stable/promise/all-settled": ["es.promise", "es.promise.all-settled"], "core-js/stable/promise/finally": ["es.promise", "es.promise.finally"], "core-js/stable/queue-microtask": ["web.queue-microtask"], "core-js/stable/reflect": ["es.reflect.apply", "es.reflect.construct", "es.reflect.define-property", "es.reflect.delete-property", "es.reflect.get", "es.reflect.get-own-property-descriptor", "es.reflect.get-prototype-of", "es.reflect.has", "es.reflect.is-extensible", "es.reflect.own-keys", "es.reflect.prevent-extensions", "es.reflect.set", "es.reflect.set-prototype-of"], "core-js/stable/reflect/apply": ["es.reflect.apply"], "core-js/stable/reflect/construct": ["es.reflect.construct"], "core-js/stable/reflect/define-property": ["es.reflect.define-property"], "core-js/stable/reflect/delete-property": ["es.reflect.delete-property"], "core-js/stable/reflect/get": ["es.reflect.get"], "core-js/stable/reflect/get-own-property-descriptor": ["es.reflect.get-own-property-descriptor"], "core-js/stable/reflect/get-prototype-of": ["es.reflect.get-prototype-of"], "core-js/stable/reflect/has": ["es.reflect.has"], "core-js/stable/reflect/is-extensible": ["es.reflect.is-extensible"], "core-js/stable/reflect/own-keys": ["es.reflect.own-keys"], "core-js/stable/reflect/prevent-extensions": ["es.reflect.prevent-extensions"], "core-js/stable/reflect/set": ["es.reflect.set"], "core-js/stable/reflect/set-prototype-of": ["es.reflect.set-prototype-of"], "core-js/stable/regexp": ["es.regexp.constructor", "es.regexp.exec", "es.regexp.flags", "es.regexp.sticky", "es.regexp.test", "es.regexp.to-string", "es.string.match", "es.string.replace", "es.string.search", "es.string.split"], "core-js/stable/regexp/constructor": ["es.regexp.constructor"], "core-js/stable/regexp/flags": ["es.regexp.flags"], "core-js/stable/regexp/match": ["es.string.match"], "core-js/stable/regexp/replace": ["es.string.replace"], "core-js/stable/regexp/search": ["es.string.search"], "core-js/stable/regexp/split": ["es.string.split"], "core-js/stable/regexp/sticky": ["es.regexp.sticky"], "core-js/stable/regexp/test": ["es.regexp.exec", "es.regexp.test"], "core-js/stable/regexp/to-string": ["es.regexp.to-string"], "core-js/stable/set": ["es.object.to-string", "es.set", "es.string.iterator", "web.dom-collections.iterator"], "core-js/stable/set-immediate": ["web.immediate"], "core-js/stable/set-interval": ["web.timers"], "core-js/stable/set-timeout": ["web.timers"], "core-js/stable/string": ["es.regexp.exec", "es.string.code-point-at", "es.string.ends-with", "es.string.from-code-point", "es.string.includes", "es.string.iterator", "es.string.match", "es.string.match-all", "es.string.pad-end", "es.string.pad-start", "es.string.raw", "es.string.repeat", "es.string.replace", "es.string.search", "es.string.split", "es.string.starts-with", "es.string.trim", "es.string.trim-end", "es.string.trim-start", "es.string.anchor", "es.string.big", "es.string.blink", "es.string.bold", "es.string.fixed", "es.string.fontcolor", "es.string.fontsize", "es.string.italics", "es.string.link", "es.string.small", "es.string.strike", "es.string.sub", "es.string.sup"], "core-js/stable/string/anchor": ["es.string.anchor"], "core-js/stable/string/big": ["es.string.big"], "core-js/stable/string/blink": ["es.string.blink"], "core-js/stable/string/bold": ["es.string.bold"], "core-js/stable/string/code-point-at": ["es.string.code-point-at"], "core-js/stable/string/ends-with": ["es.string.ends-with"], "core-js/stable/string/fixed": ["es.string.fixed"], "core-js/stable/string/fontcolor": ["es.string.fontcolor"], "core-js/stable/string/fontsize": ["es.string.fontsize"], "core-js/stable/string/from-code-point": ["es.string.from-code-point"], "core-js/stable/string/includes": ["es.string.includes"], "core-js/stable/string/italics": ["es.string.italics"], "core-js/stable/string/iterator": ["es.string.iterator"], "core-js/stable/string/link": ["es.string.link"], "core-js/stable/string/match": ["es.regexp.exec", "es.string.match"], "core-js/stable/string/match-all": ["es.string.match-all"], "core-js/stable/string/pad-end": ["es.string.pad-end"], "core-js/stable/string/pad-start": ["es.string.pad-start"], "core-js/stable/string/raw": ["es.string.raw"], "core-js/stable/string/repeat": ["es.string.repeat"], "core-js/stable/string/replace": ["es.regexp.exec", "es.string.replace"], "core-js/stable/string/search": ["es.regexp.exec", "es.string.search"], "core-js/stable/string/small": ["es.string.small"], "core-js/stable/string/split": ["es.regexp.exec", "es.string.split"], "core-js/stable/string/starts-with": ["es.string.starts-with"], "core-js/stable/string/strike": ["es.string.strike"], "core-js/stable/string/sub": ["es.string.sub"], "core-js/stable/string/sup": ["es.string.sup"], "core-js/stable/string/trim": ["es.string.trim"], "core-js/stable/string/trim-end": ["es.string.trim-end"], "core-js/stable/string/trim-left": ["es.string.trim-start"], "core-js/stable/string/trim-right": ["es.string.trim-end"], "core-js/stable/string/trim-start": ["es.string.trim-start"], "core-js/stable/string/virtual": ["es.string.code-point-at", "es.string.ends-with", "es.string.includes", "es.string.iterator", "es.string.match", "es.string.match-all", "es.string.pad-end", "es.string.pad-start", "es.string.repeat", "es.string.replace", "es.string.search", "es.string.split", "es.string.starts-with", "es.string.trim", "es.string.trim-end", "es.string.trim-start", "es.string.anchor", "es.string.big", "es.string.blink", "es.string.bold", "es.string.fixed", "es.string.fontcolor", "es.string.fontsize", "es.string.italics", "es.string.link", "es.string.small", "es.string.strike", "es.string.sub", "es.string.sup"], "core-js/stable/string/virtual/anchor": ["es.string.anchor"], "core-js/stable/string/virtual/big": ["es.string.big"], "core-js/stable/string/virtual/blink": ["es.string.blink"], "core-js/stable/string/virtual/bold": ["es.string.bold"], "core-js/stable/string/virtual/code-point-at": ["es.string.code-point-at"], "core-js/stable/string/virtual/ends-with": ["es.string.ends-with"], "core-js/stable/string/virtual/fixed": ["es.string.fixed"], "core-js/stable/string/virtual/fontcolor": ["es.string.fontcolor"], "core-js/stable/string/virtual/fontsize": ["es.string.fontsize"], "core-js/stable/string/virtual/includes": ["es.string.includes"], "core-js/stable/string/virtual/italics": ["es.string.italics"], "core-js/stable/string/virtual/iterator": ["es.string.iterator"], "core-js/stable/string/virtual/link": ["es.string.link"], "core-js/stable/string/virtual/match-all": ["es.string.match-all"], "core-js/stable/string/virtual/pad-end": ["es.string.pad-end"], "core-js/stable/string/virtual/pad-start": ["es.string.pad-start"], "core-js/stable/string/virtual/repeat": ["es.string.repeat"], "core-js/stable/string/virtual/small": ["es.string.small"], "core-js/stable/string/virtual/starts-with": ["es.string.starts-with"], "core-js/stable/string/virtual/strike": ["es.string.strike"], "core-js/stable/string/virtual/sub": ["es.string.sub"], "core-js/stable/string/virtual/sup": ["es.string.sup"], "core-js/stable/string/virtual/trim": ["es.string.trim"], "core-js/stable/string/virtual/trim-end": ["es.string.trim-end"], "core-js/stable/string/virtual/trim-left": ["es.string.trim-start"], "core-js/stable/string/virtual/trim-right": ["es.string.trim-end"], "core-js/stable/string/virtual/trim-start": ["es.string.trim-start"], "core-js/stable/symbol": ["es.symbol", "es.symbol.description", "es.symbol.async-iterator", "es.symbol.has-instance", "es.symbol.is-concat-spreadable", "es.symbol.iterator", "es.symbol.match", "es.symbol.match-all", "es.symbol.replace", "es.symbol.search", "es.symbol.species", "es.symbol.split", "es.symbol.to-primitive", "es.symbol.to-string-tag", "es.symbol.unscopables", "es.array.concat", "es.json.to-string-tag", "es.math.to-string-tag", "es.object.to-string"], "core-js/stable/symbol/async-iterator": ["es.symbol.async-iterator"], "core-js/stable/symbol/description": ["es.symbol.description"], "core-js/stable/symbol/for": ["es.symbol"], "core-js/stable/symbol/has-instance": ["es.symbol.has-instance", "es.function.has-instance"], "core-js/stable/symbol/is-concat-spreadable": ["es.symbol.is-concat-spreadable", "es.array.concat"], "core-js/stable/symbol/iterator": ["es.symbol.iterator", "es.string.iterator", "web.dom-collections.iterator"], "core-js/stable/symbol/key-for": ["es.symbol"], "core-js/stable/symbol/match": ["es.symbol.match", "es.string.match"], "core-js/stable/symbol/match-all": ["es.symbol.match-all", "es.string.match-all"], "core-js/stable/symbol/replace": ["es.symbol.replace", "es.string.replace"], "core-js/stable/symbol/search": ["es.symbol.search", "es.string.search"], "core-js/stable/symbol/species": ["es.symbol.species"], "core-js/stable/symbol/split": ["es.symbol.split", "es.string.split"], "core-js/stable/symbol/to-primitive": ["es.symbol.to-primitive"], "core-js/stable/symbol/to-string-tag": ["es.symbol.to-string-tag", "es.json.to-string-tag", "es.math.to-string-tag", "es.object.to-string"], "core-js/stable/symbol/unscopables": ["es.symbol.unscopables"], "core-js/stable/typed-array": ["es.object.to-string", "es.typed-array.float32-array", "es.typed-array.float64-array", "es.typed-array.int8-array", "es.typed-array.int16-array", "es.typed-array.int32-array", "es.typed-array.uint8-array", "es.typed-array.uint8-clamped-array", "es.typed-array.uint16-array", "es.typed-array.uint32-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/stable/typed-array/copy-within": ["es.typed-array.copy-within"], "core-js/stable/typed-array/entries": ["es.typed-array.iterator"], "core-js/stable/typed-array/every": ["es.typed-array.every"], "core-js/stable/typed-array/fill": ["es.typed-array.fill"], "core-js/stable/typed-array/filter": ["es.typed-array.filter"], "core-js/stable/typed-array/find": ["es.typed-array.find"], "core-js/stable/typed-array/find-index": ["es.typed-array.find-index"], "core-js/stable/typed-array/float32-array": ["es.object.to-string", "es.typed-array.float32-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/stable/typed-array/float64-array": ["es.object.to-string", "es.typed-array.float64-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/stable/typed-array/for-each": ["es.typed-array.for-each"], "core-js/stable/typed-array/from": ["es.typed-array.from"], "core-js/stable/typed-array/includes": ["es.typed-array.includes"], "core-js/stable/typed-array/index-of": ["es.typed-array.index-of"], "core-js/stable/typed-array/int16-array": ["es.object.to-string", "es.typed-array.int16-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/stable/typed-array/int32-array": ["es.object.to-string", "es.typed-array.int32-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/stable/typed-array/int8-array": ["es.object.to-string", "es.typed-array.int8-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/stable/typed-array/iterator": ["es.typed-array.iterator"], "core-js/stable/typed-array/join": ["es.typed-array.join"], "core-js/stable/typed-array/keys": ["es.typed-array.iterator"], "core-js/stable/typed-array/last-index-of": ["es.typed-array.last-index-of"], "core-js/stable/typed-array/map": ["es.typed-array.map"], "core-js/stable/typed-array/of": ["es.typed-array.of"], "core-js/stable/typed-array/reduce": ["es.typed-array.reduce"], "core-js/stable/typed-array/reduce-right": ["es.typed-array.reduce-right"], "core-js/stable/typed-array/reverse": ["es.typed-array.reverse"], "core-js/stable/typed-array/set": ["es.typed-array.set"], "core-js/stable/typed-array/slice": ["es.typed-array.slice"], "core-js/stable/typed-array/some": ["es.typed-array.some"], "core-js/stable/typed-array/sort": ["es.typed-array.sort"], "core-js/stable/typed-array/subarray": ["es.typed-array.subarray"], "core-js/stable/typed-array/to-locale-string": ["es.typed-array.to-locale-string"], "core-js/stable/typed-array/to-string": ["es.typed-array.to-string"], "core-js/stable/typed-array/uint16-array": ["es.object.to-string", "es.typed-array.uint16-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/stable/typed-array/uint32-array": ["es.object.to-string", "es.typed-array.uint32-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/stable/typed-array/uint8-array": ["es.object.to-string", "es.typed-array.uint8-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/stable/typed-array/uint8-clamped-array": ["es.object.to-string", "es.typed-array.uint8-clamped-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string"], "core-js/stable/typed-array/values": ["es.typed-array.iterator"], "core-js/stable/url": ["web.url", "web.url.to-json", "web.url-search-params"], "core-js/stable/url-search-params": ["web.url-search-params"], "core-js/stable/url/to-json": ["web.url.to-json"], "core-js/stable/weak-map": ["es.object.to-string", "es.weak-map", "web.dom-collections.iterator"], "core-js/stable/weak-set": ["es.object.to-string", "es.weak-set", "web.dom-collections.iterator"], "core-js/stage": ["esnext.aggregate-error", "esnext.array.is-template-object", "esnext.array.last-index", "esnext.array.last-item", "esnext.async-iterator.constructor", "esnext.async-iterator.as-indexed-pairs", "esnext.async-iterator.drop", "esnext.async-iterator.every", "esnext.async-iterator.filter", "esnext.async-iterator.find", "esnext.async-iterator.flat-map", "esnext.async-iterator.for-each", "esnext.async-iterator.from", "esnext.async-iterator.map", "esnext.async-iterator.reduce", "esnext.async-iterator.some", "esnext.async-iterator.take", "esnext.async-iterator.to-array", "esnext.composite-key", "esnext.composite-symbol", "esnext.global-this", "esnext.iterator.constructor", "esnext.iterator.as-indexed-pairs", "esnext.iterator.drop", "esnext.iterator.every", "esnext.iterator.filter", "esnext.iterator.find", "esnext.iterator.flat-map", "esnext.iterator.for-each", "esnext.iterator.from", "esnext.iterator.map", "esnext.iterator.reduce", "esnext.iterator.some", "esnext.iterator.take", "esnext.iterator.to-array", "esnext.map.delete-all", "esnext.map.every", "esnext.map.filter", "esnext.map.find", "esnext.map.find-key", "esnext.map.from", "esnext.map.group-by", "esnext.map.includes", "esnext.map.key-by", "esnext.map.key-of", "esnext.map.map-keys", "esnext.map.map-values", "esnext.map.merge", "esnext.map.of", "esnext.map.reduce", "esnext.map.some", "esnext.map.update", "esnext.map.update-or-insert", "esnext.map.upsert", "esnext.math.clamp", "esnext.math.deg-per-rad", "esnext.math.degrees", "esnext.math.fscale", "esnext.math.iaddh", "esnext.math.imulh", "esnext.math.isubh", "esnext.math.rad-per-deg", "esnext.math.radians", "esnext.math.scale", "esnext.math.seeded-prng", "esnext.math.signbit", "esnext.math.umulh", "esnext.number.from-string", "esnext.object.iterate-entries", "esnext.object.iterate-keys", "esnext.object.iterate-values", "esnext.observable", "esnext.promise.all-settled", "esnext.promise.any", "esnext.promise.try", "esnext.reflect.define-metadata", "esnext.reflect.delete-metadata", "esnext.reflect.get-metadata", "esnext.reflect.get-metadata-keys", "esnext.reflect.get-own-metadata", "esnext.reflect.get-own-metadata-keys", "esnext.reflect.has-metadata", "esnext.reflect.has-own-metadata", "esnext.reflect.metadata", "esnext.set.add-all", "esnext.set.delete-all", "esnext.set.difference", "esnext.set.every", "esnext.set.filter", "esnext.set.find", "esnext.set.from", "esnext.set.intersection", "esnext.set.is-disjoint-from", "esnext.set.is-subset-of", "esnext.set.is-superset-of", "esnext.set.join", "esnext.set.map", "esnext.set.of", "esnext.set.reduce", "esnext.set.some", "esnext.set.symmetric-difference", "esnext.set.union", "esnext.string.at", "esnext.string.code-points", "esnext.string.match-all", "esnext.string.replace-all", "esnext.symbol.async-dispose", "esnext.symbol.dispose", "esnext.symbol.observable", "esnext.symbol.pattern-match", "esnext.symbol.replace-all", "esnext.weak-map.delete-all", "esnext.weak-map.from", "esnext.weak-map.of", "esnext.weak-map.upsert", "esnext.weak-set.add-all", "esnext.weak-set.delete-all", "esnext.weak-set.from", "esnext.weak-set.of", "web.url", "web.url.to-json", "web.url-search-params"], "core-js/stage/0": ["esnext.aggregate-error", "esnext.array.is-template-object", "esnext.array.last-index", "esnext.array.last-item", "esnext.async-iterator.constructor", "esnext.async-iterator.as-indexed-pairs", "esnext.async-iterator.drop", "esnext.async-iterator.every", "esnext.async-iterator.filter", "esnext.async-iterator.find", "esnext.async-iterator.flat-map", "esnext.async-iterator.for-each", "esnext.async-iterator.from", "esnext.async-iterator.map", "esnext.async-iterator.reduce", "esnext.async-iterator.some", "esnext.async-iterator.take", "esnext.async-iterator.to-array", "esnext.composite-key", "esnext.composite-symbol", "esnext.global-this", "esnext.iterator.constructor", "esnext.iterator.as-indexed-pairs", "esnext.iterator.drop", "esnext.iterator.every", "esnext.iterator.filter", "esnext.iterator.find", "esnext.iterator.flat-map", "esnext.iterator.for-each", "esnext.iterator.from", "esnext.iterator.map", "esnext.iterator.reduce", "esnext.iterator.some", "esnext.iterator.take", "esnext.iterator.to-array", "esnext.map.delete-all", "esnext.map.every", "esnext.map.filter", "esnext.map.find", "esnext.map.find-key", "esnext.map.from", "esnext.map.group-by", "esnext.map.includes", "esnext.map.key-by", "esnext.map.key-of", "esnext.map.map-keys", "esnext.map.map-values", "esnext.map.merge", "esnext.map.of", "esnext.map.reduce", "esnext.map.some", "esnext.map.update", "esnext.map.update-or-insert", "esnext.map.upsert", "esnext.math.clamp", "esnext.math.deg-per-rad", "esnext.math.degrees", "esnext.math.fscale", "esnext.math.iaddh", "esnext.math.imulh", "esnext.math.isubh", "esnext.math.rad-per-deg", "esnext.math.radians", "esnext.math.scale", "esnext.math.seeded-prng", "esnext.math.signbit", "esnext.math.umulh", "esnext.number.from-string", "esnext.object.iterate-entries", "esnext.object.iterate-keys", "esnext.object.iterate-values", "esnext.observable", "esnext.promise.all-settled", "esnext.promise.any", "esnext.promise.try", "esnext.set.add-all", "esnext.set.delete-all", "esnext.set.difference", "esnext.set.every", "esnext.set.filter", "esnext.set.find", "esnext.set.from", "esnext.set.intersection", "esnext.set.is-disjoint-from", "esnext.set.is-subset-of", "esnext.set.is-superset-of", "esnext.set.join", "esnext.set.map", "esnext.set.of", "esnext.set.reduce", "esnext.set.some", "esnext.set.symmetric-difference", "esnext.set.union", "esnext.string.at", "esnext.string.code-points", "esnext.string.match-all", "esnext.string.replace-all", "esnext.symbol.async-dispose", "esnext.symbol.dispose", "esnext.symbol.observable", "esnext.symbol.pattern-match", "esnext.symbol.replace-all", "esnext.weak-map.delete-all", "esnext.weak-map.from", "esnext.weak-map.of", "esnext.weak-map.upsert", "esnext.weak-set.add-all", "esnext.weak-set.delete-all", "esnext.weak-set.from", "esnext.weak-set.of", "web.url", "web.url.to-json", "web.url-search-params"], "core-js/stage/1": ["esnext.aggregate-error", "esnext.array.is-template-object", "esnext.array.last-index", "esnext.array.last-item", "esnext.async-iterator.constructor", "esnext.async-iterator.as-indexed-pairs", "esnext.async-iterator.drop", "esnext.async-iterator.every", "esnext.async-iterator.filter", "esnext.async-iterator.find", "esnext.async-iterator.flat-map", "esnext.async-iterator.for-each", "esnext.async-iterator.from", "esnext.async-iterator.map", "esnext.async-iterator.reduce", "esnext.async-iterator.some", "esnext.async-iterator.take", "esnext.async-iterator.to-array", "esnext.composite-key", "esnext.composite-symbol", "esnext.global-this", "esnext.iterator.constructor", "esnext.iterator.as-indexed-pairs", "esnext.iterator.drop", "esnext.iterator.every", "esnext.iterator.filter", "esnext.iterator.find", "esnext.iterator.flat-map", "esnext.iterator.for-each", "esnext.iterator.from", "esnext.iterator.map", "esnext.iterator.reduce", "esnext.iterator.some", "esnext.iterator.take", "esnext.iterator.to-array", "esnext.map.delete-all", "esnext.map.every", "esnext.map.filter", "esnext.map.find", "esnext.map.find-key", "esnext.map.from", "esnext.map.group-by", "esnext.map.includes", "esnext.map.key-by", "esnext.map.key-of", "esnext.map.map-keys", "esnext.map.map-values", "esnext.map.merge", "esnext.map.of", "esnext.map.reduce", "esnext.map.some", "esnext.map.update", "esnext.map.update-or-insert", "esnext.map.upsert", "esnext.math.clamp", "esnext.math.deg-per-rad", "esnext.math.degrees", "esnext.math.fscale", "esnext.math.rad-per-deg", "esnext.math.radians", "esnext.math.scale", "esnext.math.seeded-prng", "esnext.math.signbit", "esnext.number.from-string", "esnext.object.iterate-entries", "esnext.object.iterate-keys", "esnext.object.iterate-values", "esnext.observable", "esnext.promise.all-settled", "esnext.promise.any", "esnext.promise.try", "esnext.set.add-all", "esnext.set.delete-all", "esnext.set.difference", "esnext.set.every", "esnext.set.filter", "esnext.set.find", "esnext.set.from", "esnext.set.intersection", "esnext.set.is-disjoint-from", "esnext.set.is-subset-of", "esnext.set.is-superset-of", "esnext.set.join", "esnext.set.map", "esnext.set.of", "esnext.set.reduce", "esnext.set.some", "esnext.set.symmetric-difference", "esnext.set.union", "esnext.string.code-points", "esnext.string.match-all", "esnext.string.replace-all", "esnext.symbol.async-dispose", "esnext.symbol.dispose", "esnext.symbol.observable", "esnext.symbol.pattern-match", "esnext.symbol.replace-all", "esnext.weak-map.delete-all", "esnext.weak-map.from", "esnext.weak-map.of", "esnext.weak-map.upsert", "esnext.weak-set.add-all", "esnext.weak-set.delete-all", "esnext.weak-set.from", "esnext.weak-set.of"], "core-js/stage/2": ["esnext.aggregate-error", "esnext.array.is-template-object", "esnext.async-iterator.constructor", "esnext.async-iterator.as-indexed-pairs", "esnext.async-iterator.drop", "esnext.async-iterator.every", "esnext.async-iterator.filter", "esnext.async-iterator.find", "esnext.async-iterator.flat-map", "esnext.async-iterator.for-each", "esnext.async-iterator.from", "esnext.async-iterator.map", "esnext.async-iterator.reduce", "esnext.async-iterator.some", "esnext.async-iterator.take", "esnext.async-iterator.to-array", "esnext.global-this", "esnext.iterator.constructor", "esnext.iterator.as-indexed-pairs", "esnext.iterator.drop", "esnext.iterator.every", "esnext.iterator.filter", "esnext.iterator.find", "esnext.iterator.flat-map", "esnext.iterator.for-each", "esnext.iterator.from", "esnext.iterator.map", "esnext.iterator.reduce", "esnext.iterator.some", "esnext.iterator.take", "esnext.iterator.to-array", "esnext.map.update-or-insert", "esnext.map.upsert", "esnext.promise.all-settled", "esnext.promise.any", "esnext.set.difference", "esnext.set.intersection", "esnext.set.is-disjoint-from", "esnext.set.is-subset-of", "esnext.set.is-superset-of", "esnext.set.symmetric-difference", "esnext.set.union", "esnext.string.match-all", "esnext.string.replace-all", "esnext.symbol.async-dispose", "esnext.symbol.dispose", "esnext.symbol.replace-all", "esnext.weak-map.upsert"], "core-js/stage/3": ["esnext.aggregate-error", "esnext.global-this", "esnext.promise.all-settled", "esnext.promise.any", "esnext.string.match-all", "esnext.string.replace-all", "esnext.symbol.replace-all"], "core-js/stage/4": ["esnext.global-this", "esnext.promise.all-settled", "esnext.string.match-all"], "core-js/stage/pre": ["esnext.aggregate-error", "esnext.array.is-template-object", "esnext.array.last-index", "esnext.array.last-item", "esnext.async-iterator.constructor", "esnext.async-iterator.as-indexed-pairs", "esnext.async-iterator.drop", "esnext.async-iterator.every", "esnext.async-iterator.filter", "esnext.async-iterator.find", "esnext.async-iterator.flat-map", "esnext.async-iterator.for-each", "esnext.async-iterator.from", "esnext.async-iterator.map", "esnext.async-iterator.reduce", "esnext.async-iterator.some", "esnext.async-iterator.take", "esnext.async-iterator.to-array", "esnext.composite-key", "esnext.composite-symbol", "esnext.global-this", "esnext.iterator.constructor", "esnext.iterator.as-indexed-pairs", "esnext.iterator.drop", "esnext.iterator.every", "esnext.iterator.filter", "esnext.iterator.find", "esnext.iterator.flat-map", "esnext.iterator.for-each", "esnext.iterator.from", "esnext.iterator.map", "esnext.iterator.reduce", "esnext.iterator.some", "esnext.iterator.take", "esnext.iterator.to-array", "esnext.map.delete-all", "esnext.map.every", "esnext.map.filter", "esnext.map.find", "esnext.map.find-key", "esnext.map.from", "esnext.map.group-by", "esnext.map.includes", "esnext.map.key-by", "esnext.map.key-of", "esnext.map.map-keys", "esnext.map.map-values", "esnext.map.merge", "esnext.map.of", "esnext.map.reduce", "esnext.map.some", "esnext.map.update", "esnext.map.update-or-insert", "esnext.map.upsert", "esnext.math.clamp", "esnext.math.deg-per-rad", "esnext.math.degrees", "esnext.math.fscale", "esnext.math.iaddh", "esnext.math.imulh", "esnext.math.isubh", "esnext.math.rad-per-deg", "esnext.math.radians", "esnext.math.scale", "esnext.math.seeded-prng", "esnext.math.signbit", "esnext.math.umulh", "esnext.number.from-string", "esnext.object.iterate-entries", "esnext.object.iterate-keys", "esnext.object.iterate-values", "esnext.observable", "esnext.promise.all-settled", "esnext.promise.any", "esnext.promise.try", "esnext.reflect.define-metadata", "esnext.reflect.delete-metadata", "esnext.reflect.get-metadata", "esnext.reflect.get-metadata-keys", "esnext.reflect.get-own-metadata", "esnext.reflect.get-own-metadata-keys", "esnext.reflect.has-metadata", "esnext.reflect.has-own-metadata", "esnext.reflect.metadata", "esnext.set.add-all", "esnext.set.delete-all", "esnext.set.difference", "esnext.set.every", "esnext.set.filter", "esnext.set.find", "esnext.set.from", "esnext.set.intersection", "esnext.set.is-disjoint-from", "esnext.set.is-subset-of", "esnext.set.is-superset-of", "esnext.set.join", "esnext.set.map", "esnext.set.of", "esnext.set.reduce", "esnext.set.some", "esnext.set.symmetric-difference", "esnext.set.union", "esnext.string.at", "esnext.string.code-points", "esnext.string.match-all", "esnext.string.replace-all", "esnext.symbol.async-dispose", "esnext.symbol.dispose", "esnext.symbol.observable", "esnext.symbol.pattern-match", "esnext.symbol.replace-all", "esnext.weak-map.delete-all", "esnext.weak-map.from", "esnext.weak-map.of", "esnext.weak-map.upsert", "esnext.weak-set.add-all", "esnext.weak-set.delete-all", "esnext.weak-set.from", "esnext.weak-set.of", "web.url", "web.url.to-json", "web.url-search-params"], "core-js/web": ["web.dom-collections.for-each", "web.dom-collections.iterator", "web.immediate", "web.queue-microtask", "web.timers", "web.url", "web.url.to-json", "web.url-search-params"], "core-js/web/dom-collections": ["web.dom-collections.for-each", "web.dom-collections.iterator"], "core-js/web/immediate": ["web.immediate"], "core-js/web/queue-microtask": ["web.queue-microtask"], "core-js/web/timers": ["web.timers"], "core-js/web/url": ["web.url", "web.url.to-json", "web.url-search-params"], "core-js/web/url-search-params": ["web.url-search-params"]}