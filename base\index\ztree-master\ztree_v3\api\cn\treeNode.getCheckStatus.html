<div class="apiDetail">
<div>
	<h2><span>Function()</span><span class="path">treeNode.</span>getCheckStatus</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.excheck</span> 扩展 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>获取节点 checkbox / radio 半勾选状态。<span class="highlight_red">[setting.check.enable = true 时有效]</span></p>
			<p class="highlight_red">初始化节点数据时，由 zTree 增加此属性，请勿提前赋值</p>
		</div>
	</div>
	<h3>Function 参数说明</h3>
	<div class="desc">
	<h4><b>返回值</b><span>JSON</span></h4>
	<pre xmlns=""><code>{
	checked: true, //等同于 treeNode.checked
	half: true  //规则见下表
}</code></pre>
	<table width="100%" border="0" cellspacing="1" cellpadding="0">
		<thead>
			<tr><td colspan="4">setting.check.checkType = "checkbox"</td></tr>
			<tr><td>treeNode.checked</td><td>treeNode.check_Child_State</td><td>treeNode.halfCheck</td><td> half </td></tr>
		</thead>
		<tbody>
			<tr><td>-</td><td>-</td><td>true</td><td>true</td></tr>
			<tr><td colspan="4">&nbsp;</td></tr>
			<tr><td>true</td><td>-1</td><td>false</td><td>false</td></tr>
			<tr><td>true</td><td>0</td><td>false</td><td>true</td></tr>
			<tr><td>true</td><td>1</td><td>false</td><td>true</td></tr>
			<tr><td>true</td><td>2</td><td>false</td><td>false</td></tr>
			<tr><td colspan="4">&nbsp;</td></tr>
			<tr><td>false</td><td>-1</td><td>false</td><td>false</td></tr>
			<tr><td>false</td><td>0</td><td>false</td><td>false</td></tr>
			<tr><td>false</td><td>1</td><td>false</td><td>true</td></tr>
			<tr><td>false</td><td>2</td><td>false</td><td>true</td></tr>
		</tbody>
	</table>
	<br/>
	<table width="100%" border="0" cellspacing="1" cellpadding="0">
		<thead>
			<tr><td colspan="4">setting.check.checkType = "radio"</td></tr>
			<tr><td>treeNode.checked</td><td>treeNode.check_Child_State</td><td>treeNode.halfCheck</td><td> half </td></tr>
		</thead>
		<tbody>
			<tr><td>-</td><td>-</td><td>true</td><td>true</td></tr>
			<tr><td colspan="4">&nbsp;</td></tr>
			<tr><td>true</td><td>-1</td><td>false</td><td>false</td></tr>
			<tr><td>true</td><td>0</td><td>false</td><td>false</td></tr>
			<tr><td>true</td><td>2</td><td>false</td><td>true</td></tr>
			<tr><td colspan="4">&nbsp;</td></tr>
			<tr><td>false</td><td>-1</td><td>false</td><td>false</td></tr>
			<tr><td>false</td><td>0</td><td>false</td><td>false</td></tr>
			<tr><td>false</td><td>2</td><td>false</td><td>true</td></tr>
		</tbody>
	</table>
	</div>
	<h3>treeNode 举例</h3>
	<h4>1. 获取第一个根节点的半选状态</h4>
	<pre xmlns=""><code>var treeObj = $.fn.zTree.getZTreeObj("tree");
var halfCheck = treeObj.getNodes()[0].getCheckStatus();
</code></pre>
</div>
</div>