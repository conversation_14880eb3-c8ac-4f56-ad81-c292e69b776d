{"name": "regenerate-unicode-properties", "version": "8.2.0", "description": "Regenerate sets for Unicode properties and values.", "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "main": "index.js", "engines": {"node": ">=4"}, "files": ["LICENSE-MIT.txt", "index.js", "unicode-version.js", "Binary_Property", "General_Category", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "keywords": ["unicode", "unicode-data", "regenerate"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate-unicode-properties.git"}, "bugs": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues", "dependencies": {"regenerate": "^1.4.0"}, "devDependencies": {"ava": "^3.5.0", "fs-extra": "^8.1.0", "jsesc": "^2.5.2", "unicode-13.0.0": "^0.8.0", "unicode-canonical-property-names-ecmascript": "^1.0.4"}, "scripts": {"build": "node build.js", "test": "ava tests/tests.js"}}