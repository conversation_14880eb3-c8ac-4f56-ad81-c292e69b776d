Vue.component('todos-header', {
	template: `
		<header class="header">
			<h1>todos</h1>
			<input class="new-todo"
			placeholder="What needs to be done?"
			autofocus
			v-model="name"
			@keyup.enter="add">
		</header>
	`,
	data() {
		return {
			name: ''
		}
	},
	methods: {
		add () {
			if(this.name.trim() == '') return
			console.log(' this.zname :>> ',  this.name);
			this.$emit('zadd', this.name);
			this.name = ''
		}
	}
});
