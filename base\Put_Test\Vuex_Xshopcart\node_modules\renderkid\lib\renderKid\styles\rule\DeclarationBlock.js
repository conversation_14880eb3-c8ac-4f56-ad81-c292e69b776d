// Generated by CoffeeScript 1.9.3
var Arbitrary, DeclarationBlock, declarationClasses;

module.exports = DeclarationBlock = (function() {
  var self;

  self = DeclarationBlock;

  function DeclarationBlock() {
    this._declarations = {};
  }

  DeclarationBlock.prototype.set = function(prop, value) {
    var key, val;
    if (typeof prop === 'object') {
      for (key in prop) {
        val = prop[key];
        this.set(key, val);
      }
      return this;
    }
    prop = self.sanitizeProp(prop);
    this._getDeclarationClass(prop).setOnto(this._declarations, prop, value);
    return this;
  };

  DeclarationBlock.prototype._getDeclarationClass = function(prop) {
    var cls;
    if (prop[0] === '_') {
      return Arbitrary;
    }
    if (!(cls = declarationClasses[prop])) {
      throw Error("Unknown property `" + prop + "`. Write it as `_" + prop + "` if you're defining a custom property");
    }
    return cls;
  };

  DeclarationBlock.sanitizeProp = function(prop) {
    return String(prop).trim();
  };

  return DeclarationBlock;

})();

Arbitrary = require('./declarationBlock/Arbitrary');

declarationClasses = {
  color: require('./declarationBlock/Color'),
  background: require('./declarationBlock/Background'),
  width: require('./declarationBlock/Width'),
  height: require('./declarationBlock/Height'),
  bullet: require('./declarationBlock/Bullet'),
  display: require('./declarationBlock/Display'),
  margin: require('./declarationBlock/Margin'),
  marginTop: require('./declarationBlock/MarginTop'),
  marginLeft: require('./declarationBlock/MarginLeft'),
  marginRight: require('./declarationBlock/MarginRight'),
  marginBottom: require('./declarationBlock/MarginBottom'),
  padding: require('./declarationBlock/Padding'),
  paddingTop: require('./declarationBlock/PaddingTop'),
  paddingLeft: require('./declarationBlock/PaddingLeft'),
  paddingRight: require('./declarationBlock/PaddingRight'),
  paddingBottom: require('./declarationBlock/PaddingBottom')
};
