{"name": "@types/minimatch", "version": "3.0.3", "description": "TypeScript definitions for Minimatch", "license": "MIT", "contributors": [{"name": "vvakame", "url": "https://github.com/vvakame", "githubUsername": "vvakame"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/shantmarouti", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "e768e36348874adcc93ac67e9c3c7b5fcbd39079c0610ec16e410b8f851308d1", "typeScriptVersion": "2.0"}