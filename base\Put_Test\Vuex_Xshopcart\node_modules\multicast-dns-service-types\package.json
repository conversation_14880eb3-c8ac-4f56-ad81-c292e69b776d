{"name": "multicast-dns-service-types", "version": "1.1.0", "description": "Parse and stringify mdns service types", "main": "index.js", "dependencies": {}, "devDependencies": {"standard": "^3.5.0", "tape": "^4.0.0"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/multicast-dns-service-types.git"}, "scripts": {"test": "standard && tape test.js"}, "author": "<PERSON> (@mafintosh)", "license": "MIT", "keywords": ["mdns", "bonjour", "zero", "conf"], "bugs": {"url": "https://github.com/mafintosh/multicast-dns-service-types/issues"}, "homepage": "https://github.com/mafintosh/multicast-dns-service-types"}