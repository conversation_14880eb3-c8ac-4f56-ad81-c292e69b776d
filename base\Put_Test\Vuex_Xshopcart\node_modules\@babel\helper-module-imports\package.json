{"name": "@babel/helper-module-imports", "version": "7.10.4", "description": "Babel helper functions for inserting module loads", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-module-imports"}, "main": "lib/index.js", "dependencies": {"@babel/types": "^7.10.4"}, "devDependencies": {"@babel/core": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df"}