{"name": "@babel/runtime", "version": "7.11.2", "description": "babel's modular runtime helpers", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-runtime"}, "homepage": "https://babeljs.io/", "author": "<PERSON> <<EMAIL>>", "dependencies": {"regenerator-runtime": "^0.13.4"}, "devDependencies": {"@babel/helpers": "^7.10.4"}, "gitHead": "bc7a811fce3ceeea393229299c1cdb63858608e6"}