{"name": "supports-color", "version": "2.0.0", "description": "Detect whether a terminal supports color", "license": "MIT", "repository": "chalk/supports-color", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": ["Sindre Sorhus <<EMAIL>> (sindresorhus.com)", "<PERSON> <<EMAIL>> (jbnicolai.com)"], "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect"], "devDependencies": {"mocha": "*", "require-uncached": "^1.0.2"}}