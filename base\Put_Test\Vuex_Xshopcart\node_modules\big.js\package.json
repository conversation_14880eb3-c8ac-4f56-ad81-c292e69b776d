{"name": "big.js", "description": "A small, fast, easy-to-use library for arbitrary-precision decimal arithmetic", "version": "5.2.2", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "repository": {"type": "git", "url": "https://github.com/MikeMcl/big.js.git"}, "main": "big", "browser": "big.js", "module": "big.mjs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/MikeMcl/big.js/issues"}, "engines": {"node": "*"}, "license": "MIT", "scripts": {"test": "node ./test/every-test.js", "build": "uglifyjs big.js --source-map -c -m -o big.min.js"}, "files": ["big.js", "big.mjs", "big.min.js"], "collective": {"type": "opencollective", "url": "https://opencollective.com/bigjs"}}