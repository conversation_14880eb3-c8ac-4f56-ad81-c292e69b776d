const vm = new Vue({
	el: '.todoapp',
	data: {
		//1. 父组件提供数据
		fa_list: [
			{ id: 1, name: '吃饭2', done: false },
			{ id: 2, name: '睡觉', done: true },
			{ id: 3, name: '打豆豆', done: false }
		]
	},
	methods: {
		fa_add(name){
			// console.log("%c [z_add name ]", "font-size:13px; background:#00ffff; color:red;", name)
			const obj = {
				id:Date.now(),
				name,
				done:false
			};
			this.fa_list.unshift(obj)
		},
		fa_del(id) {
			// console.log("%c [ z id ]", "font-size:13px; background:#00ffff; color:red;", id)
			this.fa_list = this.fa_list.filter(item=>item.id!=id)
		}
	}
});


