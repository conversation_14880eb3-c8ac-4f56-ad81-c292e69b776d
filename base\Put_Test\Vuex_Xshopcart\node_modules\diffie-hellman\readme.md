diffie hellman [![Build Status](https://travis-ci.org/crypto-browserify/diffie-hellman.svg)](https://travis-ci.org/crypto-browserify/diffie-hellman)
====

pure js diffie-hellman, same api as node, most hard parts thanks to [bn.js](https://www.npmjs.org/package/bn.js) by [@indutny](https://github.com/indutny).  In node just returns an object with `crypto.createDiffieHellman` and `crypto.getDiffieHellman` in the browser returns a shim. To require the pure JavaScript one in node `require('diffie-hellman/browser');`;