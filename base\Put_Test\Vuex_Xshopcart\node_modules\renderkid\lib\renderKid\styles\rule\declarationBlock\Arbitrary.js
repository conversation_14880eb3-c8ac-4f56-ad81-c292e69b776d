// Generated by CoffeeScript 1.9.3
var Arbitrary, _Declaration,
  extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
  hasProp = {}.hasOwnProperty;

_Declaration = require('./_Declaration');

module.exports = Arbitrary = (function(superClass) {
  extend(Arbitrary, superClass);

  function Arbitrary() {
    return Arbitrary.__super__.constructor.apply(this, arguments);
  }

  return Arbitrary;

})(_Declaration);
