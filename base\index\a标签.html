<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>a标签</title>
    <style>
        a{
            color:red;
            text-decoration: none;
        }
        a:hover{
            color:blue;
        }
        a:visited{ 
            /* 只有有历史记录才能使用 */
            color:blue;
        }
        div{
            width: 100px;
            height: 100px;
            background: yellowgreen;
        }
    </style>
</head>

<body>
    <!-- <a href="http://www.baidu.com" >点击</a> -->
    <button class="btn">点击</button>
    <script src="./jquery/jquery-1.12.4.js"></script>
    <script>
        console.log("%c [ document ]", "font-size:13px; background:#00ffff; color:red;", document)
        $(document).mouseup((e)=>{
            let btn = $('.btn')
            if(!btn.is(e.target)&&btn.has(e.target).length===0){
                alert(1111)
            }
        })
    </script>
</body>

</html>