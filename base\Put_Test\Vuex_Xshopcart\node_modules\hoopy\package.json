{"name": "hoopy", "version": "0.1.4", "license": "MIT", "description": "Like an array, but rounder.", "keywords": ["data", "structure", "collection", "array", "circular", "extends", "proxy"], "author": "<PERSON> <<EMAIL>> (https://philbooth.me/)", "main": "index.js", "engines": {"node": ">= 6.0.0"}, "scripts": {"lint": "eslint .", "test": "mocha --es_staging --ui tdd test.js", "release": "release"}, "repository": {"type": "git", "url": "git+https://gitlab.com/philbooth/hoopy.git"}, "homepage": "https://gitlab.com/philbooth/hoopy#readme", "bugs": {"url": "https://gitlab.com/philbooth/hoopy/issues"}, "devDependencies": {"chai": "^4.1.2", "eslint": "^3.19.0", "mocha": "^5.2.0", "please-release-me": "^2.0.2"}}