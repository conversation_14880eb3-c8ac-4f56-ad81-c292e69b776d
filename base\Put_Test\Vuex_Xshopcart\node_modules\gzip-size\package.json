{"name": "gzip-size", "version": "5.1.1", "description": "Get the gzipped size of a string or buffer", "license": "MIT", "repository": "sindresorhus/gzip-size", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["app", "tool", "zlib", "gzip", "compressed", "size", "string", "buffer"], "dependencies": {"duplexer": "^0.1.1", "pify": "^4.0.1"}, "devDependencies": {"ava": "^1.4.1", "p-event": "^2.1.0", "tsd": "^0.7.2", "xo": "^0.24.0"}}