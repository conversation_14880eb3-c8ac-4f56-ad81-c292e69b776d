<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Guide - Sedentary Reminder</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        
        .content-section {
            padding: 2rem 0;
        }
        
        .back-btn {
            margin-bottom: 2rem;
        }
        
        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        h2 {
            color: #667eea;
            font-weight: 600;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            border-left: 4px solid #667eea;
        }
        
        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
        }
        
        .tip-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col">
                    <h1><i class="fas fa-book me-3"></i>User Guide</h1>
                    <p class="lead mb-0">Learn how to use Sedentary Reminder effectively</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container content-section">
        <div class="back-btn">
            <a href="index.html" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Sedentary Reminder
            </a>
        </div>

        <div class="row">
            <div class="col-lg-10 mx-auto">
                <h2>Getting Started</h2>
                <div class="feature-card">
                    <h4><i class="fas fa-play-circle me-2"></i>Quick Start</h4>
                    <ol>
                        <li><span class="step-number">1</span>Choose your reminder interval (30-120 minutes)</li>
                        <li><span class="step-number">2</span>Click "Start Reminder" to begin</li>
                        <li><span class="step-number">3</span>Get notified when it's time to take a break</li>
                        <li><span class="step-number">4</span>Stand up, stretch, and move around</li>
                    </ol>
                </div>

                <h2>Key Features</h2>
                
                <div class="feature-card">
                    <h4><i class="fas fa-clock me-2"></i>Customizable Intervals</h4>
                    <p>Choose from preset intervals or set your own:</p>
                    <ul>
                        <li><strong>30 minutes:</strong> Ideal for intensive work sessions</li>
                        <li><strong>45-60 minutes:</strong> Standard office work</li>
                        <li><strong>90-120 minutes:</strong> For longer focus periods</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-volume-up me-2"></i>Sound Options</h4>
                    <p>Customize your reminder sounds:</p>
                    <ul>
                        <li><strong>No Sound:</strong> Silent notifications only</li>
                        <li><strong>Beep:</strong> Simple alert sound</li>
                        <li><strong>Chime:</strong> Pleasant musical tone</li>
                    </ul>
                    <div class="tip-box">
                        <i class="fas fa-lightbulb me-2"></i><strong>Tip:</strong> Use the "Listen" button to preview sounds before saving.
                    </div>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-bell me-2"></i>Browser Notifications</h4>
                    <p>Enable browser notifications for the best experience:</p>
                    <ol>
                        <li>Click "Grant Permission" when prompted</li>
                        <li>Allow notifications in your browser</li>
                        <li>Notifications will appear even when the tab is not active</li>
                    </ol>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-chart-bar me-2"></i>Progress Tracking</h4>
                    <p>Monitor your healthy habits:</p>
                    <ul>
                        <li><strong>Today's Count:</strong> Reminders received today</li>
                        <li><strong>Total Count:</strong> All-time reminder statistics</li>
                        <li><strong>Progress Bar:</strong> Visual countdown to next reminder</li>
                    </ul>
                </div>

                <h2>Settings & Customization</h2>
                
                <div class="feature-card">
                    <h4><i class="fas fa-cog me-2"></i>Accessing Settings</h4>
                    <p>Click the settings icon (⚙️) to customize:</p>
                    <ul>
                        <li>Reminder sound preferences</li>
                        <li>Auto-start on page load</li>
                        <li>Language selection</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-globe me-2"></i>Multi-Language Support</h4>
                    <p>Available in 16 languages including:</p>
                    <ul>
                        <li>English, 中文, Español, Français</li>
                        <li>Deutsch, Italiano, 日本語, 한국어</li>
                        <li>And many more...</li>
                    </ul>
                </div>

                <h2>Health Benefits</h2>
                
                <div class="feature-card">
                    <h4><i class="fas fa-heart me-2"></i>Why Take Regular Breaks?</h4>
                    <ul>
                        <li><strong>Reduce Back Pain:</strong> Prevent lumbar disc problems</li>
                        <li><strong>Improve Circulation:</strong> Keep blood flowing properly</li>
                        <li><strong>Boost Productivity:</strong> Mental breaks enhance focus</li>
                        <li><strong>Prevent Eye Strain:</strong> Rest your eyes regularly</li>
                    </ul>
                </div>

                <h2>Recommended Exercises</h2>
                
                <div class="feature-card">
                    <h4><i class="fas fa-dumbbell me-2"></i>Quick Office Exercises</h4>
                    <p>When your reminder sounds, try these 2-3 minute activities:</p>
                    <ul>
                        <li><strong>Neck Rolls:</strong> Gentle circular motions</li>
                        <li><strong>Shoulder Shrugs:</strong> Lift and release tension</li>
                        <li><strong>Waist Twists:</strong> Rotate left and right</li>
                        <li><strong>Calf Raises:</strong> Stand and lift heels</li>
                        <li><strong>Deep Breathing:</strong> Take 5 deep breaths</li>
                    </ul>
                </div>

                <h2>Troubleshooting</h2>
                
                <div class="feature-card">
                    <h4><i class="fas fa-question-circle me-2"></i>Common Issues</h4>
                    <ul>
                        <li><strong>No notifications:</strong> Check browser permission settings</li>
                        <li><strong>No sound:</strong> Verify sound settings and browser audio</li>
                        <li><strong>Timer not working:</strong> Ensure tab remains open</li>
                        <li><strong>Settings not saved:</strong> Enable local storage in browser</li>
                    </ul>
                </div>

                <div class="text-center mt-4">
                    <a href="index.html" class="btn btn-primary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>Start Using Sedentary Reminder
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0 text-muted">© 2025 Sedentary Reminder. All rights reserved.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
