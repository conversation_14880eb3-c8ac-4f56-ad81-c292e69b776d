{"name": "is-path-in-cwd", "version": "2.1.0", "description": "Check if a path is in the current working directory", "license": "MIT", "repository": "sindresorhus/is-path-in-cwd", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["path", "cwd", "pwd", "check", "filepath", "file", "folder", "in", "inside"], "dependencies": {"is-path-inside": "^2.1.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}