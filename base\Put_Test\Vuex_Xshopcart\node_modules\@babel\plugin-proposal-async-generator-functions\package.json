{"name": "@babel/plugin-proposal-async-generator-functions", "version": "7.10.5", "description": "Turn async generator functions into ES2015 generators", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-async-generator-functions"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-remap-async-to-generator": "^7.10.4", "@babel/plugin-syntax-async-generators": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "gitHead": "f7964a9ac51356f7df6404a25b27ba1cffba1ba7"}