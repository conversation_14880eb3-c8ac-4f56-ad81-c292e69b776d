{"name": "css-loader", "version": "3.6.0", "description": "css loader module for webpack", "license": "MIT", "repository": "webpack-contrib/css-loader", "author": "<PERSON> @sokra", "homepage": "https://github.com/webpack-contrib/css-loader", "bugs": "https://github.com/webpack-contrib/css-loader/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 8.9.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "validate:runtime": "es-check es5 \"dist/runtime/**/*.js\"", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "postbuild": "npm run validate:runtime", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "files": ["dist"], "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"camelcase": "^5.3.1", "cssesc": "^3.0.0", "icss-utils": "^4.1.1", "loader-utils": "^1.2.3", "normalize-path": "^3.0.0", "postcss": "^7.0.32", "postcss-modules-extract-imports": "^2.0.0", "postcss-modules-local-by-default": "^3.0.2", "postcss-modules-scope": "^2.2.0", "postcss-modules-values": "^3.0.0", "postcss-value-parser": "^4.1.0", "schema-utils": "^2.7.0", "semver": "^6.3.0"}, "devDependencies": {"@babel/cli": "^7.10.1", "@babel/core": "^7.10.2", "@babel/preset-env": "^7.10.2", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^25.5.1", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.1", "es-check": "^5.1.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.21.2", "file-loader": "^6.0.0", "husky": "^4.2.5", "jest": "^25.5.4", "lint-staged": "^10.2.10", "memfs": "^3.2.0", "npm-run-all": "^4.1.5", "postcss-loader": "^3.0.0", "postcss-preset-env": "^6.7.0", "prettier": "^2.0.5", "sass": "^1.26.8", "sass-loader": "^8.0.2", "standard-version": "^8.0.0", "strip-ansi": "^6.0.0", "url-loader": "^4.1.0", "webpack": "^4.43.0"}, "keywords": ["webpack", "css", "loader", "url", "import"]}