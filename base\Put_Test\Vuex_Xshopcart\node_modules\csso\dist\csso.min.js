!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).csso={})}(this,(function(e){"use strict";function t(e){return{prev:null,next:null,data:e}}function n(e,t,n){var r;return null!==a?(r=a,a=a.cursor,r.prev=t,r.next=n,r.cursor=e.cursor):r={prev:t,next:n,cursor:e.cursor},e.cursor=r,r}function r(e){var t=e.cursor;e.cursor=t.cursor,t.prev=null,t.next=null,t.cursor=a,a=t}var a=null,i=function(){this.cursor=null,this.head=null,this.tail=null};i.createItem=t,i.prototype.createItem=t,i.prototype.updateCursors=function(e,t,n,r){for(var a=this.cursor;null!==a;)a.prev===e&&(a.prev=t),a.next===n&&(a.next=r),a=a.cursor},i.prototype.getSize=function(){for(var e=0,t=this.head;t;)e++,t=t.next;return e},i.prototype.fromArray=function(e){var n=null;this.head=null;for(var r=0;r<e.length;r++){var a=t(e[r]);null!==n?n.next=a:this.head=a,a.prev=n,n=a}return this.tail=n,this},i.prototype.toArray=function(){for(var e=this.head,t=[];e;)t.push(e.data),e=e.next;return t},i.prototype.toJSON=i.prototype.toArray,i.prototype.isEmpty=function(){return null===this.head},i.prototype.first=function(){return this.head&&this.head.data},i.prototype.last=function(){return this.tail&&this.tail.data},i.prototype.each=function(e,t){var a;void 0===t&&(t=this);for(var i=n(this,null,this.head);null!==i.next;)a=i.next,i.next=a.next,e.call(t,a.data,a,this);r(this)},i.prototype.forEach=i.prototype.each,i.prototype.eachRight=function(e,t){var a;void 0===t&&(t=this);for(var i=n(this,this.tail,null);null!==i.prev;)a=i.prev,i.prev=a.prev,e.call(t,a.data,a,this);r(this)},i.prototype.forEachRight=i.prototype.eachRight,i.prototype.nextUntil=function(e,t,a){if(null!==e){var i;void 0===a&&(a=this);for(var o=n(this,null,e);null!==o.next&&(i=o.next,o.next=i.next,!t.call(a,i.data,i,this)););r(this)}},i.prototype.prevUntil=function(e,t,a){if(null!==e){var i;void 0===a&&(a=this);for(var o=n(this,e,null);null!==o.prev&&(i=o.prev,o.prev=i.prev,!t.call(a,i.data,i,this)););r(this)}},i.prototype.some=function(e,t){var n=this.head;for(void 0===t&&(t=this);null!==n;){if(e.call(t,n.data,n,this))return!0;n=n.next}return!1},i.prototype.map=function(e,t){var n=new i,r=this.head;for(void 0===t&&(t=this);null!==r;)n.appendData(e.call(t,r.data,r,this)),r=r.next;return n},i.prototype.filter=function(e,t){var n=new i,r=this.head;for(void 0===t&&(t=this);null!==r;)e.call(t,r.data,r,this)&&n.appendData(r.data),r=r.next;return n},i.prototype.clear=function(){this.head=null,this.tail=null},i.prototype.copy=function(){for(var e=new i,n=this.head;null!==n;)e.insert(t(n.data)),n=n.next;return e},i.prototype.prepend=function(e){return this.updateCursors(null,e,this.head,e),null!==this.head?(this.head.prev=e,e.next=this.head):this.tail=e,this.head=e,this},i.prototype.prependData=function(e){return this.prepend(t(e))},i.prototype.append=function(e){return this.insert(e)},i.prototype.appendData=function(e){return this.insert(t(e))},i.prototype.insert=function(e,t){if(null!=t)if(this.updateCursors(t.prev,e,t,e),null===t.prev){if(this.head!==t)throw new Error("before doesn't belong to list");this.head=e,t.prev=e,e.next=t,this.updateCursors(null,e)}else t.prev.next=e,e.prev=t.prev,t.prev=e,e.next=t;else this.updateCursors(this.tail,e,null,e),null!==this.tail?(this.tail.next=e,e.prev=this.tail):this.head=e,this.tail=e;return this},i.prototype.insertData=function(e,n){return this.insert(t(e),n)},i.prototype.remove=function(e){if(this.updateCursors(e,e.prev,e,e.next),null!==e.prev)e.prev.next=e.next;else{if(this.head!==e)throw new Error("item doesn't belong to list");this.head=e.next}if(null!==e.next)e.next.prev=e.prev;else{if(this.tail!==e)throw new Error("item doesn't belong to list");this.tail=e.prev}return e.prev=null,e.next=null,e},i.prototype.push=function(e){this.insert(t(e))},i.prototype.pop=function(){if(null!==this.tail)return this.remove(this.tail)},i.prototype.unshift=function(e){this.prepend(t(e))},i.prototype.shift=function(){if(null!==this.head)return this.remove(this.head)},i.prototype.prependList=function(e){return this.insertList(e,this.head)},i.prototype.appendList=function(e){return this.insertList(e)},i.prototype.insertList=function(e,t){return null===e.head||(null!=t?(this.updateCursors(t.prev,e.tail,t,e.head),null!==t.prev?(t.prev.next=e.head,e.head.prev=t.prev):this.head=e.head,t.prev=e.tail,e.tail.next=t):(this.updateCursors(this.tail,e.tail,null,e.head),null!==this.tail?(this.tail.next=e.head,e.head.prev=this.tail):this.head=e.head,this.tail=e.tail),e.head=null,e.tail=null),this},i.prototype.replace=function(e,t){"head"in t?this.insertList(t,e):this.insert(t,e),this.remove(e)};var o=i,s=function(e,t){var n=Object.create(SyntaxError.prototype),r=new Error;return n.name=e,n.message=t,Object.defineProperty(n,"stack",{get:function(){return(r.stack||"").replace(/^(.+\n){1,3}/,e+": "+t+"\n")}}),n};function l(e,t){function n(e,t){return r.slice(e,t).map((function(t,n){for(var r=String(e+n+1);r.length<l;)r=" "+r;return r+" |"+t})).join("\n")}var r=e.source.split(/\r\n?|\n|\f/),a=e.line,i=e.column,o=Math.max(1,a-t)-1,s=Math.min(a+t,r.length+1),l=Math.max(4,String(s).length)+1,c=0;(i+=("    ".length-1)*(r[a-1].substr(0,i-1).match(/\t/g)||[]).length)>100&&(c=i-60+3,i=58);for(var u=o;u<=s;u++)u>=0&&u<r.length&&(r[u]=r[u].replace(/\t/g,"    "),r[u]=(c>0&&r[u].length>c?"…":"")+r[u].substr(c,98)+(r[u].length>c+100-1?"…":""));return[n(o,a),new Array(i+l+2).join("-")+"^",n(a,s)].filter(Boolean).join("\n")}var c=function(e,t,n,r,a){var i=s("SyntaxError",e);return i.source=t,i.offset=n,i.line=r,i.column=a,i.sourceFragment=function(e){return l(i,isNaN(e)?0:e)},Object.defineProperty(i,"formattedMessage",{get:function(){return"Parse error: "+i.message+"\n"+l(i,2)}}),i.parseError={offset:n,line:r,column:a},i},u={EOF:0,Ident:1,Function:2,AtKeyword:3,Hash:4,String:5,BadString:6,Url:7,BadUrl:8,Delim:9,Number:10,Percentage:11,Dimension:12,WhiteSpace:13,CDO:14,CDC:15,Colon:16,Semicolon:17,Comma:18,LeftSquareBracket:19,RightSquareBracket:20,LeftParenthesis:21,RightParenthesis:22,LeftCurlyBracket:23,RightCurlyBracket:24,Comment:25},h=Object.keys(u).reduce((function(e,t){return e[u[t]]=t,e}),{}),d={TYPE:u,NAME:h};function p(e){return e>=48&&e<=57}function f(e){return e>=65&&e<=90}function m(e){return e>=97&&e<=122}function g(e){return f(e)||m(e)}function b(e){return e>=128}function y(e){return g(e)||b(e)||95===e}function k(e){return e>=0&&e<=8||11===e||e>=14&&e<=31||127===e}function v(e){return 10===e||13===e||12===e}function w(e){return v(e)||32===e||9===e}function x(e,t){return 92===e&&(!v(t)&&0!==t)}var S=new Array(128);A.Eof=128,A.WhiteSpace=130,A.Digit=131,A.NameStart=132,A.NonPrintable=133;for(var C=0;C<S.length;C++)switch(!0){case w(C):S[C]=A.WhiteSpace;break;case p(C):S[C]=A.Digit;break;case y(C):S[C]=A.NameStart;break;case k(C):S[C]=A.NonPrintable;break;default:S[C]=C||A.Eof}function A(e){return e<128?S[e]:A.NameStart}var z={isDigit:p,isHexDigit:function(e){return p(e)||e>=65&&e<=70||e>=97&&e<=102},isUppercaseLetter:f,isLowercaseLetter:m,isLetter:g,isNonAscii:b,isNameStart:y,isName:function(e){return y(e)||p(e)||45===e},isNonPrintable:k,isNewline:v,isWhiteSpace:w,isValidEscape:x,isIdentifierStart:function(e,t,n){return 45===e?y(t)||45===t||x(t,n):!!y(e)||92===e&&x(e,t)},isNumberStart:function(e,t,n){return 43===e||45===e?p(t)?2:46===t&&p(n)?3:0:46===e?p(t)?2:0:p(e)?1:0},isBOM:function(e){return 65279===e||65534===e?1:0},charCodeCategory:A},P=z.isDigit,L=z.isHexDigit,T=z.isUppercaseLetter,E=z.isName,O=z.isWhiteSpace,D=z.isValidEscape;function R(e,t){return t<e.length?e.charCodeAt(t):0}function I(e,t,n){return 13===n&&10===R(e,t+1)?2:1}function M(e,t,n){var r=e.charCodeAt(t);return T(r)&&(r|=32),r===n}function N(e,t){for(;t<e.length&&P(e.charCodeAt(t));t++);return t}function B(e,t){if(L(R(e,(t+=2)-1))){for(var n=Math.min(e.length,t+5);t<n&&L(R(e,t));t++);var r=R(e,t);O(r)&&(t+=I(e,t,r))}return t}var j={consumeEscaped:B,consumeName:function(e,t){for(;t<e.length;t++){var n=e.charCodeAt(t);if(!E(n)){if(!D(n,R(e,t+1)))break;t=B(e,t)-1}}return t},consumeNumber:function(e,t){var n=e.charCodeAt(t);if(43!==n&&45!==n||(n=e.charCodeAt(t+=1)),P(n)&&(t=N(e,t+1),n=e.charCodeAt(t)),46===n&&P(e.charCodeAt(t+1))&&(n=e.charCodeAt(t+=2),t=N(e,t)),M(e,t,101)){var r=0;45!==(n=e.charCodeAt(t+1))&&43!==n||(r=1,n=e.charCodeAt(t+2)),P(n)&&(t=N(e,t+1+r+1))}return t},consumeBadUrlRemnants:function(e,t){for(;t<e.length;t++){var n=e.charCodeAt(t);if(41===n){t++;break}D(n,R(e,t+1))&&(t=B(e,t))}return t},cmpChar:M,cmpStr:function(e,t,n,r){if(n-t!==r.length)return!1;if(t<0||n>e.length)return!1;for(var a=t;a<n;a++){var i=e.charCodeAt(a),o=r.charCodeAt(a-t);if(T(i)&&(i|=32),i!==o)return!1}return!0},getNewlineLength:I,findWhiteSpaceStart:function(e,t){for(;t>=0&&O(e.charCodeAt(t));t--);return t+1},findWhiteSpaceEnd:function(e,t){for(;t<e.length&&O(e.charCodeAt(t));t++);return t}},_=d.TYPE,q=d.NAME,W=j.cmpStr,F=_.EOF,U=_.WhiteSpace,Y=_.Comment,H=function(){this.offsetAndType=null,this.balance=null,this.reset()};H.prototype={reset:function(){this.eof=!1,this.tokenIndex=-1,this.tokenType=0,this.tokenStart=this.firstCharOffset,this.tokenEnd=this.firstCharOffset},lookupType:function(e){return(e+=this.tokenIndex)<this.tokenCount?this.offsetAndType[e]>>24:F},lookupOffset:function(e){return(e+=this.tokenIndex)<this.tokenCount?16777215&this.offsetAndType[e-1]:this.source.length},lookupValue:function(e,t){return(e+=this.tokenIndex)<this.tokenCount&&W(this.source,16777215&this.offsetAndType[e-1],16777215&this.offsetAndType[e],t)},getTokenStart:function(e){return e===this.tokenIndex?this.tokenStart:e>0?e<this.tokenCount?16777215&this.offsetAndType[e-1]:16777215&this.offsetAndType[this.tokenCount]:this.firstCharOffset},getRawLength:function(e,t){var n,r=e,a=16777215&this.offsetAndType[Math.max(r-1,0)];e:for(;r<this.tokenCount&&!((n=this.balance[r])<e);r++)switch(t(this.offsetAndType[r]>>24,this.source,a)){case 1:break e;case 2:r++;break e;default:a=16777215&this.offsetAndType[r],this.balance[n]===r&&(r=n)}return r-this.tokenIndex},isBalanceEdge:function(e){return this.balance[this.tokenIndex]<e},isDelim:function(e,t){return t?this.lookupType(t)===_.Delim&&this.source.charCodeAt(this.lookupOffset(t))===e:this.tokenType===_.Delim&&this.source.charCodeAt(this.tokenStart)===e},getTokenValue:function(){return this.source.substring(this.tokenStart,this.tokenEnd)},getTokenLength:function(){return this.tokenEnd-this.tokenStart},substrToCursor:function(e){return this.source.substring(e,this.tokenStart)},skipWS:function(){for(var e=this.tokenIndex,t=0;e<this.tokenCount&&this.offsetAndType[e]>>24===U;e++,t++);t>0&&this.skip(t)},skipSC:function(){for(;this.tokenType===U||this.tokenType===Y;)this.next()},skip:function(e){var t=this.tokenIndex+e;t<this.tokenCount?(this.tokenIndex=t,this.tokenStart=16777215&this.offsetAndType[t-1],t=this.offsetAndType[t],this.tokenType=t>>24,this.tokenEnd=16777215&t):(this.tokenIndex=this.tokenCount,this.next())},next:function(){var e=this.tokenIndex+1;e<this.tokenCount?(this.tokenIndex=e,this.tokenStart=this.tokenEnd,e=this.offsetAndType[e],this.tokenType=e>>24,this.tokenEnd=16777215&e):(this.tokenIndex=this.tokenCount,this.eof=!0,this.tokenType=F,this.tokenStart=this.tokenEnd=this.source.length)},dump:function(){var e=this.firstCharOffset;return Array.prototype.slice.call(this.offsetAndType,0,this.tokenCount).map((function(t,n){var r=e,a=16777215&t;return e=a,{idx:n,type:q[t>>24],chunk:this.source.substring(r,a),balance:this.balance[n]}}),this)}};var V=H;function K(e){return e}function G(e,t,n,r){var a,i;switch(e.type){case"Group":a=function(e,t,n,r){var a=" "===e.combinator||r?e.combinator:" "+e.combinator+" ",i=e.terms.map((function(e){return G(e,t,n,r)})).join(a);return(e.explicit||n)&&(i=(r||","===i[0]?"[":"[ ")+i+(r?"]":" ]")),i}(e,t,n,r)+(e.disallowEmpty?"!":"");break;case"Multiplier":return G(e.term,t,n,r)+t(0===(i=e).min&&0===i.max?"*":0===i.min&&1===i.max?"?":1===i.min&&0===i.max?i.comma?"#":"+":1===i.min&&1===i.max?"":(i.comma?"#":"")+(i.min===i.max?"{"+i.min+"}":"{"+i.min+","+(0!==i.max?i.max:"")+"}"),e);case"Type":a="<"+e.name+(e.opts?t(function(e){switch(e.type){case"Range":return" ["+(null===e.min?"-∞":e.min)+","+(null===e.max?"∞":e.max)+"]";default:throw new Error("Unknown node type `"+e.type+"`")}}(e.opts),e.opts):"")+">";break;case"Property":a="<'"+e.name+"'>";break;case"Keyword":a=e.name;break;case"AtKeyword":a="@"+e.name;break;case"Function":a=e.name+"(";break;case"String":case"Token":a=e.value;break;case"Comma":a=",";break;default:throw new Error("Unknown node type `"+e.type+"`")}return t(a,e)}var $=function(e,t){var n=K,r=!1,a=!1;return"function"==typeof t?n=t:t&&(r=Boolean(t.forceBraces),a=Boolean(t.compact),"function"==typeof t.decorate&&(n=t.decorate)),G(e,n,r,a)};function Q(e,t){var n=e&&e.loc&&e.loc[t];return n?{offset:n.offset,line:n.line,column:n.column}:null}var X=function(e,t){var n=s("SyntaxReferenceError",e+(t?" `"+t+"`":""));return n.reference=t,n},Z=function(e,t,n,r){var a=s("SyntaxMatchError",e),i=function(e){for(var t=e.tokens,n=e.longestMatch,r=n<t.length?t[n].node:null,a=-1,i=0,o="",s=0;s<t.length;s++)s===n&&(a=o.length),null!==r&&t[s].node===r&&(s<=n?i++:i=0),o+=t[s].value;return{node:r,css:o,mismatchOffset:-1===a?o.length:a,last:null===r||i>1}}(r),o=i.mismatchOffset||0,l=i.node||n,c=Q(l,"end"),u=i.last?c:Q(l,"start"),h=i.css;return a.rawMessage=e,a.syntax=t?$(t):"<generic>",a.css=h,a.mismatchOffset=o,a.loc={source:l&&l.loc&&l.loc.source||"<unknown>",start:u,end:c},a.line=u?u.line:void 0,a.column=u?u.column:void 0,a.offset=u?u.offset:void 0,a.message=e+"\n  syntax: "+a.syntax+"\n   value: "+(a.css||"<empty string>")+"\n  --------"+new Array(a.mismatchOffset+1).join("-")+"^",a},J=Object.prototype.hasOwnProperty,ee=Object.create(null),te=Object.create(null);function ne(e,t){return t=t||0,e.length-t>=2&&45===e.charCodeAt(t)&&45===e.charCodeAt(t+1)}function re(e,t){if(t=t||0,e.length-t>=3&&45===e.charCodeAt(t)&&45!==e.charCodeAt(t+1)){var n=e.indexOf("-",t+2);if(-1!==n)return e.substring(t,n+1)}return""}var ae={keyword:function(e){if(J.call(ee,e))return ee[e];var t=e.toLowerCase();if(J.call(ee,t))return ee[e]=ee[t];var n=ne(t,0),r=n?"":re(t,0);return ee[e]=Object.freeze({basename:t.substr(r.length),name:t,vendor:r,prefix:r,custom:n})},property:function(e){if(J.call(te,e))return te[e];var t=e,n=e[0];"/"===n?n="/"===e[1]?"//":"/":"_"!==n&&"*"!==n&&"$"!==n&&"#"!==n&&"+"!==n&&"&"!==n&&(n="");var r=ne(t,n.length);if(!r&&(t=t.toLowerCase(),J.call(te,t)))return te[e]=te[t];var a=r?"":re(t,n.length),i=t.substr(0,n.length+a.length);return te[e]=Object.freeze({basename:t.substr(i.length),name:t.substr(n.length),hack:n,vendor:a,prefix:i,custom:r})},isCustomProperty:ne,vendorPrefix:re},ie="undefined"!=typeof Uint32Array?Uint32Array:Array,oe=function(e,t){return null===e||e.length<t?new ie(Math.max(t+1024,16384)):e},se=d.TYPE,le=z.isNewline,ce=z.isName,ue=z.isValidEscape,he=z.isNumberStart,de=z.isIdentifierStart,pe=z.charCodeCategory,fe=z.isBOM,me=j.cmpStr,ge=j.getNewlineLength,be=j.findWhiteSpaceEnd,ye=j.consumeEscaped,ke=j.consumeName,ve=j.consumeNumber,we=j.consumeBadUrlRemnants;function xe(e,t){function n(t){return t<o?e.charCodeAt(t):0}function r(){return h=ve(e,h),de(n(h),n(h+1),n(h+2))?(g=se.Dimension,void(h=ke(e,h))):37===n(h)?(g=se.Percentage,void h++):void(g=se.Number)}function a(){const t=h;return h=ke(e,h),me(e,t,h,"url")&&40===n(h)?34===n(h=be(e,h+1))||39===n(h)?(g=se.Function,void(h=t+4)):void function(){for(g=se.Url,h=be(e,h);h<e.length;h++){var t=e.charCodeAt(h);switch(pe(t)){case 41:return void h++;case pe.Eof:return;case pe.WhiteSpace:return 41===n(h=be(e,h))||h>=e.length?void(h<e.length&&h++):(h=we(e,h),void(g=se.BadUrl));case 34:case 39:case 40:case pe.NonPrintable:return h=we(e,h),void(g=se.BadUrl);case 92:if(ue(t,n(h+1))){h=ye(e,h)-1;break}return h=we(e,h),void(g=se.BadUrl)}}}():40===n(h)?(g=se.Function,void h++):void(g=se.Ident)}function i(t){for(t||(t=n(h++)),g=se.String;h<e.length;h++){var r=e.charCodeAt(h);switch(pe(r)){case t:return void h++;case pe.Eof:return;case pe.WhiteSpace:if(le(r))return h+=ge(e,h,r),void(g=se.BadString);break;case 92:if(h===e.length-1)break;var a=n(h+1);le(a)?h+=ge(e,h+1,a):ue(r,a)&&(h=ye(e,h)-1)}}}t||(t=new V);for(var o=(e=String(e||"")).length,s=oe(t.offsetAndType,o+1),l=oe(t.balance,o+1),c=0,u=fe(n(0)),h=u,d=0,p=0,f=0;h<o;){var m=e.charCodeAt(h),g=0;switch(l[c]=o,pe(m)){case pe.WhiteSpace:g=se.WhiteSpace,h=be(e,h+1);break;case 34:i();break;case 35:ce(n(h+1))||ue(n(h+1),n(h+2))?(g=se.Hash,h=ke(e,h+1)):(g=se.Delim,h++);break;case 39:i();break;case 40:g=se.LeftParenthesis,h++;break;case 41:g=se.RightParenthesis,h++;break;case 43:he(m,n(h+1),n(h+2))?r():(g=se.Delim,h++);break;case 44:g=se.Comma,h++;break;case 45:he(m,n(h+1),n(h+2))?r():45===n(h+1)&&62===n(h+2)?(g=se.CDC,h+=3):de(m,n(h+1),n(h+2))?a():(g=se.Delim,h++);break;case 46:he(m,n(h+1),n(h+2))?r():(g=se.Delim,h++);break;case 47:42===n(h+1)?(g=se.Comment,1===(h=e.indexOf("*/",h+2)+2)&&(h=e.length)):(g=se.Delim,h++);break;case 58:g=se.Colon,h++;break;case 59:g=se.Semicolon,h++;break;case 60:33===n(h+1)&&45===n(h+2)&&45===n(h+3)?(g=se.CDO,h+=4):(g=se.Delim,h++);break;case 64:de(n(h+1),n(h+2),n(h+3))?(g=se.AtKeyword,h=ke(e,h+1)):(g=se.Delim,h++);break;case 91:g=se.LeftSquareBracket,h++;break;case 92:ue(m,n(h+1))?a():(g=se.Delim,h++);break;case 93:g=se.RightSquareBracket,h++;break;case 123:g=se.LeftCurlyBracket,h++;break;case 125:g=se.RightCurlyBracket,h++;break;case pe.Digit:r();break;case pe.NameStart:a();break;case pe.Eof:break;default:g=se.Delim,h++}switch(g){case d:for(d=(p=l[f=16777215&p])>>24,l[c]=f,l[f++]=c;f<c;f++)l[f]===o&&(l[f]=c);break;case se.LeftParenthesis:case se.Function:l[c]=p,p=(d=se.RightParenthesis)<<24|c;break;case se.LeftSquareBracket:l[c]=p,p=(d=se.RightSquareBracket)<<24|c;break;case se.LeftCurlyBracket:l[c]=p,p=(d=se.RightCurlyBracket)<<24|c}s[c++]=g<<24|h}for(s[c]=se.EOF<<24|h,l[c]=o,l[o]=o;0!==p;)p=l[f=16777215&p],l[f]=o;return t.source=e,t.firstCharOffset=u,t.offsetAndType=s,t.tokenCount=c,t.balance=l,t.reset(),t.next(),t}Object.keys(d).forEach((function(e){xe[e]=d[e]})),Object.keys(z).forEach((function(e){xe[e]=z[e]})),Object.keys(j).forEach((function(e){xe[e]=j[e]}));var Se=xe,Ce=Se.isDigit,Ae=Se.cmpChar,ze=Se.TYPE,Pe=ze.Delim,Le=ze.WhiteSpace,Te=ze.Comment,Ee=ze.Ident,Oe=ze.Number,De=ze.Dimension;function Re(e,t){return null!==e&&e.type===Pe&&e.value.charCodeAt(0)===t}function Ie(e,t,n){for(;null!==e&&(e.type===Le||e.type===Te);)e=n(++t);return t}function Me(e,t,n,r){if(!e)return 0;var a=e.value.charCodeAt(t);if(43===a||45===a){if(n)return 0;t++}for(;t<e.value.length;t++)if(!Ce(e.value.charCodeAt(t)))return 0;return r+1}function Ne(e,t,n){var r=!1,a=Ie(e,t,n);if(null===(e=n(a)))return t;if(e.type!==Oe){if(!Re(e,43)&&!Re(e,45))return t;if(r=!0,a=Ie(n(++a),a,n),null===(e=n(a))&&e.type!==Oe)return 0}if(!r){var i=e.value.charCodeAt(0);if(43!==i&&45!==i)return 0}return Me(e,r?0:1,r,a)}var Be=Se.isHexDigit,je=Se.cmpChar,_e=Se.TYPE,qe=_e.Ident,We=_e.Delim,Fe=_e.Number,Ue=_e.Dimension;function Ye(e,t){return null!==e&&e.type===We&&e.value.charCodeAt(0)===t}function He(e,t){return e.value.charCodeAt(0)===t}function Ve(e,t,n){for(var r=t,a=0;r<e.value.length;r++){var i=e.value.charCodeAt(r);if(45===i&&n&&0!==a)return Ve(e,t+a+1,!1)>0?6:0;if(!Be(i))return 0;if(++a>6)return 0}return a}function Ke(e,t,n){if(!e)return 0;for(;Ye(n(t),63);){if(++e>6)return 0;t++}return t}var Ge=Se.isIdentifierStart,$e=Se.isHexDigit,Qe=Se.isDigit,Xe=Se.cmpStr,Ze=Se.consumeNumber,Je=Se.TYPE,et=["unset","initial","inherit"],tt=["calc(","-moz-calc(","-webkit-calc("];function nt(e,t){return t<e.length?e.charCodeAt(t):0}function rt(e,t){return Xe(e,0,e.length,t)}function at(e,t){for(var n=0;n<t.length;n++)if(rt(e,t[n]))return!0;return!1}function it(e,t){return t===e.length-2&&(92===e.charCodeAt(t)&&Qe(e.charCodeAt(t+1)))}function ot(e,t,n){if(e&&"Range"===e.type){var r=Number(void 0!==n&&n!==t.length?t.substr(0,n):t);if(isNaN(r))return!0;if(null!==e.min&&r<e.min)return!0;if(null!==e.max&&r>e.max)return!0}return!1}function st(e,t){var n=e.index,r=0;do{if(r++,e.balance<=n)break}while(e=t(r));return r}function lt(e){return function(t,n,r){return null===t?0:t.type===Je.Function&&at(t.value,tt)?st(t,n):e(t,n,r)}}function ct(e){return function(t){return null===t||t.type!==e?0:1}}function ut(e){return function(t,n,r){if(null===t||t.type!==Je.Dimension)return 0;var a=Ze(t.value,0);if(null!==e){var i=t.value.indexOf("\\",a),o=-1!==i&&it(t.value,i)?t.value.substring(a,i):t.value.substr(a);if(!1===e.hasOwnProperty(o.toLowerCase()))return 0}return ot(r,t.value,a)?0:1}}function ht(e){return"function"!=typeof e&&(e=function(){return 0}),function(t,n,r){return null!==t&&t.type===Je.Number&&0===Number(t.value)?1:e(t,n,r)}}var dt,pt={"ident-token":ct(Je.Ident),"function-token":ct(Je.Function),"at-keyword-token":ct(Je.AtKeyword),"hash-token":ct(Je.Hash),"string-token":ct(Je.String),"bad-string-token":ct(Je.BadString),"url-token":ct(Je.Url),"bad-url-token":ct(Je.BadUrl),"delim-token":ct(Je.Delim),"number-token":ct(Je.Number),"percentage-token":ct(Je.Percentage),"dimension-token":ct(Je.Dimension),"whitespace-token":ct(Je.WhiteSpace),"CDO-token":ct(Je.CDO),"CDC-token":ct(Je.CDC),"colon-token":ct(Je.Colon),"semicolon-token":ct(Je.Semicolon),"comma-token":ct(Je.Comma),"[-token":ct(Je.LeftSquareBracket),"]-token":ct(Je.RightSquareBracket),"(-token":ct(Je.LeftParenthesis),")-token":ct(Je.RightParenthesis),"{-token":ct(Je.LeftCurlyBracket),"}-token":ct(Je.RightCurlyBracket),string:ct(Je.String),ident:ct(Je.Ident),"custom-ident":function(e){if(null===e||e.type!==Je.Ident)return 0;var t=e.value.toLowerCase();return at(t,et)||rt(t,"default")?0:1},"custom-property-name":function(e){return null===e||e.type!==Je.Ident||45!==nt(e.value,0)||45!==nt(e.value,1)?0:1},"hex-color":function(e){if(null===e||e.type!==Je.Hash)return 0;var t=e.value.length;if(4!==t&&5!==t&&7!==t&&9!==t)return 0;for(var n=1;n<t;n++)if(!$e(e.value.charCodeAt(n)))return 0;return 1},"id-selector":function(e){return null===e||e.type!==Je.Hash?0:Ge(nt(e.value,1),nt(e.value,2),nt(e.value,3))?1:0},"an-plus-b":function(e,t){var n=0;if(!e)return 0;if(e.type===Oe)return Me(e,0,!1,n);if(e.type===Ee&&45===e.value.charCodeAt(0)){if(!Ae(e.value,1,110))return 0;switch(e.value.length){case 2:return Ne(t(++n),n,t);case 3:return 45!==e.value.charCodeAt(2)?0:(n=Ie(t(++n),n,t),Me(e=t(n),0,!0,n));default:return 45!==e.value.charCodeAt(2)?0:Me(e,3,!0,n)}}else if(e.type===Ee||Re(e,43)&&t(n+1).type===Ee){if(e.type!==Ee&&(e=t(++n)),null===e||!Ae(e.value,0,110))return 0;switch(e.value.length){case 1:return Ne(t(++n),n,t);case 2:return 45!==e.value.charCodeAt(1)?0:(n=Ie(t(++n),n,t),Me(e=t(n),0,!0,n));default:return 45!==e.value.charCodeAt(1)?0:Me(e,2,!0,n)}}else if(e.type===De){for(var r=e.value.charCodeAt(0),a=43===r||45===r?1:0,i=a;i<e.value.length&&Ce(e.value.charCodeAt(i));i++);return i===a?0:Ae(e.value,i,110)?i+1===e.value.length?Ne(t(++n),n,t):45!==e.value.charCodeAt(i+1)?0:i+2===e.value.length?(n=Ie(t(++n),n,t),Me(e=t(n),0,!0,n)):Me(e,i+2,!0,n):0}return 0},urange:function(e,t){var n=0;if(null===e||e.type!==qe||!je(e.value,0,117))return 0;if(null===(e=t(++n)))return 0;if(Ye(e,43))return null===(e=t(++n))?0:e.type===qe?Ke(Ve(e,0,!0),++n,t):Ye(e,63)?Ke(1,++n,t):0;if(e.type===Fe){if(!He(e,43))return 0;var r=Ve(e,1,!0);return 0===r?0:null===(e=t(++n))?n:e.type===Ue||e.type===Fe?He(e,45)&&Ve(e,1,!1)?n+1:0:Ke(r,n,t)}return e.type===Ue&&He(e,43)?Ke(Ve(e,1,!0),++n,t):0},"declaration-value":function(e,t){if(!e)return 0;var n=0,r=0,a=e.index;e:do{switch(e.type){case Je.BadString:case Je.BadUrl:break e;case Je.RightCurlyBracket:case Je.RightParenthesis:case Je.RightSquareBracket:if(e.balance>e.index||e.balance<a)break e;r--;break;case Je.Semicolon:if(0===r)break e;break;case Je.Delim:if("!"===e.value&&0===r)break e;break;case Je.Function:case Je.LeftParenthesis:case Je.LeftSquareBracket:case Je.LeftCurlyBracket:r++}if(n++,e.balance<=a)break}while(e=t(n));return n},"any-value":function(e,t){if(!e)return 0;var n=e.index,r=0;e:do{switch(e.type){case Je.BadString:case Je.BadUrl:break e;case Je.RightCurlyBracket:case Je.RightParenthesis:case Je.RightSquareBracket:if(e.balance>e.index||e.balance<n)break e}if(r++,e.balance<=n)break}while(e=t(r));return r},dimension:lt(ut(null)),angle:lt(ut({deg:!0,grad:!0,rad:!0,turn:!0})),decibel:lt(ut({db:!0})),frequency:lt(ut({hz:!0,khz:!0})),flex:lt(ut({fr:!0})),length:lt(ht(ut({px:!0,mm:!0,cm:!0,in:!0,pt:!0,pc:!0,q:!0,em:!0,ex:!0,ch:!0,rem:!0,vh:!0,vw:!0,vmin:!0,vmax:!0,vm:!0}))),resolution:lt(ut({dpi:!0,dpcm:!0,dppx:!0,x:!0})),semitones:lt(ut({st:!0})),time:lt(ut({s:!0,ms:!0})),percentage:lt((function(e,t,n){return null===e||e.type!==Je.Percentage||ot(n,e.value,e.value.length-1)?0:1})),zero:ht(),number:lt((function(e,t,n){if(null===e)return 0;var r=Ze(e.value,0);return r===e.value.length||it(e.value,r)?ot(n,e.value,r)?0:1:0})),integer:lt((function(e,t,n){if(null===e||e.type!==Je.Number)return 0;for(var r=43===e.value.charCodeAt(0)||45===e.value.charCodeAt(0)?1:0;r<e.value.length;r++)if(!Qe(e.value.charCodeAt(r)))return 0;return ot(n,e.value,r)?0:1})),"-ms-legacy-expression":(dt="expression",dt+="(",function(e,t){return null!==e&&rt(e.value,dt)?st(e,t):0})},ft=function(e,t,n){var r=s("SyntaxError",e);return r.input=t,r.offset=n,r.rawMessage=e,r.message=r.rawMessage+"\n  "+r.input+"\n--"+new Array((r.offset||r.input.length)+1).join("-")+"^",r},mt=function(e){this.str=e,this.pos=0};mt.prototype={charCodeAt:function(e){return e<this.str.length?this.str.charCodeAt(e):0},charCode:function(){return this.charCodeAt(this.pos)},nextCharCode:function(){return this.charCodeAt(this.pos+1)},nextNonWsCode:function(e){return this.charCodeAt(this.findWsEnd(e))},findWsEnd:function(e){for(;e<this.str.length;e++){var t=this.str.charCodeAt(e);if(13!==t&&10!==t&&12!==t&&32!==t&&9!==t)break}return e},substringToPos:function(e){return this.str.substring(this.pos,this.pos=e)},eat:function(e){this.charCode()!==e&&this.error("Expect `"+String.fromCharCode(e)+"`"),this.pos++},peek:function(){return this.pos<this.str.length?this.str.charAt(this.pos++):""},error:function(e){throw new ft(e,this.str,this.pos)}};var gt=mt,bt=function(e){for(var t="function"==typeof Uint32Array?new Uint32Array(128):new Array(128),n=0;n<128;n++)t[n]=e(String.fromCharCode(n))?1:0;return t}((function(e){return/[a-zA-Z0-9\-]/.test(e)})),yt={" ":1,"&&":2,"||":3,"|":4};function kt(e){return e.substringToPos(e.findWsEnd(e.pos))}function vt(e){for(var t=e.pos;t<e.str.length;t++){var n=e.str.charCodeAt(t);if(n>=128||0===bt[n])break}return e.pos===t&&e.error("Expect a keyword"),e.substringToPos(t)}function wt(e){for(var t=e.pos;t<e.str.length;t++){var n=e.str.charCodeAt(t);if(n<48||n>57)break}return e.pos===t&&e.error("Expect a number"),e.substringToPos(t)}function xt(e){var t=e.str.indexOf("'",e.pos+1);return-1===t&&(e.pos=e.str.length,e.error("Expect an apostrophe")),e.substringToPos(t+1)}function St(e){var t,n=null;return e.eat(123),t=wt(e),44===e.charCode()?(e.pos++,125!==e.charCode()&&(n=wt(e))):n=t,e.eat(125),{min:Number(t),max:n?Number(n):0}}function Ct(e,t){var n=function(e){var t=null,n=!1;switch(e.charCode()){case 42:e.pos++,t={min:0,max:0};break;case 43:e.pos++,t={min:1,max:0};break;case 63:e.pos++,t={min:0,max:1};break;case 35:e.pos++,n=!0,t=123===e.charCode()?St(e):{min:1,max:0};break;case 123:t=St(e);break;default:return null}return{type:"Multiplier",comma:n,min:t.min,max:t.max,term:null}}(e);return null!==n?(n.term=t,n):t}function At(e){var t=e.peek();return""===t?null:{type:"Token",value:t}}function zt(e){var t,n=null;return e.eat(60),t=vt(e),40===e.charCode()&&41===e.nextCharCode()&&(e.pos+=2,t+="()"),91===e.charCodeAt(e.findWsEnd(e.pos))&&(kt(e),n=function(e){var t=null,n=null,r=1;return e.eat(91),45===e.charCode()&&(e.peek(),r=-1),-1==r&&8734===e.charCode()?e.peek():t=r*Number(wt(e)),kt(e),e.eat(44),kt(e),8734===e.charCode()?e.peek():(r=1,45===e.charCode()&&(e.peek(),r=-1),n=r*Number(wt(e))),e.eat(93),null===t&&null===n?null:{type:"Range",min:t,max:n}}(e)),e.eat(62),Ct(e,{type:"Type",name:t,opts:n})}function Pt(e,t){function n(e,t){return{type:"Group",terms:e,combinator:t,disallowEmpty:!1,explicit:!1}}for(t=Object.keys(t).sort((function(e,t){return yt[e]-yt[t]}));t.length>0;){for(var r=t.shift(),a=0,i=0;a<e.length;a++){var o=e[a];"Combinator"===o.type&&(o.value===r?(-1===i&&(i=a-1),e.splice(a,1),a--):(-1!==i&&a-i>1&&(e.splice(i,a-i,n(e.slice(i,a),r)),a=i+1),i=-1))}-1!==i&&t.length&&e.splice(i,a-i,n(e.slice(i,a),r))}return r}function Lt(e){for(var t,n=[],r={},a=null,i=e.pos;t=Tt(e);)"Spaces"!==t.type&&("Combinator"===t.type?(null!==a&&"Combinator"!==a.type||(e.pos=i,e.error("Unexpected combinator")),r[t.value]=!0):null!==a&&"Combinator"!==a.type&&(r[" "]=!0,n.push({type:"Combinator",value:" "})),n.push(t),a=t,i=e.pos);return null!==a&&"Combinator"===a.type&&(e.pos-=i,e.error("Unexpected combinator")),{type:"Group",terms:n,combinator:Pt(n,r)||" ",disallowEmpty:!1,explicit:!1}}function Tt(e){var t=e.charCode();if(t<128&&1===bt[t])return function(e){var t;return t=vt(e),40===e.charCode()?(e.pos++,{type:"Function",name:t}):Ct(e,{type:"Keyword",name:t})}(e);switch(t){case 93:break;case 91:return Ct(e,function(e){var t;return e.eat(91),t=Lt(e),e.eat(93),t.explicit=!0,33===e.charCode()&&(e.pos++,t.disallowEmpty=!0),t}(e));case 60:return 39===e.nextCharCode()?function(e){var t;return e.eat(60),e.eat(39),t=vt(e),e.eat(39),e.eat(62),Ct(e,{type:"Property",name:t})}(e):zt(e);case 124:return{type:"Combinator",value:e.substringToPos(124===e.nextCharCode()?e.pos+2:e.pos+1)};case 38:return e.pos++,e.eat(38),{type:"Combinator",value:"&&"};case 44:return e.pos++,{type:"Comma"};case 39:return Ct(e,{type:"String",value:xt(e)});case 32:case 9:case 10:case 13:case 12:return{type:"Spaces",value:kt(e)};case 64:return(t=e.nextCharCode())<128&&1===bt[t]?(e.pos++,{type:"AtKeyword",name:vt(e)}):At(e);case 42:case 43:case 63:case 35:case 33:break;case 123:if((t=e.nextCharCode())<48||t>57)return At(e);break;default:return At(e)}}function Et(e){var t=new gt(e),n=Lt(t);return t.pos!==e.length&&t.error("Unexpected input"),1===n.terms.length&&"Group"===n.terms[0].type&&(n=n.terms[0]),n}Et("[a&&<b>#|<'c'>*||e() f{2} /,(% g#{1,2} h{2,})]!");var Ot=Et,Dt=function(){};function Rt(e){return"function"==typeof e?e:Dt}var It=function(e,t,n){var r=Dt,a=Dt;if("function"==typeof t?r=t:t&&(r=Rt(t.enter),a=Rt(t.leave)),r===Dt&&a===Dt)throw new Error("Neither `enter` nor `leave` walker handler is set or both aren't a function");!function e(t){switch(r.call(n,t),t.type){case"Group":t.terms.forEach(e);break;case"Multiplier":e(t.term);break;case"Type":case"Property":case"Keyword":case"AtKeyword":case"Function":case"String":case"Token":case"Comma":break;default:throw new Error("Unknown type: "+t.type)}a.call(n,t)}(e)},Mt=new V,Nt={decorator:function(e){var t=null,n={len:0,node:null},r=[n],a="";return{children:e.children,node:function(n){var r=t;t=n,e.node.call(this,n),t=r},chunk:function(e){a+=e,n.node!==t?r.push({len:e.length,node:t}):n.len+=e.length},result:function(){return Bt(a,r)}}}};function Bt(e,t){var n=[],r=0,a=0,i=t?t[a].node:null;for(Se(e,Mt);!Mt.eof;){if(t)for(;a<t.length&&r+t[a].len<=Mt.tokenStart;)r+=t[a++].len,i=t[a].node;n.push({type:Mt.tokenType,value:Mt.getTokenValue(),index:Mt.tokenIndex,balance:Mt.balance[Mt.tokenIndex],node:i}),Mt.next()}return n}var jt={type:"Match"},_t={type:"Mismatch"},qt={type:"DisallowEmpty"};function Wt(e,t,n){return t===jt&&n===_t||e===jt&&t===jt&&n===jt?e:("If"===e.type&&e.else===_t&&t===jt&&(t=e.then,e=e.match),{type:"If",match:e,then:t,else:n})}function Ft(e){return e.length>2&&40===e.charCodeAt(e.length-2)&&41===e.charCodeAt(e.length-1)}function Ut(e){return"Keyword"===e.type||"AtKeyword"===e.type||"Function"===e.type||"Type"===e.type&&Ft(e.name)}function Yt(e){if("function"==typeof e)return{type:"Generic",fn:e};switch(e.type){case"Group":var t=function e(t,n,r){switch(t){case" ":for(var a=jt,i=n.length-1;i>=0;i--){a=Wt(l=n[i],a,_t)}return a;case"|":a=_t;var o=null;for(i=n.length-1;i>=0;i--){if(Ut(l=n[i])&&(null===o&&i>0&&Ut(n[i-1])&&(a=Wt({type:"Enum",map:o=Object.create(null)},jt,a)),null!==o)){var s=(Ft(l.name)?l.name.slice(0,-1):l.name).toLowerCase();if(s in o==!1){o[s]=l;continue}}o=null,a=Wt(l,jt,a)}return a;case"&&":if(n.length>5)return{type:"MatchOnce",terms:n,all:!0};for(a=_t,i=n.length-1;i>=0;i--){var l=n[i];c=n.length>1?e(t,n.filter((function(e){return e!==l})),!1):jt,a=Wt(l,c,a)}return a;case"||":if(n.length>5)return{type:"MatchOnce",terms:n,all:!1};for(a=r?jt:_t,i=n.length-1;i>=0;i--){var c;l=n[i];c=n.length>1?e(t,n.filter((function(e){return e!==l})),!0):jt,a=Wt(l,c,a)}return a}}(e.combinator,e.terms.map(Yt),!1);return e.disallowEmpty&&(t=Wt(t,qt,_t)),t;case"Multiplier":return function(e){var t=jt,n=Yt(e.term);if(0===e.max)n=Wt(n,qt,_t),(t=Wt(n,null,_t)).then=Wt(jt,jt,t),e.comma&&(t.then.else=Wt({type:"Comma",syntax:e},t,_t));else for(var r=e.min||1;r<=e.max;r++)e.comma&&t!==jt&&(t=Wt({type:"Comma",syntax:e},t,_t)),t=Wt(n,Wt(jt,jt,t),_t);if(0===e.min)t=Wt(jt,jt,t);else for(r=0;r<e.min-1;r++)e.comma&&t!==jt&&(t=Wt({type:"Comma",syntax:e},t,_t)),t=Wt(n,t,_t);return t}(e);case"Type":case"Property":return{type:e.type,name:e.name,syntax:e};case"Keyword":return{type:e.type,name:e.name.toLowerCase(),syntax:e};case"AtKeyword":return{type:e.type,name:"@"+e.name.toLowerCase(),syntax:e};case"Function":return{type:e.type,name:e.name.toLowerCase()+"(",syntax:e};case"String":return 3===e.value.length?{type:"Token",value:e.value.charAt(1),syntax:e}:{type:e.type,value:e.value.substr(1,e.value.length-2).replace(/\\'/g,"'"),syntax:e};case"Token":return{type:e.type,value:e.value,syntax:e};case"Comma":return{type:e.type,syntax:e};default:throw new Error("Unknown node type:",e.type)}}var Ht=jt,Vt=_t,Kt=qt,Gt=function(e,t){return"string"==typeof e&&(e=Ot(e)),{type:"MatchGraph",match:Yt(e),syntax:t||null,source:e}},$t=Object.prototype.hasOwnProperty,Qt=Ht,Xt=Vt,Zt=Kt,Jt=d.TYPE;function en(e){for(var t=null,n=null,r=e;null!==r;)n=r.prev,r.prev=t,t=r,r=n;return t}function tn(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++){var r=e.charCodeAt(n);if(r>=65&&r<=90&&(r|=32),r!==t.charCodeAt(n))return!1}return!0}function nn(e){return null===e||(e.type===Jt.Comma||e.type===Jt.Function||e.type===Jt.LeftParenthesis||e.type===Jt.LeftSquareBracket||e.type===Jt.LeftCurlyBracket||e.type===Jt.Delim)}function rn(e){return null===e||(e.type===Jt.RightParenthesis||e.type===Jt.RightSquareBracket||e.type===Jt.RightCurlyBracket||e.type===Jt.Delim)}function an(e,t,n){function r(){do{b++,g=b<e.length?e[b]:null}while(null!==g&&(g.type===Jt.WhiteSpace||g.type===Jt.Comment))}function a(t){var n=b+t;return n<e.length?e[n]:null}function i(e,t){return{nextState:e,matchStack:k,syntaxStack:u,thenStack:h,tokenIndex:b,prev:t}}function o(e){h={nextState:e,matchStack:k,syntaxStack:u,prev:h}}function s(e){d=i(e,d)}function l(){k={type:1,syntax:t.syntax,token:g,prev:k},r(),p=null,b>y&&(y=b)}function c(){k=2===k.type?k.prev:{type:3,syntax:u.syntax,token:k.token,prev:k},u=u.prev}var u=null,h=null,d=null,p=null,f=0,m=null,g=null,b=-1,y=0,k={type:0,syntax:null,token:null,prev:null};for(r();null===m&&++f<15e3;)switch(t.type){case"Match":if(null===h){if(null!==g&&(b!==e.length-1||"\\0"!==g.value&&"\\9"!==g.value)){t=Xt;break}m="Match";break}if((t=h.nextState)===Zt){if(h.matchStack===k){t=Xt;break}t=Qt}for(;h.syntaxStack!==u;)c();h=h.prev;break;case"Mismatch":if(null!==p&&!1!==p)(null===d||b>d.tokenIndex)&&(d=p,p=!1);else if(null===d){m="Mismatch";break}t=d.nextState,h=d.thenStack,u=d.syntaxStack,k=d.matchStack,b=d.tokenIndex,g=b<e.length?e[b]:null,d=d.prev;break;case"MatchGraph":t=t.match;break;case"If":t.else!==Xt&&s(t.else),t.then!==Qt&&o(t.then),t=t.match;break;case"MatchOnce":t={type:"MatchOnceBuffer",syntax:t,index:0,mask:0};break;case"MatchOnceBuffer":var v=t.syntax.terms;if(t.index===v.length){if(0===t.mask||t.syntax.all){t=Xt;break}t=Qt;break}if(t.mask===(1<<v.length)-1){t=Qt;break}for(;t.index<v.length;t.index++){var w=1<<t.index;if(0==(t.mask&w)){s(t),o({type:"AddMatchOnce",syntax:t.syntax,mask:t.mask|w}),t=v[t.index++];break}}break;case"AddMatchOnce":t={type:"MatchOnceBuffer",syntax:t.syntax,index:0,mask:t.mask};break;case"Enum":if(null!==g)if(-1!==(z=g.value.toLowerCase()).indexOf("\\")&&(z=z.replace(/\\[09].*$/,"")),$t.call(t.map,z)){t=t.map[z];break}t=Xt;break;case"Generic":var x=null!==u?u.opts:null,S=b+Math.floor(t.fn(g,a,x));if(!isNaN(S)&&S>b){for(;b<S;)l();t=Qt}else t=Xt;break;case"Type":case"Property":var C="Type"===t.type?"types":"properties",A=$t.call(n,C)?n[C][t.name]:null;if(!A||!A.match)throw new Error("Bad syntax reference: "+("Type"===t.type?"<"+t.name+">":"<'"+t.name+"'>"));if(!1!==p&&null!==g&&"Type"===t.type)if("custom-ident"===t.name&&g.type===Jt.Ident||"length"===t.name&&"0"===g.value){null===p&&(p=i(t,d)),t=Xt;break}u={syntax:t.syntax,opts:t.syntax.opts||null!==u&&u.opts||null,prev:u},k={type:2,syntax:t.syntax,token:k.token,prev:k},t=A.match;break;case"Keyword":var z=t.name;if(null!==g){var P=g.value;if(-1!==P.indexOf("\\")&&(P=P.replace(/\\[09].*$/,"")),tn(P,z)){l(),t=Qt;break}}t=Xt;break;case"AtKeyword":case"Function":if(null!==g&&tn(g.value,t.name)){l(),t=Qt;break}t=Xt;break;case"Token":if(null!==g&&g.value===t.value){l(),t=Qt;break}t=Xt;break;case"Comma":null!==g&&g.type===Jt.Comma?nn(k.token)?t=Xt:(l(),t=rn(g)?Xt:Qt):t=nn(k.token)||rn(g)?Qt:Xt;break;case"String":var L="";for(S=b;S<e.length&&L.length<t.value.length;S++)L+=e[S].value;if(tn(L,t.value)){for(;b<S;)l();t=Qt}else t=Xt;break;default:throw new Error("Unknown node type: "+t.type)}switch(f,m){case null:console.warn("[csstree-match] BREAK after 15000 iterations"),m="Maximum iteration number exceeded (please fill an issue on https://github.com/csstree/csstree/issues)",k=null;break;case"Match":for(;null!==u;)c();break;default:k=null}return{tokens:e,reason:m,iterations:f,match:k,longestMatch:y}}var on=function(e,t,n){var r=an(e,t,n||{});if(null===r.match)return r;var a=r.match,i=r.match={syntax:t.syntax||null,match:[]},o=[i];for(a=en(a).prev;null!==a;){switch(a.type){case 2:i.match.push(i={syntax:a.syntax,match:[]}),o.push(i);break;case 3:o.pop(),i=o[o.length-1];break;default:i.match.push({syntax:a.syntax||null,token:a.token.value,node:a.token.node})}a=a.prev}return r};function sn(e){function t(e){return null!==e&&("Type"===e.type||"Property"===e.type||"Keyword"===e.type)}var n=null;return null!==this.matched&&function r(a){if(Array.isArray(a.match)){for(var i=0;i<a.match.length;i++)if(r(a.match[i]))return t(a.syntax)&&n.unshift(a.syntax),!0}else if(a.node===e)return n=t(a.syntax)?[a.syntax]:[],!0;return!1}(this.matched),n}function ln(e,t,n){var r=sn.call(e,t);return null!==r&&r.some(n)}var cn={getTrace:sn,isType:function(e,t){return ln(this,e,(function(e){return"Type"===e.type&&e.name===t}))},isProperty:function(e,t){return ln(this,e,(function(e){return"Property"===e.type&&e.name===t}))},isKeyword:function(e){return ln(this,e,(function(e){return"Keyword"===e.type}))}};var un={matchFragments:function(e,t,n,r,a){var i=[];return null!==n.matched&&function n(s){if(null!==s.syntax&&s.syntax.type===r&&s.syntax.name===a){var l=function e(t){return"node"in t?t.node:e(t.match[0])}(s),c=function e(t){return"node"in t?t.node:e(t.match[t.match.length-1])}(s);e.syntax.walk(t,(function(e,t,n){if(e===l){var r=new o;do{if(r.appendData(t.data),t.data===c)break;t=t.next}while(null!==t);i.push({parent:n,nodes:r})}}))}Array.isArray(s.match)&&s.match.forEach(n)}(n.matched),i}},hn=Object.prototype.hasOwnProperty;function dn(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&e>=0}function pn(e){return Boolean(e)&&dn(e.offset)&&dn(e.line)&&dn(e.column)}function fn(e,t){return function(n,r){if(!n||n.constructor!==Object)return r(n,"Type of node should be an Object");for(var a in n){var i=!0;if(!1!==hn.call(n,a)){if("type"===a)n.type!==e&&r(n,"Wrong node type `"+n.type+"`, expected `"+e+"`");else if("loc"===a){if(null===n.loc)continue;if(n.loc&&n.loc.constructor===Object)if("string"!=typeof n.loc.source)a+=".source";else if(pn(n.loc.start)){if(pn(n.loc.end))continue;a+=".end"}else a+=".start";i=!1}else if(t.hasOwnProperty(a)){var s=0;for(i=!1;!i&&s<t[a].length;s++){var l=t[a][s];switch(l){case String:i="string"==typeof n[a];break;case Boolean:i="boolean"==typeof n[a];break;case null:i=null===n[a];break;default:"string"==typeof l?i=n[a]&&n[a].type===l:Array.isArray(l)&&(i=n[a]instanceof o)}}}else r(n,"Unknown field `"+a+"` for "+e+" node type");i||r(n,"Bad value for `"+e+"."+a+"`")}}for(var a in t)hn.call(t,a)&&!1===hn.call(n,a)&&r(n,"Field `"+e+"."+a+"` is missed")}}function mn(e,t){var n=t.structure,r={type:String,loc:!0},a={type:'"'+e+'"'};for(var i in n)if(!1!==hn.call(n,i)){for(var o=[],s=r[i]=Array.isArray(n[i])?n[i].slice():[n[i]],l=0;l<s.length;l++){var c=s[l];if(c===String||c===Boolean)o.push(c.name);else if(null===c)o.push("null");else if("string"==typeof c)o.push("<"+c+">");else{if(!Array.isArray(c))throw new Error("Wrong value `"+c+"` in `"+e+"."+i+"` structure definition");o.push("List")}}a[i]=o.join(" | ")}return{docs:a,check:fn(e,r)}}var gn=X,bn=Z,yn=Gt,kn=on,vn=function(e){var t={};if(e.node)for(var n in e.node)if(hn.call(e.node,n)){var r=e.node[n];if(!r.structure)throw new Error("Missed `structure` field in `"+n+"` node type definition");t[n]=mn(n,r)}return t},wn=yn("inherit | initial | unset"),xn=yn("inherit | initial | unset | <-ms-legacy-expression>");function Sn(e,t,n){var r={};for(var a in e)e[a].syntax&&(r[a]=n?e[a].syntax:$(e[a].syntax,{compact:t}));return r}function Cn(e,t,n){return{matched:e,iterations:n,error:t,getTrace:cn.getTrace,isType:cn.isType,isProperty:cn.isProperty,isKeyword:cn.isKeyword}}function An(e,t,n,r){var a,i=function(e,t){return"string"==typeof e?Bt(e,null):t.generate(e,Nt)}(n,e.syntax);return function(e){for(var t=0;t<e.length;t++)if("var("===e[t].value.toLowerCase())return!0;return!1}(i)?Cn(null,new Error("Matching for a tree with var() is not supported")):(r&&(a=kn(i,e.valueCommonSyntax,e)),r&&a.match||(a=kn(i,t.match,e)).match?Cn(a.match,null,a.iterations):Cn(null,new bn(a.reason,t.syntax,n,a),a.iterations))}var zn=function(e,t,n){if(this.valueCommonSyntax=wn,this.syntax=t,this.generic=!1,this.atrules={},this.properties={},this.types={},this.structure=n||vn(e),e){if(e.types)for(var r in e.types)this.addType_(r,e.types[r]);if(e.generic)for(var r in this.generic=!0,pt)this.addType_(r,pt[r]);if(e.atrules)for(var r in e.atrules)this.addAtrule_(r,e.atrules[r]);if(e.properties)for(var r in e.properties)this.addProperty_(r,e.properties[r])}};zn.prototype={structure:{},checkStructure:function(e){function t(e,t){r.push({node:e,message:t})}var n=this.structure,r=[];return this.syntax.walk(e,(function(e){n.hasOwnProperty(e.type)?n[e.type].check(e,t):t(e,"Unknown node type `"+e.type+"`")})),!!r.length&&r},createDescriptor:function(e,t,n){var r={type:t,name:n},a={type:t,name:n,syntax:null,match:null};return"function"==typeof e?a.match=yn(e,r):("string"==typeof e?Object.defineProperty(a,"syntax",{get:function(){return Object.defineProperty(a,"syntax",{value:Ot(e)}),a.syntax}}):a.syntax=e,Object.defineProperty(a,"match",{get:function(){return Object.defineProperty(a,"match",{value:yn(a.syntax,r)}),a.match}})),a},addAtrule_:function(e,t){this.atrules[e]={prelude:t.prelude?this.createDescriptor(t.prelude,"AtrulePrelude",e):null,descriptors:t.descriptors?Object.keys(t.descriptors).reduce((e,n)=>(e[n]=this.createDescriptor(t.descriptors[n],"AtruleDescriptor",n),e),{}):null}},addProperty_:function(e,t){this.properties[e]=this.createDescriptor(t,"Property",e)},addType_:function(e,t){this.types[e]=this.createDescriptor(t,"Type",e),t===pt["-ms-legacy-expression"]&&(this.valueCommonSyntax=xn)},matchAtrulePrelude:function(e,t){var n=ae.keyword(e),r=n.vendor?this.getAtrulePrelude(n.name)||this.getAtrulePrelude(n.basename):this.getAtrulePrelude(n.name);return r?An(this,r,t,!0):n.basename in this.atrules?Cn(null,new Error("At-rule `"+e+"` should not contain a prelude")):Cn(null,new gn("Unknown at-rule",e))},matchAtruleDescriptor:function(e,t,n){var r=ae.keyword(e),a=ae.keyword(t),i=r.vendor?this.atrules[r.name]||this.atrules[r.basename]:this.atrules[r.name];if(!i)return Cn(null,new gn("Unknown at-rule",e));if(!i.descriptors)return Cn(null,new Error("At-rule `"+e+"` has no known descriptors"));var o=a.vendor?i.descriptors[a.name]||i.descriptors[a.basename]:i.descriptors[a.name];return o?An(this,o,n,!0):Cn(null,new gn("Unknown at-rule descriptor",t))},matchDeclaration:function(e){return"Declaration"!==e.type?Cn(null,new Error("Not a Declaration node")):this.matchProperty(e.property,e.value)},matchProperty:function(e,t){var n=ae.property(e);if(n.custom)return Cn(null,new Error("Lexer matching doesn't applicable for custom properties"));var r=n.vendor?this.getProperty(n.name)||this.getProperty(n.basename):this.getProperty(n.name);return r?An(this,r,t,!0):Cn(null,new gn("Unknown property",e))},matchType:function(e,t){var n=this.getType(e);return n?An(this,n,t,!1):Cn(null,new gn("Unknown type",e))},match:function(e,t){return"string"==typeof e||e&&e.type?("string"!=typeof e&&e.match||(e=this.createDescriptor(e,"Type","anonymous")),An(this,e,t,!1)):Cn(null,new gn("Bad syntax"))},findValueFragments:function(e,t,n,r){return un.matchFragments(this,t,this.matchProperty(e,t),n,r)},findDeclarationValueFragments:function(e,t,n){return un.matchFragments(this,e.value,this.matchDeclaration(e),t,n)},findAllFragments:function(e,t,n){var r=[];return this.syntax.walk(e,{visit:"Declaration",enter:function(e){r.push.apply(r,this.findDeclarationValueFragments(e,t,n))}.bind(this)}),r},getAtrulePrelude:function(e){return this.atrules.hasOwnProperty(e)?this.atrules[e].prelude:null},getAtruleDescriptor:function(e,t){return this.atrules.hasOwnProperty(e)&&this.atrules.declarators&&this.atrules[e].declarators[t]||null},getProperty:function(e){return this.properties.hasOwnProperty(e)?this.properties[e]:null},getType:function(e){return this.types.hasOwnProperty(e)?this.types[e]:null},validate:function(){function e(r,a,i,o){if(i.hasOwnProperty(a))return i[a];i[a]=!1,null!==o.syntax&&It(o.syntax,(function(o){if("Type"===o.type||"Property"===o.type){var s="Type"===o.type?r.types:r.properties,l="Type"===o.type?t:n;s.hasOwnProperty(o.name)&&!e(r,o.name,l,s[o.name])||(i[a]=!0)}}),this)}var t={},n={};for(var r in this.types)e(this,r,t,this.types[r]);for(var r in this.properties)e(this,r,n,this.properties[r]);return t=Object.keys(t).filter((function(e){return t[e]})),n=Object.keys(n).filter((function(e){return n[e]})),t.length||n.length?{types:t,properties:n}:null},dump:function(e,t){return{generic:this.generic,types:Sn(this.types,!t,e),properties:Sn(this.properties,!t,e)}},toString:function(){return JSON.stringify(this.dump())}};var Pn=zn,Ln={SyntaxError:ft,parse:Ot,generate:$,walk:It},Tn=Se.isBOM;var En=function(){this.lines=null,this.columns=null,this.linesAndColumnsComputed=!1};En.prototype={setSource:function(e,t,n,r){this.source=e,this.startOffset=void 0===t?0:t,this.startLine=void 0===n?1:n,this.startColumn=void 0===r?1:r,this.linesAndColumnsComputed=!1},ensureLinesAndColumnsComputed:function(){this.linesAndColumnsComputed||(!function(e,t){for(var n=t.length,r=oe(e.lines,n),a=e.startLine,i=oe(e.columns,n),o=e.startColumn,s=t.length>0?Tn(t.charCodeAt(0)):0;s<n;s++){var l=t.charCodeAt(s);r[s]=a,i[s]=o++,10!==l&&13!==l&&12!==l||(13===l&&s+1<n&&10===t.charCodeAt(s+1)&&(r[++s]=a,i[s]=o),a++,o=1)}r[s]=a,i[s]=o,e.lines=r,e.columns=i}(this,this.source),this.linesAndColumnsComputed=!0)},getLocation:function(e,t){return this.ensureLinesAndColumnsComputed(),{source:t,offset:this.startOffset+e,line:this.lines[e],column:this.columns[e]}},getLocationRange:function(e,t,n){return this.ensureLinesAndColumnsComputed(),{source:n,start:{offset:this.startOffset+e,line:this.lines[e],column:this.columns[e]},end:{offset:this.startOffset+t,line:this.lines[t],column:this.columns[t]}}}};var On=En,Dn=Se.TYPE,Rn=Dn.WhiteSpace,In=Dn.Comment,Mn=function(e){var t=this.createList(),n=null,r={recognizer:e,space:null,ignoreWS:!1,ignoreWSAfter:!1};for(this.scanner.skipSC();!this.scanner.eof;){switch(this.scanner.tokenType){case In:this.scanner.next();continue;case Rn:r.ignoreWS?this.scanner.next():r.space=this.WhiteSpace();continue}if(void 0===(n=e.getNode.call(this,r)))break;null!==r.space&&(t.push(r.space),r.space=null),t.push(n),r.ignoreWSAfter?(r.ignoreWSAfter=!1,r.ignoreWS=!0):r.ignoreWS=!1}return t},Nn=j.findWhiteSpaceStart,Bn=function(){},jn=d.TYPE,_n=d.NAME,qn=jn.WhiteSpace,Wn=jn.Ident,Fn=jn.Function,Un=jn.Url,Yn=jn.Hash,Hn=jn.Percentage,Vn=jn.Number;function Kn(e){return function(){return this[e]()}}var Gn=function(e){var t={scanner:new V,locationMap:new On,filename:"<unknown>",needPositions:!1,onParseError:Bn,onParseErrorThrow:!1,parseAtrulePrelude:!0,parseRulePrelude:!0,parseValue:!0,parseCustomProperty:!1,readSequence:Mn,createList:function(){return new o},createSingleNodeList:function(e){return(new o).appendData(e)},getFirstListNode:function(e){return e&&e.first()},getLastListNode:function(e){return e.last()},parseWithFallback:function(e,t){var n=this.scanner.tokenIndex;try{return e.call(this)}catch(e){if(this.onParseErrorThrow)throw e;var r=t.call(this,n);return this.onParseErrorThrow=!0,this.onParseError(e,r),this.onParseErrorThrow=!1,r}},lookupNonWSType:function(e){do{var t=this.scanner.lookupType(e++);if(t!==qn)return t}while(0!==t);return 0},eat:function(e){if(this.scanner.tokenType!==e){var t=this.scanner.tokenStart,n=_n[e]+" is expected";switch(e){case Wn:this.scanner.tokenType===Fn||this.scanner.tokenType===Un?(t=this.scanner.tokenEnd-1,n="Identifier is expected but function found"):n="Identifier is expected";break;case Yn:this.scanner.isDelim(35)&&(this.scanner.next(),t++,n="Name is expected");break;case Hn:this.scanner.tokenType===Vn&&(t=this.scanner.tokenEnd,n="Percent sign is expected");break;default:this.scanner.source.charCodeAt(this.scanner.tokenStart)===e&&(t+=1)}this.error(n,t)}this.scanner.next()},consume:function(e){var t=this.scanner.getTokenValue();return this.eat(e),t},consumeFunctionName:function(){var e=this.scanner.source.substring(this.scanner.tokenStart,this.scanner.tokenEnd-1);return this.eat(Fn),e},getLocation:function(e,t){return this.needPositions?this.locationMap.getLocationRange(e,t,this.filename):null},getLocationFromList:function(e){if(this.needPositions){var t=this.getFirstListNode(e),n=this.getLastListNode(e);return this.locationMap.getLocationRange(null!==t?t.loc.start.offset-this.locationMap.startOffset:this.scanner.tokenStart,null!==n?n.loc.end.offset-this.locationMap.startOffset:this.scanner.tokenStart,this.filename)}return null},error:function(e,t){var n=void 0!==t&&t<this.scanner.source.length?this.locationMap.getLocation(t):this.scanner.eof?this.locationMap.getLocation(Nn(this.scanner.source,this.scanner.source.length-1)):this.locationMap.getLocation(this.scanner.tokenStart);throw new c(e||"Unexpected input",this.scanner.source,n.offset,n.line,n.column)}};for(var n in e=function(e){var t={context:{},scope:{},atrule:{},pseudo:{}};if(e.parseContext)for(var n in e.parseContext)switch(typeof e.parseContext[n]){case"function":t.context[n]=e.parseContext[n];break;case"string":t.context[n]=Kn(e.parseContext[n])}if(e.scope)for(var n in e.scope)t.scope[n]=e.scope[n];if(e.atrule)for(var n in e.atrule){var r=e.atrule[n];r.parse&&(t.atrule[n]=r.parse)}if(e.pseudo)for(var n in e.pseudo){var a=e.pseudo[n];a.parse&&(t.pseudo[n]=a.parse)}if(e.node)for(var n in e.node)t[n]=e.node[n].parse;return t}(e||{}))t[n]=e[n];return function(e,n){var r,a=(n=n||{}).context||"default";if(Se(e,t.scanner),t.locationMap.setSource(e,n.offset,n.line,n.column),t.filename=n.filename||"<unknown>",t.needPositions=Boolean(n.positions),t.onParseError="function"==typeof n.onParseError?n.onParseError:Bn,t.onParseErrorThrow=!1,t.parseAtrulePrelude=!("parseAtrulePrelude"in n)||Boolean(n.parseAtrulePrelude),t.parseRulePrelude=!("parseRulePrelude"in n)||Boolean(n.parseRulePrelude),t.parseValue=!("parseValue"in n)||Boolean(n.parseValue),t.parseCustomProperty="parseCustomProperty"in n&&Boolean(n.parseCustomProperty),!t.context.hasOwnProperty(a))throw new Error("Unknown context `"+a+"`");return r=t.context[a].call(t,n),t.scanner.eof||t.error(),r}},$n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),Qn=function(e){if(0<=e&&e<$n.length)return $n[e];throw new TypeError("Must be between 0 and 63: "+e)};var Xn=function(e){var t,n="",r=function(e){return e<0?1+(-e<<1):0+(e<<1)}(e);do{t=31&r,(r>>>=5)>0&&(t|=32),n+=Qn(t)}while(r>0);return n};function Zn(e){return e&&e.default||e}var Jn=function(e,t){return e(t={exports:{}},t.exports),t.exports}((function(e,t){t.getArg=function(e,t,n){if(t in e)return e[t];if(3===arguments.length)return n;throw new Error('"'+t+'" is a required argument.')};var n=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.-]*)(?::(\d+))?(.*)$/,r=/^data:.+\,.+$/;function a(e){var t=e.match(n);return t?{scheme:t[1],auth:t[2],host:t[3],port:t[4],path:t[5]}:null}function i(e){var t="";return e.scheme&&(t+=e.scheme+":"),t+="//",e.auth&&(t+=e.auth+"@"),e.host&&(t+=e.host),e.port&&(t+=":"+e.port),e.path&&(t+=e.path),t}function o(e){var n=e,r=a(e);if(r){if(!r.path)return e;n=r.path}for(var o,s=t.isAbsolute(n),l=n.split(/\/+/),c=0,u=l.length-1;u>=0;u--)"."===(o=l[u])?l.splice(u,1):".."===o?c++:c>0&&(""===o?(l.splice(u+1,c),c=0):(l.splice(u,2),c--));return""===(n=l.join("/"))&&(n=s?"/":"."),r?(r.path=n,i(r)):n}function s(e,t){""===e&&(e="."),""===t&&(t=".");var n=a(t),s=a(e);if(s&&(e=s.path||"/"),n&&!n.scheme)return s&&(n.scheme=s.scheme),i(n);if(n||t.match(r))return t;if(s&&!s.host&&!s.path)return s.host=t,i(s);var l="/"===t.charAt(0)?t:o(e.replace(/\/+$/,"")+"/"+t);return s?(s.path=l,i(s)):l}t.urlParse=a,t.urlGenerate=i,t.normalize=o,t.join=s,t.isAbsolute=function(e){return"/"===e.charAt(0)||n.test(e)},t.relative=function(e,t){""===e&&(e="."),e=e.replace(/\/$/,"");for(var n=0;0!==t.indexOf(e+"/");){var r=e.lastIndexOf("/");if(r<0)return t;if((e=e.slice(0,r)).match(/^([^\/]+:\/)?\/*$/))return t;++n}return Array(n+1).join("../")+t.substr(e.length+1)};var l=!("__proto__"in Object.create(null));function c(e){return e}function u(e){if(!e)return!1;var t=e.length;if(t<9)return!1;if(95!==e.charCodeAt(t-1)||95!==e.charCodeAt(t-2)||111!==e.charCodeAt(t-3)||116!==e.charCodeAt(t-4)||111!==e.charCodeAt(t-5)||114!==e.charCodeAt(t-6)||112!==e.charCodeAt(t-7)||95!==e.charCodeAt(t-8)||95!==e.charCodeAt(t-9))return!1;for(var n=t-10;n>=0;n--)if(36!==e.charCodeAt(n))return!1;return!0}function h(e,t){return e===t?0:null===e?1:null===t?-1:e>t?1:-1}t.toSetString=l?c:function(e){return u(e)?"$"+e:e},t.fromSetString=l?c:function(e){return u(e)?e.slice(1):e},t.compareByOriginalPositions=function(e,t,n){var r=h(e.source,t.source);return 0!==r||0!==(r=e.originalLine-t.originalLine)||0!==(r=e.originalColumn-t.originalColumn)||n||0!==(r=e.generatedColumn-t.generatedColumn)||0!==(r=e.generatedLine-t.generatedLine)?r:h(e.name,t.name)},t.compareByGeneratedPositionsDeflated=function(e,t,n){var r=e.generatedLine-t.generatedLine;return 0!==r||0!==(r=e.generatedColumn-t.generatedColumn)||n||0!==(r=h(e.source,t.source))||0!==(r=e.originalLine-t.originalLine)||0!==(r=e.originalColumn-t.originalColumn)?r:h(e.name,t.name)},t.compareByGeneratedPositionsInflated=function(e,t){var n=e.generatedLine-t.generatedLine;return 0!==n||0!==(n=e.generatedColumn-t.generatedColumn)||0!==(n=h(e.source,t.source))||0!==(n=e.originalLine-t.originalLine)||0!==(n=e.originalColumn-t.originalColumn)?n:h(e.name,t.name)},t.parseSourceMapInput=function(e){return JSON.parse(e.replace(/^\)]}'[^\n]*\n/,""))},t.computeSourceURL=function(e,t,n){if(t=t||"",e&&("/"!==e[e.length-1]&&"/"!==t[0]&&(e+="/"),t=e+t),n){var r=a(n);if(!r)throw new Error("sourceMapURL could not be parsed");if(r.path){var l=r.path.lastIndexOf("/");l>=0&&(r.path=r.path.substring(0,l+1))}t=s(i(r),t)}return o(t)}})),er=(Jn.getArg,Jn.urlParse,Jn.urlGenerate,Jn.normalize,Jn.join,Jn.isAbsolute,Jn.relative,Jn.toSetString,Jn.fromSetString,Jn.compareByOriginalPositions,Jn.compareByGeneratedPositionsDeflated,Jn.compareByGeneratedPositionsInflated,Jn.parseSourceMapInput,Jn.computeSourceURL,Object.prototype.hasOwnProperty),tr="undefined"!=typeof Map;function nr(){this._array=[],this._set=tr?new Map:Object.create(null)}nr.fromArray=function(e,t){for(var n=new nr,r=0,a=e.length;r<a;r++)n.add(e[r],t);return n},nr.prototype.size=function(){return tr?this._set.size:Object.getOwnPropertyNames(this._set).length},nr.prototype.add=function(e,t){var n=tr?e:Jn.toSetString(e),r=tr?this.has(e):er.call(this._set,n),a=this._array.length;r&&!t||this._array.push(e),r||(tr?this._set.set(e,a):this._set[n]=a)},nr.prototype.has=function(e){if(tr)return this._set.has(e);var t=Jn.toSetString(e);return er.call(this._set,t)},nr.prototype.indexOf=function(e){if(tr){var t=this._set.get(e);if(t>=0)return t}else{var n=Jn.toSetString(e);if(er.call(this._set,n))return this._set[n]}throw new Error('"'+e+'" is not in the set.')},nr.prototype.at=function(e){if(e>=0&&e<this._array.length)return this._array[e];throw new Error("No element indexed by "+e)},nr.prototype.toArray=function(){return this._array.slice()};var rr={ArraySet:nr};function ar(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}ar.prototype.unsortedForEach=function(e,t){this._array.forEach(e,t)},ar.prototype.add=function(e){var t,n,r,a,i,o;t=this._last,n=e,r=t.generatedLine,a=n.generatedLine,i=t.generatedColumn,o=n.generatedColumn,a>r||a==r&&o>=i||Jn.compareByGeneratedPositionsInflated(t,n)<=0?(this._last=e,this._array.push(e)):(this._sorted=!1,this._array.push(e))},ar.prototype.toArray=function(){return this._sorted||(this._array.sort(Jn.compareByGeneratedPositionsInflated),this._sorted=!0),this._array};var ir=rr.ArraySet,or={MappingList:ar}.MappingList;function sr(e){e||(e={}),this._file=Jn.getArg(e,"file",null),this._sourceRoot=Jn.getArg(e,"sourceRoot",null),this._skipValidation=Jn.getArg(e,"skipValidation",!1),this._sources=new ir,this._names=new ir,this._mappings=new or,this._sourcesContents=null}sr.prototype._version=3,sr.fromSourceMap=function(e){var t=e.sourceRoot,n=new sr({file:e.file,sourceRoot:t});return e.eachMapping((function(e){var r={generated:{line:e.generatedLine,column:e.generatedColumn}};null!=e.source&&(r.source=e.source,null!=t&&(r.source=Jn.relative(t,r.source)),r.original={line:e.originalLine,column:e.originalColumn},null!=e.name&&(r.name=e.name)),n.addMapping(r)})),e.sources.forEach((function(r){var a=r;null!==t&&(a=Jn.relative(t,r)),n._sources.has(a)||n._sources.add(a);var i=e.sourceContentFor(r);null!=i&&n.setSourceContent(r,i)})),n},sr.prototype.addMapping=function(e){var t=Jn.getArg(e,"generated"),n=Jn.getArg(e,"original",null),r=Jn.getArg(e,"source",null),a=Jn.getArg(e,"name",null);this._skipValidation||this._validateMapping(t,n,r,a),null!=r&&(r=String(r),this._sources.has(r)||this._sources.add(r)),null!=a&&(a=String(a),this._names.has(a)||this._names.add(a)),this._mappings.add({generatedLine:t.line,generatedColumn:t.column,originalLine:null!=n&&n.line,originalColumn:null!=n&&n.column,source:r,name:a})},sr.prototype.setSourceContent=function(e,t){var n=e;null!=this._sourceRoot&&(n=Jn.relative(this._sourceRoot,n)),null!=t?(this._sourcesContents||(this._sourcesContents=Object.create(null)),this._sourcesContents[Jn.toSetString(n)]=t):this._sourcesContents&&(delete this._sourcesContents[Jn.toSetString(n)],0===Object.keys(this._sourcesContents).length&&(this._sourcesContents=null))},sr.prototype.applySourceMap=function(e,t,n){var r=t;if(null==t){if(null==e.file)throw new Error('SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map\'s "file" property. Both were omitted.');r=e.file}var a=this._sourceRoot;null!=a&&(r=Jn.relative(a,r));var i=new ir,o=new ir;this._mappings.unsortedForEach((function(t){if(t.source===r&&null!=t.originalLine){var s=e.originalPositionFor({line:t.originalLine,column:t.originalColumn});null!=s.source&&(t.source=s.source,null!=n&&(t.source=Jn.join(n,t.source)),null!=a&&(t.source=Jn.relative(a,t.source)),t.originalLine=s.line,t.originalColumn=s.column,null!=s.name&&(t.name=s.name))}var l=t.source;null==l||i.has(l)||i.add(l);var c=t.name;null==c||o.has(c)||o.add(c)}),this),this._sources=i,this._names=o,e.sources.forEach((function(t){var r=e.sourceContentFor(t);null!=r&&(null!=n&&(t=Jn.join(n,t)),null!=a&&(t=Jn.relative(a,t)),this.setSourceContent(t,r))}),this)},sr.prototype._validateMapping=function(e,t,n,r){if(t&&"number"!=typeof t.line&&"number"!=typeof t.column)throw new Error("original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.");if((!(e&&"line"in e&&"column"in e&&e.line>0&&e.column>=0)||t||n||r)&&!(e&&"line"in e&&"column"in e&&t&&"line"in t&&"column"in t&&e.line>0&&e.column>=0&&t.line>0&&t.column>=0&&n))throw new Error("Invalid mapping: "+JSON.stringify({generated:e,source:n,original:t,name:r}))},sr.prototype._serializeMappings=function(){for(var e,t,n,r,a=0,i=1,o=0,s=0,l=0,c=0,u="",h=this._mappings.toArray(),d=0,p=h.length;d<p;d++){if(e="",(t=h[d]).generatedLine!==i)for(a=0;t.generatedLine!==i;)e+=";",i++;else if(d>0){if(!Jn.compareByGeneratedPositionsInflated(t,h[d-1]))continue;e+=","}e+=Xn(t.generatedColumn-a),a=t.generatedColumn,null!=t.source&&(r=this._sources.indexOf(t.source),e+=Xn(r-c),c=r,e+=Xn(t.originalLine-1-s),s=t.originalLine-1,e+=Xn(t.originalColumn-o),o=t.originalColumn,null!=t.name&&(n=this._names.indexOf(t.name),e+=Xn(n-l),l=n)),u+=e}return u},sr.prototype._generateSourcesContent=function(e,t){return e.map((function(e){if(!this._sourcesContents)return null;null!=t&&(e=Jn.relative(t,e));var n=Jn.toSetString(e);return Object.prototype.hasOwnProperty.call(this._sourcesContents,n)?this._sourcesContents[n]:null}),this)},sr.prototype.toJSON=function(){var e={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};return null!=this._file&&(e.file=this._file),null!=this._sourceRoot&&(e.sourceRoot=this._sourceRoot),this._sourcesContents&&(e.sourcesContent=this._generateSourcesContent(e.sources,e.sourceRoot)),e},sr.prototype.toString=function(){return JSON.stringify(this.toJSON())};var lr={SourceMapGenerator:sr}.SourceMapGenerator,cr={Atrule:!0,Selector:!0,Declaration:!0},ur=Object.prototype.hasOwnProperty;function hr(e,t){var n=e.children,r=null;"function"!=typeof t?n.forEach(this.node,this):n.forEach((function(e){null!==r&&t.call(this,r),this.node(e),r=e}),this)}var dr=function(e){function t(e){if(!ur.call(n,e.type))throw new Error("Unknown node type: "+e.type);n[e.type].call(this,e)}var n={};if(e.node)for(var r in e.node)n[r]=e.node[r].generate;return function(e,n){var r="",a={children:hr,node:t,chunk:function(e){r+=e},result:function(){return r}};return n&&("function"==typeof n.decorator&&(a=n.decorator(a)),n.sourceMap&&(a=function(e){var t=new lr,n=1,r=0,a={line:1,column:0},i={line:0,column:0},o=!1,s={line:1,column:0},l={generated:s},c=e.node;e.node=function(e){if(e.loc&&e.loc.start&&cr.hasOwnProperty(e.type)){var u=e.loc.start.line,h=e.loc.start.column-1;i.line===u&&i.column===h||(i.line=u,i.column=h,a.line=n,a.column=r,o&&(o=!1,a.line===s.line&&a.column===s.column||t.addMapping(l)),o=!0,t.addMapping({source:e.loc.source,original:i,generated:a}))}c.call(this,e),o&&cr.hasOwnProperty(e.type)&&(s.line=n,s.column=r)};var u=e.chunk;e.chunk=function(e){for(var t=0;t<e.length;t++)10===e.charCodeAt(t)?(n++,r=0):r++;u(e)};var h=e.result;return e.result=function(){return o&&t.addMapping(l),{css:h(),map:t}},e}(a))),a.node(e),a.result()}},pr=Object.prototype.hasOwnProperty,fr=function(){};function mr(e){return"function"==typeof e?e:fr}function gr(e,t){return function(n,r,a){n.type===t&&e.call(this,n,r,a)}}function br(e,t){var n=t.structure,r=[];for(var a in n)if(!1!==pr.call(n,a)){var i=n[a],o={name:a,type:!1,nullable:!1};Array.isArray(n[a])||(i=[n[a]]);for(var s=0;s<i.length;s++){var l=i[s];null===l?o.nullable=!0:"string"==typeof l?o.type="node":Array.isArray(l)&&(o.type="list")}o.type&&r.push(o)}return r.length?{context:t.walkContext,fields:r}:null}function yr(e,t){var n=e.fields.slice(),r=e.context,a="string"==typeof r;return t&&n.reverse(),function(e,i,o){var s;a&&(s=i[r],i[r]=e);for(var l=0;l<n.length;l++){var c=n[l],u=e[c.name];c.nullable&&!u||("list"===c.type?t?u.forEachRight(o):u.forEach(o):o(u))}a&&(i[r]=s)}}function kr(e){return{Atrule:{StyleSheet:e.StyleSheet,Atrule:e.Atrule,Rule:e.Rule,Block:e.Block},Rule:{StyleSheet:e.StyleSheet,Atrule:e.Atrule,Rule:e.Rule,Block:e.Block},Declaration:{StyleSheet:e.StyleSheet,Atrule:e.Atrule,Rule:e.Rule,Block:e.Block,DeclarationList:e.DeclarationList}}}var vr=function(e){var t=function(e){var t={};for(var n in e.node)if(pr.call(e.node,n)){var r=e.node[n];if(!r.structure)throw new Error("Missed `structure` field in `"+n+"` node type definition");t[n]=br(0,r)}return t}(e),n={},r={};for(var a in t)pr.call(t,a)&&null!==t[a]&&(n[a]=yr(t[a],!1),r[a]=yr(t[a],!0));var i=kr(n),o=kr(r),s=function(e,a){var s=fr,l=fr,c=n,u={root:e,stylesheet:null,atrule:null,atrulePrelude:null,rule:null,selector:null,block:null,declaration:null,function:null};if("function"==typeof a)s=a;else if(a&&(s=mr(a.enter),l=mr(a.leave),a.reverse&&(c=r),a.visit)){if(i.hasOwnProperty(a.visit))c=a.reverse?o[a.visit]:i[a.visit];else if(!t.hasOwnProperty(a.visit))throw new Error("Bad value `"+a.visit+"` for `visit` option (should be: "+Object.keys(t).join(", ")+")");s=gr(s,a.visit),l=gr(l,a.visit)}if(s===fr&&l===fr)throw new Error("Neither `enter` nor `leave` walker handler is set or both aren't a function");if(a.reverse){var h=s;s=l,l=h}!function e(t,n,r){s.call(u,t,n,r),c.hasOwnProperty(t.type)&&c[t.type](t,u,e),l.call(u,t,n,r)}(e)};return s.find=function(e,t){var n=null;return s(e,(function(e,r,a){null===n&&t.call(this,e,r,a)&&(n=e)})),n},s.findLast=function(e,t){var n=null;return s(e,{reverse:!0,enter:function(e,r,a){null===n&&t.call(this,e,r,a)&&(n=e)}}),n},s.findAll=function(e,t){var n=[];return s(e,(function(e,r,a){t.call(this,e,r,a)&&n.push(e)})),n},s},wr=function e(t){var n={};for(var r in t){var a=t[r];a&&(Array.isArray(a)||a instanceof o?a=a.map(e):a.constructor===Object&&(a=e(a))),n[r]=a}return n},xr=Object.prototype.hasOwnProperty,Sr={generic:!0,types:{},atrules:{},properties:{},parseContext:{},scope:{},atrule:["parse"],pseudo:["parse"],node:["name","structure","parse","generate","walkContext"]};function Cr(e){return e&&e.constructor===Object}function Ar(e){return Cr(e)?Object.assign({},e):e}function zr(e,t){for(var n in t)xr.call(t,n)&&(Cr(e[n])?zr(e[n],Ar(t[n])):e[n]=Ar(t[n]))}var Pr=function(e,t){return function e(t,n,r){for(var a in r)if(!1!==xr.call(r,a))if(!0===r[a])a in n&&xr.call(n,a)&&(t[a]=Ar(n[a]));else if(r[a]){if(Cr(r[a]))zr(i={},t[a]),zr(i,n[a]),t[a]=i;else if(Array.isArray(r[a])){var i={},o=r[a].reduce((function(e,t){return e[t]=!0,e}),{});for(var s in t[a])xr.call(t[a],s)&&(i[s]={},t[a]&&t[a][s]&&e(i[s],t[a][s],o));for(var s in n[a])xr.call(n[a],s)&&(i[s]||(i[s]={}),n[a]&&n[a][s]&&e(i[s],n[a][s],o));t[a]=i}}return t}(e,t,Sr)};function Lr(e){var t=Gn(e),n=vr(e),r=dr(e),a=function(e){return{fromPlainObject:function(t){return e(t,{enter:function(e){e.children&&e.children instanceof o==!1&&(e.children=(new o).fromArray(e.children))}}),t},toPlainObject:function(t){return e(t,{leave:function(e){e.children&&e.children instanceof o&&(e.children=e.children.toArray())}}),t}}}(n),i={List:o,SyntaxError:c,TokenStream:V,Lexer:Pn,vendorPrefix:ae.vendorPrefix,keyword:ae.keyword,property:ae.property,isCustomProperty:ae.isCustomProperty,definitionSyntax:Ln,lexer:null,createLexer:function(e){return new Pn(e,i,i.lexer.structure)},tokenize:Se,parse:t,walk:n,generate:r,find:n.find,findLast:n.findLast,findAll:n.findAll,clone:wr,fromPlainObject:a.fromPlainObject,toPlainObject:a.toPlainObject,createSyntax:function(e){return Lr(Pr({},e))},fork:function(t){var n=Pr({},e);return Lr("function"==typeof t?t(n,Object.assign):Pr(n,t))}};return i.lexer=new Pn({generic:!0,types:e.types,atrules:e.atrules,properties:e.properties,node:e.node},i),i}var Tr=function(e){return Lr(Pr({},e))},Er={"absolute-size":"xx-small|x-small|small|medium|large|x-large|xx-large","alpha-value":"<number>|<percentage>","angle-percentage":"<angle>|<percentage>","angular-color-hint":"<angle-percentage>","angular-color-stop":"<color>&&<color-stop-angle>?","angular-color-stop-list":"[<angular-color-stop> [, <angular-color-hint>]?]# , <angular-color-stop>","animateable-feature":"scroll-position|contents|<custom-ident>",attachment:"scroll|fixed|local","attr()":"attr( <attr-name> <type-or-unit>? [, <attr-fallback>]? )","attr-matcher":"['~'|'|'|'^'|'$'|'*']? '='","attr-modifier":"i|s","attribute-selector":"'[' <wq-name> ']'|'[' <wq-name> <attr-matcher> [<string-token>|<ident-token>] <attr-modifier>? ']'","auto-repeat":"repeat( [auto-fill|auto-fit] , [<line-names>? <fixed-size>]+ <line-names>? )","auto-track-list":"[<line-names>? [<fixed-size>|<fixed-repeat>]]* <line-names>? <auto-repeat> [<line-names>? [<fixed-size>|<fixed-repeat>]]* <line-names>?","baseline-position":"[first|last]? baseline","basic-shape":"<inset()>|<circle()>|<ellipse()>|<polygon()>","bg-image":"none|<image>","bg-layer":"<bg-image>||<bg-position> [/ <bg-size>]?||<repeat-style>||<attachment>||<box>||<box>","bg-position":"[[left|center|right|top|bottom|<length-percentage>]|[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]|[center|[left|right] <length-percentage>?]&&[center|[top|bottom] <length-percentage>?]]","bg-size":"[<length-percentage>|auto]{1,2}|cover|contain","blur()":"blur( <length> )","blend-mode":"normal|multiply|screen|overlay|darken|lighten|color-dodge|color-burn|hard-light|soft-light|difference|exclusion|hue|saturation|color|luminosity",box:"border-box|padding-box|content-box","brightness()":"brightness( <number-percentage> )","calc()":"calc( <calc-sum> )","calc-sum":"<calc-product> [['+'|'-'] <calc-product>]*","calc-product":"<calc-value> ['*' <calc-value>|'/' <number>]*","calc-value":"<number>|<dimension>|<percentage>|( <calc-sum> )","cf-final-image":"<image>|<color>","cf-mixing-image":"<percentage>?&&<image>","circle()":"circle( [<shape-radius>]? [at <position>]? )","clamp()":"clamp( <calc-sum>#{3} )","class-selector":"'.' <ident-token>","clip-source":"<url>",color:"<rgb()>|<rgba()>|<hsl()>|<hsla()>|<hex-color>|<named-color>|currentcolor|<deprecated-system-color>","color-stop":"<color-stop-length>|<color-stop-angle>","color-stop-angle":"<angle-percentage>{1,2}","color-stop-length":"<length-percentage>{1,2}","color-stop-list":"[<linear-color-stop> [, <linear-color-hint>]?]# , <linear-color-stop>",combinator:"'>'|'+'|'~'|['||']","common-lig-values":"[common-ligatures|no-common-ligatures]",compat:"searchfield|textarea|push-button|button-bevel|slider-horizontal|checkbox|radio|square-button|menulist|menulist-button|listbox|meter|progress-bar","composite-style":"clear|copy|source-over|source-in|source-out|source-atop|destination-over|destination-in|destination-out|destination-atop|xor","compositing-operator":"add|subtract|intersect|exclude","compound-selector":"[<type-selector>? <subclass-selector>* [<pseudo-element-selector> <pseudo-class-selector>*]*]!","compound-selector-list":"<compound-selector>#","complex-selector":"<compound-selector> [<combinator>? <compound-selector>]*","complex-selector-list":"<complex-selector>#","conic-gradient()":"conic-gradient( [from <angle>]? [at <position>]? , <angular-color-stop-list> )","contextual-alt-values":"[contextual|no-contextual]","content-distribution":"space-between|space-around|space-evenly|stretch","content-list":"[<string>|contents|<url>|<quote>|<attr()>|counter( <ident> , <'list-style-type'>? )]+","content-position":"center|start|end|flex-start|flex-end","content-replacement":"<image>","contrast()":"contrast( [<number-percentage>] )","counter()":"counter( <custom-ident> , [<counter-style>|none]? )","counter-style":"<counter-style-name>|symbols( )","counter-style-name":"<custom-ident>","counters()":"counters( <custom-ident> , <string> , [<counter-style>|none]? )","cross-fade()":"cross-fade( <cf-mixing-image> , <cf-final-image>? )","cubic-bezier-timing-function":"ease|ease-in|ease-out|ease-in-out|cubic-bezier( <number> , <number> , <number> , <number> )","deprecated-system-color":"ActiveBorder|ActiveCaption|AppWorkspace|Background|ButtonFace|ButtonHighlight|ButtonShadow|ButtonText|CaptionText|GrayText|Highlight|HighlightText|InactiveBorder|InactiveCaption|InactiveCaptionText|InfoBackground|InfoText|Menu|MenuText|Scrollbar|ThreeDDarkShadow|ThreeDFace|ThreeDHighlight|ThreeDLightShadow|ThreeDShadow|Window|WindowFrame|WindowText","discretionary-lig-values":"[discretionary-ligatures|no-discretionary-ligatures]","display-box":"contents|none","display-inside":"flow|flow-root|table|flex|grid|ruby","display-internal":"table-row-group|table-header-group|table-footer-group|table-row|table-cell|table-column-group|table-column|table-caption|ruby-base|ruby-text|ruby-base-container|ruby-text-container","display-legacy":"inline-block|inline-list-item|inline-table|inline-flex|inline-grid","display-listitem":"<display-outside>?&&[flow|flow-root]?&&list-item","display-outside":"block|inline|run-in","drop-shadow()":"drop-shadow( <length>{2,3} <color>? )","east-asian-variant-values":"[jis78|jis83|jis90|jis04|simplified|traditional]","east-asian-width-values":"[full-width|proportional-width]","element()":"element( <id-selector> )","ellipse()":"ellipse( [<shape-radius>{2}]? [at <position>]? )","ending-shape":"circle|ellipse","env()":"env( <custom-ident> , <declaration-value>? )","explicit-track-list":"[<line-names>? <track-size>]+ <line-names>?","family-name":"<string>|<custom-ident>+","feature-tag-value":"<string> [<integer>|on|off]?","feature-type":"@stylistic|@historical-forms|@styleset|@character-variant|@swash|@ornaments|@annotation","feature-value-block":"<feature-type> '{' <feature-value-declaration-list> '}'","feature-value-block-list":"<feature-value-block>+","feature-value-declaration":"<custom-ident> : <integer>+ ;","feature-value-declaration-list":"<feature-value-declaration>","feature-value-name":"<custom-ident>","fill-rule":"nonzero|evenodd","filter-function":"<blur()>|<brightness()>|<contrast()>|<drop-shadow()>|<grayscale()>|<hue-rotate()>|<invert()>|<opacity()>|<saturate()>|<sepia()>","filter-function-list":"[<filter-function>|<url>]+","final-bg-layer":"<'background-color'>||<bg-image>||<bg-position> [/ <bg-size>]?||<repeat-style>||<attachment>||<box>||<box>","fit-content()":"fit-content( [<length>|<percentage>] )","fixed-breadth":"<length-percentage>","fixed-repeat":"repeat( [<positive-integer>] , [<line-names>? <fixed-size>]+ <line-names>? )","fixed-size":"<fixed-breadth>|minmax( <fixed-breadth> , <track-breadth> )|minmax( <inflexible-breadth> , <fixed-breadth> )","font-stretch-absolute":"normal|ultra-condensed|extra-condensed|condensed|semi-condensed|semi-expanded|expanded|extra-expanded|ultra-expanded|<percentage>","font-variant-css21":"[normal|small-caps]","font-weight-absolute":"normal|bold|<number>","frequency-percentage":"<frequency>|<percentage>","general-enclosed":"[<function-token> <any-value> )]|( <ident> <any-value> )","generic-family":"serif|sans-serif|cursive|fantasy|monospace|-apple-system","generic-name":"serif|sans-serif|cursive|fantasy|monospace","geometry-box":"<shape-box>|fill-box|stroke-box|view-box",gradient:"<linear-gradient()>|<repeating-linear-gradient()>|<radial-gradient()>|<repeating-radial-gradient()>|<conic-gradient()>|<-legacy-gradient>","grayscale()":"grayscale( <number-percentage> )","grid-line":"auto|<custom-ident>|[<integer>&&<custom-ident>?]|[span&&[<integer>||<custom-ident>]]","historical-lig-values":"[historical-ligatures|no-historical-ligatures]","hsl()":"hsl( <hue> <percentage> <percentage> [/ <alpha-value>]? )|hsl( <hue> , <percentage> , <percentage> , <alpha-value>? )","hsla()":"hsla( <hue> <percentage> <percentage> [/ <alpha-value>]? )|hsla( <hue> , <percentage> , <percentage> , <alpha-value>? )",hue:"<number>|<angle>","hue-rotate()":"hue-rotate( <angle> )",image:"<url>|<image()>|<image-set()>|<element()>|<cross-fade()>|<gradient>","image()":"image( <image-tags>? [<image-src>? , <color>?]! )","image-set()":"image-set( <image-set-option># )","image-set-option":"[<image>|<string>] <resolution>","image-src":"<url>|<string>","image-tags":"ltr|rtl","inflexible-breadth":"<length>|<percentage>|min-content|max-content|auto","inset()":"inset( <length-percentage>{1,4} [round <'border-radius'>]? )","invert()":"invert( <number-percentage> )","keyframes-name":"<custom-ident>|<string>","keyframe-block":"<keyframe-selector># { <declaration-list> }","keyframe-block-list":"<keyframe-block>+","keyframe-selector":"from|to|<percentage>","leader()":"leader( <leader-type> )","leader-type":"dotted|solid|space|<string>","length-percentage":"<length>|<percentage>","line-names":"'[' <custom-ident>* ']'","line-name-list":"[<line-names>|<name-repeat>]+","line-style":"none|hidden|dotted|dashed|solid|double|groove|ridge|inset|outset","line-width":"<length>|thin|medium|thick","linear-color-hint":"<length-percentage>","linear-color-stop":"<color> <color-stop-length>?","linear-gradient()":"linear-gradient( [<angle>|to <side-or-corner>]? , <color-stop-list> )","mask-layer":"<mask-reference>||<position> [/ <bg-size>]?||<repeat-style>||<geometry-box>||[<geometry-box>|no-clip]||<compositing-operator>||<masking-mode>","mask-position":"[<length-percentage>|left|center|right] [<length-percentage>|top|center|bottom]?","mask-reference":"none|<image>|<mask-source>","mask-source":"<url>","masking-mode":"alpha|luminance|match-source","matrix()":"matrix( <number>#{6} )","matrix3d()":"matrix3d( <number>#{16} )","max()":"max( <calc-sum># )","media-and":"<media-in-parens> [and <media-in-parens>]+","media-condition":"<media-not>|<media-and>|<media-or>|<media-in-parens>","media-condition-without-or":"<media-not>|<media-and>|<media-in-parens>","media-feature":"( [<mf-plain>|<mf-boolean>|<mf-range>] )","media-in-parens":"( <media-condition> )|<media-feature>|<general-enclosed>","media-not":"not <media-in-parens>","media-or":"<media-in-parens> [or <media-in-parens>]+","media-query":"<media-condition>|[not|only]? <media-type> [and <media-condition-without-or>]?","media-query-list":"<media-query>#","media-type":"<ident>","mf-boolean":"<mf-name>","mf-name":"<ident>","mf-plain":"<mf-name> : <mf-value>","mf-range":"<mf-name> ['<'|'>']? '='? <mf-value>|<mf-value> ['<'|'>']? '='? <mf-name>|<mf-value> '<' '='? <mf-name> '<' '='? <mf-value>|<mf-value> '>' '='? <mf-name> '>' '='? <mf-value>","mf-value":"<number>|<dimension>|<ident>|<ratio>","min()":"min( <calc-sum># )","minmax()":"minmax( [<length>|<percentage>|<flex>|min-content|max-content|auto] , [<length>|<percentage>|<flex>|min-content|max-content|auto] )","named-color":"transparent|aliceblue|antiquewhite|aqua|aquamarine|azure|beige|bisque|black|blanchedalmond|blue|blueviolet|brown|burlywood|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen|fuchsia|gainsboro|ghostwhite|gold|goldenrod|gray|green|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray|lightslategrey|lightsteelblue|lightyellow|lime|limegreen|linen|magenta|maroon|mediumaquamarine|mediumblue|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|navy|oldlace|olive|olivedrab|orange|orangered|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum|powderblue|purple|rebeccapurple|red|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell|sienna|silver|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|teal|thistle|tomato|turquoise|violet|wheat|white|whitesmoke|yellow|yellowgreen|<-non-standard-color>","namespace-prefix":"<ident>","ns-prefix":"[<ident-token>|'*']? '|'","number-percentage":"<number>|<percentage>","numeric-figure-values":"[lining-nums|oldstyle-nums]","numeric-fraction-values":"[diagonal-fractions|stacked-fractions]","numeric-spacing-values":"[proportional-nums|tabular-nums]",nth:"<an-plus-b>|even|odd","opacity()":"opacity( [<number-percentage>] )","overflow-position":"unsafe|safe","outline-radius":"<length>|<percentage>","page-body":"<declaration>? [; <page-body>]?|<page-margin-box> <page-body>","page-margin-box":"<page-margin-box-type> '{' <declaration-list> '}'","page-margin-box-type":"@top-left-corner|@top-left|@top-center|@top-right|@top-right-corner|@bottom-left-corner|@bottom-left|@bottom-center|@bottom-right|@bottom-right-corner|@left-top|@left-middle|@left-bottom|@right-top|@right-middle|@right-bottom","page-selector-list":"[<page-selector>#]?","page-selector":"<pseudo-page>+|<ident> <pseudo-page>*","perspective()":"perspective( <length> )","polygon()":"polygon( <fill-rule>? , [<length-percentage> <length-percentage>]# )",position:"[[left|center|right]||[top|center|bottom]|[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]?|[[left|right] <length-percentage>]&&[[top|bottom] <length-percentage>]]","pseudo-class-selector":"':' <ident-token>|':' <function-token> <any-value> ')'","pseudo-element-selector":"':' <pseudo-class-selector>","pseudo-page":": [left|right|first|blank]",quote:"open-quote|close-quote|no-open-quote|no-close-quote","radial-gradient()":"radial-gradient( [<ending-shape>||<size>]? [at <position>]? , <color-stop-list> )","relative-selector":"<combinator>? <complex-selector>","relative-selector-list":"<relative-selector>#","relative-size":"larger|smaller","repeat-style":"repeat-x|repeat-y|[repeat|space|round|no-repeat]{1,2}","repeating-linear-gradient()":"repeating-linear-gradient( [<angle>|to <side-or-corner>]? , <color-stop-list> )","repeating-radial-gradient()":"repeating-radial-gradient( [<ending-shape>||<size>]? [at <position>]? , <color-stop-list> )","rgb()":"rgb( <percentage>{3} [/ <alpha-value>]? )|rgb( <number>{3} [/ <alpha-value>]? )|rgb( <percentage>#{3} , <alpha-value>? )|rgb( <number>#{3} , <alpha-value>? )","rgba()":"rgba( <percentage>{3} [/ <alpha-value>]? )|rgba( <number>{3} [/ <alpha-value>]? )|rgba( <percentage>#{3} , <alpha-value>? )|rgba( <number>#{3} , <alpha-value>? )","rotate()":"rotate( [<angle>|<zero>] )","rotate3d()":"rotate3d( <number> , <number> , <number> , [<angle>|<zero>] )","rotateX()":"rotateX( [<angle>|<zero>] )","rotateY()":"rotateY( [<angle>|<zero>] )","rotateZ()":"rotateZ( [<angle>|<zero>] )","saturate()":"saturate( <number-percentage> )","scale()":"scale( <number> , <number>? )","scale3d()":"scale3d( <number> , <number> , <number> )","scaleX()":"scaleX( <number> )","scaleY()":"scaleY( <number> )","scaleZ()":"scaleZ( <number> )","self-position":"center|start|end|self-start|self-end|flex-start|flex-end","shape-radius":"<length-percentage>|closest-side|farthest-side","skew()":"skew( [<angle>|<zero>] , [<angle>|<zero>]? )","skewX()":"skewX( [<angle>|<zero>] )","skewY()":"skewY( [<angle>|<zero>] )","sepia()":"sepia( <number-percentage> )",shadow:"inset?&&<length>{2,4}&&<color>?","shadow-t":"[<length>{2,3}&&<color>?]",shape:"rect( <top> , <right> , <bottom> , <left> )|rect( <top> <right> <bottom> <left> )","shape-box":"<box>|margin-box","side-or-corner":"[left|right]||[top|bottom]","single-animation":"<time>||<timing-function>||<time>||<single-animation-iteration-count>||<single-animation-direction>||<single-animation-fill-mode>||<single-animation-play-state>||[none|<keyframes-name>]","single-animation-direction":"normal|reverse|alternate|alternate-reverse","single-animation-fill-mode":"none|forwards|backwards|both","single-animation-iteration-count":"infinite|<number>","single-animation-play-state":"running|paused","single-transition":"[none|<single-transition-property>]||<time>||<timing-function>||<time>","single-transition-property":"all|<custom-ident>",size:"closest-side|farthest-side|closest-corner|farthest-corner|<length>|<length-percentage>{2}","step-position":"jump-start|jump-end|jump-none|jump-both|start|end","step-timing-function":"step-start|step-end|steps( <integer> [, <step-position>]? )","subclass-selector":"<id-selector>|<class-selector>|<attribute-selector>|<pseudo-class-selector>","supports-condition":"not <supports-in-parens>|<supports-in-parens> [and <supports-in-parens>]*|<supports-in-parens> [or <supports-in-parens>]*","supports-in-parens":"( <supports-condition> )|<supports-feature>|<general-enclosed>","supports-feature":"<supports-decl>|<supports-selector-fn>","supports-decl":"( <declaration> )","supports-selector-fn":"selector( <complex-selector> )",symbol:"<string>|<image>|<custom-ident>",target:"<target-counter()>|<target-counters()>|<target-text()>","target-counter()":"target-counter( [<string>|<url>] , <custom-ident> , <counter-style>? )","target-counters()":"target-counters( [<string>|<url>] , <custom-ident> , <string> , <counter-style>? )","target-text()":"target-text( [<string>|<url>] , [content|before|after|first-letter]? )","time-percentage":"<time>|<percentage>","timing-function":"linear|<cubic-bezier-timing-function>|<step-timing-function>","track-breadth":"<length-percentage>|<flex>|min-content|max-content|auto","track-list":"[<line-names>? [<track-size>|<track-repeat>]]+ <line-names>?","track-repeat":"repeat( [<positive-integer>] , [<line-names>? <track-size>]+ <line-names>? )","track-size":"<track-breadth>|minmax( <inflexible-breadth> , <track-breadth> )|fit-content( [<length>|<percentage>] )","transform-function":"<matrix()>|<translate()>|<translateX()>|<translateY()>|<scale()>|<scaleX()>|<scaleY()>|<rotate()>|<skew()>|<skewX()>|<skewY()>|<matrix3d()>|<translate3d()>|<translateZ()>|<scale3d()>|<scaleZ()>|<rotate3d()>|<rotateX()>|<rotateY()>|<rotateZ()>|<perspective()>","transform-list":"<transform-function>+","translate()":"translate( <length-percentage> , <length-percentage>? )","translate3d()":"translate3d( <length-percentage> , <length-percentage> , <length> )","translateX()":"translateX( <length-percentage> )","translateY()":"translateY( <length-percentage> )","translateZ()":"translateZ( <length> )","type-or-unit":"string|color|url|integer|number|length|angle|time|frequency|cap|ch|em|ex|ic|lh|rlh|rem|vb|vi|vw|vh|vmin|vmax|mm|Q|cm|in|pt|pc|px|deg|grad|rad|turn|ms|s|Hz|kHz|%","type-selector":"<wq-name>|<ns-prefix>? '*'","var()":"var( <custom-property-name> , <declaration-value>? )","viewport-length":"auto|<length-percentage>","wq-name":"<ns-prefix>? <ident-token>","-legacy-gradient":"<-webkit-gradient()>|<-legacy-linear-gradient>|<-legacy-repeating-linear-gradient>|<-legacy-radial-gradient>|<-legacy-repeating-radial-gradient>","-legacy-linear-gradient":"-moz-linear-gradient( <-legacy-linear-gradient-arguments> )|-webkit-linear-gradient( <-legacy-linear-gradient-arguments> )|-o-linear-gradient( <-legacy-linear-gradient-arguments> )","-legacy-repeating-linear-gradient":"-moz-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )|-webkit-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )|-o-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )","-legacy-linear-gradient-arguments":"[<angle>|<side-or-corner>]? , <color-stop-list>","-legacy-radial-gradient":"-moz-radial-gradient( <-legacy-radial-gradient-arguments> )|-webkit-radial-gradient( <-legacy-radial-gradient-arguments> )|-o-radial-gradient( <-legacy-radial-gradient-arguments> )","-legacy-repeating-radial-gradient":"-moz-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )|-webkit-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )|-o-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )","-legacy-radial-gradient-arguments":"[<position> ,]? [[[<-legacy-radial-gradient-shape>||<-legacy-radial-gradient-size>]|[<length>|<percentage>]{2}] ,]? <color-stop-list>","-legacy-radial-gradient-size":"closest-side|closest-corner|farthest-side|farthest-corner|contain|cover","-legacy-radial-gradient-shape":"circle|ellipse","-non-standard-font":"-apple-system-body|-apple-system-headline|-apple-system-subheadline|-apple-system-caption1|-apple-system-caption2|-apple-system-footnote|-apple-system-short-body|-apple-system-short-headline|-apple-system-short-subheadline|-apple-system-short-caption1|-apple-system-short-footnote|-apple-system-tall-body","-non-standard-color":"-moz-ButtonDefault|-moz-ButtonHoverFace|-moz-ButtonHoverText|-moz-CellHighlight|-moz-CellHighlightText|-moz-Combobox|-moz-ComboboxText|-moz-Dialog|-moz-DialogText|-moz-dragtargetzone|-moz-EvenTreeRow|-moz-Field|-moz-FieldText|-moz-html-CellHighlight|-moz-html-CellHighlightText|-moz-mac-accentdarkestshadow|-moz-mac-accentdarkshadow|-moz-mac-accentface|-moz-mac-accentlightesthighlight|-moz-mac-accentlightshadow|-moz-mac-accentregularhighlight|-moz-mac-accentregularshadow|-moz-mac-chrome-active|-moz-mac-chrome-inactive|-moz-mac-focusring|-moz-mac-menuselect|-moz-mac-menushadow|-moz-mac-menutextselect|-moz-MenuHover|-moz-MenuHoverText|-moz-MenuBarText|-moz-MenuBarHoverText|-moz-nativehyperlinktext|-moz-OddTreeRow|-moz-win-communicationstext|-moz-win-mediatext|-moz-activehyperlinktext|-moz-default-background-color|-moz-default-color|-moz-hyperlinktext|-moz-visitedhyperlinktext|-webkit-activelink|-webkit-focus-ring-color|-webkit-link|-webkit-text","-non-standard-image-rendering":"optimize-contrast|-moz-crisp-edges|-o-crisp-edges|-webkit-optimize-contrast","-non-standard-overflow":"-moz-scrollbars-none|-moz-scrollbars-horizontal|-moz-scrollbars-vertical|-moz-hidden-unscrollable","-non-standard-width":"min-intrinsic|intrinsic|-moz-min-content|-moz-max-content|-webkit-min-content|-webkit-max-content","-webkit-gradient()":"-webkit-gradient( <-webkit-gradient-type> , <-webkit-gradient-point> [, <-webkit-gradient-point>|, <-webkit-gradient-radius> , <-webkit-gradient-point>] [, <-webkit-gradient-radius>]? [, <-webkit-gradient-color-stop>]* )","-webkit-gradient-color-stop":"from( <color> )|color-stop( [<number-zero-one>|<percentage>] , <color> )|to( <color> )","-webkit-gradient-point":"[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]","-webkit-gradient-radius":"<length>|<percentage>","-webkit-gradient-type":"linear|radial","-webkit-mask-box-repeat":"repeat|stretch|round","-webkit-mask-clip-style":"border|border-box|padding|padding-box|content|content-box|text","-ms-filter-function-list":"<-ms-filter-function>+","-ms-filter-function":"<-ms-filter-function-progid>|<-ms-filter-function-legacy>","-ms-filter-function-progid":"'progid:' [<ident-token> '.']* [<ident-token>|<function-token> <any-value>? )]","-ms-filter-function-legacy":"<ident-token>|<function-token> <any-value>? )","-ms-filter":"<string>",age:"child|young|old","attr-name":"<wq-name>","attr-fallback":"<any-value>","border-radius":"<length-percentage>{1,2}",bottom:"<length>|auto","generic-voice":"[<age>? <gender> <integer>?]",gender:"male|female|neutral",left:"<length>|auto","mask-image":"<mask-reference>#","name-repeat":"repeat( [<positive-integer>|auto-fill] , <line-names>+ )",paint:"none|<color>|<url> [none|<color>]?|context-fill|context-stroke","path()":"path( <string> )",ratio:"<integer> / <integer>",right:"<length>|auto","svg-length":"<percentage>|<length>|<number>","svg-writing-mode":"lr-tb|rl-tb|tb-rl|lr|rl|tb",top:"<length>|auto","track-group":"'(' [<string>* <track-minmax> <string>*]+ ')' ['[' <positive-integer> ']']?|<track-minmax>","track-list-v0":"[<string>* <track-group> <string>*]+|none","track-minmax":"minmax( <track-breadth> , <track-breadth> )|auto|<track-breadth>|fit-content",x:"<number>",y:"<number>",declaration:"<ident-token> : <declaration-value>? ['!' important]?","declaration-list":"[<declaration>? ';']* <declaration>?",url:"url( <string> <url-modifier>* )|<url-token>","url-modifier":"<ident>|<function-token> <any-value> )","number-zero-one":"<number [0,1]>","number-one-or-greater":"<number [1,∞]>","positive-integer":"<integer [0,∞]>"},Or={"--*":"<declaration-value>","-ms-accelerator":"false|true","-ms-block-progression":"tb|rl|bt|lr","-ms-content-zoom-chaining":"none|chained","-ms-content-zooming":"none|zoom","-ms-content-zoom-limit":"<'-ms-content-zoom-limit-min'> <'-ms-content-zoom-limit-max'>","-ms-content-zoom-limit-max":"<percentage>","-ms-content-zoom-limit-min":"<percentage>","-ms-content-zoom-snap":"<'-ms-content-zoom-snap-type'>||<'-ms-content-zoom-snap-points'>","-ms-content-zoom-snap-points":"snapInterval( <percentage> , <percentage> )|snapList( <percentage># )","-ms-content-zoom-snap-type":"none|proximity|mandatory","-ms-filter":"<string>","-ms-flow-from":"[none|<custom-ident>]#","-ms-flow-into":"[none|<custom-ident>]#","-ms-high-contrast-adjust":"auto|none","-ms-hyphenate-limit-chars":"auto|<integer>{1,3}","-ms-hyphenate-limit-lines":"no-limit|<integer>","-ms-hyphenate-limit-zone":"<percentage>|<length>","-ms-ime-align":"auto|after","-ms-overflow-style":"auto|none|scrollbar|-ms-autohiding-scrollbar","-ms-scrollbar-3dlight-color":"<color>","-ms-scrollbar-arrow-color":"<color>","-ms-scrollbar-base-color":"<color>","-ms-scrollbar-darkshadow-color":"<color>","-ms-scrollbar-face-color":"<color>","-ms-scrollbar-highlight-color":"<color>","-ms-scrollbar-shadow-color":"<color>","-ms-scrollbar-track-color":"<color>","-ms-scroll-chaining":"chained|none","-ms-scroll-limit":"<'-ms-scroll-limit-x-min'> <'-ms-scroll-limit-y-min'> <'-ms-scroll-limit-x-max'> <'-ms-scroll-limit-y-max'>","-ms-scroll-limit-x-max":"auto|<length>","-ms-scroll-limit-x-min":"<length>","-ms-scroll-limit-y-max":"auto|<length>","-ms-scroll-limit-y-min":"<length>","-ms-scroll-rails":"none|railed","-ms-scroll-snap-points-x":"snapInterval( <length-percentage> , <length-percentage> )|snapList( <length-percentage># )","-ms-scroll-snap-points-y":"snapInterval( <length-percentage> , <length-percentage> )|snapList( <length-percentage># )","-ms-scroll-snap-type":"none|proximity|mandatory","-ms-scroll-snap-x":"<'-ms-scroll-snap-type'> <'-ms-scroll-snap-points-x'>","-ms-scroll-snap-y":"<'-ms-scroll-snap-type'> <'-ms-scroll-snap-points-y'>","-ms-scroll-translation":"none|vertical-to-horizontal","-ms-text-autospace":"none|ideograph-alpha|ideograph-numeric|ideograph-parenthesis|ideograph-space","-ms-touch-select":"grippers|none","-ms-user-select":"none|element|text","-ms-wrap-flow":"auto|both|start|end|maximum|clear","-ms-wrap-margin":"<length>","-ms-wrap-through":"wrap|none","-moz-appearance":"none|button|button-arrow-down|button-arrow-next|button-arrow-previous|button-arrow-up|button-bevel|button-focus|caret|checkbox|checkbox-container|checkbox-label|checkmenuitem|dualbutton|groupbox|listbox|listitem|menuarrow|menubar|menucheckbox|menuimage|menuitem|menuitemtext|menulist|menulist-button|menulist-text|menulist-textfield|menupopup|menuradio|menuseparator|meterbar|meterchunk|progressbar|progressbar-vertical|progresschunk|progresschunk-vertical|radio|radio-container|radio-label|radiomenuitem|range|range-thumb|resizer|resizerpanel|scale-horizontal|scalethumbend|scalethumb-horizontal|scalethumbstart|scalethumbtick|scalethumb-vertical|scale-vertical|scrollbarbutton-down|scrollbarbutton-left|scrollbarbutton-right|scrollbarbutton-up|scrollbarthumb-horizontal|scrollbarthumb-vertical|scrollbartrack-horizontal|scrollbartrack-vertical|searchfield|separator|sheet|spinner|spinner-downbutton|spinner-textfield|spinner-upbutton|splitter|statusbar|statusbarpanel|tab|tabpanel|tabpanels|tab-scroll-arrow-back|tab-scroll-arrow-forward|textfield|textfield-multiline|toolbar|toolbarbutton|toolbarbutton-dropdown|toolbargripper|toolbox|tooltip|treeheader|treeheadercell|treeheadersortarrow|treeitem|treeline|treetwisty|treetwistyopen|treeview|-moz-mac-unified-toolbar|-moz-win-borderless-glass|-moz-win-browsertabbar-toolbox|-moz-win-communicationstext|-moz-win-communications-toolbox|-moz-win-exclude-glass|-moz-win-glass|-moz-win-mediatext|-moz-win-media-toolbox|-moz-window-button-box|-moz-window-button-box-maximized|-moz-window-button-close|-moz-window-button-maximize|-moz-window-button-minimize|-moz-window-button-restore|-moz-window-frame-bottom|-moz-window-frame-left|-moz-window-frame-right|-moz-window-titlebar|-moz-window-titlebar-maximized","-moz-binding":"<url>|none","-moz-border-bottom-colors":"<color>+|none","-moz-border-left-colors":"<color>+|none","-moz-border-right-colors":"<color>+|none","-moz-border-top-colors":"<color>+|none","-moz-context-properties":"none|[fill|fill-opacity|stroke|stroke-opacity]#","-moz-float-edge":"border-box|content-box|margin-box|padding-box","-moz-force-broken-image-icon":"<integer>","-moz-image-region":"<shape>|auto","-moz-orient":"inline|block|horizontal|vertical","-moz-outline-radius":"<outline-radius>{1,4} [/ <outline-radius>{1,4}]?","-moz-outline-radius-bottomleft":"<outline-radius>","-moz-outline-radius-bottomright":"<outline-radius>","-moz-outline-radius-topleft":"<outline-radius>","-moz-outline-radius-topright":"<outline-radius>","-moz-stack-sizing":"ignore|stretch-to-fit","-moz-text-blink":"none|blink","-moz-user-focus":"ignore|normal|select-after|select-before|select-menu|select-same|select-all|none","-moz-user-input":"auto|none|enabled|disabled","-moz-user-modify":"read-only|read-write|write-only","-moz-window-dragging":"drag|no-drag","-moz-window-shadow":"default|menu|tooltip|sheet|none","-webkit-appearance":"none|button|button-bevel|caps-lock-indicator|caret|checkbox|default-button|listbox|listitem|media-fullscreen-button|media-mute-button|media-play-button|media-seek-back-button|media-seek-forward-button|media-slider|media-sliderthumb|menulist|menulist-button|menulist-text|menulist-textfield|push-button|radio|scrollbarbutton-down|scrollbarbutton-left|scrollbarbutton-right|scrollbarbutton-up|scrollbargripper-horizontal|scrollbargripper-vertical|scrollbarthumb-horizontal|scrollbarthumb-vertical|scrollbartrack-horizontal|scrollbartrack-vertical|searchfield|searchfield-cancel-button|searchfield-decoration|searchfield-results-button|searchfield-results-decoration|slider-horizontal|slider-vertical|sliderthumb-horizontal|sliderthumb-vertical|square-button|textarea|textfield","-webkit-border-before":"<'border-width'>||<'border-style'>||<'color'>","-webkit-border-before-color":"<'color'>","-webkit-border-before-style":"<'border-style'>","-webkit-border-before-width":"<'border-width'>","-webkit-box-reflect":"[above|below|right|left]? <length>? <image>?","-webkit-line-clamp":"none|<integer>","-webkit-mask":"[<mask-reference>||<position> [/ <bg-size>]?||<repeat-style>||[<box>|border|padding|content|text]||[<box>|border|padding|content]]#","-webkit-mask-attachment":"<attachment>#","-webkit-mask-clip":"[<box>|border|padding|content|text]#","-webkit-mask-composite":"<composite-style>#","-webkit-mask-image":"<mask-reference>#","-webkit-mask-origin":"[<box>|border|padding|content]#","-webkit-mask-position":"<position>#","-webkit-mask-position-x":"[<length-percentage>|left|center|right]#","-webkit-mask-position-y":"[<length-percentage>|top|center|bottom]#","-webkit-mask-repeat":"<repeat-style>#","-webkit-mask-repeat-x":"repeat|no-repeat|space|round","-webkit-mask-repeat-y":"repeat|no-repeat|space|round","-webkit-mask-size":"<bg-size>#","-webkit-overflow-scrolling":"auto|touch","-webkit-tap-highlight-color":"<color>","-webkit-text-fill-color":"<color>","-webkit-text-stroke":"<length>||<color>","-webkit-text-stroke-color":"<color>","-webkit-text-stroke-width":"<length>","-webkit-touch-callout":"default|none","-webkit-user-modify":"read-only|read-write|read-write-plaintext-only","align-content":"normal|<baseline-position>|<content-distribution>|<overflow-position>? <content-position>","align-items":"normal|stretch|<baseline-position>|[<overflow-position>? <self-position>]","align-self":"auto|normal|stretch|<baseline-position>|<overflow-position>? <self-position>",all:"initial|inherit|unset|revert",animation:"<single-animation>#","animation-delay":"<time>#","animation-direction":"<single-animation-direction>#","animation-duration":"<time>#","animation-fill-mode":"<single-animation-fill-mode>#","animation-iteration-count":"<single-animation-iteration-count>#","animation-name":"[none|<keyframes-name>]#","animation-play-state":"<single-animation-play-state>#","animation-timing-function":"<timing-function>#",appearance:"none|auto|button|textfield|<compat>",azimuth:"<angle>|[[left-side|far-left|left|center-left|center|center-right|right|far-right|right-side]||behind]|leftwards|rightwards","backdrop-filter":"none|<filter-function-list>","backface-visibility":"visible|hidden",background:"[<bg-layer> ,]* <final-bg-layer>","background-attachment":"<attachment>#","background-blend-mode":"<blend-mode>#","background-clip":"<box>#","background-color":"<color>","background-image":"<bg-image>#","background-origin":"<box>#","background-position":"<bg-position>#","background-position-x":"[center|[left|right|x-start|x-end]? <length-percentage>?]#","background-position-y":"[center|[top|bottom|y-start|y-end]? <length-percentage>?]#","background-repeat":"<repeat-style>#","background-size":"<bg-size>#","block-overflow":"clip|ellipsis|<string>","block-size":"<'width'>",border:"<line-width>||<line-style>||<color>","border-block":"<'border-top-width'>||<'border-top-style'>||<'color'>","border-block-color":"<'border-top-color'>{1,2}","border-block-style":"<'border-top-style'>","border-block-width":"<'border-top-width'>","border-block-end":"<'border-top-width'>||<'border-top-style'>||<'color'>","border-block-end-color":"<'border-top-color'>","border-block-end-style":"<'border-top-style'>","border-block-end-width":"<'border-top-width'>","border-block-start":"<'border-top-width'>||<'border-top-style'>||<'color'>","border-block-start-color":"<'border-top-color'>","border-block-start-style":"<'border-top-style'>","border-block-start-width":"<'border-top-width'>","border-bottom":"<line-width>||<line-style>||<color>","border-bottom-color":"<'border-top-color'>","border-bottom-left-radius":"<length-percentage>{1,2}","border-bottom-right-radius":"<length-percentage>{1,2}","border-bottom-style":"<line-style>","border-bottom-width":"<line-width>","border-collapse":"collapse|separate","border-color":"<color>{1,4}","border-end-end-radius":"<length-percentage>{1,2}","border-end-start-radius":"<length-percentage>{1,2}","border-image":"<'border-image-source'>||<'border-image-slice'> [/ <'border-image-width'>|/ <'border-image-width'>? / <'border-image-outset'>]?||<'border-image-repeat'>","border-image-outset":"[<length>|<number>]{1,4}","border-image-repeat":"[stretch|repeat|round|space]{1,2}","border-image-slice":"<number-percentage>{1,4}&&fill?","border-image-source":"none|<image>","border-image-width":"[<length-percentage>|<number>|auto]{1,4}","border-inline":"<'border-top-width'>||<'border-top-style'>||<'color'>","border-inline-end":"<'border-top-width'>||<'border-top-style'>||<'color'>","border-inline-color":"<'border-top-color'>{1,2}","border-inline-style":"<'border-top-style'>","border-inline-width":"<'border-top-width'>","border-inline-end-color":"<'border-top-color'>","border-inline-end-style":"<'border-top-style'>","border-inline-end-width":"<'border-top-width'>","border-inline-start":"<'border-top-width'>||<'border-top-style'>||<'color'>","border-inline-start-color":"<'border-top-color'>","border-inline-start-style":"<'border-top-style'>","border-inline-start-width":"<'border-top-width'>","border-left":"<line-width>||<line-style>||<color>","border-left-color":"<color>","border-left-style":"<line-style>","border-left-width":"<line-width>","border-radius":"<length-percentage>{1,4} [/ <length-percentage>{1,4}]?","border-right":"<line-width>||<line-style>||<color>","border-right-color":"<color>","border-right-style":"<line-style>","border-right-width":"<line-width>","border-spacing":"<length> <length>?","border-start-end-radius":"<length-percentage>{1,2}","border-start-start-radius":"<length-percentage>{1,2}","border-style":"<line-style>{1,4}","border-top":"<line-width>||<line-style>||<color>","border-top-color":"<color>","border-top-left-radius":"<length-percentage>{1,2}","border-top-right-radius":"<length-percentage>{1,2}","border-top-style":"<line-style>","border-top-width":"<line-width>","border-width":"<line-width>{1,4}",bottom:"<length>|<percentage>|auto","box-align":"start|center|end|baseline|stretch","box-decoration-break":"slice|clone","box-direction":"normal|reverse|inherit","box-flex":"<number>","box-flex-group":"<integer>","box-lines":"single|multiple","box-ordinal-group":"<integer>","box-orient":"horizontal|vertical|inline-axis|block-axis|inherit","box-pack":"start|center|end|justify","box-shadow":"none|<shadow>#","box-sizing":"content-box|border-box","break-after":"auto|avoid|always|all|avoid-page|page|left|right|recto|verso|avoid-column|column|avoid-region|region","break-before":"auto|avoid|always|all|avoid-page|page|left|right|recto|verso|avoid-column|column|avoid-region|region","break-inside":"auto|avoid|avoid-page|avoid-column|avoid-region","caption-side":"top|bottom|block-start|block-end|inline-start|inline-end","caret-color":"auto|<color>",clear:"none|left|right|both|inline-start|inline-end",clip:"<shape>|auto","clip-path":"<clip-source>|[<basic-shape>||<geometry-box>]|none",color:"<color>","color-adjust":"economy|exact","column-count":"<integer>|auto","column-fill":"auto|balance|balance-all","column-gap":"normal|<length-percentage>","column-rule":"<'column-rule-width'>||<'column-rule-style'>||<'column-rule-color'>","column-rule-color":"<color>","column-rule-style":"<'border-style'>","column-rule-width":"<'border-width'>","column-span":"none|all","column-width":"<length>|auto",columns:"<'column-width'>||<'column-count'>",contain:"none|strict|content|[size||layout||style||paint]",content:"normal|none|[<content-replacement>|<content-list>] [/ <string>]?","counter-increment":"[<custom-ident> <integer>?]+|none","counter-reset":"[<custom-ident> <integer>?]+|none","counter-set":"[<custom-ident> <integer>?]+|none",cursor:"[[<url> [<x> <y>]? ,]* [auto|default|none|context-menu|help|pointer|progress|wait|cell|crosshair|text|vertical-text|alias|copy|move|no-drop|not-allowed|e-resize|n-resize|ne-resize|nw-resize|s-resize|se-resize|sw-resize|w-resize|ew-resize|ns-resize|nesw-resize|nwse-resize|col-resize|row-resize|all-scroll|zoom-in|zoom-out|grab|grabbing|hand|-webkit-grab|-webkit-grabbing|-webkit-zoom-in|-webkit-zoom-out|-moz-grab|-moz-grabbing|-moz-zoom-in|-moz-zoom-out]]",direction:"ltr|rtl",display:"block|contents|flex|flow|flow-root|grid|inline|inline-block|inline-flex|inline-grid|inline-list-item|inline-table|list-item|none|ruby|ruby-base|ruby-base-container|ruby-text|ruby-text-container|run-in|table|table-caption|table-cell|table-column|table-column-group|table-footer-group|table-header-group|table-row|table-row-group|-ms-flexbox|-ms-inline-flexbox|-ms-grid|-ms-inline-grid|-webkit-flex|-webkit-inline-flex|-webkit-box|-webkit-inline-box|-moz-inline-stack|-moz-box|-moz-inline-box","empty-cells":"show|hide",filter:"none|<filter-function-list>|<-ms-filter-function-list>",flex:"none|[<'flex-grow'> <'flex-shrink'>?||<'flex-basis'>]","flex-basis":"content|<'width'>","flex-direction":"row|row-reverse|column|column-reverse","flex-flow":"<'flex-direction'>||<'flex-wrap'>","flex-grow":"<number>","flex-shrink":"<number>","flex-wrap":"nowrap|wrap|wrap-reverse",float:"left|right|none|inline-start|inline-end",font:"[[<'font-style'>||<font-variant-css21>||<'font-weight'>||<'font-stretch'>]? <'font-size'> [/ <'line-height'>]? <'font-family'>]|caption|icon|menu|message-box|small-caption|status-bar","font-family":"[<family-name>|<generic-family>]#","font-feature-settings":"normal|<feature-tag-value>#","font-kerning":"auto|normal|none","font-language-override":"normal|<string>","font-optical-sizing":"auto|none","font-variation-settings":"normal|[<string> <number>]#","font-size":"<absolute-size>|<relative-size>|<length-percentage>","font-size-adjust":"none|<number>","font-stretch":"<font-stretch-absolute>","font-style":"normal|italic|oblique <angle>?","font-synthesis":"none|[weight||style]","font-variant":"normal|none|[<common-lig-values>||<discretionary-lig-values>||<historical-lig-values>||<contextual-alt-values>||stylistic( <feature-value-name> )||historical-forms||styleset( <feature-value-name># )||character-variant( <feature-value-name># )||swash( <feature-value-name> )||ornaments( <feature-value-name> )||annotation( <feature-value-name> )||[small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps]||<numeric-figure-values>||<numeric-spacing-values>||<numeric-fraction-values>||ordinal||slashed-zero||<east-asian-variant-values>||<east-asian-width-values>||ruby]","font-variant-alternates":"normal|[stylistic( <feature-value-name> )||historical-forms||styleset( <feature-value-name># )||character-variant( <feature-value-name># )||swash( <feature-value-name> )||ornaments( <feature-value-name> )||annotation( <feature-value-name> )]","font-variant-caps":"normal|small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps","font-variant-east-asian":"normal|[<east-asian-variant-values>||<east-asian-width-values>||ruby]","font-variant-ligatures":"normal|none|[<common-lig-values>||<discretionary-lig-values>||<historical-lig-values>||<contextual-alt-values>]","font-variant-numeric":"normal|[<numeric-figure-values>||<numeric-spacing-values>||<numeric-fraction-values>||ordinal||slashed-zero]","font-variant-position":"normal|sub|super","font-weight":"<font-weight-absolute>|bolder|lighter",gap:"<'row-gap'> <'column-gap'>?",grid:"<'grid-template'>|<'grid-template-rows'> / [auto-flow&&dense?] <'grid-auto-columns'>?|[auto-flow&&dense?] <'grid-auto-rows'>? / <'grid-template-columns'>","grid-area":"<grid-line> [/ <grid-line>]{0,3}","grid-auto-columns":"<track-size>+","grid-auto-flow":"[row|column]||dense","grid-auto-rows":"<track-size>+","grid-column":"<grid-line> [/ <grid-line>]?","grid-column-end":"<grid-line>","grid-column-gap":"<length-percentage>","grid-column-start":"<grid-line>","grid-gap":"<'grid-row-gap'> <'grid-column-gap'>?","grid-row":"<grid-line> [/ <grid-line>]?","grid-row-end":"<grid-line>","grid-row-gap":"<length-percentage>","grid-row-start":"<grid-line>","grid-template":"none|[<'grid-template-rows'> / <'grid-template-columns'>]|[<line-names>? <string> <track-size>? <line-names>?]+ [/ <explicit-track-list>]?","grid-template-areas":"none|<string>+","grid-template-columns":"none|<track-list>|<auto-track-list>","grid-template-rows":"none|<track-list>|<auto-track-list>","hanging-punctuation":"none|[first||[force-end|allow-end]||last]",height:"[<length>|<percentage>]&&[border-box|content-box]?|available|min-content|max-content|fit-content|auto",hyphens:"none|manual|auto","image-orientation":"from-image|<angle>|[<angle>? flip]","image-rendering":"auto|crisp-edges|pixelated|optimizeSpeed|optimizeQuality|<-non-standard-image-rendering>","image-resolution":"[from-image||<resolution>]&&snap?","ime-mode":"auto|normal|active|inactive|disabled","initial-letter":"normal|[<number> <integer>?]","initial-letter-align":"[auto|alphabetic|hanging|ideographic]","inline-size":"<'width'>",inset:"<'top'>{1,4}","inset-block":"<'top'>{1,2}","inset-block-end":"<'top'>","inset-block-start":"<'top'>","inset-inline":"<'top'>{1,2}","inset-inline-end":"<'top'>","inset-inline-start":"<'top'>",isolation:"auto|isolate","justify-content":"normal|<content-distribution>|<overflow-position>? [<content-position>|left|right]","justify-items":"normal|stretch|<baseline-position>|<overflow-position>? [<self-position>|left|right]|legacy|legacy&&[left|right|center]","justify-self":"auto|normal|stretch|<baseline-position>|<overflow-position>? [<self-position>|left|right]",left:"<length>|<percentage>|auto","letter-spacing":"normal|<length-percentage>","line-break":"auto|loose|normal|strict","line-clamp":"none|<integer>","line-height":"normal|<number>|<length>|<percentage>","line-height-step":"<length>","list-style":"<'list-style-type'>||<'list-style-position'>||<'list-style-image'>","list-style-image":"<url>|none","list-style-position":"inside|outside","list-style-type":"<counter-style>|<string>|none",margin:"[<length>|<percentage>|auto]{1,4}","margin-block":"<'margin-left'>{1,2}","margin-block-end":"<'margin-left'>","margin-block-start":"<'margin-left'>","margin-bottom":"<length>|<percentage>|auto","margin-inline":"<'margin-left'>{1,2}","margin-inline-end":"<'margin-left'>","margin-inline-start":"<'margin-left'>","margin-left":"<length>|<percentage>|auto","margin-right":"<length>|<percentage>|auto","margin-top":"<length>|<percentage>|auto",mask:"<mask-layer>#","mask-border":"<'mask-border-source'>||<'mask-border-slice'> [/ <'mask-border-width'>? [/ <'mask-border-outset'>]?]?||<'mask-border-repeat'>||<'mask-border-mode'>","mask-border-mode":"luminance|alpha","mask-border-outset":"[<length>|<number>]{1,4}","mask-border-repeat":"[stretch|repeat|round|space]{1,2}","mask-border-slice":"<number-percentage>{1,4} fill?","mask-border-source":"none|<image>","mask-border-width":"[<length-percentage>|<number>|auto]{1,4}","mask-clip":"[<geometry-box>|no-clip]#","mask-composite":"<compositing-operator>#","mask-image":"<mask-reference>#","mask-mode":"<masking-mode>#","mask-origin":"<geometry-box>#","mask-position":"<position>#","mask-repeat":"<repeat-style>#","mask-size":"<bg-size>#","mask-type":"luminance|alpha","max-block-size":"<'max-width'>","max-height":"<length>|<percentage>|none|max-content|min-content|fit-content|fill-available","max-inline-size":"<'max-width'>","max-lines":"none|<integer>","max-width":"<length>|<percentage>|none|max-content|min-content|fit-content|fill-available|<-non-standard-width>","min-block-size":"<'min-width'>","min-height":"<length>|<percentage>|auto|max-content|min-content|fit-content|fill-available","min-inline-size":"<'min-width'>","min-width":"<length>|<percentage>|auto|max-content|min-content|fit-content|fill-available|<-non-standard-width>","mix-blend-mode":"<blend-mode>","object-fit":"fill|contain|cover|none|scale-down","object-position":"<position>",offset:"[<'offset-position'>? [<'offset-path'> [<'offset-distance'>||<'offset-rotate'>]?]?]! [/ <'offset-anchor'>]?","offset-anchor":"auto|<position>","offset-distance":"<length-percentage>","offset-path":"none|ray( [<angle>&&<size>?&&contain?] )|<path()>|<url>|[<basic-shape>||<geometry-box>]","offset-position":"auto|<position>","offset-rotate":"[auto|reverse]||<angle>",opacity:"<number-zero-one>",order:"<integer>",orphans:"<integer>",outline:"[<'outline-color'>||<'outline-style'>||<'outline-width'>]","outline-color":"<color>|invert","outline-offset":"<length>","outline-style":"auto|<'border-style'>","outline-width":"<line-width>",overflow:"[visible|hidden|clip|scroll|auto]{1,2}|<-non-standard-overflow>","overflow-anchor":"auto|none","overflow-block":"visible|hidden|clip|scroll|auto","overflow-clip-box":"padding-box|content-box","overflow-inline":"visible|hidden|clip|scroll|auto","overflow-wrap":"normal|break-word|anywhere","overflow-x":"visible|hidden|clip|scroll|auto","overflow-y":"visible|hidden|clip|scroll|auto","overscroll-behavior":"[contain|none|auto]{1,2}","overscroll-behavior-x":"contain|none|auto","overscroll-behavior-y":"contain|none|auto",padding:"[<length>|<percentage>]{1,4}","padding-block":"<'padding-left'>{1,2}","padding-block-end":"<'padding-left'>","padding-block-start":"<'padding-left'>","padding-bottom":"<length>|<percentage>","padding-inline":"<'padding-left'>{1,2}","padding-inline-end":"<'padding-left'>","padding-inline-start":"<'padding-left'>","padding-left":"<length>|<percentage>","padding-right":"<length>|<percentage>","padding-top":"<length>|<percentage>","page-break-after":"auto|always|avoid|left|right|recto|verso","page-break-before":"auto|always|avoid|left|right|recto|verso","page-break-inside":"auto|avoid","paint-order":"normal|[fill||stroke||markers]",perspective:"none|<length>","perspective-origin":"<position>","place-content":"<'align-content'> <'justify-content'>?","place-items":"<'align-items'> <'justify-items'>?","place-self":"<'align-self'> <'justify-self'>?","pointer-events":"auto|none|visiblePainted|visibleFill|visibleStroke|visible|painted|fill|stroke|all|inherit",position:"static|relative|absolute|sticky|fixed|-webkit-sticky",quotes:"none|[<string> <string>]+",resize:"none|both|horizontal|vertical|block|inline",right:"<length>|<percentage>|auto",rotate:"none|<angle>|[x|y|z|<number>{3}]&&<angle>","row-gap":"normal|<length-percentage>","ruby-align":"start|center|space-between|space-around","ruby-merge":"separate|collapse|auto","ruby-position":"over|under|inter-character",scale:"none|<number>{1,3}","scrollbar-color":"auto|dark|light|<color>{2}","scrollbar-width":"auto|thin|none","scroll-behavior":"auto|smooth","scroll-margin":"<length>{1,4}","scroll-margin-block":"<length>{1,2}","scroll-margin-block-start":"<length>","scroll-margin-block-end":"<length>","scroll-margin-bottom":"<length>","scroll-margin-inline":"<length>{1,2}","scroll-margin-inline-start":"<length>","scroll-margin-inline-end":"<length>","scroll-margin-left":"<length>","scroll-margin-right":"<length>","scroll-margin-top":"<length>","scroll-padding":"[auto|<length-percentage>]{1,4}","scroll-padding-block":"[auto|<length-percentage>]{1,2}","scroll-padding-block-start":"auto|<length-percentage>","scroll-padding-block-end":"auto|<length-percentage>","scroll-padding-bottom":"auto|<length-percentage>","scroll-padding-inline":"[auto|<length-percentage>]{1,2}","scroll-padding-inline-start":"auto|<length-percentage>","scroll-padding-inline-end":"auto|<length-percentage>","scroll-padding-left":"auto|<length-percentage>","scroll-padding-right":"auto|<length-percentage>","scroll-padding-top":"auto|<length-percentage>","scroll-snap-align":"[none|start|end|center]{1,2}","scroll-snap-coordinate":"none|<position>#","scroll-snap-destination":"<position>","scroll-snap-points-x":"none|repeat( <length-percentage> )","scroll-snap-points-y":"none|repeat( <length-percentage> )","scroll-snap-stop":"normal|always","scroll-snap-type":"none|[x|y|block|inline|both] [mandatory|proximity]?","scroll-snap-type-x":"none|mandatory|proximity","scroll-snap-type-y":"none|mandatory|proximity","shape-image-threshold":"<number>","shape-margin":"<length-percentage>","shape-outside":"none|<shape-box>||<basic-shape>|<image>","tab-size":"<integer>|<length>","table-layout":"auto|fixed","text-align":"start|end|left|right|center|justify|match-parent","text-align-last":"auto|start|end|left|right|center|justify","text-combine-upright":"none|all|[digits <integer>?]","text-decoration":"<'text-decoration-line'>||<'text-decoration-style'>||<'text-decoration-color'>","text-decoration-color":"<color>","text-decoration-line":"none|[underline||overline||line-through||blink]","text-decoration-skip":"none|[objects||[spaces|[leading-spaces||trailing-spaces]]||edges||box-decoration]","text-decoration-skip-ink":"auto|none","text-decoration-style":"solid|double|dotted|dashed|wavy","text-emphasis":"<'text-emphasis-style'>||<'text-emphasis-color'>","text-emphasis-color":"<color>","text-emphasis-position":"[over|under]&&[right|left]","text-emphasis-style":"none|[[filled|open]||[dot|circle|double-circle|triangle|sesame]]|<string>","text-indent":"<length-percentage>&&hanging?&&each-line?","text-justify":"auto|inter-character|inter-word|none","text-orientation":"mixed|upright|sideways","text-overflow":"[clip|ellipsis|<string>]{1,2}","text-rendering":"auto|optimizeSpeed|optimizeLegibility|geometricPrecision","text-shadow":"none|<shadow-t>#","text-size-adjust":"none|auto|<percentage>","text-transform":"none|capitalize|uppercase|lowercase|full-width|full-size-kana","text-underline-position":"auto|[under||[left|right]]",top:"<length>|<percentage>|auto","touch-action":"auto|none|[[pan-x|pan-left|pan-right]||[pan-y|pan-up|pan-down]||pinch-zoom]|manipulation",transform:"none|<transform-list>","transform-box":"border-box|fill-box|view-box","transform-origin":"[<length-percentage>|left|center|right|top|bottom]|[[<length-percentage>|left|center|right]&&[<length-percentage>|top|center|bottom]] <length>?","transform-style":"flat|preserve-3d",transition:"<single-transition>#","transition-delay":"<time>#","transition-duration":"<time>#","transition-property":"none|<single-transition-property>#","transition-timing-function":"<timing-function>#",translate:"none|<length-percentage> [<length-percentage> <length>?]?","unicode-bidi":"normal|embed|isolate|bidi-override|isolate-override|plaintext|-moz-isolate|-moz-isolate-override|-moz-plaintext|-webkit-isolate","user-select":"auto|text|none|contain|all","vertical-align":"baseline|sub|super|text-top|text-bottom|middle|top|bottom|<percentage>|<length>",visibility:"visible|hidden|collapse","white-space":"normal|pre|nowrap|pre-wrap|pre-line",widows:"<integer>",width:"[<length>|<percentage>]&&[border-box|content-box]?|available|min-content|max-content|fit-content|auto","will-change":"auto|<animateable-feature>#","word-break":"normal|break-all|keep-all|break-word","word-spacing":"normal|<length-percentage>","word-wrap":"normal|break-word","writing-mode":"horizontal-tb|vertical-rl|vertical-lr|sideways-rl|sideways-lr|<svg-writing-mode>","z-index":"auto|<integer>",zoom:"normal|reset|<number>|<percentage>","-moz-background-clip":"padding|border","-moz-border-radius-bottomleft":"<'border-bottom-left-radius'>","-moz-border-radius-bottomright":"<'border-bottom-right-radius'>","-moz-border-radius-topleft":"<'border-top-left-radius'>","-moz-border-radius-topright":"<'border-bottom-right-radius'>","-moz-control-character-visibility":"visible|hidden","-moz-osx-font-smoothing":"auto|grayscale","-moz-user-select":"none|text|all|-moz-none","-ms-flex-align":"start|end|center|baseline|stretch","-ms-flex-item-align":"auto|start|end|center|baseline|stretch","-ms-flex-line-pack":"start|end|center|justify|distribute|stretch","-ms-flex-negative":"<'flex-shrink'>","-ms-flex-pack":"start|end|center|justify|distribute","-ms-flex-order":"<integer>","-ms-flex-positive":"<'flex-grow'>","-ms-flex-preferred-size":"<'flex-basis'>","-ms-interpolation-mode":"nearest-neighbor|bicubic","-ms-grid-column-align":"start|end|center|stretch","-ms-grid-columns":"<track-list-v0>","-ms-grid-row-align":"start|end|center|stretch","-ms-grid-rows":"<track-list-v0>","-ms-hyphenate-limit-last":"none|always|column|page|spread","-webkit-background-clip":"[<box>|border|padding|content|text]#","-webkit-column-break-after":"always|auto|avoid","-webkit-column-break-before":"always|auto|avoid","-webkit-column-break-inside":"always|auto|avoid","-webkit-font-smoothing":"auto|none|antialiased|subpixel-antialiased","-webkit-mask-box-image":"[<url>|<gradient>|none] [<length-percentage>{4} <-webkit-mask-box-repeat>{2}]?","-webkit-print-color-adjust":"economy|exact","-webkit-text-security":"none|circle|disc|square","-webkit-user-drag":"none|element|auto","-webkit-user-select":"auto|none|text|all","alignment-baseline":"auto|baseline|before-edge|text-before-edge|middle|central|after-edge|text-after-edge|ideographic|alphabetic|hanging|mathematical","baseline-shift":"baseline|sub|super|<svg-length>",behavior:"<url>+","clip-rule":"nonzero|evenodd",cue:"<'cue-before'> <'cue-after'>?","cue-after":"<url> <decibel>?|none","cue-before":"<url> <decibel>?|none","dominant-baseline":"auto|use-script|no-change|reset-size|ideographic|alphabetic|hanging|mathematical|central|middle|text-after-edge|text-before-edge",fill:"<paint>","fill-opacity":"<number-zero-one>","fill-rule":"nonzero|evenodd","glyph-orientation-horizontal":"<angle>","glyph-orientation-vertical":"<angle>",kerning:"auto|<svg-length>",marker:"none|<url>","marker-end":"none|<url>","marker-mid":"none|<url>","marker-start":"none|<url>",pause:"<'pause-before'> <'pause-after'>?","pause-after":"<time>|none|x-weak|weak|medium|strong|x-strong","pause-before":"<time>|none|x-weak|weak|medium|strong|x-strong",rest:"<'rest-before'> <'rest-after'>?","rest-after":"<time>|none|x-weak|weak|medium|strong|x-strong","rest-before":"<time>|none|x-weak|weak|medium|strong|x-strong","shape-rendering":"auto|optimizeSpeed|crispEdges|geometricPrecision",src:"[<url> [format( <string># )]?|local( <family-name> )]#",speak:"auto|none|normal","speak-as":"normal|spell-out||digits||[literal-punctuation|no-punctuation]",stroke:"<paint>","stroke-dasharray":"none|[<svg-length>+]#","stroke-dashoffset":"<svg-length>","stroke-linecap":"butt|round|square","stroke-linejoin":"miter|round|bevel","stroke-miterlimit":"<number-one-or-greater>","stroke-opacity":"<number-zero-one>","stroke-width":"<svg-length>","text-anchor":"start|middle|end","unicode-range":"<urange>#","voice-balance":"<number>|left|center|right|leftwards|rightwards","voice-duration":"auto|<time>","voice-family":"[[<family-name>|<generic-voice>] ,]* [<family-name>|<generic-voice>]|preserve","voice-pitch":"<frequency>&&absolute|[[x-low|low|medium|high|x-high]||[<frequency>|<semitones>|<percentage>]]","voice-range":"<frequency>&&absolute|[[x-low|low|medium|high|x-high]||[<frequency>|<semitones>|<percentage>]]","voice-rate":"[normal|x-slow|slow|medium|fast|x-fast]||<percentage>","voice-stress":"normal|strong|moderate|none|reduced","voice-volume":"silent|[[x-soft|soft|medium|loud|x-loud]||<decibel>]"},Dr={generic:!0,types:Er,properties:Or},Rr=Object.freeze({__proto__:null,generic:!0,types:Er,properties:Or,default:Dr}),Ir=Se.cmpChar,Mr=Se.isDigit,Nr=Se.TYPE,Br=Nr.WhiteSpace,jr=Nr.Comment,_r=Nr.Ident,qr=Nr.Number,Wr=Nr.Dimension;function Fr(e,t){var n=this.scanner.tokenStart+e,r=this.scanner.source.charCodeAt(n);for(43!==r&&45!==r||(t&&this.error("Number sign is not allowed"),n++);n<this.scanner.tokenEnd;n++)Mr(this.scanner.source.charCodeAt(n))||this.error("Integer is expected",n)}function Ur(e){return Fr.call(this,0,e)}function Yr(e,t){if(!Ir(this.scanner.source,this.scanner.tokenStart+e,t)){var n="";switch(t){case 110:n="N is expected";break;case 45:n="HyphenMinus is expected"}this.error(n,this.scanner.tokenStart+e)}}function Hr(){for(var e=0,t=0,n=this.scanner.tokenType;n===Br||n===jr;)n=this.scanner.lookupType(++e);if(n!==qr){if(!this.scanner.isDelim(43,e)&&!this.scanner.isDelim(45,e))return null;t=this.scanner.isDelim(43,e)?43:45;do{n=this.scanner.lookupType(++e)}while(n===Br||n===jr);n!==qr&&(this.scanner.skip(e),Ur.call(this,!0))}return e>0&&this.scanner.skip(e),0===t&&43!==(n=this.scanner.source.charCodeAt(this.scanner.tokenStart))&&45!==n&&this.error("Number sign is expected"),Ur.call(this,0!==t),45===t?"-"+this.consume(qr):this.consume(qr)}var Vr={name:"AnPlusB",structure:{a:[String,null],b:[String,null]},parse:function(){var e=this.scanner.tokenStart,t=null,n=null;if(this.scanner.tokenType===qr)Ur.call(this,!1),n=this.consume(qr);else if(this.scanner.tokenType===_r&&Ir(this.scanner.source,this.scanner.tokenStart,45))switch(t="-1",Yr.call(this,1,110),this.scanner.getTokenLength()){case 2:this.scanner.next(),n=Hr.call(this);break;case 3:Yr.call(this,2,45),this.scanner.next(),this.scanner.skipSC(),Ur.call(this,!0),n="-"+this.consume(qr);break;default:Yr.call(this,2,45),Fr.call(this,3,!0),this.scanner.next(),n=this.scanner.substrToCursor(e+2)}else if(this.scanner.tokenType===_r||this.scanner.isDelim(43)&&this.scanner.lookupType(1)===_r){var r=0;switch(t="1",this.scanner.isDelim(43)&&(r=1,this.scanner.next()),Yr.call(this,0,110),this.scanner.getTokenLength()){case 1:this.scanner.next(),n=Hr.call(this);break;case 2:Yr.call(this,1,45),this.scanner.next(),this.scanner.skipSC(),Ur.call(this,!0),n="-"+this.consume(qr);break;default:Yr.call(this,1,45),Fr.call(this,2,!0),this.scanner.next(),n=this.scanner.substrToCursor(e+r+1)}}else if(this.scanner.tokenType===Wr){for(var a=this.scanner.source.charCodeAt(this.scanner.tokenStart),i=(r=43===a||45===a,this.scanner.tokenStart+r);i<this.scanner.tokenEnd&&Mr(this.scanner.source.charCodeAt(i));i++);i===this.scanner.tokenStart+r&&this.error("Integer is expected",this.scanner.tokenStart+r),Yr.call(this,i-this.scanner.tokenStart,110),t=this.scanner.source.substring(e,i),i+1===this.scanner.tokenEnd?(this.scanner.next(),n=Hr.call(this)):(Yr.call(this,i-this.scanner.tokenStart+1,45),i+2===this.scanner.tokenEnd?(this.scanner.next(),this.scanner.skipSC(),Ur.call(this,!0),n="-"+this.consume(qr)):(Fr.call(this,i-this.scanner.tokenStart+2,!0),this.scanner.next(),n=this.scanner.substrToCursor(i+1)))}else this.error();return null!==t&&43===t.charCodeAt(0)&&(t=t.substr(1)),null!==n&&43===n.charCodeAt(0)&&(n=n.substr(1)),{type:"AnPlusB",loc:this.getLocation(e,this.scanner.tokenStart),a:t,b:n}},generate:function(e){var t=null!==e.a&&void 0!==e.a,n=null!==e.b&&void 0!==e.b;t?(this.chunk("+1"===e.a?"+n":"1"===e.a?"n":"-1"===e.a?"-n":e.a+"n"),n&&("-"===(n=String(e.b)).charAt(0)||"+"===n.charAt(0)?(this.chunk(n.charAt(0)),this.chunk(n.substr(1))):(this.chunk("+"),this.chunk(n)))):this.chunk(String(e.b))}},Kr=Se.TYPE,Gr=Kr.WhiteSpace,$r=Kr.Semicolon,Qr=Kr.LeftCurlyBracket,Xr=Kr.Delim;function Zr(){return this.scanner.tokenIndex>0&&this.scanner.lookupType(-1)===Gr?this.scanner.tokenIndex>1?this.scanner.getTokenStart(this.scanner.tokenIndex-1):this.scanner.firstCharOffset:this.scanner.tokenStart}function Jr(){return 0}var ea={name:"Raw",structure:{value:String},parse:function(e,t,n){var r,a=this.scanner.getTokenStart(e);return this.scanner.skip(this.scanner.getRawLength(e,t||Jr)),r=n&&this.scanner.tokenStart>a?Zr.call(this):this.scanner.tokenStart,{type:"Raw",loc:this.getLocation(a,r),value:this.scanner.source.substring(a,r)}},generate:function(e){this.chunk(e.value)},mode:{default:Jr,leftCurlyBracket:function(e){return e===Qr?1:0},leftCurlyBracketOrSemicolon:function(e){return e===Qr||e===$r?1:0},exclamationMarkOrSemicolon:function(e,t,n){return e===Xr&&33===t.charCodeAt(n)||e===$r?1:0},semicolonIncluded:function(e){return e===$r?2:0}}},ta=Se.TYPE,na=ea.mode,ra=ta.AtKeyword,aa=ta.Semicolon,ia=ta.LeftCurlyBracket,oa=ta.RightCurlyBracket;function sa(e){return this.Raw(e,na.leftCurlyBracketOrSemicolon,!0)}function la(){for(var e,t=1;e=this.scanner.lookupType(t);t++){if(e===oa)return!0;if(e===ia||e===ra)return!1}return!1}var ca={name:"Atrule",structure:{name:String,prelude:["AtrulePrelude","Raw",null],block:["Block",null]},parse:function(){var e,t,n=this.scanner.tokenStart,r=null,a=null;switch(this.eat(ra),t=(e=this.scanner.substrToCursor(n+1)).toLowerCase(),this.scanner.skipSC(),!1===this.scanner.eof&&this.scanner.tokenType!==ia&&this.scanner.tokenType!==aa&&(this.parseAtrulePrelude?"AtrulePrelude"===(r=this.parseWithFallback(this.AtrulePrelude.bind(this,e),sa)).type&&null===r.children.head&&(r=null):r=sa.call(this,this.scanner.tokenIndex),this.scanner.skipSC()),this.scanner.tokenType){case aa:this.scanner.next();break;case ia:a=this.atrule.hasOwnProperty(t)&&"function"==typeof this.atrule[t].block?this.atrule[t].block.call(this):this.Block(la.call(this))}return{type:"Atrule",loc:this.getLocation(n,this.scanner.tokenStart),name:e,prelude:r,block:a}},generate:function(e){this.chunk("@"),this.chunk(e.name),null!==e.prelude&&(this.chunk(" "),this.node(e.prelude)),e.block?this.node(e.block):this.chunk(";")},walkContext:"atrule"},ua=Se.TYPE,ha=ua.Semicolon,da=ua.LeftCurlyBracket,pa={name:"AtrulePrelude",structure:{children:[[]]},parse:function(e){var t=null;return null!==e&&(e=e.toLowerCase()),this.scanner.skipSC(),t=this.atrule.hasOwnProperty(e)&&"function"==typeof this.atrule[e].prelude?this.atrule[e].prelude.call(this):this.readSequence(this.scope.AtrulePrelude),this.scanner.skipSC(),!0!==this.scanner.eof&&this.scanner.tokenType!==da&&this.scanner.tokenType!==ha&&this.error("Semicolon or block is expected"),null===t&&(t=this.createList()),{type:"AtrulePrelude",loc:this.getLocationFromList(t),children:t}},generate:function(e){this.children(e)},walkContext:"atrulePrelude"},fa=Se.TYPE,ma=fa.Ident,ga=fa.String,ba=fa.Colon,ya=fa.LeftSquareBracket,ka=fa.RightSquareBracket;function va(){this.scanner.eof&&this.error("Unexpected end of input");var e=this.scanner.tokenStart,t=!1,n=!0;return this.scanner.isDelim(42)?(t=!0,n=!1,this.scanner.next()):this.scanner.isDelim(124)||this.eat(ma),this.scanner.isDelim(124)?61!==this.scanner.source.charCodeAt(this.scanner.tokenStart+1)?(this.scanner.next(),this.eat(ma)):t&&this.error("Identifier is expected",this.scanner.tokenEnd):t&&this.error("Vertical line is expected"),n&&this.scanner.tokenType===ba&&(this.scanner.next(),this.eat(ma)),{type:"Identifier",loc:this.getLocation(e,this.scanner.tokenStart),name:this.scanner.substrToCursor(e)}}function wa(){var e=this.scanner.tokenStart,t=this.scanner.source.charCodeAt(e);return 61!==t&&126!==t&&94!==t&&36!==t&&42!==t&&124!==t&&this.error("Attribute selector (=, ~=, ^=, $=, *=, |=) is expected"),this.scanner.next(),61!==t&&(this.scanner.isDelim(61)||this.error("Equal sign is expected"),this.scanner.next()),this.scanner.substrToCursor(e)}var xa={name:"AttributeSelector",structure:{name:"Identifier",matcher:[String,null],value:["String","Identifier",null],flags:[String,null]},parse:function(){var e,t=this.scanner.tokenStart,n=null,r=null,a=null;return this.eat(ya),this.scanner.skipSC(),e=va.call(this),this.scanner.skipSC(),this.scanner.tokenType!==ka&&(this.scanner.tokenType!==ma&&(n=wa.call(this),this.scanner.skipSC(),r=this.scanner.tokenType===ga?this.String():this.Identifier(),this.scanner.skipSC()),this.scanner.tokenType===ma&&(a=this.scanner.getTokenValue(),this.scanner.next(),this.scanner.skipSC())),this.eat(ka),{type:"AttributeSelector",loc:this.getLocation(t,this.scanner.tokenStart),name:e,matcher:n,value:r,flags:a}},generate:function(e){var t=" ";this.chunk("["),this.node(e.name),null!==e.matcher&&(this.chunk(e.matcher),null!==e.value&&(this.node(e.value),"String"===e.value.type&&(t=""))),null!==e.flags&&(this.chunk(t),this.chunk(e.flags)),this.chunk("]")}},Sa=Se.TYPE,Ca=ea.mode,Aa=Sa.WhiteSpace,za=Sa.Comment,Pa=Sa.Semicolon,La=Sa.AtKeyword,Ta=Sa.LeftCurlyBracket,Ea=Sa.RightCurlyBracket;function Oa(e){return this.Raw(e,null,!0)}function Da(){return this.parseWithFallback(this.Rule,Oa)}function Ra(e){return this.Raw(e,Ca.semicolonIncluded,!0)}function Ia(){if(this.scanner.tokenType===Pa)return Ra.call(this,this.scanner.tokenIndex);var e=this.parseWithFallback(this.Declaration,Ra);return this.scanner.tokenType===Pa&&this.scanner.next(),e}var Ma={name:"Block",structure:{children:[["Atrule","Rule","Declaration"]]},parse:function(e){var t=e?Ia:Da,n=this.scanner.tokenStart,r=this.createList();this.eat(Ta);e:for(;!this.scanner.eof;)switch(this.scanner.tokenType){case Ea:break e;case Aa:case za:this.scanner.next();break;case La:r.push(this.parseWithFallback(this.Atrule,Oa));break;default:r.push(t.call(this))}return this.scanner.eof||this.eat(Ea),{type:"Block",loc:this.getLocation(n,this.scanner.tokenStart),children:r}},generate:function(e){this.chunk("{"),this.children(e,(function(e){"Declaration"===e.type&&this.chunk(";")})),this.chunk("}")},walkContext:"block"},Na=Se.TYPE,Ba=Na.LeftSquareBracket,ja=Na.RightSquareBracket,_a={name:"Brackets",structure:{children:[[]]},parse:function(e,t){var n,r=this.scanner.tokenStart;return this.eat(Ba),n=e.call(this,t),this.scanner.eof||this.eat(ja),{type:"Brackets",loc:this.getLocation(r,this.scanner.tokenStart),children:n}},generate:function(e){this.chunk("["),this.children(e),this.chunk("]")}},qa=Se.TYPE.CDC,Wa={name:"CDC",structure:[],parse:function(){var e=this.scanner.tokenStart;return this.eat(qa),{type:"CDC",loc:this.getLocation(e,this.scanner.tokenStart)}},generate:function(){this.chunk("--\x3e")}},Fa=Se.TYPE.CDO,Ua={name:"CDO",structure:[],parse:function(){var e=this.scanner.tokenStart;return this.eat(Fa),{type:"CDO",loc:this.getLocation(e,this.scanner.tokenStart)}},generate:function(){this.chunk("\x3c!--")}},Ya=Se.TYPE.Ident,Ha={name:"ClassSelector",structure:{name:String},parse:function(){return this.scanner.isDelim(46)||this.error("Full stop is expected"),this.scanner.next(),{type:"ClassSelector",loc:this.getLocation(this.scanner.tokenStart-1,this.scanner.tokenEnd),name:this.consume(Ya)}},generate:function(e){this.chunk("."),this.chunk(e.name)}},Va=Se.TYPE.Ident,Ka={name:"Combinator",structure:{name:String},parse:function(){var e=this.scanner.tokenStart;switch(this.scanner.source.charCodeAt(this.scanner.tokenStart)){case 62:case 43:case 126:this.scanner.next();break;case 47:this.scanner.next(),this.scanner.tokenType===Va&&!1!==this.scanner.lookupValue(0,"deep")||this.error("Identifier `deep` is expected"),this.scanner.next(),this.scanner.isDelim(47)||this.error("Solidus is expected"),this.scanner.next();break;default:this.error("Combinator is expected")}return{type:"Combinator",loc:this.getLocation(e,this.scanner.tokenStart),name:this.scanner.substrToCursor(e)}},generate:function(e){this.chunk(e.name)}},Ga=Se.TYPE.Comment,$a={name:"Comment",structure:{value:String},parse:function(){var e=this.scanner.tokenStart,t=this.scanner.tokenEnd;return this.eat(Ga),t-e+2>=2&&42===this.scanner.source.charCodeAt(t-2)&&47===this.scanner.source.charCodeAt(t-1)&&(t-=2),{type:"Comment",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.source.substring(e+2,t)}},generate:function(e){this.chunk("/*"),this.chunk(e.value),this.chunk("*/")}},Qa=ae.isCustomProperty,Xa=Se.TYPE,Za=ea.mode,Ja=Xa.Ident,ei=Xa.Hash,ti=Xa.Colon,ni=Xa.Semicolon,ri=Xa.Delim;function ai(e){return this.Raw(e,Za.exclamationMarkOrSemicolon,!0)}function ii(e){return this.Raw(e,Za.exclamationMarkOrSemicolon,!1)}function oi(){var e=this.scanner.tokenIndex,t=this.Value();return"Raw"!==t.type&&!1===this.scanner.eof&&this.scanner.tokenType!==ni&&!1===this.scanner.isDelim(33)&&!1===this.scanner.isBalanceEdge(e)&&this.error(),t}var si={name:"Declaration",structure:{important:[Boolean,String],property:String,value:["Value","Raw"]},parse:function(){var e,t=this.scanner.tokenStart,n=this.scanner.tokenIndex,r=li.call(this),a=Qa(r),i=a?this.parseCustomProperty:this.parseValue,o=a?ii:ai,s=!1;return this.scanner.skipSC(),this.eat(ti),a||this.scanner.skipSC(),e=i?this.parseWithFallback(oi,o):o.call(this,this.scanner.tokenIndex),this.scanner.isDelim(33)&&(s=ci.call(this),this.scanner.skipSC()),!1===this.scanner.eof&&this.scanner.tokenType!==ni&&!1===this.scanner.isBalanceEdge(n)&&this.error(),{type:"Declaration",loc:this.getLocation(t,this.scanner.tokenStart),important:s,property:r,value:e}},generate:function(e){this.chunk(e.property),this.chunk(":"),this.node(e.value),e.important&&this.chunk(!0===e.important?"!important":"!"+e.important)},walkContext:"declaration"};function li(){var e=this.scanner.tokenStart;if(this.scanner.tokenType===ri)switch(this.scanner.source.charCodeAt(this.scanner.tokenStart)){case 42:case 36:case 43:case 35:case 38:this.scanner.next();break;case 47:this.scanner.next(),this.scanner.isDelim(47)&&this.scanner.next()}return this.scanner.tokenType===ei?this.eat(ei):this.eat(Ja),this.scanner.substrToCursor(e)}function ci(){this.eat(ri),this.scanner.skipSC();var e=this.consume(Ja);return"important"===e||e}var ui=Se.TYPE,hi=ea.mode,di=ui.WhiteSpace,pi=ui.Comment,fi=ui.Semicolon;function mi(e){return this.Raw(e,hi.semicolonIncluded,!0)}var gi={name:"DeclarationList",structure:{children:[["Declaration"]]},parse:function(){for(var e=this.createList();!this.scanner.eof;)switch(this.scanner.tokenType){case di:case pi:case fi:this.scanner.next();break;default:e.push(this.parseWithFallback(this.Declaration,mi))}return{type:"DeclarationList",loc:this.getLocationFromList(e),children:e}},generate:function(e){this.children(e,(function(e){"Declaration"===e.type&&this.chunk(";")}))}},bi=j.consumeNumber,yi=Se.TYPE.Dimension,ki={name:"Dimension",structure:{value:String,unit:String},parse:function(){var e=this.scanner.tokenStart,t=bi(this.scanner.source,e);return this.eat(yi),{type:"Dimension",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.source.substring(e,t),unit:this.scanner.source.substring(t,this.scanner.tokenStart)}},generate:function(e){this.chunk(e.value),this.chunk(e.unit)}},vi=Se.TYPE.RightParenthesis,wi={name:"Function",structure:{name:String,children:[[]]},parse:function(e,t){var n,r=this.scanner.tokenStart,a=this.consumeFunctionName(),i=a.toLowerCase();return n=t.hasOwnProperty(i)?t[i].call(this,t):e.call(this,t),this.scanner.eof||this.eat(vi),{type:"Function",loc:this.getLocation(r,this.scanner.tokenStart),name:a,children:n}},generate:function(e){this.chunk(e.name),this.chunk("("),this.children(e),this.chunk(")")},walkContext:"function"},xi=Se.TYPE.Hash,Si={name:"HexColor",structure:{value:String},parse:function(){var e=this.scanner.tokenStart;return this.eat(xi),{type:"HexColor",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.substrToCursor(e+1)}},generate:function(e){this.chunk("#"),this.chunk(e.value)}},Ci=Se.TYPE.Ident,Ai={name:"Identifier",structure:{name:String},parse:function(){return{type:"Identifier",loc:this.getLocation(this.scanner.tokenStart,this.scanner.tokenEnd),name:this.consume(Ci)}},generate:function(e){this.chunk(e.name)}},zi=Se.TYPE.Hash,Pi={name:"IdSelector",structure:{name:String},parse:function(){var e=this.scanner.tokenStart;return this.eat(zi),{type:"IdSelector",loc:this.getLocation(e,this.scanner.tokenStart),name:this.scanner.substrToCursor(e+1)}},generate:function(e){this.chunk("#"),this.chunk(e.name)}},Li=Se.TYPE,Ti=Li.Ident,Ei=Li.Number,Oi=Li.Dimension,Di=Li.LeftParenthesis,Ri=Li.RightParenthesis,Ii=Li.Colon,Mi=Li.Delim,Ni={name:"MediaFeature",structure:{name:String,value:["Identifier","Number","Dimension","Ratio",null]},parse:function(){var e,t=this.scanner.tokenStart,n=null;if(this.eat(Di),this.scanner.skipSC(),e=this.consume(Ti),this.scanner.skipSC(),this.scanner.tokenType!==Ri){switch(this.eat(Ii),this.scanner.skipSC(),this.scanner.tokenType){case Ei:n=this.lookupNonWSType(1)===Mi?this.Ratio():this.Number();break;case Oi:n=this.Dimension();break;case Ti:n=this.Identifier();break;default:this.error("Number, dimension, ratio or identifier is expected")}this.scanner.skipSC()}return this.eat(Ri),{type:"MediaFeature",loc:this.getLocation(t,this.scanner.tokenStart),name:e,value:n}},generate:function(e){this.chunk("("),this.chunk(e.name),null!==e.value&&(this.chunk(":"),this.node(e.value)),this.chunk(")")}},Bi=Se.TYPE,ji=Bi.WhiteSpace,_i=Bi.Comment,qi=Bi.Ident,Wi=Bi.LeftParenthesis,Fi={name:"MediaQuery",structure:{children:[["Identifier","MediaFeature","WhiteSpace"]]},parse:function(){this.scanner.skipSC();var e=this.createList(),t=null,n=null;e:for(;!this.scanner.eof;){switch(this.scanner.tokenType){case _i:this.scanner.next();continue;case ji:n=this.WhiteSpace();continue;case qi:t=this.Identifier();break;case Wi:t=this.MediaFeature();break;default:break e}null!==n&&(e.push(n),n=null),e.push(t)}return null===t&&this.error("Identifier or parenthesis is expected"),{type:"MediaQuery",loc:this.getLocationFromList(e),children:e}},generate:function(e){this.children(e)}},Ui=Se.TYPE.Comma,Yi={name:"MediaQueryList",structure:{children:[["MediaQuery"]]},parse:function(e){var t=this.createList();for(this.scanner.skipSC();!this.scanner.eof&&(t.push(this.MediaQuery(e)),this.scanner.tokenType===Ui);)this.scanner.next();return{type:"MediaQueryList",loc:this.getLocationFromList(t),children:t}},generate:function(e){this.children(e,(function(){this.chunk(",")}))}},Hi=Se.TYPE.Number,Vi={name:"Number",structure:{value:String},parse:function(){return{type:"Number",loc:this.getLocation(this.scanner.tokenStart,this.scanner.tokenEnd),value:this.consume(Hi)}},generate:function(e){this.chunk(e.value)}},Ki={name:"Operator",structure:{value:String},parse:function(){var e=this.scanner.tokenStart;return this.scanner.next(),{type:"Operator",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.substrToCursor(e)}},generate:function(e){this.chunk(e.value)}},Gi=Se.TYPE,$i=Gi.LeftParenthesis,Qi=Gi.RightParenthesis,Xi={name:"Parentheses",structure:{children:[[]]},parse:function(e,t){var n,r=this.scanner.tokenStart;return this.eat($i),n=e.call(this,t),this.scanner.eof||this.eat(Qi),{type:"Parentheses",loc:this.getLocation(r,this.scanner.tokenStart),children:n}},generate:function(e){this.chunk("("),this.children(e),this.chunk(")")}},Zi=j.consumeNumber,Ji=Se.TYPE.Percentage,eo={name:"Percentage",structure:{value:String},parse:function(){var e=this.scanner.tokenStart,t=Zi(this.scanner.source,e);return this.eat(Ji),{type:"Percentage",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.source.substring(e,t)}},generate:function(e){this.chunk(e.value),this.chunk("%")}},to=Se.TYPE,no=to.Ident,ro=to.Function,ao=to.Colon,io=to.RightParenthesis,oo={name:"PseudoClassSelector",structure:{name:String,children:[["Raw"],null]},parse:function(){var e,t,n=this.scanner.tokenStart,r=null;return this.eat(ao),this.scanner.tokenType===ro?(t=(e=this.consumeFunctionName()).toLowerCase(),this.pseudo.hasOwnProperty(t)?(this.scanner.skipSC(),r=this.pseudo[t].call(this),this.scanner.skipSC()):(r=this.createList()).push(this.Raw(this.scanner.tokenIndex,null,!1)),this.eat(io)):e=this.consume(no),{type:"PseudoClassSelector",loc:this.getLocation(n,this.scanner.tokenStart),name:e,children:r}},generate:function(e){this.chunk(":"),this.chunk(e.name),null!==e.children&&(this.chunk("("),this.children(e),this.chunk(")"))},walkContext:"function"},so=Se.TYPE,lo=so.Ident,co=so.Function,uo=so.Colon,ho=so.RightParenthesis,po={name:"PseudoElementSelector",structure:{name:String,children:[["Raw"],null]},parse:function(){var e,t,n=this.scanner.tokenStart,r=null;return this.eat(uo),this.eat(uo),this.scanner.tokenType===co?(t=(e=this.consumeFunctionName()).toLowerCase(),this.pseudo.hasOwnProperty(t)?(this.scanner.skipSC(),r=this.pseudo[t].call(this),this.scanner.skipSC()):(r=this.createList()).push(this.Raw(this.scanner.tokenIndex,null,!1)),this.eat(ho)):e=this.consume(lo),{type:"PseudoElementSelector",loc:this.getLocation(n,this.scanner.tokenStart),name:e,children:r}},generate:function(e){this.chunk("::"),this.chunk(e.name),null!==e.children&&(this.chunk("("),this.children(e),this.chunk(")"))},walkContext:"function"},fo=Se.isDigit,mo=Se.TYPE,go=mo.Number,bo=mo.Delim;function yo(){this.scanner.skipWS();for(var e=this.consume(go),t=0;t<e.length;t++){var n=e.charCodeAt(t);fo(n)||46===n||this.error("Unsigned number is expected",this.scanner.tokenStart-e.length+t)}return 0===Number(e)&&this.error("Zero number is not allowed",this.scanner.tokenStart-e.length),e}var ko={name:"Ratio",structure:{left:String,right:String},parse:function(){var e,t=this.scanner.tokenStart,n=yo.call(this);return this.scanner.skipWS(),this.scanner.isDelim(47)||this.error("Solidus is expected"),this.eat(bo),e=yo.call(this),{type:"Ratio",loc:this.getLocation(t,this.scanner.tokenStart),left:n,right:e}},generate:function(e){this.chunk(e.left),this.chunk("/"),this.chunk(e.right)}},vo=Se.TYPE,wo=ea.mode,xo=vo.LeftCurlyBracket;function So(e){return this.Raw(e,wo.leftCurlyBracket,!0)}function Co(){var e=this.SelectorList();return"Raw"!==e.type&&!1===this.scanner.eof&&this.scanner.tokenType!==xo&&this.error(),e}var Ao={name:"Rule",structure:{prelude:["SelectorList","Raw"],block:["Block"]},parse:function(){var e,t,n=this.scanner.tokenIndex,r=this.scanner.tokenStart;return e=this.parseRulePrelude?this.parseWithFallback(Co,So):So.call(this,n),t=this.Block(!0),{type:"Rule",loc:this.getLocation(r,this.scanner.tokenStart),prelude:e,block:t}},generate:function(e){this.node(e.prelude),this.node(e.block)},walkContext:"rule"},zo=Se.TYPE.Comma,Po={name:"SelectorList",structure:{children:[["Selector","Raw"]]},parse:function(){for(var e=this.createList();!this.scanner.eof&&(e.push(this.Selector()),this.scanner.tokenType===zo);)this.scanner.next();return{type:"SelectorList",loc:this.getLocationFromList(e),children:e}},generate:function(e){this.children(e,(function(){this.chunk(",")}))},walkContext:"selector"},Lo=Se.TYPE.String,To={name:"String",structure:{value:String},parse:function(){return{type:"String",loc:this.getLocation(this.scanner.tokenStart,this.scanner.tokenEnd),value:this.consume(Lo)}},generate:function(e){this.chunk(e.value)}},Eo=Se.TYPE,Oo=Eo.WhiteSpace,Do=Eo.Comment,Ro=Eo.AtKeyword,Io=Eo.CDO,Mo=Eo.CDC;function No(e){return this.Raw(e,null,!1)}var Bo={name:"StyleSheet",structure:{children:[["Comment","CDO","CDC","Atrule","Rule","Raw"]]},parse:function(){for(var e,t=this.scanner.tokenStart,n=this.createList();!this.scanner.eof;){switch(this.scanner.tokenType){case Oo:this.scanner.next();continue;case Do:if(33!==this.scanner.source.charCodeAt(this.scanner.tokenStart+2)){this.scanner.next();continue}e=this.Comment();break;case Io:e=this.CDO();break;case Mo:e=this.CDC();break;case Ro:e=this.parseWithFallback(this.Atrule,No);break;default:e=this.parseWithFallback(this.Rule,No)}n.push(e)}return{type:"StyleSheet",loc:this.getLocation(t,this.scanner.tokenStart),children:n}},generate:function(e){this.children(e)},walkContext:"stylesheet"},jo=Se.TYPE.Ident;function _o(){this.scanner.tokenType!==jo&&!1===this.scanner.isDelim(42)&&this.error("Identifier or asterisk is expected"),this.scanner.next()}var qo={name:"TypeSelector",structure:{name:String},parse:function(){var e=this.scanner.tokenStart;return this.scanner.isDelim(124)?(this.scanner.next(),_o.call(this)):(_o.call(this),this.scanner.isDelim(124)&&(this.scanner.next(),_o.call(this))),{type:"TypeSelector",loc:this.getLocation(e,this.scanner.tokenStart),name:this.scanner.substrToCursor(e)}},generate:function(e){this.chunk(e.name)}},Wo=Se.isHexDigit,Fo=Se.cmpChar,Uo=Se.TYPE,Yo=Se.NAME,Ho=Uo.Ident,Vo=Uo.Number,Ko=Uo.Dimension;function Go(e,t){for(var n=this.scanner.tokenStart+e,r=0;n<this.scanner.tokenEnd;n++){var a=this.scanner.source.charCodeAt(n);if(45===a&&t&&0!==r)return 0===Go.call(this,e+r+1,!1)&&this.error(),-1;Wo(a)||this.error(t&&0!==r?"HyphenMinus"+(r<6?" or hex digit":"")+" is expected":r<6?"Hex digit is expected":"Unexpected input",n),++r>6&&this.error("Too many hex digits",n)}return this.scanner.next(),r}function $o(e){for(var t=0;this.scanner.isDelim(63);)++t>e&&this.error("Too many question marks"),this.scanner.next()}function Qo(e){this.scanner.source.charCodeAt(this.scanner.tokenStart)!==e&&this.error(Yo[e]+" is expected")}function Xo(){var e=0;return this.scanner.isDelim(43)?(this.scanner.next(),this.scanner.tokenType===Ho?void((e=Go.call(this,0,!0))>0&&$o.call(this,6-e)):this.scanner.isDelim(63)?(this.scanner.next(),void $o.call(this,5)):void this.error("Hex digit or question mark is expected")):this.scanner.tokenType===Vo?(Qo.call(this,43),e=Go.call(this,1,!0),this.scanner.isDelim(63)?void $o.call(this,6-e):this.scanner.tokenType===Ko||this.scanner.tokenType===Vo?(Qo.call(this,45),void Go.call(this,1,!1)):void 0):this.scanner.tokenType===Ko?(Qo.call(this,43),void((e=Go.call(this,1,!0))>0&&$o.call(this,6-e))):void this.error()}var Zo={name:"UnicodeRange",structure:{value:String},parse:function(){var e=this.scanner.tokenStart;return Fo(this.scanner.source,e,117)||this.error("U is expected"),Fo(this.scanner.source,e+1,43)||this.error("Plus sign is expected"),this.scanner.next(),Xo.call(this),{type:"UnicodeRange",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.substrToCursor(e)}},generate:function(e){this.chunk(e.value)}},Jo=Se.isWhiteSpace,es=Se.cmpStr,ts=Se.TYPE,ns=ts.Function,rs=ts.Url,as=ts.RightParenthesis,is={name:"Url",structure:{value:["String","Raw"]},parse:function(){var e,t=this.scanner.tokenStart;switch(this.scanner.tokenType){case rs:for(var n=t+4,r=this.scanner.tokenEnd-1;n<r&&Jo(this.scanner.source.charCodeAt(n));)n++;for(;n<r&&Jo(this.scanner.source.charCodeAt(r-1));)r--;e={type:"Raw",loc:this.getLocation(n,r),value:this.scanner.source.substring(n,r)},this.eat(rs);break;case ns:es(this.scanner.source,this.scanner.tokenStart,this.scanner.tokenEnd,"url(")||this.error("Function name must be `url`"),this.eat(ns),this.scanner.skipSC(),e=this.String(),this.scanner.skipSC(),this.eat(as);break;default:this.error("Url or Function is expected")}return{type:"Url",loc:this.getLocation(t,this.scanner.tokenStart),value:e}},generate:function(e){this.chunk("url"),this.chunk("("),this.node(e.value),this.chunk(")")}},os=Se.TYPE.WhiteSpace,ss=Object.freeze({type:"WhiteSpace",loc:null,value:" "}),ls={AnPlusB:Vr,Atrule:ca,AtrulePrelude:pa,AttributeSelector:xa,Block:Ma,Brackets:_a,CDC:Wa,CDO:Ua,ClassSelector:Ha,Combinator:Ka,Comment:$a,Declaration:si,DeclarationList:gi,Dimension:ki,Function:wi,HexColor:Si,Identifier:Ai,IdSelector:Pi,MediaFeature:Ni,MediaQuery:Fi,MediaQueryList:Yi,Nth:{name:"Nth",structure:{nth:["AnPlusB","Identifier"],selector:["SelectorList",null]},parse:function(e){this.scanner.skipSC();var t,n=this.scanner.tokenStart,r=n,a=null;return t=this.scanner.lookupValue(0,"odd")||this.scanner.lookupValue(0,"even")?this.Identifier():this.AnPlusB(),this.scanner.skipSC(),e&&this.scanner.lookupValue(0,"of")?(this.scanner.next(),a=this.SelectorList(),this.needPositions&&(r=this.getLastListNode(a.children).loc.end.offset)):this.needPositions&&(r=t.loc.end.offset),{type:"Nth",loc:this.getLocation(n,r),nth:t,selector:a}},generate:function(e){this.node(e.nth),null!==e.selector&&(this.chunk(" of "),this.node(e.selector))}},Number:Vi,Operator:Ki,Parentheses:Xi,Percentage:eo,PseudoClassSelector:oo,PseudoElementSelector:po,Ratio:ko,Raw:ea,Rule:Ao,Selector:{name:"Selector",structure:{children:[["TypeSelector","IdSelector","ClassSelector","AttributeSelector","PseudoClassSelector","PseudoElementSelector","Combinator","WhiteSpace"]]},parse:function(){var e=this.readSequence(this.scope.Selector);return null===this.getFirstListNode(e)&&this.error("Selector is expected"),{type:"Selector",loc:this.getLocationFromList(e),children:e}},generate:function(e){this.children(e)}},SelectorList:Po,String:To,StyleSheet:Bo,TypeSelector:qo,UnicodeRange:Zo,Url:is,Value:{name:"Value",structure:{children:[[]]},parse:function(){var e=this.scanner.tokenStart,t=this.readSequence(this.scope.Value);return{type:"Value",loc:this.getLocation(e,this.scanner.tokenStart),children:t}},generate:function(e){this.children(e)}},WhiteSpace:{name:"WhiteSpace",structure:{value:String},parse:function(){return this.eat(os),ss},generate:function(e){this.chunk(e.value)}}},cs=Zn(Rr),us={generic:!0,types:cs.types,atrules:cs.atrules,properties:cs.properties,node:ls},hs=Se.cmpChar,ds=Se.cmpStr,ps=Se.TYPE,fs=ps.Ident,ms=ps.String,gs=ps.Number,bs=ps.Function,ys=ps.Url,ks=ps.Hash,vs=ps.Dimension,ws=ps.Percentage,xs=ps.LeftParenthesis,Ss=ps.LeftSquareBracket,Cs=ps.Comma,As=ps.Delim,zs=function(e){switch(this.scanner.tokenType){case ks:return this.HexColor();case Cs:return e.space=null,e.ignoreWSAfter=!0,this.Operator();case xs:return this.Parentheses(this.readSequence,e.recognizer);case Ss:return this.Brackets(this.readSequence,e.recognizer);case ms:return this.String();case vs:return this.Dimension();case ws:return this.Percentage();case gs:return this.Number();case bs:return ds(this.scanner.source,this.scanner.tokenStart,this.scanner.tokenEnd,"url(")?this.Url():this.Function(this.readSequence,e.recognizer);case ys:return this.Url();case fs:return hs(this.scanner.source,this.scanner.tokenStart,117)&&hs(this.scanner.source,this.scanner.tokenStart+1,43)?this.UnicodeRange():this.Identifier();case As:var t=this.scanner.source.charCodeAt(this.scanner.tokenStart);if(47===t||42===t||43===t||45===t)return this.Operator();35===t&&this.error("Hex or identifier is expected",this.scanner.tokenStart+1)}},Ps={getNode:zs},Ls=Se.TYPE,Ts=Ls.Delim,Es=Ls.Ident,Os=Ls.Dimension,Ds=Ls.Percentage,Rs=Ls.Number,Is=Ls.Hash,Ms=Ls.Colon,Ns=Ls.LeftSquareBracket;var Bs={getNode:function(e){switch(this.scanner.tokenType){case Ns:return this.AttributeSelector();case Is:return this.IdSelector();case Ms:return this.scanner.lookupType(1)===Ms?this.PseudoElementSelector():this.PseudoClassSelector();case Es:return this.TypeSelector();case Rs:case Ds:return this.Percentage();case Os:46===this.scanner.source.charCodeAt(this.scanner.tokenStart)&&this.error("Identifier is expected",this.scanner.tokenStart+1);break;case Ts:switch(this.scanner.source.charCodeAt(this.scanner.tokenStart)){case 43:case 62:case 126:return e.space=null,e.ignoreWSAfter=!0,this.Combinator();case 47:return this.Combinator();case 46:return this.ClassSelector();case 42:case 124:return this.TypeSelector();case 35:return this.IdSelector()}}}},js=function(){this.scanner.skipSC();var e=this.createSingleNodeList(this.IdSelector());return this.scanner.skipSC(),e},_s=Se.TYPE,qs=ea.mode,Ws=_s.Comma,Fs={AtrulePrelude:Ps,Selector:Bs,Value:{getNode:zs,"-moz-element":js,element:js,expression:function(){return this.createSingleNodeList(this.Raw(this.scanner.tokenIndex,null,!1))},var:function(){var e=this.createList();return this.scanner.skipSC(),e.push(this.Identifier()),this.scanner.skipSC(),this.scanner.tokenType===Ws&&(e.push(this.Operator()),e.push(this.parseCustomProperty?this.Value(null):this.Raw(this.scanner.tokenIndex,qs.exclamationMarkOrSemicolon,!1))),e}}},Us=Se.TYPE,Ys=Us.String,Hs=Us.Ident,Vs=Us.Url,Ks=Us.Function,Gs=Us.LeftParenthesis,$s={parse:{prelude:function(){var e=this.createList();switch(this.scanner.skipSC(),this.scanner.tokenType){case Ys:e.push(this.String());break;case Vs:case Ks:e.push(this.Url());break;default:this.error("String or url() is expected")}return this.lookupNonWSType(0)!==Hs&&this.lookupNonWSType(0)!==Gs||(e.push(this.WhiteSpace()),e.push(this.MediaQueryList())),e},block:null}},Qs=Se.TYPE,Xs=Qs.WhiteSpace,Zs=Qs.Comment,Js=Qs.Ident,el=Qs.Function,tl=Qs.Colon,nl=Qs.LeftParenthesis;function rl(){return this.createSingleNodeList(this.Raw(this.scanner.tokenIndex,null,!1))}function al(){return this.scanner.skipSC(),this.scanner.tokenType===Js&&this.lookupNonWSType(1)===tl?this.createSingleNodeList(this.Declaration()):il.call(this)}function il(){var e,t=this.createList(),n=null;this.scanner.skipSC();e:for(;!this.scanner.eof;){switch(this.scanner.tokenType){case Xs:n=this.WhiteSpace();continue;case Zs:this.scanner.next();continue;case el:e=this.Function(rl,this.scope.AtrulePrelude);break;case Js:e=this.Identifier();break;case nl:e=this.Parentheses(al,this.scope.AtrulePrelude);break;default:break e}null!==n&&(t.push(n),n=null),t.push(e)}return t}var ol={parse:function(){return this.createSingleNodeList(this.SelectorList())}},sl={parse:function(){return this.createSingleNodeList(this.Nth(!0))}},ll={parse:function(){return this.createSingleNodeList(this.Nth(!1))}};var cl=Tr(function(){for(var e={},t=0;t<arguments.length;t++){var n=arguments[t];for(var r in n)e[r]=n[r]}return e}(us,{parseContext:{default:"StyleSheet",stylesheet:"StyleSheet",atrule:"Atrule",atrulePrelude:function(e){return this.AtrulePrelude(e.atrule?String(e.atrule):null)},mediaQueryList:"MediaQueryList",mediaQuery:"MediaQuery",rule:"Rule",selectorList:"SelectorList",selector:"Selector",block:function(){return this.Block(!0)},declarationList:"DeclarationList",declaration:"Declaration",value:"Value"},scope:Fs,atrule:{"font-face":{parse:{prelude:null,block:function(){return this.Block(!0)}}},import:$s,media:{parse:{prelude:function(){return this.createSingleNodeList(this.MediaQueryList())},block:function(){return this.Block(!1)}}},page:{parse:{prelude:function(){return this.createSingleNodeList(this.SelectorList())},block:function(){return this.Block(!0)}}},supports:{parse:{prelude:function(){var e=il.call(this);return null===this.getFirstListNode(e)&&this.error("Condition is expected"),e},block:function(){return this.Block(!1)}}}},pseudo:{dir:{parse:function(){return this.createSingleNodeList(this.Identifier())}},has:{parse:function(){return this.createSingleNodeList(this.SelectorList())}},lang:{parse:function(){return this.createSingleNodeList(this.Identifier())}},matches:ol,not:ol,"nth-child":sl,"nth-last-child":sl,"nth-last-of-type":ll,"nth-of-type":ll,slotted:{parse:function(){return this.createSingleNodeList(this.Selector())}}},node:ls},{node:ls})),ul=Object.prototype.hasOwnProperty;function hl(e,t){var n=Object.create(null);if(!Array.isArray(e))return null;for(var r=0;r<e.length;r++){var a=e[r];t&&(a=a.toLowerCase()),n[a]=!0}return n}function dl(e){if(!e)return null;var t=hl(e.tags,!0),n=hl(e.ids),r=hl(e.classes);return null===t&&null===n&&null===r?null:{tags:t,ids:n,classes:r}}var pl={buildIndex:function(e){var t=!1;if(e.scopes&&Array.isArray(e.scopes)){t=Object.create(null);for(var n=0;n<e.scopes.length;n++){var r=e.scopes[n];if(!r||!Array.isArray(r))throw new Error("Wrong usage format");for(var a=0;a<r.length;a++){var i=r[a];if(ul.call(t,i))throw new Error("Class can't be used for several scopes: "+i);t[i]=n+1}}}return{whitelist:dl(e),blacklist:dl(e.blacklist),scopes:t}}},fl={hasNoChildren:function(e){return!e||!e.children||e.children.isEmpty()},isNodeChildrenList:function(e,t){return null!==e&&e.children===t}},ml=cl.keyword,{hasNoChildren:gl}=fl,{isNodeChildrenList:bl}=fl,yl=Object.prototype.hasOwnProperty,kl=cl.walk,{hasNoChildren:vl}=fl;var{isNodeChildrenList:wl}=fl;function xl(e){return"Operator"===e.type&&"+"!==e.value&&"-"!==e.value}var Sl=cl.walk,Cl={Atrule:function(e,t,n){if(e.block&&(null!==this.stylesheet&&(this.stylesheet.firstAtrulesAllowed=!1),gl(e.block)))n.remove(t);else switch(e.name){case"charset":if(gl(e.prelude))return void n.remove(t);if(t.prev)return void n.remove(t);break;case"import":if(null===this.stylesheet||!this.stylesheet.firstAtrulesAllowed)return void n.remove(t);n.prevUntil(t.prev,(function(e){if("Atrule"!==e.type||"import"!==e.name&&"charset"!==e.name)return this.root.firstAtrulesAllowed=!1,n.remove(t),!0}),this);break;default:var r=ml(e.name).basename;"keyframes"!==r&&"media"!==r&&"supports"!==r||(gl(e.prelude)||gl(e.block))&&n.remove(t)}},Comment:function(e,t,n){n.remove(t)},Declaration:function(e,t,n){e.value.children&&e.value.children.isEmpty()&&n.remove(t)},Raw:function(e,t,n){(bl(this.stylesheet,n)||bl(this.block,n))&&n.remove(t)},Rule:function(e,t,n,r){if(vl(e.prelude)||vl(e.block))n.remove(t);else{var a=r.usage;!a||null===a.whitelist&&null===a.blacklist||(function e(t,n){return t.children.each((function(r,a,i){var o=!1;kl(r,(function(r){if(null===this.selector||this.selector===t)switch(r.type){case"SelectorList":null!==this.function&&"not"===this.function.name.toLowerCase()||e(r,n)&&(o=!0);break;case"ClassSelector":null===n.whitelist||null===n.whitelist.classes||yl.call(n.whitelist.classes,r.name)||(o=!0),null!==n.blacklist&&null!==n.blacklist.classes&&yl.call(n.blacklist.classes,r.name)&&(o=!0);break;case"IdSelector":null===n.whitelist||null===n.whitelist.ids||yl.call(n.whitelist.ids,r.name)||(o=!0),null!==n.blacklist&&null!==n.blacklist.ids&&yl.call(n.blacklist.ids,r.name)&&(o=!0);break;case"TypeSelector":"*"!==r.name.charAt(r.name.length-1)&&(null===n.whitelist||null===n.whitelist.tags||yl.call(n.whitelist.tags,r.name.toLowerCase())||(o=!0),null!==n.blacklist&&null!==n.blacklist.tags&&yl.call(n.blacklist.tags,r.name.toLowerCase())&&(o=!0))}})),o&&i.remove(a)})),t.children.isEmpty()}(e.prelude,a),!vl(e.prelude))||n.remove(t)}},TypeSelector:function(e,t,n){if("*"===t.data.name){var r=t.next&&t.next.data.type;"IdSelector"!==r&&"ClassSelector"!==r&&"AttributeSelector"!==r&&"PseudoClassSelector"!==r&&"PseudoElementSelector"!==r||n.remove(t)}},WhiteSpace:function(e,t,n){null!==t.next&&null!==t.prev?wl(this.stylesheet,n)||wl(this.block,n)?n.remove(t):"WhiteSpace"!==t.next.data.type?(xl(t.prev.data)||xl(t.next.data))&&n.remove(t):n.remove(t):n.remove(t)}},Al=cl.keyword,zl=/\\([0-9A-Fa-f]{1,6})(\r\n|[ \t\n\f\r])?|\\./g,Pl=/^(-?\d|--)|[\u0000-\u002c\u002e\u002f\u003A-\u0040\u005B-\u005E\u0060\u007B-\u009f]/;var Ll=cl.List;var Tl=function(e){e.children.each((function(e,t,n){"Identifier"===e.type&&"none"===e.name.toLowerCase()&&(n.head===n.tail?t.data={type:"Number",loc:e.loc,value:"0"}:function(e,t){var n=t.prev,r=t.next;null!==r?"WhiteSpace"!==r.data.type||null!==n&&"WhiteSpace"!==n.data.type||e.remove(r):null!==n&&"WhiteSpace"===n.data.type&&e.remove(n),e.remove(t)}(n,t))}))},El=cl.property,Ol={font:function(e){var t=e.children;t.eachRight((function(e,t){if("Identifier"===e.type)if("bold"===e.name)t.data={type:"Number",loc:e.loc,value:"700"};else if("normal"===e.name){var n=t.prev;n&&"Operator"===n.data.type&&"/"===n.data.value&&this.remove(n),this.remove(t)}else if("medium"===e.name){var r=t.next;r&&"Operator"===r.data.type||this.remove(t)}})),t.each((function(e,t){"WhiteSpace"===e.type&&(t.prev&&t.next&&"WhiteSpace"!==t.next.data.type||this.remove(t))})),t.isEmpty()&&t.insert(t.createItem({type:"Identifier",name:"normal"}))},"font-weight":function(e){var t=e.children.head.data;if("Identifier"===t.type)switch(t.name){case"normal":e.children.head.data={type:"Number",loc:t.loc,value:"400"};break;case"bold":e.children.head.data={type:"Number",loc:t.loc,value:"700"}}},background:function(e){function t(){if(a.length)return a[a.length-1].type}function n(){"WhiteSpace"===t()&&a.pop(),a.length||a.unshift({type:"Number",loc:null,value:"0"},{type:"WhiteSpace",value:" "},{type:"Number",loc:null,value:"0"}),r.push.apply(r,a),a=[]}var r=[],a=[];e.children.each((function(e){if("Operator"===e.type&&","===e.value)return n(),void r.push(e);("Identifier"!==e.type||"transparent"!==e.name&&"none"!==e.name&&"repeat"!==e.name&&"scroll"!==e.name)&&("WhiteSpace"!==e.type||a.length&&"WhiteSpace"!==t())&&a.push(e)})),n(),e.children=(new Ll).fromArray(r)},border:Tl,outline:Tl},Dl=/^(?:\+|(-))?0*(\d*)(?:\.0*|(\.\d*?)0*)?$/,Rl=/^([\+\-])?0*(\d*)(?:\.0*|(\.\d*?)0*)?$/,Il={Dimension:!0,HexColor:!0,Identifier:!0,Number:!0,Raw:!0,UnicodeRange:!0};function Ml(e,t){var n=t&&null!==t.prev&&Il.hasOwnProperty(t.prev.data.type)?Rl:Dl;return""!==(e=String(e).replace(n,"$1$2$3"))&&"-"!==e||(e="0"),e}var Nl=function(e,t){e.value=Ml(e.value,t)},Bl=Nl.pack=Ml,jl={px:!0,mm:!0,cm:!0,in:!0,pt:!0,pc:!0,em:!0,ex:!0,ch:!0,rem:!0,vh:!0,vw:!0,vmin:!0,vmax:!0,vm:!0},_l=cl.lexer,ql=Nl.pack,Wl=new Set(["width","min-width","max-width","height","min-height","max-height","flex","-ms-flex"]),Fl=new RegExp("^((\\\\[0-9a-f]{1,6}(\\r\\n|[ \\n\\r\\t\\f])?|\\\\[^\\n\\r\\f0-9a-fA-F])|[^\"'\\(\\)\\\\\\s\0\b\v-])*$","i"),Ul=cl.lexer,Yl=Nl.pack,Hl={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgrey:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",grey:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"639",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},Vl={8e5:"maroon",800080:"purple",808e3:"olive",808080:"gray","00ffff":"cyan",f0ffff:"azure",f5f5dc:"beige",ffe4c4:"bisque","000000":"black","0000ff":"blue",a52a2a:"brown",ff7f50:"coral",ffd700:"gold","008000":"green","4b0082":"indigo",fffff0:"ivory",f0e68c:"khaki","00ff00":"lime",faf0e6:"linen","000080":"navy",ffa500:"orange",da70d6:"orchid",cd853f:"peru",ffc0cb:"pink",dda0dd:"plum",f00:"red",ff0000:"red",fa8072:"salmon",a0522d:"sienna",c0c0c0:"silver",fffafa:"snow",d2b48c:"tan","008080":"teal",ff6347:"tomato",ee82ee:"violet",f5deb3:"wheat",ffffff:"white",ffff00:"yellow"};function Kl(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Gl(e,t,n,r){var a,i,o;if(0===t)a=i=o=n;else{var s=n<.5?n*(1+t):n+t-n*t,l=2*n-s;a=Kl(l,s,e+1/3),i=Kl(l,s,e),o=Kl(l,s,e-1/3)}return[Math.round(255*a),Math.round(255*i),Math.round(255*o),r]}function $l(e){return 1===(e=e.toString(16)).length?"0"+e:e}function Ql(e,t,n){for(var r=e.head,a=[],i=!1;null!==r;){var o=r.data,s=o.type;switch(s){case"Number":case"Percentage":if(i)return;i=!0,a.push({type:s,value:Number(o.value)});break;case"Operator":if(","===o.value){if(!i)return;i=!1}else if(i||"+"!==o.value)return;break;default:return}r=r.next}if(a.length===t){if(4===a.length){if("Number"!==a[3].type)return;a[3].type="Alpha"}if(n){if(a[0].type!==a[1].type||a[0].type!==a[2].type)return}else{if("Number"!==a[0].type||"Percentage"!==a[1].type||"Percentage"!==a[2].type)return;a[0].type="Angle"}return a.map((function(e){var t=Math.max(0,e.value);switch(e.type){case"Number":t=Math.min(t,255);break;case"Percentage":if(t=Math.min(t,100)/100,!n)return t;t*=255;break;case"Angle":return(t%360+360)%360/360;case"Alpha":return Math.min(t,1)}return Math.round(t)}))}}function Xl(e,t){var n=e.value.toLowerCase();6===n.length&&n[0]===n[1]&&n[2]===n[3]&&n[4]===n[5]&&(n=n[0]+n[2]+n[4]),Vl[n]?t.data={type:"Identifier",loc:e.loc,name:Vl[n]}:e.value=n}var Zl={compressFunction:function(e,t,n){var r,a=e.name;if("rgba"===a||"hsla"===a){if(!(r=Ql(e.children,4,"rgba"===a)))return;if("hsla"===a&&(r=Gl.apply(null,r),e.name="rgba"),0===r[3]){var i=this.function&&this.function.name;if(0===r[0]&&0===r[1]&&0===r[2]||!/^(?:to|from|color-stop)$|gradient$/i.test(i))return void(t.data={type:"Identifier",loc:e.loc,name:"transparent"})}if(1!==r[3])return void e.children.each((function(e,t,n){"Operator"!==e.type?t.data={type:"Number",loc:e.loc,value:Yl(r.shift(),null)}:","!==e.value&&n.remove(t)}));a="rgb"}if("hsl"===a){if(!(r=r||Ql(e.children,3,!1)))return;r=Gl.apply(null,r),a="rgb"}if("rgb"===a){if(!(r=r||Ql(e.children,3,!0)))return;var o=t.next;o&&"WhiteSpace"!==o.data.type&&n.insert(n.createItem({type:"WhiteSpace",value:" "}),o),t.data={type:"HexColor",loc:e.loc,value:$l(r[0])+$l(r[1])+$l(r[2])},Xl(t.data,t)}},compressIdent:function(e,t){if(null!==this.declaration){var n=e.name.toLowerCase();if(Hl.hasOwnProperty(n)&&Ul.matchDeclaration(this.declaration).isType(e,"color")){var r=Hl[n];r.length+1<=n.length?t.data={type:"HexColor",loc:e.loc,value:r}:("grey"===n&&(n="gray"),e.name=n)}}},compressHex:Xl},Jl=cl.walk,ec={Atrule:function(e){"keyframes"===Al(e.name).basename&&function(e){e.block.children.each((function(e){e.prelude.children.each((function(e){e.children.each((function(e,t){"Percentage"===e.type&&"100"===e.value?t.data={type:"TypeSelector",loc:e.loc,name:"to"}:"TypeSelector"===e.type&&"from"===e.name&&(t.data={type:"Percentage",loc:e.loc,value:"0"})}))}))}))}(e)},AttributeSelector:function(e){var t=e.value;if(t&&"String"===t.type){var n=t.value.replace(/^(.)(.*)\1$/,"$2");(function(e){if(""!==e&&"-"!==e)return e=e.replace(zl,"a"),!Pl.test(e)})(n)&&(e.value={type:"Identifier",loc:t.loc,name:n})}},Value:function(e){if(this.declaration){var t=El(this.declaration.property);Ol.hasOwnProperty(t.basename)&&Ol[t.basename](e)}},Dimension:function(e,t){var n=Bl(e.value,t);if(e.value=n,"0"===n&&null!==this.declaration&&null===this.atrulePrelude){var r=e.unit.toLowerCase();if(!jl.hasOwnProperty(r))return;if("-ms-flex"===this.declaration.property||"flex"===this.declaration.property)return;if(this.function&&"calc"===this.function.name)return;t.data={type:"Number",loc:e.loc,value:n}}},Percentage:function(e,t){e.value=ql(e.value,t),"0"===e.value&&this.declaration&&!Wl.has(this.declaration.property)&&(t.data={type:"Number",loc:e.loc,value:e.value},_l.matchDeclaration(this.declaration).isType(t.data,"length")||(t.data=e))},Number:Nl,String:function(e){var t=e.value;t=t.replace(/\\(\r\n|\r|\n|\f)/g,""),e.value=t},Url:function(e){var t=e.value;if("String"===t.type){var n=t.value[0],r=t.value.substr(1,t.value.length-2);r=r.replace(/\\\\/g,"/"),Fl.test(r)?e.value={type:"Raw",loc:e.value.loc,value:r}:e.value.value=-1===r.indexOf('"')?'"'+r+'"':n+r+n}},HexColor:Zl.compressHex,Identifier:Zl.compressIdent,Function:Zl.compressFunction},tc=cl.generate;function nc(){this.seed=0,this.map=Object.create(null)}nc.prototype.resolve=function(e){var t=this.map[e];return t||(t=++this.seed,this.map[e]=t),t};var rc=cl.generate,ac={"first-letter":!0,"first-line":!0,after:!0,before:!0},ic={link:!0,visited:!0,hover:!0,active:!0,"first-letter":!0,"first-line":!0,after:!0,before:!0},oc=cl.keyword,sc=cl.walk,lc=cl.generate,cc=function(e,t){var n,r=(n=new nc,function(e){var t=tc(e);return e.id=n.resolve(t),e.length=t.length,e.fingerprint=null,e});return sc(e,{visit:"Rule",enter:function(e){e.block.children.each(r),function(e,t){var n=Object.create(null),r=!1;e.prelude.children.each((function(e){var a="*",i=0;e.children.each((function(o){switch(o.type){case"ClassSelector":if(t&&t.scopes){var s=t.scopes[o.name]||0;if(0!==i&&s!==i)throw new Error("Selector can't has classes from different scopes: "+rc(e));i=s}break;case"PseudoClassSelector":var l=o.name.toLowerCase();ic.hasOwnProperty(l)||(n[l]=!0,r=!0);break;case"PseudoElementSelector":l=o.name.toLowerCase();ac.hasOwnProperty(l)||(n[l]=!0,r=!0);break;case"TypeSelector":a=o.name.toLowerCase();break;case"AttributeSelector":o.flags&&(n["["+o.flags.toLowerCase()+"]"]=!0,r=!0);break;case"WhiteSpace":case"Combinator":a="*"}})),e.compareMarker=function(e){var t=0,n=0,r=0;return e.children.each((function e(a){switch(a.type){case"SelectorList":case"Selector":a.children.each(e);break;case"IdSelector":t++;break;case"ClassSelector":case"AttributeSelector":n++;break;case"PseudoClassSelector":switch(a.name.toLowerCase()){case"not":a.children.each(e);break;case"before":case"after":case"first-line":case"first-letter":r++;break;default:n++}break;case"PseudoElementSelector":r++;break;case"TypeSelector":"*"!==a.name.charAt(a.name.length-1)&&r++}})),[t,n,r]}(e).toString(),e.id=null,e.id=rc(e),i&&(e.compareMarker+=":"+i),"*"!==a&&(e.compareMarker+=","+a)})),e.pseudoSignature=r&&Object.keys(n).sort().join(",")}(e,t.usage)}}),sc(e,{visit:"Atrule",enter:function(e){e.prelude&&(e.prelude.id=null,e.prelude.id=lc(e.prelude)),"keyframes"===oc(e.name).basename&&(e.block.avoidRulesMerge=!0,e.block.children.each((function(e){e.prelude.children.each((function(e){e.compareMarker=e.id}))})))}}),{declaration:r}},uc=cl.List,hc=cl.keyword,dc=Object.prototype.hasOwnProperty,pc=cl.walk;function fc(e,t,n,r){var a=t.data,i=hc(a.name).basename,o=a.name.toLowerCase()+"/"+(a.prelude?a.prelude.id:null);dc.call(e,i)||(e[i]=Object.create(null)),r&&delete e[i][o],dc.call(e[i],o)||(e[i][o]=new uc),e[i][o].append(n.remove(t))}function mc(e){return"Atrule"===e.type&&"media"===e.name}function gc(e,t,n){if(mc(e)){var r=t.prev&&t.prev.data;r&&mc(r)&&e.prelude&&r.prelude&&e.prelude.id===r.prelude.id&&(r.block.children.appendList(e.block.children),n.remove(t))}}var bc=function(e,t){!function(e,t){var n=Object.create(null),r=null;for(var a in e.children.each((function(e,a,i){if("Atrule"===e.type){var o=hc(e.name).basename;switch(o){case"keyframes":return void fc(n,a,i,!0);case"media":if(t.forceMediaMerge)return void fc(n,a,i,!1)}null===r&&"charset"!==o&&"import"!==o&&(r=a)}else null===r&&(r=a)})),n)for(var i in n[a])e.children.insertList(n[a][i],"media"===a?null:r)}(e,t),pc(e,{visit:"Atrule",reverse:!0,enter:gc})},yc=Object.prototype.hasOwnProperty;function kc(e,t){for(var n=e.head;null!==n;){for(var r=t.head;null!==r;){if(n.data.compareMarker===r.data.compareMarker)return!0;r=r.next}n=n.next}return!1}var vc={isEqualSelectors:function(e,t){for(var n=e.head,r=t.head;null!==n&&null!==r&&n.data.id===r.data.id;)n=n.next,r=r.next;return null===n&&null===r},isEqualDeclarations:function(e,t){for(var n=e.head,r=t.head;null!==n&&null!==r&&n.data.id===r.data.id;)n=n.next,r=r.next;return null===n&&null===r},compareDeclarations:function(e,t){for(var n={eq:[],ne1:[],ne2:[],ne2overrided:[]},r=Object.create(null),a=Object.create(null),i=t.head;i;i=i.next)a[i.data.id]=!0;for(i=e.head;i;i=i.next){(o=i.data).fingerprint&&(r[o.fingerprint]=o.important),a[o.id]?(a[o.id]=!1,n.eq.push(o)):n.ne1.push(o)}for(i=t.head;i;i=i.next){var o;a[(o=i.data).id]&&((!yc.call(r,o.fingerprint)||!r[o.fingerprint]&&o.important)&&n.ne2.push(o),n.ne2overrided.push(o))}return n},addSelectors:function(e,t){return t.each((function(t){for(var n=t.id,r=e.head;r;){var a=r.data.id;if(a===n)return;if(a>n)break;r=r.next}e.insert(e.createItem(t),r)})),e},hasSimilarSelectors:kc,unsafeToSkipNode:function e(t){switch(t.type){case"Rule":return kc(t.prelude.children,this);case"Atrule":if(t.block)return t.block.children.some(e,this);break;case"Declaration":return!1}return!0}},wc=cl.walk;function xc(e,t,n){var r=e.prelude.children,a=e.block.children;n.prevUntil(t.prev,(function(i){if("Rule"!==i.type)return vc.unsafeToSkipNode.call(r,i);var o=i.prelude.children,s=i.block.children;if(e.pseudoSignature===i.pseudoSignature){if(vc.isEqualSelectors(o,r))return s.appendList(a),n.remove(t),!0;if(vc.isEqualDeclarations(a,s))return vc.addSelectors(o,r),n.remove(t),!0}return vc.hasSimilarSelectors(r,o)}))}var Sc=cl.List,Cc=cl.walk;function Ac(e,t,n){for(var r=e.prelude.children;r.head!==r.tail;){var a=new Sc;a.insert(r.remove(r.head)),n.insert(n.createItem({type:"Rule",loc:e.loc,prelude:{type:"SelectorList",loc:e.prelude.loc,children:a},block:{type:"Block",loc:e.block.loc,children:e.block.children.copy()},pseudoSignature:e.pseudoSignature}),t)}}var zc=cl.List,Pc=cl.generate,Lc=cl.walk,Tc=["top","right","bottom","left"],Ec={"margin-top":"top","margin-right":"right","margin-bottom":"bottom","margin-left":"left","padding-top":"top","padding-right":"right","padding-bottom":"bottom","padding-left":"left","border-top-color":"top","border-right-color":"right","border-bottom-color":"bottom","border-left-color":"left","border-top-width":"top","border-right-width":"right","border-bottom-width":"bottom","border-left-width":"left","border-top-style":"top","border-right-style":"right","border-bottom-style":"bottom","border-left-style":"left"},Oc={margin:"margin","margin-top":"margin","margin-right":"margin","margin-bottom":"margin","margin-left":"margin",padding:"padding","padding-top":"padding","padding-right":"padding","padding-bottom":"padding","padding-left":"padding","border-color":"border-color","border-top-color":"border-color","border-right-color":"border-color","border-bottom-color":"border-color","border-left-color":"border-color","border-width":"border-width","border-top-width":"border-width","border-right-width":"border-width","border-bottom-width":"border-width","border-left-width":"border-width","border-style":"border-style","border-top-style":"border-style","border-right-style":"border-style","border-bottom-style":"border-style","border-left-style":"border-style"};function Dc(e){this.name=e,this.loc=null,this.iehack=void 0,this.sides={top:null,right:null,bottom:null,left:null}}function Rc(e,t,n,r){var a=e.block.children,i=e.prelude.children.first().id;return e.block.children.eachRight((function(e,o){var s=e.property;if(Oc.hasOwnProperty(s)){var l,c,u=Oc[s];r&&i!==r||u in t&&(c=2,l=t[u]),l&&l.add(s,e)||(c=1,(l=new Dc(u)).add(s,e))?(t[u]=l,n.push({operation:c,block:a,item:o,shorthand:l}),r=i):r=null}})),r}Dc.prototype.getValueSequence=function(e,t){var n=[],r="";return!(e.value.children.some((function(t){var a=!1;switch(t.type){case"Identifier":switch(t.name){case"\\0":case"\\9":return void(r=t.name);case"inherit":case"initial":case"unset":case"revert":a=t.name}break;case"Dimension":switch(t.unit){case"rem":case"vw":case"vh":case"vmin":case"vmax":case"vm":a=t.unit}break;case"HexColor":case"Number":case"Percentage":break;case"Function":a=t.name;break;case"WhiteSpace":return!1;default:return!0}n.push({node:t,special:a,important:e.important})}))||n.length>t)&&(("string"!=typeof this.iehack||this.iehack===r)&&(this.iehack=r,n))},Dc.prototype.canOverride=function(e,t){var n=this.sides[e];return!n||t.important&&!n.important},Dc.prototype.add=function(e,t){return!!function(){var n=this.sides,r=Ec[e];if(r){if(r in n==!1)return!1;if(!(i=this.getValueSequence(t,1))||!i.length)return!1;for(var a in n)if(null!==n[a]&&n[a].special!==i[0].special)return!1;return!this.canOverride(r,i[0])||(n[r]=i[0],!0)}if(e===this.name){var i;if(!(i=this.getValueSequence(t,4))||!i.length)return!1;switch(i.length){case 1:i[1]=i[0],i[2]=i[0],i[3]=i[0];break;case 2:i[2]=i[0],i[3]=i[1];break;case 3:i[3]=i[1]}for(var o=0;o<4;o++)for(var a in n)if(null!==n[a]&&n[a].special!==i[o].special)return!1;for(o=0;o<4;o++)this.canOverride(Tc[o],i[o])&&(n[Tc[o]]=i[o]);return!0}}.call(this)&&(this.loc||(this.loc=t.loc),!0)},Dc.prototype.isOkToMinimize=function(){var e=this.sides.top,t=this.sides.right,n=this.sides.bottom,r=this.sides.left;if(e&&t&&n&&r){var a=e.important+t.important+n.important+r.important;return 0===a||4===a}return!1},Dc.prototype.getValue=function(){var e=new zc,t=this.sides,n=[t.top,t.right,t.bottom,t.left],r=[Pc(t.top.node),Pc(t.right.node),Pc(t.bottom.node),Pc(t.left.node)];r[3]===r[1]&&(n.pop(),r[2]===r[0]&&(n.pop(),r[1]===r[0]&&n.pop()));for(var a=0;a<n.length;a++)a&&e.appendData({type:"WhiteSpace",value:" "}),e.appendData(n[a].node);return this.iehack&&(e.appendData({type:"WhiteSpace",value:" "}),e.appendData({type:"Identifier",loc:null,name:this.iehack})),{type:"Value",loc:null,children:e}},Dc.prototype.getDeclaration=function(){return{type:"Declaration",loc:this.loc,important:this.sides.top.important,property:this.name,value:this.getValue()}};var Ic=function(e,t){var n={},r=[];Lc(e,{visit:"Rule",reverse:!0,enter:function(e){var t,a,i=this.block||this.stylesheet,o=(e.pseudoSignature||"")+"|"+e.prelude.children.first().id;n.hasOwnProperty(i.id)?t=n[i.id]:(t={lastShortSelector:null},n[i.id]=t),t.hasOwnProperty(o)?a=t[o]:(a={},t[o]=a),t.lastShortSelector=Rc.call(this,e,a,r,t.lastShortSelector)}}),function(e,t){e.forEach((function(e){var n=e.shorthand;n.isOkToMinimize()&&(1===e.operation?e.item.data=t(n.getDeclaration()):e.block.remove(e.item))}))}(r,t.declaration)},Mc=cl.property,Nc=cl.keyword,Bc=cl.walk,jc=cl.generate,_c=1,qc={src:1},Wc={display:/table|ruby|flex|-(flex)?box$|grid|contents|run-in/i,"text-align":/^(start|end|match-parent|justify-all)$/i},Fc=["auto","crosshair","default","move","text","wait","help","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","pointer","progress","not-allowed","no-drop","vertical-text","all-scroll","col-resize","row-resize"],Uc=["static","relative","absolute","fixed"],Yc={"border-width":["border"],"border-style":["border"],"border-color":["border"],"border-top":["border"],"border-right":["border"],"border-bottom":["border"],"border-left":["border"],"border-top-width":["border-top","border-width","border"],"border-right-width":["border-right","border-width","border"],"border-bottom-width":["border-bottom","border-width","border"],"border-left-width":["border-left","border-width","border"],"border-top-style":["border-top","border-style","border"],"border-right-style":["border-right","border-style","border"],"border-bottom-style":["border-bottom","border-style","border"],"border-left-style":["border-left","border-style","border"],"border-top-color":["border-top","border-color","border"],"border-right-color":["border-right","border-color","border"],"border-bottom-color":["border-bottom","border-color","border"],"border-left-color":["border-left","border-color","border"],"margin-top":["margin"],"margin-right":["margin"],"margin-bottom":["margin"],"margin-left":["margin"],"padding-top":["padding"],"padding-right":["padding"],"padding-bottom":["padding"],"padding-left":["padding"],"font-style":["font"],"font-variant":["font"],"font-weight":["font"],"font-size":["font"],"font-family":["font"],"list-style-type":["list-style"],"list-style-position":["list-style"],"list-style-image":["list-style"]};function Hc(e,t,n){var r=Mc(e).basename;if("background"===r)return e+":"+jc(t.value);var a=t.id,i=n[a];if(!i){switch(t.value.type){case"Value":var o="",s="",l={},c=!1;t.value.children.each((function e(t){switch(t.type){case"Value":case"Brackets":case"Parentheses":t.children.each(e);break;case"Raw":c=!0;break;case"Identifier":var n=t.name;o||(o=Nc(n).vendor),/\\[09]/.test(n)&&(s=RegExp.lastMatch),"cursor"===r?-1===Fc.indexOf(n)&&(l[n]=!0):"position"===r?-1===Uc.indexOf(n)&&(l[n]=!0):Wc.hasOwnProperty(r)&&Wc[r].test(n)&&(l[n]=!0);break;case"Function":n=t.name;if(o||(o=Nc(n).vendor),"rect"===n)t.children.some((function(e){return"Operator"===e.type&&","===e.value}))||(n="rect-backward");l[n+"()"]=!0,t.children.each(e);break;case"Dimension":var a=t.unit;switch(/\\[09]/.test(a)&&(s=RegExp.lastMatch),a){case"rem":case"vw":case"vh":case"vmin":case"vmax":case"vm":l[a]=!0}}})),i=c?"!"+_c++:"!"+Object.keys(l).sort()+"|"+s+o;break;case"Raw":i="!"+t.value.value;break;default:i=jc(t.value)}n[a]=i}return e+i}function Vc(e,t,n,r,a){var i=e.block.children;i.eachRight((function(e,t){var n,o=e.property,s=Hc(o,e,a);(n=r[s])&&!qc.hasOwnProperty(o)?e.important&&!n.item.data.important?(r[s]={block:i,item:t},n.block.remove(n.item)):i.remove(t):(n=function(e,t,n){var r=Mc(t.property);if(Yc.hasOwnProperty(r.basename))for(var a=Yc[r.basename],i=0;i<a.length;i++){var o=Hc(r.prefix+a[i],t,n),s=e.hasOwnProperty(o)?e[o]:null;if(s&&(!t.important||s.item.data.important))return s}}(r,e,a))?i.remove(t):(e.fingerprint=s,r[s]={block:i,item:t})})),i.isEmpty()&&n.remove(t)}var Kc=cl.walk;function Gc(e,t,n){var r=e.prelude.children,a=e.block.children,i=r.first().compareMarker,o={};n.nextUntil(t.next,(function(t,s){if("Rule"!==t.type)return vc.unsafeToSkipNode.call(r,t);if(e.pseudoSignature!==t.pseudoSignature)return!0;var l=t.prelude.children.head,c=t.block.children,u=l.data.compareMarker;if(u in o)return!0;if(r.head===r.tail&&r.first().id===l.data.id)return a.appendList(c),void n.remove(s);if(vc.isEqualDeclarations(a,c)){var h=l.data.id;return r.some((function(e,t){var n=e.id;return h<n?(r.insert(l,t),!0):t.next?void 0:(r.insert(l),!0)})),void n.remove(s)}if(u===i)return!0;o[u]=!0}))}var $c=cl.List,Qc=cl.walk;function Xc(e){var t=0;return e.each((function(e){t+=e.id.length+1})),t-1}function Zc(e){for(var t=0,n=0;n<e.length;n++)t+=e[n].length;return t+e.length-1}function Jc(e,t,n){var r=null!==this.block&&this.block.avoidRulesMerge,a=e.prelude.children,i=e.block,o=Object.create(null),s=!0,l=!0;n.prevUntil(t.prev,(function(c,u){var h=c.block,d=c.type;if("Rule"!==d){var p=vc.unsafeToSkipNode.call(a,c);return!p&&"Atrule"===d&&h&&Qc(h,{visit:"Rule",enter:function(e){e.prelude.children.each((function(e){o[e.compareMarker]=!0}))}}),p}var f=c.prelude.children;if(e.pseudoSignature!==c.pseudoSignature)return!0;if(!(l=!f.some((function(e){return e.compareMarker in o})))&&!s)return!0;if(s&&vc.isEqualSelectors(f,a))return h.children.appendList(i.children),n.remove(t),!0;var m=vc.compareDeclarations(i.children,h.children);if(m.eq.length){if(!m.ne1.length&&!m.ne2.length)return l&&(vc.addSelectors(a,f),n.remove(u)),!0;if(!r)if(m.ne1.length&&!m.ne2.length){var g=Xc(a),b=Zc(m.eq);s&&g<b&&(vc.addSelectors(f,a),i.children=(new $c).fromArray(m.ne1))}else if(!m.ne1.length&&m.ne2.length){g=Xc(f),b=Zc(m.eq);l&&g<b&&(vc.addSelectors(a,f),h.children=(new $c).fromArray(m.ne2))}else{var y={type:"SelectorList",loc:null,children:vc.addSelectors(f.copy(),a)},k=Xc(y.children)+2;if((b=Zc(m.eq))>=k){var v=n.createItem({type:"Rule",loc:null,prelude:y,block:{type:"Block",loc:null,children:(new $c).fromArray(m.eq)},pseudoSignature:e.pseudoSignature});return i.children=(new $c).fromArray(m.ne1),h.children=(new $c).fromArray(m.ne2overrided),s?n.insert(v,u):n.insert(v,t),!0}}}s&&(s=!f.some((function(e){return a.some((function(t){return t.compareMarker===e.compareMarker}))}))),f.each((function(e){o[e.compareMarker]=!0}))}))}var eu=function(e,t){var n=cc(e,t);t.logger("prepare",e),bc(e,t),t.logger("mergeAtrule",e),function(e){wc(e,{visit:"Rule",enter:xc})}(e),t.logger("initialMergeRuleset",e),function(e){Cc(e,{visit:"Rule",reverse:!0,enter:Ac})}(e),t.logger("disjoinRuleset",e),Ic(e,n),t.logger("restructShorthand",e),function(e){var t={},n=Object.create(null);Bc(e,{visit:"Rule",reverse:!0,enter:function(e,r,a){var i,o,s=this.block||this.stylesheet,l=(e.pseudoSignature||"")+"|"+e.prelude.children.first().id;t.hasOwnProperty(s.id)?i=t[s.id]:(i={},t[s.id]=i),i.hasOwnProperty(l)?o=i[l]:(o={},i[l]=o),Vc.call(this,e,r,a,o,n)}})}(e),t.logger("restructBlock",e),function(e){Kc(e,{visit:"Rule",enter:Gc})}(e),t.logger("mergeRuleset",e),function(e){Qc(e,{visit:"Rule",reverse:!0,enter:Jc})}(e),t.logger("restructRuleset",e)},tu=cl.List,nu=cl.clone,ru=cl.walk;function au(e,t){var n,r=new tu,a=!1;return e.nextUntil(e.head,(function(e,i,o){if("Comment"===e.type)return t&&"!"===e.value.charAt(0)?!(!a&&!n)||(o.remove(i),void(n=e)):void o.remove(i);"WhiteSpace"!==e.type&&(a=!0),r.insert(o.remove(i))})),{comment:n,stylesheet:{type:"StyleSheet",loc:null,children:r}}}function iu(e,t,n,r){r.logger("Compress block #"+n,null,!0);var a=1;return"StyleSheet"===e.type&&(e.firstAtrulesAllowed=t,e.id=a++),ru(e,{visit:"Atrule",enter:function(e){null!==e.block&&(e.block.id=a++)}}),r.logger("init",e),function(e,t){Sl(e,{leave:function(e,n,r){Cl.hasOwnProperty(e.type)&&Cl[e.type].call(this,e,n,r,t)}})}(e,r),r.logger("clean",e),function(e){Jl(e,{leave:function(e,t,n){ec.hasOwnProperty(e.type)&&ec[e.type].call(this,e,t,n)}})}(e),r.logger("replace",e),r.restructuring&&eu(e,r),e}function ou(e){return"restructure"in e?e.restructure:!("restructuring"in e)||e.restructuring}var su=function(e,t){e=e||{type:"StyleSheet",loc:null,children:new tu};var n,r,a,i,o={logger:"function"==typeof(t=t||{}).logger?t.logger:function(){},restructuring:ou(t),forceMediaMerge:Boolean(t.forceMediaMerge),usage:!!t.usage&&pl.buildIndex(t.usage)},s=function(e){var t="comments"in e?e.comments:"exclamation";return"boolean"==typeof t?t=!!t&&"exclamation":"exclamation"!==t&&"first-exclamation"!==t&&(t=!1),t}(t),l=!0,c=new tu,u=1;t.clone&&(e=nu(e)),"StyleSheet"===e.type?(n=e.children,e.children=c):(i=e,n=(new tu).appendData({type:"Rule",loc:null,prelude:{type:"SelectorList",loc:null,children:(new tu).appendData({type:"Selector",loc:null,children:(new tu).appendData({type:"TypeSelector",loc:null,name:"x"})})},block:i}));do{if(iu((r=au(n,Boolean(s))).stylesheet,l,u++,o),a=r.stylesheet.children,r.comment&&(c.isEmpty()||c.insert(tu.createItem({type:"Raw",value:"\n"})),c.insert(tu.createItem(r.comment)),a.isEmpty()||c.insert(tu.createItem({type:"Raw",value:"\n"}))),l&&!a.isEmpty()){var h=a.last();("Atrule"!==h.type||"import"!==h.name&&"charset"!==h.name)&&(l=!1)}"exclamation"!==s&&(s=!1),c.appendList(a)}while(!n.isEmpty());return{ast:e}},lu={version:"4.0.3"},cu=Zn(Object.freeze({__proto__:null,version:"4.0.3",default:lu})),uu=cl.parse,hu=cl.generate;function du(e,t,n,r){return t.debug&&console.error("## "+e+" done in %d ms\n",Date.now()-n),r}function pu(e){var t,n;return"function"!=typeof(e=function(e){var t={};for(var n in e)t[n]=e[n];return t}(e)).logger&&e.debug&&(e.logger=(t=e.debug,function(e,r){var a=e;if(r&&(a="["+((Date.now()-n)/1e3).toFixed(3)+"s] "+a),t>1&&r){var i=hu(r);2===t&&i.length>256&&(i=i.substr(0,256)+"..."),a+="\n  "+i+"\n"}console.error(a),n=Date.now()})),e}function fu(e,t,n){Array.isArray(n)||(n=[n]),n.forEach((function(n){n(e,t)}))}function mu(e,t,n){var r=(n=n||{}).filename||"<unknown>",a=du("parsing",n,Date.now(),uu(t,{context:e,filename:r,positions:Boolean(n.sourceMap)}));n.beforeCompress&&du("beforeCompress",n,Date.now(),fu(a,n,n.beforeCompress));var i,o=du("compress",n,Date.now(),su(a,pu(n)));return n.afterCompress&&du("afterCompress",n,Date.now(),fu(o,n,n.afterCompress)),n.sourceMap?du("generate(sourceMap: true)",n,Date.now(),((i=hu(o.ast,{sourceMap:!0})).map._file=r,i.map.setSourceContent(r,t),i)):du("generate",n,Date.now(),{css:hu(o.ast),map:null})}var gu={version:cu.version,minify:function(e,t){return mu("stylesheet",e,t)},minifyBlock:function(e,t){return mu("declarationList",e,t)},syntax:Object.assign({compress:su},cl)},bu=gu.version,yu=gu.minify,ku=gu.minifyBlock,vu=gu.syntax;e.default=gu,e.minify=yu,e.minifyBlock=ku,e.syntax=vu,e.version=bu,Object.defineProperty(e,"__esModule",{value:!0})}));