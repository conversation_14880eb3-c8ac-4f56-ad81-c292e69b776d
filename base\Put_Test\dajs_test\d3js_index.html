<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
<script src="https://d3js.org/d3.v5.min.js"></script>
<script type="module">
    const width=1000, height = 500;
    const svg = d3.select("body").append("svg").attr('width', width).attr('height', height);

    // 选取颜色色系 
    const color = d3.scaleOrdinal(d3.schemeTableau10)

    async function load(){
        // 加载数据
        const nodes = await d3.json("https://cdn.xiaxiang.tech/E%3A%2Ffrondend%2Ftravel%2Fcss%2F%E6%AF%8F%E6%97%A5CSS%2F2020__12%2Fnode_data.json");
        const edges = await d3.json("https://cdn.xiaxiang.tech/E%3A%2Ffrondend%2Ftravel%2Fcss%2F%E6%AF%8F%E6%97%A5CSS%2F2020__12%2Fnode_data.json");

        // 绑定数据并形成圆
        const node = svg.selectAll("circle")
            .data(nodes)
            .enter()
            .append('circle')
            .attr("cx", d=>d.x)
            .attr("cy", d=>d.y)
            .attr("r", d=>d.r)
            .attr("fill", (d, i)=>color(i));

        // 绑定数据并形成线
        const lines = svg.selectAll('lines')
            .data(edges)
            .enter()
            .append('line')
            .attr('x1',d=>nodes[d.source].x)
            .attr('x2',d=>nodes[d.target].x)
            .attr('y1',d=>nodes[d.source].y)
            .attr('y2',d=>nodes[d.target].y)
            .attr('stroke', "#5d5d66")
            .attr('stroke-width', 2)
        console.log('x :>> ',nodes[d.source]);
        const centerForce = {
            x: 700,
            y: 250
        }

        // 创建中心力的圆
        svg.append('circle')
            .attr('cx', centerForce.x)
            .attr('cy', centerForce.y)
            .attr('r', 100)
            .attr('stroke', '#6b6f80')
            .attr('stroke-width', 3)
            .attr('fill', "#00000000")

        // 力的模拟
        const sim = d3.forceSimulation(nodes)
            // 指定位置的拉力
            // .force('x', d3.forceX(width/2))
            // .force('y', d3.forceY(height/2))
            // .force('x', d3.forceX(width/2).strength(0.06))
            // .force('y', d3.forceY(height/2).strength(0.06))
            // 碰撞力
            // .force('collide', d3.forceCollide().radius(10))
            .force('collide', d3.forceCollide().radius(d=>d.r+1))
            // 原子力
            .force('charge', d3.forceManyBody().strength(7))
            // 链接力
            // .force('link', d3.forceLink(edges))
            // 径向力
            // .force('radial', d3.forceRadial(240, width/2, height/2))
            // .force('radial', d3.forceRadial(d => d.r+200, width/2, height/2))
            // 中心力，规划
            .force('center', d3.forceCenter(centerForce.x, centerForce.y))
        // .stop()
        //  .tick(100);


        sim.on("tick", () => {
            // sim 直接改变了
            node.attr("cx", function(d){ return d.x;})
                .attr("cy", function(d){ return d.y;});


            lines.attr('x1',d=>d.source.x)
                .attr('y1',d=>d.source.y)
                .attr('x2',d=>d.target.x)
                .attr('y2',d=>d.target.y);
        })
    }
    load()
</script>
</body>
</html>