<div class="apiDetail">
<div>
	<h2><span>Boolean</span><span class="path">setting.view.</span>selectedMulti</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.core</span> 核心 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>设置是否允许同时选中多个节点。</p>
			<p>默认值: true</p>
		</div>
	</div>
	<h3>Boolean 格式说明</h3>
	<div class="desc">
	<p> true / false 分别表示 支持 / 不支持 同时选中多个节点</p>
	<p class="highlight_red">1、设置为 true时，按下 Ctrl 或 Cmd 键可以选中多个节点</p>
	<p class="highlight_red">2、设置为 true / false 都不影响按下 Ctrl 或 Cmd 键可以让已选中的节点取消选中状态（ 取消选中状态可以参考 setting.view.autoCancelSelected ）</p>
	</div>
	<h3>setting 举例</h3>
	<h4>1. 禁止多点同时选中的功能</h4>
	<pre xmlns=""><code>var setting = {
	view: {
		selectedMulti: false
	}
};
......</code></pre>
</div>
</div>