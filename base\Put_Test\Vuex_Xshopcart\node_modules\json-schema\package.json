{"name": "json-schema", "version": "0.2.3", "author": "<PERSON>", "description": "JSON Schema validation and specifications", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>"}], "keywords": ["json", "schema"], "licenses": [{"type": "AFLv2.1", "url": "http://trac.dojotoolkit.org/browser/dojo/trunk/LICENSE#L43"}, {"type": "BSD", "url": "http://trac.dojotoolkit.org/browser/dojo/trunk/LICENSE#L13"}], "repository": {"type": "git", "url": "http://github.com/kriszyp/json-schema"}, "directories": {"lib": "./lib"}, "main": "./lib/validate.js", "devDependencies": {"vows": "*"}, "scripts": {"test": "echo TESTS DISABLED vows --spec test/*.js"}}