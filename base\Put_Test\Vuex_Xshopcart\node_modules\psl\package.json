{"name": "psl", "version": "1.8.0", "description": "Domain name parser based on the Public Suffix List", "repository": {"type": "git", "url": "**************:lupomontero/psl.git"}, "main": "index.js", "scripts": {"pretest": "eslint .", "test": "mocha test && karma start ./karma.conf.js --single-run", "watch": "mocha test --watch", "prebuild": "./scripts/update-rules.js", "build": "browserify ./index.js --standalone=psl > ./dist/psl.js", "postbuild": "cat ./dist/psl.js | uglifyjs -c -m > ./dist/psl.min.js", "commit-and-pr": "commit-and-pr", "changelog": "git log $(git describe --tags --abbrev=0)..HEAD --oneline --format=\"%h %s (%an <%ae>)\""}, "keywords": ["publicsuffix", "publicsuffixlist"], "author": "<PERSON><PERSON> <<EMAIL>> (https://lupomontero.com/)", "license": "MIT", "devDependencies": {"JSONStream": "^1.3.5", "browserify": "^16.5.0", "commit-and-pr": "^1.0.4", "eslint": "^6.8.0", "eslint-config-hapi": "^12.0.0", "eslint-plugin-hapi": "^4.1.0", "karma": "^4.4.1", "karma-browserify": "^7.0.0", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4", "mocha": "^7.1.1", "phantomjs-prebuilt": "^2.1.16", "request": "^2.88.2", "uglify-js": "^3.8.0", "watchify": "^3.11.1"}}