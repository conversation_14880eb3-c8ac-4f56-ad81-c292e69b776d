{"name": "postcss-load-config", "version": "2.1.0", "description": "Autoload Config for PostCSS", "main": "src/index.js", "files": ["src"], "engines": {"node": ">= 4"}, "scripts": {"lint": "standard --env jest", "test": "jest --verbose --coverage", "docs": "jsdoc2md src/*.js > DOCS.md", "clean": "npx rimraf ./coverage", "release": "standard-version"}, "dependencies": {"cosmiconfig": "^5.0.0", "import-cwd": "^2.0.0"}, "devDependencies": {"cssnano": "^4.0.0", "jest": "^24.0.0", "jsdoc-to-markdown": "^5.0.0", "postcss": "^7.0.0", "postcss-import": "^12.0.0", "postcss-nested": "^4.0.0", "standard": "^12.0.0", "standard-version": "6.0.0", "sugarss": "^2.0.0"}, "keywords": ["postcss", "postcssrc", "postcss.config.js"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "repository": "https://github.com/micha<PERSON>-c<PERSON><PERSON><PERSON>/postcss-load-config.git", "bugs": "https://github.com/micha<PERSON>-c<PERSON><PERSON><PERSON>/postcss-load-config/issues", "homepage": "https://github.com/micha<PERSON>-c<PERSON><PERSON><PERSON>/postcss-load-config#readme", "license": "MIT"}