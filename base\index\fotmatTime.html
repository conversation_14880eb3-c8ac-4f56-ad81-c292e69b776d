<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        .box{
            overflow-y: scroll;
            max-height: 400px;
        }
    </style>
</head>
<body>
<header class="box">
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
    <h1>222222</h1>
</header>
    <script>

    var a = function (date,num) { //获取当前时间前几月日期，勿用该方法获取日
        var time = new Date(date);
        // time.setDate(1); //防止日期溢出而导致的月份不准
        time.setMonth(date.getMonth() + num);  
        return time;
    }
    var b = a(new Date(),5)
    console.log('b :>> ', b);


    </script>
</body>
</html>