{"name": "find-cache-dir", "version": "0.1.1", "description": "My well-made module", "license": "MIT", "repository": "jamestalmage/find-cache-dir", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && nyc --reporter=lcov --reporter=text ava"}, "files": ["index.js"], "keywords": ["cache", "directory", "dir"], "dependencies": {"commondir": "^1.0.1", "mkdirp": "^0.5.1", "pkg-dir": "^1.0.0"}, "devDependencies": {"ava": "^0.8.0", "coveralls": "^2.11.6", "nyc": "^5.0.1", "rimraf": "^2.5.0", "xo": "^0.12.1"}}