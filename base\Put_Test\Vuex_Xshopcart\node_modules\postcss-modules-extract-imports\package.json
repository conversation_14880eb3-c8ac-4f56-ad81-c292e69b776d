{"name": "postcss-modules-extract-imports", "version": "2.0.0", "description": "A CSS Modules transform to extract local aliases for inline imports", "main": "src/index.js", "engines": {"node": ">= 6"}, "files": ["src"], "scripts": {"test": "jest --coverage", "precommit": "lint-staged", "prepublish": "yarn run test"}, "lint-staged": {"*.js": ["prettier --single-quote --no-semi --write", "git add"]}, "repository": {"type": "git", "url": "https://github.com/css-modules/postcss-modules-extract-imports.git"}, "keywords": ["css-modules", "postcss", "plugin"], "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/css-modules/postcss-modules-extract-imports/issues"}, "homepage": "https://github.com/css-modules/postcss-modules-extract-imports", "dependencies": {"postcss": "^7.0.5"}, "devDependencies": {"codecov.io": "^0.1.2", "coveralls": "^2.11.2", "husky": "^0.13.3", "jest": "^20.0.3", "lint-staged": "^3.4.2", "prettier": "^1.3.1"}}