!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n(((e=e||self).prettierPlugins=e.prettierPlugins||{},e.prettierPlugins.graphql={}))}(this,(function(e){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var t=function(e,n){var t=new SyntaxError(e+" ("+n.start.line+":"+n.start.column+")");return t.loc=n,t};var i=function(e){return/^\s*#[^\n\S]*@(format|prettier)\s*(\n|$)/.test(e)};function r(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function o(e,n){return e(n={exports:{}},n.exports),n.exports}var a=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(e,n){if(!Boolean(e))throw new Error(n)}}));r(a);var s=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(e){"function"==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e.prototype,Symbol.toStringTag,{get:function(){return this.constructor.name}})}}));r(s);var c=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.Source=void 0;var t=r(a),i=r(s);function r(e){return e&&e.__esModule?e:{default:e}}var o=function(e,n,i){this.body=e,this.name=n||"GraphQL request",this.locationOffset=i||{line:1,column:1},this.locationOffset.line>0||(0,t.default)(0,"line in locationOffset is 1-indexed and must be positive"),this.locationOffset.column>0||(0,t.default)(0,"column in locationOffset is 1-indexed and must be positive")};n.Source=o,(0,i.default)(o)}));r(c);c.Source;var u=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.getLocation=function(e,n){var t,i=/\r\n|[\n\r]/g,r=1,o=n+1;for(;(t=i.exec(e.body))&&t.index<n;)r+=1,o=n+1-(t.index+t[0].length);return{line:r,column:o}}}));r(u);u.getLocation;var l=o((function(e,n){function t(e,n){var t=e.locationOffset.column-1,o=r(t)+e.body,a=n.line-1,s=e.locationOffset.line-1,c=n.line+s,u=1===n.line?t:0,l=n.column+u,d="".concat(e.name,":").concat(c,":").concat(l,"\n"),p=o.split(/\r\n|[\n\r]/g),f=p[a];if(f.length>120){for(var h=Math.floor(l/80),T=l%80,v=[],E=0;E<f.length;E+=80)v.push(f.slice(E,E+80));return d+i([["".concat(c),v[0]]].concat(v.slice(1,h+1).map((function(e){return["",e]})),[[" ",r(T-1)+"^"],["",v[h+1]]]))}return d+i([["".concat(c-1),p[a-1]],["".concat(c),f],["",r(l-1)+"^"],["".concat(c+1),p[a+1]]])}function i(e){var n=e.filter((function(e){e[0];return void 0!==e[1]})),t=Math.max.apply(Math,n.map((function(e){return e[0].length})));return n.map((function(e){var n,i=e[0],o=e[1];return r(t-(n=i).length)+n+(o?" | "+o:" |")})).join("\n")}function r(e){return Array(e+1).join(" ")}Object.defineProperty(n,"__esModule",{value:!0}),n.printLocation=function(e){return t(e.source,(0,u.getLocation)(e.source,e.start))},n.printSourceLocation=t}));r(l);l.printLocation,l.printSourceLocation;var d=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.Kind=void 0;var t=Object.freeze({NAME:"Name",DOCUMENT:"Document",OPERATION_DEFINITION:"OperationDefinition",VARIABLE_DEFINITION:"VariableDefinition",SELECTION_SET:"SelectionSet",FIELD:"Field",ARGUMENT:"Argument",FRAGMENT_SPREAD:"FragmentSpread",INLINE_FRAGMENT:"InlineFragment",FRAGMENT_DEFINITION:"FragmentDefinition",VARIABLE:"Variable",INT:"IntValue",FLOAT:"FloatValue",STRING:"StringValue",BOOLEAN:"BooleanValue",NULL:"NullValue",ENUM:"EnumValue",LIST:"ListValue",OBJECT:"ObjectValue",OBJECT_FIELD:"ObjectField",DIRECTIVE:"Directive",NAMED_TYPE:"NamedType",LIST_TYPE:"ListType",NON_NULL_TYPE:"NonNullType",SCHEMA_DEFINITION:"SchemaDefinition",OPERATION_TYPE_DEFINITION:"OperationTypeDefinition",SCALAR_TYPE_DEFINITION:"ScalarTypeDefinition",OBJECT_TYPE_DEFINITION:"ObjectTypeDefinition",FIELD_DEFINITION:"FieldDefinition",INPUT_VALUE_DEFINITION:"InputValueDefinition",INTERFACE_TYPE_DEFINITION:"InterfaceTypeDefinition",UNION_TYPE_DEFINITION:"UnionTypeDefinition",ENUM_TYPE_DEFINITION:"EnumTypeDefinition",ENUM_VALUE_DEFINITION:"EnumValueDefinition",INPUT_OBJECT_TYPE_DEFINITION:"InputObjectTypeDefinition",DIRECTIVE_DEFINITION:"DirectiveDefinition",SCHEMA_EXTENSION:"SchemaExtension",SCALAR_TYPE_EXTENSION:"ScalarTypeExtension",OBJECT_TYPE_EXTENSION:"ObjectTypeExtension",INTERFACE_TYPE_EXTENSION:"InterfaceTypeExtension",UNION_TYPE_EXTENSION:"UnionTypeExtension",ENUM_TYPE_EXTENSION:"EnumTypeExtension",INPUT_OBJECT_TYPE_EXTENSION:"InputObjectTypeExtension"});n.Kind=t}));r(d);d.Kind;var p=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.TokenKind=void 0;var t=Object.freeze({SOF:"<SOF>",EOF:"<EOF>",BANG:"!",DOLLAR:"$",AMP:"&",PAREN_L:"(",PAREN_R:")",SPREAD:"...",COLON:":",EQUALS:"=",AT:"@",BRACKET_L:"[",BRACKET_R:"]",BRACE_L:"{",PIPE:"|",BRACE_R:"}",NAME:"Name",INT:"Int",FLOAT:"Float",STRING:"String",BLOCK_STRING:"BlockString",COMMENT:"Comment"});n.TokenKind=t}));r(p);p.TokenKind;var f=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var t="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):void 0;n.default=t}));r(f);var h=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.prototype.toString;e.prototype.toJSON=n,e.prototype.inspect=n,i.default&&(e.prototype[i.default]=n)};var t,i=(t=f)&&t.__esModule?t:{default:t}}));r(h);var T=o((function(e,t){function i(e){return(i="function"==typeof Symbol&&"symbol"===n(Symbol.iterator)?function(e){return n(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":n(e)})(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return"object"==i(e)&&null!==e}}));r(T);var v=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.GraphQLError=r,n.printError=o;var t,i=(t=T)&&t.__esModule?t:{default:t};function r(e,n,t,o,a,s,c){var l=Array.isArray(n)?0!==n.length?n:void 0:n?[n]:void 0,d=t;if(!d&&l){var p=l[0];d=p&&p.loc&&p.loc.source}var f,h=o;!h&&l&&(h=l.reduce((function(e,n){return n.loc&&e.push(n.loc.start),e}),[])),h&&0===h.length&&(h=void 0),o&&t?f=o.map((function(e){return(0,u.getLocation)(t,e)})):l&&(f=l.reduce((function(e,n){return n.loc&&e.push((0,u.getLocation)(n.loc.source,n.loc.start)),e}),[]));var T=c;if(null==T&&null!=s){var v=s.extensions;(0,i.default)(v)&&(T=v)}Object.defineProperties(this,{message:{value:e,enumerable:!0,writable:!0},locations:{value:f||void 0,enumerable:Boolean(f)},path:{value:a||void 0,enumerable:Boolean(a)},nodes:{value:l||void 0},source:{value:d||void 0},positions:{value:h||void 0},originalError:{value:s},extensions:{value:T||void 0,enumerable:Boolean(T)}}),s&&s.stack?Object.defineProperty(this,"stack",{value:s.stack,writable:!0,configurable:!0}):Error.captureStackTrace?Error.captureStackTrace(this,r):Object.defineProperty(this,"stack",{value:Error().stack,writable:!0,configurable:!0})}function o(e){var n=e.message;if(e.nodes)for(var t=0,i=e.nodes;t<i.length;t++){var r=i[t];r.loc&&(n+="\n\n"+(0,l.printLocation)(r.loc))}else if(e.source&&e.locations)for(var o=0,a=e.locations;o<a.length;o++){var s=a[o];n+="\n\n"+(0,l.printSourceLocation)(e.source,s)}return n}r.prototype=Object.create(Error.prototype,{constructor:{value:r},name:{value:"GraphQLError"},toString:{value:function(){return o(this)}}})}));r(v);v.GraphQLError,v.printError;var E=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.syntaxError=function(e,n,t){return new v.GraphQLError("Syntax Error: ".concat(t),void 0,e,[n])}}));r(E);E.syntaxError;var y=o((function(e,n){function t(e){for(var n=null,t=1;t<e.length;t++){var r=e[t],o=i(r);if(o!==r.length&&((null===n||o<n)&&0===(n=o)))break}return null===n?0:n}function i(e){for(var n=0;n<e.length&&(" "===e[n]||"\t"===e[n]);)n++;return n}function r(e){return i(e)===e.length}Object.defineProperty(n,"__esModule",{value:!0}),n.dedentBlockStringValue=function(e){var n=e.split(/\r\n|[\n\r]/g),i=t(n);if(0!==i)for(var o=1;o<n.length;o++)n[o]=n[o].slice(i);for(;n.length>0&&r(n[0]);)n.shift();for(;n.length>0&&r(n[n.length-1]);)n.pop();return n.join("\n")},n.getBlockStringIndentation=t,n.printBlockString=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",t=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=-1===e.indexOf("\n"),r=" "===e[0]||"\t"===e[0],o='"'===e[e.length-1],a=!i||o||t,s="";!a||i&&r||(s+="\n"+n);s+=n?e.replace(/\n/g,"\n"+n):e,a&&(s+="\n");return'"""'+s.replace(/"""/g,'\\"""')+'"""'}}));r(y);y.dedentBlockStringValue,y.getBlockStringIndentation,y.printBlockString;var N=o((function(e,n){var t;function i(){return this.lastToken=this.token,this.token=this.lookahead()}function r(){var e=this.token;if(e.kind!==p.TokenKind.EOF)do{e=e.next||(e.next=s(this,e))}while(e.kind===p.TokenKind.COMMENT);return e}function o(e,n,t,i,r,o,a){this.kind=e,this.start=n,this.end=t,this.line=i,this.column=r,this.value=a,this.prev=o,this.next=null}function a(e){return isNaN(e)?p.TokenKind.EOF:e<127?JSON.stringify(String.fromCharCode(e)):'"\\u'.concat(("00"+e.toString(16).toUpperCase()).slice(-4),'"')}function s(e,n){var t=e.source,i=t.body,r=i.length,s=function(e,n,t){var i=e.length,r=n;for(;r<i;){var o=e.charCodeAt(r);if(9===o||32===o||44===o||65279===o)++r;else if(10===o)++r,++t.line,t.lineStart=r;else{if(13!==o)break;10===e.charCodeAt(r+1)?r+=2:++r,++t.line,t.lineStart=r}}return r}(i,n.end,e),l=e.line,d=1+s-e.lineStart;if(s>=r)return new o(p.TokenKind.EOF,r,r,l,d,n);var f=i.charCodeAt(s);switch(f){case 33:return new o(p.TokenKind.BANG,s,s+1,l,d,n);case 35:return function(e,n,t,i,r){var a,s=e.body,c=n;do{a=s.charCodeAt(++c)}while(!isNaN(a)&&(a>31||9===a));return new o(p.TokenKind.COMMENT,n,c,t,i,r,s.slice(n+1,c))}(t,s,l,d,n);case 36:return new o(p.TokenKind.DOLLAR,s,s+1,l,d,n);case 38:return new o(p.TokenKind.AMP,s,s+1,l,d,n);case 40:return new o(p.TokenKind.PAREN_L,s,s+1,l,d,n);case 41:return new o(p.TokenKind.PAREN_R,s,s+1,l,d,n);case 46:if(46===i.charCodeAt(s+1)&&46===i.charCodeAt(s+2))return new o(p.TokenKind.SPREAD,s,s+3,l,d,n);break;case 58:return new o(p.TokenKind.COLON,s,s+1,l,d,n);case 61:return new o(p.TokenKind.EQUALS,s,s+1,l,d,n);case 64:return new o(p.TokenKind.AT,s,s+1,l,d,n);case 91:return new o(p.TokenKind.BRACKET_L,s,s+1,l,d,n);case 93:return new o(p.TokenKind.BRACKET_R,s,s+1,l,d,n);case 123:return new o(p.TokenKind.BRACE_L,s,s+1,l,d,n);case 124:return new o(p.TokenKind.PIPE,s,s+1,l,d,n);case 125:return new o(p.TokenKind.BRACE_R,s,s+1,l,d,n);case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 95:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:return function(e,n,t,i,r){var a=e.body,s=a.length,c=n+1,u=0;for(;c!==s&&!isNaN(u=a.charCodeAt(c))&&(95===u||u>=48&&u<=57||u>=65&&u<=90||u>=97&&u<=122);)++c;return new o(p.TokenKind.NAME,n,c,t,i,r,a.slice(n,c))}(t,s,l,d,n);case 45:case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return function(e,n,t,i,r,s){var u=e.body,l=t,d=n,f=!1;45===l&&(l=u.charCodeAt(++d));if(48===l){if((l=u.charCodeAt(++d))>=48&&l<=57)throw(0,E.syntaxError)(e,d,"Invalid number, unexpected digit after 0: ".concat(a(l),"."))}else d=c(e,d,l),l=u.charCodeAt(d);46===l&&(f=!0,l=u.charCodeAt(++d),d=c(e,d,l),l=u.charCodeAt(d));69!==l&&101!==l||(f=!0,43!==(l=u.charCodeAt(++d))&&45!==l||(l=u.charCodeAt(++d)),d=c(e,d,l),l=u.charCodeAt(d));if(46===l||69===l||101===l)throw(0,E.syntaxError)(e,d,"Invalid number, expected digit but got: ".concat(a(l),"."));return new o(f?p.TokenKind.FLOAT:p.TokenKind.INT,n,d,i,r,s,u.slice(n,d))}(t,s,f,l,d,n);case 34:return 34===i.charCodeAt(s+1)&&34===i.charCodeAt(s+2)?function(e,n,t,i,r,s){var c=e.body,u=n+3,l=u,d=0,f="";for(;u<c.length&&!isNaN(d=c.charCodeAt(u));){if(34===d&&34===c.charCodeAt(u+1)&&34===c.charCodeAt(u+2))return f+=c.slice(l,u),new o(p.TokenKind.BLOCK_STRING,n,u+3,t,i,r,(0,y.dedentBlockStringValue)(f));if(d<32&&9!==d&&10!==d&&13!==d)throw(0,E.syntaxError)(e,u,"Invalid character within String: ".concat(a(d),"."));10===d?(++u,++s.line,s.lineStart=u):13===d?(10===c.charCodeAt(u+1)?u+=2:++u,++s.line,s.lineStart=u):92===d&&34===c.charCodeAt(u+1)&&34===c.charCodeAt(u+2)&&34===c.charCodeAt(u+3)?(f+=c.slice(l,u)+'"""',l=u+=4):++u}throw(0,E.syntaxError)(e,u,"Unterminated string.")}(t,s,l,d,n,e):function(e,n,t,i,r){var s=e.body,c=n+1,l=c,d=0,f="";for(;c<s.length&&!isNaN(d=s.charCodeAt(c))&&10!==d&&13!==d;){if(34===d)return f+=s.slice(l,c),new o(p.TokenKind.STRING,n,c+1,t,i,r,f);if(d<32&&9!==d)throw(0,E.syntaxError)(e,c,"Invalid character within String: ".concat(a(d),"."));if(++c,92===d){switch(f+=s.slice(l,c-1),d=s.charCodeAt(c)){case 34:f+='"';break;case 47:f+="/";break;case 92:f+="\\";break;case 98:f+="\b";break;case 102:f+="\f";break;case 110:f+="\n";break;case 114:f+="\r";break;case 116:f+="\t";break;case 117:var h=(v=s.charCodeAt(c+1),y=s.charCodeAt(c+2),N=s.charCodeAt(c+3),m=s.charCodeAt(c+4),u(v)<<12|u(y)<<8|u(N)<<4|u(m));if(h<0){var T=s.slice(c+1,c+5);throw(0,E.syntaxError)(e,c,"Invalid character escape sequence: \\u".concat(T,"."))}f+=String.fromCharCode(h),c+=4;break;default:throw(0,E.syntaxError)(e,c,"Invalid character escape sequence: \\".concat(String.fromCharCode(d),"."))}l=++c}}var v,y,N,m;throw(0,E.syntaxError)(e,c,"Unterminated string.")}(t,s,l,d,n)}throw(0,E.syntaxError)(t,s,function(e){if(e<32&&9!==e&&10!==e&&13!==e)return"Cannot contain the invalid character ".concat(a(e),".");if(39===e)return"Unexpected single quote character ('), did you mean to use a double quote (\")?";return"Cannot parse the unexpected character ".concat(a(e),".")}(f))}function c(e,n,t){var i=e.body,r=n,o=t;if(o>=48&&o<=57){do{o=i.charCodeAt(++r)}while(o>=48&&o<=57);return r}throw(0,E.syntaxError)(e,r,"Invalid number, expected digit but got: ".concat(a(o),"."))}function u(e){return e>=48&&e<=57?e-48:e>=65&&e<=70?e-55:e>=97&&e<=102?e-87:-1}Object.defineProperty(n,"__esModule",{value:!0}),n.createLexer=function(e,n){var t=new o(p.TokenKind.SOF,0,0,0,0,null);return{source:e,options:n,lastToken:t,token:t,line:1,lineStart:0,advance:i,lookahead:r}},n.isPunctuatorToken=function(e){var n=e.kind;return n===p.TokenKind.BANG||n===p.TokenKind.DOLLAR||n===p.TokenKind.AMP||n===p.TokenKind.PAREN_L||n===p.TokenKind.PAREN_R||n===p.TokenKind.SPREAD||n===p.TokenKind.COLON||n===p.TokenKind.EQUALS||n===p.TokenKind.AT||n===p.TokenKind.BRACKET_L||n===p.TokenKind.BRACKET_R||n===p.TokenKind.BRACE_L||n===p.TokenKind.PIPE||n===p.TokenKind.BRACE_R},(0,((t=h)&&t.__esModule?t:{default:t}).default)(o,(function(){return{kind:this.kind,value:this.value,line:this.line,column:this.column}}))}));r(N);N.createLexer,N.isPunctuatorToken;var m=o((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return c(e,[])};var i,r=(i=f)&&i.__esModule?i:{default:i};function o(e){return(o="function"==typeof Symbol&&"symbol"===n(Symbol.iterator)?function(e){return n(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":n(e)})(e)}var a=10,s=2;function c(e,n){switch(o(e)){case"string":return JSON.stringify(e);case"function":return e.name?"[function ".concat(e.name,"]"):"[function]";case"object":return null===e?"null":function(e,n){if(-1!==n.indexOf(e))return"[Circular]";var t=[].concat(n,[e]),i=function(e){var n=e[String(r.default)];if("function"==typeof n)return n;if("function"==typeof e.inspect)return e.inspect}(e);if(void 0!==i){var o=i.call(e);if(o!==e)return"string"==typeof o?o:c(o,t)}else if(Array.isArray(e))return function(e,n){if(0===e.length)return"[]";if(n.length>s)return"[Array]";for(var t=Math.min(a,e.length),i=e.length-t,r=[],o=0;o<t;++o)r.push(c(e[o],n));1===i?r.push("... 1 more item"):i>1&&r.push("... ".concat(i," more items"));return"["+r.join(", ")+"]"}(e,t);return function(e,n){var t=Object.keys(e);if(0===t.length)return"{}";if(n.length>s)return"["+function(e){var n=Object.prototype.toString.call(e).replace(/^\[object /,"").replace(/]$/,"");if("Object"===n&&"function"==typeof e.constructor){var t=e.constructor.name;if("string"==typeof t&&""!==t)return t}return n}(e)+"]";return"{ "+t.map((function(t){return t+": "+c(e[t],n)})).join(", ")+" }"}(e,t)}(e,n);default:return String(e)}}}));r(m);var k=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.DirectiveLocation=void 0;var t=Object.freeze({QUERY:"QUERY",MUTATION:"MUTATION",SUBSCRIPTION:"SUBSCRIPTION",FIELD:"FIELD",FRAGMENT_DEFINITION:"FRAGMENT_DEFINITION",FRAGMENT_SPREAD:"FRAGMENT_SPREAD",INLINE_FRAGMENT:"INLINE_FRAGMENT",VARIABLE_DEFINITION:"VARIABLE_DEFINITION",SCHEMA:"SCHEMA",SCALAR:"SCALAR",OBJECT:"OBJECT",FIELD_DEFINITION:"FIELD_DEFINITION",ARGUMENT_DEFINITION:"ARGUMENT_DEFINITION",INTERFACE:"INTERFACE",UNION:"UNION",ENUM:"ENUM",ENUM_VALUE:"ENUM_VALUE",INPUT_OBJECT:"INPUT_OBJECT",INPUT_FIELD_DEFINITION:"INPUT_FIELD_DEFINITION"});n.DirectiveLocation=t}));r(k);k.DirectiveLocation;var I=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.parse=function(e,n){return new s(e,n).parseDocument()},n.parseValue=function(e,n){var t=new s(e,n);t.expectToken(p.TokenKind.SOF);var i=t.parseValueLiteral(!1);return t.expectToken(p.TokenKind.EOF),i},n.parseType=function(e,n){var t=new s(e,n);t.expectToken(p.TokenKind.SOF);var i=t.parseTypeReference();return t.expectToken(p.TokenKind.EOF),i};var t=o(m),i=o(a),r=o(h);function o(e){return e&&e.__esModule?e:{default:e}}var s=function(){function e(e,n){var r="string"==typeof e?new c.Source(e):e;r instanceof c.Source||(0,i.default)(0,"Must provide Source. Received: ".concat((0,t.default)(r))),this._lexer=(0,N.createLexer)(r),this._options=n||{}}var n=e.prototype;return n.parseName=function(){var e=this.expectToken(p.TokenKind.NAME);return{kind:d.Kind.NAME,value:e.value,loc:this.loc(e)}},n.parseDocument=function(){var e=this._lexer.token;return{kind:d.Kind.DOCUMENT,definitions:this.many(p.TokenKind.SOF,this.parseDefinition,p.TokenKind.EOF),loc:this.loc(e)}},n.parseDefinition=function(){if(this.peek(p.TokenKind.NAME))switch(this._lexer.token.value){case"query":case"mutation":case"subscription":return this.parseOperationDefinition();case"fragment":return this.parseFragmentDefinition();case"schema":case"scalar":case"type":case"interface":case"union":case"enum":case"input":case"directive":return this.parseTypeSystemDefinition();case"extend":return this.parseTypeSystemExtension()}else{if(this.peek(p.TokenKind.BRACE_L))return this.parseOperationDefinition();if(this.peekDescription())return this.parseTypeSystemDefinition()}throw this.unexpected()},n.parseOperationDefinition=function(){var e=this._lexer.token;if(this.peek(p.TokenKind.BRACE_L))return{kind:d.Kind.OPERATION_DEFINITION,operation:"query",name:void 0,variableDefinitions:[],directives:[],selectionSet:this.parseSelectionSet(),loc:this.loc(e)};var n,t=this.parseOperationType();return this.peek(p.TokenKind.NAME)&&(n=this.parseName()),{kind:d.Kind.OPERATION_DEFINITION,operation:t,name:n,variableDefinitions:this.parseVariableDefinitions(),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(e)}},n.parseOperationType=function(){var e=this.expectToken(p.TokenKind.NAME);switch(e.value){case"query":return"query";case"mutation":return"mutation";case"subscription":return"subscription"}throw this.unexpected(e)},n.parseVariableDefinitions=function(){return this.optionalMany(p.TokenKind.PAREN_L,this.parseVariableDefinition,p.TokenKind.PAREN_R)},n.parseVariableDefinition=function(){var e=this._lexer.token;return{kind:d.Kind.VARIABLE_DEFINITION,variable:this.parseVariable(),type:(this.expectToken(p.TokenKind.COLON),this.parseTypeReference()),defaultValue:this.expectOptionalToken(p.TokenKind.EQUALS)?this.parseValueLiteral(!0):void 0,directives:this.parseDirectives(!0),loc:this.loc(e)}},n.parseVariable=function(){var e=this._lexer.token;return this.expectToken(p.TokenKind.DOLLAR),{kind:d.Kind.VARIABLE,name:this.parseName(),loc:this.loc(e)}},n.parseSelectionSet=function(){var e=this._lexer.token;return{kind:d.Kind.SELECTION_SET,selections:this.many(p.TokenKind.BRACE_L,this.parseSelection,p.TokenKind.BRACE_R),loc:this.loc(e)}},n.parseSelection=function(){return this.peek(p.TokenKind.SPREAD)?this.parseFragment():this.parseField()},n.parseField=function(){var e,n,t=this._lexer.token,i=this.parseName();return this.expectOptionalToken(p.TokenKind.COLON)?(e=i,n=this.parseName()):n=i,{kind:d.Kind.FIELD,alias:e,name:n,arguments:this.parseArguments(!1),directives:this.parseDirectives(!1),selectionSet:this.peek(p.TokenKind.BRACE_L)?this.parseSelectionSet():void 0,loc:this.loc(t)}},n.parseArguments=function(e){var n=e?this.parseConstArgument:this.parseArgument;return this.optionalMany(p.TokenKind.PAREN_L,n,p.TokenKind.PAREN_R)},n.parseArgument=function(){var e=this._lexer.token,n=this.parseName();return this.expectToken(p.TokenKind.COLON),{kind:d.Kind.ARGUMENT,name:n,value:this.parseValueLiteral(!1),loc:this.loc(e)}},n.parseConstArgument=function(){var e=this._lexer.token;return{kind:d.Kind.ARGUMENT,name:this.parseName(),value:(this.expectToken(p.TokenKind.COLON),this.parseValueLiteral(!0)),loc:this.loc(e)}},n.parseFragment=function(){var e=this._lexer.token;this.expectToken(p.TokenKind.SPREAD);var n=this.expectOptionalKeyword("on");return!n&&this.peek(p.TokenKind.NAME)?{kind:d.Kind.FRAGMENT_SPREAD,name:this.parseFragmentName(),directives:this.parseDirectives(!1),loc:this.loc(e)}:{kind:d.Kind.INLINE_FRAGMENT,typeCondition:n?this.parseNamedType():void 0,directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(e)}},n.parseFragmentDefinition=function(){var e=this._lexer.token;return this.expectKeyword("fragment"),this._options.experimentalFragmentVariables?{kind:d.Kind.FRAGMENT_DEFINITION,name:this.parseFragmentName(),variableDefinitions:this.parseVariableDefinitions(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(e)}:{kind:d.Kind.FRAGMENT_DEFINITION,name:this.parseFragmentName(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(e)}},n.parseFragmentName=function(){if("on"===this._lexer.token.value)throw this.unexpected();return this.parseName()},n.parseValueLiteral=function(e){var n=this._lexer.token;switch(n.kind){case p.TokenKind.BRACKET_L:return this.parseList(e);case p.TokenKind.BRACE_L:return this.parseObject(e);case p.TokenKind.INT:return this._lexer.advance(),{kind:d.Kind.INT,value:n.value,loc:this.loc(n)};case p.TokenKind.FLOAT:return this._lexer.advance(),{kind:d.Kind.FLOAT,value:n.value,loc:this.loc(n)};case p.TokenKind.STRING:case p.TokenKind.BLOCK_STRING:return this.parseStringLiteral();case p.TokenKind.NAME:return"true"===n.value||"false"===n.value?(this._lexer.advance(),{kind:d.Kind.BOOLEAN,value:"true"===n.value,loc:this.loc(n)}):"null"===n.value?(this._lexer.advance(),{kind:d.Kind.NULL,loc:this.loc(n)}):(this._lexer.advance(),{kind:d.Kind.ENUM,value:n.value,loc:this.loc(n)});case p.TokenKind.DOLLAR:if(!e)return this.parseVariable()}throw this.unexpected()},n.parseStringLiteral=function(){var e=this._lexer.token;return this._lexer.advance(),{kind:d.Kind.STRING,value:e.value,block:e.kind===p.TokenKind.BLOCK_STRING,loc:this.loc(e)}},n.parseList=function(e){var n=this,t=this._lexer.token;return{kind:d.Kind.LIST,values:this.any(p.TokenKind.BRACKET_L,(function(){return n.parseValueLiteral(e)}),p.TokenKind.BRACKET_R),loc:this.loc(t)}},n.parseObject=function(e){var n=this,t=this._lexer.token;return{kind:d.Kind.OBJECT,fields:this.any(p.TokenKind.BRACE_L,(function(){return n.parseObjectField(e)}),p.TokenKind.BRACE_R),loc:this.loc(t)}},n.parseObjectField=function(e){var n=this._lexer.token,t=this.parseName();return this.expectToken(p.TokenKind.COLON),{kind:d.Kind.OBJECT_FIELD,name:t,value:this.parseValueLiteral(e),loc:this.loc(n)}},n.parseDirectives=function(e){for(var n=[];this.peek(p.TokenKind.AT);)n.push(this.parseDirective(e));return n},n.parseDirective=function(e){var n=this._lexer.token;return this.expectToken(p.TokenKind.AT),{kind:d.Kind.DIRECTIVE,name:this.parseName(),arguments:this.parseArguments(e),loc:this.loc(n)}},n.parseTypeReference=function(){var e,n=this._lexer.token;return this.expectOptionalToken(p.TokenKind.BRACKET_L)?(e=this.parseTypeReference(),this.expectToken(p.TokenKind.BRACKET_R),e={kind:d.Kind.LIST_TYPE,type:e,loc:this.loc(n)}):e=this.parseNamedType(),this.expectOptionalToken(p.TokenKind.BANG)?{kind:d.Kind.NON_NULL_TYPE,type:e,loc:this.loc(n)}:e},n.parseNamedType=function(){var e=this._lexer.token;return{kind:d.Kind.NAMED_TYPE,name:this.parseName(),loc:this.loc(e)}},n.parseTypeSystemDefinition=function(){var e=this.peekDescription()?this._lexer.lookahead():this._lexer.token;if(e.kind===p.TokenKind.NAME)switch(e.value){case"schema":return this.parseSchemaDefinition();case"scalar":return this.parseScalarTypeDefinition();case"type":return this.parseObjectTypeDefinition();case"interface":return this.parseInterfaceTypeDefinition();case"union":return this.parseUnionTypeDefinition();case"enum":return this.parseEnumTypeDefinition();case"input":return this.parseInputObjectTypeDefinition();case"directive":return this.parseDirectiveDefinition()}throw this.unexpected(e)},n.peekDescription=function(){return this.peek(p.TokenKind.STRING)||this.peek(p.TokenKind.BLOCK_STRING)},n.parseDescription=function(){if(this.peekDescription())return this.parseStringLiteral()},n.parseSchemaDefinition=function(){var e=this._lexer.token;this.expectKeyword("schema");var n=this.parseDirectives(!0),t=this.many(p.TokenKind.BRACE_L,this.parseOperationTypeDefinition,p.TokenKind.BRACE_R);return{kind:d.Kind.SCHEMA_DEFINITION,directives:n,operationTypes:t,loc:this.loc(e)}},n.parseOperationTypeDefinition=function(){var e=this._lexer.token,n=this.parseOperationType();this.expectToken(p.TokenKind.COLON);var t=this.parseNamedType();return{kind:d.Kind.OPERATION_TYPE_DEFINITION,operation:n,type:t,loc:this.loc(e)}},n.parseScalarTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("scalar");var t=this.parseName(),i=this.parseDirectives(!0);return{kind:d.Kind.SCALAR_TYPE_DEFINITION,description:n,name:t,directives:i,loc:this.loc(e)}},n.parseObjectTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("type");var t=this.parseName(),i=this.parseImplementsInterfaces(),r=this.parseDirectives(!0),o=this.parseFieldsDefinition();return{kind:d.Kind.OBJECT_TYPE_DEFINITION,description:n,name:t,interfaces:i,directives:r,fields:o,loc:this.loc(e)}},n.parseImplementsInterfaces=function(){var e=[];if(this.expectOptionalKeyword("implements")){this.expectOptionalToken(p.TokenKind.AMP);do{e.push(this.parseNamedType())}while(this.expectOptionalToken(p.TokenKind.AMP)||this._options.allowLegacySDLImplementsInterfaces&&this.peek(p.TokenKind.NAME))}return e},n.parseFieldsDefinition=function(){return this._options.allowLegacySDLEmptyFields&&this.peek(p.TokenKind.BRACE_L)&&this._lexer.lookahead().kind===p.TokenKind.BRACE_R?(this._lexer.advance(),this._lexer.advance(),[]):this.optionalMany(p.TokenKind.BRACE_L,this.parseFieldDefinition,p.TokenKind.BRACE_R)},n.parseFieldDefinition=function(){var e=this._lexer.token,n=this.parseDescription(),t=this.parseName(),i=this.parseArgumentDefs();this.expectToken(p.TokenKind.COLON);var r=this.parseTypeReference(),o=this.parseDirectives(!0);return{kind:d.Kind.FIELD_DEFINITION,description:n,name:t,arguments:i,type:r,directives:o,loc:this.loc(e)}},n.parseArgumentDefs=function(){return this.optionalMany(p.TokenKind.PAREN_L,this.parseInputValueDef,p.TokenKind.PAREN_R)},n.parseInputValueDef=function(){var e=this._lexer.token,n=this.parseDescription(),t=this.parseName();this.expectToken(p.TokenKind.COLON);var i,r=this.parseTypeReference();this.expectOptionalToken(p.TokenKind.EQUALS)&&(i=this.parseValueLiteral(!0));var o=this.parseDirectives(!0);return{kind:d.Kind.INPUT_VALUE_DEFINITION,description:n,name:t,type:r,defaultValue:i,directives:o,loc:this.loc(e)}},n.parseInterfaceTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("interface");var t=this.parseName(),i=this.parseDirectives(!0),r=this.parseFieldsDefinition();return{kind:d.Kind.INTERFACE_TYPE_DEFINITION,description:n,name:t,directives:i,fields:r,loc:this.loc(e)}},n.parseUnionTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("union");var t=this.parseName(),i=this.parseDirectives(!0),r=this.parseUnionMemberTypes();return{kind:d.Kind.UNION_TYPE_DEFINITION,description:n,name:t,directives:i,types:r,loc:this.loc(e)}},n.parseUnionMemberTypes=function(){var e=[];if(this.expectOptionalToken(p.TokenKind.EQUALS)){this.expectOptionalToken(p.TokenKind.PIPE);do{e.push(this.parseNamedType())}while(this.expectOptionalToken(p.TokenKind.PIPE))}return e},n.parseEnumTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("enum");var t=this.parseName(),i=this.parseDirectives(!0),r=this.parseEnumValuesDefinition();return{kind:d.Kind.ENUM_TYPE_DEFINITION,description:n,name:t,directives:i,values:r,loc:this.loc(e)}},n.parseEnumValuesDefinition=function(){return this.optionalMany(p.TokenKind.BRACE_L,this.parseEnumValueDefinition,p.TokenKind.BRACE_R)},n.parseEnumValueDefinition=function(){var e=this._lexer.token,n=this.parseDescription(),t=this.parseName(),i=this.parseDirectives(!0);return{kind:d.Kind.ENUM_VALUE_DEFINITION,description:n,name:t,directives:i,loc:this.loc(e)}},n.parseInputObjectTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("input");var t=this.parseName(),i=this.parseDirectives(!0),r=this.parseInputFieldsDefinition();return{kind:d.Kind.INPUT_OBJECT_TYPE_DEFINITION,description:n,name:t,directives:i,fields:r,loc:this.loc(e)}},n.parseInputFieldsDefinition=function(){return this.optionalMany(p.TokenKind.BRACE_L,this.parseInputValueDef,p.TokenKind.BRACE_R)},n.parseTypeSystemExtension=function(){var e=this._lexer.lookahead();if(e.kind===p.TokenKind.NAME)switch(e.value){case"schema":return this.parseSchemaExtension();case"scalar":return this.parseScalarTypeExtension();case"type":return this.parseObjectTypeExtension();case"interface":return this.parseInterfaceTypeExtension();case"union":return this.parseUnionTypeExtension();case"enum":return this.parseEnumTypeExtension();case"input":return this.parseInputObjectTypeExtension()}throw this.unexpected(e)},n.parseSchemaExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("schema");var n=this.parseDirectives(!0),t=this.optionalMany(p.TokenKind.BRACE_L,this.parseOperationTypeDefinition,p.TokenKind.BRACE_R);if(0===n.length&&0===t.length)throw this.unexpected();return{kind:d.Kind.SCHEMA_EXTENSION,directives:n,operationTypes:t,loc:this.loc(e)}},n.parseScalarTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("scalar");var n=this.parseName(),t=this.parseDirectives(!0);if(0===t.length)throw this.unexpected();return{kind:d.Kind.SCALAR_TYPE_EXTENSION,name:n,directives:t,loc:this.loc(e)}},n.parseObjectTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("type");var n=this.parseName(),t=this.parseImplementsInterfaces(),i=this.parseDirectives(!0),r=this.parseFieldsDefinition();if(0===t.length&&0===i.length&&0===r.length)throw this.unexpected();return{kind:d.Kind.OBJECT_TYPE_EXTENSION,name:n,interfaces:t,directives:i,fields:r,loc:this.loc(e)}},n.parseInterfaceTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("interface");var n=this.parseName(),t=this.parseDirectives(!0),i=this.parseFieldsDefinition();if(0===t.length&&0===i.length)throw this.unexpected();return{kind:d.Kind.INTERFACE_TYPE_EXTENSION,name:n,directives:t,fields:i,loc:this.loc(e)}},n.parseUnionTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("union");var n=this.parseName(),t=this.parseDirectives(!0),i=this.parseUnionMemberTypes();if(0===t.length&&0===i.length)throw this.unexpected();return{kind:d.Kind.UNION_TYPE_EXTENSION,name:n,directives:t,types:i,loc:this.loc(e)}},n.parseEnumTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("enum");var n=this.parseName(),t=this.parseDirectives(!0),i=this.parseEnumValuesDefinition();if(0===t.length&&0===i.length)throw this.unexpected();return{kind:d.Kind.ENUM_TYPE_EXTENSION,name:n,directives:t,values:i,loc:this.loc(e)}},n.parseInputObjectTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("input");var n=this.parseName(),t=this.parseDirectives(!0),i=this.parseInputFieldsDefinition();if(0===t.length&&0===i.length)throw this.unexpected();return{kind:d.Kind.INPUT_OBJECT_TYPE_EXTENSION,name:n,directives:t,fields:i,loc:this.loc(e)}},n.parseDirectiveDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("directive"),this.expectToken(p.TokenKind.AT);var t=this.parseName(),i=this.parseArgumentDefs(),r=this.expectOptionalKeyword("repeatable");this.expectKeyword("on");var o=this.parseDirectiveLocations();return{kind:d.Kind.DIRECTIVE_DEFINITION,description:n,name:t,arguments:i,repeatable:r,locations:o,loc:this.loc(e)}},n.parseDirectiveLocations=function(){this.expectOptionalToken(p.TokenKind.PIPE);var e=[];do{e.push(this.parseDirectiveLocation())}while(this.expectOptionalToken(p.TokenKind.PIPE));return e},n.parseDirectiveLocation=function(){var e=this._lexer.token,n=this.parseName();if(void 0!==k.DirectiveLocation[n.value])return n;throw this.unexpected(e)},n.loc=function(e){if(!this._options.noLocation)return new u(e,this._lexer.lastToken,this._lexer.source)},n.peek=function(e){return this._lexer.token.kind===e},n.expectToken=function(e){var n=this._lexer.token;if(n.kind===e)return this._lexer.advance(),n;throw(0,E.syntaxError)(this._lexer.source,n.start,"Expected ".concat(e,", found ").concat(l(n)))},n.expectOptionalToken=function(e){var n=this._lexer.token;if(n.kind===e)return this._lexer.advance(),n},n.expectKeyword=function(e){var n=this._lexer.token;if(n.kind!==p.TokenKind.NAME||n.value!==e)throw(0,E.syntaxError)(this._lexer.source,n.start,'Expected "'.concat(e,'", found ').concat(l(n)));this._lexer.advance()},n.expectOptionalKeyword=function(e){var n=this._lexer.token;return n.kind===p.TokenKind.NAME&&n.value===e&&(this._lexer.advance(),!0)},n.unexpected=function(e){var n=e||this._lexer.token;return(0,E.syntaxError)(this._lexer.source,n.start,"Unexpected ".concat(l(n)))},n.any=function(e,n,t){this.expectToken(e);for(var i=[];!this.expectOptionalToken(t);)i.push(n.call(this));return i},n.optionalMany=function(e,n,t){if(this.expectOptionalToken(e)){var i=[];do{i.push(n.call(this))}while(!this.expectOptionalToken(t));return i}return[]},n.many=function(e,n,t){this.expectToken(e);var i=[];do{i.push(n.call(this))}while(!this.expectOptionalToken(t));return i},e}();function u(e,n,t){this.start=e.start,this.end=n.end,this.startToken=e,this.endToken=n,this.source=t}function l(e){var n=e.value;return n?"".concat(e.kind,' "').concat(n,'"'):e.kind}(0,r.default)(u,(function(){return{start:this.start,end:this.end}}))}));r(I);I.parse,I.parseValue,I.parseType;var _=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.visit=function(e,n){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r,c=void 0,u=Array.isArray(e),l=[e],d=-1,p=[],f=void 0,h=void 0,T=void 0,v=[],E=[],y=e;do{var N=++d===l.length,m=N&&0!==p.length;if(N){if(h=0===E.length?void 0:v[v.length-1],f=T,T=E.pop(),m){if(u)f=f.slice();else{for(var k={},I=0,_=Object.keys(f);I<_.length;I++){var O=_[I];k[O]=f[O]}f=k}for(var D=0,x=0;x<p.length;x++){var K=p[x][0],A=p[x][1];u&&(K-=D),u&&null===A?(f.splice(K,1),D++):f[K]=A}}d=c.index,l=c.keys,p=c.edits,u=c.inArray,c=c.prev}else{if(h=T?u?d:l[d]:void 0,null==(f=T?T[h]:y))continue;T&&v.push(h)}var b=void 0;if(!Array.isArray(f)){if(!a(f))throw new Error("Invalid AST Node: "+(0,i.default)(f));var S=s(n,f.kind,N);if(S){if((b=S.call(n,f,h,T,v,E))===o)break;if(!1===b){if(!N){v.pop();continue}}else if(void 0!==b&&(p.push([h,b]),!N)){if(!a(b)){v.pop();continue}f=b}}}void 0===b&&m&&p.push([h,f]),N?v.pop():(c={inArray:u,index:d,keys:l,edits:p,prev:c},u=Array.isArray(f),l=u?f:t[f.kind]||[],d=-1,p=[],T&&E.push(T),T=f)}while(void 0!==c);0!==p.length&&(y=p[p.length-1][1]);return y},n.visitInParallel=function(e){var n=new Array(e.length);return{enter:function(t){for(var i=0;i<e.length;i++)if(!n[i]){var r=s(e[i],t.kind,!1);if(r){var a=r.apply(e[i],arguments);if(!1===a)n[i]=t;else if(a===o)n[i]=o;else if(void 0!==a)return a}}},leave:function(t){for(var i=0;i<e.length;i++)if(n[i])n[i]===t&&(n[i]=null);else{var r=s(e[i],t.kind,!0);if(r){var a=r.apply(e[i],arguments);if(a===o)n[i]=o;else if(void 0!==a&&!1!==a)return a}}}}},n.visitWithTypeInfo=function(e,n){return{enter:function(t){e.enter(t);var i=s(n,t.kind,!1);if(i){var r=i.apply(n,arguments);return void 0!==r&&(e.leave(t),a(r)&&e.enter(r)),r}},leave:function(t){var i,r=s(n,t.kind,!0);return r&&(i=r.apply(n,arguments)),e.leave(t),i}}},n.getVisitFn=s,n.BREAK=n.QueryDocumentKeys=void 0;var t,i=(t=m)&&t.__esModule?t:{default:t};var r={Name:[],Document:["definitions"],OperationDefinition:["name","variableDefinitions","directives","selectionSet"],VariableDefinition:["variable","type","defaultValue","directives"],Variable:["name"],SelectionSet:["selections"],Field:["alias","name","arguments","directives","selectionSet"],Argument:["name","value"],FragmentSpread:["name","directives"],InlineFragment:["typeCondition","directives","selectionSet"],FragmentDefinition:["name","variableDefinitions","typeCondition","directives","selectionSet"],IntValue:[],FloatValue:[],StringValue:[],BooleanValue:[],NullValue:[],EnumValue:[],ListValue:["values"],ObjectValue:["fields"],ObjectField:["name","value"],Directive:["name","arguments"],NamedType:["name"],ListType:["type"],NonNullType:["type"],SchemaDefinition:["directives","operationTypes"],OperationTypeDefinition:["type"],ScalarTypeDefinition:["description","name","directives"],ObjectTypeDefinition:["description","name","interfaces","directives","fields"],FieldDefinition:["description","name","arguments","type","directives"],InputValueDefinition:["description","name","type","defaultValue","directives"],InterfaceTypeDefinition:["description","name","directives","fields"],UnionTypeDefinition:["description","name","directives","types"],EnumTypeDefinition:["description","name","directives","values"],EnumValueDefinition:["description","name","directives"],InputObjectTypeDefinition:["description","name","directives","fields"],DirectiveDefinition:["description","name","arguments","locations"],SchemaExtension:["directives","operationTypes"],ScalarTypeExtension:["name","directives"],ObjectTypeExtension:["name","interfaces","directives","fields"],InterfaceTypeExtension:["name","directives","fields"],UnionTypeExtension:["name","directives","types"],EnumTypeExtension:["name","directives","values"],InputObjectTypeExtension:["name","directives","fields"]};n.QueryDocumentKeys=r;var o=Object.freeze({});function a(e){return Boolean(e&&"string"==typeof e.kind)}function s(e,n,t){var i=e[n];if(i){if(!t&&"function"==typeof i)return i;var r=t?i.leave:i.enter;if("function"==typeof r)return r}else{var o=t?e.leave:e.enter;if(o){if("function"==typeof o)return o;var a=o[n];if("function"==typeof a)return a}}}n.BREAK=o}));r(_);_.visit,_.visitInParallel,_.visitWithTypeInfo,_.getVisitFn,_.BREAK,_.QueryDocumentKeys;var O=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.print=function(e){return(0,_.visit)(e,{leave:t})};var t={Name:function(e){return e.value},Variable:function(e){return"$"+e.name},Document:function(e){return r(e.definitions,"\n\n")+"\n"},OperationDefinition:function(e){var n=e.operation,t=e.name,i=a("(",r(e.variableDefinitions,", "),")"),o=r(e.directives," "),s=e.selectionSet;return t||o||i||"query"!==n?r([n,r([t,i]),o,s]," "):s},VariableDefinition:function(e){var n=e.variable,t=e.type,i=e.defaultValue,o=e.directives;return n+": "+t+a(" = ",i)+a(" ",r(o," "))},SelectionSet:function(e){return o(e.selections)},Field:function(e){var n=e.alias,t=e.name,i=e.arguments,o=e.directives,s=e.selectionSet;return r([a("",n,": ")+t+a("(",r(i,", "),")"),r(o," "),s]," ")},Argument:function(e){return e.name+": "+e.value},FragmentSpread:function(e){return"..."+e.name+a(" ",r(e.directives," "))},InlineFragment:function(e){var n=e.typeCondition,t=e.directives,i=e.selectionSet;return r(["...",a("on ",n),r(t," "),i]," ")},FragmentDefinition:function(e){var n=e.name,t=e.typeCondition,i=e.variableDefinitions,o=e.directives,s=e.selectionSet;return("fragment ".concat(n).concat(a("(",r(i,", "),")")," ")+"on ".concat(t," ").concat(a("",r(o," ")," "))+s)},IntValue:function(e){return e.value},FloatValue:function(e){return e.value},StringValue:function(e,n){var t=e.value;return e.block?(0,y.printBlockString)(t,"description"===n?"":"  "):JSON.stringify(t)},BooleanValue:function(e){return e.value?"true":"false"},NullValue:function(){return"null"},EnumValue:function(e){return e.value},ListValue:function(e){return"["+r(e.values,", ")+"]"},ObjectValue:function(e){return"{"+r(e.fields,", ")+"}"},ObjectField:function(e){return e.name+": "+e.value},Directive:function(e){return"@"+e.name+a("(",r(e.arguments,", "),")")},NamedType:function(e){return e.name},ListType:function(e){return"["+e.type+"]"},NonNullType:function(e){return e.type+"!"},SchemaDefinition:function(e){var n=e.directives,t=e.operationTypes;return r(["schema",r(n," "),o(t)]," ")},OperationTypeDefinition:function(e){return e.operation+": "+e.type},ScalarTypeDefinition:i((function(e){return r(["scalar",e.name,r(e.directives," ")]," ")})),ObjectTypeDefinition:i((function(e){var n=e.name,t=e.interfaces,i=e.directives,s=e.fields;return r(["type",n,a("implements ",r(t," & ")),r(i," "),o(s)]," ")})),FieldDefinition:i((function(e){var n=e.name,t=e.arguments,i=e.type,o=e.directives;return n+(u(t)?a("(\n",s(r(t,"\n")),"\n)"):a("(",r(t,", "),")"))+": "+i+a(" ",r(o," "))})),InputValueDefinition:i((function(e){var n=e.name,t=e.type,i=e.defaultValue,o=e.directives;return r([n+": "+t,a("= ",i),r(o," ")]," ")})),InterfaceTypeDefinition:i((function(e){var n=e.name,t=e.directives,i=e.fields;return r(["interface",n,r(t," "),o(i)]," ")})),UnionTypeDefinition:i((function(e){var n=e.name,t=e.directives,i=e.types;return r(["union",n,r(t," "),i&&0!==i.length?"= "+r(i," | "):""]," ")})),EnumTypeDefinition:i((function(e){var n=e.name,t=e.directives,i=e.values;return r(["enum",n,r(t," "),o(i)]," ")})),EnumValueDefinition:i((function(e){return r([e.name,r(e.directives," ")]," ")})),InputObjectTypeDefinition:i((function(e){var n=e.name,t=e.directives,i=e.fields;return r(["input",n,r(t," "),o(i)]," ")})),DirectiveDefinition:i((function(e){var n=e.name,t=e.arguments,i=e.repeatable,o=e.locations;return"directive @"+n+(u(t)?a("(\n",s(r(t,"\n")),"\n)"):a("(",r(t,", "),")"))+(i?" repeatable":"")+" on "+r(o," | ")})),SchemaExtension:function(e){var n=e.directives,t=e.operationTypes;return r(["extend schema",r(n," "),o(t)]," ")},ScalarTypeExtension:function(e){return r(["extend scalar",e.name,r(e.directives," ")]," ")},ObjectTypeExtension:function(e){var n=e.name,t=e.interfaces,i=e.directives,s=e.fields;return r(["extend type",n,a("implements ",r(t," & ")),r(i," "),o(s)]," ")},InterfaceTypeExtension:function(e){var n=e.name,t=e.directives,i=e.fields;return r(["extend interface",n,r(t," "),o(i)]," ")},UnionTypeExtension:function(e){var n=e.name,t=e.directives,i=e.types;return r(["extend union",n,r(t," "),i&&0!==i.length?"= "+r(i," | "):""]," ")},EnumTypeExtension:function(e){var n=e.name,t=e.directives,i=e.values;return r(["extend enum",n,r(t," "),o(i)]," ")},InputObjectTypeExtension:function(e){var n=e.name,t=e.directives,i=e.fields;return r(["extend input",n,r(t," "),o(i)]," ")}};function i(e){return function(n){return r([n.description,e(n)],"\n")}}function r(e,n){return e?e.filter((function(e){return e})).join(n||""):""}function o(e){return e&&0!==e.length?"{\n"+s(r(e,"\n"))+"\n}":""}function a(e,n,t){return n?e+n+(t||""):""}function s(e){return e&&"  "+e.replace(/\n/g,"\n  ")}function c(e){return-1!==e.indexOf("\n")}function u(e){return e&&e.some(c)}}));r(O);O.print;var D=o((function(e,n){function t(e){return e.kind===d.Kind.OPERATION_DEFINITION||e.kind===d.Kind.FRAGMENT_DEFINITION}function i(e){return e.kind===d.Kind.SCHEMA_DEFINITION||r(e)||e.kind===d.Kind.DIRECTIVE_DEFINITION}function r(e){return e.kind===d.Kind.SCALAR_TYPE_DEFINITION||e.kind===d.Kind.OBJECT_TYPE_DEFINITION||e.kind===d.Kind.INTERFACE_TYPE_DEFINITION||e.kind===d.Kind.UNION_TYPE_DEFINITION||e.kind===d.Kind.ENUM_TYPE_DEFINITION||e.kind===d.Kind.INPUT_OBJECT_TYPE_DEFINITION}function o(e){return e.kind===d.Kind.SCHEMA_EXTENSION||a(e)}function a(e){return e.kind===d.Kind.SCALAR_TYPE_EXTENSION||e.kind===d.Kind.OBJECT_TYPE_EXTENSION||e.kind===d.Kind.INTERFACE_TYPE_EXTENSION||e.kind===d.Kind.UNION_TYPE_EXTENSION||e.kind===d.Kind.ENUM_TYPE_EXTENSION||e.kind===d.Kind.INPUT_OBJECT_TYPE_EXTENSION}Object.defineProperty(n,"__esModule",{value:!0}),n.isDefinitionNode=function(e){return t(e)||i(e)||o(e)},n.isExecutableDefinitionNode=t,n.isSelectionNode=function(e){return e.kind===d.Kind.FIELD||e.kind===d.Kind.FRAGMENT_SPREAD||e.kind===d.Kind.INLINE_FRAGMENT},n.isValueNode=function(e){return e.kind===d.Kind.VARIABLE||e.kind===d.Kind.INT||e.kind===d.Kind.FLOAT||e.kind===d.Kind.STRING||e.kind===d.Kind.BOOLEAN||e.kind===d.Kind.NULL||e.kind===d.Kind.ENUM||e.kind===d.Kind.LIST||e.kind===d.Kind.OBJECT},n.isTypeNode=function(e){return e.kind===d.Kind.NAMED_TYPE||e.kind===d.Kind.LIST_TYPE||e.kind===d.Kind.NON_NULL_TYPE},n.isTypeSystemDefinitionNode=i,n.isTypeDefinitionNode=r,n.isTypeSystemExtensionNode=o,n.isTypeExtensionNode=a}));r(D);D.isDefinitionNode,D.isExecutableDefinitionNode,D.isSelectionNode,D.isValueNode,D.isTypeNode,D.isTypeSystemDefinitionNode,D.isTypeDefinitionNode,D.isTypeSystemExtensionNode,D.isTypeExtensionNode;var x=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"Source",{enumerable:!0,get:function(){return c.Source}}),Object.defineProperty(n,"getLocation",{enumerable:!0,get:function(){return u.getLocation}}),Object.defineProperty(n,"printLocation",{enumerable:!0,get:function(){return l.printLocation}}),Object.defineProperty(n,"printSourceLocation",{enumerable:!0,get:function(){return l.printSourceLocation}}),Object.defineProperty(n,"Kind",{enumerable:!0,get:function(){return d.Kind}}),Object.defineProperty(n,"TokenKind",{enumerable:!0,get:function(){return p.TokenKind}}),Object.defineProperty(n,"createLexer",{enumerable:!0,get:function(){return N.createLexer}}),Object.defineProperty(n,"parse",{enumerable:!0,get:function(){return I.parse}}),Object.defineProperty(n,"parseValue",{enumerable:!0,get:function(){return I.parseValue}}),Object.defineProperty(n,"parseType",{enumerable:!0,get:function(){return I.parseType}}),Object.defineProperty(n,"print",{enumerable:!0,get:function(){return O.print}}),Object.defineProperty(n,"visit",{enumerable:!0,get:function(){return _.visit}}),Object.defineProperty(n,"visitInParallel",{enumerable:!0,get:function(){return _.visitInParallel}}),Object.defineProperty(n,"visitWithTypeInfo",{enumerable:!0,get:function(){return _.visitWithTypeInfo}}),Object.defineProperty(n,"getVisitFn",{enumerable:!0,get:function(){return _.getVisitFn}}),Object.defineProperty(n,"BREAK",{enumerable:!0,get:function(){return _.BREAK}}),Object.defineProperty(n,"isDefinitionNode",{enumerable:!0,get:function(){return D.isDefinitionNode}}),Object.defineProperty(n,"isExecutableDefinitionNode",{enumerable:!0,get:function(){return D.isExecutableDefinitionNode}}),Object.defineProperty(n,"isSelectionNode",{enumerable:!0,get:function(){return D.isSelectionNode}}),Object.defineProperty(n,"isValueNode",{enumerable:!0,get:function(){return D.isValueNode}}),Object.defineProperty(n,"isTypeNode",{enumerable:!0,get:function(){return D.isTypeNode}}),Object.defineProperty(n,"isTypeSystemDefinitionNode",{enumerable:!0,get:function(){return D.isTypeSystemDefinitionNode}}),Object.defineProperty(n,"isTypeDefinitionNode",{enumerable:!0,get:function(){return D.isTypeDefinitionNode}}),Object.defineProperty(n,"isTypeSystemExtensionNode",{enumerable:!0,get:function(){return D.isTypeSystemExtensionNode}}),Object.defineProperty(n,"isTypeExtensionNode",{enumerable:!0,get:function(){return D.isTypeExtensionNode}}),Object.defineProperty(n,"DirectiveLocation",{enumerable:!0,get:function(){return k.DirectiveLocation}})}));r(x);var K=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.locatedError=function(e,n,t){if(e&&Array.isArray(e.path))return e;return new v.GraphQLError(e&&e.message,e&&e.nodes||n,e&&e.source,e&&e.positions,t,e)}}));r(K);K.locatedError;var A=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.formatError=function(e){e||(0,i.default)(0,"Received null or undefined error.");var n=e.message||"An unknown error occurred.",t=e.locations,r=e.path,o=e.extensions;return o?{message:n,locations:t,path:r,extensions:o}:{message:n,locations:t,path:r}};var t,i=(t=a)&&t.__esModule?t:{default:t}}));r(A);A.formatError;var b=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"GraphQLError",{enumerable:!0,get:function(){return v.GraphQLError}}),Object.defineProperty(n,"printError",{enumerable:!0,get:function(){return v.printError}}),Object.defineProperty(n,"syntaxError",{enumerable:!0,get:function(){return E.syntaxError}}),Object.defineProperty(n,"locatedError",{enumerable:!0,get:function(){return K.locatedError}}),Object.defineProperty(n,"formatError",{enumerable:!0,get:function(){return A.formatError}})}));r(b);var S={parsers:{graphql:{parse:function(e){var i=x;try{var r=function(e,n){var t={allowLegacySDLImplementsInterfaces:!1,experimentalFragmentVariables:!0};try{return e(n,t)}catch(i){return t.allowLegacySDLImplementsInterfaces=!0,e(n,t)}}(i.parse,e);return r.comments=function(e){for(var n=[],t=e.loc.startToken.next;"<EOF>"!==t.kind;)"Comment"===t.kind&&(Object.assign(t,{column:t.column-1}),n.push(t)),t=t.next;return n}(r),function e(t){if(t&&"object"===n(t))for(var i in delete t.startToken,delete t.endToken,delete t.prev,delete t.next,t)e(t[i]);return t}(r),r}catch(e){throw e instanceof b.GraphQLError?t(e.message,{start:{line:e.locations[0].line,column:e.locations[0].column}}):e}},astFormat:"graphql",hasPragma:i,locStart:function(e){return"number"==typeof e.start?e.start:e.loc&&e.loc.start},locEnd:function(e){return"number"==typeof e.end?e.end:e.loc&&e.loc.end}}}},g=S.parsers;e.default=S,e.parsers=g,Object.defineProperty(e,"__esModule",{value:!0})}));
