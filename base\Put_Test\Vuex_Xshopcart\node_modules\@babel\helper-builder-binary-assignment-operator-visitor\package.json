{"name": "@babel/helper-builder-binary-assignment-operator-visitor", "version": "7.10.4", "description": "Helper function to build binary assignment operator visitors", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-builder-binary-assignment-operator-visitor"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-explode-assignable-expression": "^7.10.4", "@babel/types": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df"}