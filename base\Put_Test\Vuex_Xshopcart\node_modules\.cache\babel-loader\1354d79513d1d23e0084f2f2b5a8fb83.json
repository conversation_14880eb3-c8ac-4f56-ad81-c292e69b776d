{"remainingRequest": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\registerServiceWorker.js", "dependencies": [{"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\registerServiceWorker.js", "mtime": 1649424514414}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1657986293299}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1657986298727}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\eslint-loader\\index.js", "mtime": 1657986295398}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:LyogZXNsaW50LWRpc2FibGUgbm8tY29uc29sZSAqLwppbXBvcnQgeyByZWdpc3RlciB9IGZyb20gJ3JlZ2lzdGVyLXNlcnZpY2Utd29ya2VyJzsKCmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7CiAgcmVnaXN0ZXIoIiIuY29uY2F0KHByb2Nlc3MuZW52LkJBU0VfVVJMLCAic2VydmljZS13b3JrZXIuanMiKSwgewogICAgcmVhZHk6IGZ1bmN0aW9uIHJlYWR5KCkgewogICAgICBjb25zb2xlLmxvZygnQXBwIGlzIGJlaW5nIHNlcnZlZCBmcm9tIGNhY2hlIGJ5IGEgc2VydmljZSB3b3JrZXIuXG4nICsgJ0ZvciBtb3JlIGRldGFpbHMsIHZpc2l0IGh0dHBzOi8vZ29vLmdsL0FGc2txQicpOwogICAgfSwKICAgIHJlZ2lzdGVyZWQ6IGZ1bmN0aW9uIHJlZ2lzdGVyZWQoKSB7CiAgICAgIGNvbnNvbGUubG9nKCdTZXJ2aWNlIHdvcmtlciBoYXMgYmVlbiByZWdpc3RlcmVkLicpOwogICAgfSwKICAgIGNhY2hlZDogZnVuY3Rpb24gY2FjaGVkKCkgewogICAgICBjb25zb2xlLmxvZygnQ29udGVudCBoYXMgYmVlbiBjYWNoZWQgZm9yIG9mZmxpbmUgdXNlLicpOwogICAgfSwKICAgIHVwZGF0ZWZvdW5kOiBmdW5jdGlvbiB1cGRhdGVmb3VuZCgpIHsKICAgICAgY29uc29sZS5sb2coJ05ldyBjb250ZW50IGlzIGRvd25sb2FkaW5nLicpOwogICAgfSwKICAgIHVwZGF0ZWQ6IGZ1bmN0aW9uIHVwZGF0ZWQoKSB7CiAgICAgIGNvbnNvbGUubG9nKCdOZXcgY29udGVudCBpcyBhdmFpbGFibGU7IHBsZWFzZSByZWZyZXNoLicpOwogICAgfSwKICAgIG9mZmxpbmU6IGZ1bmN0aW9uIG9mZmxpbmUoKSB7CiAgICAgIGNvbnNvbGUubG9nKCdObyBpbnRlcm5ldCBjb25uZWN0aW9uIGZvdW5kLiBBcHAgaXMgcnVubmluZyBpbiBvZmZsaW5lIG1vZGUuJyk7CiAgICB9LAogICAgZXJyb3I6IGZ1bmN0aW9uIGVycm9yKF9lcnJvcikgewogICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkdXJpbmcgc2VydmljZSB3b3JrZXIgcmVnaXN0cmF0aW9uOicsIF9lcnJvcik7CiAgICB9CiAgfSk7Cn0="}, {"version": 3, "sources": ["D:/No.1 folder/No.2 folder/dev/dev2/vue/base/Vuex_Xshopcart/src/registerServiceWorker.js"], "names": ["register", "process", "env", "NODE_ENV", "BASE_URL", "ready", "console", "log", "registered", "cached", "updatefound", "updated", "offline", "error"], "mappings": "AAAA;AAEA,SAASA,QAAT,QAAyB,yBAAzB;;AAEA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;AACzCH,EAAAA,QAAQ,WAAIC,OAAO,CAACC,GAAR,CAAYE,QAAhB,wBAA6C;AACnDC,IAAAA,KADmD,mBAC1C;AACPC,MAAAA,OAAO,CAACC,GAAR,CACE,0DACA,+CAFF;AAID,KANkD;AAOnDC,IAAAA,UAPmD,wBAOrC;AACZF,MAAAA,OAAO,CAACC,GAAR,CAAY,qCAAZ;AACD,KATkD;AAUnDE,IAAAA,MAVmD,oBAUzC;AACRH,MAAAA,OAAO,CAACC,GAAR,CAAY,0CAAZ;AACD,KAZkD;AAanDG,IAAAA,WAbmD,yBAapC;AACbJ,MAAAA,OAAO,CAACC,GAAR,CAAY,6BAAZ;AACD,KAfkD;AAgBnDI,IAAAA,OAhBmD,qBAgBxC;AACTL,MAAAA,OAAO,CAACC,GAAR,CAAY,2CAAZ;AACD,KAlBkD;AAmBnDK,IAAAA,OAnBmD,qBAmBxC;AACTN,MAAAA,OAAO,CAACC,GAAR,CAAY,+DAAZ;AACD,KArBkD;AAsBnDM,IAAAA,KAtBmD,iBAsB5CA,MAtB4C,EAsBrC;AACZP,MAAAA,OAAO,CAACO,KAAR,CAAc,2CAAd,EAA2DA,MAA3D;AACD;AAxBkD,GAA7C,CAAR;AA0BD", "sourcesContent": ["/* eslint-disable no-console */\n\nimport { register } from 'register-service-worker'\n\nif (process.env.NODE_ENV === 'production') {\n  register(`${process.env.BASE_URL}service-worker.js`, {\n    ready () {\n      console.log(\n        'App is being served from cache by a service worker.\\n' +\n        'For more details, visit https://goo.gl/AFskqB'\n      )\n    },\n    registered () {\n      console.log('Service worker has been registered.')\n    },\n    cached () {\n      console.log('Content has been cached for offline use.')\n    },\n    updatefound () {\n      console.log('New content is downloading.')\n    },\n    updated () {\n      console.log('New content is available; please refresh.')\n    },\n    offline () {\n      console.log('No internet connection found. App is running in offline mode.')\n    },\n    error (error) {\n      console.error('Error during service worker registration:', error)\n    }\n  })\n}\n"]}]}