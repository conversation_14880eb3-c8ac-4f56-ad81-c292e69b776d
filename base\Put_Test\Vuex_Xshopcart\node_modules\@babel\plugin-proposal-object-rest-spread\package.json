{"name": "@babel/plugin-proposal-object-rest-spread", "version": "7.11.0", "description": "Compile object rest and spread to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-object-rest-spread"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.0", "@babel/plugin-transform-parameters": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.11.0", "@babel/helper-plugin-test-runner": "^7.10.4"}, "gitHead": "38dda069eeac2e31bce3f56290998d30bee1ed6b"}