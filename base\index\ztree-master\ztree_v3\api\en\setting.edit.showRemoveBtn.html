<div class="apiDetail">
<div>
	<h2><span>Boolean / Function(treeId, treeNode)</span><span class="path">setting.edit.</span>showRemoveBtn</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.exedit</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Set to show or hide the remove button. It is valid when <span class="highlight_red">[setting.edit.enable = true]</span></p>
			<p>When you click the remove button:</p>
			<p>1. zTree will trigger the <span class="highlight_red">setting.callback.beforeRemove</span> callback, and you can decide whether to allow delete.</p>
			<p>2. If you don't set the 'beforeRemove' or  the 'beforeRemove' callback return true, so zT<PERSON> will trigger the <span class="highlight_red">setting.callback.onRemove</span> callback after remove the node.</p>
			<p>Default: true</p>
		</div>
	</div>
	<h3>Boolean Format</h3>
	<div class="desc">
	<p> true means: show the remove button</p>
	<p> false means: hide the remove button</p>
	</div>
	<h3>Function Parameter Descriptions</h3>
	<div class="desc">
	<h4><b>treeId</b><span>String</span></h4>
	<p>zTree unique identifier: <b class="highlight_red">treeId</b>, easy for users to control.</p>
	<h4 class="topLine"><b>treeNode</b><span>JSON</span></h4>
	<p>JSON data object of the node which show the remove button</p>
	<h4 class="topLine"><b>Return </b><span>Boolean</span></h4>
	<p>Return value is same as 'Boolean Format'</p>
	</div>
	<h3>Examples of setting & function</h3>
	<h4>1. Hide the remove button</h4>
	<pre xmlns=""><code>var setting = {
	edit: {
		enable: true,
		showRemoveBtn: false
	}
};
......</code></pre>
	<h4>2. Hide the remove button of parent node</h4>
	<pre xmlns=""><code>function setRemoveBtn(treeId, treeNode) {
	return !treeNode.isParent;
}
var setting = {
	edit: {
		enable: true,
		showRemoveBtn: setRemoveBtn
	}
};
......</code></pre>
</div>
</div>