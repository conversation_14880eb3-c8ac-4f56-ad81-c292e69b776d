{"name": "@babel/helper-module-transforms", "version": "7.11.0", "description": "Babel helper functions for implementing ES6 module transformations", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-module-transforms"}, "main": "lib/index.js", "dependencies": {"@babel/helper-module-imports": "^7.10.4", "@babel/helper-replace-supers": "^7.10.4", "@babel/helper-simple-access": "^7.10.4", "@babel/helper-split-export-declaration": "^7.11.0", "@babel/template": "^7.10.4", "@babel/types": "^7.11.0", "lodash": "^4.17.19"}}