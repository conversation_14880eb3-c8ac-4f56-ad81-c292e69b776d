{"name": "@babel/helpers", "version": "7.10.4", "description": "Collection of helper functions used by Babel transforms.", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helpers"}, "main": "lib/index.js", "dependencies": {"@babel/template": "^7.10.4", "@babel/traverse": "^7.10.4", "@babel/types": "^7.10.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df"}