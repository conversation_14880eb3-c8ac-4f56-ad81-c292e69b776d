{"name": "@babel/helper-validator-identifier", "version": "7.10.4", "description": "Validate identifier/keywords name", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-validator-identifier"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": "./lib/index.js", "devDependencies": {"charcodes": "^0.2.0", "unicode-13.0.0": "^0.8.0"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df"}