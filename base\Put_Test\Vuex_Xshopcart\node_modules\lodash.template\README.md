# lodash.template v4.5.0

The [Lodash](https://lodash.com/) method `_.template` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.template
```

In Node.js:
```js
var template = require('lodash.template');
```

See the [documentation](https://lodash.com/docs#template) or [package source](https://github.com/lodash/lodash/blob/4.5.0-npm-packages/lodash.template) for more details.
