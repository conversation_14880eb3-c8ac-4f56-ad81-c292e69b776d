{"name": "@babel/plugin-transform-parameters", "version": "7.10.5", "description": "Compile ES2015 default and rest parameters to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-parameters"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-get-function-arity": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}, "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "gitHead": "f7964a9ac51356f7df6404a25b27ba1cffba1ba7"}