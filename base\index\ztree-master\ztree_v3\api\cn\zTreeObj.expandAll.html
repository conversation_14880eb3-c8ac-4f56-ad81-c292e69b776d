<div class="apiDetail">
<div>
	<h2><span>Function(expandFlag)</span><span class="path">zTreeObj.</span>expandAll</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.core</span> 核心 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>展开 / 折叠 全部节点</p>
			<p class="highlight_red">此方法不会触发  beforeExpand / onExpand  和 beforeCollapse / onCollapse 事件回调函数。</p>
			<p class="highlight_red">请通过 zTree 对象执行此方法。</p>
		</div>
	</div>
	<h3>Function 参数说明</h3>
	<div class="desc">
	<h4><b>expandFlag</b><span>Boolean</span></h4>
	<p>expandFlag = true 表示 展开 全部节点</p>
	<p>expandFlag =  false 表示 折叠 全部节点</p>
	<h4 class="topLine"><b>返回值</b><span>Boolean</span></h4>
	<p>返回值表示最终实际操作情况</p>
	<p>true 表示 展开 全部节点</p>
	<p>false 表示 折叠 全部节点</p>
	<p>null 表示 不存在任何父节点</p>
	</div>
	<h3>function 举例</h3>
	<h4>1. 展开全部节点</h4>
	<pre xmlns=""><code>var treeObj = $.fn.zTree.getZTreeObj("tree");
treeObj.expandAll(true);
</code></pre>
</div>
</div>