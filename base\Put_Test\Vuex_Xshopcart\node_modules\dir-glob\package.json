{"name": "dir-glob", "version": "2.2.2", "description": "Convert directories to glob compatible strings", "license": "MIT", "repository": "kevva/dir-glob", "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["convert", "directory", "extensions", "files", "glob"], "dependencies": {"path-type": "^3.0.0"}, "devDependencies": {"ava": "^0.25.0", "del": "^3.0.0", "make-dir": "^1.0.0", "rimraf": "^2.5.0", "xo": "^0.20.3"}}