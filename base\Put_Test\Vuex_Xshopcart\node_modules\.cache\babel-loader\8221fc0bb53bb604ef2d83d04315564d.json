{"remainingRequest": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\components\\HelloWorld.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\components\\HelloWorld.vue", "mtime": 1658035355533}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1657986293299}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1657986298727}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1657986293299}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1657986322488}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["HelloWorld.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,SACA,QADA,EAEA,UAFA,CAGA;AACA;AAJA,OAKA,MALA;AAMA,eAAA;AACA,EAAA,IAAA,EAAA,YADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA,EAAA;AACA,GAJA;AAKA,EAAA,OALA,qBAKA;AACA;AACA;AACA,QAAA,KAAA,MAAA,CAAA,KAAA,CAAA,KAAA,EAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,MAAA,CAAA,KAAA,CAAA,KAAA,CAAA,MAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,EAAA,GAAA,CAAA,KAAA,CAAA;AAAA,OAAA,CAAA;AACA;;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,6BAAA,EAAA,KAAA,MAAA,CAAA,KAAA,CAAA,IAAA;AACA,GAZA;AAaA,EAAA,QAAA,kCAEA,QAAA,CAAA;AACA,IAAA,KAAA,EAAA,eAAA,KAAA;AAAA,aAAA,KAAA,CAAA,IAAA,CAAA,KAAA;AAAA,KADA;AAEA,IAAA,QAAA,EAAA,kBAAA,KAAA;AAAA,aAAA,KAAA,CAAA,IAAA,CAAA,QAAA;AAAA,KAFA;AAGA,IAAA,UAAA,EAAA,oBAAA,KAAA;AAAA,aAAA,KAAA,CAAA,IAAA,CAAA,UAAA;AAAA;AAHA,GAAA,CAFA,GAOA,UAAA,CAAA,CAAA,UAAA,CAAA,CAPA,CAbA;AA+BA,EAAA,OAAA,EAAA;AACA,IAAA,SADA,2BACA;AAAA,UAAA,KAAA,QAAA,KAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAA,MAAA,CAAA,QAAA,CAAA;AACA,QAAA,IAAA,EAAA,WADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAAA;AAIA,KAfA;AAgBA,IAAA,YAhBA,+BAgBA;AAAA,UAAA,KAAA,SAAA,KAAA;AACA;AACA;AACA;AACA;AACA,WAAA,MAAA,CAAA,MAAA,CAAA;AACA,QAAA,IAAA,EAAA,QADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAAA;AAIA;AAzBA,GA/BA,CA0DA;AACA;AACA;;AA5DA,CAAA", "sourcesContent": ["<template>\n  <div class=\"hello\">\n    <ul class=\"shop_container\">\n      <li class=\"shop_container_li\" v-for=\"(item, index) in goodsObj\" :key=\"item.index\">\n        <div class=\"shop_img\">\n          <img width=\"100%\" height=\"100%\" :src=\"item.img\" />\n        </div>\n        <div class=\"shop_detail\">\n          <p>{{ item.name }}</p>\n          <p>{{ item.hint }}</p>\n          <p>￥{{ item.price }}</p>\n          <p>\n            <span class=\"shop_reduce\" @click=\"handleReduce({ index })\">-</span>\n            <span class=\"shop_num\">{{ item.num }}</span>\n            <span class=\"shop_add\" @click=\"handleAdd({ index })\">+</span>\n          </p>\n        </div>\n      </li>\n    </ul>\n    <div class=\"foot\">\n      <div class=\"total_price\">\n        <span>合计：{{ totalNum }}</span>\n      </div>\n      <div class=\"total_num\">\n        <span>去结账：￥{{ totalPrice }}</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  mapState,\n  mapGetters,\n  // mapMutations,\n  // mapActions,\n} from \"vuex\";\nexport default {\n  name: \"HelloWorld\",\n  data() {\n    return {};\n  },\n  created() {\n    // console.log(this.$store.getters.getById(1));\n    // 根据id获取商品值\n    if(this.$store.state.goods){\n      console.log(this.$store.state.goods.filter((item) => item.id - 0 === 1));\n    }\n    console.log('this.$store.state.cart :>> ', this.$store.state.cart );\n  },\n  computed: {\n    // ...mapState(['goods', 'totalNum', 'totalPrice']),\n    ...mapState({\n      goods: (state) => state.cart.goods, \n      totalNum: (state) => state.cart.totalNum,\n      totalPrice: (state) => state.cart.totalPrice,\n    }),\n    ...mapGetters([\"goodsObj\"]),\n    // ...mapState({\n    //   goods: state => state.goods\n    // }),\n    // goods() {\n    //   return this.$store.state.goods\n    // }\n    // goodsObj() {\n    //   return this.$store.getters.goodsObj\n    // }\n  },\n  methods: {\n    handleAdd({ index }) {\n      // console.log(\"%c [ index ]\", \"font-size:13px; background:#00ffff; color:red;\", index)\n      // this.$store.commit('add', index)\n      // this.$store.commit({\n      //   type: 'ADD',\n      //   index\n      // })\n      // this.$store.dispatch('increment', {\n      //   index\n      // })\n      this.$store.dispatch({\n        type: \"increment\",\n        index, \n      });\n    },\n    handleReduce({ index }) {\n      // this.$store.dispatch({\n      //   type: \"reduce\",\n      //   index,\n      // });\n      this.$store.commit({\n        type:'REDUCE',\n        index\n      })\n    },\n  },\n//   答：如果请求的数据是多个组件共享的，为了⽅便只写⼀份，就写vuex⾥⾯，如果是组件独⽤的就写在当前组件⾥⾯。\n// 如果请求来的数据不是要被其他组件公⽤，仅仅在请求的组件内使⽤，就不需要放⼊ vuex 的 state ⾥\n// 如果被其他地⽅复⽤，请将请求放⼊ action ⾥，⽅便复⽤，并包装成 promise 返回\n\n};\n</script>\n\n<!-- Add \"scoped\" attribute to limit CSS to this component only -->\n\n<style scoped>\nbody,\nli,\nul,\np {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.hello {\n  position: relative;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n}\n\n.shop_container {\n  width: 100%;\n}\n\n.shop_container_li {\n  background-color: #f5f5f5;\n  height: 130px;\n  margin-bottom: 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.shop_img {\n  flex: 1;\n  padding: 10px;\n  height: 110px;\n}\n\n.shop_detail {\n  flex: 2;\n}\n\n.shop_detail p {\n  font-size: 14px;\n  line-height: 25px;\n  height: 25px;\n}\n\n.shop_reduce,\n.shop_add {\n  font-size: 18px;\n  font-weight: 700;\n  display: inline-block;\n  text-align: center;\n  width: 20px;\n  height: 20px;\n  border: 1px solid #f5f5f5;\n  background-color: #ffffff;\n}\n\n.shop_num {\n  margin: 0 5px;\n}\n\n.foot {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 40px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.total_price {\n  background-color: #4cd964;\n  flex: 2;\n  height: 100%;\n  line-height: 40px;\n}\n\n.total_num {\n  flex: 1;\n  background-color: #666;\n  height: 100%;\n  line-height: 40px;\n}\n\n.payment {\n  background-color: #3b95e9;\n}\n</style>\n"], "sourceRoot": "src/components"}]}