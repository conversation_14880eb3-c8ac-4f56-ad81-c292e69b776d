2015/AbstractRelationalComparison.js	spackled linguist-generated=true
2015/DateFromTime.js	spackled linguist-generated=true
2015/Day.js	spackled linguist-generated=true
2015/DayFromYear.js	spackled linguist-generated=true
2015/DayWithinYear.js	spackled linguist-generated=true
2015/DaysInYear.js	spackled linguist-generated=true
2015/HourFromTime.js	spackled linguist-generated=true
2015/InLeapYear.js	spackled linguist-generated=true
2015/IsCallable.js	spackled linguist-generated=true
2015/IsPropertyDescriptor.js	spackled linguist-generated=true
2015/MakeDate.js	spackled linguist-generated=true
2015/MakeDay.js	spackled linguist-generated=true
2015/MakeTime.js	spackled linguist-generated=true
2015/MinFromTime.js	spackled linguist-generated=true
2015/MonthFromTime.js	spackled linguist-generated=true
2015/SameValue.js	spackled linguist-generated=true
2015/SecFromTime.js	spackled linguist-generated=true
2015/StrictEqualityComparison.js	spackled linguist-generated=true
2015/TimeClip.js	spackled linguist-generated=true
2015/TimeFromYear.js	spackled linguist-generated=true
2015/TimeWithinDay.js	spackled linguist-generated=true
2015/ToBoolean.js	spackled linguist-generated=true
2015/ToInt32.js	spackled linguist-generated=true
2015/ToPropertyDescriptor.js	spackled linguist-generated=true
2015/ToUint16.js	spackled linguist-generated=true
2015/ToUint32.js	spackled linguist-generated=true
2015/WeekDay.js	spackled linguist-generated=true
2015/YearFromTime.js	spackled linguist-generated=true
2015/modulo.js	spackled linguist-generated=true
2015/msFromTime.js	spackled linguist-generated=true
2016/AbstractEqualityComparison.js	spackled linguist-generated=true
2016/AbstractRelationalComparison.js	spackled linguist-generated=true
2016/AdvanceStringIndex.js	spackled linguist-generated=true
2016/ArrayCreate.js	spackled linguist-generated=true
2016/ArraySetLength.js	spackled linguist-generated=true
2016/ArraySpeciesCreate.js	spackled linguist-generated=true
2016/Call.js	spackled linguist-generated=true
2016/CanonicalNumericIndexString.js	spackled linguist-generated=true
2016/CompletePropertyDescriptor.js	spackled linguist-generated=true
2016/CreateDataProperty.js	spackled linguist-generated=true
2016/CreateDataPropertyOrThrow.js	spackled linguist-generated=true
2016/CreateHTML.js	spackled linguist-generated=true
2016/CreateIterResultObject.js	spackled linguist-generated=true
2016/CreateListFromArrayLike.js	spackled linguist-generated=true
2016/CreateMethodProperty.js	spackled linguist-generated=true
2016/DateFromTime.js	spackled linguist-generated=true
2016/Day.js	spackled linguist-generated=true
2016/DayFromYear.js	spackled linguist-generated=true
2016/DayWithinYear.js	spackled linguist-generated=true
2016/DaysInYear.js	spackled linguist-generated=true
2016/DefinePropertyOrThrow.js	spackled linguist-generated=true
2016/DeletePropertyOrThrow.js	spackled linguist-generated=true
2016/EnumerableOwnNames.js	spackled linguist-generated=true
2016/FromPropertyDescriptor.js	spackled linguist-generated=true
2016/Get.js	spackled linguist-generated=true
2016/GetIterator.js	spackled linguist-generated=true
2016/GetMethod.js	spackled linguist-generated=true
2016/GetOwnPropertyKeys.js	spackled linguist-generated=true
2016/GetPrototypeFromConstructor.js	spackled linguist-generated=true
2016/GetSubstitution.js	spackled linguist-generated=true
2016/GetV.js	spackled linguist-generated=true
2016/HasOwnProperty.js	spackled linguist-generated=true
2016/HasProperty.js	spackled linguist-generated=true
2016/HourFromTime.js	spackled linguist-generated=true
2016/InLeapYear.js	spackled linguist-generated=true
2016/InstanceofOperator.js	spackled linguist-generated=true
2016/Invoke.js	spackled linguist-generated=true
2016/IsAccessorDescriptor.js	spackled linguist-generated=true
2016/IsArray.js	spackled linguist-generated=true
2016/IsCallable.js	spackled linguist-generated=true
2016/IsConcatSpreadable.js	spackled linguist-generated=true
2016/IsConstructor.js	spackled linguist-generated=true
2016/IsDataDescriptor.js	spackled linguist-generated=true
2016/IsExtensible.js	spackled linguist-generated=true
2016/IsGenericDescriptor.js	spackled linguist-generated=true
2016/IsInteger.js	spackled linguist-generated=true
2016/IsPromise.js	spackled linguist-generated=true
2016/IsPropertyDescriptor.js	spackled linguist-generated=true
2016/IsPropertyKey.js	spackled linguist-generated=true
2016/IsRegExp.js	spackled linguist-generated=true
2016/IteratorClose.js	spackled linguist-generated=true
2016/IteratorComplete.js	spackled linguist-generated=true
2016/IteratorNext.js	spackled linguist-generated=true
2016/IteratorStep.js	spackled linguist-generated=true
2016/IteratorValue.js	spackled linguist-generated=true
2016/MakeDate.js	spackled linguist-generated=true
2016/MakeDay.js	spackled linguist-generated=true
2016/MakeTime.js	spackled linguist-generated=true
2016/MinFromTime.js	spackled linguist-generated=true
2016/MonthFromTime.js	spackled linguist-generated=true
2016/ObjectCreate.js	spackled linguist-generated=true
2016/OrdinaryDefineOwnProperty.js	spackled linguist-generated=true
2016/OrdinaryGetOwnProperty.js	spackled linguist-generated=true
2016/OrdinaryHasInstance.js	spackled linguist-generated=true
2016/OrdinaryHasProperty.js	spackled linguist-generated=true
2016/RegExpExec.js	spackled linguist-generated=true
2016/RequireObjectCoercible.js	spackled linguist-generated=true
2016/SameValue.js	spackled linguist-generated=true
2016/SameValueZero.js	spackled linguist-generated=true
2016/SecFromTime.js	spackled linguist-generated=true
2016/Set.js	spackled linguist-generated=true
2016/SetFunctionName.js	spackled linguist-generated=true
2016/SetIntegrityLevel.js	spackled linguist-generated=true
2016/SpeciesConstructor.js	spackled linguist-generated=true
2016/StrictEqualityComparison.js	spackled linguist-generated=true
2016/SymbolDescriptiveString.js	spackled linguist-generated=true
2016/TestIntegrityLevel.js	spackled linguist-generated=true
2016/TimeClip.js	spackled linguist-generated=true
2016/TimeFromYear.js	spackled linguist-generated=true
2016/TimeWithinDay.js	spackled linguist-generated=true
2016/ToBoolean.js	spackled linguist-generated=true
2016/ToDateString.js	spackled linguist-generated=true
2016/ToInt16.js	spackled linguist-generated=true
2016/ToInt32.js	spackled linguist-generated=true
2016/ToInt8.js	spackled linguist-generated=true
2016/ToInteger.js	spackled linguist-generated=true
2016/ToLength.js	spackled linguist-generated=true
2016/ToNumber.js	spackled linguist-generated=true
2016/ToObject.js	spackled linguist-generated=true
2016/ToPrimitive.js	spackled linguist-generated=true
2016/ToPropertyDescriptor.js	spackled linguist-generated=true
2016/ToPropertyKey.js	spackled linguist-generated=true
2016/ToString.js	spackled linguist-generated=true
2016/ToUint16.js	spackled linguist-generated=true
2016/ToUint32.js	spackled linguist-generated=true
2016/ToUint8.js	spackled linguist-generated=true
2016/ToUint8Clamp.js	spackled linguist-generated=true
2016/Type.js	spackled linguist-generated=true
2016/ValidateAndApplyPropertyDescriptor.js	spackled linguist-generated=true
2016/WeekDay.js	spackled linguist-generated=true
2016/YearFromTime.js	spackled linguist-generated=true
2016/modulo.js	spackled linguist-generated=true
2016/msFromTime.js	spackled linguist-generated=true
2016/thisBooleanValue.js	spackled linguist-generated=true
2016/thisNumberValue.js	spackled linguist-generated=true
2016/thisStringValue.js	spackled linguist-generated=true
2016/thisTimeValue.js	spackled linguist-generated=true
2017/AbstractEqualityComparison.js	spackled linguist-generated=true
2017/AbstractRelationalComparison.js	spackled linguist-generated=true
2017/AdvanceStringIndex.js	spackled linguist-generated=true
2017/ArrayCreate.js	spackled linguist-generated=true
2017/ArraySetLength.js	spackled linguist-generated=true
2017/ArraySpeciesCreate.js	spackled linguist-generated=true
2017/Call.js	spackled linguist-generated=true
2017/CanonicalNumericIndexString.js	spackled linguist-generated=true
2017/CompletePropertyDescriptor.js	spackled linguist-generated=true
2017/CreateDataProperty.js	spackled linguist-generated=true
2017/CreateDataPropertyOrThrow.js	spackled linguist-generated=true
2017/CreateHTML.js	spackled linguist-generated=true
2017/CreateIterResultObject.js	spackled linguist-generated=true
2017/CreateListFromArrayLike.js	spackled linguist-generated=true
2017/CreateMethodProperty.js	spackled linguist-generated=true
2017/DateFromTime.js	spackled linguist-generated=true
2017/Day.js	spackled linguist-generated=true
2017/DayFromYear.js	spackled linguist-generated=true
2017/DayWithinYear.js	spackled linguist-generated=true
2017/DaysInYear.js	spackled linguist-generated=true
2017/DefinePropertyOrThrow.js	spackled linguist-generated=true
2017/DeletePropertyOrThrow.js	spackled linguist-generated=true
2017/FromPropertyDescriptor.js	spackled linguist-generated=true
2017/Get.js	spackled linguist-generated=true
2017/GetIterator.js	spackled linguist-generated=true
2017/GetMethod.js	spackled linguist-generated=true
2017/GetOwnPropertyKeys.js	spackled linguist-generated=true
2017/GetPrototypeFromConstructor.js	spackled linguist-generated=true
2017/GetSubstitution.js	spackled linguist-generated=true
2017/GetV.js	spackled linguist-generated=true
2017/HasOwnProperty.js	spackled linguist-generated=true
2017/HasProperty.js	spackled linguist-generated=true
2017/HourFromTime.js	spackled linguist-generated=true
2017/InLeapYear.js	spackled linguist-generated=true
2017/InstanceofOperator.js	spackled linguist-generated=true
2017/Invoke.js	spackled linguist-generated=true
2017/IsAccessorDescriptor.js	spackled linguist-generated=true
2017/IsArray.js	spackled linguist-generated=true
2017/IsCallable.js	spackled linguist-generated=true
2017/IsConcatSpreadable.js	spackled linguist-generated=true
2017/IsConstructor.js	spackled linguist-generated=true
2017/IsDataDescriptor.js	spackled linguist-generated=true
2017/IsExtensible.js	spackled linguist-generated=true
2017/IsGenericDescriptor.js	spackled linguist-generated=true
2017/IsInteger.js	spackled linguist-generated=true
2017/IsPromise.js	spackled linguist-generated=true
2017/IsPropertyDescriptor.js	spackled linguist-generated=true
2017/IsPropertyKey.js	spackled linguist-generated=true
2017/IsRegExp.js	spackled linguist-generated=true
2017/IteratorClose.js	spackled linguist-generated=true
2017/IteratorComplete.js	spackled linguist-generated=true
2017/IteratorNext.js	spackled linguist-generated=true
2017/IteratorStep.js	spackled linguist-generated=true
2017/IteratorValue.js	spackled linguist-generated=true
2017/MakeDate.js	spackled linguist-generated=true
2017/MakeDay.js	spackled linguist-generated=true
2017/MakeTime.js	spackled linguist-generated=true
2017/MinFromTime.js	spackled linguist-generated=true
2017/MonthFromTime.js	spackled linguist-generated=true
2017/ObjectCreate.js	spackled linguist-generated=true
2017/OrdinaryDefineOwnProperty.js	spackled linguist-generated=true
2017/OrdinaryGetOwnProperty.js	spackled linguist-generated=true
2017/OrdinaryGetPrototypeOf.js	spackled linguist-generated=true
2017/OrdinaryHasInstance.js	spackled linguist-generated=true
2017/OrdinaryHasProperty.js	spackled linguist-generated=true
2017/OrdinarySetPrototypeOf.js	spackled linguist-generated=true
2017/RegExpExec.js	spackled linguist-generated=true
2017/RequireObjectCoercible.js	spackled linguist-generated=true
2017/SameValue.js	spackled linguist-generated=true
2017/SameValueNonNumber.js	spackled linguist-generated=true
2017/SameValueZero.js	spackled linguist-generated=true
2017/SecFromTime.js	spackled linguist-generated=true
2017/Set.js	spackled linguist-generated=true
2017/SetFunctionName.js	spackled linguist-generated=true
2017/SetIntegrityLevel.js	spackled linguist-generated=true
2017/SpeciesConstructor.js	spackled linguist-generated=true
2017/StrictEqualityComparison.js	spackled linguist-generated=true
2017/SymbolDescriptiveString.js	spackled linguist-generated=true
2017/TestIntegrityLevel.js	spackled linguist-generated=true
2017/TimeClip.js	spackled linguist-generated=true
2017/TimeFromYear.js	spackled linguist-generated=true
2017/TimeWithinDay.js	spackled linguist-generated=true
2017/ToBoolean.js	spackled linguist-generated=true
2017/ToDateString.js	spackled linguist-generated=true
2017/ToInt16.js	spackled linguist-generated=true
2017/ToInt32.js	spackled linguist-generated=true
2017/ToInt8.js	spackled linguist-generated=true
2017/ToInteger.js	spackled linguist-generated=true
2017/ToLength.js	spackled linguist-generated=true
2017/ToNumber.js	spackled linguist-generated=true
2017/ToObject.js	spackled linguist-generated=true
2017/ToPrimitive.js	spackled linguist-generated=true
2017/ToPropertyDescriptor.js	spackled linguist-generated=true
2017/ToPropertyKey.js	spackled linguist-generated=true
2017/ToString.js	spackled linguist-generated=true
2017/ToUint16.js	spackled linguist-generated=true
2017/ToUint32.js	spackled linguist-generated=true
2017/ToUint8.js	spackled linguist-generated=true
2017/ToUint8Clamp.js	spackled linguist-generated=true
2017/Type.js	spackled linguist-generated=true
2017/ValidateAndApplyPropertyDescriptor.js	spackled linguist-generated=true
2017/WeekDay.js	spackled linguist-generated=true
2017/YearFromTime.js	spackled linguist-generated=true
2017/modulo.js	spackled linguist-generated=true
2017/msFromTime.js	spackled linguist-generated=true
2017/thisBooleanValue.js	spackled linguist-generated=true
2017/thisNumberValue.js	spackled linguist-generated=true
2017/thisStringValue.js	spackled linguist-generated=true
2017/thisTimeValue.js	spackled linguist-generated=true
2018/AbstractEqualityComparison.js	spackled linguist-generated=true
2018/AbstractRelationalComparison.js	spackled linguist-generated=true
2018/AdvanceStringIndex.js	spackled linguist-generated=true
2018/ArrayCreate.js	spackled linguist-generated=true
2018/ArraySetLength.js	spackled linguist-generated=true
2018/ArraySpeciesCreate.js	spackled linguist-generated=true
2018/Call.js	spackled linguist-generated=true
2018/CanonicalNumericIndexString.js	spackled linguist-generated=true
2018/CompletePropertyDescriptor.js	spackled linguist-generated=true
2018/CreateDataProperty.js	spackled linguist-generated=true
2018/CreateDataPropertyOrThrow.js	spackled linguist-generated=true
2018/CreateHTML.js	spackled linguist-generated=true
2018/CreateIterResultObject.js	spackled linguist-generated=true
2018/CreateListFromArrayLike.js	spackled linguist-generated=true
2018/CreateMethodProperty.js	spackled linguist-generated=true
2018/DateFromTime.js	spackled linguist-generated=true
2018/Day.js	spackled linguist-generated=true
2018/DayFromYear.js	spackled linguist-generated=true
2018/DayWithinYear.js	spackled linguist-generated=true
2018/DaysInYear.js	spackled linguist-generated=true
2018/DefinePropertyOrThrow.js	spackled linguist-generated=true
2018/DeletePropertyOrThrow.js	spackled linguist-generated=true
2018/FromPropertyDescriptor.js	spackled linguist-generated=true
2018/Get.js	spackled linguist-generated=true
2018/GetIterator.js	spackled linguist-generated=true
2018/GetMethod.js	spackled linguist-generated=true
2018/GetOwnPropertyKeys.js	spackled linguist-generated=true
2018/GetPrototypeFromConstructor.js	spackled linguist-generated=true
2018/GetV.js	spackled linguist-generated=true
2018/HasOwnProperty.js	spackled linguist-generated=true
2018/HasProperty.js	spackled linguist-generated=true
2018/HourFromTime.js	spackled linguist-generated=true
2018/InLeapYear.js	spackled linguist-generated=true
2018/InstanceofOperator.js	spackled linguist-generated=true
2018/Invoke.js	spackled linguist-generated=true
2018/IsAccessorDescriptor.js	spackled linguist-generated=true
2018/IsArray.js	spackled linguist-generated=true
2018/IsCallable.js	spackled linguist-generated=true
2018/IsConcatSpreadable.js	spackled linguist-generated=true
2018/IsConstructor.js	spackled linguist-generated=true
2018/IsDataDescriptor.js	spackled linguist-generated=true
2018/IsExtensible.js	spackled linguist-generated=true
2018/IsGenericDescriptor.js	spackled linguist-generated=true
2018/IsInteger.js	spackled linguist-generated=true
2018/IsPromise.js	spackled linguist-generated=true
2018/IsPropertyKey.js	spackled linguist-generated=true
2018/IsRegExp.js	spackled linguist-generated=true
2018/IterableToList.js	spackled linguist-generated=true
2018/IteratorClose.js	spackled linguist-generated=true
2018/IteratorComplete.js	spackled linguist-generated=true
2018/IteratorNext.js	spackled linguist-generated=true
2018/IteratorStep.js	spackled linguist-generated=true
2018/IteratorValue.js	spackled linguist-generated=true
2018/MakeDate.js	spackled linguist-generated=true
2018/MakeDay.js	spackled linguist-generated=true
2018/MakeTime.js	spackled linguist-generated=true
2018/MinFromTime.js	spackled linguist-generated=true
2018/MonthFromTime.js	spackled linguist-generated=true
2018/ObjectCreate.js	spackled linguist-generated=true
2018/OrdinaryDefineOwnProperty.js	spackled linguist-generated=true
2018/OrdinaryGetOwnProperty.js	spackled linguist-generated=true
2018/OrdinaryGetPrototypeOf.js	spackled linguist-generated=true
2018/OrdinaryHasInstance.js	spackled linguist-generated=true
2018/OrdinaryHasProperty.js	spackled linguist-generated=true
2018/OrdinarySetPrototypeOf.js	spackled linguist-generated=true
2018/RegExpExec.js	spackled linguist-generated=true
2018/RequireObjectCoercible.js	spackled linguist-generated=true
2018/SameValue.js	spackled linguist-generated=true
2018/SameValueNonNumber.js	spackled linguist-generated=true
2018/SameValueZero.js	spackled linguist-generated=true
2018/SecFromTime.js	spackled linguist-generated=true
2018/Set.js	spackled linguist-generated=true
2018/SetFunctionName.js	spackled linguist-generated=true
2018/SetIntegrityLevel.js	spackled linguist-generated=true
2018/SpeciesConstructor.js	spackled linguist-generated=true
2018/StrictEqualityComparison.js	spackled linguist-generated=true
2018/SymbolDescriptiveString.js	spackled linguist-generated=true
2018/TestIntegrityLevel.js	spackled linguist-generated=true
2018/TimeClip.js	spackled linguist-generated=true
2018/TimeFromYear.js	spackled linguist-generated=true
2018/TimeWithinDay.js	spackled linguist-generated=true
2018/ToBoolean.js	spackled linguist-generated=true
2018/ToDateString.js	spackled linguist-generated=true
2018/ToIndex.js	spackled linguist-generated=true
2018/ToInt16.js	spackled linguist-generated=true
2018/ToInt32.js	spackled linguist-generated=true
2018/ToInt8.js	spackled linguist-generated=true
2018/ToInteger.js	spackled linguist-generated=true
2018/ToLength.js	spackled linguist-generated=true
2018/ToNumber.js	spackled linguist-generated=true
2018/ToObject.js	spackled linguist-generated=true
2018/ToPrimitive.js	spackled linguist-generated=true
2018/ToPropertyDescriptor.js	spackled linguist-generated=true
2018/ToPropertyKey.js	spackled linguist-generated=true
2018/ToString.js	spackled linguist-generated=true
2018/ToUint16.js	spackled linguist-generated=true
2018/ToUint32.js	spackled linguist-generated=true
2018/ToUint8.js	spackled linguist-generated=true
2018/ToUint8Clamp.js	spackled linguist-generated=true
2018/Type.js	spackled linguist-generated=true
2018/ValidateAndApplyPropertyDescriptor.js	spackled linguist-generated=true
2018/WeekDay.js	spackled linguist-generated=true
2018/YearFromTime.js	spackled linguist-generated=true
2018/modulo.js	spackled linguist-generated=true
2018/msFromTime.js	spackled linguist-generated=true
2018/thisBooleanValue.js	spackled linguist-generated=true
2018/thisNumberValue.js	spackled linguist-generated=true
2018/thisStringValue.js	spackled linguist-generated=true
2018/thisTimeValue.js	spackled linguist-generated=true
2019/AbstractEqualityComparison.js	spackled linguist-generated=true
2019/AbstractRelationalComparison.js	spackled linguist-generated=true
2019/AdvanceStringIndex.js	spackled linguist-generated=true
2019/ArrayCreate.js	spackled linguist-generated=true
2019/ArraySetLength.js	spackled linguist-generated=true
2019/ArraySpeciesCreate.js	spackled linguist-generated=true
2019/Call.js	spackled linguist-generated=true
2019/CanonicalNumericIndexString.js	spackled linguist-generated=true
2019/CompletePropertyDescriptor.js	spackled linguist-generated=true
2019/CopyDataProperties.js	spackled linguist-generated=true
2019/CreateDataProperty.js	spackled linguist-generated=true
2019/CreateDataPropertyOrThrow.js	spackled linguist-generated=true
2019/CreateHTML.js	spackled linguist-generated=true
2019/CreateIterResultObject.js	spackled linguist-generated=true
2019/CreateListFromArrayLike.js	spackled linguist-generated=true
2019/CreateMethodProperty.js	spackled linguist-generated=true
2019/DateFromTime.js	spackled linguist-generated=true
2019/DateString.js	spackled linguist-generated=true
2019/Day.js	spackled linguist-generated=true
2019/DayFromYear.js	spackled linguist-generated=true
2019/DayWithinYear.js	spackled linguist-generated=true
2019/DaysInYear.js	spackled linguist-generated=true
2019/DefinePropertyOrThrow.js	spackled linguist-generated=true
2019/DeletePropertyOrThrow.js	spackled linguist-generated=true
2019/EnumerableOwnPropertyNames.js	spackled linguist-generated=true
2019/FromPropertyDescriptor.js	spackled linguist-generated=true
2019/Get.js	spackled linguist-generated=true
2019/GetIterator.js	spackled linguist-generated=true
2019/GetMethod.js	spackled linguist-generated=true
2019/GetOwnPropertyKeys.js	spackled linguist-generated=true
2019/GetPrototypeFromConstructor.js	spackled linguist-generated=true
2019/GetSubstitution.js	spackled linguist-generated=true
2019/GetV.js	spackled linguist-generated=true
2019/HasOwnProperty.js	spackled linguist-generated=true
2019/HasProperty.js	spackled linguist-generated=true
2019/HourFromTime.js	spackled linguist-generated=true
2019/InLeapYear.js	spackled linguist-generated=true
2019/InstanceofOperator.js	spackled linguist-generated=true
2019/Invoke.js	spackled linguist-generated=true
2019/IsAccessorDescriptor.js	spackled linguist-generated=true
2019/IsArray.js	spackled linguist-generated=true
2019/IsCallable.js	spackled linguist-generated=true
2019/IsConcatSpreadable.js	spackled linguist-generated=true
2019/IsConstructor.js	spackled linguist-generated=true
2019/IsDataDescriptor.js	spackled linguist-generated=true
2019/IsExtensible.js	spackled linguist-generated=true
2019/IsGenericDescriptor.js	spackled linguist-generated=true
2019/IsInteger.js	spackled linguist-generated=true
2019/IsPromise.js	spackled linguist-generated=true
2019/IsPropertyKey.js	spackled linguist-generated=true
2019/IsRegExp.js	spackled linguist-generated=true
2019/IsStringPrefix.js	spackled linguist-generated=true
2019/IterableToList.js	spackled linguist-generated=true
2019/IteratorClose.js	spackled linguist-generated=true
2019/IteratorComplete.js	spackled linguist-generated=true
2019/IteratorNext.js	spackled linguist-generated=true
2019/IteratorStep.js	spackled linguist-generated=true
2019/IteratorValue.js	spackled linguist-generated=true
2019/MakeDate.js	spackled linguist-generated=true
2019/MakeDay.js	spackled linguist-generated=true
2019/MakeTime.js	spackled linguist-generated=true
2019/MinFromTime.js	spackled linguist-generated=true
2019/MonthFromTime.js	spackled linguist-generated=true
2019/NumberToString.js	spackled linguist-generated=true
2019/ObjectCreate.js	spackled linguist-generated=true
2019/OrdinaryDefineOwnProperty.js	spackled linguist-generated=true
2019/OrdinaryGetOwnProperty.js	spackled linguist-generated=true
2019/OrdinaryGetPrototypeOf.js	spackled linguist-generated=true
2019/OrdinaryHasInstance.js	spackled linguist-generated=true
2019/OrdinaryHasProperty.js	spackled linguist-generated=true
2019/OrdinarySetPrototypeOf.js	spackled linguist-generated=true
2019/PromiseResolve.js	spackled linguist-generated=true
2019/RegExpExec.js	spackled linguist-generated=true
2019/RequireObjectCoercible.js	spackled linguist-generated=true
2019/SameValue.js	spackled linguist-generated=true
2019/SameValueNonNumber.js	spackled linguist-generated=true
2019/SameValueZero.js	spackled linguist-generated=true
2019/SecFromTime.js	spackled linguist-generated=true
2019/Set.js	spackled linguist-generated=true
2019/SetFunctionName.js	spackled linguist-generated=true
2019/SetIntegrityLevel.js	spackled linguist-generated=true
2019/SpeciesConstructor.js	spackled linguist-generated=true
2019/StrictEqualityComparison.js	spackled linguist-generated=true
2019/SymbolDescriptiveString.js	spackled linguist-generated=true
2019/TestIntegrityLevel.js	spackled linguist-generated=true
2019/TimeClip.js	spackled linguist-generated=true
2019/TimeFromYear.js	spackled linguist-generated=true
2019/TimeString.js	spackled linguist-generated=true
2019/TimeWithinDay.js	spackled linguist-generated=true
2019/ToBoolean.js	spackled linguist-generated=true
2019/ToDateString.js	spackled linguist-generated=true
2019/ToIndex.js	spackled linguist-generated=true
2019/ToInt16.js	spackled linguist-generated=true
2019/ToInt32.js	spackled linguist-generated=true
2019/ToInt8.js	spackled linguist-generated=true
2019/ToInteger.js	spackled linguist-generated=true
2019/ToLength.js	spackled linguist-generated=true
2019/ToNumber.js	spackled linguist-generated=true
2019/ToObject.js	spackled linguist-generated=true
2019/ToPrimitive.js	spackled linguist-generated=true
2019/ToPropertyDescriptor.js	spackled linguist-generated=true
2019/ToPropertyKey.js	spackled linguist-generated=true
2019/ToString.js	spackled linguist-generated=true
2019/ToUint16.js	spackled linguist-generated=true
2019/ToUint32.js	spackled linguist-generated=true
2019/ToUint8.js	spackled linguist-generated=true
2019/ToUint8Clamp.js	spackled linguist-generated=true
2019/Type.js	spackled linguist-generated=true
2019/ValidateAndApplyPropertyDescriptor.js	spackled linguist-generated=true
2019/WeekDay.js	spackled linguist-generated=true
2019/YearFromTime.js	spackled linguist-generated=true
2019/modulo.js	spackled linguist-generated=true
2019/msFromTime.js	spackled linguist-generated=true
2019/thisBooleanValue.js	spackled linguist-generated=true
2019/thisNumberValue.js	spackled linguist-generated=true
2019/thisStringValue.js	spackled linguist-generated=true
2019/thisSymbolValue.js	spackled linguist-generated=true