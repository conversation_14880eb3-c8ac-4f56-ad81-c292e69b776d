<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8" />
<title>只能JS获取括号内容??，无法只拿括号？</title>
</head>
<body>
<script type="text/javascript">
var str="123{xxxx}456[我的]789123[你的]456(1389090)789";
var rx2 = /([\[\{<])([\s\S]+?)([\]\}>])]/;
var regex1 = /\((.+?)\)/g; // () 小括号
var regex2 = /\[(.+?)\]/g; // [] 中括号
var regex3 = /\{(.+?)\}/g; // {} 花括号，大括号
// 输出是一个数组
console.log('rx2===>',  rx2.test(str)); 
// console.log(str.match(regex2));
// console.log(str.match(regex3));
</script>
</body>
</html>

