{"name": "cli-highlight", "version": "2.1.4", "description": "Syntax highlighting in your terminal", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "bin", "README.md", "LICENSE.txt"], "bin": {"highlight": "./bin/highlight"}, "engines": {"node": ">=8.0.0", "npm": ">=5.0.0"}, "scripts": {"test": "jest", "lint": "npm run tslint && npm run prettier", "tslint": "tslint -c tslint.json -p tsconfig.json", "prettier": "prettier --write --list-different '**/{*.ts,*.json,.prettierrc}'", "build": "tsc -p .", "watch": "tsc -p . -w", "typedoc": "typedoc --media media --mode file --excludeNotExported --out typedoc src/index.ts", "semantic-release": "semantic-release"}, "jest": {"collectCoverage": true, "transform": {"^.+\\.tsx?$": "ts-jest"}, "testRegex": "(/test/.*|/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "testPathIgnorePatterns": ["/node_modules/", "/src/test/__fixtures__/"], "coverageReporters": ["json", "text"], "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json"]}, "husky": {"hooks": {"commit-msg": "commitlint -e $HUSKY_GIT_PARAMS"}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"type": "git", "url": "https://github.com/felixfbecker/cli-highlight.git"}, "keywords": ["terminal", "syntax", "highlight", "color", "cli", "ansi"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/felixfbecker/cli-highlight/issues"}, "homepage": "https://github.com/felixfbecker/cli-highlight#readme", "dependencies": {"chalk": "^3.0.0", "highlight.js": "^9.6.0", "mz": "^2.4.0", "parse5": "^5.1.1", "parse5-htmlparser2-tree-adapter": "^5.1.1", "yargs": "^15.0.0"}, "devDependencies": {"@commitlint/cli": "^8.2.0", "@commitlint/config-conventional": "^8.2.0", "@sourcegraph/prettierrc": "^3.0.0", "@sourcegraph/tslint-config": "^11.0.1", "@types/highlight.js": "^9.12.1", "@types/jest": "^24.0.9", "@types/mz": "0.0.32", "@types/node": "^8.0.53", "@types/parse5": "^5.0.2", "@types/parse5-htmlparser2-tree-adapter": "^5.0.1", "@types/yargs": "^13.0.0", "husky": "^3.0.0", "jest": "^24.1.0", "prettier": "^1.12.1", "semantic-release": "^15.13.4", "ts-jest": "^24.0.0", "tslint": "^5.8.0", "typedoc": "^0.15.0", "typescript": "~3.6.4"}}