<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">

<head>
    <meta charset="UTF-8">
    <title>Sleep Calculator - Best Time to Sleep and Wake Up</title>

    <meta name="description"
        content="Want the perfect wake-up time? Find the best time to sleep or wake up, ensuring you&#039;re well-rested and full of energy with our sleep calculator.">

    <meta name="twitter:title" content="Sleep Calculator - Best Time to Sleep and Wake Up">
    <meta name="twitter:description"
        content="Want the perfect wake-up time? Find the best time to sleep or wake up, ensuring you&#039;re well-rested and full of energy with our sleep calculator.">
    <meta name="twitter:url" content="https://sleepcalculator.app/">
    <meta name="twitter:image" content="https://sleepcalculator.app/assets/sleep-calculator.jpg">
    <meta name="twitter:card" content="summary">

    <meta property="og:type" content="website">
    <meta property="og:url" content="https://sleepcalculator.app/">
    <meta property="og:title" content="Sleep Calculator - Best Time to Sleep and Wake Up">
    <meta property="og:description"
        content="Want the perfect wake-up time? Find the best time to sleep or wake up, ensuring you&#039;re well-rested and full of energy with our sleep calculator.">
    <meta property="og:image" content="https://sleepcalculator.app/assets/sleep-calculator.jpg">

    <link rel="apple-touch-icon" sizes="180x180" href="https://sleepcalculator.app/public/icons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://sleepcalculator.app/public/icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://sleepcalculator.app/public/icons/favicon-16x16.png">
    <link rel="icon" href="https://sleepcalculator.app/public/icons/favicon.ico">
    <meta name="mobile-web-app-capable" content="yes">
    <link rel="manifest" href="https://sleepcalculator.app/public/icons/manifest.json">


    <link rel="canonical" href="https://sleepcalculator.app/">
    <link rel="alternate" hreflang="x-default" href="https://sleepcalculator.app/">
    <link rel="alternate" hreflang="en" href="https://sleepcalculator.app/">
    <link rel="alternate" hreflang="da" href="https://sleepcalculator.app/da/">
    <link rel="alternate" hreflang="de" href="https://sleepcalculator.app/de/">
    <link rel="alternate" hreflang="es" href="https://sleepcalculator.app/es/">
    <link rel="alternate" hreflang="fr" href="https://sleepcalculator.app/fr/">
    <link rel="alternate" hreflang="he" href="https://sleepcalculator.app/he/">
    <link rel="alternate" hreflang="it" href="https://sleepcalculator.app/it/">
    <link rel="alternate" hreflang="ja" href="https://sleepcalculator.app/ja/">
    <link rel="alternate" hreflang="ko" href="https://sleepcalculator.app/ko/">
    <link rel="alternate" hreflang="nl" href="https://sleepcalculator.app/nl/">
    <link rel="alternate" hreflang="pl" href="https://sleepcalculator.app/pl/">
    <link rel="alternate" hreflang="pt" href="https://sleepcalculator.app/pt/">
    <link rel="alternate" hreflang="ru" href="https://sleepcalculator.app/ru/">
    <link rel="alternate" hreflang="sv" href="https://sleepcalculator.app/sv/">
    <link rel="alternate" hreflang="uk" href="https://sleepcalculator.app/uk/">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://sleepcalculator.app/css/style.css">

    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-1192275768252854"
        crossorigin="anonymous"></script>
    <link rel='stylesheet' href='https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&amp;display=swap'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css'>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=0">
    <style>
        .nav-tabs .nav-link {
            border: none;
        }

        .nav-tabs .nav-link.active {
            border-bottom: 2px solid #ad9c36;
            background-color: transparent;
        }

        .nav-tabs .nav-link:hover {
            border-bottom: 2px solid #ad9c36;
        }
    </style>
</head>

<body class="bg-body-tertiary">
    <!-- Header -->
    <header>
        <nav class="d-flex align-items-center p-3">
            <!-- Left Section (Menu Button) -->

            <div class="d-flex align-items-center m-0">
                <ul class="navbar-nav d-flex align-items-center mb-0">
                    <li class="nav-item dropdown m-0">
                        <i class="nav-link hover-effect d-flex align-items-center justify-content-center fa-solid fa-earth-americas m-0"
                            role="button" data-bs-toggle="dropdown" data-bs-auto-close="outside"
                            aria-expanded="false"></i>
                        <ul class="dropdown-menu shadow">
                            <li><a class="dropdown-item" href="https://sleepcalculator.app/">English</a></li>
                            <li><a class="dropdown-item" href="https://sleepcalculator.app/da/">Dansk</a></li>
                            <li><a class="dropdown-item" href="https://sleepcalculator.app/es/">Español</a></li>
                            <li><a class="dropdown-item" href="https://sleepcalculator.app/de/">Deutsch</a></li>
                            <li><a class="dropdown-item" href="https://sleepcalculator.app/fr/">Français</a></li>
                            <li><a class="dropdown-item" href="https://sleepcalculator.app/he/">עברית</a></li>
                            <li><a class="dropdown-item" href="https://sleepcalculator.app/it/">Italiano</a></li>
                            <li><a class="dropdown-item" href="https://sleepcalculator.app/ja/">日本語</a></li>
                            <li><a class="dropdown-item" href="https://sleepcalculator.app/ko/">한국어</a></li>
                            <li><a class="dropdown-item" href="https://sleepcalculator.app/nl/">Nederlands</a>
                            </li>
                            <li><a class="dropdown-item" href="https://sleepcalculator.app/pl/">Polski</a></li>
                            <li><a class="dropdown-item" href="https://sleepcalculator.app/pt/">Português</a>
                            </li>
                            <li><a class="dropdown-item" href="https://sleepcalculator.app/ru/">Русский</a></li>
                            <li><a class="dropdown-item" href="https://sleepcalculator.app/sv/">Svenska</a></li>
                            <li><a class="dropdown-item" href="https://sleepcalculator.app/uk/">Українська</a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>

            <!-- Center Section (Logo) -->
            <div class="text-center flex-grow-1 m-0">
                <h1 class="h2 responsive-h1 m-0"><img src="https://sleepcalculator.app/assets/logo-icon.svg" width="30"
                        height="30" alt="Logo"> Sleep Calculator</h1>
            </div>

            <!-- Right Section (Sun Icon and Dropdown) -->
            <div class="d-flex align-items-center m-0">
                <ul class="navbar-nav d-flex align-items-center mb-0">
                    <li class="nav-item m-0">
                        <i class="nav-link hover-effect d-flex align-items-center justify-content-center fa fa-cog m-0"
                            role="button" data-bs-toggle="modal" data-bs-target="#settingsModal"></i>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="container my-5">

        <section>
            <ul class="nav nav-tabs nav-justified" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link active" id="bedtime-tab" data-bs-toggle="tab" href="#bedtime" role="tab"
                        aria-controls="bedtime" aria-selected="true">Bedtime</a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="wakeup-tab" data-bs-toggle="tab" href="#wakeup" role="tab"
                        aria-controls="wakeup" aria-selected="false">Wake-up Time</a>
                </li>
            </ul>

            <div class="tab-content mt-4">
                <div class="tab-pane fade show active" id="bedtime" role="tabpanel" aria-labelledby="bedtime-tab">
                    <div class="mb-4">
                        <p class="lead mb-4">What time do you want to wake up?</p>
                        <input type="time" class="form-control form-control-lg mb-4" id="wakeTime" value="07:00">
                    </div>
                    <button id="calculate" class="btn btn-warning w-100 btn-lg"><i class="fa fa-bed"></i>
                        Calculate Bedtime</button>
                    <div id="bedtimeResult" class="mt-3"></div>
                </div>

                <div class="tab-pane fade" id="wakeup" role="tabpanel" aria-labelledby="wakeup-tab">
                    <p class="lead">What time do you want to go to sleep?</p>
                    <button id="bedNow" class="btn btn-warning">Now</button>
                    <button id="bed30MinLater" class="btn btn-warning">30 Minutes Later</button>
                    <button id="bed1HourLater" class="btn btn-warning">1 Hour Later</button>

                    <div class="mt-3 mb-3">
                        <label for="customBedTime" class="form-label lead">Or specify a time:</label>
                        <input type="time" class="form-control form-control-lg mb-4" id="customBedTime" value="20:00">
                    </div>
                    <button id="bedCustomTime" class="btn btn-warning w-100 btn-lg"><i class="fa fa-clock"></i>
                        Use Specified Time</button>
                    <div id="wakeupResult" class="mt-3"></div>
                </div>
            </div>

            <div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel"
                aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="settingsModalLabel">Settings</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form>
                                <div class="mb-3">
                                    <label for="sleepCycleLength" class="form-label">My sleep cycle length is</label>
                                    <input type="number" class="form-control" id="sleepCycleLength" value="90">
                                    <div class="form-text">Minutes</div>
                                </div>
                                <div class="mb-3">
                                    <label for="fallAsleepTime" class="form-label">It takes me</label>
                                    <input type="number" class="form-control" id="fallAsleepTime" value="15">
                                    <div class="form-text">Minutes to fall asleep</div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-warning" id="saveSettings">Save Settings</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <article class="mt-5">
            <h2>How Does the Sleep Calculator Work?</h2>
            <p>If you wake up in the morning feeling unrefreshed or thinking you&#039;re not a morning person, try our
                online sleep
                calculator. It tells you the best times to go to bed or wake up based on when you want to rise. The
                calculator
                uses the idea that sleep cycles last about 90 minutes and that it takes approximately 15 minutes to fall
                asleep.</p>

            <h2>What is a Sleep Cycle?</h2>
            <p>A sleep cycle is the pattern of a 90-minute period marked by sleep and wakefulness that repeats
                throughout the
                night. Your body&#039;s internal clock, which responds to light and dark, helps manage this cycle.
                Sometimes, things
                like travel across time zones can throw off this rhythm.</p>

            <h2>Why is a Sleep Cycle Important?</h2>
            <p>Following a regular sleep cycle helps you feel more rested. Disruptions in this cycle, such as staying up
                too
                late or waking up often, can make you feel tired the next day.</p>

            <h2>How Does Sleep Affect You?</h2>
            <p>Good sleep lets you wake up feeling fresh and alert. If you don&#039;t sleep well, you might feel tired
                or have
                trouble focusing during the day.</p>

            <h2>How Much Sleep Do I Need?</h2>
            <p>The amount of sleep you need varies by age. Here&#039;s a simple table based on CDC recommendations:</p>
            <table class="table table-dark">
                <tr>
                    <th>Age Group</th>
                    <th>Recommended Sleep</th>
                </tr>
                <tr>
                    <td>0–3 months</td>
                    <td>14–17 hours</td>
                </tr>
                <tr>
                    <td>4–12 months</td>
                    <td>12–16 hours</td>
                </tr>
                <tr>
                    <td>1–2 years</td>
                    <td>11–14 hours</td>
                </tr>
                <tr>
                    <td>3–5 years</td>
                    <td>10–13 hours</td>
                </tr>
                <tr>
                    <td>6–12 years</td>
                    <td>9–12 hours</td>
                </tr>
                <tr>
                    <td>13–18 years</td>
                    <td>8–10 hours</td>
                </tr>
                <tr>
                    <td>18–60 years</td>
                    <td>7+ hours</td>
                </tr>
                <tr>
                    <td>61–64 years</td>
                    <td>7–9 hours</td>
                </tr>
                <tr>
                    <td>65 years and up</td>
                    <td>7–8 hours</td>
                </tr>
            </table>

            <h2>When Should I Go to Bed?</h2>
            <p>Input your wake-up time to get suggested bedtimes. These times help you wake up feeling more refreshed.
            </p>

            <h2>What If I Go to Bed Right Now?</h2>
            <p>Click &#039;Wake-up Time&#039; and then click &#039;Now&#039; to see the best times to wake up if you go
                to bed immediately.</p>

            <h2>Can I Set My Own Bedtime?</h2>
            <p>Yes, you can choose a specific time to go to bed and find out the best times to wake up.</p>

            <p>You can read more about <a href="https://en.wikipedia.org/wiki/Sleep_cycle" target="_blank"
                    rel="noreferrer">sleep cycle here</a>,
                or about <a href="https://en.wikipedia.org/wiki/Sleep_inertia" target="_blank" rel="noreferrer">sleep
                    inertia here</a>.</p>
        </article>

        <div class="mt-5">
            <div class="share-container">
                <h3 class="h2 mb-4 mt-4">Share</h3>
                <div class="share-buttons">
                    <!-- Facebook Button -->
                    <button id="facebook-share" aria-label="facebook" class="btn btn-sm btn-social-outline">
                        <svg viewBox="0 0 64 64" width="32" height="32">
                            <circle cx="32" cy="32" r="31" fill="#3b5998"></circle>
                            <path
                                d="M34.1,47V33.3h4.6l0.7-5.3h-5.3v-3.4c0-1.5,0.4-2.6,2.6-2.6l2.8,0v-4.8c-0.5-0.1-2.2-0.2-4.1-0.2 c-4.1,0-6.9,2.5-6.9,7V28H24v5.3h4.6V47H34.1z"
                                fill="white"></path>
                        </svg>
                    </button>

                    <!-- Twitter Button -->
                    <button id="twitter-share" aria-label="twitter" class="btn btn-sm btn-social-outline">
                        <svg viewBox="0 0 64 64" width="32" height="32">
                            <circle cx="32" cy="32" r="31" fill="#00aced"></circle>
                            <path
                                d="M48,22.1c-1.2,0.5-2.4,0.9-3.8,1c1.4-0.8,2.4-2.1,2.9-3.6c-1.3,0.8-2.7,1.3-4.2,1.6 C41.7,19.8,40,19,38.2,19c-3.6,0-6.6,2.9-6.6,6.6c0,0.5,0.1,1,0.2,1.5c-5.5-0.3-10.3-2.9-13.5-6.9c-0.6,1-0.9,2.1-0.9,3.3 c0,2.3,1.2,4.3,2.9,5.5c-1.1,0-2.1-0.3-3-0.8c0,0,0,0.1,0,0.1c0,3.2,2.3,5.8,5.3,6.4c-0.6,0.1-1.1,0.2-1.7,0.2c-0.4,0-0.8,0-1.2-0.1 c0.8,2.6,3.3,4.5,6.1,4.6c-2.2,1.8-5.1,2.8-8.2,2.8c-0.5,0-1.1,0-1.6-0.1c2.9,1.9,6.4,2.9,10.1,2.9c12.1,0,18.7-10,18.7-18.7 c0-0.3,0-0.6,0-0.8C46,24.5,47.1,23.4,48,22.1z"
                                fill="white"></path>
                        </svg>
                    </button>

                    <!-- LinkedIn Button -->
                    <button id="linkedin-share" aria-label="linkedin" class="btn btn-sm btn-social-outline">
                        <svg viewBox="0 0 64 64" width="32" height="32">
                            <circle cx="32" cy="32" r="31" fill="#007fb1"></circle>
                            <path
                                d="M20.4,44h5.4V26.6h-5.4V44z M23.1,18c-1.7,0-3.1,1.4-3.1,3.1c0,1.7,1.4,3.1,3.1,3.1 c1.7,0,3.1-1.4,3.1-3.1C26.2,19.4,24.8,18,23.1,18z M39.5,26.2c-2.6,0-4.4,1.4-5.1,2.8h-0.1v-2.4h-5.2V44h5.4v-8.6 c0-2.3,0.4-4.5,3.2-4.5c2.8,0,2.8,2.6,2.8,4.6V44H46v-9.5C46,29.8,45,26.2,39.5,26.2z"
                                fill="white"></path>
                        </svg>
                    </button>

                    <!-- Reddit Button -->
                    <button id="reddit-share" aria-label="reddit" class="btn btn-sm btn-social-outline">
                        <svg viewBox="0 0 64 64" width="32" height="32">
                            <circle cx="32" cy="32" r="31" fill="#ff4500"></circle>
                            <path
                                d="m 52.8165,31.942362 c 0,-2.4803 -2.0264,-4.4965 -4.5169,-4.4965 -1.2155,0 -2.3171,0.4862 -3.128,1.2682 -3.077,-2.0247 -7.2403,-3.3133 -11.8507,-3.4782 l 2.5211,-7.9373 6.8272,1.5997 -0.0102,0.0986 c 0,2.0281 1.6575,3.6771 3.6958,3.6771 2.0366,0 3.6924,-1.649 3.6924,-3.6771 0,-2.0281 -1.6575,-3.6788 -3.6924,-3.6788 -1.564,0 -2.8968,0.9758 -3.4357,2.3443 l -7.3593,-1.7255 c -0.3213,-0.0782 -0.6477,0.1071 -0.748,0.4233 L 32,25.212062 c -4.8246,0.0578 -9.1953,1.3566 -12.41,3.4425 -0.8058,-0.7446 -1.8751,-1.2104 -3.0583,-1.2104 -2.4905,0 -4.5152,2.0179 -4.5152,4.4982 0,1.649 0.9061,3.0787 2.2389,3.8607 -0.0884,0.4794 -0.1462,0.9639 -0.1462,1.4569 0,6.6487 8.1736,12.0581 18.2223,12.0581 10.0487,0 18.224,-5.4094 18.224,-12.0581 0,-0.4658 -0.0493,-0.9248 -0.1275,-1.377 1.4144,-0.7599 2.3885,-2.2304 2.3885,-3.9406 z m -29.2808,3.0872 c 0,-1.4756 1.207,-2.6775 2.6894,-2.6775 1.4824,0 2.6877,1.2019 2.6877,2.6775 0,1.4756 -1.2053,2.6758 -2.6877,2.6758 -1.4824,0 -2.6894,-1.2002 -2.6894,-2.6758 z m 15.4037,7.9373 c -1.3549,1.3481 -3.4816,2.0043 -6.5008,2.0043 l -0.0221,-0.0051 -0.0221,0.0051 c -3.0209,0 -5.1476,-0.6562 -6.5008,-2.0043 -0.2465,-0.2448 -0.2465,-0.6443 0,-0.8891 0.2465,-0.2465 0.6477,-0.2465 0.8942,0 1.105,1.0999 2.9393,1.6337 5.6066,1.6337 l 0.0221,0.0051 0.0221,-0.0051 c 2.6673,0 4.5016,-0.5355 5.6066,-1.6354 0.2465,-0.2465 0.6477,-0.2448 0.8942,0 0.2465,0.2465 0.2465,0.6443 0,0.8908 z m -0.3213,-5.2615 c -1.4824,0 -2.6877,-1.2002 -2.6877,-2.6758 0,-1.4756 1.2053,-2.6775 2.6877,-2.6775 1.4824,0 2.6877,1.2019 2.6877,2.6775 0,1.4756 -1.2053,2.6758 -2.6877,2.6758 z"
                                fill="white"></path>
                        </svg>
                    </button>

                    <!-- WhatsApp Button -->
                    <button id="whatsapp-share" aria-label="whatsapp" class="btn btn-sm btn-social-outline">
                        <svg viewBox="0 0 64 64" width="32" height="32">
                            <circle cx="32" cy="32" r="31" fill="#25D366"></circle>
                            <path
                                d="m42.32286,33.93287c-0.5178,-0.2589 -3.04726,-1.49644 -3.52105,-1.66732c-0.4712,-0.17346 -0.81554,-0.2589 -1.15987,0.2589c-0.34175,0.51004 -1.33075,1.66474 -1.63108,2.00648c-0.30032,0.33658 -0.60064,0.36247 -1.11327,0.12945c-0.5178,-0.2589 -2.17994,-0.80259 -4.14759,-2.56312c-1.53269,-1.37217 -2.56312,-3.05503 -2.86603,-3.57283c-0.30033,-0.5178 -0.03366,-0.80259 0.22524,-1.06149c0.23301,-0.23301 0.5178,-0.59547 0.7767,-0.90616c0.25372,-0.31068 0.33657,-0.5178 0.51262,-0.85437c0.17088,-0.36246 0.08544,-0.64725 -0.04402,-0.90615c-0.12945,-0.2589 -1.15987,-2.79613 -1.58964,-3.80584c-0.41424,-1.00971 -0.84142,-0.88027 -1.15987,-0.88027c-0.29773,-0.02588 -0.64208,-0.02588 -0.98382,-0.02588c-0.34693,0 -0.90616,0.12945 -1.37736,0.62136c-0.4712,0.5178 -1.80194,1.76053 -1.80194,4.27186c0,2.51134 1.84596,4.945 2.10227,5.30747c0.2589,0.33657 3.63497,5.51458 8.80262,7.74113c1.23237,0.5178 2.1903,0.82848 2.94111,1.08738c1.23237,0.38836 2.35599,0.33657 3.24402,0.20712c0.99159,-0.15534 3.04985,-1.24272 3.47963,-2.45956c0.44013,-1.21683 0.44013,-2.22654 0.31068,-2.45955c-0.12945,-0.23301 -0.46601,-0.36247 -0.98382,-0.59548m-9.40068,12.84407l-0.02589,0c-3.05503,0 -6.08417,-0.82849 -8.72495,-2.38189l-0.62136,-0.37023l-6.47252,1.68286l1.73463,-6.29129l-0.41424,-0.64725c-1.70875,-2.71846 -2.6149,-5.85116 -2.6149,-9.07706c0,-9.39809 7.68934,-17.06155 17.15993,-17.06155c4.58253,0 8.88029,1.78642 12.11655,5.02268c3.23625,3.21036 5.02267,7.50812 5.02267,12.06476c-0.0078,9.3981 -7.69712,17.06155 -17.14699,17.06155m14.58906,-31.58846c-3.93529,-3.80584 -9.1133,-5.95471 -14.62789,-5.95471c-11.36055,0 -20.60848,9.2065 -20.61625,20.52564c0,3.61684 0.94757,7.14565 2.75211,10.26282l-2.92557,10.63564l10.93337,-2.85309c3.0136,1.63108 6.4052,2.4958 9.85634,2.49839l0.01037,0c11.36574,0 20.61884,-9.2091 20.62403,-20.53082c0,-5.48093 -2.14111,-10.64081 -6.03239,-14.51915"
                                fill="white"></path>
                        </svg>
                    </button>

                    <!-- Telegram Button -->
                    <button id="telegram-share" aria-label="telegram" class="btn btn-sm btn-social-outline">
                        <svg viewBox="0 0 64 64" width="32" height="32">
                            <circle cx="32" cy="32" r="31" fill="#37aee2"></circle>
                            <path
                                d="m45.90873,15.44335c-0.6901,-0.0281 -1.37668,0.14048 -1.96142,0.41265c-0.84989,0.32661 -8.63939,3.33986 -16.5237,6.39174c-3.9685,1.53296 -7.93349,3.06593 -10.98537,4.24067c-3.05012,1.1765 -5.34694,2.05098 -5.4681,2.09312c-0.80775,0.28096 -1.89996,0.63566 -2.82712,1.72788c-0.23354,0.27218 -0.46884,0.62161 -0.58825,1.10275c-0.11941,0.48114 -0.06673,1.09222 0.16682,1.5716c0.46533,0.96052 1.25376,1.35737 2.18443,1.71383c3.09051,0.99037 6.28638,1.93508 8.93263,2.8236c0.97632,3.44171 1.91401,6.89571 2.84116,10.34268c0.30554,0.69185 0.97105,0.94823 1.65764,0.95525l-0.00351,0.03512c0,0 0.53908,0.05268 1.06412,-0.07375c0.52679,-0.12292 1.18879,-0.42846 1.79109,-0.99212c0.662,-0.62161 2.45836,-2.38812 3.47683,-3.38552l7.6736,5.66477l0.06146,0.03512c0,0 0.84989,0.59703 2.09312,0.68132c0.62161,0.04214 1.4399,-0.07726 2.14229,-0.59176c0.70766,-0.51626 1.1765,-1.34683 1.396,-2.29506c0.65673,-2.86224 5.00979,-23.57745 5.75257,-27.00686l-0.02107,0.08077c0.51977,-1.93157 0.32837,-3.70159 -0.87096,-4.74991c-0.60054,-0.52152 -1.2924,-0.7498 -1.98425,-0.77965l0,0.00176zm-0.2072,3.29069c0.04741,0.0439 0.0439,0.0439 0.00351,0.04741c-0.01229,-0.00351 0.14048,0.2072 -0.15804,1.32576l-0.01229,0.04214l-0.00878,0.03863c-0.75858,3.50668 -5.15554,24.40802 -5.74203,26.96472c-0.08077,0.34417 -0.11414,0.31959 -0.09482,0.29852c-0.1756,-0.02634 -0.50045,-0.16506 -0.52679,-0.1756l-13.13468,-9.70175c4.4988,-4.33199 9.09945,-8.25307 13.744,-12.43229c0.8218,-0.41265 0.68483,-1.68573 -0.29852,-1.70681c-1.04305,0.24584 -1.92279,0.99564 -2.8798,1.47502c-5.49971,3.2626 -11.11882,6.13186 -16.55882,9.49279c-2.792,-0.97105 -5.57873,-1.77704 -8.15298,-2.57601c2.2336,-0.89555 4.00889,-1.55579 5.75608,-2.23009c3.05188,-1.1765 7.01687,-2.7042 10.98537,-4.24067c7.94051,-3.06944 15.92667,-6.16346 16.62028,-6.43037l0.05619,-0.02283l0.05268,-0.02283c0.19316,-0.0878 0.30378,-0.09658 0.35471,-0.10009c0,0 -0.01756,-0.05795 -0.00351,-0.04566l-0.00176,0zm-20.91715,22.0638l2.16687,1.60145c-0.93418,0.91311 -1.81743,1.77353 -2.45485,2.38812l0.28798,-3.98957"
                                fill="white"></path>
                        </svg>
                    </button>

                    <!-- Email Button -->
                    <button id="email-share" aria-label="email" class="btn btn-sm btn-social-outline">
                        <svg viewBox="0 0 64 64" width="32" height="32">
                            <circle cx="32" cy="32" r="31" fill="#7f7f7f"></circle>
                            <path
                                d="M17,22v20h30V22H17z M41.1,25L32,32.1L22.9,25H41.1z M20,39V26.6l12,9.3l12-9.3V39H20z"
                                fill="white"></path>
                        </svg>
                    </button>

                </div>
                <div class="mt-4">
                    <!-- Copy URL Section -->
                    <div class="copy-url mb-4">
                        <button class="btn btn-warning btn-sm" id="copyUrlBtn">Copy Page URL</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="py-3">
        <div class="container text-center">
            <p class="mb-0 text-body-secondary">© 2024 SleepCalculator.app</p>


            <ul class="list-unstyled list-inline small mt-2 mb-0">

                <li class="list-inline-item me-0">
                    <a href="https://sleepcalculator.app/privacy-policy/" class="nav-link px-2 text-body-secondary">
                        Privacy policy
                    </a>
                </li>
                <li class="list-inline-item me-0">
                    <a href="https://sleepcalculator.app/terms-of-service/" class="nav-link px-2 text-body-secondary">
                        Terms Of Service
                    </a>
                </li>
                <li class="list-inline-item me-0">
                    <a href="https://sleepcalculator.app/contact-us/" class="nav-link px-2 text-body-secondary">
                        Contact us
                    </a>
                </li>
            </ul>
        </div>
    </footer>
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.2.0-beta1/dist/js/bootstrap.bundle.min.js'></script>
    <script src="https://sleepcalculator.app/js/script.js"></script>
    <script>
        var userSleepCycleLength = 90;
        var userFallAsleepTime = 15;

        document.getElementById('saveSettings').addEventListener('click', function () {
            userSleepCycleLength = parseInt(document.getElementById('sleepCycleLength').value);
            userFallAsleepTime = parseInt(document.getElementById('fallAsleepTime').value);
            var settingsModal = bootstrap.Modal.getInstance(document.getElementById('settingsModal'));
            settingsModal.hide();
        });

        function scrollToElement(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        document.getElementById('calculate').addEventListener('click', function () {
            let wakeTime = document.getElementById('wakeTime').value;
            let resultDiv = document.getElementById('bedtimeResult');

            if (wakeTime) {
                let bedtimes = calculateBedtimes(wakeTime);
                displayBedtimes(bedtimes, resultDiv, wakeTime);
                scrollToElement('bedtime');
            } else {
                resultDiv.innerHTML =
                    '<p class="lead">sleep-calculator.please_enter_a_wake_up_time</p>';
            }
        });

        document.getElementById('bedNow').addEventListener('click', function () {
            handleBedTimeSelection(new Date());
            scrollToElement('wakeup');
        });

        document.getElementById('bedCustomTime').addEventListener('click', function () {
            let customTimeValue = document.getElementById('customBedTime').value;
            if (customTimeValue) {
                let customTime = new Date();
                let [hours, minutes] = customTimeValue.split(':');
                customTime.setHours(hours, minutes);
                handleBedTimeSelection(customTime);
                scrollToElement('wakeup');
            } else {
                let resultDiv = document.getElementById('wakeupResult');
                resultDiv.innerHTML = '<p>sleep-calculator.please_enter_a_custom_time</p>';
            }
        });

        document.getElementById('bed30MinLater').addEventListener('click', function () {
            let time = new Date();
            time.setMinutes(time.getMinutes() + 30);
            handleBedTimeSelection(time);
            scrollToElement('wakeup');
        });

        document.getElementById('bed1HourLater').addEventListener('click', function () {
            let time = new Date();
            time.setMinutes(time.getMinutes() + 60);
            handleBedTimeSelection(time);
            scrollToElement('wakeup');
        });


        function handleBedTimeSelection(selectedBedTime) {
            let currentTime = new Date();
            let delayInMinutes = (selectedBedTime - currentTime) / 60000;

            selectedBedTime.setMinutes(selectedBedTime.getMinutes() + userFallAsleepTime);
            let wakeTimes = calculateWakeTimes(selectedBedTime);
            let resultDiv = document.getElementById('wakeupResult');
            displayWakeupTimes(wakeTimes, resultDiv, delayInMinutes);
        }

        function calculateBedtimes(wakeTime) {
            let bedtimes = [];
            let wakeDate = new Date(`01/01/2000 ${wakeTime}`);
            for (let i = 1; i <= 6; i++) {
                let bedtime = new Date(wakeDate.getTime() - (i * 90 + 15) * 60000);
                if (bedtime.getDate() !== wakeDate.getDate()) {
                    bedtime.setDate(wakeDate.getDate());
                }
                bedtimes.push(bedtime.toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: 'numeric',
                    hour12: true
                }));
            }

            return bedtimes.reverse();
        }

        function displayBedtimes(bedtimes, resultDiv, wakeTime) {
            resultDiv.innerHTML =
                `<h3 class="mt-4 mb-4">Optimal Bedtime</h3>
                           <p class="lead">The average person takes 15 minutes to fall asleep. We included that time when calculating your result.</p>
                           <p class="lead">If you want to wake up refreshed at ${formatTime(wakeTime)}, you should try to go to sleep at one of the following times:</p>`;

            bedtimes.forEach((time, index) => {
                let sleepInfo = calculateSleepInfo(time, wakeTime);
                resultDiv.innerHTML += `
            <div class="card mt-2">
                <div class="card-body">
                    <h5 class="card-title">${time} <span class="text-warning">${index === 0 || index === 1 ? 'Suggested' : ''}</span></h5>
                    <p class="card-text">${sleepInfo.hoursOfSleep}</p>
                    <p class="card-text">${sleepInfo.sleepCycles} sleep cycles</p>
                </div>
            </div>
        `;
            });
            resultDiv.innerHTML +=
                `<p class="mt-4 lead">For a good sleep, you need 5-6 of these cycles each night.</p>`;
        }

        function calculateSleepInfo(bedtime, wakeTime, delayInMinutes = 0) {
            let bedtimeDate = new Date(`01/01/2000 ${bedtime}`);
            let wakeDate = new Date(`01/01/2000 ${wakeTime}`);
            if (bedtimeDate > wakeDate) {
                wakeDate.setDate(wakeDate.getDate() + 1);
            }

            let diff = wakeDate - bedtimeDate - (delayInMinutes * 60000);
            let hoursOfSleep = Math.abs(diff / 36e5);
            let sleepCycles = Math.floor(hoursOfSleep / 1.5);

            let totalMinutes = hoursOfSleep * 60;
            let hours = Math.floor(totalMinutes / 60);
            let minutes = Math.round(totalMinutes % 60);
            return {
                hoursOfSleep: `${hours} hours ${minutes} minutes of sleep`,
                sleepCycles
            };
        }

        function calculateWakeTimes(currentTime) {
            let wakeTimes = [];
            for (let i = 1; i <= 6; i++) {
                let wakeTime = new Date(currentTime.getTime() + (i * userSleepCycleLength) * 60000);
                wakeTimes.push(wakeTime.toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: 'numeric',
                    hour12: true
                }));
            }
            return wakeTimes.reverse();
        }


        function displayWakeupTimes(wakeTimes, resultDiv, delayInMinutes) {
            let bedtime = new Date();

            resultDiv.innerHTML =
                `<h3 class="mt-4 mb-4">Optimal Wake-up Times</h3>
                           <p class="lead mb-4">If you go to bed now, consider waking up at one of the following times for optimal rest:</p>`;

            wakeTimes.forEach((time, index) => {
                let sleepInfo = calculateSleepInfo(bedtime.toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: 'numeric',
                    hour12: true
                }), time, delayInMinutes);

                let suggested = index === 0 || index === 1 ? 'Suggested' :
                    '';

                resultDiv.innerHTML += `
            <div class="card mt-2">
                <div class="card-body">
                    <h5 class="card-title">${time} <span class="text-warning">${suggested}</span></h5>
                    <p class="card-text">${sleepInfo.hoursOfSleep}</p>
                    <p class="card-text">${sleepInfo.sleepCycles} sleep cycles</p>
                </div>
            </div>
        `;
            });

            resultDiv.innerHTML +=
                `<p class="lead mt-4">For a good sleep, you need 5-6 of these cycles each night.</p>`;
        }

        function formatTime(time) {
            let [hours, minutes] = time.split(':');
            hours = parseInt(hours, 10);
            let suffix = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12;
            hours = hours || 12;
            return `${hours}:${minutes} ${suffix}`;
        }
    </script>
</body>

</html>