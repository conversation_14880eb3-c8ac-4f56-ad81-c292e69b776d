parcelRequire=function(e,r,t,n){var i,o="function"==typeof parcelRequire&&parcelRequire,u="function"==typeof require&&require;function f(t,n){if(!r[t]){if(!e[t]){var i="function"==typeof parcelRequire&&parcelRequire;if(!n&&i)return i(t,!0);if(o)return o(t,!0);if(u&&"string"==typeof t)return u(t);var c=new Error("Cannot find module '"+t+"'");throw c.code="MODULE_NOT_FOUND",c}p.resolve=function(r){return e[t][1][r]||r},p.cache={};var l=r[t]=new f.Module(t);e[t][0].call(l.exports,p,l,l.exports,this)}return r[t].exports;function p(e){return f(p.resolve(e))}}f.isParcelRequire=!0,f.Module=function(e){this.id=e,this.bundle=f,this.exports={}},f.modules=e,f.cache=r,f.parent=o,f.register=function(r,t){e[r]=[function(e,r){r.exports=t},{}]};for(var c=0;c<t.length;c++)try{f(t[c])}catch(e){i||(i=e)}if(t.length){var l=f(t[t.length-1]);"object"==typeof exports&&"undefined"!=typeof module?module.exports=l:"function"==typeof define&&define.amd?define(function(){return l}):n&&(this[n]=l)}if(parcelRequire=f,i)throw i;return f}({"SyYu":[function(require,module,exports) {
var define;
var A;!function(e,t){"use strict";"function"==typeof A&&A.amd?A([],t):"object"==typeof module&&module.exports?module.exports=t():(e.AnchorJS=t(),e.anchors=new e.AnchorJS)}(this,function(){"use strict";return function(A){function e(A){A.icon=A.hasOwnProperty("icon")?A.icon:"",A.visible=A.hasOwnProperty("visible")?A.visible:"hover",A.placement=A.hasOwnProperty("placement")?A.placement:"right",A.ariaLabel=A.hasOwnProperty("ariaLabel")?A.ariaLabel:"Anchor",A.class=A.hasOwnProperty("class")?A.class:"",A.base=A.hasOwnProperty("base")?A.base:"",A.truncate=A.hasOwnProperty("truncate")?Math.floor(A.truncate):64,A.titleText=A.hasOwnProperty("titleText")?A.titleText:""}function t(A){var e;if("string"==typeof A||A instanceof String)e=[].slice.call(document.querySelectorAll(A));else{if(!(Array.isArray(A)||A instanceof NodeList))throw new Error("The selector provided to AnchorJS was invalid.");e=[].slice.call(A)}return e}this.options=A||{},this.elements=[],e(this.options),this.isTouchDevice=function(){return!!("ontouchstart"in window||window.DocumentTouch&&document instanceof DocumentTouch)},this.add=function(A){var i,n,o,s,a,r,c,h,l,u,d,p,w=[];if(e(this.options),"touch"===(d=this.options.visible)&&(d=this.isTouchDevice()?"always":"hover"),A||(A="h2, h3, h4, h5, h6"),0===(i=t(A)).length)return this;for(function(){if(null===document.head.querySelector("style.anchorjs")){var A,e=document.createElement("style");e.className="anchorjs",e.appendChild(document.createTextNode("")),void 0===(A=document.head.querySelector('[rel="stylesheet"], style'))?document.head.appendChild(e):document.head.insertBefore(e,A),e.sheet.insertRule(" .anchorjs-link {   opacity: 0;   text-decoration: none;   -webkit-font-smoothing: antialiased;   -moz-osx-font-smoothing: grayscale; }",e.sheet.cssRules.length),e.sheet.insertRule(" *:hover > .anchorjs-link, .anchorjs-link:focus  {   opacity: 1; }",e.sheet.cssRules.length),e.sheet.insertRule(" [data-anchorjs-icon]::after {   content: attr(data-anchorjs-icon); }",e.sheet.cssRules.length),e.sheet.insertRule(' @font-face {   font-family: "anchorjs-icons";   src: url(data:n/a;base64,AAEAAAALAIAAAwAwT1MvMg8yG2cAAAE4AAAAYGNtYXDp3gC3AAABpAAAAExnYXNwAAAAEAAAA9wAAAAIZ2x5ZlQCcfwAAAH4AAABCGhlYWQHFvHyAAAAvAAAADZoaGVhBnACFwAAAPQAAAAkaG10eASAADEAAAGYAAAADGxvY2EACACEAAAB8AAAAAhtYXhwAAYAVwAAARgAAAAgbmFtZQGOH9cAAAMAAAAAunBvc3QAAwAAAAADvAAAACAAAQAAAAEAAHzE2p9fDzz1AAkEAAAAAADRecUWAAAAANQA6R8AAAAAAoACwAAAAAgAAgAAAAAAAAABAAADwP/AAAACgAAA/9MCrQABAAAAAAAAAAAAAAAAAAAAAwABAAAAAwBVAAIAAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAMCQAGQAAUAAAKZAswAAACPApkCzAAAAesAMwEJAAAAAAAAAAAAAAAAAAAAARAAAAAAAAAAAAAAAAAAAAAAQAAg//0DwP/AAEADwABAAAAAAQAAAAAAAAAAAAAAIAAAAAAAAAIAAAACgAAxAAAAAwAAAAMAAAAcAAEAAwAAABwAAwABAAAAHAAEADAAAAAIAAgAAgAAACDpy//9//8AAAAg6cv//f///+EWNwADAAEAAAAAAAAAAAAAAAAACACEAAEAAAAAAAAAAAAAAAAxAAACAAQARAKAAsAAKwBUAAABIiYnJjQ3NzY2MzIWFxYUBwcGIicmNDc3NjQnJiYjIgYHBwYUFxYUBwYGIwciJicmNDc3NjIXFhQHBwYUFxYWMzI2Nzc2NCcmNDc2MhcWFAcHBgYjARQGDAUtLXoWOR8fORYtLTgKGwoKCjgaGg0gEhIgDXoaGgkJBQwHdR85Fi0tOAobCgoKOBoaDSASEiANehoaCQkKGwotLXoWOR8BMwUFLYEuehYXFxYugC44CQkKGwo4GkoaDQ0NDXoaShoKGwoFBe8XFi6ALjgJCQobCjgaShoNDQ0NehpKGgobCgoKLYEuehYXAAAADACWAAEAAAAAAAEACAAAAAEAAAAAAAIAAwAIAAEAAAAAAAMACAAAAAEAAAAAAAQACAAAAAEAAAAAAAUAAQALAAEAAAAAAAYACAAAAAMAAQQJAAEAEAAMAAMAAQQJAAIABgAcAAMAAQQJAAMAEAAMAAMAAQQJAAQAEAAMAAMAAQQJAAUAAgAiAAMAAQQJAAYAEAAMYW5jaG9yanM0MDBAAGEAbgBjAGgAbwByAGoAcwA0ADAAMABAAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAH//wAP) format("truetype"); }',e.sheet.cssRules.length)}}(),n=document.querySelectorAll("[id]"),o=[].map.call(n,function(A){return A.id}),a=0;a<i.length;a++)if(this.hasAnchorJSLink(i[a]))w.push(a);else{if(i[a].hasAttribute("id"))s=i[a].getAttribute("id");else if(i[a].hasAttribute("data-anchor-id"))s=i[a].getAttribute("data-anchor-id");else{l=h=this.urlify(i[a].textContent),c=0;do{void 0!==r&&(l=h+"-"+c),r=o.indexOf(l),c+=1}while(-1!==r);r=void 0,o.push(l),i[a].setAttribute("id",l),s=l}(u=document.createElement("a")).className="anchorjs-link "+this.options.class,u.setAttribute("aria-label",this.options.ariaLabel),u.setAttribute("data-anchorjs-icon",this.options.icon),this.options.titleText&&(u.title=this.options.titleText),p=document.querySelector("base")?window.location.pathname+window.location.search:"",p=this.options.base||p,u.href=p+"#"+s,"always"===d&&(u.style.opacity="1"),""===this.options.icon&&(u.style.font="1em/1 anchorjs-icons","left"===this.options.placement&&(u.style.lineHeight="inherit")),"left"===this.options.placement?(u.style.position="absolute",u.style.marginLeft="-1em",u.style.paddingRight="0.5em",i[a].insertBefore(u,i[a].firstChild)):(u.style.paddingLeft="0.375em",i[a].appendChild(u))}for(a=0;a<w.length;a++)i.splice(w[a]-a,1);return this.elements=this.elements.concat(i),this},this.remove=function(A){for(var e,i,n=t(A),o=0;o<n.length;o++)(i=n[o].querySelector(".anchorjs-link"))&&(-1!==(e=this.elements.indexOf(n[o]))&&this.elements.splice(e,1),n[o].removeChild(i));return this},this.removeAll=function(){this.remove(this.elements)},this.urlify=function(A){return this.options.truncate||e(this.options),A.trim().replace(/\'/gi,"").replace(/[& +$,:;=?@"#{}|^~[`%!'<>\]\.\/\(\)\*\\\n\t\b\v]/g,"-").replace(/-{2,}/g,"-").substring(0,this.options.truncate).replace(/^-+|-+$/gm,"").toLowerCase()},this.hasAnchorJSLink=function(A){var e=A.firstChild&&(" "+A.firstChild.className+" ").indexOf(" anchorjs-link ")>-1,t=A.lastChild&&(" "+A.lastChild.className+" ").indexOf(" anchorjs-link ")>-1;return e||t||!1}}});
},{}],"SFxP":[function(require,module,exports) {
var e=require("anchor-js"),t=new e;t.options.placement="left",t.add("h3");var n=document.getElementById("toc").getElementsByTagName("a");document.getElementById("filter-input").addEventListener("keyup",function(e){var t,s,i;if(13===e.keyCode)for(t=0;t<n.length;t++)if(!(s=n[t]).classList.contains("display-none"))return location.replace(s.firstChild.href),e.preventDefault();var a=function(){return!0},o=this.value.toLowerCase();for(o.match(/^\s*$/)||(a=function(e){var t=e.firstChild.innerHTML||e.textContent;return t&&-1!==t.toLowerCase().indexOf(o)}),t=0;t<n.length;t++)s=n[t],i=Array.from(s.getElementsByTagName("a")),a(s)||i.some(a)?s.classList.remove("display-none"):s.classList.add("display-none")});for(var s=document.getElementsByClassName("toggle-sibling"),i=0;i<s.length;i++)s[i].addEventListener("click",a);function a(){var e=this.nextElementSibling,t=this.getElementsByClassName("icon")[0],n="display-none";e.classList.contains(n)?(e.classList.remove(n),t.innerHTML="▾"):(e.classList.add(n),t.innerHTML="▸")}function o(e){if(e){var t=document.getElementById(e);t&&0===t.offsetHeight&&t.parentNode.parentNode.classList.contains("display-none")&&t.parentNode.parentNode.classList.remove("display-none")}}function r(e){if(e&&!history.state){var t=document.getElementById(e);t&&t.scrollIntoView()}}function l(){o(location.hash.substring(1)),r(location.hash.substring(1))}window.addEventListener("hashchange",l),l();for(var d=document.getElementsByClassName("pre-open"),c=0;c<d.length;c++)d[c].addEventListener("mousedown",u,!1);function u(){o(this.hash.substring(1))}function m(){history.replaceState({},document.title)}function f(e){e&&history.replaceState(e.state,document.title)}window.addEventListener("load",function(){setTimeout(function(){f(),m()},1)}),window.addEventListener("popstate",f);
},{"anchor-js":"SyYu"}],"imtx":[function(require,module,exports) {
require("./site");
},{"./site":"SFxP"}]},{},["imtx"], null)