{"name": "call-me-maybe", "version": "1.0.1", "description": "Let your JS API users either give you a callback or receive a promise", "main": "index.js", "dependencies": {}, "devDependencies": {"mocha": "^2.3.2", "promise": "^7.0.4", "zuul": "^3.4.0"}, "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/limulus/call-me-maybe.git"}, "keywords": ["promise", "callback", "denodeify", "promisify", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "author": "<PERSON> <<EMAIL>> (http://www.limulus.net/)", "license": "MIT", "bugs": {"url": "https://github.com/limulus/call-me-maybe/issues"}, "homepage": "https://github.com/limulus/call-me-maybe#readme"}