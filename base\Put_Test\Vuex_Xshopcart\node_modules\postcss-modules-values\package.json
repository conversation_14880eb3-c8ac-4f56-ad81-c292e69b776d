{"name": "postcss-modules-values", "version": "3.0.0", "description": "PostCSS plugin for CSS Modules to pass arbitrary values between your module files", "main": "src/index.js", "files": ["src"], "scripts": {"lint": "eslint src test", "pretest": "yarn lint", "test": "mocha", "autotest": "chokidar src test -c 'npm test'", "cover": "nyc mocha", "travis": "yarn lint && yarn cover", "prepublishOnly": "yarn test"}, "repository": {"type": "git", "url": "git+https://github.com/css-modules/postcss-modules-values.git"}, "keywords": ["css", "modules", "postcss"], "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/css-modules/postcss-modules-values/issues"}, "homepage": "https://github.com/css-modules/postcss-modules-values#readme", "devDependencies": {"chokidar-cli": "^1.0.1", "codecov.io": "^0.1.2", "coveralls": "^3.0.2", "eslint": "^5.9.0", "mocha": "^6.1.4", "nyc": "^14.1.0"}, "dependencies": {"icss-utils": "^4.0.0", "postcss": "^7.0.6"}}