{"name": "@types/uglify-js", "version": "3.9.3", "description": "TypeScript definitions for UglifyJS", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/alan-agius4", "githubUsername": "alan-agius4"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/uglify-js"}, "scripts": {}, "dependencies": {"source-map": "^0.6.1"}, "typesPublisherContentHash": "671a8814ea621e187cc783206085b238166d93c466434ea4bb28ce2915eeed76", "typeScriptVersion": "3.0"}