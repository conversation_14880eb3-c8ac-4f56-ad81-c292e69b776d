{"name": "@types/webpack", "version": "4.41.22", "description": "TypeScript definitions for webpack", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/tkqubo", "githubUsername": "tkqubo"}, {"name": "<PERSON>", "url": "https://github.com/bumbleblym", "githubUsername": "bumbleblym"}, {"name": "<PERSON>", "url": "https://github.com/bcherny", "githubUsername": "b<PERSON>ny"}, {"name": "<PERSON>", "url": "https://github.com/tommy<PERSON>ylin", "githubUsername": "tommytroylin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/mohsen1", "githubUsername": "mohsen1"}, {"name": "<PERSON>", "url": "https://github.com/jcreamer898", "githubUsername": "jcreamer898"}, {"name": "<PERSON>", "url": "https://github.com/alan-agius4", "githubUsername": "alan-agius4"}, {"name": "<PERSON>", "url": "https://github.com/dennispg", "githubUsername": "dennispg"}, {"name": "<PERSON>", "url": "https://github.com/christo<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "ZSkycat", "url": "https://github.com/ZSkycat", "githubUsername": "ZSkycat"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/rwaskie<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/kuehlein", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/grgur", "githubUsername": "grgur"}, {"name": "<PERSON><PERSON><PERSON> Gonçalves Cavalcante", "url": "https://github.com/rubenspgcavalcante", "githubUsername": "rubenspgcavalcante"}, {"name": "<PERSON>", "url": "https://github.com/andersk", "githubUsername": "andersk"}, {"name": "<PERSON>", "url": "https://github.com/ofhouse", "githubUsername": "ofhouse"}, {"name": "<PERSON>", "url": "https://github.com/danielthank", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/sasurau4", "githubUsername": "sasurau4"}, {"name": "<PERSON>", "url": "https://github.com/dionshihk", "githubUsername": "dionshihk"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/spamshaker", "githubUsername": "spamshaker"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/webpack"}, "scripts": {}, "dependencies": {"@types/anymatch": "*", "@types/node": "*", "@types/tapable": "*", "@types/uglify-js": "*", "@types/webpack-sources": "*", "source-map": "^0.6.0"}, "typesPublisherContentHash": "898c156d9e67e55114c82c26876dd495ec549513cd18f98ee91906c1a95b46e4", "typeScriptVersion": "3.1"}