{"name": "nice-try", "version": "1.0.5", "authors": ["<PERSON> <<EMAIL>>"], "description": "Tries to execute a function and discards any error that occurs", "main": "src/index.js", "keywords": ["try", "catch", "error"], "license": "MIT", "homepage": "https://github.com/electerious/nice-try", "repository": {"type": "git", "url": "https://github.com/electerious/nice-try.git"}, "files": ["src"], "scripts": {"coveralls": "nyc report --reporter=text-lcov | coveralls", "test": "nyc node_modules/mocha/bin/_mocha"}, "devDependencies": {"chai": "^4.1.2", "coveralls": "^3.0.0", "nyc": "^12.0.1", "mocha": "^5.1.1"}}