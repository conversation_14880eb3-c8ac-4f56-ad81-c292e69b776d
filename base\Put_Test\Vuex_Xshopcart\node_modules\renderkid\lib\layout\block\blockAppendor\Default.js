// Generated by CoffeeScript 1.9.3
var DefaultBlockAppendor, tools,
  extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
  hasProp = {}.hasOwnProperty;

tools = require('../../../tools');

module.exports = DefaultBlockAppendor = (function(superClass) {
  extend(DefaultBlockAppendor, superClass);

  function DefaultBlockAppendor() {
    return DefaultBlockAppendor.__super__.constructor.apply(this, arguments);
  }

  DefaultBlockAppendor.prototype._render = function(options) {
    return tools.repeatString("\n", this._config.amount);
  };

  return DefaultBlockAppendor;

})(require('./_BlockAppendor'));
