# 0.4.0

- Changed: Invalid conversions now return `null` instead of `undefined`
- Changed: Moved to XO standard
- Fixed: a few details in package.json
- Fixed: readme output regarding wrapped hue values ([#21](https://github.com/MoOx/color-string/pull/21))

# 0.3.0

- Fixed: HSL alpha channel ([#16](https://github.com/harthur/color-string/pull/16))
- Fixed: ability to parse signed number ([#15](https://github.com/harthur/color-string/pull/15))
- Removed: component.json
- Removed: browser build
- Added: license field to package.json ([#17](https://github.com/harthur/color-string/pull/17))

---

Check out commit logs for earlier releases
