{"name": "@babel/traverse", "version": "7.11.5", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-traverse"}, "main": "lib/index.js", "dependencies": {"@babel/code-frame": "^7.10.4", "@babel/generator": "^7.11.5", "@babel/helper-function-name": "^7.10.4", "@babel/helper-split-export-declaration": "^7.11.0", "@babel/parser": "^7.11.5", "@babel/types": "^7.11.5", "debug": "^4.1.0", "globals": "^11.1.0", "lodash": "^4.17.19"}, "devDependencies": {"@babel/helper-plugin-test-runner": "^7.10.4"}, "gitHead": "af64ccb2b00bc7574943674996c2f0507cdbfb6f"}