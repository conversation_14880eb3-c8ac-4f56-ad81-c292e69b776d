# [postcss][postcss]-normalize-whitespace

> Normalize whitespace with PostCSS.

## Install

With [npm](https://npmjs.org/package/postcss-normalize-whitespace) do:

```
npm install postcss-normalize-whitespace --save
```

## Example

### Input

```css
h1{
    width: calc(10px - ( 100px / var(--test) )) 
}
```

### Output

```css
h1{
    width: calc(10px - 100px / var(--test))
}
``` 

## Usage

See the [PostCSS documentation](https://github.com/postcss/postcss#usage) for
examples for your environment.

## Contributors

See [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).

## License

MIT © [<PERSON>](http://beneb.info)

[postcss]: https://github.com/postcss/postcss
