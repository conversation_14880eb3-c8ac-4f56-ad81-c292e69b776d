{"name": "array-union", "version": "1.0.2", "description": "Create an array of unique values, in order, from the input arrays", "license": "MIT", "repository": "sindresorhus/array-union", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["array", "arr", "set", "uniq", "unique", "duplicate", "remove", "union", "combine", "merge"], "dependencies": {"array-uniq": "^1.0.1"}, "devDependencies": {"ava": "*", "xo": "*"}}