// Generated by CoffeeScript 1.9.3
var AnsiPainter, Layout, RenderKid, Styles, blockStyleApplier, inlineStyleApplier, object, stripAnsi, terminalWidth, tools;

inlineStyleApplier = require('./renderKid/styleApplier/inline');

blockStyleApplier = require('./renderKid/styleApplier/block');

AnsiPainter = require('./AnsiPainter');

Styles = require('./renderKid/Styles');

Layout = require('./Layout');

tools = require('./tools');

object = require('utila').object;

stripAnsi = require('strip-ansi');

terminalWidth = require('./tools').getCols();

module.exports = RenderKid = (function() {
  var self;

  self = RenderKid;

  RenderKid.AnsiPainter = AnsiPainter;

  RenderKid.Layout = Layout;

  RenderKid.quote = tools.quote;

  RenderKid.tools = tools;

  RenderKid._defaultConfig = {
    layout: {
      terminalWidth: terminalWidth
    }
  };

  function RenderKid(config) {
    if (config == null) {
      config = {};
    }
    this.tools = self.tools;
    this._config = object.append(self._defaultConfig, config);
    this._initStyles();
  }

  RenderKid.prototype._initStyles = function() {
    return this._styles = new Styles;
  };

  RenderKid.prototype.style = function() {
    return this._styles.setRule.apply(this._styles, arguments);
  };

  RenderKid.prototype._getStyleFor = function(el) {
    return this._styles.getStyleFor(el);
  };

  RenderKid.prototype.render = function(input, withColors) {
    if (withColors == null) {
      withColors = true;
    }
    return this._paint(this._renderDom(this._toDom(input)), withColors);
  };

  RenderKid.prototype._toDom = function(input) {
    if (typeof input === 'string') {
      return this._parse(input);
    } else if (object.isBareObject(input) || Array.isArray(input)) {
      return this._objToDom(input);
    } else {
      throw Error("Invalid input type. Only strings, arrays and objects are accepted");
    }
  };

  RenderKid.prototype._objToDom = function(o, injectFakeRoot) {
    if (injectFakeRoot == null) {
      injectFakeRoot = true;
    }
    if (injectFakeRoot) {
      o = {
        body: o
      };
    }
    return tools.objectToDom(o);
  };

  RenderKid.prototype._paint = function(text, withColors) {
    var painted;
    painted = AnsiPainter.paint(text);
    if (withColors) {
      return painted;
    } else {
      return stripAnsi(painted);
    }
  };

  RenderKid.prototype._parse = function(string, injectFakeRoot) {
    if (injectFakeRoot == null) {
      injectFakeRoot = true;
    }
    if (injectFakeRoot) {
      string = '<body>' + string + '</body>';
    }
    return tools.stringToDom(string);
  };

  RenderKid.prototype._renderDom = function(dom) {
    var bodyTag, layout, rootBlock;
    bodyTag = dom[0];
    layout = new Layout(this._config.layout);
    rootBlock = layout.getRootBlock();
    this._renderBlockNode(bodyTag, null, rootBlock);
    return layout.get();
  };

  RenderKid.prototype._renderChildrenOf = function(parentNode, parentBlock) {
    var i, len, node, nodes;
    nodes = parentNode.children;
    for (i = 0, len = nodes.length; i < len; i++) {
      node = nodes[i];
      this._renderNode(node, parentNode, parentBlock);
    }
  };

  RenderKid.prototype._renderNode = function(node, parentNode, parentBlock) {
    if (node.type === 'text') {
      this._renderText(node, parentNode, parentBlock);
    } else if (node.name === 'br') {
      this._renderBr(node, parentNode, parentBlock);
    } else if (this._isBlock(node)) {
      this._renderBlockNode(node, parentNode, parentBlock);
    } else if (this._isNone(node)) {
      return;
    } else {
      this._renderInlineNode(node, parentNode, parentBlock);
    }
  };

  RenderKid.prototype._renderText = function(node, parentNode, parentBlock) {
    var ref, text;
    text = node.data;
    text = text.replace(/\s+/g, ' ');
    if ((parentNode != null ? (ref = parentNode.styles) != null ? ref.display : void 0 : void 0) !== 'inline') {
      text = text.trim();
    }
    if (text.length === 0) {
      return;
    }
    text = text.replace(/&nl;/g, "\n");
    return parentBlock.write(text);
  };

  RenderKid.prototype._renderBlockNode = function(node, parentNode, parentBlock) {
    var after, before, block, blockConfig, ref;
    ref = blockStyleApplier.applyTo(node, this._getStyleFor(node)), before = ref.before, after = ref.after, blockConfig = ref.blockConfig;
    block = parentBlock.openBlock(blockConfig);
    if (before !== '') {
      block.write(before);
    }
    this._renderChildrenOf(node, block);
    if (after !== '') {
      block.write(after);
    }
    return block.close();
  };

  RenderKid.prototype._renderInlineNode = function(node, parentNode, parentBlock) {
    var after, before, ref;
    ref = inlineStyleApplier.applyTo(node, this._getStyleFor(node)), before = ref.before, after = ref.after;
    if (before !== '') {
      parentBlock.write(before);
    }
    this._renderChildrenOf(node, parentBlock);
    if (after !== '') {
      return parentBlock.write(after);
    }
  };

  RenderKid.prototype._renderBr = function(node, parentNode, parentBlock) {
    return parentBlock.write("\n");
  };

  RenderKid.prototype._isBlock = function(node) {
    return !(node.type === 'text' || node.name === 'br' || this._getStyleFor(node).display !== 'block');
  };

  RenderKid.prototype._isNone = function(node) {
    return !(node.type === 'text' || node.name === 'br' || this._getStyleFor(node).display !== 'none');
  };

  return RenderKid;

})();
