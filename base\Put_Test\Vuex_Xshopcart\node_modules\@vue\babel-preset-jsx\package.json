{"name": "@vue/babel-preset-jsx", "version": "1.1.2", "description": "Babel preset for Vue JSX", "main": "dist/plugin.cjs.js", "repository": "https://github.com/vuejs/jsx/tree/master/packages/babel-preset-jsx", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "private": false, "files": [], "scripts": {"build": "rollup -c", "prerelease": "yarn build"}, "dependencies": {"@vue/babel-helper-vue-jsx-merge-props": "^1.0.0", "@vue/babel-plugin-transform-vue-jsx": "^1.1.2", "@vue/babel-sugar-functional-vue": "^1.1.2", "@vue/babel-sugar-inject-h": "^1.1.2", "@vue/babel-sugar-v-model": "^1.1.2", "@vue/babel-sugar-v-on": "^1.1.2"}, "devDependencies": {"rollup": "^0.67.4", "rollup-plugin-babel-minify": "^6.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "gitHead": "67d6d39beab9f853118b4e0bbe901f5899ae7245"}