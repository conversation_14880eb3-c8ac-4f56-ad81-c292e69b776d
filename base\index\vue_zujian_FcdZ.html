<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>组件：父传子</title>
    <style>
     .name{
        color:red;
      }
    </style>
</head>
 
<body>
    <div id="app">
       <child :fname="name1"></child>
    </div>
    <script src="https://cdn.staticfile.org/vue/2.2.2/vue.min.js"></script>
    <script>
        Vue.component('child',{
            template: `
                <div>child子组件接收：{{fname}}</div>
            `,
            props: ['fname']
        })
        const vm = new Vue({
           el:'#app',
           data() { 
               return {
                  name1: '父组件要传递的值☞： 張三'
               }
           }
        });
    </script>
</body>

</html>