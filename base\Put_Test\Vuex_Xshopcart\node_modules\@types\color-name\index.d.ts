// Type definitions for color-name 1.1
// Project: https://github.com/colorjs/color-name
// Definitions by: <PERSON><PERSON><PERSON> <https://github.com/Ailrun>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped

/**
 * Tuple of Red, Green, and Blue
 * @example
 * // Red = 55, Green = 70, Blue = 0
 * const rgb: RGB = [55, 70, 0];
 */
export type RGB = [number, number, number];

export const aliceblue: RGB;
export const antiquewhite: RGB;
export const aqua: RGB;
export const aquamarine: RGB;
export const azure: RGB;
export const beige: RGB;
export const bisque: RGB;
export const black: RGB;
export const blanchedalmond: RGB;
export const blue: RGB;
export const blueviolet: RGB;
export const brown: RGB;
export const burlywood: RGB;
export const cadetblue: RGB;
export const chartreuse: RGB;
export const chocolate: RGB;
export const coral: RGB;
export const cornflowerblue: RGB;
export const cornsilk: RGB;
export const crimson: RGB;
export const cyan: RGB;
export const darkblue: RGB;
export const darkcyan: RGB;
export const darkgoldenrod: RGB;
export const darkgray: RGB;
export const darkgreen: RGB;
export const darkgrey: RGB;
export const darkkhaki: RGB;
export const darkmagenta: RGB;
export const darkolivegreen: RGB;
export const darkorange: RGB;
export const darkorchid: RGB;
export const darkred: RGB;
export const darksalmon: RGB;
export const darkseagreen: RGB;
export const darkslateblue: RGB;
export const darkslategray: RGB;
export const darkslategrey: RGB;
export const darkturquoise: RGB;
export const darkviolet: RGB;
export const deeppink: RGB;
export const deepskyblue: RGB;
export const dimgray: RGB;
export const dimgrey: RGB;
export const dodgerblue: RGB;
export const firebrick: RGB;
export const floralwhite: RGB;
export const forestgreen: RGB;
export const fuchsia: RGB;
export const gainsboro: RGB;
export const ghostwhite: RGB;
export const gold: RGB;
export const goldenrod: RGB;
export const gray: RGB;
export const green: RGB;
export const greenyellow: RGB;
export const grey: RGB;
export const honeydew: RGB;
export const hotpink: RGB;
export const indianred: RGB;
export const indigo: RGB;
export const ivory: RGB;
export const khaki: RGB;
export const lavender: RGB;
export const lavenderblush: RGB;
export const lawngreen: RGB;
export const lemonchiffon: RGB;
export const lightblue: RGB;
export const lightcoral: RGB;
export const lightcyan: RGB;
export const lightgoldenrodyellow: RGB;
export const lightgray: RGB;
export const lightgreen: RGB;
export const lightgrey: RGB;
export const lightpink: RGB;
export const lightsalmon: RGB;
export const lightseagreen: RGB;
export const lightskyblue: RGB;
export const lightslategray: RGB;
export const lightslategrey: RGB;
export const lightsteelblue: RGB;
export const lightyellow: RGB;
export const lime: RGB;
export const limegreen: RGB;
export const linen: RGB;
export const magenta: RGB;
export const maroon: RGB;
export const mediumaquamarine: RGB;
export const mediumblue: RGB;
export const mediumorchid: RGB;
export const mediumpurple: RGB;
export const mediumseagreen: RGB;
export const mediumslateblue: RGB;
export const mediumspringgreen: RGB;
export const mediumturquoise: RGB;
export const mediumvioletred: RGB;
export const midnightblue: RGB;
export const mintcream: RGB;
export const mistyrose: RGB;
export const moccasin: RGB;
export const navajowhite: RGB;
export const navy: RGB;
export const oldlace: RGB;
export const olive: RGB;
export const olivedrab: RGB;
export const orange: RGB;
export const orangered: RGB;
export const orchid: RGB;
export const palegoldenrod: RGB;
export const palegreen: RGB;
export const paleturquoise: RGB;
export const palevioletred: RGB;
export const papayawhip: RGB;
export const peachpuff: RGB;
export const peru: RGB;
export const pink: RGB;
export const plum: RGB;
export const powderblue: RGB;
export const purple: RGB;
export const rebeccapurple: RGB;
export const red: RGB;
export const rosybrown: RGB;
export const royalblue: RGB;
export const saddlebrown: RGB;
export const salmon: RGB;
export const sandybrown: RGB;
export const seagreen: RGB;
export const seashell: RGB;
export const sienna: RGB;
export const silver: RGB;
export const skyblue: RGB;
export const slateblue: RGB;
export const slategray: RGB;
export const slategrey: RGB;
export const snow: RGB;
export const springgreen: RGB;
export const steelblue: RGB;
export const tan: RGB;
export const teal: RGB;
export const thistle: RGB;
export const tomato: RGB;
export const turquoise: RGB;
export const violet: RGB;
export const wheat: RGB;
export const white: RGB;
export const whitesmoke: RGB;
export const yellow: RGB;
export const yellowgreen: RGB;
