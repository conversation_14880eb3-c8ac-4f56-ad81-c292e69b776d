{"name": "postcss-selector-parser", "version": "6.0.2", "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.3.4", "@babel/plugin-proposal-class-properties": "^7.3.4", "@babel/preset-env": "^7.3.4", "@babel/register": "^7.0.0", "ava": "^1.3.1", "babel-eslint": "^10.0.1", "babel-plugin-add-module-exports": "^1.0.0", "coveralls": "^3.0.3", "del-cli": "^1.1.0", "eslint": "^5.15.1", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-import": "^2.16.0", "glob": "^7.1.3", "minimist": "^1.2.0", "nyc": "^13.3.0", "postcss": "^7.0.14", "semver": "^5.6.0"}, "main": "dist/index.js", "types": "postcss-selector-parser.d.ts", "files": ["API.md", "CHANGELOG.md", "LICENSE-MIT", "dist", "postcss-selector-parser.d.ts"], "scripts": {"pretest": "eslint src", "prepare": "del-cli dist && BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/", "lintfix": "eslint --fix src", "report": "nyc report --reporter=html", "test": "nyc ava src/__tests__/*.js", "testone": "ava"}, "dependencies": {"cssesc": "^3.0.0", "indexes-of": "^1.0.1", "uniq": "^1.0.1"}, "license": "MIT", "engines": {"node": ">=4"}, "homepage": "https://github.com/postcss/postcss-selector-parser", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/chrise<PERSON><PERSON>"}], "repository": "postcss/postcss-selector-parser", "ava": {"require": ["@babel/register"], "concurrency": 5}, "nyc": {"exclude": ["node_modules", "**/__tests__"]}}