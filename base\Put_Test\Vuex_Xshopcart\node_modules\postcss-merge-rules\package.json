{"name": "postcss-merge-rules", "version": "4.0.3", "description": "Merge CSS rules with PostCSS.", "main": "dist/index.js", "files": ["LICENSE-MIT", "dist"], "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "keywords": ["css", "optimise", "postcss", "postcss-plugin"], "license": "MIT", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0", "postcss-discard-comments": "^4.0.0", "postcss-simple-vars": "^5.0.1"}, "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "dependencies": {"browserslist": "^4.0.0", "caniuse-api": "^3.0.0", "cssnano-util-same-parent": "^4.0.0", "postcss": "^7.0.0", "postcss-selector-parser": "^3.0.0", "vendors": "^1.0.0"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": ">=6.9.0"}}