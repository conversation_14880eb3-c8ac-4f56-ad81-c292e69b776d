用vercel部署Serverless Api，不购买云服务器也能拥有自己的动态网站，酷！！！！
有人可能会问了。能部署api还不行啊，没有数据库啊。身为一个混迹江湖多年的白嫖党，请跟我继续往下走~
去https://cloud.mongodb.com上白嫖一个数据库如何？
首先我们打开https://cloud.mongodb.com并注册登录
https://cloud.mongodb.com提供512M免费的MongoDB存储额度，作为个人网站使用足够了，当然还有其他的免费数据库，如db4free.net、mlab.com等各位各取所需。了解更多可以访问免费在线 MySQL/PostgreSQL/MongoDB/Redis 数据库云服务合集了解
我是觉得https://cloud.mongodb.com最好用，所以，本文中我只介绍它的使用方式。


详情：👉 https://zhuanlan.zhihu.com/p/347990778