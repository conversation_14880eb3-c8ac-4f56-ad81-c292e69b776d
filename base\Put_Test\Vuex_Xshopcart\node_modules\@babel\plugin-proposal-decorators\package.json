{"name": "@babel/plugin-proposal-decorators", "version": "7.10.5", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "publishConfig": {"access": "public"}, "description": "Compile class and object decorators to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-decorators"}, "main": "lib/index.js", "keywords": ["babel", "babel-plugin", "decorators"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.10.5", "@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-decorators": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "gitHead": "f7964a9ac51356f7df6404a25b27ba1cffba1ba7"}