/* 久坐提醒助手样式文件 - 浅色温馨主题 */

/* 自定义浅色系颜色变量 */
:root {
    --light-pink: #FFB6C1;
    --light-coral: #FFA07A;
    --light-green: #48C0A3   ; 
    --pale-green: #A4E2C6   ;
    --light-blue: #87CEEB;
    --lavender: #E6E6FA;

    --peach: #FFDAB9;
    --mint: #F0FFF0;
}

/* 全局样式 */
body {
    font-family: 'Poppins', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    background: linear-gradient(135deg, #FFF8DC 0%, #F0F8FF 100%);
}

/* 头部样式 */
header h1 {
    color: #181818;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

header p {
    color: #4d4d4d;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}
.bg{
    background: #FFFAF0;
}
.sandal {
    color:#B36D61;
}  
.form-text{
    color: #4d4d4d;
}
.light-green{
    color: #48C0A3; 
}
.natural-green{
    color:#198754;
}
.official-green{
    color: #4682B4;
}
#progressBar{
width: 0%; 
background: linear-gradient(90deg, #FFB6C1, #FFA07A);
}
/* 卡片样式 */
.card {
    border: none;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(255,182,193,0.2);
}

/* 时间选项按钮 */
.time-option {
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.time-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.time-option:hover::before {
    left: 100%;
}

.time-option {
    background: linear-gradient(135deg, #FFF8DC, #F0F8FF);
    color: #8B4513;
    transition: all 0.2s ease;
}

.time-option:hover {
    background: linear-gradient(135deg, var(--peach), var(--lavender));
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 182, 193, 0.4);
}

.time-option.active {
    background: linear-gradient(135deg, var(--light-pink), var(--light-coral));
    border-color: var(--light-coral);
    color: #8B4513;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 160, 122, 0.5);
}

/* 进度条样式 */
.progress {
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, #FFB6C1, #FFA07A);
    transition: width 1s ease;
}

/* 统计卡片 */
.stat-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: none;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(255,182,193,0.2);
}

/* 危害卡片 */
.harm-card {
    border-left: 4px solid #FFB6C1;
    background: linear-gradient(135deg, #FFF8DC, #FFFAF0);
    border-radius: 15px;
}

/* 建议卡片 */
.tip-card {
    border-left: 4px solid var(--light-green);
    background: linear-gradient(135deg, #F0FFF0, #FFFAF0);
    border-radius: 15px;
}

/* 按钮样式 */
.btn {
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover {
    color: #8B4513;
}

.btn:hover::before {
    left: 100%;
}

.btn-success {
    background: linear-gradient(135deg, var(--light-green), var(--pale-green));
    border: none;
    color: #2F4F4F;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(152, 251, 152, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--pale-green), var(--light-green));
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(152, 251, 152, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, var(--light-pink), var(--light-coral));
    border: none;
    color: #8B4513!important;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(255, 182, 193, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, var(--light-coral), var(--light-pink));
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(255, 182, 193, 0.4);
}

/* 通知权限提示 */
.notification-permission {
    animation: slideInRight 0.5s ease;
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(255,182,193,0.3);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    header h1 {
        font-size: 1.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .time-option {
        font-size: 0.875rem;
        padding: 0.5rem;
    }
}

/* 动画效果 */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.btn:active {
    animation: pulse 0.3s ease;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #dfe0e2;
    border-radius: 10px;
}


/* 表格样式 */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table th {
    color: #212529;
    border: none;
    font-weight: 600;
}

.table td {
    border-color: #e9ecef;
    vertical-align: middle;
}

/* 页脚样式 */
footer a {
    color: #6c757d;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #B36D61;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 成功状态 */
.status-success {
    color: var(--light-green);
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

/* 运行状态 */
.status-running {
    color: var(--light-pink);
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

/* 停止状态 */
.status-stopped {
    color: #B0B0B0;
    font-weight: 500;
}
