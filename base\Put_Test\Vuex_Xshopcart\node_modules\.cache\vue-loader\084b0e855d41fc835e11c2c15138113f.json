{"remainingRequest": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\App.vue?vue&type=style&index=0&lang=scss&", "dependencies": [{"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\App.vue", "mtime": 1649424514352}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1657986309260}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1657986331183}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1657986317252}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1657986293337}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1657986293299}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1657986322488}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKI2FwcCB7CiAgZm9udC1mYW1pbHk6IEF2ZW5pciwgSGVsdmV0aWNhLCBBcmlhbCwgc2Fucy1zZXJpZjsKICAtd2Via2l0LWZvbnQtc21vb3RoaW5nOiBhbnRpYWxpYXNlZDsKICAtbW96LW9zeC1mb250LXNtb290aGluZzogZ3JheXNjYWxlOwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBjb2xvcjogIzJjM2U1MDsKICBtYXJnaW4tdG9wOiA2MHB4Owp9Cg=="}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\n<div id=\"app\">\n  <HelloWorld msg=\"Welcome to Your Vue.js App\" />\n</div>\n</template>\n\n<script>\nimport HelloWorld from './components/HelloWorld.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    HelloWorld\n  }\n}\n</script>\n\n<style lang=\"scss\">\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-align: center;\n  color: #2c3e50;\n  margin-top: 60px;\n}\n</style>\n"]}]}