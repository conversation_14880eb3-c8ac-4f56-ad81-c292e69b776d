<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>兄弟组件之间的传值--触发事件传递</title>
		<script src="https://unpkg.com/mitt/dist/mitt.umd.js"></script>
        <script src="https://cdn.staticfile.org/vue/2.2.2/vue.min.js"></script>
	</head>
	<body>
		<div id="app">
			<div>我是头部</div>：
			<tempbtn></tempbtn>

			<div>我是底部</div>
			<tempbtn2></tempbtn2>
		</div>
		<template id="temp1">
			<h4>我是组件1内容</h4>
			<button @click="tempclick">点击我</button>
		</template>
		<template id="temp2">
			<h4>我是组件2内容</h4>
			<h4>{{msg}}</h4>
		</template>
		
	</body>
	<script type="text/javascript">
	//需求：点击按钮把组件1的msg传给组件2
	// 1.单独定义一个vue实例对象,不需要绑定视图层和数据层,
	// 让这个vue实例对象专门用来触发事件,侦听事件
		// 需要引入mitt的cdn
		const hub=window.mitt()
	
			
		//组件1的数据层
		const tempdata={
			navdata:["首页","商品","订单"],
			msg:"我是组件1中的msg内容"
			
		}
		//组件2的数据层
		const tempdata2={
			msg:'22222'
			
		}
		
		//定义组件1
		const tempapp={
		  data() {
			return tempdata
		  },
		  template: '#temp1',
		  methods:{
			  tempclick(){
				  console.log("-----click函数")
				  console.log(this.msg)
				  // 注意父子传递与兄弟传递的区别:
				  // 父子组件之间的传递因为是共用一个vue实例对象,this指的就是这个对象
				  // this.$emit("toparent",this.msg)
				  // 2.定义触发事件：兄弟之间没有一个公共的实例对象,所以创建一个hub来将组件1的值传出去
				  hub.emit("tobrother",this.msg)
			  }
		  } 
		}
		//定义组件2
		const tempapp2={
		  data() {
			return tempdata2
		  },
		  template: '#temp2',
		  created(){
			  // 3。触发事件的侦听:最好写在初始化函数里面，程序刚运行就自动执行
				// 参数(触发事件的名称,绑定该事件的函数,负责接收数据.)
				hub.on("tobrother",this.getmsg)
                // dose not provide an export named 'default'
		  },
		  methods:{
			// 4.在组件2中接收组件1的值
			getmsg(msg){
				console.log(msg);
				this.msg=msg
			}  
		  } 
		}
		
		// app的数据层
		const data = {
			nav:[],
			msg:''
			
		}	
		const vm = Vue.createApp({
		  data() {
			return data
		  },
		 
		  components:{
			  tempbtn: tempapp,
			  tempbtn2: tempapp2
			  
		  }
		  
		  
		})
		
		vm.mount('#app')
	
			
		</script>
</html>


