const set = require('regenerate')();
set.addRange(0xFDD0, 0xFDEF).addRange(0xFFFE, 0xFFFF).addRange(0x1FFFE, 0x1FFFF).addRange(0x2FFFE, 0x2FFFF).addRange(0x3FFFE, 0x3FFFF).addRange(0x4FFFE, 0x4FFFF).addRange(0x5FFFE, 0x5FFFF).addRange(0x6FFFE, 0x6FFFF).addRange(0x7FFFE, 0x7FFFF).addRange(0x8FFFE, 0x8FFFF).addRange(0x9FFFE, 0x9FFFF).addRange(0xAFFFE, 0xAFFFF).addRange(0xBFFFE, 0xBFFFF).addRange(0xCFFFE, 0xCFFFF).addRange(0xDFFFE, 0xDFFFF).addRange(0xEFFFE, 0xEFFFF).addRange(0xFFFFE, 0xFFFFF).addRange(0x10FFFE, 0x10FFFF);
module.exports = set;
