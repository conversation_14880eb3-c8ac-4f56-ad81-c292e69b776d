{"name": "is-path-inside", "version": "2.1.0", "description": "Check if a path is inside another path", "license": "MIT", "repository": "sindresorhus/is-path-inside", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["path", "inside", "folder", "directory", "dir", "file", "resolve"], "dependencies": {"path-is-inside": "^1.0.2"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}