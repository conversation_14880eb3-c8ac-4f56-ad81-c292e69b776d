{"name": "@types/anymatch", "version": "1.3.1", "description": "TypeScript definitions for anymatch", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "226033cdde45fb78998524c0f183693e47b7bae0c375ca7ed333309df5735f0f", "typeScriptVersion": "2.0"}