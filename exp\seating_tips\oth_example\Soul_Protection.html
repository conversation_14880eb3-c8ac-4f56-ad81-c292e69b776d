<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>心灵守护 - 儿童心理健康APP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        :root {
            --primary: #5D69F3;
            --secondary: #8A94E9;
            --accent: #FF7E9D;
            --light: #F0F5FF;
            --dark: #2E3650;
            --success: #4CAF50;
            --warning: #FFC107;
            --danger: #F44336;
            --text: #333;
            --text-light: #666;
            --border-radius: 16px;
        }

        body {
            background-color: var(--light);
            color: var(--text);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
            padding-bottom: 80px;
        }

        .app-container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }

        /* 头部样式 */
        .app-header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 20px 16px;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .app-title {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
            font-weight: bold;
            font-size: 18px;
        }

        /* 主内容区域 */
        .main-content {
            padding: 16px;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--dark);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-title i {
            color: var(--accent);
        }

        /* 情绪卡片 */
        .emotion-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }

        .emotion-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 6px;
            height: 100%;
            background: var(--primary);
        }

        .emotion-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .emotion-title {
            font-size: 1.1rem;
            font-weight: 600;
        }

        .emotion-status {
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            background-color: rgba(93, 105, 243, 0.15);
            color: var(--primary);
        }

        .emotion-chart-container {
            height: 180px;
            margin: 0 -10px;
        }

        /* 活动卡片 */
        .activities-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            margin-bottom: 20px;
        }

        .activity-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }

        .activity-card:active {
            transform: scale(0.98);
        }

        .activity-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            margin: 0 auto 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .activity-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--dark);
        }

        .activity-desc {
            font-size: 0.9rem;
            color: var(--text-light);
        }

        /* 任务卡片 */
        .task-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 16px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 16px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .task-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: linear-gradient(135deg, var(--accent), #FF9EB3);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .task-content {
            flex: 1;
        }

        .task-title {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .task-progress {
            font-size: 0.85rem;
            color: var(--text-light);
        }

        .progress-bar {
            height: 6px;
            background-color: #eee;
            border-radius: 3px;
            margin-top: 8px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: var(--accent);
            border-radius: 3px;
            width: 65%;
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            display: flex;
            justify-content: space-around;
            padding: 12px 0;
            box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.05);
            max-width: 500px;
            margin: 0 auto;
            z-index: 100;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            color: var(--text-light);
            text-decoration: none;
            font-size: 0.8rem;
            transition: color 0.3s ease;
            padding: 5px 10px;
            border-radius: 12px;
        }

        .nav-item.active {
            color: var(--primary);
            background-color: rgba(93, 105, 243, 0.1);
        }

        .nav-item i {
            font-size: 1.4rem;
        }

        /* 模态框 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }

        .modal.active {
            opacity: 1;
            pointer-events: all;
        }

        .modal-content {
            background: white;
            width: 90%;
            max-width: 400px;
            border-radius: var(--border-radius);
            overflow: hidden;
            transform: translateY(20px);
            transition: transform 0.3s ease;
        }

        .modal.active .modal-content {
            transform: translateY(0);
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 20px;
            text-align: center;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .modal-text {
            color: var(--text-light);
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .modal-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 12px 20px;
            border-radius: 50px;
            font-weight: 600;
            cursor: pointer;
            border: none;
            flex: 1;
            text-align: center;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-outline {
            background: transparent;
            color: var(--primary);
            border: 2px solid var(--primary);
        }

        /* 游戏区域 */
        .game-area {
            background-color: #e8f4f8;
            border-radius: var(--border-radius);
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .game-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--dark);
        }

        .game-canvas {
            width: 100%;
            height: 200px;
            background-color: white;
            border-radius: 12px;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }

        .game-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
        }

        /* 响应式设计 */
        @media (max-width: 400px) {
            .activities-grid {
                grid-template-columns: 1fr;
            }

            .app-title {
                font-size: 1.3rem;
            }
        }
    </style>
</head>

<body>
    <div class="app-container">
        <!-- 应用头部 -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">心灵守护</h1>
                <div class="user-avatar">童</div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 情绪监测卡片 -->
            <div class="section-title">
                <i class="fas fa-heartbeat"></i>
                <h2>情绪状态</h2>
            </div>
            <div class="emotion-card">
                <div class="emotion-header">
                    <div class="emotion-title">本周情绪波动</div>
                    <div class="emotion-status">良好</div>
                </div>
                <div class="emotion-chart-container">
                    <canvas id="emotionChart"></canvas>
                </div>
            </div>

            <!-- 今日活动卡片 -->
            <div class="section-title">
                <i class="fas fa-sun"></i>
                <h2>今日活动</h2>
            </div>
            <div class="activities-grid">
                <div class="activity-card" onclick="showMindfulnessModal()">
                    <div class="activity-icon">
                        <i class="fas fa-cloud"></i>
                    </div>
                    <div class="activity-title">正念冥想</div>
                    <div class="activity-desc">5分钟放松练习</div>
                </div>

                <div class="activity-card" onclick="startBreathingExercise()">
                    <div class="activity-icon">
                        <i class="fas fa-wind"></i>
                    </div>
                    <div class="activity-title">呼吸练习</div>
                    <div class="activity-desc">调整呼吸节奏</div>
                </div>

                <div class="activity-card" onclick="showGameModal()">
                    <div class="activity-icon">
                        <i class="fas fa-gamepad"></i>
                    </div>
                    <div class="activity-title">减压游戏</div>
                    <div class="activity-desc">放松心情</div>
                </div>

                <div class="activity-card" onclick="showCBTModal()">
                    <div class="activity-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="activity-title">认知训练</div>
                    <div class="activity-desc">挑战负面想法</div>
                </div>
            </div>

            <!-- 今日任务 -->
            <div class="section-title">
                <i class="fas fa-tasks"></i>
                <h2>今日任务</h2>
            </div>
            <div class="task-card">
                <div class="task-icon">
                    <i class="fas fa-running"></i>
                </div>
                <div class="task-content">
                    <div class="task-title">每日运动</div>
                    <div class="task-progress">已完成65%</div>
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                </div>
            </div>

            <div class="task-card">
                <div class="task-icon">
                    <i class="fas fa-book"></i>
                </div>
                <div class="task-content">
                    <div class="task-title">学习时间</div>
                    <div class="task-progress">已完成40分钟</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 80%;"></div>
                    </div>
                </div>
            </div>

            <!-- 减压游戏 -->
            <div class="game-area">
                <div class="game-title">正念堆石头游戏</div>
                <div class="game-canvas" id="gameCanvas">
                    <!-- 游戏内容将通过JS渲染 -->
                </div>
                <div class="game-controls">
                    <button class="btn btn-primary" onclick="startGame()">开始游戏</button>
                    <button class="btn btn-outline" onclick="resetGame()">重置</button>
                </div>
            </div>
        </main>

        <!-- 底部导航 -->
        <nav class="bottom-nav">
            <a href="#" class="nav-item active">
                <i class="fas fa-home"></i>
                <span>首页</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-brain"></i>
                <span>训练</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span>数据</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </nav>

        <!-- 正念冥想模态框 -->
        <div class="modal" id="mindfulnessModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">正念冥想</h3>
                </div>
                <div class="modal-body">
                    <p class="modal-text">找一个安静的地方，以舒适的姿势坐好，闭上眼睛，将注意力集中在呼吸上...</p>
                    <div class="modal-actions">
                        <button class="btn btn-outline" onclick="closeModal('mindfulnessModal')">稍后</button>
                        <button class="btn btn-primary" onclick="startMeditation()">开始冥想</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 游戏模态框 -->
        <div class="modal" id="gameModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">减压小游戏</h3>
                </div>
                <div class="modal-body">
                    <p class="modal-text">通过堆石头游戏培养专注力，缓解焦虑情绪。尝试堆叠尽可能多的石头！</p>
                    <div class="modal-actions">
                        <button class="btn btn-outline" onclick="closeModal('gameModal')">取消</button>
                        <button class="btn btn-primary" onclick="startGame()">开始游戏</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化情绪图表
        function initEmotionChart() {
            const ctx = document.getElementById('emotionChart').getContext('2d');
            const emotionChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                    datasets: [{
                        label: '情绪值',
                        data: [65, 59, 80, 74, 56, 82, 70],
                        borderColor: '#5D69F3',
                        backgroundColor: 'rgba(93, 105, 243, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 5,
                        pointHoverRadius: 7
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            min: 0,
                            max: 100,
                            grid: {
                                color: 'rgba(0,0,0,0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // 显示模态框
        function showModal(modalId) {
            document.getElementById(modalId).classList.add('active');
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('active');
        }

        // 显示正念冥想模态框
        function showMindfulnessModal() {
            showModal('mindfulnessModal');
        }

        // 显示游戏模态框
        function showGameModal() {
            showModal('gameModal');
        }

        // 开始冥想
        function startMeditation() {
            closeModal('mindfulnessModal');
            alert('冥想引导开始...找个舒适的位置，闭上眼睛，跟随引导进行练习');
            // 实际应用中这里会开始播放冥想音频
        }

        // 开始呼吸练习
        function startBreathingExercise() {
            alert('呼吸练习开始：吸气4秒，屏息4秒，呼气6秒...');
            // 实际应用中这里会显示呼吸动画
        }

        // 显示CBT训练
        function showCBTModal() {
            alert('认知行为训练：识别并挑战负面想法');
            // 实际应用中这里会进入CBT训练模块
        }

        // 开始游戏
        function startGame() {
            closeModal('gameModal');
            const canvas = document.getElementById('gameCanvas');
            canvas.innerHTML = '<p style="margin-top:80px;color:#666;">游戏进行中...尝试堆叠石头</p>';
            canvas.style.backgroundColor = '#e8f4f8';
        }

        // 重置游戏
        function resetGame() {
            const canvas = document.getElementById('gameCanvas');
            canvas.innerHTML = '';
            canvas.style.backgroundColor = 'white';
        }

        // 页面加载完成后初始化
        window.onload = function () {
            initEmotionChart();

            // 添加设备运动检测（用于游戏控制）
            if (window.DeviceMotionEvent) {
                window.addEventListener('devicemotion', function (e) {
                    // 实际应用中会使用这些数据来控制游戏
                    const gravity = e.accelerationIncludingGravity;
                    // console.log('Gravity:', gravity);
                });
            }
        };
    </script>
</body>

</html>