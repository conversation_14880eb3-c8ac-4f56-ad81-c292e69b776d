const set = require('regenerate')(0xCDE, 0x1CD0, 0x1CD2, 0x1CDA, 0x1CF2, 0x1CF4);
set.addRange(0x951, 0x952).addRange(0x964, 0x965).addRange(0xC80, 0xC8C).addRange(0xC8E, 0xC90).addRange(0xC92, 0xCA8).addRange(0xCAA, 0xCB3).addRange(0xCB5, 0xCB9).addRange(0xCBC, 0xCC4).addRange(0xCC6, 0xCC8).addRange(0xCCA, 0xCCD).addRange(0xCD5, 0xCD6).addRange(0xCE0, 0xCE3).addRange(0xCE6, 0xCEF).addRange(0xCF1, 0xCF2).addRange(0xA830, 0xA835);
module.exports = set;
