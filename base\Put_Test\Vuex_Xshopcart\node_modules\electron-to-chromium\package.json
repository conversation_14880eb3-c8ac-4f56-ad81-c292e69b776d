{"name": "electron-to-chromium", "version": "1.3.567", "description": "Provides a list of electron-to-chromium version mappings", "main": "index.js", "files": ["versions.js", "full-versions.js", "chromium-versions.js", "full-chromium-versions.js", "LICENSE"], "scripts": {"build": "node build.js", "update": "node automated-update.js", "test": "nyc ava --verbose", "report": "nyc report --reporter=text-lcov > coverage.lcov && codecov"}, "repository": {"type": "git", "url": "https://github.com/kilian/electron-to-chromium/"}, "keywords": ["electron", "chrome", "browserlist"], "author": "<PERSON><PERSON>", "license": "ISC", "devDependencies": {"ava": "^3.8.2", "codecov": "^3.1.0", "electron-releases": "^3.509.0", "nyc": "^15.1.0", "request": "^2.88.0", "shelljs": "^0.7.6"}}