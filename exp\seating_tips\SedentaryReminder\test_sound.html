<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sound Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <h1>Sedentary Reminder Sound Test</h1>
    
    <div class="test-section">
        <h2>Sound Settings Test</h2>
        <p>Test different sound settings:</p>
        
        <label for="soundSelect">Select Sound:</label>
        <select id="soundSelect">
            <option value="none">No Sound (Silent)</option>
            <option value="beep" selected>Beep</option>
            <option value="chime">Chime</option>
        </select>
        
        <button onclick="testSound()">Test Sound</button>
        
        <div class="status" id="status">Ready to test</div>
    </div>
    
    <div class="test-section">
        <h2>Settings Persistence Test</h2>
        <p>Test if settings are saved and loaded correctly:</p>
        
        <button onclick="saveTestSettings()">Save Current Setting</button>
        <button onclick="loadTestSettings()">Load Saved Setting</button>
        <button onclick="clearTestSettings()">Clear Settings</button>
        
        <div class="status" id="persistenceStatus">No settings saved</div>
    </div>

    <script>
        let reminderSoundSetting = 'beep';

        // Play reminder sound function (copied from main app)
        function playReminderSound() {
            if (reminderSoundSetting === 'none') {
                document.getElementById('status').textContent = 'No Sound mode - Silent (working correctly!)';
                document.getElementById('status').style.background = '#d4edda';
                return; // No sound mode - silent
            }

            try {
                let audioContext;
                if (window.AudioContext) {
                    audioContext = new AudioContext();
                } else if (window.webkitAudioContext) {
                    audioContext = new webkitAudioContext();
                } else {
                    document.getElementById('status').textContent = 'Web Audio API not supported';
                    document.getElementById('status').style.background = '#f8d7da';
                    return;
                }

                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                if (reminderSoundSetting === 'beep') {
                    document.getElementById('status').textContent = 'Playing Beep sound...';
                    document.getElementById('status').style.background = '#cce5ff';
                    
                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
                    oscillator.start(audioContext.currentTime);
                    oscillator.stop(audioContext.currentTime + 0.5);
                } else if (reminderSoundSetting === 'chime') {
                    document.getElementById('status').textContent = 'Playing Chime sound...';
                    document.getElementById('status').style.background = '#cce5ff';
                    
                    const frequencies = [523, 659, 784]; // C, E, G notes
                    frequencies.forEach((freq, index) => {
                        const osc = audioContext.createOscillator();
                        const gain = audioContext.createGain();
                        
                        osc.connect(gain);
                        gain.connect(audioContext.destination);
                        
                        osc.frequency.setValueAtTime(freq, audioContext.currentTime);
                        gain.gain.setValueAtTime(0.2, audioContext.currentTime + index * 0.2);
                        gain.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + index * 0.2 + 0.8);
                        
                        osc.start(audioContext.currentTime + index * 0.2);
                        osc.stop(audioContext.currentTime + index * 0.2 + 0.8);
                    });
                }
                
                setTimeout(() => {
                    document.getElementById('status').textContent = 'Sound test completed';
                    document.getElementById('status').style.background = '#f8f9fa';
                }, 2000);
            } catch (error) {
                document.getElementById('status').textContent = 'Error playing sound: ' + error.message;
                document.getElementById('status').style.background = '#f8d7da';
            }
        }

        function testSound() {
            const selectedSound = document.getElementById('soundSelect').value;
            reminderSoundSetting = selectedSound;
            playReminderSound();
        }

        function saveTestSettings() {
            const settings = {
                reminderSound: document.getElementById('soundSelect').value
            };
            localStorage.setItem('sedentaryReminderTestSettings', JSON.stringify(settings));
            document.getElementById('persistenceStatus').textContent = 'Settings saved: ' + settings.reminderSound;
            document.getElementById('persistenceStatus').style.background = '#d4edda';
        }

        function loadTestSettings() {
            const saved = localStorage.getItem('sedentaryReminderTestSettings');
            if (saved) {
                const settings = JSON.parse(saved);
                document.getElementById('soundSelect').value = settings.reminderSound;
                reminderSoundSetting = settings.reminderSound;
                document.getElementById('persistenceStatus').textContent = 'Settings loaded: ' + settings.reminderSound;
                document.getElementById('persistenceStatus').style.background = '#cce5ff';
            } else {
                document.getElementById('persistenceStatus').textContent = 'No saved settings found';
                document.getElementById('persistenceStatus').style.background = '#fff3cd';
            }
        }

        function clearTestSettings() {
            localStorage.removeItem('sedentaryReminderTestSettings');
            document.getElementById('persistenceStatus').textContent = 'Settings cleared';
            document.getElementById('persistenceStatus').style.background = '#f8f9fa';
        }

        // Initialize
        document.getElementById('soundSelect').addEventListener('change', function() {
            reminderSoundSetting = this.value;
        });
    </script>
</body>
</html>
