{"name": "html-comment-regex", "version": "1.1.2", "description": "Regular expression for matching HTML comments", "homepage": "https://github.com/stevemao/html-comment-regex", "author": {"name": "<PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com", "url": "https://github.com/stevemao"}, "repository": "stevemao/html-comment-regex", "license": "MIT", "files": ["index.js"], "keywords": ["html-comment-regex", "text", "string", "regex", "regexp", "re", "match", "test", "find", "pattern", "comment", "comments", "html", "HTML", "HyperText Markup Language"], "dependencies": {}, "devDependencies": {"jscs": "^1.11.3", "jshint": "^2.6.3", "mocha": "*"}, "scripts": {"lint": "jshint *.js --exclude node_modules && jscs *.js", "test": "npm run-script lint && mocha"}}