<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>

<body>
    <script>
        // 1.请实现一段计算逻辑，将数据”a”缓存在浏览器本地，每次打开页面后查看浏览器存储中
        // 是否存在，如果不存在则重新存储，如果存在则alter提示用户已存在，但在每年的圣诞节
        // 时，不用前面的判断，直接alert（"圣诞快乐"）。

        // var now = new Date();
        // var year = now.getFullYear();
        // var month = now.getMonth() + 1;
        // var day = now.getDate();

        // if (month === 12 && day === 25) {
        //     alert("圣诞快乐");
        // } else {
        //     var a = localStorage.getItem("a");
        //     if (a) {
        //         alert("已存在");
        //     } else {
        //         localStorage.setItem("a", "123");
        //     }
        // }

        // 2.实现一个函数sum，该函数可以实现不同参数的累加功能。例如：
        // sum(1)(2)(3) 输出 6
        // sum(10)(20) 输出 30

        function sum(num) {
            debugger
            var result = num;
            function innerSum(nextNum) {
                result += nextNum;
                return innerSum;
            }
            innerSum.toString = function () {
                return result;
            };
            return innerSum;
        }
        sum(1)(2)(3)

        // 这个函数接受一个数字作为参数，返回一个函数。
        // 这个返回的函数也接受一个数字作为参数，然后返回自身。
        // 这样就可以实现链式调用。当最后调用返回的函数时，它会返回累加的结果。
        // 当调用sum(1)时，返回一个函数addNext，total为1。
        // 闭包
        // 当调用addNext(2)时，total变为3，返回addNext函数。
        // 当调用addNext(3)时，total变为6，返回addNext函数。
        // 最后调用返回的函数时，它会返回累加的结果6。因此，sum(1)(2)(3)的结果为6。


        // **项目中遇到的最大的困难？怎么解决的？**
        // 组件通信：在多组件间通信时，我遇到了困难。我通过使用 Vuex 解决了这个问题，这是一个全局状态管理工具，可以在组件之间共享数据。路由：在使用 Vue Router 时，我遇到了路由守卫的困难。我通过实现 beforeEach 守卫函数解决了这个问题，它可以在导航到每个路由前验证用户是否已经登录。数据绑定：在绑定数据时，我遇到了更新问题。我通过使用 Vue.set()函数解决了这个问题，它可以动态地向 Vue 实例添加响应式属性。性能问题：在项目中，我遇到了性能问题。我通过使用异步组件和懒加载图片解决了这个问题，这样可以减少页面加载时间。

        // 用webpack打包后访问index.html出现资源加载404问题

        // 解决方案：config中index.js中，build对象中的assetsPublicPath属性的层级需要由‘/’ 调整为'./'
        // 开发环境的static文件夹是基于根目录的，所以直接用‘/’ 。例如这种格式：http://localhost:8080/static/img/logo.png。

        // webpack打包会自动把static文件夹打包进去，默认会生成一个dist文件夹作为生产环境文件的根目录，
        // 在dist里面才会生成static文件夹。因此生成的格式应该为http://localhost:8080/dist/static/img/logo.png。
        // 并不是基于根目录，所以 ‘/’ 肯定是找不到对应资源的。

        // 1.  有两个非空的链表，表示两个非负的整数。它们每位数字都是按照逆序的方式存
        // 储的，并且每个节点只能存储 一位 数字, 请你两个数相加，并以相同形式返回
        // 一个表示和的链表。输入：l1 = [2,4,3], l2 = [6,6,4]
        // 输出：[8,0,8]
        // 过程：342 + 466 = 808。  请用js实现它

        function ListNode(val) {
            this.val = val;
            this.next = null;
        }

        function addTwoNumbers(l1, l2) {
            let dummy = new ListNode(0); // 定义一个虚拟头节点
            let p = l1, q = l2, curr = dummy; // 定义两个指针和一个当前节点指针
            let carry = 0; // 定义进位值，初始值为0
            while (p != null || q != null) { // 遍历两个链表
                let x = (p != null) ? p.val : 0; // 如果链表已经遍历完了，则将节点的值设为0
                let y = (q != null) ? q.val : 0;
                let sum = carry + x + y; // 计算两个节点的和以及进位值
                carry = Math.floor(sum / 10); // 更新进位值
                curr.next = new ListNode(sum % 10); // 创建新节点，并将其添加到新链表中
                curr = curr.next; // 将当前节点指针向后移动一位
                if (p != null) p = p.next; // 将两个链表的指针向后移动一位
                if (q != null) q = q.next;
            }
            if (carry > 0) { // 如果进位值不为0，则需要在新链表末尾添加一个节点
                curr.next = new ListNode(carry);
            }
            return dummy.next; // 返回新链表的头节点
        }

        // 测试用例
        let l1 = new ListNode(2);
        l1.next = new ListNode(4);
        l1.next.next = new ListNode(3);

        let l2 = new ListNode(6);
        l2.next = new ListNode(6);
        l2.next.next = new ListNode(4);

        let result = addTwoNumbers(l1, l2);
        console.log(result); // 输出 [8,0,8]


        // 2. 列表由在范围 [1, n] 中的所有整数组成，并按严格递增排序。现在从左到右
        // 删除第一个数字，然后每隔一个数字删除一个，直到到达列表末尾。重复上面
        // 的步骤，但这次是从右到左。也就是，删除最右侧的数字，然后剩下的数字每
        // 隔一个删除一个。不断重复这两步，从左到右和从右到左交替进行，直到只剩
        // 下一个数字。给你整数 n ，返回最后剩下的数字。
        // 举例:
        // 输入：n = 9
        // 输出：6
        // 过程：L1 = [1, 2, 3, 4, 5, 6, 7, 8, 9]
        // L2 = [2, 4, 6, 8]
        // L3 = [2, 6]
        // L4 = [6] ，请用js实现，并在控制台输出结果。

        // 解决流程：
        // 1. 定义一个数组，用于存储所有的数字。
        // 2. 定义一个变量，表示当前删除的数字在数组中的下标，初始值为0。
        // 3. 定义一个变量，表示当前删除数字的方向，初始值为1，表示从左到右。
        // 4. 定义一个变量，表示当前剩余数字的个数，初始值为n。
        // 5. 当剩余数字的个数大于1时，执行以下步骤：
        //    - 如果当前方向为从左到右，则从当前下标开始，每隔一个数字删除一个，并将方向改为从右到左。
        //    - 如果当前方向为从右到左，则从当前下标开始，每隔一个数字删除一个，并将方向改为从左到右。
        //    - 更新当前下标和剩余数字的个数。
        // 6. 返回数组中剩余数字的值。

        // 下面是使用JavaScript实现的代码：

        // function lastRemaining(n) {
        //     let nums = Array.from({ length: n }, (_, i) => i + 1); // 定义一个数组，存储所有数字
        //     let index = 0; // 定义当前删除数字的下标，初始值为0
        //     let direction = 1; // 定义当前删除数字的方向，初始值为1，表示从左到右
        //     while (nums.length > 1) { // 当剩余数字的个数大于1时
        //         if (direction === 1) { // 如果当前方向为从左到右
        //             for (let i = index; i < nums.length; i += 2) { // 从当前下标开始，每隔一个数字删除一个
        //                 nums.splice(i, 1);
        //             }
        //             direction = -1; // 将方向改为从右到左
        //             index = nums.length - 1; // 更新当前下标
        //         } else { // 如果当前方向为从右到左
        //             for (let i = index; i >= 0; i -= 2) { // 从当前下标开始，每隔一个数字删除一个
        //                 nums.splice(i, 1);
        //             }
        //             direction = 1; // 将方向改为从左到右
        //             index = 0; // 更新当前下标
        //         }
        //     }
        //     return nums[0]; // 返回数组中剩余数字的值
        // }

        // // 测试用例
        // console.log(lastRemaining(9)); // 输出 6


        // for (var i = 0; i < 10; i++) {
        //     setTimeout(function () {
        //         console.log(i)
        //     }, 1000);
        // }




    </script>
</body>

</html>