<div class="apiDetail">
<div>
	<h2><span>Function(editable)</span><span class="path">zTreeObj.</span>setEditable</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.exedit</span> 扩展 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>设置 zTree 进入 / 取消 编辑状态。</p>
			<p class="highlight_red">对于编辑状态的各种功能需要提前设置对应 setting 中的不同属性</p>
			<p class="highlight_red">请通过 zTree 对象执行此方法。</p>
		</div>
	</div>
	<h3>Function 参数说明</h3>
	<div class="desc">
	<h4><b>editable</b><span>Boolean</span></h4>
	<p>true 表示进入 编辑状态</p>
	<p>false 表示取消 编辑状态</p>
	<h4 class="topLine"><b>返回值</b><span>无</span></h4>
	<p>目前无任何返回值</p>
	</div>
	<h3>function 举例</h3>
	<h4>1. 设置 zTree 进入编辑状态</h4>
	<pre xmlns=""><code>var treeObj = $.fn.zTree.getZTreeObj("tree");
treeObj.setEditable(true);
</code></pre>
</div>
</div>