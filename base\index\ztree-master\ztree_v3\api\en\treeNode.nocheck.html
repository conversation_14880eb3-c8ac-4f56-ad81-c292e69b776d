<div class="apiDetail">
<div>
	<h2><span>Boolean</span><span class="path">treeNode.</span>nocheck</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.excheck</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>1. Set node to hide the checkbox or radio. It is valid when <span class="highlight_red">[setting.check.enable = true]</span></p>
			<p class="highlight_red">2. zTree support identification string 'true' & 'false'.</p>
			<p>Default: false</p>
		</div>
	</div>
	<h3>Boolean Format</h3>
	<div class="desc">
	<p class="highlight_red">true means: the node hide the checkbox or radio, and don't affect the checked association, and don't affect its parent node's half-checked status.</p>
	<p class="highlight_red">false means: the node show the checkbox or radio.</p>
	</div>
	<h3>Examples of treeNode</h3>
	<h4>1. Hide some node's checkbox / radio </h4>
	<pre xmlns=""><code>var nodes = [
	{ "id":1, "name":"test1", "nocheck":true},
	{ "id":2, "name":"test2"},
	{ "id":3, "name":"test3"}
]</code></pre>
</div>
</div>