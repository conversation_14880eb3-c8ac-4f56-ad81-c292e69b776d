{"name": "object.assign", "version": "4.1.1", "author": "<PERSON>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run --silent lint && es-shim-api --bound", "test": "npm run --silent tests-only && npm run test:ses", "posttest": "aud --production", "tests-only": "npm run --silent test:implementation && npm run --silent test:shim", "test:native": "node test/native", "test:shim": "node test/shimmed", "test:implementation": "node test", "test:ses": "node test/ses-compat", "coverage": "covert test/*.js", "lint": "eslint .", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "prepublish": "safe-publish-latest && npm run build"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object.assign.git"}, "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore", "es-shim API", "polyfill", "shim"], "dependencies": {"define-properties": "^1.1.3", "es-abstract": "^1.18.0-next.0", "has-symbols": "^1.0.1", "object-keys": "^1.1.1"}, "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^17.2.0", "aud": "^1.1.2", "browserify": "^16.5.0", "covert": "^1.1.1", "eslint": "^7.8.1", "for-each": "^0.3.3", "functions-have-names": "^1.2.1", "has": "^1.0.3", "is": "^3.3.0", "safe-publish-latest": "^1.1.4", "ses": "^0.10.3", "tape": "^5.0.1"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}}