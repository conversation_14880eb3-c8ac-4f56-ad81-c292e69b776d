{"name": "onetime", "version": "2.0.1", "description": "Ensure a function is only called once", "license": "MIT", "repository": "sindresorhus/onetime", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["once", "function", "one", "onetime", "func", "fn", "single", "call", "called", "prevent"], "dependencies": {"mimic-fn": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}}