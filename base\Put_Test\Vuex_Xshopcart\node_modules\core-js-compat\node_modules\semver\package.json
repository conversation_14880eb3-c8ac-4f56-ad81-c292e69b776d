{"name": "semver", "version": "7.0.0", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.10.1"}, "license": "ISC", "repository": "https://github.com/npm/node-semver", "bin": {"semver": "./bin/semver.js"}, "files": ["bin", "range.bnf", "classes", "functions", "internal", "ranges", "index.js"], "tap": {"check-coverage": true, "coverage-map": "map.js"}}