{"name": "@webassemblyjs/floating-point-hex-parser", "scripts": {"build-fuzzer": "[ -f ./test/fuzzing/parse.out ] || gcc ./test/fuzzing/parse.c -o ./test/fuzzing/parse.out -lm -Wall"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "version": "1.9.0", "description": "A function to parse floating point hexadecimal strings as defined by the WebAssembly specification", "main": "lib/index.js", "module": "esm/index.js", "keywords": ["webassembly", "floating-point"], "author": "<PERSON><PERSON>", "license": "MIT", "gitHead": "0440b420888c1f7701eb9762ec657775506b87d8"}