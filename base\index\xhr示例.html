<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <script>
    let xhr = new XMLHttpRequest();
    xhr.open('get','http://127.0.0.1:4523/mock/525878/pet/1');
    xhr.setRequestHeader("Content-type","application/x-www-form-urlencoded");
    xhr.send();
    xhr.onreadystatechange = function(){
      if(xhr.readyState == 4 && xhr.status == 200){
        let data = xhr.responseText
        console.log("%c [  data ]", "font-size:13px; background:#00ffff; color:red;", data)
      

      }
    }


    </script>
</body>
</html>