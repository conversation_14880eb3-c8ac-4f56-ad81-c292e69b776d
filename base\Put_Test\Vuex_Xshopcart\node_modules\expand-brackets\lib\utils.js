'use strict';

var toRegex = require('to-regex');
var regexNot = require('regex-not');
var cached;

/**
 * Get the last element from `array`
 * @param {Array} `array`
 * @return {*}
 */

exports.last = function(arr) {
  return arr[arr.length - 1];
};

/**
 * Create and cache regex to use for text nodes
 */

exports.createRegex = function(pattern, include) {
  if (cached) return cached;
  var opts = {contains: true, strictClose: false};
  var not = regexNot.create(pattern, opts);
  var re;

  if (typeof include === 'string') {
    re = toRegex('^(?:' + include + '|' + not + ')', opts);
  } else {
    re = toRegex(not, opts);
  }

  return (cached = re);
};
