{"name": "@babel/helper-member-expression-to-functions", "version": "7.11.0", "description": "Helper function to replace certain member expressions with function calls", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-member-expression-to-functions"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "author": "<PERSON> <<EMAIL>>", "dependencies": {"@babel/types": "^7.11.0"}}