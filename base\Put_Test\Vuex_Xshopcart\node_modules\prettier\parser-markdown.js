!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(((e=e||self).prettierPlugins=e.prettierPlugins||{},e.prettierPlugins.markdown={}))}(this,(function(e){"use strict";function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function i(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&u(e,t)}function o(e){return(o=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function u(e,t){return(u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function s(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function c(e,t,r){return(c="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=o(e)););return e}(e,t);if(n){var i=Object.getOwnPropertyDescriptor(n,t);return i.get?i.get.call(r):i.value}})(e,t,r||e)}function l(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if(!(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e)))return;var r=[],n=!0,i=!1,a=void 0;try{for(var o,u=e[Symbol.iterator]();!(n=(o=u.next()).done)&&(r.push(o.value),!t||r.length!==t);n=!0);}catch(e){i=!0,a=e}finally{try{n||null==u.return||u.return()}finally{if(i)throw a}}return r}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}var p=function(){for(var e={},t=0;t<arguments.length;t++){var r=arguments[t];for(var n in r)f.call(r,n)&&(e[n]=r[n])}return e},f=Object.prototype.hasOwnProperty;function h(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function d(e,t){return e(t={exports:{}},t.exports),t.exports}function D(e){return e&&e.default||e}var g=Object.freeze({__proto__:null,default:{}}),m=d((function(e){"function"==typeof Object.create?e.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:e.exports=function(e,t){if(t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}}})),v=D(g),b=d((function(e){try{var t=v;if("function"!=typeof t.inherits)throw"";e.exports=t.inherits}catch(t){e.exports=m}})),y=function(e){var r,n,i;for(n in b(o,e),b(a,o),r=o.prototype)(i=r[n])&&"object"===t(i)&&(r[n]="concat"in i?i.concat():p(i));return o;function a(t){return e.apply(this,t)}function o(){return this instanceof o?e.apply(this,arguments):new a(arguments)}}
/**
   * <AUTHOR> Wormer
   * @copyright 2016 Titus Wormer
   * @license MIT
   * @module state-toggle
   * @fileoverview Enter/exit a state.
   */;var E=function(e,t,r){return function(){var n=r||this,i=n[e];return n[e]=!t,function(){n[e]=i}}};var C=function(e){var t=function(e){var t=[],r=e.indexOf("\n");for(;-1!==r;)t.push(r+1),r=e.indexOf("\n",r+1);return t.push(e.length+1),t}(String(e));return{toPosition:A(t),toOffset:w(t)}};function A(e){return function(t){var r=-1,n=e.length;if(t<0)return{};for(;++r<n;)if(e[r]>t)return{line:r+1,column:t-(e[r-1]||0)+1,offset:t};return{}}}function w(e){return function(t){var r=t&&t.line,n=t&&t.column;if(!isNaN(r)&&!isNaN(n)&&r-1 in e)return(e[r-2]||0)+n-1||0;return-1}}var k=function(e,t){return function(r){var n,i=0,a=r.indexOf("\\"),o=e[t],u=[];for(;-1!==a;)u.push(r.slice(i,a)),i=a+1,(n=r.charAt(i))&&-1!==o.indexOf(n)||u.push("\\"),a=r.indexOf("\\",i);return u.push(r.slice(i)),u.join("")}};var T={AEli:"Æ",AElig:"Æ",AM:"&",AMP:"&",Aacut:"Á",Aacute:"Á",Abreve:"Ă",Acir:"Â",Acirc:"Â",Acy:"А",Afr:"𝔄",Agrav:"À",Agrave:"À",Alpha:"Α",Amacr:"Ā",And:"⩓",Aogon:"Ą",Aopf:"𝔸",ApplyFunction:"⁡",Arin:"Å",Aring:"Å",Ascr:"𝒜",Assign:"≔",Atild:"Ã",Atilde:"Ã",Aum:"Ä",Auml:"Ä",Backslash:"∖",Barv:"⫧",Barwed:"⌆",Bcy:"Б",Because:"∵",Bernoullis:"ℬ",Beta:"Β",Bfr:"𝔅",Bopf:"𝔹",Breve:"˘",Bscr:"ℬ",Bumpeq:"≎",CHcy:"Ч",COP:"©",COPY:"©",Cacute:"Ć",Cap:"⋒",CapitalDifferentialD:"ⅅ",Cayleys:"ℭ",Ccaron:"Č",Ccedi:"Ç",Ccedil:"Ç",Ccirc:"Ĉ",Cconint:"∰",Cdot:"Ċ",Cedilla:"¸",CenterDot:"·",Cfr:"ℭ",Chi:"Χ",CircleDot:"⊙",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",Colon:"∷",Colone:"⩴",Congruent:"≡",Conint:"∯",ContourIntegral:"∮",Copf:"ℂ",Coproduct:"∐",CounterClockwiseContourIntegral:"∳",Cross:"⨯",Cscr:"𝒞",Cup:"⋓",CupCap:"≍",DD:"ⅅ",DDotrahd:"⤑",DJcy:"Ђ",DScy:"Ѕ",DZcy:"Џ",Dagger:"‡",Darr:"↡",Dashv:"⫤",Dcaron:"Ď",Dcy:"Д",Del:"∇",Delta:"Δ",Dfr:"𝔇",DiacriticalAcute:"´",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",Diamond:"⋄",DifferentialD:"ⅆ",Dopf:"𝔻",Dot:"¨",DotDot:"⃜",DotEqual:"≐",DoubleContourIntegral:"∯",DoubleDot:"¨",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",Downarrow:"⇓",Dscr:"𝒟",Dstrok:"Đ",ENG:"Ŋ",ET:"Ð",ETH:"Ð",Eacut:"É",Eacute:"É",Ecaron:"Ě",Ecir:"Ê",Ecirc:"Ê",Ecy:"Э",Edot:"Ė",Efr:"𝔈",Egrav:"È",Egrave:"È",Element:"∈",Emacr:"Ē",EmptySmallSquare:"◻",EmptyVerySmallSquare:"▫",Eogon:"Ę",Eopf:"𝔼",Epsilon:"Ε",Equal:"⩵",EqualTilde:"≂",Equilibrium:"⇌",Escr:"ℰ",Esim:"⩳",Eta:"Η",Eum:"Ë",Euml:"Ë",Exists:"∃",ExponentialE:"ⅇ",Fcy:"Ф",Ffr:"𝔉",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",Fopf:"𝔽",ForAll:"∀",Fouriertrf:"ℱ",Fscr:"ℱ",GJcy:"Ѓ",G:">",GT:">",Gamma:"Γ",Gammad:"Ϝ",Gbreve:"Ğ",Gcedil:"Ģ",Gcirc:"Ĝ",Gcy:"Г",Gdot:"Ġ",Gfr:"𝔊",Gg:"⋙",Gopf:"𝔾",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"𝒢",Gt:"≫",HARDcy:"Ъ",Hacek:"ˇ",Hat:"^",Hcirc:"Ĥ",Hfr:"ℌ",HilbertSpace:"ℋ",Hopf:"ℍ",HorizontalLine:"─",Hscr:"ℋ",Hstrok:"Ħ",HumpDownHump:"≎",HumpEqual:"≏",IEcy:"Е",IJlig:"Ĳ",IOcy:"Ё",Iacut:"Í",Iacute:"Í",Icir:"Î",Icirc:"Î",Icy:"И",Idot:"İ",Ifr:"ℑ",Igrav:"Ì",Igrave:"Ì",Im:"ℑ",Imacr:"Ī",ImaginaryI:"ⅈ",Implies:"⇒",Int:"∬",Integral:"∫",Intersection:"⋂",InvisibleComma:"⁣",InvisibleTimes:"⁢",Iogon:"Į",Iopf:"𝕀",Iota:"Ι",Iscr:"ℐ",Itilde:"Ĩ",Iukcy:"І",Ium:"Ï",Iuml:"Ï",Jcirc:"Ĵ",Jcy:"Й",Jfr:"𝔍",Jopf:"𝕁",Jscr:"𝒥",Jsercy:"Ј",Jukcy:"Є",KHcy:"Х",KJcy:"Ќ",Kappa:"Κ",Kcedil:"Ķ",Kcy:"К",Kfr:"𝔎",Kopf:"𝕂",Kscr:"𝒦",LJcy:"Љ",L:"<",LT:"<",Lacute:"Ĺ",Lambda:"Λ",Lang:"⟪",Laplacetrf:"ℒ",Larr:"↞",Lcaron:"Ľ",Lcedil:"Ļ",Lcy:"Л",LeftAngleBracket:"⟨",LeftArrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",LeftRightArrow:"↔",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",Leftarrow:"⇐",Leftrightarrow:"⇔",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",LessLess:"⪡",LessSlantEqual:"⩽",LessTilde:"≲",Lfr:"𝔏",Ll:"⋘",Lleftarrow:"⇚",Lmidot:"Ŀ",LongLeftArrow:"⟵",LongLeftRightArrow:"⟷",LongRightArrow:"⟶",Longleftarrow:"⟸",Longleftrightarrow:"⟺",Longrightarrow:"⟹",Lopf:"𝕃",LowerLeftArrow:"↙",LowerRightArrow:"↘",Lscr:"ℒ",Lsh:"↰",Lstrok:"Ł",Lt:"≪",Map:"⤅",Mcy:"М",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"𝔐",MinusPlus:"∓",Mopf:"𝕄",Mscr:"ℳ",Mu:"Μ",NJcy:"Њ",Nacute:"Ń",Ncaron:"Ň",Ncedil:"Ņ",Ncy:"Н",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",Nfr:"𝔑",NoBreak:"⁠",NonBreakingSpace:" ",Nopf:"ℕ",Not:"⫬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",Nscr:"𝒩",Ntild:"Ñ",Ntilde:"Ñ",Nu:"Ν",OElig:"Œ",Oacut:"Ó",Oacute:"Ó",Ocir:"Ô",Ocirc:"Ô",Ocy:"О",Odblac:"Ő",Ofr:"𝔒",Ograv:"Ò",Ograve:"Ò",Omacr:"Ō",Omega:"Ω",Omicron:"Ο",Oopf:"𝕆",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",Or:"⩔",Oscr:"𝒪",Oslas:"Ø",Oslash:"Ø",Otild:"Õ",Otilde:"Õ",Otimes:"⨷",Oum:"Ö",Ouml:"Ö",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",PartialD:"∂",Pcy:"П",Pfr:"𝔓",Phi:"Φ",Pi:"Π",PlusMinus:"±",Poincareplane:"ℌ",Popf:"ℙ",Pr:"⪻",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",Prime:"″",Product:"∏",Proportion:"∷",Proportional:"∝",Pscr:"𝒫",Psi:"Ψ",QUO:'"',QUOT:'"',Qfr:"𝔔",Qopf:"ℚ",Qscr:"𝒬",RBarr:"⤐",RE:"®",REG:"®",Racute:"Ŕ",Rang:"⟫",Rarr:"↠",Rarrtl:"⤖",Rcaron:"Ř",Rcedil:"Ŗ",Rcy:"Р",Re:"ℜ",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",Rfr:"ℜ",Rho:"Ρ",RightAngleBracket:"⟩",RightArrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",Rightarrow:"⇒",Ropf:"ℝ",RoundImplies:"⥰",Rrightarrow:"⇛",Rscr:"ℛ",Rsh:"↱",RuleDelayed:"⧴",SHCHcy:"Щ",SHcy:"Ш",SOFTcy:"Ь",Sacute:"Ś",Sc:"⪼",Scaron:"Š",Scedil:"Ş",Scirc:"Ŝ",Scy:"С",Sfr:"𝔖",ShortDownArrow:"↓",ShortLeftArrow:"←",ShortRightArrow:"→",ShortUpArrow:"↑",Sigma:"Σ",SmallCircle:"∘",Sopf:"𝕊",Sqrt:"√",Square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",Sscr:"𝒮",Star:"⋆",Sub:"⋐",Subset:"⋐",SubsetEqual:"⊆",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",SuchThat:"∋",Sum:"∑",Sup:"⋑",Superset:"⊃",SupersetEqual:"⊇",Supset:"⋑",THOR:"Þ",THORN:"Þ",TRADE:"™",TSHcy:"Ћ",TScy:"Ц",Tab:"\t",Tau:"Τ",Tcaron:"Ť",Tcedil:"Ţ",Tcy:"Т",Tfr:"𝔗",Therefore:"∴",Theta:"Θ",ThickSpace:"  ",ThinSpace:" ",Tilde:"∼",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",Topf:"𝕋",TripleDot:"⃛",Tscr:"𝒯",Tstrok:"Ŧ",Uacut:"Ú",Uacute:"Ú",Uarr:"↟",Uarrocir:"⥉",Ubrcy:"Ў",Ubreve:"Ŭ",Ucir:"Û",Ucirc:"Û",Ucy:"У",Udblac:"Ű",Ufr:"𝔘",Ugrav:"Ù",Ugrave:"Ù",Umacr:"Ū",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",Uopf:"𝕌",UpArrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",UpEquilibrium:"⥮",UpTee:"⊥",UpTeeArrow:"↥",Uparrow:"⇑",Updownarrow:"⇕",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",Upsilon:"Υ",Uring:"Ů",Uscr:"𝒰",Utilde:"Ũ",Uum:"Ü",Uuml:"Ü",VDash:"⊫",Vbar:"⫫",Vcy:"В",Vdash:"⊩",Vdashl:"⫦",Vee:"⋁",Verbar:"‖",Vert:"‖",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"𝔙",Vopf:"𝕍",Vscr:"𝒱",Vvdash:"⊪",Wcirc:"Ŵ",Wedge:"⋀",Wfr:"𝔚",Wopf:"𝕎",Wscr:"𝒲",Xfr:"𝔛",Xi:"Ξ",Xopf:"𝕏",Xscr:"𝒳",YAcy:"Я",YIcy:"Ї",YUcy:"Ю",Yacut:"Ý",Yacute:"Ý",Ycirc:"Ŷ",Ycy:"Ы",Yfr:"𝔜",Yopf:"𝕐",Yscr:"𝒴",Yuml:"Ÿ",ZHcy:"Ж",Zacute:"Ź",Zcaron:"Ž",Zcy:"З",Zdot:"Ż",ZeroWidthSpace:"​",Zeta:"Ζ",Zfr:"ℨ",Zopf:"ℤ",Zscr:"𝒵",aacut:"á",aacute:"á",abreve:"ă",ac:"∾",acE:"∾̳",acd:"∿",acir:"â",acirc:"â",acut:"´",acute:"´",acy:"а",aeli:"æ",aelig:"æ",af:"⁡",afr:"𝔞",agrav:"à",agrave:"à",alefsym:"ℵ",aleph:"ℵ",alpha:"α",amacr:"ā",amalg:"⨿",am:"&",amp:"&",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"Å",angzarr:"⍼",aogon:"ą",aopf:"𝕒",ap:"≈",apE:"⩰",apacir:"⩯",ape:"≊",apid:"≋",apos:"'",approx:"≈",approxeq:"≊",arin:"å",aring:"å",ascr:"𝒶",ast:"*",asymp:"≈",asympeq:"≍",atild:"ã",atilde:"ã",aum:"ä",auml:"ä",awconint:"∳",awint:"⨑",bNot:"⫭",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",barvee:"⊽",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",bcy:"б",bdquo:"„",becaus:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",beta:"β",beth:"ℶ",between:"≬",bfr:"𝔟",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bnot:"⌐",bopf:"𝕓",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxDL:"╗",boxDR:"╔",boxDl:"╖",boxDr:"╓",boxH:"═",boxHD:"╦",boxHU:"╩",boxHd:"╤",boxHu:"╧",boxUL:"╝",boxUR:"╚",boxUl:"╜",boxUr:"╙",boxV:"║",boxVH:"╬",boxVL:"╣",boxVR:"╠",boxVh:"╫",boxVl:"╢",boxVr:"╟",boxbox:"⧉",boxdL:"╕",boxdR:"╒",boxdl:"┐",boxdr:"┌",boxh:"─",boxhD:"╥",boxhU:"╨",boxhd:"┬",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxuL:"╛",boxuR:"╘",boxul:"┘",boxur:"└",boxv:"│",boxvH:"╪",boxvL:"╡",boxvR:"╞",boxvh:"┼",boxvl:"┤",boxvr:"├",bprime:"‵",breve:"˘",brvba:"¦",brvbar:"¦",bscr:"𝒷",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",bumpeq:"≏",cacute:"ć",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",caps:"∩︀",caret:"⁁",caron:"ˇ",ccaps:"⩍",ccaron:"č",ccedi:"ç",ccedil:"ç",ccirc:"ĉ",ccups:"⩌",ccupssm:"⩐",cdot:"ċ",cedi:"¸",cedil:"¸",cemptyv:"⦲",cen:"¢",cent:"¢",centerdot:"·",cfr:"𝔠",chcy:"ч",check:"✓",checkmark:"✓",chi:"χ",cir:"○",cirE:"⧃",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledR:"®",circledS:"Ⓢ",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",clubs:"♣",clubsuit:"♣",colon:":",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",conint:"∮",copf:"𝕔",coprod:"∐",cop:"©",copy:"©",copysr:"℗",crarr:"↵",cross:"✗",cscr:"𝒸",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",cup:"∪",cupbrcap:"⩈",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curre:"¤",curren:"¤",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",dArr:"⇓",dHar:"⥥",dagger:"†",daleth:"ℸ",darr:"↓",dash:"‐",dashv:"⊣",dbkarow:"⤏",dblac:"˝",dcaron:"ď",dcy:"д",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",ddotseq:"⩷",de:"°",deg:"°",delta:"δ",demptyv:"⦱",dfisht:"⥿",dfr:"𝔡",dharl:"⇃",dharr:"⇂",diam:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"¨",digamma:"ϝ",disin:"⋲",div:"÷",divid:"÷",divide:"÷",divideontimes:"⋇",divonx:"⋇",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",dopf:"𝕕",dot:"˙",doteq:"≐",doteqdot:"≑",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",downarrow:"↓",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",dscr:"𝒹",dscy:"ѕ",dsol:"⧶",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",dzcy:"џ",dzigrarr:"⟿",eDDot:"⩷",eDot:"≑",eacut:"é",eacute:"é",easter:"⩮",ecaron:"ě",ecir:"ê",ecirc:"ê",ecolon:"≕",ecy:"э",edot:"ė",ee:"ⅇ",efDot:"≒",efr:"𝔢",eg:"⪚",egrav:"è",egrave:"è",egs:"⪖",egsdot:"⪘",el:"⪙",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",emacr:"ē",empty:"∅",emptyset:"∅",emptyv:"∅",emsp13:" ",emsp14:" ",emsp:" ",eng:"ŋ",ensp:" ",eogon:"ę",eopf:"𝕖",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",equals:"=",equest:"≟",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erDot:"≓",erarr:"⥱",escr:"ℯ",esdot:"≐",esim:"≂",eta:"η",et:"ð",eth:"ð",eum:"ë",euml:"ë",euro:"€",excl:"!",exist:"∃",expectation:"ℰ",exponentiale:"ⅇ",fallingdotseq:"≒",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",ffr:"𝔣",filig:"ﬁ",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",fopf:"𝕗",forall:"∀",fork:"⋔",forkv:"⫙",fpartint:"⨍",frac1:"¼",frac12:"½",frac13:"⅓",frac14:"¼",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac3:"¾",frac34:"¾",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",fscr:"𝒻",gE:"≧",gEl:"⪌",gacute:"ǵ",gamma:"γ",gammad:"ϝ",gap:"⪆",gbreve:"ğ",gcirc:"ĝ",gcy:"г",gdot:"ġ",ge:"≥",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",gfr:"𝔤",gg:"≫",ggg:"⋙",gimel:"ℷ",gjcy:"ѓ",gl:"≷",glE:"⪒",gla:"⪥",glj:"⪤",gnE:"≩",gnap:"⪊",gnapprox:"⪊",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",gopf:"𝕘",grave:"`",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",g:">",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",hArr:"⇔",hairsp:" ",half:"½",hamilt:"ℋ",hardcy:"ъ",harr:"↔",harrcir:"⥈",harrw:"↭",hbar:"ℏ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",hfr:"𝔥",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",hopf:"𝕙",horbar:"―",hscr:"𝒽",hslash:"ℏ",hstrok:"ħ",hybull:"⁃",hyphen:"‐",iacut:"í",iacute:"í",ic:"⁣",icir:"î",icirc:"î",icy:"и",iecy:"е",iexc:"¡",iexcl:"¡",iff:"⇔",ifr:"𝔦",igrav:"ì",igrave:"ì",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",ijlig:"ĳ",imacr:"ī",image:"ℑ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",int:"∫",intcal:"⊺",integers:"ℤ",intercal:"⊺",intlarhk:"⨗",intprod:"⨼",iocy:"ё",iogon:"į",iopf:"𝕚",iota:"ι",iprod:"⨼",iques:"¿",iquest:"¿",iscr:"𝒾",isin:"∈",isinE:"⋹",isindot:"⋵",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",itilde:"ĩ",iukcy:"і",ium:"ï",iuml:"ï",jcirc:"ĵ",jcy:"й",jfr:"𝔧",jmath:"ȷ",jopf:"𝕛",jscr:"𝒿",jsercy:"ј",jukcy:"є",kappa:"κ",kappav:"ϰ",kcedil:"ķ",kcy:"к",kfr:"𝔨",kgreen:"ĸ",khcy:"х",kjcy:"ќ",kopf:"𝕜",kscr:"𝓀",lAarr:"⇚",lArr:"⇐",lAtail:"⤛",lBarr:"⤎",lE:"≦",lEg:"⪋",lHar:"⥢",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",lambda:"λ",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",laqu:"«",laquo:"«",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",latail:"⤙",late:"⪭",lates:"⪭︀",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",lcaron:"ľ",lcedil:"ļ",lceil:"⌈",lcub:"{",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",le:"≤",leftarrow:"←",leftarrowtail:"↢",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",leftthreetimes:"⋋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",lessgtr:"≶",lesssim:"≲",lfisht:"⥼",lfloor:"⌊",lfr:"𝔩",lg:"≶",lgE:"⪑",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",ljcy:"љ",ll:"≪",llarr:"⇇",llcorner:"⌞",llhard:"⥫",lltri:"◺",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnE:"≨",lnap:"⪉",lnapprox:"⪉",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",longleftarrow:"⟵",longleftrightarrow:"⟷",longmapsto:"⟼",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",lopf:"𝕝",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",lscr:"𝓁",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",lstrok:"ł",l:"<",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltrPar:"⦖",ltri:"◃",ltrie:"⊴",ltrif:"◂",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",mDDot:"∺",mac:"¯",macr:"¯",male:"♂",malt:"✠",maltese:"✠",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",mcy:"м",mdash:"—",measuredangle:"∡",mfr:"𝔪",mho:"℧",micr:"µ",micro:"µ",mid:"∣",midast:"*",midcir:"⫰",middo:"·",middot:"·",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",mopf:"𝕞",mp:"∓",mscr:"𝓂",mstpos:"∾",mu:"μ",multimap:"⊸",mumap:"⊸",nGg:"⋙̸",nGt:"≫⃒",nGtv:"≫̸",nLeftarrow:"⇍",nLeftrightarrow:"⇎",nLl:"⋘̸",nLt:"≪⃒",nLtv:"≪̸",nRightarrow:"⇏",nVDash:"⊯",nVdash:"⊮",nabla:"∇",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbs:" ",nbsp:" ",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",ncaron:"ň",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",ncy:"н",ndash:"–",ne:"≠",neArr:"⇗",nearhk:"⤤",nearr:"↗",nearrow:"↗",nedot:"≐̸",nequiv:"≢",nesear:"⤨",nesim:"≂̸",nexist:"∄",nexists:"∄",nfr:"𝔫",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",ngsim:"≵",ngt:"≯",ngtr:"≯",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",njcy:"њ",nlArr:"⇍",nlE:"≦̸",nlarr:"↚",nldr:"‥",nle:"≰",nleftarrow:"↚",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nlsim:"≴",nlt:"≮",nltri:"⋪",nltrie:"⋬",nmid:"∤",nopf:"𝕟",no:"¬",not:"¬",notin:"∉",notinE:"⋹̸",notindot:"⋵̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",nscr:"𝓃",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",ntild:"ñ",ntilde:"ñ",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",nu:"ν",num:"#",numero:"№",numsp:" ",nvDash:"⊭",nvHarr:"⤄",nvap:"≍⃒",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwArr:"⇖",nwarhk:"⤣",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",oS:"Ⓢ",oacut:"ó",oacute:"ó",oast:"⊛",ocir:"ô",ocirc:"ô",ocy:"о",odash:"⊝",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",oelig:"œ",ofcir:"⦿",ofr:"𝔬",ogon:"˛",ograv:"ò",ograve:"ò",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",omacr:"ō",omega:"ω",omicron:"ο",omid:"⦶",ominus:"⊖",oopf:"𝕠",opar:"⦷",operp:"⦹",oplus:"⊕",or:"∨",orarr:"↻",ord:"º",order:"ℴ",orderof:"ℴ",ordf:"ª",ordm:"º",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oscr:"ℴ",oslas:"ø",oslash:"ø",osol:"⊘",otild:"õ",otilde:"õ",otimes:"⊗",otimesas:"⨶",oum:"ö",ouml:"ö",ovbar:"⌽",par:"¶",para:"¶",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",pfr:"𝔭",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",plusm:"±",plusmn:"±",plussim:"⨦",plustwo:"⨧",pm:"±",pointint:"⨕",popf:"𝕡",poun:"£",pound:"£",pr:"≺",prE:"⪳",prap:"⪷",prcue:"≼",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",prime:"′",primes:"ℙ",prnE:"⪵",prnap:"⪹",prnsim:"⋨",prod:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",propto:"∝",prsim:"≾",prurel:"⊰",pscr:"𝓅",psi:"ψ",puncsp:" ",qfr:"𝔮",qint:"⨌",qopf:"𝕢",qprime:"⁗",qscr:"𝓆",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",quo:'"',quot:'"',rAarr:"⇛",rArr:"⇒",rAtail:"⤜",rBarr:"⤏",rHar:"⥤",race:"∽̱",racute:"ŕ",radic:"√",raemptyv:"⦳",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raqu:"»",raquo:"»",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",rarrtl:"↣",rarrw:"↝",ratail:"⤚",ratio:"∶",rationals:"ℚ",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",rcaron:"ř",rcedil:"ŗ",rceil:"⌉",rcub:"}",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",re:"®",reg:"®",rfisht:"⥽",rfloor:"⌋",rfr:"𝔯",rhard:"⇁",rharu:"⇀",rharul:"⥬",rho:"ρ",rhov:"ϱ",rightarrow:"→",rightarrowtail:"↣",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",rightthreetimes:"⋌",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",ropf:"𝕣",roplus:"⨮",rotimes:"⨵",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",rsaquo:"›",rscr:"𝓇",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",ruluhar:"⥨",rx:"℞",sacute:"ś",sbquo:"‚",sc:"≻",scE:"⪴",scap:"⪸",scaron:"š",sccue:"≽",sce:"⪰",scedil:"ş",scirc:"ŝ",scnE:"⪶",scnap:"⪺",scnsim:"⋩",scpolint:"⨓",scsim:"≿",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",seArr:"⇘",searhk:"⤥",searr:"↘",searrow:"↘",sec:"§",sect:"§",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",sfr:"𝔰",sfrown:"⌢",sharp:"♯",shchcy:"щ",shcy:"ш",shortmid:"∣",shortparallel:"∥",sh:"­",shy:"­",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",sopf:"𝕤",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",square:"□",squarf:"▪",squf:"▪",srarr:"→",sscr:"𝓈",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"¯",sub:"⊂",subE:"⫅",subdot:"⪽",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",sum:"∑",sung:"♪",sup:"⊃",sup1:"¹",sup2:"²",sup3:"³",supE:"⫆",supdot:"⪾",supdsub:"⫘",supe:"⊇",supedot:"⫄",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swArr:"⇙",swarhk:"⤦",swarr:"↙",swarrow:"↙",swnwar:"⤪",szli:"ß",szlig:"ß",target:"⌖",tau:"τ",tbrk:"⎴",tcaron:"ť",tcedil:"ţ",tcy:"т",tdot:"⃛",telrec:"⌕",tfr:"𝔱",there4:"∴",therefore:"∴",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",thinsp:" ",thkap:"≈",thksim:"∼",thor:"þ",thorn:"þ",tilde:"˜",time:"×",times:"×",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",topf:"𝕥",topfork:"⫚",tosa:"⤩",tprime:"‴",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",tscr:"𝓉",tscy:"ц",tshcy:"ћ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",uArr:"⇑",uHar:"⥣",uacut:"ú",uacute:"ú",uarr:"↑",ubrcy:"ў",ubreve:"ŭ",ucir:"û",ucirc:"û",ucy:"у",udarr:"⇅",udblac:"ű",udhar:"⥮",ufisht:"⥾",ufr:"𝔲",ugrav:"ù",ugrave:"ù",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",umacr:"ū",um:"¨",uml:"¨",uogon:"ų",uopf:"𝕦",uparrow:"↑",updownarrow:"↕",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",upsi:"υ",upsih:"ϒ",upsilon:"υ",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",uring:"ů",urtri:"◹",uscr:"𝓊",utdot:"⋰",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",uum:"ü",uuml:"ü",uwangle:"⦧",vArr:"⇕",vBar:"⫨",vBarv:"⫩",vDash:"⊨",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",vcy:"в",vdash:"⊢",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",verbar:"|",vert:"|",vfr:"𝔳",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",vopf:"𝕧",vprop:"∝",vrtri:"⊳",vscr:"𝓋",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",vzigzag:"⦚",wcirc:"ŵ",wedbar:"⩟",wedge:"∧",wedgeq:"≙",weierp:"℘",wfr:"𝔴",wopf:"𝕨",wp:"℘",wr:"≀",wreath:"≀",wscr:"𝓌",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",xfr:"𝔵",xhArr:"⟺",xharr:"⟷",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",xopf:"𝕩",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",xscr:"𝓍",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",yacut:"ý",yacute:"ý",yacy:"я",ycirc:"ŷ",ycy:"ы",ye:"¥",yen:"¥",yfr:"𝔶",yicy:"ї",yopf:"𝕪",yscr:"𝓎",yucy:"ю",yum:"ÿ",yuml:"ÿ",zacute:"ź",zcaron:"ž",zcy:"з",zdot:"ż",zeetrf:"ℨ",zeta:"ζ",zfr:"𝔷",zhcy:"ж",zigrarr:"⇝",zopf:"𝕫",zscr:"𝓏",zwj:"‍",zwnj:"‌"},_=Object.freeze({__proto__:null,AEli:"Æ",AElig:"Æ",AM:"&",AMP:"&",Aacut:"Á",Aacute:"Á",Abreve:"Ă",Acir:"Â",Acirc:"Â",Acy:"А",Afr:"𝔄",Agrav:"À",Agrave:"À",Alpha:"Α",Amacr:"Ā",And:"⩓",Aogon:"Ą",Aopf:"𝔸",ApplyFunction:"⁡",Arin:"Å",Aring:"Å",Ascr:"𝒜",Assign:"≔",Atild:"Ã",Atilde:"Ã",Aum:"Ä",Auml:"Ä",Backslash:"∖",Barv:"⫧",Barwed:"⌆",Bcy:"Б",Because:"∵",Bernoullis:"ℬ",Beta:"Β",Bfr:"𝔅",Bopf:"𝔹",Breve:"˘",Bscr:"ℬ",Bumpeq:"≎",CHcy:"Ч",COP:"©",COPY:"©",Cacute:"Ć",Cap:"⋒",CapitalDifferentialD:"ⅅ",Cayleys:"ℭ",Ccaron:"Č",Ccedi:"Ç",Ccedil:"Ç",Ccirc:"Ĉ",Cconint:"∰",Cdot:"Ċ",Cedilla:"¸",CenterDot:"·",Cfr:"ℭ",Chi:"Χ",CircleDot:"⊙",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",Colon:"∷",Colone:"⩴",Congruent:"≡",Conint:"∯",ContourIntegral:"∮",Copf:"ℂ",Coproduct:"∐",CounterClockwiseContourIntegral:"∳",Cross:"⨯",Cscr:"𝒞",Cup:"⋓",CupCap:"≍",DD:"ⅅ",DDotrahd:"⤑",DJcy:"Ђ",DScy:"Ѕ",DZcy:"Џ",Dagger:"‡",Darr:"↡",Dashv:"⫤",Dcaron:"Ď",Dcy:"Д",Del:"∇",Delta:"Δ",Dfr:"𝔇",DiacriticalAcute:"´",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",Diamond:"⋄",DifferentialD:"ⅆ",Dopf:"𝔻",Dot:"¨",DotDot:"⃜",DotEqual:"≐",DoubleContourIntegral:"∯",DoubleDot:"¨",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",Downarrow:"⇓",Dscr:"𝒟",Dstrok:"Đ",ENG:"Ŋ",ET:"Ð",ETH:"Ð",Eacut:"É",Eacute:"É",Ecaron:"Ě",Ecir:"Ê",Ecirc:"Ê",Ecy:"Э",Edot:"Ė",Efr:"𝔈",Egrav:"È",Egrave:"È",Element:"∈",Emacr:"Ē",EmptySmallSquare:"◻",EmptyVerySmallSquare:"▫",Eogon:"Ę",Eopf:"𝔼",Epsilon:"Ε",Equal:"⩵",EqualTilde:"≂",Equilibrium:"⇌",Escr:"ℰ",Esim:"⩳",Eta:"Η",Eum:"Ë",Euml:"Ë",Exists:"∃",ExponentialE:"ⅇ",Fcy:"Ф",Ffr:"𝔉",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",Fopf:"𝔽",ForAll:"∀",Fouriertrf:"ℱ",Fscr:"ℱ",GJcy:"Ѓ",G:">",GT:">",Gamma:"Γ",Gammad:"Ϝ",Gbreve:"Ğ",Gcedil:"Ģ",Gcirc:"Ĝ",Gcy:"Г",Gdot:"Ġ",Gfr:"𝔊",Gg:"⋙",Gopf:"𝔾",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"𝒢",Gt:"≫",HARDcy:"Ъ",Hacek:"ˇ",Hat:"^",Hcirc:"Ĥ",Hfr:"ℌ",HilbertSpace:"ℋ",Hopf:"ℍ",HorizontalLine:"─",Hscr:"ℋ",Hstrok:"Ħ",HumpDownHump:"≎",HumpEqual:"≏",IEcy:"Е",IJlig:"Ĳ",IOcy:"Ё",Iacut:"Í",Iacute:"Í",Icir:"Î",Icirc:"Î",Icy:"И",Idot:"İ",Ifr:"ℑ",Igrav:"Ì",Igrave:"Ì",Im:"ℑ",Imacr:"Ī",ImaginaryI:"ⅈ",Implies:"⇒",Int:"∬",Integral:"∫",Intersection:"⋂",InvisibleComma:"⁣",InvisibleTimes:"⁢",Iogon:"Į",Iopf:"𝕀",Iota:"Ι",Iscr:"ℐ",Itilde:"Ĩ",Iukcy:"І",Ium:"Ï",Iuml:"Ï",Jcirc:"Ĵ",Jcy:"Й",Jfr:"𝔍",Jopf:"𝕁",Jscr:"𝒥",Jsercy:"Ј",Jukcy:"Є",KHcy:"Х",KJcy:"Ќ",Kappa:"Κ",Kcedil:"Ķ",Kcy:"К",Kfr:"𝔎",Kopf:"𝕂",Kscr:"𝒦",LJcy:"Љ",L:"<",LT:"<",Lacute:"Ĺ",Lambda:"Λ",Lang:"⟪",Laplacetrf:"ℒ",Larr:"↞",Lcaron:"Ľ",Lcedil:"Ļ",Lcy:"Л",LeftAngleBracket:"⟨",LeftArrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",LeftRightArrow:"↔",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",Leftarrow:"⇐",Leftrightarrow:"⇔",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",LessLess:"⪡",LessSlantEqual:"⩽",LessTilde:"≲",Lfr:"𝔏",Ll:"⋘",Lleftarrow:"⇚",Lmidot:"Ŀ",LongLeftArrow:"⟵",LongLeftRightArrow:"⟷",LongRightArrow:"⟶",Longleftarrow:"⟸",Longleftrightarrow:"⟺",Longrightarrow:"⟹",Lopf:"𝕃",LowerLeftArrow:"↙",LowerRightArrow:"↘",Lscr:"ℒ",Lsh:"↰",Lstrok:"Ł",Lt:"≪",Mcy:"М",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"𝔐",MinusPlus:"∓",Mopf:"𝕄",Mscr:"ℳ",Mu:"Μ",NJcy:"Њ",Nacute:"Ń",Ncaron:"Ň",Ncedil:"Ņ",Ncy:"Н",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",Nfr:"𝔑",NoBreak:"⁠",NonBreakingSpace:" ",Nopf:"ℕ",Not:"⫬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",Nscr:"𝒩",Ntild:"Ñ",Ntilde:"Ñ",Nu:"Ν",OElig:"Œ",Oacut:"Ó",Oacute:"Ó",Ocir:"Ô",Ocirc:"Ô",Ocy:"О",Odblac:"Ő",Ofr:"𝔒",Ograv:"Ò",Ograve:"Ò",Omacr:"Ō",Omega:"Ω",Omicron:"Ο",Oopf:"𝕆",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",Or:"⩔",Oscr:"𝒪",Oslas:"Ø",Oslash:"Ø",Otild:"Õ",Otilde:"Õ",Otimes:"⨷",Oum:"Ö",Ouml:"Ö",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",PartialD:"∂",Pcy:"П",Pfr:"𝔓",Phi:"Φ",Pi:"Π",PlusMinus:"±",Poincareplane:"ℌ",Popf:"ℙ",Pr:"⪻",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",Prime:"″",Product:"∏",Proportion:"∷",Proportional:"∝",Pscr:"𝒫",Psi:"Ψ",QUO:'"',QUOT:'"',Qfr:"𝔔",Qopf:"ℚ",Qscr:"𝒬",RBarr:"⤐",RE:"®",REG:"®",Racute:"Ŕ",Rang:"⟫",Rarr:"↠",Rarrtl:"⤖",Rcaron:"Ř",Rcedil:"Ŗ",Rcy:"Р",Re:"ℜ",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",Rfr:"ℜ",Rho:"Ρ",RightAngleBracket:"⟩",RightArrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",Rightarrow:"⇒",Ropf:"ℝ",RoundImplies:"⥰",Rrightarrow:"⇛",Rscr:"ℛ",Rsh:"↱",RuleDelayed:"⧴",SHCHcy:"Щ",SHcy:"Ш",SOFTcy:"Ь",Sacute:"Ś",Sc:"⪼",Scaron:"Š",Scedil:"Ş",Scirc:"Ŝ",Scy:"С",Sfr:"𝔖",ShortDownArrow:"↓",ShortLeftArrow:"←",ShortRightArrow:"→",ShortUpArrow:"↑",Sigma:"Σ",SmallCircle:"∘",Sopf:"𝕊",Sqrt:"√",Square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",Sscr:"𝒮",Star:"⋆",Sub:"⋐",Subset:"⋐",SubsetEqual:"⊆",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",SuchThat:"∋",Sum:"∑",Sup:"⋑",Superset:"⊃",SupersetEqual:"⊇",Supset:"⋑",THOR:"Þ",THORN:"Þ",TRADE:"™",TSHcy:"Ћ",TScy:"Ц",Tab:"\t",Tau:"Τ",Tcaron:"Ť",Tcedil:"Ţ",Tcy:"Т",Tfr:"𝔗",Therefore:"∴",Theta:"Θ",ThickSpace:"  ",ThinSpace:" ",Tilde:"∼",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",Topf:"𝕋",TripleDot:"⃛",Tscr:"𝒯",Tstrok:"Ŧ",Uacut:"Ú",Uacute:"Ú",Uarr:"↟",Uarrocir:"⥉",Ubrcy:"Ў",Ubreve:"Ŭ",Ucir:"Û",Ucirc:"Û",Ucy:"У",Udblac:"Ű",Ufr:"𝔘",Ugrav:"Ù",Ugrave:"Ù",Umacr:"Ū",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",Uopf:"𝕌",UpArrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",UpEquilibrium:"⥮",UpTee:"⊥",UpTeeArrow:"↥",Uparrow:"⇑",Updownarrow:"⇕",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",Upsilon:"Υ",Uring:"Ů",Uscr:"𝒰",Utilde:"Ũ",Uum:"Ü",Uuml:"Ü",VDash:"⊫",Vbar:"⫫",Vcy:"В",Vdash:"⊩",Vdashl:"⫦",Vee:"⋁",Verbar:"‖",Vert:"‖",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"𝔙",Vopf:"𝕍",Vscr:"𝒱",Vvdash:"⊪",Wcirc:"Ŵ",Wedge:"⋀",Wfr:"𝔚",Wopf:"𝕎",Wscr:"𝒲",Xfr:"𝔛",Xi:"Ξ",Xopf:"𝕏",Xscr:"𝒳",YAcy:"Я",YIcy:"Ї",YUcy:"Ю",Yacut:"Ý",Yacute:"Ý",Ycirc:"Ŷ",Ycy:"Ы",Yfr:"𝔜",Yopf:"𝕐",Yscr:"𝒴",Yuml:"Ÿ",ZHcy:"Ж",Zacute:"Ź",Zcaron:"Ž",Zcy:"З",Zdot:"Ż",ZeroWidthSpace:"​",Zeta:"Ζ",Zfr:"ℨ",Zopf:"ℤ",Zscr:"𝒵",aacut:"á",aacute:"á",abreve:"ă",ac:"∾",acE:"∾̳",acd:"∿",acir:"â",acirc:"â",acut:"´",acute:"´",acy:"а",aeli:"æ",aelig:"æ",af:"⁡",afr:"𝔞",agrav:"à",agrave:"à",alefsym:"ℵ",aleph:"ℵ",alpha:"α",amacr:"ā",amalg:"⨿",am:"&",amp:"&",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"Å",angzarr:"⍼",aogon:"ą",aopf:"𝕒",ap:"≈",apE:"⩰",apacir:"⩯",ape:"≊",apid:"≋",apos:"'",approx:"≈",approxeq:"≊",arin:"å",aring:"å",ascr:"𝒶",ast:"*",asymp:"≈",asympeq:"≍",atild:"ã",atilde:"ã",aum:"ä",auml:"ä",awconint:"∳",awint:"⨑",bNot:"⫭",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",barvee:"⊽",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",bcy:"б",bdquo:"„",becaus:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",beta:"β",beth:"ℶ",between:"≬",bfr:"𝔟",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bnot:"⌐",bopf:"𝕓",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxDL:"╗",boxDR:"╔",boxDl:"╖",boxDr:"╓",boxH:"═",boxHD:"╦",boxHU:"╩",boxHd:"╤",boxHu:"╧",boxUL:"╝",boxUR:"╚",boxUl:"╜",boxUr:"╙",boxV:"║",boxVH:"╬",boxVL:"╣",boxVR:"╠",boxVh:"╫",boxVl:"╢",boxVr:"╟",boxbox:"⧉",boxdL:"╕",boxdR:"╒",boxdl:"┐",boxdr:"┌",boxh:"─",boxhD:"╥",boxhU:"╨",boxhd:"┬",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxuL:"╛",boxuR:"╘",boxul:"┘",boxur:"└",boxv:"│",boxvH:"╪",boxvL:"╡",boxvR:"╞",boxvh:"┼",boxvl:"┤",boxvr:"├",bprime:"‵",breve:"˘",brvba:"¦",brvbar:"¦",bscr:"𝒷",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",bumpeq:"≏",cacute:"ć",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",caps:"∩︀",caret:"⁁",caron:"ˇ",ccaps:"⩍",ccaron:"č",ccedi:"ç",ccedil:"ç",ccirc:"ĉ",ccups:"⩌",ccupssm:"⩐",cdot:"ċ",cedi:"¸",cedil:"¸",cemptyv:"⦲",cen:"¢",cent:"¢",centerdot:"·",cfr:"𝔠",chcy:"ч",check:"✓",checkmark:"✓",chi:"χ",cir:"○",cirE:"⧃",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledR:"®",circledS:"Ⓢ",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",clubs:"♣",clubsuit:"♣",colon:":",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",conint:"∮",copf:"𝕔",coprod:"∐",cop:"©",copy:"©",copysr:"℗",crarr:"↵",cross:"✗",cscr:"𝒸",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",cup:"∪",cupbrcap:"⩈",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curre:"¤",curren:"¤",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",dArr:"⇓",dHar:"⥥",dagger:"†",daleth:"ℸ",darr:"↓",dash:"‐",dashv:"⊣",dbkarow:"⤏",dblac:"˝",dcaron:"ď",dcy:"д",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",ddotseq:"⩷",de:"°",deg:"°",delta:"δ",demptyv:"⦱",dfisht:"⥿",dfr:"𝔡",dharl:"⇃",dharr:"⇂",diam:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"¨",digamma:"ϝ",disin:"⋲",div:"÷",divid:"÷",divide:"÷",divideontimes:"⋇",divonx:"⋇",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",dopf:"𝕕",dot:"˙",doteq:"≐",doteqdot:"≑",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",downarrow:"↓",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",dscr:"𝒹",dscy:"ѕ",dsol:"⧶",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",dzcy:"џ",dzigrarr:"⟿",eDDot:"⩷",eDot:"≑",eacut:"é",eacute:"é",easter:"⩮",ecaron:"ě",ecir:"ê",ecirc:"ê",ecolon:"≕",ecy:"э",edot:"ė",ee:"ⅇ",efDot:"≒",efr:"𝔢",eg:"⪚",egrav:"è",egrave:"è",egs:"⪖",egsdot:"⪘",el:"⪙",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",emacr:"ē",empty:"∅",emptyset:"∅",emptyv:"∅",emsp13:" ",emsp14:" ",emsp:" ",eng:"ŋ",ensp:" ",eogon:"ę",eopf:"𝕖",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",equals:"=",equest:"≟",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erDot:"≓",erarr:"⥱",escr:"ℯ",esdot:"≐",esim:"≂",eta:"η",et:"ð",eth:"ð",eum:"ë",euml:"ë",euro:"€",excl:"!",exist:"∃",expectation:"ℰ",exponentiale:"ⅇ",fallingdotseq:"≒",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",ffr:"𝔣",filig:"ﬁ",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",fopf:"𝕗",forall:"∀",fork:"⋔",forkv:"⫙",fpartint:"⨍",frac1:"¼",frac12:"½",frac13:"⅓",frac14:"¼",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac3:"¾",frac34:"¾",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",fscr:"𝒻",gE:"≧",gEl:"⪌",gacute:"ǵ",gamma:"γ",gammad:"ϝ",gap:"⪆",gbreve:"ğ",gcirc:"ĝ",gcy:"г",gdot:"ġ",ge:"≥",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",gfr:"𝔤",gg:"≫",ggg:"⋙",gimel:"ℷ",gjcy:"ѓ",gl:"≷",glE:"⪒",gla:"⪥",glj:"⪤",gnE:"≩",gnap:"⪊",gnapprox:"⪊",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",gopf:"𝕘",grave:"`",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",g:">",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",hArr:"⇔",hairsp:" ",half:"½",hamilt:"ℋ",hardcy:"ъ",harr:"↔",harrcir:"⥈",harrw:"↭",hbar:"ℏ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",hfr:"𝔥",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",hopf:"𝕙",horbar:"―",hscr:"𝒽",hslash:"ℏ",hstrok:"ħ",hybull:"⁃",hyphen:"‐",iacut:"í",iacute:"í",ic:"⁣",icir:"î",icirc:"î",icy:"и",iecy:"е",iexc:"¡",iexcl:"¡",iff:"⇔",ifr:"𝔦",igrav:"ì",igrave:"ì",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",ijlig:"ĳ",imacr:"ī",image:"ℑ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",int:"∫",intcal:"⊺",integers:"ℤ",intercal:"⊺",intlarhk:"⨗",intprod:"⨼",iocy:"ё",iogon:"į",iopf:"𝕚",iota:"ι",iprod:"⨼",iques:"¿",iquest:"¿",iscr:"𝒾",isin:"∈",isinE:"⋹",isindot:"⋵",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",itilde:"ĩ",iukcy:"і",ium:"ï",iuml:"ï",jcirc:"ĵ",jcy:"й",jfr:"𝔧",jmath:"ȷ",jopf:"𝕛",jscr:"𝒿",jsercy:"ј",jukcy:"є",kappa:"κ",kappav:"ϰ",kcedil:"ķ",kcy:"к",kfr:"𝔨",kgreen:"ĸ",khcy:"х",kjcy:"ќ",kopf:"𝕜",kscr:"𝓀",lAarr:"⇚",lArr:"⇐",lAtail:"⤛",lBarr:"⤎",lE:"≦",lEg:"⪋",lHar:"⥢",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",lambda:"λ",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",laqu:"«",laquo:"«",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",latail:"⤙",late:"⪭",lates:"⪭︀",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",lcaron:"ľ",lcedil:"ļ",lceil:"⌈",lcub:"{",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",le:"≤",leftarrow:"←",leftarrowtail:"↢",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",leftthreetimes:"⋋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",lessgtr:"≶",lesssim:"≲",lfisht:"⥼",lfloor:"⌊",lfr:"𝔩",lg:"≶",lgE:"⪑",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",ljcy:"љ",ll:"≪",llarr:"⇇",llcorner:"⌞",llhard:"⥫",lltri:"◺",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnE:"≨",lnap:"⪉",lnapprox:"⪉",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",longleftarrow:"⟵",longleftrightarrow:"⟷",longmapsto:"⟼",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",lopf:"𝕝",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",lscr:"𝓁",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",lstrok:"ł",l:"<",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltrPar:"⦖",ltri:"◃",ltrie:"⊴",ltrif:"◂",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",mDDot:"∺",mac:"¯",macr:"¯",male:"♂",malt:"✠",maltese:"✠",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",mcy:"м",mdash:"—",measuredangle:"∡",mfr:"𝔪",mho:"℧",micr:"µ",micro:"µ",mid:"∣",midast:"*",midcir:"⫰",middo:"·",middot:"·",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",mopf:"𝕞",mp:"∓",mscr:"𝓂",mstpos:"∾",mu:"μ",multimap:"⊸",mumap:"⊸",nGg:"⋙̸",nGt:"≫⃒",nGtv:"≫̸",nLeftarrow:"⇍",nLeftrightarrow:"⇎",nLl:"⋘̸",nLt:"≪⃒",nLtv:"≪̸",nRightarrow:"⇏",nVDash:"⊯",nVdash:"⊮",nabla:"∇",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbs:" ",nbsp:" ",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",ncaron:"ň",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",ncy:"н",ndash:"–",ne:"≠",neArr:"⇗",nearhk:"⤤",nearr:"↗",nearrow:"↗",nedot:"≐̸",nequiv:"≢",nesear:"⤨",nesim:"≂̸",nexist:"∄",nexists:"∄",nfr:"𝔫",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",ngsim:"≵",ngt:"≯",ngtr:"≯",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",njcy:"њ",nlArr:"⇍",nlE:"≦̸",nlarr:"↚",nldr:"‥",nle:"≰",nleftarrow:"↚",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nlsim:"≴",nlt:"≮",nltri:"⋪",nltrie:"⋬",nmid:"∤",nopf:"𝕟",no:"¬",not:"¬",notin:"∉",notinE:"⋹̸",notindot:"⋵̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",nscr:"𝓃",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",ntild:"ñ",ntilde:"ñ",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",nu:"ν",num:"#",numero:"№",numsp:" ",nvDash:"⊭",nvHarr:"⤄",nvap:"≍⃒",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwArr:"⇖",nwarhk:"⤣",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",oS:"Ⓢ",oacut:"ó",oacute:"ó",oast:"⊛",ocir:"ô",ocirc:"ô",ocy:"о",odash:"⊝",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",oelig:"œ",ofcir:"⦿",ofr:"𝔬",ogon:"˛",ograv:"ò",ograve:"ò",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",omacr:"ō",omega:"ω",omicron:"ο",omid:"⦶",ominus:"⊖",oopf:"𝕠",opar:"⦷",operp:"⦹",oplus:"⊕",or:"∨",orarr:"↻",ord:"º",order:"ℴ",orderof:"ℴ",ordf:"ª",ordm:"º",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oscr:"ℴ",oslas:"ø",oslash:"ø",osol:"⊘",otild:"õ",otilde:"õ",otimes:"⊗",otimesas:"⨶",oum:"ö",ouml:"ö",ovbar:"⌽",par:"¶",para:"¶",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",pfr:"𝔭",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",plusm:"±",plusmn:"±",plussim:"⨦",plustwo:"⨧",pm:"±",pointint:"⨕",popf:"𝕡",poun:"£",pound:"£",pr:"≺",prE:"⪳",prap:"⪷",prcue:"≼",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",prime:"′",primes:"ℙ",prnE:"⪵",prnap:"⪹",prnsim:"⋨",prod:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",propto:"∝",prsim:"≾",prurel:"⊰",pscr:"𝓅",psi:"ψ",puncsp:" ",qfr:"𝔮",qint:"⨌",qopf:"𝕢",qprime:"⁗",qscr:"𝓆",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",quo:'"',quot:'"',rAarr:"⇛",rArr:"⇒",rAtail:"⤜",rBarr:"⤏",rHar:"⥤",race:"∽̱",racute:"ŕ",radic:"√",raemptyv:"⦳",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raqu:"»",raquo:"»",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",rarrtl:"↣",rarrw:"↝",ratail:"⤚",ratio:"∶",rationals:"ℚ",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",rcaron:"ř",rcedil:"ŗ",rceil:"⌉",rcub:"}",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",re:"®",reg:"®",rfisht:"⥽",rfloor:"⌋",rfr:"𝔯",rhard:"⇁",rharu:"⇀",rharul:"⥬",rho:"ρ",rhov:"ϱ",rightarrow:"→",rightarrowtail:"↣",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",rightthreetimes:"⋌",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",ropf:"𝕣",roplus:"⨮",rotimes:"⨵",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",rsaquo:"›",rscr:"𝓇",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",ruluhar:"⥨",rx:"℞",sacute:"ś",sbquo:"‚",sc:"≻",scE:"⪴",scap:"⪸",scaron:"š",sccue:"≽",sce:"⪰",scedil:"ş",scirc:"ŝ",scnE:"⪶",scnap:"⪺",scnsim:"⋩",scpolint:"⨓",scsim:"≿",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",seArr:"⇘",searhk:"⤥",searr:"↘",searrow:"↘",sec:"§",sect:"§",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",sfr:"𝔰",sfrown:"⌢",sharp:"♯",shchcy:"щ",shcy:"ш",shortmid:"∣",shortparallel:"∥",sh:"­",shy:"­",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",sopf:"𝕤",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",square:"□",squarf:"▪",squf:"▪",srarr:"→",sscr:"𝓈",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"¯",sub:"⊂",subE:"⫅",subdot:"⪽",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",sum:"∑",sung:"♪",sup:"⊃",sup1:"¹",sup2:"²",sup3:"³",supE:"⫆",supdot:"⪾",supdsub:"⫘",supe:"⊇",supedot:"⫄",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swArr:"⇙",swarhk:"⤦",swarr:"↙",swarrow:"↙",swnwar:"⤪",szli:"ß",szlig:"ß",target:"⌖",tau:"τ",tbrk:"⎴",tcaron:"ť",tcedil:"ţ",tcy:"т",tdot:"⃛",telrec:"⌕",tfr:"𝔱",there4:"∴",therefore:"∴",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",thinsp:" ",thkap:"≈",thksim:"∼",thor:"þ",thorn:"þ",tilde:"˜",time:"×",times:"×",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",topf:"𝕥",topfork:"⫚",tosa:"⤩",tprime:"‴",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",tscr:"𝓉",tscy:"ц",tshcy:"ћ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",uArr:"⇑",uHar:"⥣",uacut:"ú",uacute:"ú",uarr:"↑",ubrcy:"ў",ubreve:"ŭ",ucir:"û",ucirc:"û",ucy:"у",udarr:"⇅",udblac:"ű",udhar:"⥮",ufisht:"⥾",ufr:"𝔲",ugrav:"ù",ugrave:"ù",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",umacr:"ū",um:"¨",uml:"¨",uogon:"ų",uopf:"𝕦",uparrow:"↑",updownarrow:"↕",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",upsi:"υ",upsih:"ϒ",upsilon:"υ",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",uring:"ů",urtri:"◹",uscr:"𝓊",utdot:"⋰",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",uum:"ü",uuml:"ü",uwangle:"⦧",vArr:"⇕",vBar:"⫨",vBarv:"⫩",vDash:"⊨",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",vcy:"в",vdash:"⊢",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",verbar:"|",vert:"|",vfr:"𝔳",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",vopf:"𝕧",vprop:"∝",vrtri:"⊳",vscr:"𝓋",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",vzigzag:"⦚",wcirc:"ŵ",wedbar:"⩟",wedge:"∧",wedgeq:"≙",weierp:"℘",wfr:"𝔴",wopf:"𝕨",wp:"℘",wr:"≀",wreath:"≀",wscr:"𝓌",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",xfr:"𝔵",xhArr:"⟺",xharr:"⟷",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",xopf:"𝕩",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",xscr:"𝓍",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",yacut:"ý",yacute:"ý",yacy:"я",ycirc:"ŷ",ycy:"ы",ye:"¥",yen:"¥",yfr:"𝔶",yicy:"ї",yopf:"𝕪",yscr:"𝓎",yucy:"ю",yum:"ÿ",yuml:"ÿ",zacute:"ź",zcaron:"ž",zcy:"з",zdot:"ż",zeetrf:"ℨ",zeta:"ζ",zfr:"𝔷",zhcy:"ж",zigrarr:"⇝",zopf:"𝕫",zscr:"𝓏",zwj:"‍",zwnj:"‌",default:T}),S={AElig:"Æ",AMP:"&",Aacute:"Á",Acirc:"Â",Agrave:"À",Aring:"Å",Atilde:"Ã",Auml:"Ä",COPY:"©",Ccedil:"Ç",ETH:"Ð",Eacute:"É",Ecirc:"Ê",Egrave:"È",Euml:"Ë",GT:">",Iacute:"Í",Icirc:"Î",Igrave:"Ì",Iuml:"Ï",LT:"<",Ntilde:"Ñ",Oacute:"Ó",Ocirc:"Ô",Ograve:"Ò",Oslash:"Ø",Otilde:"Õ",Ouml:"Ö",QUOT:'"',REG:"®",THORN:"Þ",Uacute:"Ú",Ucirc:"Û",Ugrave:"Ù",Uuml:"Ü",Yacute:"Ý",aacute:"á",acirc:"â",acute:"´",aelig:"æ",agrave:"à",amp:"&",aring:"å",atilde:"ã",auml:"ä",brvbar:"¦",ccedil:"ç",cedil:"¸",cent:"¢",copy:"©",curren:"¤",deg:"°",divide:"÷",eacute:"é",ecirc:"ê",egrave:"è",eth:"ð",euml:"ë",frac12:"½",frac14:"¼",frac34:"¾",gt:">",iacute:"í",icirc:"î",iexcl:"¡",igrave:"ì",iquest:"¿",iuml:"ï",laquo:"«",lt:"<",macr:"¯",micro:"µ",middot:"·",nbsp:" ",not:"¬",ntilde:"ñ",oacute:"ó",ocirc:"ô",ograve:"ò",ordf:"ª",ordm:"º",oslash:"ø",otilde:"õ",ouml:"ö",para:"¶",plusmn:"±",pound:"£",quot:'"',raquo:"»",reg:"®",sect:"§",shy:"­",sup1:"¹",sup2:"²",sup3:"³",szlig:"ß",thorn:"þ",times:"×",uacute:"ú",ucirc:"û",ugrave:"ù",uml:"¨",uuml:"ü",yacute:"ý",yen:"¥",yuml:"ÿ"},F=Object.freeze({__proto__:null,AElig:"Æ",AMP:"&",Aacute:"Á",Acirc:"Â",Agrave:"À",Aring:"Å",Atilde:"Ã",Auml:"Ä",COPY:"©",Ccedil:"Ç",ETH:"Ð",Eacute:"É",Ecirc:"Ê",Egrave:"È",Euml:"Ë",GT:">",Iacute:"Í",Icirc:"Î",Igrave:"Ì",Iuml:"Ï",LT:"<",Ntilde:"Ñ",Oacute:"Ó",Ocirc:"Ô",Ograve:"Ò",Oslash:"Ø",Otilde:"Õ",Ouml:"Ö",QUOT:'"',REG:"®",THORN:"Þ",Uacute:"Ú",Ucirc:"Û",Ugrave:"Ù",Uuml:"Ü",Yacute:"Ý",aacute:"á",acirc:"â",acute:"´",aelig:"æ",agrave:"à",amp:"&",aring:"å",atilde:"ã",auml:"ä",brvbar:"¦",ccedil:"ç",cedil:"¸",cent:"¢",copy:"©",curren:"¤",deg:"°",divide:"÷",eacute:"é",ecirc:"ê",egrave:"è",eth:"ð",euml:"ë",frac12:"½",frac14:"¼",frac34:"¾",gt:">",iacute:"í",icirc:"î",iexcl:"¡",igrave:"ì",iquest:"¿",iuml:"ï",laquo:"«",lt:"<",macr:"¯",micro:"µ",middot:"·",nbsp:" ",not:"¬",ntilde:"ñ",oacute:"ó",ocirc:"ô",ograve:"ò",ordf:"ª",ordm:"º",oslash:"ø",otilde:"õ",ouml:"ö",para:"¶",plusmn:"±",pound:"£",quot:'"',raquo:"»",reg:"®",sect:"§",shy:"­",sup1:"¹",sup2:"²",sup3:"³",szlig:"ß",thorn:"þ",times:"×",uacute:"ú",ucirc:"û",ugrave:"ù",uml:"¨",uuml:"ü",yacute:"ý",yen:"¥",yuml:"ÿ",default:S}),x=Object.freeze({__proto__:null,default:{0:"�",128:"€",130:"‚",131:"ƒ",132:"„",133:"…",134:"†",135:"‡",136:"ˆ",137:"‰",138:"Š",139:"‹",140:"Œ",142:"Ž",145:"‘",146:"’",147:"“",148:"”",149:"•",150:"–",151:"—",152:"˜",153:"™",154:"š",155:"›",156:"œ",158:"ž",159:"Ÿ"}}),q=function(e){var t="string"==typeof e?e.charCodeAt(0):e;return t>=48&&t<=57};var N=function(e){var t="string"==typeof e?e.charCodeAt(0):e;return t>=97&&t<=102||t>=65&&t<=70||t>=48&&t<=57};var L=function(e){var t="string"==typeof e?e.charCodeAt(0):e;return t>=97&&t<=122||t>=65&&t<=90};var B=function(e){return L(e)||q(e)};var P=D(_),O=D(F),R=D(x),I=function(e,t){var r,n,i={};t||(t={});for(n in te)r=t[n],i[n]=null==r?te[n]:r;(i.position.indent||i.position.start)&&(i.indent=i.position.indent||[],i.position=i.position.start);return function(e,t){var r,n,i,a,o,u,s,c,l,p,f,h,d,D,g,m,v,b,y=t.additional,E=t.nonTerminated,C=t.text,A=t.reference,w=t.warning,k=t.textContext,T=t.referenceContext,_=t.warningContext,S=t.position,F=t.indent||[],x=e.length,q=0,N=-1,L=S.column||1,I=S.line||1,te=K,de=[];g=ge(),s=w?function(e,t){var r=ge();r.column+=t,r.offset+=t,w.call(_,me[e],r,e)}:V,q--,x++;for(;++q<x;)if(a===X&&(L=F[N]||1),(a=be(q))!==z)a===X&&(I++,N++,L=0),a?(te+=a,L++):ye();else{if((u=be(q+1))===ee||u===X||u===j||u===Y||u===Z||u===z||u===K||y&&u===y){te+=a,L++;continue}for(f=h=q+1,b=h,u!==G?d=re:(b=++f,(u=be(b))===W||u===Q?(d=ne,b=++f):d=ie),r=K,p=K,i=K,D=oe[d],b--;++b<x&&(u=be(b),D(u));)i+=u,d===re&&U.call(O,i)&&(r=i,p=O[i]);(n=be(b)===H)&&(b++,d===re&&U.call(P,i)&&(r=i,p=P[i])),v=1+b-h,(n||E)&&(i?d===re?(n&&!p?s(pe,1):(r!==i&&(b=f+r.length,v=1+b-f,n=!1),n||(c=r?ue:ce,t.attribute?(u=be(b))===J?(s(c,v),p=null):B(u)?p=null:s(c,v):s(c,v))),o=p):(n||s(se,v),o=parseInt(i,ae[d]),(De=o)>=55296&&De<=57343||De>1114111?(s(he,v),o=M):o in R?(s(fe,v),o=R[o]):(l=K,ve(o)&&s(fe,v),o>65535&&(l+=$((o-=65536)>>>10|55296),o=56320|1023&o),o=l+$(o))):d!==re&&s(le,v)),o?(ye(),g=ge(),q=b-1,L+=b-h+1,de.push(o),(m=ge()).offset++,A&&A.call(T,o,{start:g,end:m},e.slice(h-1,b)),g=m):(i=e.slice(h-1,b),te+=i,L+=i.length,q=b-1)}var De;return de.join(K);function ge(){return{line:I,column:L,offset:q+(S.offset||0)}}function be(t){return e.charAt(t)}function ye(){te&&(de.push(te),C&&C.call(k,te,{start:g,end:ge()}),te=K)}}(e,i)},U={}.hasOwnProperty,$=String.fromCharCode,V=Function.prototype,M="�",j="\f",z="&",G="#",H=";",X="\n",W="x",Q="X",Y=" ",Z="<",J="=",K="",ee="\t",te={warning:null,reference:null,text:null,warningContext:null,referenceContext:null,textContext:null,position:{},additional:null,attribute:!1,nonTerminated:!0},re="named",ne="hexadecimal",ie="decimal",ae={};ae[ne]=16,ae[ie]=10;var oe={};oe[re]=B,oe[ie]=q,oe[ne]=N;var ue=1,se=2,ce=3,le=4,pe=5,fe=6,he=7,de="Numeric character references",De=" must be terminated by a semicolon",ge=" cannot be empty",me={};function ve(e){return e>=1&&e<=8||11===e||e>=13&&e<=31||e>=127&&e<=159||e>=64976&&e<=65007||65535==(65535&e)||65534==(65535&e)}me[ue]="Named character references"+De,me[se]=de+De,me[ce]="Named character references"+ge,me[le]=de+ge,me[pe]="Named character references must be known",me[fe]=de+" cannot be disallowed",me[he]=de+" cannot be outside the permissible Unicode range";var be=function(e){return n.raw=function(e,n,i){return I(e,p(i,{position:t(n),warning:r}))},n;function t(t){for(var r=e.offset,n=t.line,i=[];++n&&n in r;)i.push((r[n]||0)+1);return{start:t,indent:i}}function r(t,r,n){3!==n&&e.file.message(t,r)}function n(n,i,a){I(n,{position:t(i),warning:r,text:a,reference:a,textContext:e,referenceContext:e})}};var ye=function(e){return function(t,r){var n,i,a,o,u,s,c=this,l=c.offset,p=[],f=c[e+"Methods"],h=c[e+"Tokenizers"],d=r.line,D=r.column;if(!t)return p;b.now=m,b.file=c.file,g("");for(;t;){for(n=-1,i=f.length,u=!1;++n<i&&(o=f[n],!(a=h[o])||a.onlyAtStart&&!c.atStart||a.notInList&&c.inList||a.notInBlock&&c.inBlock||a.notInLink&&c.inLink||(s=t.length,a.apply(c,[b,t]),!(u=s!==t.length))););u||c.file.fail(new Error("Infinite loop"),b.now())}return c.eof=m(),p;function g(e){for(var t=-1,r=e.indexOf("\n");-1!==r;)d++,t=r,r=e.indexOf("\n",r+1);-1===t?D+=e.length:D=e.length-t,d in l&&(-1!==t?D+=l[d]:D<=l[d]&&(D=l[d]+1))}function m(){var e={line:d,column:D};return e.offset=c.toOffset(e),e}function v(e){this.start=e,this.end=m()}function b(e){var r,n=function(){var e=[],t=d+1;return function(){for(var r=d+1;t<r;)e.push((l[t]||0)+1),t++;return e}}(),i=(r=m(),function(e,t){var n=e.position,i=n?n.start:r,a=[],o=n&&n.end.line,u=r.line;if(e.position=new v(i),n&&t&&n.indent){if(a=n.indent,o<u){for(;++o<u;)a.push((l[o]||0)+1);a.push(r.column)}t=a.concat(t)}return e.position.indent=t||[],e}),a=m();return function(e){t.substring(0,e.length)!==e&&c.file.fail(new Error("Incorrectly eaten value: please report this warning on http://git.io/vg5Ft"),m())}(e),o.reset=u,u.test=s,o.test=s,t=t.substring(e.length),g(e),n=n(),o;function o(e,t){return i(function(e,t){var r=t?t.children:p,n=r[r.length-1];return n&&e.type===n.type&&e.type in Ee&&Ce(n)&&Ce(e)&&(e=Ee[e.type].call(c,n,e)),e!==n&&r.push(e),c.atStart&&0!==p.length&&c.exitStart(),e}(i(e),t),n)}function u(){var r=o.apply(null,arguments);return d=a.line,D=a.column,t=e+t,r}function s(){var r=i({});return d=a.line,D=a.column,t=e+t,r.position}}}},Ee={text:function(e,t){return e.value+=t.value,e},blockquote:function(e,t){if(this.options.commonmark)return t;return e.children=e.children.concat(t.children),e}};function Ce(e){var t,r;return"text"!==e.type||!e.position||(t=e.position.start,r=e.position.end,t.line!==r.line||r.column-t.column===e.value.length)}var Ae=_e,we=["\\","`","*","{","}","[","]","(",")","#","+","-",".","!","_",">"],ke=we.concat(["~","|"]),Te=ke.concat(["\n",'"',"$","%","&","'",",","/",":",";","<","=","?","@","^"]);function _e(e){var t=e||{};return t.commonmark?Te:t.gfm?ke:we}_e.default=we,_e.gfm=ke,_e.commonmark=Te;var Se={position:!0,gfm:!0,commonmark:!1,footnotes:!1,pedantic:!1,blocks:D(Object.freeze({__proto__:null,default:["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","iframe","legend","li","link","main","menu","menuitem","meta","nav","noframes","ol","optgroup","option","p","param","pre","section","source","title","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"]}))},Fe=function(e){var r,n,i=this.options;if(null==e)e={};else{if("object"!==t(e))throw new Error("Invalid value `"+e+"` for setting `options`");e=p(e)}for(r in Se){if(null==(n=e[r])&&(n=i[r]),"blocks"!==r&&"boolean"!=typeof n||"blocks"===r&&"object"!==t(n))throw new Error("Invalid value `"+n+"` for setting `options."+r+"`");e[r]=n}return this.options=e,this.escape=Ae(e),this};var xe=function(e,t,r,n){"function"==typeof t&&(n=r,r=t,t=null);function i(e,a,o){var u;return a=a||(o?0:null),t&&e.type!==t||(u=r(e,a,o||null)),e.children&&!1!==u?function(e,t){var r,a=n?-1:1,o=e.length,u=(n?o:-1)+a;for(;u>-1&&u<o;){if((r=e[u])&&!1===i(r,u,t))return!1;u+=a}return!0}(e.children,e):u}i(e)};var qe=function(e,t){return xe(e,t?Ne:Le),e};function Ne(e){delete e.position}function Le(e){e.position=void 0}var Be=function(){var e,t=String(this.file),r={line:1,column:1,offset:0},n=p(r);65279===(t=t.replace(Oe,Pe)).charCodeAt(0)&&(t=t.slice(1),n.column++,n.offset++);e={type:"root",children:this.tokenizeBlock(t,n),position:{start:r,end:this.eof||p(r)}},this.options.position||qe(e,!0);return e},Pe="\n",Oe=/\r\n|\r/g;var Re=function(e){return Ue.test("number"==typeof e?Ie(e):e.charAt(0))},Ie=String.fromCharCode,Ue=/\s/;var $e=function(e,t,r){var n,i,a,o,u=t.charAt(0);if("\n"!==u)return;if(r)return!0;o=1,n=t.length,i=u,a="";for(;o<n&&(u=t.charAt(o),Re(u));)a+=u,"\n"===u&&(i+=a,a=""),o++;e(i)}
/*!
   * repeat-string <https://github.com/jonschlinkert/repeat-string>
   *
   * Copyright (c) 2014-2015, Jon Schlinkert.
   * Licensed under the MIT License.
   */;var Ve,Me="",je=function(e,t){if("string"!=typeof e)throw new TypeError("expected a string");if(1===t)return e;if(2===t)return e+e;var r=e.length*t;if(Ve!==e||void 0===Ve)Ve=e,Me="";else if(Me.length>=r)return Me.substr(0,r);for(;r>Me.length&&t>1;)1&t&&(Me+=e),t>>=1,e+=e;return Me=(Me+=e).substr(0,r)};var ze=function(e){var t=String(e),r=t.length;for(;t.charAt(--r)===Ge;);return t.slice(0,r+1)},Ge="\n";var He=function(e,t,r){var n,i,a,o=-1,u=t.length,s="",c="",l="",p="";for(;++o<u;)if(n=t.charAt(o),a)if(a=!1,s+=l,c+=p,l="",p="",n===Xe)l=n,p=n;else for(s+=n,c+=n;++o<u;){if(!(n=t.charAt(o))||n===Xe){p=n,l=n;break}s+=n,c+=n}else if(n===Qe&&t.charAt(o+1)===n&&t.charAt(o+2)===n&&t.charAt(o+3)===n)l+=Ye,o+=3,a=!0;else if(n===We)l+=n,a=!0;else{for(i="";n===We||n===Qe;)i+=n,n=t.charAt(++o);if(n!==Xe)break;l+=i+n,p+=n}if(c)return!!r||e(s)({type:"code",lang:null,value:ze(c)})},Xe="\n",We="\t",Qe=" ",Ye=je(Qe,4);var Ze=function(e,t,r){var n,i,a,o,u,s,c,l,p,f,h,d=this.options,D=t.length+1,g=0,m="";if(!d.gfm)return;for(;g<D&&((a=t.charAt(g))===et||a===Ke);)m+=a,g++;if(f=g,(a=t.charAt(g))!==tt&&a!==rt)return;g++,i=a,n=1,m+=a;for(;g<D&&(a=t.charAt(g))===i;)m+=a,n++,g++;if(n<nt)return;for(;g<D&&((a=t.charAt(g))===et||a===Ke);)m+=a,g++;o="",u="";for(;g<D&&(a=t.charAt(g))!==Je&&a!==tt&&a!==rt;)a===et||a===Ke?u+=a:(o+=u+a,u=""),g++;if((a=t.charAt(g))&&a!==Je)return;if(r)return!0;(h=e.now()).column+=m.length,h.offset+=m.length,m+=o,o=this.decode.raw(this.unescape(o),h),u&&(m+=u);u="",l="",p="",s="",c="";for(;g<D;)if(a=t.charAt(g),s+=l,c+=p,l="",p="",a===Je){for(s?(l+=a,p+=a):m+=a,u="",g++;g<D&&(a=t.charAt(g))===et;)u+=a,g++;if(l+=u,p+=u.slice(f),!(u.length>=it)){for(u="";g<D&&(a=t.charAt(g))===i;)u+=a,g++;if(l+=u,p+=u,!(u.length<n)){for(u="";g<D&&((a=t.charAt(g))===et||a===Ke);)l+=a,p+=a,g++;if(!a||a===Je)break}}}else s+=a,p+=a,g++;return e(m+=s+l)({type:"code",lang:o||null,value:ze(c)})},Je="\n",Ke="\t",et=" ",tt="~",rt="`",nt=3,it=4;var at=d((function(e,t){(t=e.exports=function(e){return e.replace(/^\s*|\s*$/g,"")}).left=function(e){return e.replace(/^\s*/,"")},t.right=function(e){return e.replace(/\s*$/,"")}})),ot=(at.left,at.right,function(e,t,r,n){var i,a,o,u,s,c,l=["pedantic","commonmark"],p=l.length,f=e.length,h=-1;for(;++h<f;){for(i=e[h],a=i[1]||{},o=i[0],u=-1,c=!1;++u<p;)if(void 0!==a[s=l[u]]&&a[s]!==r.options[s]){c=!0;break}if(!c&&t[o].apply(r,n))return!0}return!1});var ut=function(e,t,r){var n,i,a,o,u,s,c,l,p,f=this.offset,h=this.blockTokenizers,d=this.interruptBlockquote,D=e.now(),g=D.line,m=t.length,v=[],b=[],y=[],E=0;for(;E<m&&((i=t.charAt(E))===lt||i===ct);)E++;if(t.charAt(E)!==pt)return;if(r)return!0;E=0;for(;E<m;){for(o=t.indexOf(st,E),c=E,l=!1,-1===o&&(o=m);E<m&&((i=t.charAt(E))===lt||i===ct);)E++;if(t.charAt(E)===pt?(E++,l=!0,t.charAt(E)===lt&&E++):E=c,u=t.slice(E,o),!l&&!at(u)){E=c;break}if(!l&&(a=t.slice(E),ot(d,h,this,[e,a,!0])))break;s=c===E?u:t.slice(c,o),y.push(E-c),v.push(s),b.push(u),E=o+1}E=-1,m=y.length,n=e(v.join(st));for(;++E<m;)f[g]=(f[g]||0)+y[E],g++;return p=this.enterBlock(),b=this.tokenizeBlock(b.join(st),D),p(),n({type:"blockquote",children:b})},st="\n",ct="\t",lt=" ",pt=">";var ft=function(e,t,r){var n,i,a,o=this.options,u=t.length+1,s=-1,c=e.now(),l="",p="";for(;++s<u;){if((n=t.charAt(s))!==Dt&&n!==dt){s--;break}l+=n}a=0;for(;++s<=u;){if((n=t.charAt(s))!==gt){s--;break}l+=n,a++}if(a>mt)return;if(!a||!o.pedantic&&t.charAt(s+1)===gt)return;u=t.length+1,i="";for(;++s<u;){if((n=t.charAt(s))!==Dt&&n!==dt){s--;break}i+=n}if(!o.pedantic&&0===i.length&&n&&n!==ht)return;if(r)return!0;l+=i,i="",p="";for(;++s<u&&(n=t.charAt(s))&&n!==ht;)if(n===Dt||n===dt||n===gt){for(;n===Dt||n===dt;)i+=n,n=t.charAt(++s);for(;n===gt;)i+=n,n=t.charAt(++s);for(;n===Dt||n===dt;)i+=n,n=t.charAt(++s);s--}else p+=i+n,i="";return c.column+=l.length,c.offset+=l.length,e(l+=p+i)({type:"heading",depth:a,children:this.tokenizeInline(p,c)})},ht="\n",dt="\t",Dt=" ",gt="#",mt=6;var vt=function(e,t,r){var n,i,a,o,u=-1,s=t.length+1,c="";for(;++u<s&&((n=t.charAt(u))===yt||n===Et);)c+=n;if(n!==Ct&&n!==wt&&n!==At)return;i=n,c+=n,a=1,o="";for(;++u<s;)if((n=t.charAt(u))===i)a++,c+=o+i,o="";else{if(n!==Et)return a>=kt&&(!n||n===bt)?(c+=o,!!r||e(c)({type:"thematicBreak"})):void 0;o+=n}},bt="\n",yt="\t",Et=" ",Ct="*",At="_",wt="-",kt=3;var Tt=function(e){var t,r=0,n=0,i=e.charAt(r),a={};for(;i in _t;)t=_t[i],n+=t,t>1&&(n=Math.floor(n/t)*t),a[n]=r,i=e.charAt(++r);return{indent:n,stops:a}},_t={" ":1,"\t":4};var St=function(e,t){var r,n,i,a,o=e.split(xt),u=o.length+1,s=1/0,c=[];o.unshift(je(Ft,t)+"!");for(;u--;)if(n=Tt(o[u]),c[u]=n.stops,0!==at(o[u]).length){if(!n.indent){s=1/0;break}n.indent>0&&n.indent<s&&(s=n.indent)}if(s!==1/0)for(u=o.length;u--;){for(i=c[u],r=s;r&&!(r in i);)r--;a=0!==at(o[u]).length&&s&&r!==s?qt:"",o[u]=a+o[u].slice(r in i?i[r]+1:0)}return o.shift(),o.join(xt)},Ft=" ",xt="\n",qt="\t";var Nt=function(e,t,r){var n,i,a,o,u,s,c,l,p,f,h,d,D,g,m,v,b,y,E,C,A,w,k,T,_=this.options.commonmark,S=this.options.pedantic,F=this.blockTokenizers,x=this.interruptList,N=0,L=t.length,B=null,P=0;for(;N<L;){if((o=t.charAt(N))===It)P+=$t-P%$t;else{if(o!==Ot)break;P++}N++}if(P>=$t)return;if(o=t.charAt(N),n=_?Wt:Xt,!0===Ht[o])u=o,a=!1;else{for(a=!0,i="";N<L&&(o=t.charAt(N),q(o));)i+=o,N++;if(o=t.charAt(N),!i||!0!==n[o])return;B=parseInt(i,10),u=o}if((o=t.charAt(++N))!==Ot&&o!==It)return;if(r)return!0;N=0,g=[],m=[],v=[];for(;N<L;){for(s=t.indexOf(Rt,N),c=N,l=!1,T=!1,-1===s&&(s=L),k=N+$t,P=0;N<L;){if((o=t.charAt(N))===It)P+=$t-P%$t;else{if(o!==Ot)break;P++}N++}if(P>=$t&&(T=!0),b&&P>=b.indent&&(T=!0),o=t.charAt(N),p=null,!T){if(!0===Ht[o])p=o,N++,P++;else{for(i="";N<L&&(o=t.charAt(N),q(o));)i+=o,N++;o=t.charAt(N),N++,i&&!0===n[o]&&(p=o,P+=i.length+1)}if(p)if((o=t.charAt(N))===It)P+=$t-P%$t,N++;else if(o===Ot){for(k=N+$t;N<k&&t.charAt(N)===Ot;)N++,P++;N===k&&t.charAt(N)===Ot&&(N-=$t-1,P-=$t-1)}else o!==Rt&&""!==o&&(p=null)}if(p){if(!S&&u!==p)break;l=!0}else _||T||t.charAt(c)!==Ot?_&&b&&(T=P>=b.indent||P>$t):T=!0,l=!1,N=c;if(h=t.slice(c,s),f=c===N?h:t.slice(N,s),(p===Lt||p===Bt||p===Pt)&&F.thematicBreak.call(this,e,h,!0))break;if(d=D,D=!at(f).length,T&&b)b.value=b.value.concat(v,h),m=m.concat(v,h),v=[];else if(l)0!==v.length&&(b.value.push(""),b.trail=v.concat()),b={value:[h],indent:P,trail:[]},g.push(b),m=m.concat(v,h),v=[];else if(D){if(d)break;v.push(h)}else{if(d)break;if(ot(x,F,this,[e,h,!0]))break;b.value=b.value.concat(v,h),m=m.concat(v,h),v=[]}N=s+1}A=e(m.join(Rt)).reset({type:"list",ordered:a,start:B,loose:null,children:[]}),y=this.enterList(),E=this.enterBlock(),C=!1,N=-1,L=g.length;for(;++N<L;)b=g[N].value.join(Rt),w=e.now(),(b=e(b)(Qt(this,b,w),A)).loose&&(C=!0),b=g[N].trail.join(Rt),N!==L-1&&(b+=Rt),e(b);return y(),E(),A.loose=C,A},Lt="*",Bt="_",Pt="-",Ot=" ",Rt="\n",It="\t",Ut="x",$t=4,Vt=/\n\n(?!\s*$)/,Mt=/^\[([ \t]|x|X)][ \t]/,jt=/^([ \t]*)([*+-]|\d+[.)])( {1,4}(?! )| |\t|$|(?=\n))([^\n]*)/,zt=/^([ \t]*)([*+-]|\d+[.)])([ \t]+)/,Gt=/^( {1,4}|\t)?/gm,Ht={};Ht[Lt]=!0,Ht["+"]=!0,Ht[Pt]=!0;var Xt={".":!0},Wt={};function Qt(e,t,r){var n,i,a=e.offset,o=null;return t=(e.options.pedantic?Yt:Zt).apply(null,arguments),e.options.gfm&&(n=t.match(Mt))&&(i=n[0].length,o=n[1].toLowerCase()===Ut,a[r.line]+=i,t=t.slice(i)),{type:"listItem",loose:Vt.test(t)||t.charAt(t.length-1)===Rt,checked:o,children:e.tokenizeBlock(t,r)}}function Yt(e,t,r){var n=e.offset,i=r.line;return t=t.replace(zt,a),i=r.line,t.replace(Gt,a);function a(e){return n[i]=(n[i]||0)+e.length,i++,""}}function Zt(e,t,r){var n,i,a,o,u,s,c,l=e.offset,p=r.line;for(o=(t=t.replace(jt,(function(e,t,r,o,u){i=t+r+o,a=u,Number(r)<10&&i.length%2==1&&(r=Ot+r);return(n=t+je(Ot,r.length)+o)+a}))).split(Rt),(u=St(t,Tt(n).indent).split(Rt))[0]=a,l[p]=(l[p]||0)+i.length,p++,s=0,c=o.length;++s<c;)l[p]=(l[p]||0)+o[s].length-u[s].length,p++;return u.join(Rt)}Wt["."]=!0,Wt[")"]=!0;var Jt=function(e,t,r){var n,i,a,o,u,s=e.now(),c=t.length,l=-1,p="";for(;++l<c;){if((a=t.charAt(l))!==tr||l>=rr){l--;break}p+=a}n="",i="";for(;++l<c;){if((a=t.charAt(l))===Kt){l--;break}a===tr||a===er?i+=a:(n+=i+a,i="")}if(s.column+=p.length,s.offset+=p.length,p+=n+i,a=t.charAt(++l),o=t.charAt(++l),a!==Kt||!nr[o])return;p+=a,i=o,u=nr[o];for(;++l<c;){if((a=t.charAt(l))!==o){if(a!==Kt)return;l--;break}i+=a}if(r)return!0;return e(p+i)({type:"heading",depth:u,children:this.tokenizeInline(n,s)})},Kt="\n",er="\t",tr=" ",rr=3,nr={};nr["="]=1,nr["-"]=2;var ir="<[A-Za-z][A-Za-z0-9\\-]*(?:\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\s*=\\s*(?:[^\"'=<>`\\u0000-\\u0020]+|'[^']*'|\"[^\"]*\"))?)*\\s*\\/?>",ar="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",or={openCloseTag:new RegExp("^(?:"+ir+"|"+ar+")"),tag:new RegExp("^(?:"+ir+"|"+ar+"|\x3c!----\x3e|\x3c!--(?:-?[^>-])(?:-?[^-])*--\x3e|<[?].*?[?]>|<![A-Za-z]+\\s+[^>]*>|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>)")},ur=or.openCloseTag,sr=function(e,t,r){var n,i,a,o,u,s,c,l=this.options.blocks,p=t.length,f=0,h=[[/^<(script|pre|style)(?=(\s|>|$))/i,/<\/(script|pre|style)>/i,!0],[/^<!--/,/-->/,!0],[/^<\?/,/\?>/,!0],[/^<![A-Za-z]/,/>/,!0],[/^<!\[CDATA\[/,/\]\]>/,!0],[new RegExp("^</?("+l.join("|")+")(?=(\\s|/?>|$))","i"),/^$/,!0],[new RegExp(ur.source+"\\s*$"),/^$/,!1]];for(;f<p&&((o=t.charAt(f))===cr||o===lr);)f++;if(t.charAt(f)!==fr)return;n=-1===(n=t.indexOf(pr,f+1))?p:n,i=t.slice(f,n),a=-1,u=h.length;for(;++a<u;)if(h[a][0].test(i)){s=h[a];break}if(!s)return;if(r)return s[2];if(f=n,!s[1].test(i))for(;f<p;){if(n=-1===(n=t.indexOf(pr,f+1))?p:n,i=t.slice(f+1,n),s[1].test(i)){i&&(f=n);break}f=n}return c=t.slice(0,f),e(c)({type:"html",value:c})},cr="\t",lr=" ",pr="\n",fr="<";var hr=function(e){return String(e).replace(/\s+/g," ")};var dr=function(e){return hr(e).toLowerCase()};var Dr=kr;kr.notInList=!0,kr.notInBlock=!0;var gr="\\",mr="\n",vr="\t",br=" ",yr="[",Er="]",Cr="^",Ar=":",wr=/^( {4}|\t)?/gm;function kr(e,t,r){var n,i,a,o,u,s,c,l,p,f,h,d,D=this.offset;if(this.options.footnotes){for(n=0,i=t.length,a="",o=e.now(),u=o.line;n<i&&(p=t.charAt(n),Re(p));)a+=p,n++;if(t.charAt(n)===yr&&t.charAt(n+1)===Cr){for(n=(a+=yr+Cr).length,c="";n<i&&(p=t.charAt(n))!==Er;)p===gr&&(c+=p,n++,p=t.charAt(n)),c+=p,n++;if(c&&t.charAt(n)===Er&&t.charAt(n+1)===Ar){if(r)return!0;for(f=dr(c),n=(a+=c+Er+Ar).length;n<i&&((p=t.charAt(n))===vr||p===br);)a+=p,n++;for(o.column+=a.length,o.offset+=a.length,c="",s="",l="";n<i;){if((p=t.charAt(n))===mr){for(l=p,n++;n<i&&(p=t.charAt(n))===mr;)l+=p,n++;for(c+=l,l="";n<i&&(p=t.charAt(n))===br;)l+=p,n++;if(0===l.length)break;c+=l}c&&(s+=c,c=""),s+=p,n++}return a+=s,s=s.replace(wr,(function(e){return D[u]=(D[u]||0)+e.length,u++,""})),h=e(a),d=this.enterBlock(),s=this.tokenizeBlock(s,o),d(),h({type:"footnoteDefinition",identifier:f,children:s})}}}}var Tr=$r;$r.notInList=!0,$r.notInBlock=!0;var _r='"',Sr="'",Fr="\\",xr="\n",qr="\t",Nr=" ",Lr="[",Br="]",Pr="(",Or=")",Rr=":",Ir="<",Ur=">";function $r(e,t,r){for(var n,i,a,o,u,s,c,l,p=this.options.commonmark,f=0,h=t.length,d="";f<h&&((o=t.charAt(f))===Nr||o===qr);)d+=o,f++;if((o=t.charAt(f))===Lr){for(f++,d+=o,a="";f<h&&(o=t.charAt(f))!==Br;)o===Fr&&(a+=o,f++,o=t.charAt(f)),a+=o,f++;if(a&&t.charAt(f)===Br&&t.charAt(f+1)===Rr){for(s=a,f=(d+=a+Br+Rr).length,a="";f<h&&((o=t.charAt(f))===qr||o===Nr||o===xr);)d+=o,f++;if(a="",n=d,(o=t.charAt(f))===Ir){for(f++;f<h&&Vr(o=t.charAt(f));)a+=o,f++;if((o=t.charAt(f))===Vr.delimiter)d+=Ir+a+o,f++;else{if(p)return;f-=a.length+1,a=""}}if(!a){for(;f<h&&Mr(o=t.charAt(f));)a+=o,f++;d+=a}if(a){for(c=a,a="";f<h&&((o=t.charAt(f))===qr||o===Nr||o===xr);)a+=o,f++;if(u=null,(o=t.charAt(f))===_r?u=_r:o===Sr?u=Sr:o===Pr&&(u=Or),u){if(!a)return;for(f=(d+=a+o).length,a="";f<h&&(o=t.charAt(f))!==u;){if(o===xr){if(f++,(o=t.charAt(f))===xr||o===u)return;a+=xr}a+=o,f++}if((o=t.charAt(f))!==u)return;i=d,d+=a+o,f++,l=a,a=""}else a="",f=d.length;for(;f<h&&((o=t.charAt(f))===qr||o===Nr);)d+=o,f++;return(o=t.charAt(f))&&o!==xr?void 0:!!r||(n=e(n).test().end,c=this.decode.raw(this.unescape(c),n,{nonTerminated:!1}),l&&(i=e(i).test().end,l=this.decode.raw(this.unescape(l),i)),e(d)({type:"definition",identifier:dr(s),title:l||null,url:c}))}}}}function Vr(e){return e!==Ur&&e!==Lr&&e!==Br}function Mr(e){return e!==Lr&&e!==Br&&!Re(e)}Vr.delimiter=Ur;var jr=function(e,t,r){var n,i,a,o,u,s,c,l,p,f,h,d,D,g,m,v,b,y,E,C,A,w,k,T;if(!this.options.gfm)return;n=0,y=0,s=t.length+1,c=[];for(;n<s;){if(w=t.indexOf(Yr,n),k=t.indexOf(Xr,n+1),-1===w&&(w=t.length),-1===k||k>w){if(y<Kr)return;break}c.push(t.slice(n,w)),y++,n=w+1}o=c.join(Yr),i=c.splice(1,1)[0]||[],n=0,s=i.length,y--,a=!1,h=[];for(;n<s;){if((p=i.charAt(n))===Xr){if(f=null,!1===a){if(!1===T)return}else h.push(a),a=!1;T=!1}else if(p===Hr)f=!0,a=a||nn;else if(p===Wr)a=a===en?tn:f&&a===nn?rn:en;else if(!Re(p))return;n++}!1!==a&&h.push(a);if(h.length<Jr)return;if(r)return!0;b=-1,C=[],A=e(o).reset({type:"table",align:h,children:C});for(;++b<y;){for(E=c[b],u={type:"tableRow",children:[]},b&&e(Yr),e(E).reset(u,A),s=E.length+1,n=0,l="",d="",D=!0,g=null,m=null;n<s;)if((p=E.charAt(n))!==Zr&&p!==Qr){if(""===p||p===Xr)if(D)e(p);else{if(p&&m){l+=p,n++;continue}!d&&!p||D||(o=d,l.length>1&&(p?(o+=l.slice(0,l.length-1),l=l.charAt(l.length-1)):(o+=l,l="")),v=e.now(),e(o)({type:"tableCell",children:this.tokenizeInline(d,v)},u)),e(l+p),l="",d=""}else if(l&&(d+=l,l=""),d+=p,p===zr&&n!==s-2&&(d+=E.charAt(n+1),n++),p===Gr){for(g=1;E.charAt(n+1)===p;)d+=p,n++,g++;m?g>=m&&(m=0):m=g}D=!1,n++}else d?l+=p:e(p),n++;b||e(Yr+i)}return A},zr="\\",Gr="`",Hr="-",Xr="|",Wr=":",Qr=" ",Yr="\n",Zr="\t",Jr=1,Kr=2,en="left",tn="center",rn="right",nn=null;var an=function(e,t,r){var n,i,a,o,u,s=this.options,c=s.commonmark,l=s.gfm,p=this.blockTokenizers,f=this.interruptParagraph,h=t.indexOf(on),d=t.length;for(;h<d;){if(-1===h){h=d;break}if(t.charAt(h+1)===on)break;if(c){for(o=0,n=h+1;n<d;){if((a=t.charAt(n))===un){o=cn;break}if(a!==sn)break;o++,n++}if(o>=cn){h=t.indexOf(on,h+1);continue}}if(i=t.slice(h+1),ot(f,p,this,[e,i,!0]))break;if(p.list.call(this,e,i,!0)&&(this.inList||c||l&&!q(at.left(i).charAt(0))))break;if(n=h,-1!==(h=t.indexOf(on,h+1))&&""===at(t.slice(n,h))){h=n;break}}if(i=t.slice(0,h),""===at(i))return e(i),null;if(r)return!0;return u=e.now(),i=ze(i),e(i)({type:"paragraph",children:this.tokenizeInline(i,u)})},on="\n",un="\t",sn=" ",cn=4;var ln=function(e,t){return e.indexOf("\\",t)};var pn=fn;function fn(e,t,r){var n,i;if("\\"===t.charAt(0)&&(n=t.charAt(1),-1!==this.escape.indexOf(n)))return!!r||(i="\n"===n?{type:"break"}:{type:"text",value:n},e("\\"+n)(i))}fn.locator=ln;var hn=function(e,t){return e.indexOf("<",t)};var dn=En;En.locator=hn,En.notInLink=!0;var Dn="<",gn=">",mn="@",vn="/",bn="mailto:",yn=bn.length;function En(e,t,r){var n,i,a,o,u,s,c,l,p,f,h;if(t.charAt(0)===Dn){for(this,n="",i=t.length,a=0,o="",s=!1,c="",a++,n=Dn;a<i&&(u=t.charAt(a),!(Re(u)||u===gn||u===mn||":"===u&&t.charAt(a+1)===vn));)o+=u,a++;if(o){if(c+=o,o="",c+=u=t.charAt(a),a++,u===mn)s=!0;else{if(":"!==u||t.charAt(a+1)!==vn)return;c+=vn,a++}for(;a<i&&(u=t.charAt(a),!Re(u)&&u!==gn);)o+=u,a++;if(u=t.charAt(a),o&&u===gn)return!!r||(p=c+=o,n+=c+u,(l=e.now()).column++,l.offset++,s&&(c.slice(0,yn).toLowerCase()===bn?(p=p.substr(yn),l.column+=yn,l.offset+=yn):c=bn+c),f=this.inlineTokenizers,this.inlineTokenizers={text:f.text},h=this.enterLink(),p=this.tokenizeInline(p,l),this.inlineTokenizers=f,h(),e(n)({type:"link",title:null,url:I(c,{nonTerminated:!1}),children:p}))}}}var Cn=function(e,t){var r,n=An.length,i=-1,a=-1;if(!this.options.gfm)return-1;for(;++i<n;)-1!==(r=e.indexOf(An[i],t))&&(r<a||-1===a)&&(a=r);return a},An=["https://","http://","mailto:"];var wn=Bn;Bn.locator=Cn,Bn.notInLink=!0;var kn="[",Tn="]",_n="(",Sn=")",Fn="<",xn="@",qn="mailto:",Nn=["http://","https://",qn],Ln=Nn.length;function Bn(e,t,r){var n,i,a,o,u,s,c,l,p,f,h,d;if(this.options.gfm){for(n="",o=-1,l=Ln;++o<l;)if(s=Nn[o],(c=t.slice(0,s.length)).toLowerCase()===s){n=c;break}if(n){for(o=n.length,l=t.length,p="",f=0;o<l&&(a=t.charAt(o),!Re(a)&&a!==Fn)&&("."!==a&&","!==a&&":"!==a&&";"!==a&&'"'!==a&&"'"!==a&&")"!==a&&"]"!==a||(h=t.charAt(o+1))&&!Re(h))&&(a!==_n&&a!==kn||f++,a!==Sn&&a!==Tn||!(--f<0));)p+=a,o++;if(p){if(i=n+=p,s===qn){if(-1===(u=p.indexOf(xn))||u===l-1)return;i=i.substr(qn.length)}return!!r||(d=this.enterLink(),i=this.tokenizeInline(i,e.now()),d(),e(n)({type:"link",title:null,url:I(n,{nonTerminated:!1}),children:i}))}}}}var Pn=or.tag,On=Un;Un.locator=hn;var Rn=/^<a /i,In=/^<\/a>/i;function Un(e,t,r){var n,i,a=t.length;if(!("<"!==t.charAt(0)||a<3)&&(n=t.charAt(1),(L(n)||"?"===n||"!"===n||"/"===n)&&(i=t.match(Pn))))return!!r||(i=i[0],!this.inLink&&Rn.test(i)?this.inLink=!0:this.inLink&&In.test(i)&&(this.inLink=!1),e(i)({type:"html",value:i}))}var $n=function(e,t){var r=e.indexOf("[",t),n=e.indexOf("![",t);if(-1===n)return r;return r<n?r:n};var Vn=Kn;Kn.locator=$n;var Mn={}.hasOwnProperty,jn="\\",zn="[",Gn="]",Hn="(",Xn=")",Wn="<",Qn=">",Yn="`",Zn={'"':'"',"'":"'"},Jn={};function Kn(e,t,r){var n,i,a,o,u,s,c,l,p,f,h,d,D,g,m,v,b,y,E,C="",A=0,w=t.charAt(0),k=this.options.pedantic,T=this.options.commonmark,_=this.options.gfm;if("!"===w&&(p=!0,C=w,w=t.charAt(++A)),w===zn&&(p||!this.inLink)){for(C+=w,m="",A++,d=t.length,g=0,(b=e.now()).column+=A,b.offset+=A;A<d;){if(s=w=t.charAt(A),w===Yn){for(i=1;t.charAt(A+1)===Yn;)s+=w,A++,i++;a?i>=a&&(a=0):a=i}else if(w===jn)A++,s+=t.charAt(A);else if(a&&!_||w!==zn){if((!a||_)&&w===Gn){if(!g){if(!k)for(;A<d&&(w=t.charAt(A+1),Re(w));)s+=w,A++;if(t.charAt(A+1)!==Hn)return;s+=Hn,n=!0,A++;break}g--}}else g++;m+=s,s="",A++}if(n){for(f=m,C+=m+s,A++;A<d&&(w=t.charAt(A),Re(w));)C+=w,A++;if(w=t.charAt(A),l=T?Jn:Zn,m="",o=C,w===Wn){for(A++,o+=Wn;A<d&&(w=t.charAt(A))!==Qn;){if(T&&"\n"===w)return;m+=w,A++}if(t.charAt(A)!==Qn)return;C+=Wn+m+Qn,v=m,A++}else{for(w=null,s="";A<d&&(w=t.charAt(A),!s||!Mn.call(l,w));){if(Re(w)){if(!k)break;s+=w}else{if(w===Hn)g++;else if(w===Xn){if(0===g)break;g--}m+=s,s="",w===jn&&(m+=jn,w=t.charAt(++A)),m+=w}A++}v=m,A=(C+=m).length}for(m="";A<d&&(w=t.charAt(A),Re(w));)m+=w,A++;if(w=t.charAt(A),C+=m,m&&Mn.call(l,w))if(A++,C+=w,m="",h=l[w],u=C,T){for(;A<d&&(w=t.charAt(A))!==h;)w===jn&&(m+=jn,w=t.charAt(++A)),A++,m+=w;if((w=t.charAt(A))!==h)return;for(D=m,C+=m+w,A++;A<d&&(w=t.charAt(A),Re(w));)C+=w,A++}else for(s="";A<d;){if((w=t.charAt(A))===h)c&&(m+=h+s,s=""),c=!0;else if(c){if(w===Xn){C+=m+h+s,D=m;break}Re(w)?s+=w:(m+=h+s+w,s="",c=!1)}else m+=w;A++}if(t.charAt(A)===Xn)return!!r||(C+=Xn,v=this.decode.raw(this.unescape(v),e(o).test().end,{nonTerminated:!1}),D&&(u=e(u).test().end,D=this.decode.raw(this.unescape(D),u)),E={type:p?"image":"link",title:D||null,url:v},p?E.alt=this.decode.raw(this.unescape(f),b)||null:(y=this.enterLink(),E.children=this.tokenizeInline(f,b),y()),e(C)(E))}}}Jn['"']='"',Jn["'"]="'",Jn[Hn]=Xn;var ei=pi;pi.locator=$n;var ti="link",ri="image",ni="footnote",ii="shortcut",ai="collapsed",oi="full",ui="^",si="\\",ci="[",li="]";function pi(e,t,r){var n,i,a,o,u,s,c,l,p=t.charAt(0),f=0,h=t.length,d="",D="",g=ti,m=ii;if("!"===p&&(g=ri,D=p,p=t.charAt(++f)),p===ci){if(f++,D+=p,s="",this.options.footnotes&&t.charAt(f)===ui){if(g===ri)return;D+=ui,f++,g=ni}for(l=0;f<h;){if((p=t.charAt(f))===ci)c=!0,l++;else if(p===li){if(!l)break;l--}p===si&&(s+=si,p=t.charAt(++f)),s+=p,f++}if(d=s,n=s,(p=t.charAt(f))===li){for(f++,d+=p,s="";f<h&&(p=t.charAt(f),Re(p));)s+=p,f++;if(p=t.charAt(f),g!==ni&&p===ci){for(i="",s+=p,f++;f<h&&(p=t.charAt(f))!==ci&&p!==li;)p===si&&(i+=si,p=t.charAt(++f)),i+=p,f++;(p=t.charAt(f))===li?(m=i?oi:ai,s+=i+p,f++):i="",d+=s,s=""}else{if(!n)return;i=n}if(m===oi||!c)return d=D+d,g===ti&&this.inLink?null:!!r||(g===ni&&-1!==n.indexOf(" ")?e(d)({type:"footnote",children:this.tokenizeInline(n,e.now())}):((a=e.now()).column+=D.length,a.offset+=D.length,o={type:g+"Reference",identifier:dr(i=m===oi?i:n)},g!==ti&&g!==ri||(o.referenceType=m),g===ti?(u=this.enterLink(),o.children=this.tokenizeInline(n,a),u()):g===ri&&(o.alt=this.decode.raw(this.unescape(n),a)||null),e(d)(o)))}}}var fi=function(e,t){var r=e.indexOf("**",t),n=e.indexOf("__",t);if(-1===n)return r;if(-1===r)return n;return n<r?n:r};var hi=gi;gi.locator=fi;var di="*",Di="_";function gi(e,t,r){var n,i,a,o,u,s,c,l=0,p=t.charAt(l);if(!(p!==di&&p!==Di||t.charAt(++l)!==p||(i=this.options.pedantic,u=(a=p)+a,s=t.length,l++,o="",p="",i&&Re(t.charAt(l)))))for(;l<s;){if(c=p,!((p=t.charAt(l))!==a||t.charAt(l+1)!==a||i&&Re(c))&&(p=t.charAt(l+2))!==a){if(!at(o))return;return!!r||((n=e.now()).column+=2,n.offset+=2,e(u+o+u)({type:"strong",children:this.tokenizeInline(o,n)}))}i||"\\"!==p||(o+=p,p=t.charAt(++l)),o+=p,l++}}var mi=function(e){return bi.test("number"==typeof e?vi(e):e.charAt(0))},vi=String.fromCharCode,bi=/\w/;var yi=function(e,t){var r=e.indexOf("*",t),n=e.indexOf("_",t);if(-1===n)return r;if(-1===r)return n;return n<r?n:r};var Ei=wi;wi.locator=yi;var Ci="*",Ai="_";function wi(e,t,r){var n,i,a,o,u,s,c,l=0,p=t.charAt(l);if(!(p!==Ci&&p!==Ai||(i=this.options.pedantic,u=p,a=p,s=t.length,l++,o="",p="",i&&Re(t.charAt(l)))))for(;l<s;){if(c=p,!((p=t.charAt(l))!==a||i&&Re(c))){if((p=t.charAt(++l))!==a){if(!at(o)||c===a)return;if(!i&&a===Ai&&mi(p)){o+=a;continue}return!!r||((n=e.now()).column++,n.offset++,e(u+o+a)({type:"emphasis",children:this.tokenizeInline(o,n)}))}o+=a}i||"\\"!==p||(o+=p,p=t.charAt(++l)),o+=p,l++}}var ki=function(e,t){return e.indexOf("~~",t)};var Ti=Fi;Fi.locator=ki;var _i="~",Si="~~";function Fi(e,t,r){var n,i,a,o="",u="",s="",c="";if(this.options.gfm&&t.charAt(0)===_i&&t.charAt(1)===_i&&!Re(t.charAt(2)))for(n=1,i=t.length,(a=e.now()).column+=2,a.offset+=2;++n<i;){if(!((o=t.charAt(n))!==_i||u!==_i||s&&Re(s)))return!!r||e(Si+c+Si)({type:"delete",children:this.tokenizeInline(c,a)});c+=u,s=u,u=o}}var xi=function(e,t){return e.indexOf("`",t)};var qi=Li;Li.locator=xi;var Ni="`";function Li(e,t,r){for(var n,i,a,o,u,s,c,l,p=t.length,f=0,h="",d="";f<p&&t.charAt(f)===Ni;)h+=Ni,f++;if(h){for(u=h,o=f,h="",l=t.charAt(f),a=0;f<p;){if(s=l,l=t.charAt(f+1),s===Ni?(a++,d+=s):(a=0,h+=s),a&&l!==Ni){if(a===o){u+=h+d,c=!0;break}h+=d,d=""}f++}if(!c){if(o%2!=0)return;h=""}if(r)return!0;for(n="",i="",p=h.length,f=-1;++f<p;)s=h.charAt(f),Re(s)?i+=s:(i&&(n&&(n+=i),i=""),n+=s);return e(u)({type:"inlineCode",value:n})}}var Bi=function(e,t){var r=e.indexOf("\n",t);for(;r>t&&" "===e.charAt(r-1);)r--;return r};var Pi=Ri;Ri.locator=Bi;var Oi=2;function Ri(e,t,r){for(var n,i=t.length,a=-1,o="";++a<i;){if("\n"===(n=t.charAt(a))){if(a<Oi)return;return!!r||e(o+=n)({type:"break"})}if(" "!==n)return;o+=n}}var Ii=function(e,t,r){var n,i,a,o,u,s,c,l,p,f;if(r)return!0;n=this.inlineMethods,o=n.length,i=this.inlineTokenizers,a=-1,p=t.length;for(;++a<o;)"text"!==(l=n[a])&&i[l]&&((c=i[l].locator)||e.file.fail("Missing locator: `"+l+"`"),-1!==(s=c.call(this,t,1))&&s<p&&(p=s));u=t.slice(0,p),f=e.now(),this.decode(u,f,(function(t,r,n){e(n||t)({type:"text",value:t})}))};var Ui=$i;function $i(e,t){this.file=t,this.offset={},this.options=p(this.options),this.setOptions({}),this.inList=!1,this.inBlock=!1,this.inLink=!1,this.atStart=!0,this.toOffset=C(t).toOffset,this.unescape=k(this,"escape"),this.decode=be(this)}var Vi=$i.prototype;function Mi(e){var t,r=[];for(t in e)r.push(t);return r}Vi.setOptions=Fe,Vi.parse=Be,Vi.options=Se,Vi.exitStart=E("atStart",!0),Vi.enterList=E("inList",!1),Vi.enterLink=E("inLink",!1),Vi.enterBlock=E("inBlock",!1),Vi.interruptParagraph=[["thematicBreak"],["atxHeading"],["fencedCode"],["blockquote"],["html"],["setextHeading",{commonmark:!1}],["definition",{commonmark:!1}],["footnote",{commonmark:!1}]],Vi.interruptList=[["atxHeading",{pedantic:!1}],["fencedCode",{pedantic:!1}],["thematicBreak",{pedantic:!1}],["definition",{commonmark:!1}],["footnote",{commonmark:!1}]],Vi.interruptBlockquote=[["indentedCode",{commonmark:!0}],["fencedCode",{commonmark:!0}],["atxHeading",{commonmark:!0}],["setextHeading",{commonmark:!0}],["thematicBreak",{commonmark:!0}],["html",{commonmark:!0}],["list",{commonmark:!0}],["definition",{commonmark:!1}],["footnote",{commonmark:!1}]],Vi.blockTokenizers={newline:$e,indentedCode:He,fencedCode:Ze,blockquote:ut,atxHeading:ft,thematicBreak:vt,list:Nt,setextHeading:Jt,html:sr,footnote:Dr,definition:Tr,table:jr,paragraph:an},Vi.inlineTokenizers={escape:pn,autoLink:dn,url:wn,html:On,link:Vn,reference:ei,strong:hi,emphasis:Ei,deletion:Ti,code:qi,break:Pi,text:Ii},Vi.blockMethods=Mi(Vi.blockTokenizers),Vi.inlineMethods=Mi(Vi.inlineTokenizers),Vi.tokenizeBlock=ye("block"),Vi.tokenizeInline=ye("inline"),Vi.tokenizeFactory=ye;var ji=zi;function zi(e){var t=y(Ui);t.prototype.options=p(t.prototype.options,this.data("settings"),e),this.Parser=t}zi.Parser=Ui;var Gi=Object.prototype.hasOwnProperty,Hi=Object.prototype.toString,Xi=Object.defineProperty,Wi=Object.getOwnPropertyDescriptor,Qi=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Hi.call(e)},Yi=function(e){if(!e||"[object Object]"!==Hi.call(e))return!1;var t,r=Gi.call(e,"constructor"),n=e.constructor&&e.constructor.prototype&&Gi.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!r&&!n)return!1;for(t in e);return void 0===t||Gi.call(e,t)},Zi=function(e,t){Xi&&"__proto__"===t.name?Xi(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},Ji=function(e,t){if("__proto__"===t){if(!Gi.call(e,t))return;if(Wi)return Wi(e,t).value}return e[t]},Ki=function e(){var r,n,i,a,o,u,s=arguments[0],c=1,l=arguments.length,p=!1;for("boolean"==typeof s&&(p=s,s=arguments[1]||{},c=2),(null==s||"object"!==t(s)&&"function"!=typeof s)&&(s={});c<l;++c)if(null!=(r=arguments[c]))for(n in r)i=Ji(s,n),s!==(a=Ji(r,n))&&(p&&a&&(Yi(a)||(o=Qi(a)))?(o?(o=!1,u=i&&Qi(i)?i:[]):u=i&&Yi(i)?i:{},Zi(s,{name:n,newValue:e(p,u,a)})):void 0!==a&&Zi(s,{name:n,newValue:a}));return s},ea=function(e){if(e)throw e};var ta={}.hasOwnProperty,ra=function(e){if(!e||"object"!==t(e))return"";if(ta.call(e,"position")||ta.call(e,"type"))return ia(e.position);if(ta.call(e,"start")||ta.call(e,"end"))return ia(e);if(ta.call(e,"line")||ta.call(e,"column"))return na(e);return""};function na(e){return e&&"object"===t(e)||(e={}),aa(e.line)+":"+aa(e.column)}function ia(e){return e&&"object"===t(e)||(e={}),na(e.start)+"-"+na(e.end)}function aa(e){return e&&"number"==typeof e?e:1}var oa=ca;function ua(){}ua.prototype=Error.prototype,ca.prototype=new ua;var sa=ca.prototype;function ca(e,t,r){var n,i,a;"string"==typeof t&&(r=t,t=null),n=function(e){var t,r=[null,null];"string"==typeof e&&(-1===(t=e.indexOf(":"))?r[1]=e:(r[0]=e.slice(0,t),r[1]=e.slice(t+1)));return r}(r),i=ra(t)||"1:1",a={start:{line:null,column:null},end:{line:null,column:null}},t&&t.position&&(t=t.position),t&&(t.start?(a=t,t=t.start):a.start=t),e.stack&&(this.stack=e.stack,e=e.message),this.message=e,this.name=i,this.reason=e,this.line=t?t.line:null,this.column=t?t.column:null,this.location=a,this.source=n[0],this.ruleId=n[1]}sa.file="",sa.name="",sa.reason="",sa.message="",sa.stack="",sa.fatal=null,sa.column=null,sa.line=null;var la="undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{};function pa(){throw new Error("setTimeout has not been defined")}function fa(){throw new Error("clearTimeout has not been defined")}var ha=pa,da=fa;function Da(e){if(ha===setTimeout)return setTimeout(e,0);if((ha===pa||!ha)&&setTimeout)return ha=setTimeout,setTimeout(e,0);try{return ha(e,0)}catch(t){try{return ha.call(null,e,0)}catch(t){return ha.call(this,e,0)}}}"function"==typeof la.setTimeout&&(ha=setTimeout),"function"==typeof la.clearTimeout&&(da=clearTimeout);var ga,ma=[],va=!1,ba=-1;function ya(){va&&ga&&(va=!1,ga.length?ma=ga.concat(ma):ba=-1,ma.length&&Ea())}function Ea(){if(!va){var e=Da(ya);va=!0;for(var t=ma.length;t;){for(ga=ma,ma=[];++ba<t;)ga&&ga[ba].run();ba=-1,t=ma.length}ga=null,va=!1,function(e){if(da===clearTimeout)return clearTimeout(e);if((da===fa||!da)&&clearTimeout)return da=clearTimeout,clearTimeout(e);try{da(e)}catch(t){try{return da.call(null,e)}catch(t){return da.call(this,e)}}}(e)}}function Ca(e,t){this.fun=e,this.array=t}Ca.prototype.run=function(){this.fun.apply(null,this.array)};function Aa(){}var wa=Aa,ka=Aa,Ta=Aa,_a=Aa,Sa=Aa,Fa=Aa,xa=Aa;var qa=la.performance||{},Na=qa.now||qa.mozNow||qa.msNow||qa.oNow||qa.webkitNow||function(){return(new Date).getTime()};var La=new Date;var Ba={nextTick:function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];ma.push(new Ca(e,t)),1!==ma.length||va||Da(Ea)},title:"browser",browser:!0,env:{},argv:[],version:"",versions:{},on:wa,addListener:ka,once:Ta,off:_a,removeListener:Sa,removeAllListeners:Fa,emit:xa,binding:function(e){throw new Error("process.binding is not supported")},cwd:function(){return"/"},chdir:function(e){throw new Error("process.chdir is not supported")},umask:function(){return 0},hrtime:function(e){var t=.001*Na.call(qa),r=Math.floor(t),n=Math.floor(t%1*1e9);return e&&(r-=e[0],(n-=e[1])<0&&(r--,n+=1e9)),[r,n]},platform:"browser",release:{},config:{},uptime:function(){return(new Date-La)/1e3}};var Pa=D(Object.freeze({__proto__:null,extname:function(e){var t=e.lastIndexOf(".");return-1===t?"":e.slice(t)}}));var Oa=function(e,t){if("string"!=typeof e)return e;if(0===e.length)return e;var r=Pa.basename(e,Pa.extname(e))+t;return Pa.join(Pa.dirname(e),r)},Ra=function(e){return null!=e&&null!=e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},Ia=Ma,Ua={}.hasOwnProperty,$a=Ma.prototype,Va=["history","path","basename","stem","extname","dirname"];
/*!
   * Determine if an object is a Buffer
   *
   * <AUTHOR> Aboukhadijeh <https://feross.org>
   * @license  MIT
   */function Ma(e){var t,r,n;if(e){if("string"==typeof e||Ra(e))e={contents:e};else if("message"in e&&"messages"in e)return e}else e={};if(!(this instanceof Ma))return new Ma(e);for(this.data={},this.messages=[],this.history=[],this.cwd=Ba.cwd(),r=-1,n=Va.length;++r<n;)t=Va[r],Ua.call(e,t)&&(this[t]=e[t]);for(t in e)-1===Va.indexOf(t)&&(this[t]=e[t])}function ja(e,t){if(-1!==e.indexOf(Pa.sep))throw new Error("`"+t+"` cannot be a path: did not expect `"+Pa.sep+"`")}function za(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function Ga(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}$a.toString=function(e){var t=this.contents||"";return Ra(t)?t.toString(e):String(t)},Object.defineProperty($a,"path",{get:function(){return this.history[this.history.length-1]},set:function(e){za(e,"path"),e!==this.path&&this.history.push(e)}}),Object.defineProperty($a,"dirname",{get:function(){return"string"==typeof this.path?Pa.dirname(this.path):void 0},set:function(e){Ga(this.path,"dirname"),this.path=Pa.join(e||"",this.basename)}}),Object.defineProperty($a,"basename",{get:function(){return"string"==typeof this.path?Pa.basename(this.path):void 0},set:function(e){za(e,"basename"),ja(e,"basename"),this.path=Pa.join(this.dirname||"",e)}}),Object.defineProperty($a,"extname",{get:function(){return"string"==typeof this.path?Pa.extname(this.path):void 0},set:function(e){var t=e||"";if(ja(t,"extname"),Ga(this.path,"extname"),t){if("."!==t.charAt(0))throw new Error("`extname` must start with `.`");if(-1!==t.indexOf(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=Oa(this.path,t)}}),Object.defineProperty($a,"stem",{get:function(){return"string"==typeof this.path?Pa.basename(this.path,this.extname):void 0},set:function(e){za(e,"stem"),ja(e,"stem"),this.path=Pa.join(this.dirname||"",e+(this.extname||""))}});var Ha=Ia,Xa=Ia.prototype;Xa.message=function(e,t,r){var n=this.path,i=new oa(e,t,r);n&&(i.name=n+":"+i.name,i.file=n);return i.fatal=!1,this.messages.push(i),i},Xa.info=function(){var e=this.message.apply(this,arguments);return e.fatal=null,e},Xa.fail=function(){var e=this.message.apply(this,arguments);throw e.fatal=!0,e};var Wa=function(){var e=[],t={};return t.run=function(){var t=-1,r=Qa.call(arguments,0,-1),n=arguments[arguments.length-1];if("function"!=typeof n)throw new Error("Expected function as last argument, not "+n);(function i(a){var o=e[++t],u=Qa.call(arguments,0).slice(1),s=r.length,c=-1;if(a)n(a);else{for(;++c<s;)null!==u[c]&&void 0!==u[c]||(u[c]=r[c]);r=u,o?function(e,t){var r;return function(){var t,a=Qa.call(arguments,0),o=e.length>a.length;o&&a.push(n);try{t=e.apply(null,a)}catch(e){if(o&&r)throw e;return n(e)}o||(t&&"function"==typeof t.then?t.then(i,n):t instanceof Error?n(t):i(t))};function n(){r||(r=!0,t.apply(null,arguments))}function i(e){n(null,e)}}(o,i).apply(null,r):n.apply(null,[null].concat(r))}}).apply(null,[null].concat(r))},t.use=function(r){if("function"!=typeof r)throw new Error("Expected `fn` to be a function, not "+r);return e.push(r),t},t},Qa=[].slice;var Ya=function(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.getPrototypeOf({})},Za=function e(){var r=[];var n=Wa();var i={};var a=!1;var o=-1;u.data=function(e,t){if("string"==typeof e)return 2===arguments.length?(io("data",a),i[e]=t,u):Ka.call(i,e)&&i[e]||null;if(e)return io("data",a),i=e,u;return i};u.freeze=s;u.attachers=r;u.use=function(e){var n;if(io("use",a),null==e);else if("function"==typeof e)l.apply(null,arguments);else{if("object"!==t(e))throw new Error("Expected usable value, not `"+e+"`");"length"in e?c(e):o(e)}n&&(i.settings=Ki(i.settings||{},n));return u;function o(e){c(e.plugins),e.settings&&(n=Ki(n||{},e.settings))}function s(e){if("function"==typeof e)l(e);else{if("object"!==t(e))throw new Error("Expected usable value, not `"+e+"`");"length"in e?l.apply(null,e):o(e)}}function c(e){var r,n;if(null==e);else{if(!("object"===t(e)&&"length"in e))throw new Error("Expected a list of plugins, not `"+e+"`");for(r=e.length,n=-1;++n<r;)s(e[n])}}function l(e,t){var n=function(e){var t,n=r.length,i=-1;for(;++i<n;)if((t=r[i])[0]===e)return t}(e);n?(Ya(n[1])&&Ya(t)&&(t=Ki(n[1],t)),n[1]=t):r.push(Ja.call(arguments))}};u.parse=function(e){var t,r=Ha(e);if(s(),ro("parse",t=u.Parser),to(t,"parse"))return new t(String(r),r).parse();return t(String(r),r)};u.stringify=function(e,t){var r,n=Ha(t);if(s(),no("stringify",r=u.Compiler),ao(e),to(r,"compile"))return new r(e,n).compile();return r(e,n)};u.run=c;u.runSync=function(e,t){var r,n=!1;return c(e,t,(function(e,t){n=!0,ea(e),r=t})),oo("runSync","run",n),r};u.process=l;u.processSync=function(e){var t,r=!1;return s(),ro("processSync",u.Parser),no("processSync",u.Compiler),l(t=Ha(e),(function(e){r=!0,ea(e)})),oo("processSync","process",r),t};return u;function u(){for(var t=e(),n=r.length,a=-1;++a<n;)t.use.apply(null,r[a]);return t.data(Ki(!0,{},i)),t}function s(){var e,t,i,s;if(a)return u;for(;++o<r.length;)t=(e=r[o])[0],null,!1!==(i=e[1])&&(!0===i&&(e[1]=void 0),"function"==typeof(s=t.apply(u,e.slice(1)))&&n.use(s));return a=!0,o=1/0,u}function c(e,t,r){if(ao(e),s(),r||"function"!=typeof t||(r=t,t=null),!r)return new Promise(i);function i(i,a){n.run(e,Ha(t),(function(t,n,o){n=n||e,t?a(t):i?i(n):r(null,n,o)}))}i(null,r)}function l(e,t){if(s(),ro("process",u.Parser),no("process",u.Compiler),!t)return new Promise(r);function r(r,n){var i=Ha(e);eo.run(u,{file:i},(function(e){e?n(e):r?r(i):t(null,i)}))}r(null,t)}}().freeze(),Ja=[].slice,Ka={}.hasOwnProperty,eo=Wa().use((function(e,t){t.tree=e.parse(t.file)})).use((function(e,t,r){e.run(t.tree,t.file,(function(e,n,i){e?r(e):(t.tree=n,t.file=i,r())}))})).use((function(e,t){t.file.contents=e.stringify(t.tree,t.file)}));function to(e,t){return"function"==typeof e&&e.prototype&&(function(e){var t;for(t in e)return!0;return!1}(e.prototype)||t in e.prototype)}function ro(e,t){if("function"!=typeof t)throw new Error("Cannot `"+e+"` without `Parser`")}function no(e,t){if("function"!=typeof t)throw new Error("Cannot `"+e+"` without `Compiler`")}function io(e,t){if(t)throw new Error("Cannot invoke `"+e+"` on a frozen processor.\nCreate a new processor first, by invoking it: use `processor()` instead of `processor`.")}function ao(e){if(!e||"string"!=typeof e.type)throw new Error("Expected node, got `"+e+"`")}function oo(e,t,r){if(!r)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}var uo=/[|\\{}()[\]^$+*?.]/g,so=function(e){if("string"!=typeof e)throw new TypeError("Expected a string");return e.replace(uo,"\\$&")},co={"---":"yaml","+++":"toml"};var lo=function(e){var t=Object.keys(co).map(so).join("|"),r=e.match(new RegExp("^(".concat(t,")[^\\n\\S]*\\n(?:([\\s\\S]*?)\\n)?\\1[^\\n\\S]*(\\n|$)")));if(null===r)return{frontMatter:null,content:e};var n=r[0].replace(/\n$/,""),i=r[1],a=r[2];return{frontMatter:{type:co[i],value:a,raw:n},content:r[0].replace(/[^\n]/g," ")+e.slice(r[0].length)}},po=["format","prettier"];function fo(e){var t="@(".concat(po.join("|"),")"),r=new RegExp(["\x3c!--\\s*".concat(t,"\\s*--\x3e"),"\x3c!--.*\r?\n[\\s\\S]*(^|\n)[^\\S\n]*".concat(t,"[^\\S\n]*($|\n)[\\s\\S]*\n.*--\x3e")].join("|"),"m"),n=e.match(r);return n&&0===n.index}var ho={startWithPragma:fo,hasPragma:function(e){return fo(lo(e).content.trimLeft())},insertPragma:function(e){var t=lo(e),r="\x3c!-- @".concat(po[0]," --\x3e");return t.frontMatter?"".concat(t.frontMatter.raw,"\n\n").concat(r,"\n\n").concat(t.content):"".concat(r,"\n\n").concat(t.content)}},Do="[\\u02ea-\\u02eb\\u1100-\\u11ff\\u2e80-\\u2e99\\u2e9b-\\u2ef3\\u2f00-\\u2fd5\\u3000-\\u303f\\u3041-\\u3096\\u3099-\\u309f\\u30a1-\\u30fa\\u30fc-\\u30ff\\u3105-\\u312f\\u3131-\\u318e\\u3190-\\u3191\\u3196-\\u31ba\\u31c0-\\u31e3\\u31f0-\\u321e\\u322a-\\u3247\\u3260-\\u327e\\u328a-\\u32b0\\u32c0-\\u32cb\\u32d0-\\u3370\\u337b-\\u337f\\u33e0-\\u33fe\\u3400-\\u4db5\\u4e00-\\u9fef\\ua960-\\ua97c\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\uf900-\\ufa6d\\ufa70-\\ufad9\\ufe10-\\ufe1f\\ufe30-\\ufe6f\\uff00-\\uffef]|[\\ud840-\\ud868\\ud86a-\\ud86c\\ud86f-\\ud872\\ud874-\\ud879][\\udc00-\\udfff]|\\ud82c[\\udc00-\\udd1e\\udd50-\\udd52\\udd64-\\udd67]|\\ud83c[\\ude00\\ude50-\\ude51]|\\ud869[\\udc00-\\uded6\\udf00-\\udfff]|\\ud86d[\\udc00-\\udf34\\udf40-\\udfff]|\\ud86e[\\udc00-\\udc1d\\udc20-\\udfff]|\\ud873[\\udc00-\\udea1\\udeb0-\\udfff]|\\ud87a[\\udc00-\\udfe0]|\\ud87e[\\udc00-\\ude1d]",go="[\\u1100-\\u11ff\\u3001-\\u3003\\u3008-\\u3011\\u3013-\\u301f\\u302e-\\u3030\\u3037\\u30fb\\u3131-\\u318e\\u3200-\\u321e\\u3260-\\u327e\\ua960-\\ua97c\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\ufe45-\\ufe46\\uff61-\\uff65\\uffa0-\\uffbe\\uffc2-\\uffc7\\uffca-\\uffcf\\uffd2-\\uffd7\\uffda-\\uffdc]",mo="[\\u0021-\\u002f\\u003a-\\u0040\\u005b-\\u0060\\u007b-\\u007e\\u00a1\\u00a7\\u00ab\\u00b6-\\u00b7\\u00bb\\u00bf\\u037e\\u0387\\u055a-\\u055f\\u0589-\\u058a\\u05be\\u05c0\\u05c3\\u05c6\\u05f3-\\u05f4\\u0609-\\u060a\\u060c-\\u060d\\u061b\\u061e-\\u061f\\u066a-\\u066d\\u06d4\\u0700-\\u070d\\u07f7-\\u07f9\\u0830-\\u083e\\u085e\\u0964-\\u0965\\u0970\\u09fd\\u0a76\\u0af0\\u0c77\\u0c84\\u0df4\\u0e4f\\u0e5a-\\u0e5b\\u0f04-\\u0f12\\u0f14\\u0f3a-\\u0f3d\\u0f85\\u0fd0-\\u0fd4\\u0fd9-\\u0fda\\u104a-\\u104f\\u10fb\\u1360-\\u1368\\u1400\\u166e\\u169b-\\u169c\\u16eb-\\u16ed\\u1735-\\u1736\\u17d4-\\u17d6\\u17d8-\\u17da\\u1800-\\u180a\\u1944-\\u1945\\u1a1e-\\u1a1f\\u1aa0-\\u1aa6\\u1aa8-\\u1aad\\u1b5a-\\u1b60\\u1bfc-\\u1bff\\u1c3b-\\u1c3f\\u1c7e-\\u1c7f\\u1cc0-\\u1cc7\\u1cd3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205e\\u207d-\\u207e\\u208d-\\u208e\\u2308-\\u230b\\u2329-\\u232a\\u2768-\\u2775\\u27c5-\\u27c6\\u27e6-\\u27ef\\u2983-\\u2998\\u29d8-\\u29db\\u29fc-\\u29fd\\u2cf9-\\u2cfc\\u2cfe-\\u2cff\\u2d70\\u2e00-\\u2e2e\\u2e30-\\u2e4f\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301f\\u3030\\u303d\\u30a0\\u30fb\\ua4fe-\\ua4ff\\ua60d-\\ua60f\\ua673\\ua67e\\ua6f2-\\ua6f7\\ua874-\\ua877\\ua8ce-\\ua8cf\\ua8f8-\\ua8fa\\ua8fc\\ua92e-\\ua92f\\ua95f\\ua9c1-\\ua9cd\\ua9de-\\ua9df\\uaa5c-\\uaa5f\\uaade-\\uaadf\\uaaf0-\\uaaf1\\uabeb\\ufd3e-\\ufd3f\\ufe10-\\ufe19\\ufe30-\\ufe52\\ufe54-\\ufe61\\ufe63\\ufe68\\ufe6a-\\ufe6b\\uff01-\\uff03\\uff05-\\uff0a\\uff0c-\\uff0f\\uff1a-\\uff1b\\uff1f-\\uff20\\uff3b-\\uff3d\\uff3f\\uff5b\\uff5d\\uff5f-\\uff65]|\\ud800[\\udd00-\\udd02\\udf9f\\udfd0]|\\ud801[\\udd6f]|\\ud802[\\udc57\\udd1f\\udd3f\\ude50-\\ude58\\ude7f\\udef0-\\udef6\\udf39-\\udf3f\\udf99-\\udf9c]|\\ud803[\\udf55-\\udf59]|\\ud804[\\udc47-\\udc4d\\udcbb-\\udcbc\\udcbe-\\udcc1\\udd40-\\udd43\\udd74-\\udd75\\uddc5-\\uddc8\\uddcd\\udddb\\udddd-\\udddf\\ude38-\\ude3d\\udea9]|\\ud805[\\udc4b-\\udc4f\\udc5b\\udc5d\\udcc6\\uddc1-\\uddd7\\ude41-\\ude43\\ude60-\\ude6c\\udf3c-\\udf3e]|\\ud806[\\udc3b\\udde2\\ude3f-\\ude46\\ude9a-\\ude9c\\ude9e-\\udea2]|\\ud807[\\udc41-\\udc45\\udc70-\\udc71\\udef7-\\udef8\\udfff]|\\ud809[\\udc70-\\udc74]|\\ud81a[\\ude6e-\\ude6f\\udef5\\udf37-\\udf3b\\udf44]|\\ud81b[\\ude97-\\ude9a\\udfe2]|\\ud82f[\\udc9f]|\\ud836[\\ude87-\\ude8b]|\\ud83a[\\udd5e-\\udd5f]",vo=function(e){return"string"==typeof e?e.replace(function(e){e=Object.assign({onlyFirst:!1},e);var t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:[a-zA-Z\\d]*(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e.onlyFirst?void 0:"g")}(),""):e},bo=vo,yo=vo;bo.default=yo;var Eo=function(e){return!Number.isNaN(e)&&(e>=4352&&(e<=4447||9001===e||9002===e||11904<=e&&e<=12871&&12351!==e||12880<=e&&e<=19903||19968<=e&&e<=42182||43360<=e&&e<=43388||44032<=e&&e<=55203||63744<=e&&e<=64255||65040<=e&&e<=65049||65072<=e&&e<=65131||65281<=e&&e<=65376||65504<=e&&e<=65510||110592<=e&&e<=110593||127488<=e&&e<=127569||131072<=e&&e<=262141))},Co=Eo,Ao=Eo;Co.default=Ao;var wo=function(e){if("string"!=typeof(e=e.replace(/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F|\uD83D\uDC68(?:\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68\uD83C\uDFFB|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|[\u2695\u2696\u2708]\uFE0F|\uD83D[\uDC66\uDC67]|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708])\uFE0F|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C[\uDFFB-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)\uD83C\uDFFB|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB\uDFFC])|\uD83D\uDC69(?:\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB-\uDFFD])|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|(?:(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)\uFE0F|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\u200D[\u2640\u2642])|\uD83C\uDFF4\u200D\u2620)\uFE0F|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF4\uD83C\uDDF2|\uD83C\uDDF6\uD83C\uDDE6|[#\*0-9]\uFE0F\u20E3|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83D\uDC69(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270A-\u270D]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC70\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDCAA\uDD74\uDD7A\uDD90\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD36\uDDB5\uDDB6\uDDBB\uDDD2-\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5\uDEEB\uDEEC\uDEF4-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g,"  "))||0===e.length)return 0;e=bo(e);for(var t=0,r=0;r<e.length;r++){var n=e.codePointAt(r);n<=31||n>=127&&n<=159||(n>=768&&n<=879||(n>65535&&r++,t+=Co(n)?2:1))}return t},ko=wo,To=wo;ko.default=To;function _o(e){return function(t,r,n){var i=n&&n.backwards;if(!1===r)return!1;for(var a=t.length,o=r;o>=0&&o<a;){var u=t.charAt(o);if(e instanceof RegExp){if(!e.test(u))return o}else if(-1===e.indexOf(u))return o;i?o--:o++}return(-1===o||o===a)&&o}}_o(/\s/),_o(" \t"),_o(",; \t"),_o(/[^\r\n]/);var So={};[["|>"],["??"],["||"],["&&"],["|"],["^"],["&"],["==","===","!=","!=="],["<",">","<=",">=","in","instanceof"],[">>","<<",">>>"],["+","-"],["*","/","%"],["**"]].forEach((function(e,t){e.forEach((function(e){So[e]=t}))}));var Fo=Do,xo=go,qo=mo,No=function(e){return e.length>0?e[e.length-1]:null},Lo=["liquidNode","inlineCode","emphasis","strong","delete","link","linkReference","image","imageReference","footnote","footnoteReference","sentence","whitespace","word","break","inlineMath"],Bo=Lo.concat(["tableCell","paragraph","heading"]),Po=new RegExp(xo),Oo=new RegExp(qo);var Ro={mapAst:function(e,t){return function e(r,n,i){var a=t(r,n,i=i||[]);return Array.isArray(a)?a:((a=Object.assign({},a)).children&&(a.children=a.children.reduce((function(t,r,n){var o=e(r,n,[a].concat(i));return Array.isArray(o)||(o=[o]),t.push.apply(t,o),t}),[])),a)}(e,null,null)},splitText:function(e,t){var r="non-cjk",n="cj-letter",i="cjk-punctuation",a=[];return("preserve"===t.proseWrap?e:e.replace(new RegExp("(".concat(Fo,")\n(").concat(Fo,")"),"g"),"$1$2")).split(/([ \t\n]+)/).forEach((function(e,t,u){t%2!=1?(0!==t&&t!==u.length-1||""!==e)&&e.split(new RegExp("(".concat(Fo,")"))).forEach((function(e,t,a){(0!==t&&t!==a.length-1||""!==e)&&(t%2!=0?o(Oo.test(e)?{type:"word",value:e,kind:i,hasLeadingPunctuation:!0,hasTrailingPunctuation:!0}:{type:"word",value:e,kind:Po.test(e)?"k-letter":n,hasLeadingPunctuation:!1,hasTrailingPunctuation:!1}):""!==e&&o({type:"word",value:e,kind:r,hasLeadingPunctuation:Oo.test(e[0]),hasTrailingPunctuation:Oo.test(No(e))}))})):a.push({type:"whitespace",value:/\n/.test(e)?"\n":" "})})),a;function o(e){var t,o,u=No(a);u&&"word"===u.type&&(u.kind===r&&e.kind===n&&!u.hasTrailingPunctuation||u.kind===n&&e.kind===r&&!e.hasLeadingPunctuation?a.push({type:"whitespace",value:" "}):(t=r,o=i,u.kind===t&&e.kind===o||u.kind===o&&e.kind===t||[u.value,e.value].some((function(e){return/\u3000/.test(e)}))||a.push({type:"whitespace",value:""}))),a.push(e)}},punctuationPattern:qo,getFencedCodeBlockValue:function(e,t){var r=t.slice(e.position.start.offset,e.position.end.offset),n=r.match(/^\s*/)[0].length,i=new RegExp("^\\s{0,".concat(n,"}")),a=r.split("\n"),o=r[n],u=r.slice(n).match(new RegExp("^[".concat(o,"]+")))[0],s=new RegExp("^\\s{0,3}".concat(u)).test(a[a.length-1].slice(c(a.length-1)));return a.slice(1,s?-1:void 0).map((function(e,t){return e.slice(c(t+1)).replace(i,"")})).join("\n");function c(t){return e.position.indent[t-1]-1}},getOrderedListItemInfo:function(e,t){var r=l(t.slice(e.position.start.offset,e.position.end.offset).match(/^\s*(\d+)(\.|\))(\s*)/),4);return{numberText:r[1],marker:r[2],leadingSpaces:r[3]}},INLINE_NODE_TYPES:Lo,INLINE_NODE_WRAPPER_TYPES:Bo},Io=/^import\s/,Uo=/^export\s/,$o=function(e){return Io.test(e)},Vo=function(e){return Uo.test(e)},Mo=function(e,t){var r=t.indexOf("\n\n"),n=t.slice(0,r);if(Vo(n)||$o(n))return e(n)({type:Vo(n)?"export":"import",value:n})};Mo.locator=function(e){return Vo(e)||$o(e)?-1:1};var jo={esSyntax:function(){var e=this.Parser,t=e.prototype.blockTokenizers,r=e.prototype.blockMethods;t.esSyntax=Mo,r.splice(r.indexOf("paragraph"),0,"esSyntax")},BLOCKS_REGEX:"[a-z\\.]*(\\.){0,1}[a-z][a-z0-9\\.]*",COMMENT_REGEX:"\x3c!----\x3e|\x3c!--(?:-?[^>-])(?:-?[^-])*--\x3e"};function zo(e,t){return e.indexOf("$",t)}var Go=/^\\\$/,Ho=/^\$((?:\\\$|[^$])+)\$/,Xo=/^\$\$((?:\\\$|[^$])+)\$\$/,Wo=function(e){function t(t,r,n){var i=!0,a=Xo.exec(r);a||(a=Ho.exec(r),i=!1);var o=Go.exec(r);if(o)return!!n||t(o[0])({type:"text",value:"$"});if("\\$"===r.slice(-2))return t(r)({type:"text",value:r.slice(0,-2)+"$"});if(a){if(n)return!0;if(a[0].includes("`")&&r.slice(a[0].length).includes("`")){var u=r.slice(0,r.indexOf("`"));return t(u)({type:"text",value:u})}var s=a[1].trim();return t(a[0])({type:"inlineMath",value:s,data:{hName:"span",hProperties:{className:"inlineMath"+(i&&e.inlineMathDouble?" inlineMathDouble":"")},hChildren:[{type:"text",value:s}]}})}}t.locator=zo;var r=this.Parser,n=r.prototype.inlineTokenizers,i=r.prototype.inlineMethods;n.math=t,i.splice(i.indexOf("text"),0,"math");var a=this.Compiler;null!=a&&(a.prototype.visitors.inlineMath=function(e){return"$"+e.value+"$"})},Qo="\n",Yo="\t",Zo=" ",Jo="$",Ko=2,eu=4,tu=function(e){var t=this.Parser,r=t.prototype.blockTokenizers,n=t.prototype.blockMethods;r.math=function(e,t,r){for(var n,i,a,o,u,s,c,l,p,f,h=t.length+1,d=0,D="";d<h&&((a=t.charAt(d))===Zo||a===Yo);)D+=a,d++;if(p=d,(a=t.charAt(d))===Jo){for(d++,i=a,n=1,D+=a;d<h&&(a=t.charAt(d))===i;)D+=a,n++,d++;if(!(n<Ko)){for(;d<h&&(a=t.charAt(d))!==Qo;){if(a===Jo)return;D+=a,d++}if(a=t.charAt(d),r)return!0;for((f=e.now()).column+=D.length,f.offset+=D.length,o=c=l=u=s="";d<h;)if(u+=c,s+=l,c=l="",(a=t.charAt(d))===Qo){for(u?(c+=a,l+=a):D+=a,o="",d++;d<h&&(a=t.charAt(d))===Zo;)o+=a,d++;if(c+=o,l+=o.slice(p),!(o.length>=eu)){for(o="";d<h&&(a=t.charAt(d))===i;)o+=a,d++;if(c+=o,l+=o,!(o.length<n)){for(o="";d<h&&(a=t.charAt(d))!==Qo;)c+=a,l+=a,d++;break}}}else u+=a,l+=a,d++;D+=u+c;var g=ze(s);return e(D)({type:"math",value:g,data:{hName:"div",hProperties:{className:"math"},hChildren:[{type:"text",value:g}]}})}}},n.splice(n.indexOf("fencedCode")+1,0,"math");var i=t.prototype.interruptParagraph,a=t.prototype.interruptList,o=t.prototype.interruptBlockquote;i.splice(i.indexOf("fencedCode")+1,0,["math"]),a.splice(a.indexOf("fencedCode")+1,0,["math"]),o.splice(o.indexOf("fencedCode")+1,0,["math"]);var u=this.Compiler;null!=u&&(u.prototype.visitors.math=function(e){return"$$\n"+e.value+"\n$$"})},ru=function(e){null==e&&(e={}),tu.call(this,e),Wo.call(this,e)},nu={area:"none",base:"none",basefont:"none",datalist:"none",head:"none",link:"none",meta:"none",noembed:"none",noframes:"none",param:"none",rp:"none",script:"block",source:"block",style:"none",template:"inline",track:"block",title:"none",html:"block",body:"block",address:"block",blockquote:"block",center:"block",div:"block",figure:"block",figcaption:"block",footer:"block",form:"block",header:"block",hr:"block",legend:"block",listing:"block",main:"block",p:"block",plaintext:"block",pre:"block",xmp:"block",slot:"contents",ruby:"ruby",rt:"ruby-text",article:"block",aside:"block",h1:"block",h2:"block",h3:"block",h4:"block",h5:"block",h6:"block",hgroup:"block",nav:"block",section:"block",dir:"block",dd:"block",dl:"block",dt:"block",ol:"block",ul:"block",li:"list-item",table:"table",caption:"table-caption",colgroup:"table-column-group",col:"table-column",thead:"table-header-group",tbody:"table-row-group",tfoot:"table-footer-group",tr:"table-row",td:"table-cell",th:"table-cell",fieldset:"block",button:"inline-block",video:"inline-block",audio:"inline-block"},iu="inline",au={listing:"pre",plaintext:"pre",pre:"pre",xmp:"pre",nobr:"nowrap",table:"initial",textarea:"pre-wrap"},ou="normal",uu=Object.freeze({__proto__:null,default:["a","abbr","acronym","address","applet","area","article","aside","audio","b","base","basefont","bdi","bdo","bgsound","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","command","content","data","datalist","dd","del","details","dfn","dialog","dir","div","dl","dt","element","em","embed","fieldset","figcaption","figure","font","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","image","img","input","ins","isindex","kbd","keygen","label","legend","li","link","listing","main","map","mark","marquee","math","menu","menuitem","meta","meter","multicol","nav","nextid","nobr","noembed","noframes","noscript","object","ol","optgroup","option","output","p","param","picture","plaintext","pre","progress","q","rb","rbc","rp","rt","rtc","ruby","s","samp","script","section","select","shadow","slot","small","source","spacer","span","strike","strong","style","sub","summary","sup","svg","table","tbody","td","template","textarea","tfoot","th","thead","time","title","tr","track","tt","u","ul","var","video","wbr","xmp"]}),su=["accesskey","charset","coords","download","href","hreflang","name","ping","referrerpolicy","rel","rev","shape","tabindex","target","type"],cu=["title"],lu=["align","alt","archive","code","codebase","height","hspace","name","object","vspace","width"],pu=["accesskey","alt","coords","download","href","hreflang","nohref","ping","referrerpolicy","rel","shape","tabindex","target","type"],fu=["autoplay","controls","crossorigin","loop","muted","preload","src"],hu=["href","target"],du=["color","face","size"],Du=["dir"],gu=["cite"],mu=["alink","background","bgcolor","link","text","vlink"],vu=["clear"],bu=["accesskey","autofocus","disabled","form","formaction","formenctype","formmethod","formnovalidate","formtarget","name","tabindex","type","value"],yu=["height","width"],Eu=["align"],Cu=["align","char","charoff","span","valign","width"],Au=["align","char","charoff","span","valign","width"],wu=["value"],ku=["cite","datetime"],Tu=["open"],_u=["title"],Su=["open"],Fu=["compact"],xu=["align"],qu=["compact"],Nu=["height","src","type","width"],Lu=["disabled","form","name"],Bu=["color","face","size"],Pu=["accept","accept-charset","action","autocomplete","enctype","method","name","novalidate","target"],Ou=["frameborder","longdesc","marginheight","marginwidth","name","noresize","scrolling","src"],Ru=["cols","rows"],Iu=["align"],Uu=["align"],$u=["align"],Vu=["align"],Mu=["align"],ju=["align"],zu=["profile"],Gu=["align","noshade","size","width"],Hu=["manifest","version"],Xu=["align","allow","allowfullscreen","allowpaymentrequest","allowusermedia","frameborder","height","longdesc","marginheight","marginwidth","name","referrerpolicy","sandbox","scrolling","src","srcdoc","width"],Wu=["align","alt","border","crossorigin","decoding","height","hspace","ismap","longdesc","name","referrerpolicy","sizes","src","srcset","usemap","vspace","width"],Qu=["accept","accesskey","align","alt","autocomplete","autofocus","checked","dirname","disabled","form","formaction","formenctype","formmethod","formnovalidate","formtarget","height","ismap","list","max","maxlength","min","minlength","multiple","name","pattern","placeholder","readonly","required","size","src","step","tabindex","title","type","usemap","value","width"],Yu=["cite","datetime"],Zu=["prompt"],Ju=["accesskey","for","form"],Ku=["accesskey","align"],es=["type","value"],ts=["as","charset","color","crossorigin","href","hreflang","imagesizes","imagesrcset","integrity","media","nonce","referrerpolicy","rel","rev","sizes","target","title","type"],rs=["name"],ns=["compact"],is=["charset","content","http-equiv","name","scheme"],as=["high","low","max","min","optimum","value"],os=["align","archive","border","classid","codebase","codetype","data","declare","form","height","hspace","name","standby","tabindex","type","typemustmatch","usemap","vspace","width"],us=["compact","reversed","start","type"],ss=["disabled","label"],cs=["disabled","label","selected","value"],ls=["for","form","name"],ps=["align"],fs=["name","type","value","valuetype"],hs=["width"],ds=["max","value"],Ds=["cite"],gs=["async","charset","crossorigin","defer","integrity","language","nomodule","nonce","referrerpolicy","src","type"],ms=["autocomplete","autofocus","disabled","form","multiple","name","required","size","tabindex"],vs=["name"],bs=["media","sizes","src","srcset","type"],ys=["media","nonce","title","type"],Es=["align","bgcolor","border","cellpadding","cellspacing","frame","rules","summary","width"],Cs=["align","char","charoff","valign"],As=["abbr","align","axis","bgcolor","char","charoff","colspan","headers","height","nowrap","rowspan","scope","valign","width"],ws=["accesskey","autocomplete","autofocus","cols","dirname","disabled","form","maxlength","minlength","name","placeholder","readonly","required","rows","tabindex","wrap"],ks=["align","char","charoff","valign"],Ts=["abbr","align","axis","bgcolor","char","charoff","colspan","headers","height","nowrap","rowspan","scope","valign","width"],_s=["align","char","charoff","valign"],Ss=["datetime"],Fs=["align","bgcolor","char","charoff","valign"],xs=["default","kind","label","src","srclang"],qs=["compact","type"],Ns=["autoplay","controls","crossorigin","height","loop","muted","playsinline","poster","preload","src","width"],Ls={"*":["accesskey","autocapitalize","autofocus","class","contenteditable","dir","draggable","enterkeyhint","hidden","id","inputmode","is","itemid","itemprop","itemref","itemscope","itemtype","lang","nonce","slot","spellcheck","style","tabindex","title","translate"],a:su,abbr:cu,applet:lu,area:pu,audio:fu,base:hu,basefont:du,bdo:Du,blockquote:gu,body:mu,br:vu,button:bu,canvas:yu,caption:Eu,col:Cu,colgroup:Au,data:wu,del:ku,details:Tu,dfn:_u,dialog:Su,dir:Fu,div:xu,dl:qu,embed:Nu,fieldset:Lu,font:Bu,form:Pu,frame:Ou,frameset:Ru,h1:Iu,h2:Uu,h3:$u,h4:Vu,h5:Mu,h6:ju,head:zu,hr:Gu,html:Hu,iframe:Xu,img:Wu,input:Qu,ins:Yu,isindex:Zu,label:Ju,legend:Ku,li:es,link:ts,map:rs,menu:ns,meta:is,meter:as,object:os,ol:us,optgroup:ss,option:cs,output:ls,p:ps,param:fs,pre:hs,progress:ds,q:Ds,script:gs,select:ms,slot:vs,source:bs,style:ys,table:Es,tbody:Cs,td:As,textarea:ws,tfoot:ks,th:Ts,thead:_s,time:Ss,tr:Fs,track:xs,ul:qs,video:Ns},Bs=Object.freeze({__proto__:null,a:su,abbr:cu,applet:lu,area:pu,audio:fu,base:hu,basefont:du,bdo:Du,blockquote:gu,body:mu,br:vu,button:bu,canvas:yu,caption:Eu,col:Cu,colgroup:Au,data:wu,del:ku,details:Tu,dfn:_u,dialog:Su,dir:Fu,div:xu,dl:qu,embed:Nu,fieldset:Lu,font:Bu,form:Pu,frame:Ou,frameset:Ru,h1:Iu,h2:Uu,h3:$u,h4:Vu,h5:Mu,h6:ju,head:zu,hr:Gu,html:Hu,iframe:Xu,img:Wu,input:Qu,ins:Yu,isindex:Zu,label:Ju,legend:Ku,li:es,link:ts,map:rs,menu:ns,meta:is,meter:as,object:os,ol:us,optgroup:ss,option:cs,output:ls,p:ps,param:fs,pre:hs,progress:ds,q:Ds,script:gs,select:ms,slot:vs,source:bs,style:ys,table:Es,tbody:Cs,td:As,textarea:ws,tfoot:ks,th:Ts,thead:_s,time:Ss,tr:Fs,track:xs,ul:qs,video:Ns,default:Ls}),Ps=D(uu),Os=D(Bs),Rs=nu,Is=iu,Us=au,$s=ou,Vs=Ms(Ps);function Ms(e){var t=Object.create(null),r=!0,n=!1,i=void 0;try{for(var a,o=e[Symbol.iterator]();!(r=(a=o.next()).done);r=!0){t[a.value]=!0}}catch(e){n=!0,i=e}finally{try{r||null==o.return||o.return()}finally{if(n)throw i}}return t}function js(e,t){return!("element"!==e.type||"template"!==e.fullName||!e.attrMap.lang||"html"===e.attrMap.lang)||(!("ieConditionalComment"!==e.type||!e.lastChild||e.lastChild.isSelfClosing||e.lastChild.endSourceSpan)||("ieConditionalComment"===e.type&&!e.complete||("vue"===t.parser&&"element"===e.type&&"root"===e.parent.type&&-1===["template","style","script","html"].indexOf(e.fullName)||!(!rc(e)||!e.children.some((function(e){return"text"!==e.type&&"interpolation"!==e.type}))))))}function zs(e){return"attribute"!==e.type&&!Gs(e)&&(!!e.parent&&("number"==typeof e.index&&0!==e.index&&function(e){return"comment"===e.type&&"prettier-ignore"===e.value.trim()}(e.parent.children[e.index-1])))}function Gs(e){return"text"===e.type||"comment"===e.type}function Hs(e){return"element"===e.type&&("script"===e.fullName||"style"===e.fullName||"svg:style"===e.fullName)}function Xs(e){return"yaml"===e.type||"toml"===e.type}function Ws(e){return nc(e).startsWith("pre")}function Qs(e){return"element"===e.type&&0!==e.children.length&&(-1!==["html","head","ul","ol","select"].indexOf(e.name)||e.cssDisplay.startsWith("table")&&"table-cell"!==e.cssDisplay)}function Ys(e){return ec(e)||"element"===e.type&&"br"===e.fullName||Zs(e)}function Zs(e){return Js(e)&&Ks(e)}function Js(e){return e.hasLeadingSpaces&&(e.prev?e.prev.sourceSpan.end.line<e.sourceSpan.start.line:"root"===e.parent.type||e.parent.startSourceSpan.end.line<e.sourceSpan.start.line)}function Ks(e){return e.hasTrailingSpaces&&(e.next?e.next.sourceSpan.start.line>e.sourceSpan.end.line:"root"===e.parent.type||e.parent.endSourceSpan.start.line>e.sourceSpan.end.line)}function ec(e){switch(e.type){case"ieConditionalComment":case"comment":case"directive":return!0;case"element":return-1!==["script","select"].indexOf(e.name)}return!1}function tc(e){return"block"===e||"list-item"===e||e.startsWith("table")}function rc(e){return nc(e).startsWith("pre")}function nc(e){return"element"===e.type&&!e.namespace&&Us[e.name]||$s}var ic={HTML_ELEMENT_ATTRIBUTES:function(e,t){for(var r=Object.create(null),n=0,i=Object.keys(e);n<i.length;n++){var a=i[n];r[a]=t(e[a],a)}return r}(Os,Ms),HTML_TAGS:Vs,canHaveInterpolation:function(e){return e.children&&!Hs(e)},countChars:function(e,t){for(var r=0,n=0;n<e.length;n++)e[n]===t&&r++;return r},countParents:function(e){for(var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){return!0},n=0,i=e.stack.length-1;i>=0;i--){var a=e.stack[i];a&&"object"===t(a)&&!Array.isArray(a)&&r(a)&&n++}return n},dedentString:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(e){var t=1/0,r=!0,n=!1,i=void 0;try{for(var a,o=e.split("\n")[Symbol.iterator]();!(r=(a=o.next()).done);r=!0){var u=a.value;if(0!==u.length){if(/\S/.test(u[0]))return 0;var s=u.match(/^\s*/)[0].length;u.length!==s&&s<t&&(t=s)}}}catch(e){n=!0,i=e}finally{try{r||null==o.return||o.return()}finally{if(n)throw i}}return t===1/0?0:t}(e);return 0===t?e:e.split("\n").map((function(e){return e.slice(t)})).join("\n")},forceBreakChildren:Qs,forceBreakContent:function(e){return Qs(e)||"element"===e.type&&0!==e.children.length&&(-1!==["body","script","style"].indexOf(e.name)||e.children.some((function(e){return function(e){return e.children&&e.children.some((function(e){return"text"!==e.type}))}(e)})))||e.firstChild&&e.firstChild===e.lastChild&&Js(e.firstChild)&&(!e.lastChild.isTrailingSpaceSensitive||Ks(e.lastChild))},forceNextEmptyLine:function(e){return Xs(e)||e.next&&e.sourceSpan.end.line+1<e.next.sourceSpan.start.line},getLastDescendant:function e(t){return t.lastChild?e(t.lastChild):t},getNodeCssStyleDisplay:function(e,t){if(e.prev&&"comment"===e.prev.type){var r=e.prev.value.match(/^\s*display:\s*([a-z]+)\s*$/);if(r)return r[1]}var n=!1;if("element"===e.type&&"svg"===e.namespace){if(!function(e,t){for(var r=e;r;){if(t(r))return!0;r=r.parent}return!1}(e,(function(e){return"svg:foreignObject"===e.fullName})))return"svg"===e.name?"inline-block":"block";n=!0}switch(t.htmlWhitespaceSensitivity){case"strict":return"inline";case"ignore":return"block";default:return"element"===e.type&&(!e.namespace||n)&&Rs[e.name]||Is}},getNodeCssStyleWhiteSpace:nc,getPrettierIgnoreAttributeCommentData:function(e){var t=e.trim().match(/^prettier-ignore-attribute(?:\s+([^]+))?$/);return!!t&&(!t[1]||t[1].split(/\s+/))},hasPrettierIgnore:zs,identity:function(e){return e},inferScriptParser:function(e){if("script"===e.name&&!e.attrMap.src){if(!e.attrMap.lang&&!e.attrMap.type||"module"===e.attrMap.type||"text/javascript"===e.attrMap.type||"text/babel"===e.attrMap.type||"application/javascript"===e.attrMap.type)return"babel";if("application/x-typescript"===e.attrMap.type||"ts"===e.attrMap.lang||"tsx"===e.attrMap.lang)return"typescript";if("text/markdown"===e.attrMap.type)return"markdown";if(e.attrMap.type.endsWith("json")||e.attrMap.type.endsWith("importmap"))return"json"}if("style"===e.name){if(!e.attrMap.lang||"postcss"===e.attrMap.lang||"css"===e.attrMap.lang)return"css";if("scss"===e.attrMap.lang)return"scss";if("less"===e.attrMap.lang)return"less"}return null},isDanglingSpaceSensitiveNode:function(e){return!tc(t=e.cssDisplay)&&"inline-block"!==t&&!Hs(e);var t},isFrontMatterNode:Xs,isIndentationSensitiveNode:Ws,isLeadingSpaceSensitiveNode:function(e){var t=function(){if(Xs(e))return!1;if(("text"===e.type||"interpolation"===e.type)&&e.prev&&("text"===e.prev.type||"interpolation"===e.prev.type))return!0;if(!e.parent||"none"===e.parent.cssDisplay)return!1;if(rc(e.parent))return!0;if(!e.prev&&("root"===e.parent.type||Hs(e.parent)||(t=e.parent.cssDisplay,tc(t)||"inline-block"===t)))return!1;var t;if(e.prev&&!function(e){return!tc(e)}(e.prev.cssDisplay))return!1;return!0}();return t&&!e.prev&&e.parent&&e.parent.tagDefinition&&e.parent.tagDefinition.ignoreFirstLf?"interpolation"===e.type:t},isPreLikeNode:rc,isScriptLikeTag:Hs,isTextLikeNode:Gs,isTrailingSpaceSensitiveNode:function(e){return!Xs(e)&&(!("text"!==e.type&&"interpolation"!==e.type||!e.next||"text"!==e.next.type&&"interpolation"!==e.next.type)||!(!e.parent||"none"===e.parent.cssDisplay)&&(!!rc(e.parent)||!(!e.next&&("root"===e.parent.type||Hs(e.parent)||(t=e.parent.cssDisplay,tc(t)||"inline-block"===t)))&&!(e.next&&!function(e){return!tc(e)}(e.next.cssDisplay))));var t},isWhitespaceSensitiveNode:function(e){return Hs(e)||"interpolation"===e.type||Ws(e)},normalizeParts:function(e){for(var t=[],r=e.slice();0!==r.length;){var n=r.shift();n&&("concat"!==n.type?0===t.length||"string"!=typeof t[t.length-1]||"string"!=typeof n?t.push(n):t.push(t.pop()+n):Array.prototype.unshift.apply(r,n.parts))}return t},preferHardlineAsLeadingSpaces:function(e){return ec(e)||e.prev&&Ys(e.prev)||Zs(e)},preferHardlineAsTrailingSpaces:Ys,shouldNotPrintClosingTag:function(e,t){return!e.isSelfClosing&&!e.endSourceSpan&&(zs(e)||js(e.parent,t))},shouldPreserveContent:js,unescapeQuoteEntities:function(e){return e.replace(/&apos;/g,"'").replace(/&quot;/g,'"')}};var ac=function(e){return/^\s*<!--\s*@(format|prettier)\s*-->/.test(e)};var oc=function(e,t){var r=new SyntaxError(e+" ("+t.start.line+":"+t.start.column+")");return r.loc=t,r},uc={attrs:!0,children:!0},sc=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};r(this,e);for(var n=0,i=Object.keys(t);n<i.length;n++){var a=i[n],o=t[a];a in uc?this._setNodes(a,o):this[a]=o}}return i(e,[{key:"_setNodes",value:function(e,t){t!==this[e]&&(this[e]=lc(t,this),"attrs"===e&&pc(this,{attrMap:this[e].reduce((function(e,t){return e[t.fullName]=t.value,e}),Object.create(null))}))}},{key:"map",value:function(t){var r=null;for(var n in uc){var i=this[n];if(i){var a=cc(i,(function(e){return e.map(t)}));r!==i&&(r||(r=new e),r._setNodes(n,a))}}if(r){for(var o in this)o in uc||(r[o]=this[o]);pc(r,{index:this.index,siblings:this.siblings,prev:this.prev,next:this.next,parent:this.parent})}return t(r||this)}},{key:"clone",value:function(t){return new e(t?Object.assign({},this,t):this)}},{key:"firstChild",get:function(){return this.children&&0!==this.children.length?this.children[0]:null}},{key:"lastChild",get:function(){return this.children&&0!==this.children.length?this.children[this.children.length-1]:null}},{key:"rawName",get:function(){return this.hasExplicitNamespace?this.fullName:this.name}},{key:"fullName",get:function(){return this.namespace?this.namespace+":"+this.name:this.name}}]),e}();function cc(e,t){var r=e.map(t);return r.some((function(t,r){return t!==e[r]}))?r:e}function lc(e,t){for(var r=e.map((function(e){return e instanceof sc?e.clone():new sc(e)})),n=null,i=r[0],a=r[1]||null,o=0;o<r.length;o++)pc(i,{index:o,siblings:r,prev:n,next:a,parent:t}),n=i,i=a,a=r[o+2]||null;return r}function pc(e,t){var r=Object.keys(t).reduce((function(e,r){return e[r]={value:t[r],enumerable:!1},e}),{});Object.defineProperties(e,r)}var fc={Node:sc},hc=[[/^(\[if([^\]]*?)\]>)([\s\S]*?)<!\s*\[endif\]$/,function(e,t,r){var n=l(r,4),i=n[1],a=n[2],o=n[3],u="\x3c!--".length+i.length,s=e.sourceSpan.start.moveBy(u),c=s.moveBy(o.length),p=e.sourceSpan.constructor,f=l(function(){try{return[!0,t(o,s).children]}catch(e){return[!1,[{type:"text",value:o,sourceSpan:new p(s,c)}]]}}(),2),h=f[0],d=f[1];return{type:"ieConditionalComment",complete:h,children:d,condition:a.trim().replace(/\s+/g," "),sourceSpan:e.sourceSpan,startSourceSpan:new p(e.sourceSpan.start,s),endSourceSpan:new p(c,e.sourceSpan.end)}}],[/^\[if([^\]]*?)\]><!$/,function(e,t,r){return{type:"ieConditionalStartComment",condition:l(r,2)[1].trim().replace(/\s+/g," "),sourceSpan:e.sourceSpan}}],[/^<!\s*\[endif\]$/,function(e){return{type:"ieConditionalEndComment",sourceSpan:e.sourceSpan}}]];var dc=function(e,t){if(e.value){var r,n=!0,i=!1,a=void 0;try{for(var o,u=hc[Symbol.iterator]();!(n=(o=u.next()).done);n=!0){var s=l(o.value,2),c=s[0],p=s[1];if(r=e.value.match(c))return p(e,t,r)}}catch(e){i=!0,a=e}finally{try{n||null==u.return||u.return()}finally{if(i)throw a}}}return null},Dc=d((function(e,t){function r(e){if(":"!=e[0])return[null,e];var t=e.indexOf(":",1);if(-1==t)throw new Error('Unsupported format "'.concat(e,'" expecting ":namespace:name"'));return[e.slice(1,t),e.slice(t+1)]}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0}),function(e){e[e.RAW_TEXT=0]="RAW_TEXT",e[e.ESCAPABLE_RAW_TEXT=1]="ESCAPABLE_RAW_TEXT",e[e.PARSABLE_DATA=2]="PARSABLE_DATA"}(t.TagContentType||(t.TagContentType={})),t.splitNsName=r,t.isNgContainer=function(e){return"ng-container"===r(e)[1]},t.isNgContent=function(e){return"ng-content"===r(e)[1]},t.isNgTemplate=function(e){return"ng-template"===r(e)[1]},t.getNsPrefix=function(e){return null===e?null:r(e)[0]},t.mergeNsAndName=function(e,t){return e?":".concat(e,":").concat(t):t},t.NAMED_ENTITIES={Aacute:"Á",aacute:"á",Abreve:"Ă",abreve:"ă",ac:"∾",acd:"∿",acE:"∾̳",Acirc:"Â",acirc:"â",acute:"´",Acy:"А",acy:"а",AElig:"Æ",aelig:"æ",af:"⁡",Afr:"𝔄",afr:"𝔞",Agrave:"À",agrave:"à",alefsym:"ℵ",aleph:"ℵ",Alpha:"Α",alpha:"α",Amacr:"Ā",amacr:"ā",amalg:"⨿",AMP:"&",amp:"&",And:"⩓",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"Å",angzarr:"⍼",Aogon:"Ą",aogon:"ą",Aopf:"𝔸",aopf:"𝕒",ap:"≈",apacir:"⩯",apE:"⩰",ape:"≊",apid:"≋",apos:"'",ApplyFunction:"⁡",approx:"≈",approxeq:"≊",Aring:"Å",aring:"å",Ascr:"𝒜",ascr:"𝒶",Assign:"≔",ast:"*",asymp:"≈",asympeq:"≍",Atilde:"Ã",atilde:"ã",Auml:"Ä",auml:"ä",awconint:"∳",awint:"⨑",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",Backslash:"∖",Barv:"⫧",barvee:"⊽",Barwed:"⌆",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",Bcy:"Б",bcy:"б",bdquo:"„",becaus:"∵",Because:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",Bernoullis:"ℬ",Beta:"Β",beta:"β",beth:"ℶ",between:"≬",Bfr:"𝔅",bfr:"𝔟",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bNot:"⫭",bnot:"⌐",Bopf:"𝔹",bopf:"𝕓",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxbox:"⧉",boxDL:"╗",boxDl:"╖",boxdL:"╕",boxdl:"┐",boxDR:"╔",boxDr:"╓",boxdR:"╒",boxdr:"┌",boxH:"═",boxh:"─",boxHD:"╦",boxHd:"╤",boxhD:"╥",boxhd:"┬",boxHU:"╩",boxHu:"╧",boxhU:"╨",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxUL:"╝",boxUl:"╜",boxuL:"╛",boxul:"┘",boxUR:"╚",boxUr:"╙",boxuR:"╘",boxur:"└",boxV:"║",boxv:"│",boxVH:"╬",boxVh:"╫",boxvH:"╪",boxvh:"┼",boxVL:"╣",boxVl:"╢",boxvL:"╡",boxvl:"┤",boxVR:"╠",boxVr:"╟",boxvR:"╞",boxvr:"├",bprime:"‵",Breve:"˘",breve:"˘",brvbar:"¦",Bscr:"ℬ",bscr:"𝒷",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",Bumpeq:"≎",bumpeq:"≏",Cacute:"Ć",cacute:"ć",Cap:"⋒",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",CapitalDifferentialD:"ⅅ",caps:"∩︀",caret:"⁁",caron:"ˇ",Cayleys:"ℭ",ccaps:"⩍",Ccaron:"Č",ccaron:"č",Ccedil:"Ç",ccedil:"ç",Ccirc:"Ĉ",ccirc:"ĉ",Cconint:"∰",ccups:"⩌",ccupssm:"⩐",Cdot:"Ċ",cdot:"ċ",cedil:"¸",Cedilla:"¸",cemptyv:"⦲",cent:"¢",CenterDot:"·",centerdot:"·",Cfr:"ℭ",cfr:"𝔠",CHcy:"Ч",chcy:"ч",check:"✓",checkmark:"✓",Chi:"Χ",chi:"χ",cir:"○",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",CircleDot:"⊙",circledR:"®",circledS:"Ⓢ",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",cirE:"⧃",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",clubs:"♣",clubsuit:"♣",Colon:"∷",colon:":",Colone:"⩴",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",Congruent:"≡",Conint:"∯",conint:"∮",ContourIntegral:"∮",Copf:"ℂ",copf:"𝕔",coprod:"∐",Coproduct:"∐",COPY:"©",copy:"©",copysr:"℗",CounterClockwiseContourIntegral:"∳",crarr:"↵",Cross:"⨯",cross:"✗",Cscr:"𝒞",cscr:"𝒸",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",Cup:"⋓",cup:"∪",cupbrcap:"⩈",CupCap:"≍",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"¤",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",Dagger:"‡",dagger:"†",daleth:"ℸ",Darr:"↡",dArr:"⇓",darr:"↓",dash:"‐",Dashv:"⫤",dashv:"⊣",dbkarow:"⤏",dblac:"˝",Dcaron:"Ď",dcaron:"ď",Dcy:"Д",dcy:"д",DD:"ⅅ",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",DDotrahd:"⤑",ddotseq:"⩷",deg:"°",Del:"∇",Delta:"Δ",delta:"δ",demptyv:"⦱",dfisht:"⥿",Dfr:"𝔇",dfr:"𝔡",dHar:"⥥",dharl:"⇃",dharr:"⇂",DiacriticalAcute:"´",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",diam:"⋄",Diamond:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"¨",DifferentialD:"ⅆ",digamma:"ϝ",disin:"⋲",div:"÷",divide:"÷",divideontimes:"⋇",divonx:"⋇",DJcy:"Ђ",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",Dopf:"𝔻",dopf:"𝕕",Dot:"¨",dot:"˙",DotDot:"⃜",doteq:"≐",doteqdot:"≑",DotEqual:"≐",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",DoubleContourIntegral:"∯",DoubleDot:"¨",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",Downarrow:"⇓",downarrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",Dscr:"𝒟",dscr:"𝒹",DScy:"Ѕ",dscy:"ѕ",dsol:"⧶",Dstrok:"Đ",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",DZcy:"Џ",dzcy:"џ",dzigrarr:"⟿",Eacute:"É",eacute:"é",easter:"⩮",Ecaron:"Ě",ecaron:"ě",ecir:"≖",Ecirc:"Ê",ecirc:"ê",ecolon:"≕",Ecy:"Э",ecy:"э",eDDot:"⩷",Edot:"Ė",eDot:"≑",edot:"ė",ee:"ⅇ",efDot:"≒",Efr:"𝔈",efr:"𝔢",eg:"⪚",Egrave:"È",egrave:"è",egs:"⪖",egsdot:"⪘",el:"⪙",Element:"∈",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",Emacr:"Ē",emacr:"ē",empty:"∅",emptyset:"∅",EmptySmallSquare:"◻",emptyv:"∅",EmptyVerySmallSquare:"▫",emsp:" ",emsp13:" ",emsp14:" ",ENG:"Ŋ",eng:"ŋ",ensp:" ",Eogon:"Ę",eogon:"ę",Eopf:"𝔼",eopf:"𝕖",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",Epsilon:"Ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",Equal:"⩵",equals:"=",EqualTilde:"≂",equest:"≟",Equilibrium:"⇌",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erarr:"⥱",erDot:"≓",Escr:"ℰ",escr:"ℯ",esdot:"≐",Esim:"⩳",esim:"≂",Eta:"Η",eta:"η",ETH:"Ð",eth:"ð",Euml:"Ë",euml:"ë",euro:"€",excl:"!",exist:"∃",Exists:"∃",expectation:"ℰ",ExponentialE:"ⅇ",exponentiale:"ⅇ",fallingdotseq:"≒",Fcy:"Ф",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",Ffr:"𝔉",ffr:"𝔣",filig:"ﬁ",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",Fopf:"𝔽",fopf:"𝕗",ForAll:"∀",forall:"∀",fork:"⋔",forkv:"⫙",Fouriertrf:"ℱ",fpartint:"⨍",frac12:"½",frac13:"⅓",frac14:"¼",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"¾",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",Fscr:"ℱ",fscr:"𝒻",gacute:"ǵ",Gamma:"Γ",gamma:"γ",Gammad:"Ϝ",gammad:"ϝ",gap:"⪆",Gbreve:"Ğ",gbreve:"ğ",Gcedil:"Ģ",Gcirc:"Ĝ",gcirc:"ĝ",Gcy:"Г",gcy:"г",Gdot:"Ġ",gdot:"ġ",gE:"≧",ge:"≥",gEl:"⪌",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",Gfr:"𝔊",gfr:"𝔤",Gg:"⋙",gg:"≫",ggg:"⋙",gimel:"ℷ",GJcy:"Ѓ",gjcy:"ѓ",gl:"≷",gla:"⪥",glE:"⪒",glj:"⪤",gnap:"⪊",gnapprox:"⪊",gnE:"≩",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",Gopf:"𝔾",gopf:"𝕘",grave:"`",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"𝒢",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",GT:">",Gt:"≫",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",Hacek:"ˇ",hairsp:" ",half:"½",hamilt:"ℋ",HARDcy:"Ъ",hardcy:"ъ",hArr:"⇔",harr:"↔",harrcir:"⥈",harrw:"↭",Hat:"^",hbar:"ℏ",Hcirc:"Ĥ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",Hfr:"ℌ",hfr:"𝔥",HilbertSpace:"ℋ",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",Hopf:"ℍ",hopf:"𝕙",horbar:"―",HorizontalLine:"─",Hscr:"ℋ",hscr:"𝒽",hslash:"ℏ",Hstrok:"Ħ",hstrok:"ħ",HumpDownHump:"≎",HumpEqual:"≏",hybull:"⁃",hyphen:"‐",Iacute:"Í",iacute:"í",ic:"⁣",Icirc:"Î",icirc:"î",Icy:"И",icy:"и",Idot:"İ",IEcy:"Е",iecy:"е",iexcl:"¡",iff:"⇔",Ifr:"ℑ",ifr:"𝔦",Igrave:"Ì",igrave:"ì",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",IJlig:"Ĳ",ijlig:"ĳ",Im:"ℑ",Imacr:"Ī",imacr:"ī",image:"ℑ",ImaginaryI:"ⅈ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",Implies:"⇒",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",Int:"∬",int:"∫",intcal:"⊺",integers:"ℤ",Integral:"∫",intercal:"⊺",Intersection:"⋂",intlarhk:"⨗",intprod:"⨼",InvisibleComma:"⁣",InvisibleTimes:"⁢",IOcy:"Ё",iocy:"ё",Iogon:"Į",iogon:"į",Iopf:"𝕀",iopf:"𝕚",Iota:"Ι",iota:"ι",iprod:"⨼",iquest:"¿",Iscr:"ℐ",iscr:"𝒾",isin:"∈",isindot:"⋵",isinE:"⋹",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",Itilde:"Ĩ",itilde:"ĩ",Iukcy:"І",iukcy:"і",Iuml:"Ï",iuml:"ï",Jcirc:"Ĵ",jcirc:"ĵ",Jcy:"Й",jcy:"й",Jfr:"𝔍",jfr:"𝔧",jmath:"ȷ",Jopf:"𝕁",jopf:"𝕛",Jscr:"𝒥",jscr:"𝒿",Jsercy:"Ј",jsercy:"ј",Jukcy:"Є",jukcy:"є",Kappa:"Κ",kappa:"κ",kappav:"ϰ",Kcedil:"Ķ",kcedil:"ķ",Kcy:"К",kcy:"к",Kfr:"𝔎",kfr:"𝔨",kgreen:"ĸ",KHcy:"Х",khcy:"х",KJcy:"Ќ",kjcy:"ќ",Kopf:"𝕂",kopf:"𝕜",Kscr:"𝒦",kscr:"𝓀",lAarr:"⇚",Lacute:"Ĺ",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",Lambda:"Λ",lambda:"λ",Lang:"⟪",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",Laplacetrf:"ℒ",laquo:"«",Larr:"↞",lArr:"⇐",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",lAtail:"⤛",latail:"⤙",late:"⪭",lates:"⪭︀",lBarr:"⤎",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",Lcaron:"Ľ",lcaron:"ľ",Lcedil:"Ļ",lcedil:"ļ",lceil:"⌈",lcub:"{",Lcy:"Л",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",lE:"≦",le:"≤",LeftAngleBracket:"⟨",LeftArrow:"←",Leftarrow:"⇐",leftarrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",leftarrowtail:"↢",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",LeftRightArrow:"↔",Leftrightarrow:"⇔",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",leftthreetimes:"⋋",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",lEg:"⪋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",lessgtr:"≶",LessLess:"⪡",lesssim:"≲",LessSlantEqual:"⩽",LessTilde:"≲",lfisht:"⥼",lfloor:"⌊",Lfr:"𝔏",lfr:"𝔩",lg:"≶",lgE:"⪑",lHar:"⥢",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",LJcy:"Љ",ljcy:"љ",Ll:"⋘",ll:"≪",llarr:"⇇",llcorner:"⌞",Lleftarrow:"⇚",llhard:"⥫",lltri:"◺",Lmidot:"Ŀ",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnap:"⪉",lnapprox:"⪉",lnE:"≨",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",LongLeftArrow:"⟵",Longleftarrow:"⟸",longleftarrow:"⟵",LongLeftRightArrow:"⟷",Longleftrightarrow:"⟺",longleftrightarrow:"⟷",longmapsto:"⟼",LongRightArrow:"⟶",Longrightarrow:"⟹",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",Lopf:"𝕃",lopf:"𝕝",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",LowerLeftArrow:"↙",LowerRightArrow:"↘",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",Lscr:"ℒ",lscr:"𝓁",Lsh:"↰",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",Lstrok:"Ł",lstrok:"ł",LT:"<",Lt:"≪",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltri:"◃",ltrie:"⊴",ltrif:"◂",ltrPar:"⦖",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",macr:"¯",male:"♂",malt:"✠",maltese:"✠",Map:"⤅",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",Mcy:"М",mcy:"м",mdash:"—",mDDot:"∺",measuredangle:"∡",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"𝔐",mfr:"𝔪",mho:"℧",micro:"µ",mid:"∣",midast:"*",midcir:"⫰",middot:"·",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",MinusPlus:"∓",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",Mopf:"𝕄",mopf:"𝕞",mp:"∓",Mscr:"ℳ",mscr:"𝓂",mstpos:"∾",Mu:"Μ",mu:"μ",multimap:"⊸",mumap:"⊸",nabla:"∇",Nacute:"Ń",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbsp:" ",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",Ncaron:"Ň",ncaron:"ň",Ncedil:"Ņ",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",Ncy:"Н",ncy:"н",ndash:"–",ne:"≠",nearhk:"⤤",neArr:"⇗",nearr:"↗",nearrow:"↗",nedot:"≐̸",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",nequiv:"≢",nesear:"⤨",nesim:"≂̸",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",nexist:"∄",nexists:"∄",Nfr:"𝔑",nfr:"𝔫",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",nGg:"⋙̸",ngsim:"≵",nGt:"≫⃒",ngt:"≯",ngtr:"≯",nGtv:"≫̸",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",NJcy:"Њ",njcy:"њ",nlArr:"⇍",nlarr:"↚",nldr:"‥",nlE:"≦̸",nle:"≰",nLeftarrow:"⇍",nleftarrow:"↚",nLeftrightarrow:"⇎",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nLl:"⋘̸",nlsim:"≴",nLt:"≪⃒",nlt:"≮",nltri:"⋪",nltrie:"⋬",nLtv:"≪̸",nmid:"∤",NoBreak:"⁠",NonBreakingSpace:" ",Nopf:"ℕ",nopf:"𝕟",Not:"⫬",not:"¬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",notin:"∉",notindot:"⋵̸",notinE:"⋹̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nRightarrow:"⇏",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",Nscr:"𝒩",nscr:"𝓃",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",Ntilde:"Ñ",ntilde:"ñ",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",Nu:"Ν",nu:"ν",num:"#",numero:"№",numsp:" ",nvap:"≍⃒",nVDash:"⊯",nVdash:"⊮",nvDash:"⊭",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvHarr:"⤄",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwarhk:"⤣",nwArr:"⇖",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",Oacute:"Ó",oacute:"ó",oast:"⊛",ocir:"⊚",Ocirc:"Ô",ocirc:"ô",Ocy:"О",ocy:"о",odash:"⊝",Odblac:"Ő",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",OElig:"Œ",oelig:"œ",ofcir:"⦿",Ofr:"𝔒",ofr:"𝔬",ogon:"˛",Ograve:"Ò",ograve:"ò",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",Omacr:"Ō",omacr:"ō",Omega:"Ω",omega:"ω",Omicron:"Ο",omicron:"ο",omid:"⦶",ominus:"⊖",Oopf:"𝕆",oopf:"𝕠",opar:"⦷",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",operp:"⦹",oplus:"⊕",Or:"⩔",or:"∨",orarr:"↻",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"ª",ordm:"º",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oS:"Ⓢ",Oscr:"𝒪",oscr:"ℴ",Oslash:"Ø",oslash:"ø",osol:"⊘",Otilde:"Õ",otilde:"õ",Otimes:"⨷",otimes:"⊗",otimesas:"⨶",Ouml:"Ö",ouml:"ö",ovbar:"⌽",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",par:"∥",para:"¶",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",PartialD:"∂",Pcy:"П",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",Pfr:"𝔓",pfr:"𝔭",Phi:"Φ",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",Pi:"Π",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",PlusMinus:"±",plusmn:"±",plussim:"⨦",plustwo:"⨧",pm:"±",Poincareplane:"ℌ",pointint:"⨕",Popf:"ℙ",popf:"𝕡",pound:"£",Pr:"⪻",pr:"≺",prap:"⪷",prcue:"≼",prE:"⪳",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",Prime:"″",prime:"′",primes:"ℙ",prnap:"⪹",prnE:"⪵",prnsim:"⋨",prod:"∏",Product:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",Proportion:"∷",Proportional:"∝",propto:"∝",prsim:"≾",prurel:"⊰",Pscr:"𝒫",pscr:"𝓅",Psi:"Ψ",psi:"ψ",puncsp:" ",Qfr:"𝔔",qfr:"𝔮",qint:"⨌",Qopf:"ℚ",qopf:"𝕢",qprime:"⁗",Qscr:"𝒬",qscr:"𝓆",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",QUOT:'"',quot:'"',rAarr:"⇛",race:"∽̱",Racute:"Ŕ",racute:"ŕ",radic:"√",raemptyv:"⦳",Rang:"⟫",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"»",Rarr:"↠",rArr:"⇒",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",Rarrtl:"⤖",rarrtl:"↣",rarrw:"↝",rAtail:"⤜",ratail:"⤚",ratio:"∶",rationals:"ℚ",RBarr:"⤐",rBarr:"⤏",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",Rcaron:"Ř",rcaron:"ř",Rcedil:"Ŗ",rcedil:"ŗ",rceil:"⌉",rcub:"}",Rcy:"Р",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",Re:"ℜ",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",REG:"®",reg:"®",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",rfisht:"⥽",rfloor:"⌋",Rfr:"ℜ",rfr:"𝔯",rHar:"⥤",rhard:"⇁",rharu:"⇀",rharul:"⥬",Rho:"Ρ",rho:"ρ",rhov:"ϱ",RightAngleBracket:"⟩",RightArrow:"→",Rightarrow:"⇒",rightarrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",rightarrowtail:"↣",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",rightthreetimes:"⋌",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",Ropf:"ℝ",ropf:"𝕣",roplus:"⨮",rotimes:"⨵",RoundImplies:"⥰",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",Rrightarrow:"⇛",rsaquo:"›",Rscr:"ℛ",rscr:"𝓇",Rsh:"↱",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",RuleDelayed:"⧴",ruluhar:"⥨",rx:"℞",Sacute:"Ś",sacute:"ś",sbquo:"‚",Sc:"⪼",sc:"≻",scap:"⪸",Scaron:"Š",scaron:"š",sccue:"≽",scE:"⪴",sce:"⪰",Scedil:"Ş",scedil:"ş",Scirc:"Ŝ",scirc:"ŝ",scnap:"⪺",scnE:"⪶",scnsim:"⋩",scpolint:"⨓",scsim:"≿",Scy:"С",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",searhk:"⤥",seArr:"⇘",searr:"↘",searrow:"↘",sect:"§",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",Sfr:"𝔖",sfr:"𝔰",sfrown:"⌢",sharp:"♯",SHCHcy:"Щ",shchcy:"щ",SHcy:"Ш",shcy:"ш",ShortDownArrow:"↓",ShortLeftArrow:"←",shortmid:"∣",shortparallel:"∥",ShortRightArrow:"→",ShortUpArrow:"↑",shy:"­",Sigma:"Σ",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",SmallCircle:"∘",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",SOFTcy:"Ь",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",Sopf:"𝕊",sopf:"𝕤",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",Sqrt:"√",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",Square:"□",square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",squarf:"▪",squf:"▪",srarr:"→",Sscr:"𝒮",sscr:"𝓈",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",Star:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"¯",Sub:"⋐",sub:"⊂",subdot:"⪽",subE:"⫅",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",Subset:"⋐",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",SubsetEqual:"⊆",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",SuchThat:"∋",Sum:"∑",sum:"∑",sung:"♪",Sup:"⋑",sup:"⊃",sup1:"¹",sup2:"²",sup3:"³",supdot:"⪾",supdsub:"⫘",supE:"⫆",supe:"⊇",supedot:"⫄",Superset:"⊃",SupersetEqual:"⊇",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",Supset:"⋑",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swarhk:"⤦",swArr:"⇙",swarr:"↙",swarrow:"↙",swnwar:"⤪",szlig:"ß",Tab:"\t",target:"⌖",Tau:"Τ",tau:"τ",tbrk:"⎴",Tcaron:"Ť",tcaron:"ť",Tcedil:"Ţ",tcedil:"ţ",Tcy:"Т",tcy:"т",tdot:"⃛",telrec:"⌕",Tfr:"𝔗",tfr:"𝔱",there4:"∴",Therefore:"∴",therefore:"∴",Theta:"Θ",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",ThickSpace:"  ",thinsp:" ",ThinSpace:" ",thkap:"≈",thksim:"∼",THORN:"Þ",thorn:"þ",Tilde:"∼",tilde:"˜",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",times:"×",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",Topf:"𝕋",topf:"𝕥",topfork:"⫚",tosa:"⤩",tprime:"‴",TRADE:"™",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",TripleDot:"⃛",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",Tscr:"𝒯",tscr:"𝓉",TScy:"Ц",tscy:"ц",TSHcy:"Ћ",tshcy:"ћ",Tstrok:"Ŧ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",Uacute:"Ú",uacute:"ú",Uarr:"↟",uArr:"⇑",uarr:"↑",Uarrocir:"⥉",Ubrcy:"Ў",ubrcy:"ў",Ubreve:"Ŭ",ubreve:"ŭ",Ucirc:"Û",ucirc:"û",Ucy:"У",ucy:"у",udarr:"⇅",Udblac:"Ű",udblac:"ű",udhar:"⥮",ufisht:"⥾",Ufr:"𝔘",ufr:"𝔲",Ugrave:"Ù",ugrave:"ù",uHar:"⥣",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",Umacr:"Ū",umacr:"ū",uml:"¨",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",uogon:"ų",Uopf:"𝕌",uopf:"𝕦",UpArrow:"↑",Uparrow:"⇑",uparrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",Updownarrow:"⇕",updownarrow:"↕",UpEquilibrium:"⥮",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",upsi:"υ",upsih:"ϒ",Upsilon:"Υ",upsilon:"υ",UpTee:"⊥",UpTeeArrow:"↥",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",Uring:"Ů",uring:"ů",urtri:"◹",Uscr:"𝒰",uscr:"𝓊",utdot:"⋰",Utilde:"Ũ",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",Uuml:"Ü",uuml:"ü",uwangle:"⦧",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",vArr:"⇕",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",Vbar:"⫫",vBar:"⫨",vBarv:"⫩",Vcy:"В",vcy:"в",VDash:"⊫",Vdash:"⊩",vDash:"⊨",vdash:"⊢",Vdashl:"⫦",Vee:"⋁",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",Verbar:"‖",verbar:"|",Vert:"‖",vert:"|",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"𝔙",vfr:"𝔳",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",Vopf:"𝕍",vopf:"𝕧",vprop:"∝",vrtri:"⊳",Vscr:"𝒱",vscr:"𝓋",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",Vvdash:"⊪",vzigzag:"⦚",Wcirc:"Ŵ",wcirc:"ŵ",wedbar:"⩟",Wedge:"⋀",wedge:"∧",wedgeq:"≙",weierp:"℘",Wfr:"𝔚",wfr:"𝔴",Wopf:"𝕎",wopf:"𝕨",wp:"℘",wr:"≀",wreath:"≀",Wscr:"𝒲",wscr:"𝓌",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",Xfr:"𝔛",xfr:"𝔵",xhArr:"⟺",xharr:"⟷",Xi:"Ξ",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",Xopf:"𝕏",xopf:"𝕩",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",Xscr:"𝒳",xscr:"𝓍",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",Yacute:"Ý",yacute:"ý",YAcy:"Я",yacy:"я",Ycirc:"Ŷ",ycirc:"ŷ",Ycy:"Ы",ycy:"ы",yen:"¥",Yfr:"𝔜",yfr:"𝔶",YIcy:"Ї",yicy:"ї",Yopf:"𝕐",yopf:"𝕪",Yscr:"𝒴",yscr:"𝓎",YUcy:"Ю",yucy:"ю",Yuml:"Ÿ",yuml:"ÿ",Zacute:"Ź",zacute:"ź",Zcaron:"Ž",zcaron:"ž",Zcy:"З",zcy:"з",Zdot:"Ż",zdot:"ż",zeetrf:"ℨ",ZeroWidthSpace:"​",Zeta:"Ζ",zeta:"ζ",Zfr:"ℨ",zfr:"𝔷",ZHcy:"Ж",zhcy:"ж",zigrarr:"⇝",Zopf:"ℤ",zopf:"𝕫",Zscr:"𝒵",zscr:"𝓏",zwj:"‍",zwnj:"‌"},t.NGSP_UNICODE="",t.NAMED_ENTITIES.ngsp=t.NGSP_UNICODE}));h(Dc);Dc.TagContentType,Dc.splitNsName,Dc.isNgContainer,Dc.isNgContent,Dc.isNgTemplate,Dc.getNsPrefix,Dc.mergeNsAndName,Dc.NAMED_ENTITIES,Dc.NGSP_UNICODE;var gc=d((function(e,t){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0});var n,a,o=function(){function e(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=n.closedByChildren,a=n.requiredParents,o=n.implicitNamespacePrefix,u=n.contentType,s=void 0===u?Dc.TagContentType.PARSABLE_DATA:u,c=n.closedByParent,l=void 0!==c&&c,p=n.isVoid,f=void 0!==p&&p,h=n.ignoreFirstLf,d=void 0!==h&&h;r(this,e),this.closedByChildren={},this.closedByParent=!1,this.canSelfClose=!1,i&&i.length>0&&i.forEach((function(e){return t.closedByChildren[e]=!0})),this.isVoid=f,this.closedByParent=l||f,a&&a.length>0&&(this.requiredParents={},this.parentToAdd=a[0],a.forEach((function(e){return t.requiredParents[e]=!0}))),this.implicitNamespacePrefix=o||null,this.contentType=s,this.ignoreFirstLf=d}return i(e,[{key:"requireExtraParent",value:function(e){if(!this.requiredParents)return!1;if(!e)return!0;var t=e.toLowerCase();return!("template"===t||"ng-template"===e)&&1!=this.requiredParents[t]}},{key:"isClosedByChild",value:function(e){return this.isVoid||e.toLowerCase()in this.closedByChildren}}]),e}();t.HtmlTagDefinition=o,t.getHtmlTagDefinition=function(e){return a||(n=new o,a={base:new o({isVoid:!0}),meta:new o({isVoid:!0}),area:new o({isVoid:!0}),embed:new o({isVoid:!0}),link:new o({isVoid:!0}),img:new o({isVoid:!0}),input:new o({isVoid:!0}),param:new o({isVoid:!0}),hr:new o({isVoid:!0}),br:new o({isVoid:!0}),source:new o({isVoid:!0}),track:new o({isVoid:!0}),wbr:new o({isVoid:!0}),p:new o({closedByChildren:["address","article","aside","blockquote","div","dl","fieldset","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","main","nav","ol","p","pre","section","table","ul"],closedByParent:!0}),thead:new o({closedByChildren:["tbody","tfoot"]}),tbody:new o({closedByChildren:["tbody","tfoot"],closedByParent:!0}),tfoot:new o({closedByChildren:["tbody"],closedByParent:!0}),tr:new o({closedByChildren:["tr"],requiredParents:["tbody","tfoot","thead"],closedByParent:!0}),td:new o({closedByChildren:["td","th"],closedByParent:!0}),th:new o({closedByChildren:["td","th"],closedByParent:!0}),col:new o({requiredParents:["colgroup"],isVoid:!0}),svg:new o({implicitNamespacePrefix:"svg"}),math:new o({implicitNamespacePrefix:"math"}),li:new o({closedByChildren:["li"],closedByParent:!0}),dt:new o({closedByChildren:["dt","dd"]}),dd:new o({closedByChildren:["dt","dd"],closedByParent:!0}),rb:new o({closedByChildren:["rb","rt","rtc","rp"],closedByParent:!0}),rt:new o({closedByChildren:["rb","rt","rtc","rp"],closedByParent:!0}),rtc:new o({closedByChildren:["rb","rtc","rp"],closedByParent:!0}),rp:new o({closedByChildren:["rb","rt","rtc","rp"],closedByParent:!0}),optgroup:new o({closedByChildren:["optgroup"],closedByParent:!0}),option:new o({closedByChildren:["option","optgroup"],closedByParent:!0}),pre:new o({ignoreFirstLf:!0}),listing:new o({ignoreFirstLf:!0}),style:new o({contentType:Dc.TagContentType.RAW_TEXT}),script:new o({contentType:Dc.TagContentType.RAW_TEXT}),title:new o({contentType:Dc.TagContentType.ESCAPABLE_RAW_TEXT}),textarea:new o({contentType:Dc.TagContentType.ESCAPABLE_RAW_TEXT,ignoreFirstLf:!0})}),a[e]||n}}));h(gc);gc.HtmlTagDefinition,gc.getHtmlTagDefinition;var mc=d((function(e,t){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0}),t.assertArrayOfStrings=function(e,t){if(null!=t){if(!Array.isArray(t))throw new Error("Expected '".concat(e,"' to be an array of strings."));for(var r=0;r<t.length;r+=1)if("string"!=typeof t[r])throw new Error("Expected '".concat(e,"' to be an array of strings."))}};var r=[/^\s*$/,/[<>]/,/^[{}]$/,/&(#|[a-z])/i,/^\/\//];t.assertInterpolationSymbols=function(e,t){if(!(null==t||Array.isArray(t)&&2==t.length))throw new Error("Expected '".concat(e,"' to be an array, [start, end]."));if(null!=t){var n=t[0],i=t[1];r.forEach((function(e){if(e.test(n)||e.test(i))throw new Error("['".concat(n,"', '").concat(i,"'] contains unusable interpolation symbol."))}))}}}));h(mc);mc.assertArrayOfStrings,mc.assertInterpolationSymbols;var vc=d((function(e,t){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(t,n){r(this,e),this.start=t,this.end=n}return i(e,null,[{key:"fromArray",value:function(r){return r?(mc.assertInterpolationSymbols("interpolation",r),new e(r[0],r[1])):t.DEFAULT_INTERPOLATION_CONFIG}}]),e}();t.InterpolationConfig=n,t.DEFAULT_INTERPOLATION_CONFIG=new n("{{","}}")}));h(vc);vc.InterpolationConfig,vc.DEFAULT_INTERPOLATION_CONFIG;var bc=d((function(e,t){function r(e){return t.$0<=e&&e<=t.$9}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0}),t.$EOF=0,t.$TAB=9,t.$LF=10,t.$VTAB=11,t.$FF=12,t.$CR=13,t.$SPACE=32,t.$BANG=33,t.$DQ=34,t.$HASH=35,t.$$=36,t.$PERCENT=37,t.$AMPERSAND=38,t.$SQ=39,t.$LPAREN=40,t.$RPAREN=41,t.$STAR=42,t.$PLUS=43,t.$COMMA=44,t.$MINUS=45,t.$PERIOD=46,t.$SLASH=47,t.$COLON=58,t.$SEMICOLON=59,t.$LT=60,t.$EQ=61,t.$GT=62,t.$QUESTION=63,t.$0=48,t.$9=57,t.$A=65,t.$E=69,t.$F=70,t.$X=88,t.$Z=90,t.$LBRACKET=91,t.$BACKSLASH=92,t.$RBRACKET=93,t.$CARET=94,t.$_=95,t.$a=97,t.$e=101,t.$f=102,t.$n=110,t.$r=114,t.$t=116,t.$u=117,t.$v=118,t.$x=120,t.$z=122,t.$LBRACE=123,t.$BAR=124,t.$RBRACE=125,t.$NBSP=160,t.$PIPE=124,t.$TILDA=126,t.$AT=64,t.$BT=96,t.isWhitespace=function(e){return e>=t.$TAB&&e<=t.$SPACE||e==t.$NBSP},t.isDigit=r,t.isAsciiLetter=function(e){return e>=t.$a&&e<=t.$z||e>=t.$A&&e<=t.$Z},t.isAsciiHexDigit=function(e){return e>=t.$a&&e<=t.$f||e>=t.$A&&e<=t.$F||r(e)}}));h(bc);bc.$EOF,bc.$TAB,bc.$LF,bc.$VTAB,bc.$FF,bc.$CR,bc.$SPACE,bc.$BANG,bc.$DQ,bc.$HASH,bc.$$,bc.$PERCENT,bc.$AMPERSAND,bc.$SQ,bc.$LPAREN,bc.$RPAREN,bc.$STAR,bc.$PLUS,bc.$COMMA,bc.$MINUS,bc.$PERIOD,bc.$SLASH,bc.$COLON,bc.$SEMICOLON,bc.$LT,bc.$EQ,bc.$GT,bc.$QUESTION,bc.$0,bc.$9,bc.$A,bc.$E,bc.$F,bc.$X,bc.$Z,bc.$LBRACKET,bc.$BACKSLASH,bc.$RBRACKET,bc.$CARET,bc.$_,bc.$a,bc.$e,bc.$f,bc.$n,bc.$r,bc.$t,bc.$u,bc.$v,bc.$x,bc.$z,bc.$LBRACE,bc.$BAR,bc.$RBRACE,bc.$NBSP,bc.$PIPE,bc.$TILDA,bc.$AT,bc.$BT,bc.isWhitespace,bc.isDigit,bc.isAsciiLetter,bc.isAsciiHexDigit;var yc=d((function(e,t){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(t,n,i){r(this,e),this.filePath=t,this.name=n,this.members=i}return i(e,[{key:"assertNoMembers",value:function(){if(this.members.length)throw new Error("Illegal state: symbol without members expected, but got ".concat(JSON.stringify(this),"."))}}]),e}();t.StaticSymbol=n;var a=function(){function e(){r(this,e),this.cache=new Map}return i(e,[{key:"get",value:function(e,t,r){var i=(r=r||[]).length?".".concat(r.join(".")):"",a='"'.concat(e,'".').concat(t).concat(i),o=this.cache.get(a);return o||(o=new n(e,t,r),this.cache.set(a,o)),o}}]),e}();t.StaticSymbolCache=a}));h(yc);yc.StaticSymbol,yc.StaticSymbolCache;var Ec=d((function(e,n){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(n,"__esModule",{value:!0});var a=/-+([a-z0-9])/g;function o(e,t,r){var n=e.indexOf(t);return-1==n?r:[e.slice(0,n).trim(),e.slice(n+1).trim()]}function u(e,r,n){return Array.isArray(e)?r.visitArray(e,n):"object"===t(i=e)&&null!==i&&Object.getPrototypeOf(i)===p?r.visitStringMap(e,n):null==e||"string"==typeof e||"number"==typeof e||"boolean"==typeof e?r.visitPrimitive(e,n):r.visitOther(e,n);var i}n.dashCaseToCamelCase=function(e){return e.replace(a,(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return t[1].toUpperCase()}))},n.splitAtColon=function(e,t){return o(e,":",t)},n.splitAtPeriod=function(e,t){return o(e,".",t)},n.visitValue=u,n.isDefined=function(e){return null!=e},n.noUndefined=function(e){return void 0===e?null:e};var s=function(){function e(){r(this,e)}return i(e,[{key:"visitArray",value:function(e,t){var r=this;return e.map((function(e){return u(e,r,t)}))}},{key:"visitStringMap",value:function(e,t){var r=this,n={};return Object.keys(e).forEach((function(i){n[i]=u(e[i],r,t)})),n}},{key:"visitPrimitive",value:function(e,t){return e}},{key:"visitOther",value:function(e,t){return e}}]),e}();n.ValueTransformer=s,n.SyncAsync={assertSync:function(e){if(f(e))throw new Error("Illegal state: value cannot be a promise");return e},then:function(e,t){return f(e)?e.then(t):t(e)},all:function(e){return e.some(f)?Promise.all(e):e}},n.error=function(e){throw new Error("Internal Error: ".concat(e))},n.syntaxError=function(e,t){var r=Error(e);return r[c]=!0,t&&(r[l]=t),r};var c="ngSyntaxError",l="ngParseErrors";n.isSyntaxError=function(e){return e[c]},n.getParseErrors=function(e){return e[l]||[]},n.escapeRegExp=function(e){return e.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")};var p=Object.getPrototypeOf({});function f(e){return!!e&&"function"==typeof e.then}n.utf8Encode=function(e){for(var t="",r=0;r<e.length;r++){var n=e.charCodeAt(r);if(n>=55296&&n<=56319&&e.length>r+1){var i=e.charCodeAt(r+1);i>=56320&&i<=57343&&(r++,n=(n-55296<<10)+i-56320+65536)}n<=127?t+=String.fromCharCode(n):n<=2047?t+=String.fromCharCode(n>>6&31|192,63&n|128):n<=65535?t+=String.fromCharCode(n>>12|224,n>>6&63|128,63&n|128):n<=2097151&&(t+=String.fromCharCode(n>>18&7|240,n>>12&63|128,n>>6&63|128,63&n|128))}return t},n.stringify=function e(t){if("string"==typeof t)return t;if(t instanceof Array)return"["+t.map(e).join(", ")+"]";if(null==t)return""+t;if(t.overriddenName)return"".concat(t.overriddenName);if(t.name)return"".concat(t.name);var r=t.toString();if(null==r)return""+r;var n=r.indexOf("\n");return-1===n?r:r.substring(0,n)},n.resolveForwardRef=function(e){return"function"==typeof e&&e.hasOwnProperty("__forward_ref__")?e():e},n.isPromise=f;n.Version=function e(t){r(this,e),this.full=t;var n=t.split(".");this.major=n[0],this.minor=n[1],this.patch=n.slice(2).join(".")}}));h(Ec);Ec.dashCaseToCamelCase,Ec.splitAtColon,Ec.splitAtPeriod,Ec.visitValue,Ec.isDefined,Ec.noUndefined,Ec.ValueTransformer,Ec.SyncAsync,Ec.error,Ec.syntaxError,Ec.isSyntaxError,Ec.getParseErrors,Ec.escapeRegExp,Ec.utf8Encode,Ec.stringify,Ec.resolveForwardRef,Ec.isPromise,Ec.Version;var Cc=d((function(e,t){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0});var n=/^(?:(?:\[([^\]]+)\])|(?:\(([^\)]+)\)))|(\@[-\w]+)$/;function a(e){return e.replace(/\W/g,"_")}t.sanitizeIdentifier=a;var o,u=0;function s(e){if(!e||!e.reference)return null;var t=e.reference;if(t instanceof yc.StaticSymbol)return t.name;if(t.__anonymousType)return t.__anonymousType;var r=Ec.stringify(t);return r.indexOf("(")>=0?(r="anonymous_".concat(u++),t.__anonymousType=r):r=a(r),r}t.identifierName=s,t.identifierModuleUrl=function(e){var t=e.reference;return t instanceof yc.StaticSymbol?t.filePath:"./".concat(Ec.stringify(t))},t.viewClassName=function(e,t){return"View_".concat(s({reference:e}),"_").concat(t)},t.rendererTypeName=function(e){return"RenderType_".concat(s({reference:e}))},t.hostViewClassName=function(e){return"HostView_".concat(s({reference:e}))},t.componentFactoryName=function(e){return"".concat(s({reference:e}),"NgFactory")},function(e){e[e.Pipe=0]="Pipe",e[e.Directive=1]="Directive",e[e.NgModule=2]="NgModule",e[e.Injectable=3]="Injectable"}(o=t.CompileSummaryKind||(t.CompileSummaryKind={})),t.tokenName=function(e){return null!=e.value?a(e.value):s(e.identifier)},t.tokenReference=function(e){return null!=e.identifier?e.identifier.reference:e.value};t.CompileStylesheetMetadata=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.moduleUrl,i=t.styles,a=t.styleUrls;r(this,e),this.moduleUrl=n||null,this.styles=d(i),this.styleUrls=d(a)};var c=function(){function e(t){var n=t.encapsulation,i=t.template,a=t.templateUrl,o=t.htmlAst,u=t.styles,s=t.styleUrls,c=t.externalStylesheets,l=t.animations,p=t.ngContentSelectors,f=t.interpolation,h=t.isInline,g=t.preserveWhitespaces;if(r(this,e),this.encapsulation=n,this.template=i,this.templateUrl=a,this.htmlAst=o,this.styles=d(u),this.styleUrls=d(s),this.externalStylesheets=d(c),this.animations=l?D(l):[],this.ngContentSelectors=p||[],f&&2!=f.length)throw new Error("'interpolation' should have a start and an end symbol.");this.interpolation=f,this.isInline=h,this.preserveWhitespaces=g}return i(e,[{key:"toSummary",value:function(){return{ngContentSelectors:this.ngContentSelectors,encapsulation:this.encapsulation,styles:this.styles,animations:this.animations}}}]),e}();t.CompileTemplateMetadata=c;var l=function(){function e(t){var n=t.isHost,i=t.type,a=t.isComponent,o=t.selector,u=t.exportAs,s=t.changeDetection,c=t.inputs,l=t.outputs,p=t.hostListeners,f=t.hostProperties,h=t.hostAttributes,D=t.providers,g=t.viewProviders,m=t.queries,v=t.guards,b=t.viewQueries,y=t.entryComponents,E=t.template,C=t.componentViewType,A=t.rendererType,w=t.componentFactory;r(this,e),this.isHost=!!n,this.type=i,this.isComponent=a,this.selector=o,this.exportAs=u,this.changeDetection=s,this.inputs=c,this.outputs=l,this.hostListeners=p,this.hostProperties=f,this.hostAttributes=h,this.providers=d(D),this.viewProviders=d(g),this.queries=d(m),this.guards=v,this.viewQueries=d(b),this.entryComponents=d(y),this.template=E,this.componentViewType=C,this.rendererType=A,this.componentFactory=w}return i(e,null,[{key:"create",value:function(t){var r=t.isHost,i=t.type,a=t.isComponent,o=t.selector,u=t.exportAs,s=t.changeDetection,c=t.inputs,l=t.outputs,p=t.host,f=t.providers,h=t.viewProviders,d=t.queries,D=t.guards,g=t.viewQueries,m=t.entryComponents,v=t.template,b=t.componentViewType,y=t.rendererType,E=t.componentFactory,C={},A={},w={};null!=p&&Object.keys(p).forEach((function(e){var t=p[e],r=e.match(n);null===r?w[e]=t:null!=r[1]?A[r[1]]=t:null!=r[2]&&(C[r[2]]=t)}));var k={};null!=c&&c.forEach((function(e){var t=Ec.splitAtColon(e,[e,e]);k[t[0]]=t[1]}));var T={};return null!=l&&l.forEach((function(e){var t=Ec.splitAtColon(e,[e,e]);T[t[0]]=t[1]})),new e({isHost:r,type:i,isComponent:!!a,selector:o,exportAs:u,changeDetection:s,inputs:k,outputs:T,hostListeners:C,hostProperties:A,hostAttributes:w,providers:f,viewProviders:h,queries:d,guards:D,viewQueries:g,entryComponents:m,template:v,componentViewType:b,rendererType:y,componentFactory:E})}}]),i(e,[{key:"toSummary",value:function(){return{summaryKind:o.Directive,type:this.type,isComponent:this.isComponent,selector:this.selector,exportAs:this.exportAs,inputs:this.inputs,outputs:this.outputs,hostListeners:this.hostListeners,hostProperties:this.hostProperties,hostAttributes:this.hostAttributes,providers:this.providers,viewProviders:this.viewProviders,queries:this.queries,guards:this.guards,viewQueries:this.viewQueries,entryComponents:this.entryComponents,changeDetection:this.changeDetection,template:this.template&&this.template.toSummary(),componentViewType:this.componentViewType,rendererType:this.rendererType,componentFactory:this.componentFactory}}}]),e}();t.CompileDirectiveMetadata=l;var p=function(){function e(t){var n=t.type,i=t.name,a=t.pure;r(this,e),this.type=n,this.name=i,this.pure=!!a}return i(e,[{key:"toSummary",value:function(){return{summaryKind:o.Pipe,type:this.type,name:this.name,pure:this.pure}}}]),e}();t.CompilePipeMetadata=p;t.CompileShallowModuleMetadata=function e(){r(this,e)};var f=function(){function e(t){var n=t.type,i=t.providers,a=t.declaredDirectives,o=t.exportedDirectives,u=t.declaredPipes,s=t.exportedPipes,c=t.entryComponents,l=t.bootstrapComponents,p=t.importedModules,f=t.exportedModules,h=t.schemas,D=t.transitiveModule,g=t.id;r(this,e),this.type=n||null,this.declaredDirectives=d(a),this.exportedDirectives=d(o),this.declaredPipes=d(u),this.exportedPipes=d(s),this.providers=d(i),this.entryComponents=d(c),this.bootstrapComponents=d(l),this.importedModules=d(p),this.exportedModules=d(f),this.schemas=d(h),this.id=g||null,this.transitiveModule=D||null}return i(e,[{key:"toSummary",value:function(){var e=this.transitiveModule;return{summaryKind:o.NgModule,type:this.type,entryComponents:e.entryComponents,providers:e.providers,modules:e.modules,exportedDirectives:e.exportedDirectives,exportedPipes:e.exportedPipes}}}]),e}();t.CompileNgModuleMetadata=f;var h=function(){function e(){r(this,e),this.directivesSet=new Set,this.directives=[],this.exportedDirectivesSet=new Set,this.exportedDirectives=[],this.pipesSet=new Set,this.pipes=[],this.exportedPipesSet=new Set,this.exportedPipes=[],this.modulesSet=new Set,this.modules=[],this.entryComponentsSet=new Set,this.entryComponents=[],this.providers=[]}return i(e,[{key:"addProvider",value:function(e,t){this.providers.push({provider:e,module:t})}},{key:"addDirective",value:function(e){this.directivesSet.has(e.reference)||(this.directivesSet.add(e.reference),this.directives.push(e))}},{key:"addExportedDirective",value:function(e){this.exportedDirectivesSet.has(e.reference)||(this.exportedDirectivesSet.add(e.reference),this.exportedDirectives.push(e))}},{key:"addPipe",value:function(e){this.pipesSet.has(e.reference)||(this.pipesSet.add(e.reference),this.pipes.push(e))}},{key:"addExportedPipe",value:function(e){this.exportedPipesSet.has(e.reference)||(this.exportedPipesSet.add(e.reference),this.exportedPipes.push(e))}},{key:"addModule",value:function(e){this.modulesSet.has(e.reference)||(this.modulesSet.add(e.reference),this.modules.push(e))}},{key:"addEntryComponent",value:function(e){this.entryComponentsSet.has(e.componentType)||(this.entryComponentsSet.add(e.componentType),this.entryComponents.push(e))}}]),e}();function d(e){return e||[]}t.TransitiveCompileNgModuleMetadata=h;function D(e){return e.reduce((function(e,t){var r=Array.isArray(t)?D(t):t;return e.concat(r)}),[])}function g(e){return e.replace(/(\w+:\/\/[\w:-]+)?(\/+)?/,"ng:///")}t.ProviderMeta=function e(t,n){var i=n.useClass,a=n.useValue,o=n.useExisting,u=n.useFactory,s=n.deps,c=n.multi;r(this,e),this.token=t,this.useClass=i||null,this.useValue=a,this.useExisting=o,this.useFactory=u||null,this.dependencies=s||null,this.multi=!!c},t.flatten=D,t.templateSourceUrl=function(e,t,r){var n;return n=r.isInline?t.type.reference instanceof yc.StaticSymbol?"".concat(t.type.reference.filePath,".").concat(t.type.reference.name,".html"):"".concat(s(e),"/").concat(s(t.type),".html"):r.templateUrl,t.type.reference instanceof yc.StaticSymbol?n:g(n)},t.sharedStylesheetJitUrl=function(e,t){var r=e.moduleUrl.split(/\/\\/g),n=r[r.length-1];return g("css/".concat(t).concat(n,".ngstyle.js"))},t.ngModuleJitUrl=function(e){return g("".concat(s(e.type),"/module.ngfactory.js"))},t.templateJitUrl=function(e,t){return g("".concat(s(e),"/").concat(s(t.type),".ngfactory.js"))}}));h(Cc);Cc.sanitizeIdentifier,Cc.identifierName,Cc.identifierModuleUrl,Cc.viewClassName,Cc.rendererTypeName,Cc.hostViewClassName,Cc.componentFactoryName,Cc.CompileSummaryKind,Cc.tokenName,Cc.tokenReference,Cc.CompileStylesheetMetadata,Cc.CompileTemplateMetadata,Cc.CompileDirectiveMetadata,Cc.CompilePipeMetadata,Cc.CompileShallowModuleMetadata,Cc.CompileNgModuleMetadata,Cc.TransitiveCompileNgModuleMetadata,Cc.ProviderMeta,Cc.flatten,Cc.templateSourceUrl,Cc.sharedStylesheetJitUrl,Cc.ngModuleJitUrl,Cc.templateJitUrl;var Ac=d((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var n=function(){function e(t,n,i,a){r(this,e),this.file=t,this.offset=n,this.line=i,this.col=a}return i(e,[{key:"toString",value:function(){return null!=this.offset?"".concat(this.file.url,"@").concat(this.line,":").concat(this.col):this.file.url}},{key:"moveBy",value:function(t){for(var r=this.file.content,n=r.length,i=this.offset,a=this.line,o=this.col;i>0&&t<0;){if(i--,t++,r.charCodeAt(i)==bc.$LF){a--;var u=r.substr(0,i-1).lastIndexOf(String.fromCharCode(bc.$LF));o=u>0?i-u:i}else o--}for(;i<n&&t>0;){var s=r.charCodeAt(i);i++,t--,s==bc.$LF?(a++,o=0):o++}return new e(this.file,i,a,o)}},{key:"getContext",value:function(e,t){var r=this.file.content,n=this.offset;if(null!=n){n>r.length-1&&(n=r.length-1);for(var i=n,a=0,o=0;a<e&&n>0&&(a++,"\n"!=r[--n]||++o!=t););for(a=0,o=0;a<e&&i<r.length-1&&(a++,"\n"!=r[++i]||++o!=t););return{before:r.substring(n,this.offset),after:r.substring(this.offset,i+1)}}return null}}]),e}();t.ParseLocation=n;var a=function e(t,n){r(this,e),this.content=t,this.url=n};t.ParseSourceFile=a;var o,u=function(){function e(t,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;r(this,e),this.start=t,this.end=n,this.details=i}return i(e,[{key:"toString",value:function(){return this.start.file.content.substring(this.start.offset,this.end.offset)}}]),e}();t.ParseSourceSpan=u,function(e){e[e.WARNING=0]="WARNING",e[e.ERROR=1]="ERROR"}(o=t.ParseErrorLevel||(t.ParseErrorLevel={}));var s=function(){function e(t,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:o.ERROR;r(this,e),this.span=t,this.msg=n,this.level=i}return i(e,[{key:"contextualMessage",value:function(){var e=this.span.start.getContext(100,3);return e?"".concat(this.msg,' ("').concat(e.before,"[").concat(o[this.level]," ->]").concat(e.after,'")'):this.msg}},{key:"toString",value:function(){var e=this.span.details?", ".concat(this.span.details):"";return"".concat(this.contextualMessage(),": ").concat(this.span.start).concat(e)}}]),e}();t.ParseError=s,t.typeSourceSpan=function(e,t){var r=Cc.identifierModuleUrl(t),i=null!=r?"in ".concat(e," ").concat(Cc.identifierName(t)," in ").concat(r):"in ".concat(e," ").concat(Cc.identifierName(t)),o=new a("",i);return new u(new n(o,-1,-1,-1),new n(o,-1,-1,-1))}}));h(Ac);Ac.ParseLocation,Ac.ParseSourceFile,Ac.ParseSourceSpan,Ac.ParseErrorLevel,Ac.ParseError,Ac.typeSourceSpan;var wc=d((function(e,t){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1;r(this,e),this.path=t,this.position=n}return i(e,[{key:"parentOf",value:function(e){return e&&this.path[this.path.indexOf(e)-1]}},{key:"childOf",value:function(e){return this.path[this.path.indexOf(e)+1]}},{key:"first",value:function(e){for(var t=this.path.length-1;t>=0;t--){var r=this.path[t];if(r instanceof e)return r}}},{key:"push",value:function(e){this.path.push(e)}},{key:"pop",value:function(){return this.path.pop()}},{key:"empty",get:function(){return!this.path||!this.path.length}},{key:"head",get:function(){return this.path[0]}},{key:"tail",get:function(){return this.path[this.path.length-1]}}]),e}();t.AstPath=n}));h(wc);wc.AstPath;var kc=d((function(e,t){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(t,n){r(this,e),this.value=t,this.sourceSpan=n}return i(e,[{key:"visit",value:function(e,t){return e.visitText(this,t)}}]),e}();t.Text=n;var u=function(){function e(t,n){r(this,e),this.value=t,this.sourceSpan=n}return i(e,[{key:"visit",value:function(e,t){return e.visitCdata(this,t)}}]),e}();t.CDATA=u;var c=function(){function e(t,n,i,a,o){r(this,e),this.switchValue=t,this.type=n,this.cases=i,this.sourceSpan=a,this.switchValueSourceSpan=o}return i(e,[{key:"visit",value:function(e,t){return e.visitExpansion(this,t)}}]),e}();t.Expansion=c;var l=function(){function e(t,n,i,a,o){r(this,e),this.value=t,this.expression=n,this.sourceSpan=i,this.valueSourceSpan=a,this.expSourceSpan=o}return i(e,[{key:"visit",value:function(e,t){return e.visitExpansionCase(this,t)}}]),e}();t.ExpansionCase=l;var p=function(){function e(t,n,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;r(this,e),this.name=t,this.value=n,this.sourceSpan=i,this.valueSpan=a,this.nameSpan=o}return i(e,[{key:"visit",value:function(e,t){return e.visitAttribute(this,t)}}]),e}();t.Attribute=p;var f=function(){function e(t,n,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,u=arguments.length>5&&void 0!==arguments[5]?arguments[5]:null,s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:null;r(this,e),this.name=t,this.attrs=n,this.children=i,this.sourceSpan=a,this.startSourceSpan=o,this.endSourceSpan=u,this.nameSpan=s}return i(e,[{key:"visit",value:function(e,t){return e.visitElement(this,t)}}]),e}();t.Element=f;var h=function(){function e(t,n){r(this,e),this.value=t,this.sourceSpan=n}return i(e,[{key:"visit",value:function(e,t){return e.visitComment(this,t)}}]),e}();t.Comment=h;var d=function(){function e(t,n){r(this,e),this.value=t,this.sourceSpan=n}return i(e,[{key:"visit",value:function(e,t){return e.visitDocType(this,t)}}]),e}();function D(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=[],i=e.visit?function(t){return e.visit(t,r)||t.visit(e,r)}:function(t){return t.visit(e,r)};return t.forEach((function(e){var t=i(e);t&&n.push(t)})),n}t.DocType=d,t.visitAll=D;var g=function(){function e(){r(this,e)}return i(e,[{key:"visitElement",value:function(e,t){this.visitChildren(t,(function(t){t(e.attrs),t(e.children)}))}},{key:"visitAttribute",value:function(e,t){}},{key:"visitText",value:function(e,t){}},{key:"visitCdata",value:function(e,t){}},{key:"visitComment",value:function(e,t){}},{key:"visitDocType",value:function(e,t){}},{key:"visitExpansion",value:function(e,t){return this.visitChildren(t,(function(t){t(e.cases)}))}},{key:"visitExpansionCase",value:function(e,t){}},{key:"visitChildren",value:function(e,t){var r=[],n=this;return t((function(t){t&&r.push(D(n,t,e))})),[].concat.apply([],r)}}]),e}();t.RecursiveVisitor=g,t.findNode=function(e,t){var n=[];return D(new(function(e){function u(){return r(this,u),s(this,o(u).apply(this,arguments))}return a(u,e),i(u,[{key:"visit",value:function(e,r){var i=function e(t){var r=t.sourceSpan.start.offset,n=t.sourceSpan.end.offset;return t instanceof f&&(t.endSourceSpan?n=t.endSourceSpan.end.offset:t.children&&t.children.length&&(n=e(t.children[t.children.length-1]).end)),{start:r,end:n}}(e);if(!(i.start<=t&&t<i.end))return!0;n.push(e)}}]),u}(g)),e),new wc.AstPath(n,t)}}));h(kc);kc.Text,kc.CDATA,kc.Expansion,kc.ExpansionCase,kc.Attribute,kc.Element,kc.Comment,kc.DocType,kc.visitAll,kc.RecursiveVisitor,kc.findNode;var Tc=d((function(e,t){var n;
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0}),function(e){e[e.TAG_OPEN_START=0]="TAG_OPEN_START",e[e.TAG_OPEN_END=1]="TAG_OPEN_END",e[e.TAG_OPEN_END_VOID=2]="TAG_OPEN_END_VOID",e[e.TAG_CLOSE=3]="TAG_CLOSE",e[e.TEXT=4]="TEXT",e[e.ESCAPABLE_RAW_TEXT=5]="ESCAPABLE_RAW_TEXT",e[e.RAW_TEXT=6]="RAW_TEXT",e[e.COMMENT_START=7]="COMMENT_START",e[e.COMMENT_END=8]="COMMENT_END",e[e.CDATA_START=9]="CDATA_START",e[e.CDATA_END=10]="CDATA_END",e[e.ATTR_NAME=11]="ATTR_NAME",e[e.ATTR_VALUE=12]="ATTR_VALUE",e[e.DOC_TYPE_START=13]="DOC_TYPE_START",e[e.DOC_TYPE_END=14]="DOC_TYPE_END",e[e.EXPANSION_FORM_START=15]="EXPANSION_FORM_START",e[e.EXPANSION_CASE_VALUE=16]="EXPANSION_CASE_VALUE",e[e.EXPANSION_CASE_EXP_START=17]="EXPANSION_CASE_EXP_START",e[e.EXPANSION_CASE_EXP_END=18]="EXPANSION_CASE_EXP_END",e[e.EXPANSION_FORM_END=19]="EXPANSION_FORM_END",e[e.EOF=20]="EOF"}(n=t.TokenType||(t.TokenType={}));var u=function e(t,n,i){r(this,e),this.type=t,this.parts=n,this.sourceSpan=i};t.Token=u;var c=function(e){function t(e,n,i){var a;return r(this,t),(a=s(this,o(t).call(this,i,e))).tokenType=n,a}return a(t,e),t}(Ac.ParseError);t.TokenError=c;var l=function e(t,n){r(this,e),this.tokens=t,this.errors=n};t.TokenizeResult=l,t.tokenize=function(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:vc.DEFAULT_INTERPOLATION_CONFIG,a=arguments.length>5&&void 0!==arguments[5]&&arguments[5],o=arguments.length>6&&void 0!==arguments[6]&&arguments[6];return new D(new Ac.ParseSourceFile(e,t),r,n,i,a,o).tokenize()};var p=/\r\n?/g;function f(e){var t=e===bc.$EOF?"EOF":String.fromCharCode(e);return'Unexpected character "'.concat(t,'"')}function h(e){return'Unknown entity "'.concat(e,'" - use the "&#<decimal>;" or  "&#x<hex>;" syntax')}var d=function e(t){r(this,e),this.error=t},D=function(){function e(t,n,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:vc.DEFAULT_INTERPOLATION_CONFIG,o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],u=arguments.length>5&&void 0!==arguments[5]&&arguments[5];r(this,e),this._file=t,this._getTagDefinition=n,this._tokenizeIcu=i,this._interpolationConfig=a,this.canSelfClose=o,this.allowHtmComponentClosingTags=u,this._peek=-1,this._nextPeek=-1,this._index=-1,this._line=0,this._column=-1,this._expansionCaseStack=[],this._inInterpolation=!1,this.tokens=[],this.errors=[],this._input=t.content,this._length=t.content.length,this._advance()}return i(e,[{key:"_processCarriageReturns",value:function(e){return e.replace(p,"\n")}},{key:"tokenize",value:function(){for(;this._peek!==bc.$EOF;){var e=this._getLocation();try{if(this._attemptCharCode(bc.$LT))if(this._attemptCharCode(bc.$BANG))this._attemptStr("[CDATA[")?this._consumeCdata(e):this._attemptStr("--")?this._consumeComment(e):this._attemptStrCaseInsensitive("doctype")?this._consumeDocType(e):this._consumeBogusComment(e);else if(this._attemptCharCode(bc.$SLASH))this._consumeTagClose(e);else{var t=this._savePosition();this._attemptCharCode(bc.$QUESTION)?(this._restorePosition(t),this._consumeBogusComment(e)):this._consumeTagOpen(e)}else this._tokenizeIcu&&this._tokenizeExpansionForm()||this._consumeText()}catch(e){if(!(e instanceof d))throw e;this.errors.push(e.error)}}return this._beginToken(n.EOF),this._endToken([]),new l(function(e){for(var t=[],r=void 0,i=0;i<e.length;i++){var a=e[i];r&&r.type==n.TEXT&&a.type==n.TEXT?(r.parts[0]+=a.parts[0],r.sourceSpan.end=a.sourceSpan.end):(r=a,t.push(r))}return t}(this.tokens),this.errors)}},{key:"_tokenizeExpansionForm",value:function(){if(y(this._input,this._index,this._interpolationConfig))return this._consumeExpansionFormStart(),!0;if(((e=this._peek)===bc.$EQ||bc.isAsciiLetter(e)||bc.isDigit(e))&&this._isInExpansionForm())return this._consumeExpansionCaseStart(),!0;var e;if(this._peek===bc.$RBRACE){if(this._isInExpansionCase())return this._consumeExpansionCaseEnd(),!0;if(this._isInExpansionForm())return this._consumeExpansionFormEnd(),!0}return!1}},{key:"_getLocation",value:function(){return new Ac.ParseLocation(this._file,this._index,this._line,this._column)}},{key:"_getSpan",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._getLocation(),t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._getLocation();return new Ac.ParseSourceSpan(e,t)}},{key:"_beginToken",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._getLocation();this._currentTokenStart=t,this._currentTokenType=e}},{key:"_endToken",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._getLocation(),r=new u(this._currentTokenType,e,new Ac.ParseSourceSpan(this._currentTokenStart,t));return this.tokens.push(r),this._currentTokenStart=null,this._currentTokenType=null,r}},{key:"_createError",value:function(e,t){this._isInExpansionForm()&&(e+=' (Do you have an unescaped "{" in your template? Use "{{ \'{\' }}") to escape it.)');var r=new c(e,this._currentTokenType,t);return this._currentTokenStart=null,this._currentTokenType=null,new d(r)}},{key:"_advance",value:function(){if(this._index>=this._length)throw this._createError(f(bc.$EOF),this._getSpan());this._peek===bc.$LF?(this._line++,this._column=0):this._peek!==bc.$LF&&this._peek!==bc.$CR&&this._column++,this._index++,this._peek=this._index>=this._length?bc.$EOF:this._input.charCodeAt(this._index),this._nextPeek=this._index+1>=this._length?bc.$EOF:this._input.charCodeAt(this._index+1)}},{key:"_attemptCharCode",value:function(e){return this._peek===e&&(this._advance(),!0)}},{key:"_attemptCharCodeCaseInsensitive",value:function(e){return t=this._peek,r=e,E(t)==E(r)&&(this._advance(),!0);var t,r}},{key:"_requireCharCode",value:function(e){var t=this._getLocation();if(!this._attemptCharCode(e))throw this._createError(f(this._peek),this._getSpan(t,t))}},{key:"_attemptStr",value:function(e){var t=e.length;if(this._index+t>this._length)return!1;for(var r=this._savePosition(),n=0;n<t;n++)if(!this._attemptCharCode(e.charCodeAt(n)))return this._restorePosition(r),!1;return!0}},{key:"_attemptStrCaseInsensitive",value:function(e){for(var t=0;t<e.length;t++)if(!this._attemptCharCodeCaseInsensitive(e.charCodeAt(t)))return!1;return!0}},{key:"_requireStr",value:function(e){var t=this._getLocation();if(!this._attemptStr(e))throw this._createError(f(this._peek),this._getSpan(t))}},{key:"_requireStrCaseInsensitive",value:function(e){var t=this._getLocation();if(!this._attemptStrCaseInsensitive(e))throw this._createError(f(this._peek),this._getSpan(t))}},{key:"_attemptCharCodeUntilFn",value:function(e){for(;!e(this._peek);)this._advance()}},{key:"_requireCharCodeUntilFn",value:function(e,t){var r=this._getLocation();if(this._attemptCharCodeUntilFn(e),this._index-r.offset<t)throw this._createError(f(this._peek),this._getSpan(r,r))}},{key:"_attemptUntilChar",value:function(e){for(;this._peek!==e;)this._advance()}},{key:"_readChar",value:function(e){if(e&&this._peek===bc.$AMPERSAND)return this._decodeEntity();var t=this._index;return this._advance(),this._input[t]}},{key:"_decodeEntity",value:function(){var e=this._getLocation();if(this._advance(),!this._attemptCharCode(bc.$HASH)){var t=this._savePosition();if(this._attemptCharCodeUntilFn(b),this._peek!=bc.$SEMICOLON)return this._restorePosition(t),"&";this._advance();var r=this._input.substring(e.offset+1,this._index-1),n=Dc.NAMED_ENTITIES[r];if(!n)throw this._createError(h(r),this._getSpan(e));return n}var i=this._attemptCharCode(bc.$x)||this._attemptCharCode(bc.$X),a=this._getLocation().offset;if(this._attemptCharCodeUntilFn(v),this._peek!=bc.$SEMICOLON)throw this._createError(f(this._peek),this._getSpan());this._advance();var o=this._input.substring(a,this._index-1);try{var u=parseInt(o,i?16:10);return String.fromCharCode(u)}catch(t){var s=this._input.substring(e.offset+1,this._index-1);throw this._createError(h(s),this._getSpan(e))}}},{key:"_consumeRawText",value:function(e,t,r){var i,a=this._getLocation();this._beginToken(e?n.ESCAPABLE_RAW_TEXT:n.RAW_TEXT,a);for(var o=[];i=this._getLocation(),!this._attemptCharCode(t)||!r();)for(this._index>i.offset&&o.push(this._input.substring(i.offset,this._index));this._peek!==t;)o.push(this._readChar(e));return this._endToken([this._processCarriageReturns(o.join(""))],i)}},{key:"_consumeComment",value:function(e){var t=this;this._beginToken(n.COMMENT_START,e),this._endToken([]);var r=this._consumeRawText(!1,bc.$MINUS,(function(){return t._attemptStr("->")}));this._beginToken(n.COMMENT_END,r.sourceSpan.end),this._endToken([])}},{key:"_consumeBogusComment",value:function(e){this._beginToken(n.COMMENT_START,e),this._endToken([]);var t=this._consumeRawText(!1,bc.$GT,(function(){return!0}));this._beginToken(n.COMMENT_END,t.sourceSpan.end),this._endToken([])}},{key:"_consumeCdata",value:function(e){var t=this;this._beginToken(n.CDATA_START,e),this._endToken([]);var r=this._consumeRawText(!1,bc.$RBRACKET,(function(){return t._attemptStr("]>")}));this._beginToken(n.CDATA_END,r.sourceSpan.end),this._endToken([])}},{key:"_consumeDocType",value:function(e){this._beginToken(n.DOC_TYPE_START,e),this._endToken([]);var t=this._consumeRawText(!1,bc.$GT,(function(){return!0}));this._beginToken(n.DOC_TYPE_END,t.sourceSpan.end),this._endToken([])}},{key:"_consumePrefixAndName",value:function(){for(var e,t,r=this._index,n=null;this._peek!==bc.$COLON&&!(((e=this._peek)<bc.$a||bc.$z<e)&&(e<bc.$A||bc.$Z<e)&&(e<bc.$0||e>bc.$9));)this._advance();return this._peek===bc.$COLON?(this._advance(),n=this._input.substring(r,this._index-1),t=this._index):t=r,this._requireCharCodeUntilFn(m,this._index===t?1:0),[n,this._input.substring(t,this._index)]}},{key:"_consumeTagOpen",value:function(e){var t,r,i=this._savePosition();try{if(!bc.isAsciiLetter(this._peek))throw this._createError(f(this._peek),this._getSpan());var a=this._index;for(this._consumeTagOpenStart(e),r=(t=this._input.substring(a,this._index)).toLowerCase(),this._attemptCharCodeUntilFn(g);this._peek!==bc.$SLASH&&this._peek!==bc.$GT;)this._consumeAttributeName(),this._attemptCharCodeUntilFn(g),this._attemptCharCode(bc.$EQ)&&(this._attemptCharCodeUntilFn(g),this._consumeAttributeValue()),this._attemptCharCodeUntilFn(g);this._consumeTagOpenEnd()}catch(t){if(t instanceof d)return this._restorePosition(i),this._beginToken(n.TEXT,e),void this._endToken(["<"]);throw t}if(!this.canSelfClose||this.tokens[this.tokens.length-1].type!==n.TAG_OPEN_END_VOID){var o=this._getTagDefinition(t).contentType;o===Dc.TagContentType.RAW_TEXT?this._consumeRawTextWithTagClose(r,!1):o===Dc.TagContentType.ESCAPABLE_RAW_TEXT&&this._consumeRawTextWithTagClose(r,!0)}}},{key:"_consumeRawTextWithTagClose",value:function(e,t){var r=this,i=this._consumeRawText(t,bc.$LT,(function(){return!!r._attemptCharCode(bc.$SLASH)&&(r._attemptCharCodeUntilFn(g),!!r._attemptStrCaseInsensitive(e)&&(r._attemptCharCodeUntilFn(g),r._attemptCharCode(bc.$GT)))}));this._beginToken(n.TAG_CLOSE,i.sourceSpan.end),this._endToken([null,e])}},{key:"_consumeTagOpenStart",value:function(e){this._beginToken(n.TAG_OPEN_START,e);var t=this._consumePrefixAndName();this._endToken(t)}},{key:"_consumeAttributeName",value:function(){this._beginToken(n.ATTR_NAME);var e=this._consumePrefixAndName();this._endToken(e)}},{key:"_consumeAttributeValue",value:function(){var e;if(this._beginToken(n.ATTR_VALUE),this._peek===bc.$SQ||this._peek===bc.$DQ){var t=this._peek;this._advance();for(var r=[];this._peek!==t;)r.push(this._readChar(!0));e=r.join(""),this._advance()}else{var i=this._index;this._requireCharCodeUntilFn(m,1),e=this._input.substring(i,this._index)}this._endToken([this._processCarriageReturns(e)])}},{key:"_consumeTagOpenEnd",value:function(){var e=this._attemptCharCode(bc.$SLASH)?n.TAG_OPEN_END_VOID:n.TAG_OPEN_END;this._beginToken(e),this._requireCharCode(bc.$GT),this._endToken([])}},{key:"_consumeTagClose",value:function(e){if(this._beginToken(n.TAG_CLOSE,e),this._attemptCharCodeUntilFn(g),this.allowHtmComponentClosingTags&&this._attemptCharCode(bc.$SLASH))this._attemptCharCodeUntilFn(g),this._requireCharCode(bc.$GT),this._endToken([]);else{var t=this._consumePrefixAndName();this._attemptCharCodeUntilFn(g),this._requireCharCode(bc.$GT),this._endToken(t)}}},{key:"_consumeExpansionFormStart",value:function(){this._beginToken(n.EXPANSION_FORM_START,this._getLocation()),this._requireCharCode(bc.$LBRACE),this._endToken([]),this._expansionCaseStack.push(n.EXPANSION_FORM_START),this._beginToken(n.RAW_TEXT,this._getLocation());var e=this._readUntil(bc.$COMMA);this._endToken([e],this._getLocation()),this._requireCharCode(bc.$COMMA),this._attemptCharCodeUntilFn(g),this._beginToken(n.RAW_TEXT,this._getLocation());var t=this._readUntil(bc.$COMMA);this._endToken([t],this._getLocation()),this._requireCharCode(bc.$COMMA),this._attemptCharCodeUntilFn(g)}},{key:"_consumeExpansionCaseStart",value:function(){this._beginToken(n.EXPANSION_CASE_VALUE,this._getLocation());var e=this._readUntil(bc.$LBRACE).trim();this._endToken([e],this._getLocation()),this._attemptCharCodeUntilFn(g),this._beginToken(n.EXPANSION_CASE_EXP_START,this._getLocation()),this._requireCharCode(bc.$LBRACE),this._endToken([],this._getLocation()),this._attemptCharCodeUntilFn(g),this._expansionCaseStack.push(n.EXPANSION_CASE_EXP_START)}},{key:"_consumeExpansionCaseEnd",value:function(){this._beginToken(n.EXPANSION_CASE_EXP_END,this._getLocation()),this._requireCharCode(bc.$RBRACE),this._endToken([],this._getLocation()),this._attemptCharCodeUntilFn(g),this._expansionCaseStack.pop()}},{key:"_consumeExpansionFormEnd",value:function(){this._beginToken(n.EXPANSION_FORM_END,this._getLocation()),this._requireCharCode(bc.$RBRACE),this._endToken([]),this._expansionCaseStack.pop()}},{key:"_consumeText",value:function(){var e=this._getLocation();this._beginToken(n.TEXT,e);var t=[];do{this._interpolationConfig&&this._attemptStr(this._interpolationConfig.start)?(t.push(this._interpolationConfig.start),this._inInterpolation=!0):this._interpolationConfig&&this._inInterpolation&&this._attemptStr(this._interpolationConfig.end)?(t.push(this._interpolationConfig.end),this._inInterpolation=!1):t.push(this._readChar(!0))}while(!this._isTextEnd());this._endToken([this._processCarriageReturns(t.join(""))])}},{key:"_isTextEnd",value:function(){if(this._peek===bc.$LT||this._peek===bc.$EOF)return!0;if(this._tokenizeIcu&&!this._inInterpolation){if(y(this._input,this._index,this._interpolationConfig))return!0;if(this._peek===bc.$RBRACE&&this._isInExpansionCase())return!0}return!1}},{key:"_savePosition",value:function(){return[this._peek,this._index,this._column,this._line,this.tokens.length]}},{key:"_readUntil",value:function(e){var t=this._index;return this._attemptUntilChar(e),this._input.substring(t,this._index)}},{key:"_restorePosition",value:function(e){this._peek=e[0],this._index=e[1],this._column=e[2],this._line=e[3];var t=e[4];t<this.tokens.length&&(this.tokens=this.tokens.slice(0,t))}},{key:"_isInExpansionCase",value:function(){return this._expansionCaseStack.length>0&&this._expansionCaseStack[this._expansionCaseStack.length-1]===n.EXPANSION_CASE_EXP_START}},{key:"_isInExpansionForm",value:function(){return this._expansionCaseStack.length>0&&this._expansionCaseStack[this._expansionCaseStack.length-1]===n.EXPANSION_FORM_START}}]),e}();function g(e){return!bc.isWhitespace(e)||e===bc.$EOF}function m(e){return bc.isWhitespace(e)||e===bc.$GT||e===bc.$SLASH||e===bc.$SQ||e===bc.$DQ||e===bc.$EQ}function v(e){return e==bc.$SEMICOLON||e==bc.$EOF||!bc.isAsciiHexDigit(e)}function b(e){return e==bc.$SEMICOLON||e==bc.$EOF||!bc.isAsciiLetter(e)}function y(e,t,r){var n=!!r&&e.indexOf(r.start,t)==t;return e.charCodeAt(t)==bc.$LBRACE&&!n}function E(e){return e>=bc.$a&&e<=bc.$z?e-bc.$a+bc.$A:e}}));h(Tc);Tc.TokenType,Tc.Token,Tc.TokenError,Tc.TokenizeResult,Tc.tokenize;var _c=d((function(e,t){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0});var n=function(e){function t(e,n,i){var a;return r(this,t),(a=s(this,o(t).call(this,n,i))).elementName=e,a}return a(t,e),i(t,null,[{key:"create",value:function(e,r,n){return new t(e,r,n)}}]),t}(Ac.ParseError);t.TreeError=n;var u=function e(t,n){r(this,e),this.rootNodes=t,this.errors=n};t.ParseTreeResult=u;var c=function(){function e(t){r(this,e),this.getTagDefinition=t}return i(e,[{key:"parse",value:function(e,t){var r=this,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:vc.DEFAULT_INTERPOLATION_CONFIG,a=arguments.length>4&&void 0!==arguments[4]&&arguments[4],o=arguments.length>5&&void 0!==arguments[5]&&arguments[5],s=arguments.length>6&&void 0!==arguments[6]&&arguments[6],c=s?this.getTagDefinition:function(e){return r.getTagDefinition(e.toLowerCase())},p=Tc.tokenize(e,t,c,n,i,a,o),f=new l(p.tokens,c,a,o,s).build();return new u(f.rootNodes,p.errors.concat(f.errors))}}]),e}();t.Parser=c;var l=function(){function e(t,n,i,a,o){r(this,e),this.tokens=t,this.getTagDefinition=n,this.canSelfClose=i,this.allowHtmComponentClosingTags=a,this.isTagNameCaseSensitive=o,this._index=-1,this._rootNodes=[],this._errors=[],this._elementStack=[],this._advance()}return i(e,[{key:"build",value:function(){for(;this._peek.type!==Tc.TokenType.EOF;)this._peek.type===Tc.TokenType.TAG_OPEN_START?this._consumeStartTag(this._advance()):this._peek.type===Tc.TokenType.TAG_CLOSE?this._consumeEndTag(this._advance()):this._peek.type===Tc.TokenType.CDATA_START?(this._closeVoidElement(),this._consumeCdata(this._advance())):this._peek.type===Tc.TokenType.COMMENT_START?(this._closeVoidElement(),this._consumeComment(this._advance())):this._peek.type===Tc.TokenType.TEXT||this._peek.type===Tc.TokenType.RAW_TEXT||this._peek.type===Tc.TokenType.ESCAPABLE_RAW_TEXT?(this._closeVoidElement(),this._consumeText(this._advance())):this._peek.type===Tc.TokenType.EXPANSION_FORM_START?this._consumeExpansion(this._advance()):this._peek.type===Tc.TokenType.DOC_TYPE_START?this._consumeDocType(this._advance()):this._advance();return new u(this._rootNodes,this._errors)}},{key:"_advance",value:function(){var e=this._peek;return this._index<this.tokens.length-1&&this._index++,this._peek=this.tokens[this._index],e}},{key:"_advanceIf",value:function(e){return this._peek.type===e?this._advance():null}},{key:"_consumeCdata",value:function(e){var t=this._advance(),r=this._getText(t),n=this._advanceIf(Tc.TokenType.CDATA_END);this._addToParent(new kc.CDATA(r,new Ac.ParseSourceSpan(e.sourceSpan.start,(n||t).sourceSpan.end)))}},{key:"_consumeComment",value:function(e){var t=this._advanceIf(Tc.TokenType.RAW_TEXT),r=this._advanceIf(Tc.TokenType.COMMENT_END),n=null!=t?t.parts[0].trim():null,i=new Ac.ParseSourceSpan(e.sourceSpan.start,(r||t||e).sourceSpan.end);this._addToParent(new kc.Comment(n,i))}},{key:"_consumeDocType",value:function(e){var t=this._advanceIf(Tc.TokenType.RAW_TEXT),r=this._advanceIf(Tc.TokenType.DOC_TYPE_END),n=null!=t?t.parts[0].trim():null,i=new Ac.ParseSourceSpan(e.sourceSpan.start,(r||t||e).sourceSpan.end);this._addToParent(new kc.DocType(n,i))}},{key:"_consumeExpansion",value:function(e){for(var t=this._advance(),r=this._advance(),i=[];this._peek.type===Tc.TokenType.EXPANSION_CASE_VALUE;){var a=this._parseExpansionCase();if(!a)return;i.push(a)}if(this._peek.type===Tc.TokenType.EXPANSION_FORM_END){var o=new Ac.ParseSourceSpan(e.sourceSpan.start,this._peek.sourceSpan.end);this._addToParent(new kc.Expansion(t.parts[0],r.parts[0],i,o,t.sourceSpan)),this._advance()}else this._errors.push(n.create(null,this._peek.sourceSpan,"Invalid ICU message. Missing '}'."))}},{key:"_parseExpansionCase",value:function(){var t=this._advance();if(this._peek.type!==Tc.TokenType.EXPANSION_CASE_EXP_START)return this._errors.push(n.create(null,this._peek.sourceSpan,"Invalid ICU message. Missing '{'.")),null;var r=this._advance(),i=this._collectExpansionExpTokens(r);if(!i)return null;var a=this._advance();i.push(new Tc.Token(Tc.TokenType.EOF,[],a.sourceSpan));var o=new e(i,this.getTagDefinition,this.canSelfClose,this.allowHtmComponentClosingTags,this.isTagNameCaseSensitive).build();if(o.errors.length>0)return this._errors=this._errors.concat(o.errors),null;var u=new Ac.ParseSourceSpan(t.sourceSpan.start,a.sourceSpan.end),s=new Ac.ParseSourceSpan(r.sourceSpan.start,a.sourceSpan.end);return new kc.ExpansionCase(t.parts[0],o.rootNodes,u,t.sourceSpan,s)}},{key:"_collectExpansionExpTokens",value:function(e){for(var t=[],r=[Tc.TokenType.EXPANSION_CASE_EXP_START];;){if(this._peek.type!==Tc.TokenType.EXPANSION_FORM_START&&this._peek.type!==Tc.TokenType.EXPANSION_CASE_EXP_START||r.push(this._peek.type),this._peek.type===Tc.TokenType.EXPANSION_CASE_EXP_END){if(!p(r,Tc.TokenType.EXPANSION_CASE_EXP_START))return this._errors.push(n.create(null,e.sourceSpan,"Invalid ICU message. Missing '}'.")),null;if(r.pop(),0==r.length)return t}if(this._peek.type===Tc.TokenType.EXPANSION_FORM_END){if(!p(r,Tc.TokenType.EXPANSION_FORM_START))return this._errors.push(n.create(null,e.sourceSpan,"Invalid ICU message. Missing '}'.")),null;r.pop()}if(this._peek.type===Tc.TokenType.EOF)return this._errors.push(n.create(null,e.sourceSpan,"Invalid ICU message. Missing '}'.")),null;t.push(this._advance())}}},{key:"_getText",value:function(e){var t=e.parts[0];if(t.length>0&&"\n"==t[0]){var r=this._getParentElement();null!=r&&0==r.children.length&&this.getTagDefinition(r.name).ignoreFirstLf&&(t=t.substring(1))}return t}},{key:"_consumeText",value:function(e){var t=this._getText(e);t.length>0&&this._addToParent(new kc.Text(t,e.sourceSpan))}},{key:"_closeVoidElement",value:function(){var e=this._getParentElement();e&&this.getTagDefinition(e.name).isVoid&&this._elementStack.pop()}},{key:"_consumeStartTag",value:function(e){for(var t=e.parts[0],r=e.parts[1],i=[];this._peek.type===Tc.TokenType.ATTR_NAME;)i.push(this._consumeAttr(this._advance()));var a=this._getElementFullName(t,r,this._getParentElement()),o=!1;if(this._peek.type===Tc.TokenType.TAG_OPEN_END_VOID){this._advance(),o=!0;var u=this.getTagDefinition(a);this.canSelfClose||u.canSelfClose||null!==Dc.getNsPrefix(a)||u.isVoid||this._errors.push(n.create(a,e.sourceSpan,'Only void and foreign elements can be self closed "'.concat(e.parts[1],'"')))}else this._peek.type===Tc.TokenType.TAG_OPEN_END&&(this._advance(),o=!1);var s=this._peek.sourceSpan.start,c=new Ac.ParseSourceSpan(e.sourceSpan.start,s),l=new Ac.ParseSourceSpan(e.sourceSpan.start.moveBy(1),e.sourceSpan.end),p=new kc.Element(a,i,[],c,c,void 0,l);this._pushElement(p),o&&(this._popElement(a),p.endSourceSpan=c)}},{key:"_pushElement",value:function(e){var t=this._getParentElement();t&&this.getTagDefinition(t.name).isClosedByChild(e.name)&&this._elementStack.pop();var r=this.getTagDefinition(e.name),n=this._getParentElementSkippingContainers(),i=n.parent,a=n.container;if(i&&r.requireExtraParent(i.name)){var o=new kc.Element(r.parentToAdd,[],[],e.sourceSpan,e.startSourceSpan,e.endSourceSpan);this._insertBeforeContainer(i,a,o)}this._addToParent(e),this._elementStack.push(e)}},{key:"_consumeEndTag",value:function(e){var t=this.allowHtmComponentClosingTags&&0===e.parts.length?null:this._getElementFullName(e.parts[0],e.parts[1],this._getParentElement());if(this._getParentElement()&&(this._getParentElement().endSourceSpan=e.sourceSpan),t&&this.getTagDefinition(t).isVoid)this._errors.push(n.create(t,e.sourceSpan,'Void elements do not have end tags "'.concat(e.parts[1],'"')));else if(!this._popElement(t)){var r='Unexpected closing tag "'.concat(t,'". It may happen when the tag has already been closed by another tag. For more info see https://www.w3.org/TR/html5/syntax.html#closing-elements-that-have-implied-end-tags');this._errors.push(n.create(t,e.sourceSpan,r))}}},{key:"_popElement",value:function(e){for(var t=this._elementStack.length-1;t>=0;t--){var r=this._elementStack[t];if(!e||(Dc.getNsPrefix(r.name)?r.name==e:r.name.toLowerCase()==e.toLowerCase()))return this._elementStack.splice(t,this._elementStack.length-t),!0;if(!this.getTagDefinition(r.name).closedByParent)return!1}return!1}},{key:"_consumeAttr",value:function(e){var t=Dc.mergeNsAndName(e.parts[0],e.parts[1]),r=e.sourceSpan.end,n="",i=void 0;if(this._peek.type===Tc.TokenType.ATTR_VALUE){var a=this._advance();n=a.parts[0],r=a.sourceSpan.end,i=a.sourceSpan}return new kc.Attribute(t,n,new Ac.ParseSourceSpan(e.sourceSpan.start,r),i,e.sourceSpan)}},{key:"_getParentElement",value:function(){return this._elementStack.length>0?this._elementStack[this._elementStack.length-1]:null}},{key:"_getParentElementSkippingContainers",value:function(){for(var e=null,t=this._elementStack.length-1;t>=0;t--){if(!Dc.isNgContainer(this._elementStack[t].name))return{parent:this._elementStack[t],container:e};e=this._elementStack[t]}return{parent:null,container:e}}},{key:"_addToParent",value:function(e){var t=this._getParentElement();null!=t?t.children.push(e):this._rootNodes.push(e)}},{key:"_insertBeforeContainer",value:function(e,t,r){if(t){if(e){var n=e.children.indexOf(t);e.children[n]=r}else this._rootNodes.push(r);r.children.push(t),this._elementStack.splice(this._elementStack.indexOf(t),0,r)}else this._addToParent(r),this._elementStack.push(r)}},{key:"_getElementFullName",value:function(e,t,r){return null==e&&null==(e=this.getTagDefinition(t).implicitNamespacePrefix)&&null!=r&&(e=Dc.getNsPrefix(r.name)),Dc.mergeNsAndName(e,t)}}]),e}();function p(e,t){return e.length>0&&e[e.length-1]===t}}));h(_c);_c.TreeError,_c.ParseTreeResult,_c.Parser;var Sc=d((function(e,t){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0});var n=_c;t.ParseTreeResult=n.ParseTreeResult,t.TreeError=n.TreeError;var u=function(e){function t(){return r(this,t),s(this,o(t).call(this,gc.getHtmlTagDefinition))}return a(t,e),i(t,[{key:"parse",value:function(e,r){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:vc.DEFAULT_INTERPOLATION_CONFIG,a=arguments.length>4&&void 0!==arguments[4]&&arguments[4],u=arguments.length>5&&void 0!==arguments[5]&&arguments[5],s=arguments.length>6&&void 0!==arguments[6]&&arguments[6];return c(o(t.prototype),"parse",this).call(this,e,r,n,i,a,u,s)}}]),t}(_c.Parser);t.HtmlParser=u}));h(Sc);Sc.ParseTreeResult,Sc.TreeError,Sc.HtmlParser;var Fc=d((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var r=null,n=function(){return r||(r=new Sc.HtmlParser),r};t.parse=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.canSelfClose,i=void 0!==r&&r,a=t.allowHtmComponentClosingTags,o=void 0!==a&&a,u=t.isTagNameCaseSensitive,s=void 0!==u&&u;return n().parse(e,"angular-html-parser",!1,void 0,i,o,s)}}));h(Fc);Fc.parse;var xc=ic.HTML_ELEMENT_ATTRIBUTES,qc=ic.HTML_TAGS,Nc=ac,Lc=fc.Node,Bc=dc;function Pc(e,t){var n=t.recognizeSelfClosing,u=t.normalizeTagName,c=t.normalizeAttributeName,l=t.allowHtmComponentClosingTags,p=t.isTagNameCaseSensitive,f=Fc,h=kc.RecursiveVisitor,d=kc.visitAll,D=kc.Attribute,g=kc.CDATA,m=kc.Comment,v=kc.DocType,b=kc.Element,y=kc.Text,E=Ac.ParseSourceSpan,C=gc.getHtmlTagDefinition,A=f.parse(e,{canSelfClose:n,allowHtmComponentClosingTags:l,isTagNameCaseSensitive:p}),w=A.rootNodes,k=A.errors;if(0!==k.length){var T=k[0],_=T.msg,S=T.span.start,F=S.line,x=S.col;throw oc(_,{start:{line:F+1,column:x+1}})}var q=function(e){var t=e.name.startsWith(":")?e.name.slice(1).split(":")[0]:null,r=e.nameSpan?e.nameSpan.toString():e.name,n=r.startsWith("".concat(t,":")),i=n?r.slice(t.length+1):r;e.name=i,e.namespace=t,e.hasExplicitNamespace=n},N=function(e,t){var r=e.toLowerCase();return t(r)?r:e};return d(new(function(e){function t(){return r(this,t),s(this,o(t).apply(this,arguments))}return a(t,e),i(t,[{key:"visit",value:function(e){!function(e){if(e instanceof D)e.type="attribute";else if(e instanceof g)e.type="cdata";else if(e instanceof m)e.type="comment";else if(e instanceof v)e.type="docType";else if(e instanceof b)e.type="element";else{if(!(e instanceof y))throw new Error("Unexpected node ".concat(JSON.stringify(e)));e.type="text"}}(e),function(e){e instanceof b?(q(e),e.attrs.forEach((function(e){q(e),e.valueSpan?(e.value=e.valueSpan.toString(),/['"]/.test(e.value[0])&&(e.value=e.value.slice(1,-1))):e.value=null}))):e instanceof m?e.value=e.sourceSpan.toString().slice("\x3c!--".length,-"--\x3e".length):e instanceof y&&(e.value=e.sourceSpan.toString())}(e),function(e){if(e instanceof b){var t=C(p?e.name:e.name.toLowerCase());e.namespace&&e.namespace!==t.implicitNamespacePrefix?e.tagDefinition=C(""):e.tagDefinition=t}}(e),function(e){if(e instanceof b&&(!u||e.namespace&&e.namespace!==e.tagDefinition.implicitNamespacePrefix||(e.name=N(e.name,(function(e){return e in qc}))),c)){var t=xc[e.name]||Object.create(null);e.attrs.forEach((function(r){r.namespace||(r.name=N(r.name,(function(r){return e.name in xc&&(r in xc["*"]||r in t)})))}))}}(e),function(e){e.sourceSpan&&e.endSourceSpan&&(e.sourceSpan=new E(e.sourceSpan.start,e.endSourceSpan.end))}(e)}}]),t}(h)),w),w}function Oc(e){return e.sourceSpan.start.offset}function Rc(e){return e.sourceSpan.end.offset}function Ic(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.recognizeSelfClosing,r=void 0!==t&&t,n=e.normalizeTagName,i=void 0!==n&&n,a=e.normalizeAttributeName,o=void 0!==a&&a,u=e.allowHtmComponentClosingTags,s=void 0!==u&&u,c=e.isTagNameCaseSensitive,l=void 0!==c&&c;return{parse:function(e,t,n){return function e(t,r,n){var i=!(arguments.length>3&&void 0!==arguments[3])||arguments[3]?lo(t):{frontMatter:null,content:t},a=i.frontMatter,o=i.content,u={type:"root",sourceSpan:{start:{offset:0},end:{offset:t.length}},children:Pc(o,n)};a&&u.children.unshift(a);var s=new Lc(u),c=function(i,a){var o=a.offset,u=e(t.slice(0,o).replace(/[^\r\n]/g," ")+i,r,n,!1),s=u.children[0].sourceSpan.constructor;u.sourceSpan=new s(a,u.children[u.children.length-1].sourceSpan.end);var c=u.children[0];return c.length===o?u.children.shift():(c.sourceSpan=new s(c.sourceSpan.start.moveBy(o),c.sourceSpan.end),c.value=c.value.slice(o)),u},l=function(e){return"element"===e.type&&!e.nameSpan};return s.map((function(e){if(e.children&&e.children.some(l)){var t=[],r=!0,n=!1,i=void 0;try{for(var a,o=e.children[Symbol.iterator]();!(r=(a=o.next()).done);r=!0){var u=a.value;l(u)?Array.prototype.push.apply(t,u.children):t.push(u)}}catch(e){n=!0,i=e}finally{try{r||null==o.return||o.return()}finally{if(n)throw i}}return e.clone({children:t})}if("comment"===e.type){var s=Bc(e,c);if(s)return s}return e}))}(e,n,{recognizeSelfClosing:r,normalizeTagName:i,normalizeAttributeName:o,allowHtmComponentClosingTags:s,isTagNameCaseSensitive:l})},hasPragma:Nc,astFormat:"html",locStart:Oc,locEnd:Rc}}var Uc={parsers:{html:Ic({recognizeSelfClosing:!0,normalizeTagName:!0,normalizeAttributeName:!0,allowHtmComponentClosingTags:!0}),angular:Ic(),vue:Ic({recognizeSelfClosing:!0,isTagNameCaseSensitive:!0}),lwc:Ic()}},$c=Ro.mapAst,Vc=Ro.INLINE_NODE_WRAPPER_TYPES,Mc=Uc.parsers.html;function jc(e){var t=e.isMDX;return function(e){var r=Za().use(ji,Object.assign({footnotes:!0,commonmark:!0},t&&{blocks:[jo.BLOCKS_REGEX]})).use(Hc).use(ru).use(t?jo.esSyntax:zc).use(Xc).use(t?Gc:zc);return r.runSync(r.parse(e))}}function zc(e){return e}function Gc(){return function(e){return $c(e,(function(e,t,r){var n=l(r,1)[0];if("html"!==e.type||e.value.match(jo.COMMENT_REGEX)||-1!==Vc.indexOf(n.type))return e;var i=Mc.parse(e.value).children;return i.length<=1?Object.assign({},e,{type:"jsx"}):i.reduce((function(t,r){var n=r.sourceSpan,i=r.type,a=e.value.slice(n.start.offset,n.end.offset);return a&&t.push({type:"element"===i?"jsx":i,value:a,position:n}),t}),[])}))}}function Hc(){var e=this.Parser.prototype;function t(e,t){var r=lo(t);if(r.frontMatter)return e(r.frontMatter.raw)(r.frontMatter)}e.blockMethods=["frontMatter"].concat(e.blockMethods),e.blockTokenizers.frontMatter=t,t.onlyAtStart=!0}function Xc(){var e=this.Parser.prototype,t=e.inlineMethods;function r(e,t){var r=t.match(/^({%[\s\S]*?%}|{{[\s\S]*?}})/);if(r)return e(r[0])({type:"liquidNode",value:r[0]})}t.splice(t.indexOf("text"),0,"liquid"),e.inlineTokenizers.liquid=r,r.locator=function(e,t){return e.indexOf("{",t)}}var Wc={astFormat:"mdast",hasPragma:ho.hasPragma,locStart:function(e){return e.position.start.offset},locEnd:function(e){return e.position.end.offset},preprocess:function(e){return e.replace(/\n\s+$/,"\n")}},Qc=Object.assign({},Wc,{parse:jc({isMDX:!1})}),Yc={parsers:{remark:Qc,markdown:Qc,mdx:Object.assign({},Wc,{parse:jc({isMDX:!0})})}},Zc=Yc.parsers;e.default=Yc,e.parsers=Zc,Object.defineProperty(e,"__esModule",{value:!0})}));
