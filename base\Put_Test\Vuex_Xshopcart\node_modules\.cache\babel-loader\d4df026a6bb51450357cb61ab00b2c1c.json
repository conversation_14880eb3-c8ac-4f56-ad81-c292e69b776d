{"remainingRequest": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\@babel\\runtime\\helpers\\esm\\defineProperty.js", "dependencies": [{"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\@babel\\runtime\\helpers\\esm\\defineProperty.js", "mtime": 1657986342858}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1657986293299}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1657986298727}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gX2RlZmluZVByb3BlcnR5KG9iaiwga2V5LCB2YWx1ZSkgewogIGlmIChrZXkgaW4gb2JqKSB7CiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkob2JqLCBrZXksIHsKICAgICAgdmFsdWU6IHZhbHVlLAogICAgICBlbnVtZXJhYmxlOiB0cnVlLAogICAgICBjb25maWd1cmFibGU6IHRydWUsCiAgICAgIHdyaXRhYmxlOiB0cnVlCiAgICB9KTsKICB9IGVsc2UgewogICAgb2JqW2tleV0gPSB2YWx1ZTsKICB9CgogIHJldHVybiBvYmo7Cn0="}, {"version": 3, "sources": ["D:/No.1 folder/No.2 folder/dev/dev2/vue/base/Vuex_Xshopcart/node_modules/@babel/runtime/helpers/esm/defineProperty.js"], "names": ["_defineProperty", "obj", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable"], "mappings": "AAAA,eAAe,SAASA,eAAT,CAAyBC,GAAzB,EAA8BC,GAA9B,EAAmCC,KAAnC,EAA0C;AACvD,MAAID,GAAG,IAAID,GAAX,EAAgB;AACdG,IAAAA,MAAM,CAACC,cAAP,CAAsBJ,GAAtB,EAA2BC,GAA3B,EAAgC;AAC9BC,MAAAA,KAAK,EAAEA,KADuB;AAE9BG,MAAAA,UAAU,EAAE,IAFkB;AAG9BC,MAAAA,YAAY,EAAE,IAHgB;AAI9BC,MAAAA,QAAQ,EAAE;AAJoB,KAAhC;AAMD,GAPD,MAOO;AACLP,IAAAA,GAAG,CAACC,GAAD,CAAH,GAAWC,KAAX;AACD;;AAED,SAAOF,GAAP;AACD", "sourcesContent": ["export default function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}"]}]}