{"extends": "http://json-schema.org/hyper-schema", "description": "A schema for schema interface definitions that describe programmatic class structures using JSON schema syntax", "properties": {"methods": {"type": "object", "description": "This defines the set of methods available to the class instances", "additionalProperties": {"type": "object", "description": "The definition of the method", "properties": {"parameters": {"type": "array", "description": "The set of parameters that should be passed to the method when it is called", "items": {"$ref": "#"}, "required": true}, "returns": {"$ref": "#"}}}}}}