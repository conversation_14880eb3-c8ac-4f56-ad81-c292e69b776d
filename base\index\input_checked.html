<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        .main {
            width: 500px;
            margin: 50px auto;
            padding: 15px;
            background-color: antiquewhite;
        }
    </style>
</head>
<body>
    <div class="main">
        <label v-for="item in lableArr">
            <input type="checkbox" :value="item.id" v-model="checkboxValue"/>
            {{item.value}}
            <br/>
        </label>
        <p>选中结果: {{checkboxValue}}</p>
        <!-- <label v-for="item1 in lableArr1">
            <input type="checkbox" :value="item1" v-model="checkboxValue1"/>
            {{item1}}
            <br/>
        </label> -->
        <!-- <p>选中结果：{{checkboxValue1}}</p>
        <select v-model="selected">
            <option disabled value="">请选择</option>
            <option value="1">A</option>
            <option value="2">B</option>
            <option value="3">C</option>
          </select>
        <span>Selected: {{ selected }}</span> -->
    </div>
<script src="https://cdn.bootcdn.net/ajax/libs/vue/2.6.11/vue.js"></script>
<script>
    var vueMain = new Vue({
        el: '.main',
        data: {
            lableArr: [
                {id:1, value:'篮球'},
                {id:2, value:'足球'},
                {id:3, value:'网球'},
                {id:4, value:'排球'},
                {id:5, value:'羽毛球'}
            ],
            // lableArr1: ['篮球', '足球', '网球', '排球', '羽毛球'],
            checkboxValue: [1,3,5],
            // checkboxValue1: ['篮球', '足球', '网球'],
            // selected: ''
        }
    })
</script>
</body>
</html>





<!-- <!DOCTYPE html>  
<html>  
<head>  
    <meta charset="utf-8">  
    <title>test</title>  
    <script src="https://cdn.bootcss.com/vue/2.2.2/vue.min.js"></script>  
</head>  
<body>  
    <div id='app'>
      <input type="checkbox" name="checkBoxTest" :value='0' v-model="place"  @click="checked" />北京
      <input type="checkbox" name="checkBoxTest" :value='1' v-model="place"  @click="checked" />上海
      <input type="checkbox" name="checkBoxTest" :value='2' v-model="place"  @click="checked" />广州
      <input type="checkbox" name="checkBoxTest" :value='3' v-model="place"  @click="checked" />深圳
          
      <div>{{place}} || 99</div>
    </div>
<script>
var vm = new Vue({
  el:'#app', 
  data(){
    return{
      place:[]
    }
  },
  methods:{
    checked(){
      console.log('被选中的值为:'+ this.place)
      localStorage.setItem("placeArr",this.place)
    }
  }
});
</script>
</body>  
</html> -->