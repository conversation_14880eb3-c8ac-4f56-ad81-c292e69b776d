<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>组件：子传父</title>
    <style>
    </style>
</head>

<body>
    <div id="app">
        <child @ztof="fafun"></child>
    </div>
    <script src="https://cdn.staticfile.org/vue/2.2.2/vue.min.js"></script>
    <script>
        Vue.component('child',{
            // template:`
            //     <button>子组件</button>
            // `,
            data(){
                return {
                    zmsg:'子的信息xxxx'
                }
            },
            created() {
                this.$emit('ztof',this.zmsg)
            }

        })
        const vm = new Vue({
           el:'#app',
           data() {
               return {
               }
           },
           methods:{
            fafun(zmsg){
                console.log('zmsg :>> ', zmsg);
            }
           }


        });
    </script>
</body>

</html>