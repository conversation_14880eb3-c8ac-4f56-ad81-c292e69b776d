{"name": "postcss-discard-duplicates", "version": "4.0.2", "description": "Discard duplicate rules in your CSS files with PostCSS.", "main": "dist/index.js", "files": ["dist", "LICENSE-MIT"], "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "keywords": ["css", "dedupe", "optimise", "postcss", "postcss-plugin"], "license": "MIT", "dependencies": {"postcss": "^7.0.0"}, "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0"}, "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": ">=6.9.0"}}