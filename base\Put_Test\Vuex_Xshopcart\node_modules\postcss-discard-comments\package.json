{"name": "postcss-discard-comments", "version": "4.0.2", "description": "Discard comments in your CSS files with PostCSS.", "main": "dist/index.js", "files": ["dist", "LICENSE-MIT"], "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "keywords": ["css", "comments", "postcss", "postcss-plugin"], "license": "MIT", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0", "postcss-scss": "^2.0.0", "postcss-simple-vars": "^5.0.1"}, "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "dependencies": {"postcss": "^7.0.0"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": ">=6.9.0"}}