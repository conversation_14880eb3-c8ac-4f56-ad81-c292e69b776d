{"name": "renderkid", "version": "2.0.3", "description": "Stylish console.log for node", "main": "lib/RenderKid.js", "dependencies": {"css-select": "^1.1.0", "dom-converter": "^0.2", "htmlparser2": "^3.3.0", "strip-ansi": "^3.0.0", "utila": "^0.4.0"}, "devDependencies": {"chai": "^4.1.2", "chai-changes": "^1.3.4", "chai-fuzzy": "^1.5.0", "coffee-script": "^1.9.1", "jitter": "^1.3.0", "mocha": "^5.2.0", "mocha-pretty-spec-reporter": "0.1.0-beta.2", "sinon": "^1.14.1", "sinon-chai": "^2.7.0", "underscore": "^1.8.3"}, "scripts": {"test": "mocha \"test/**/*.coffee\"", "test:watch": "mocha \"test/**/*.coffee\" --watch", "compile": "coffee --bare --compile --output ./lib ./src", "compile:watch": "jitter src lib -b", "watch": "npm run compile:watch & npm run test:watch", "winwatch": "start/b npm run compile:watch & npm run test:watch", "prepublish": "npm run compile"}, "repository": {"type": "git", "url": "https://github.com/AriaMinaei/RenderKid.git"}, "bugs": {"url": "https://github.com/AriaMinaei/RenderKid/issues"}, "author": "<PERSON>", "license": "MIT"}