{"name": "@vue/cli-plugin-vuex", "version": "4.5.6", "description": "Vuex plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-vuex"}, "keywords": ["vue", "cli", "vuex"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-vuex#readme", "publishConfig": {"access": "public"}, "devDependencies": {"@vue/cli-test-utils": "^4.5.6"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0"}, "gitHead": "6cac3af2dffbb3a770c8d89f1ac1c9b5f84f7fdb"}