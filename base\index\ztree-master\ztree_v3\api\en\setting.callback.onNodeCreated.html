<div class="apiDetail">
<div>
	<h2><span>Function(event, treeId, treeNode)</span><span class="path">setting.callback.</span>onNodeCreated</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Used to capture the event when node's DOM is created.</p>
			<p class="highlight_red">Because v3.x uses lazy loading technology, so the nodes which doesn't create DOM when initialized will not trigger this callback, until its parent node is expanded.</p>
			<p class="highlight_red">Large amount of data to load, please note: do not set onNodeCreated, can improve performance as when initialized.</p>
			<p>Default: null</p>
		</div>
	</div>
	<h3>Function Parameter Descriptions</h3>
	<div class="desc">
	<h4><b>event</b><span>js event Object</span></h4>
	<p>event Object</p>
	<h4 class="topLine"><b>treeId</b><span>String</span></h4>
	<p>zTree unique identifier: <b class="highlight_red">treeId</b>, easy for users to control.</p>
	<h4 class="topLine"><b>treeNode</b><span>JSON</span></h4>
	<p>JSON data object of the node which DOM is created</p>
	</div>
	<h3>Examples of setting & function</h3>
	<h4>1. When node's DOM is created, alert info about 'tId' and 'name'.</h4>
	<pre xmlns=""><code>function zTreeOnNodeCreated(event, treeId, treeNode) {
    alert(treeNode.tId + ", " + treeNode.name);
};
var setting = {
	callback: {
		onNodeCreated: zTreeOnNodeCreated
	}
};
......</code></pre>
</div>
</div>