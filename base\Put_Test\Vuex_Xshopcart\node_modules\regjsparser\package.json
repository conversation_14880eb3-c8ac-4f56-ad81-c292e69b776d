{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.6.4", "author": "'<PERSON>' <<EMAIL>>", "license": "BSD-2-<PERSON><PERSON>", "main": "./parser", "bin": "bin/parser", "homepage": "https://github.com/jviereck/regjsparser", "repository": {"type": "git", "url": "**************:jviereck/regjsparser.git"}, "scripts": {"test": "node test/index.js"}, "files": ["bin/", "LICENSE.BSD", "parser.js", "README.md"], "dependencies": {"jsesc": "~0.5.0"}, "devDependencies": {"regenerate": "~1.0.1", "unicode-11.0.0": "^0.7.8"}}