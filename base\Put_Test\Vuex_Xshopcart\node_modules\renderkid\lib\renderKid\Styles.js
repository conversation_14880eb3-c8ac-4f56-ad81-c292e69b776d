// Generated by CoffeeScript 1.9.3
var MixedDeclarationSet, StyleSheet, Styles, terminalWidth;

StyleSheet = require('./styles/StyleSheet');

MixedDeclarationSet = require('./styles/rule/MixedDeclarationSet');

terminalWidth = require('../tools').getCols();

module.exports = Styles = (function() {
  var self;

  self = Styles;

  Styles.defaultRules = {
    '*': {
      display: 'inline'
    },
    'body': {
      background: 'none',
      color: 'white',
      display: 'block',
      width: terminalWidth + ' !important'
    }
  };

  function Styles() {
    this._defaultStyles = new StyleSheet;
    this._userStyles = new StyleSheet;
    this._setDefaultStyles();
  }

  Styles.prototype._setDefaultStyles = function() {
    this._defaultStyles.setRule(self.defaultRules);
  };

  Styles.prototype.setRule = function(selector, rules) {
    this._userStyles.setRule.apply(this._userStyles, arguments);
    return this;
  };

  Styles.prototype.getStyleFor = function(el) {
    var styles;
    styles = el.styles;
    if (styles == null) {
      el.styles = styles = this._getComputedStyleFor(el);
    }
    return styles;
  };

  Styles.prototype._getRawStyleFor = function(el) {
    var def, user;
    def = this._defaultStyles.getRulesFor(el);
    user = this._userStyles.getRulesFor(el);
    return MixedDeclarationSet.mix(def, user).toObject();
  };

  Styles.prototype._getComputedStyleFor = function(el) {
    var decs, parent, prop, ref, val;
    decs = {};
    parent = el.parent;
    ref = this._getRawStyleFor(el);
    for (prop in ref) {
      val = ref[prop];
      if (val !== 'inherit') {
        decs[prop] = val;
      } else {
        throw Error("Inherited styles are not supported yet.");
      }
    }
    return decs;
  };

  return Styles;

})();
