{"name": "@vue/babel-sugar-v-on", "version": "1.1.2", "description": "Babel syntactic sugar for v-model support in Vue JSX", "main": "dist/plugin.js", "repository": "https://github.com/vuejs/jsx/tree/master/packages/babel-sugar-v-on", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "private": false, "files": [], "scripts": {"pretest:snapshot": "yarn build:test", "test:snapshot": "nyc --reporter=html --reporter=text-summary ava -v test/snapshot.js", "pretest:functional": "yarn build:test && nyc --reporter=html --reporter=text-summary babel test/functional.js --plugins ./dist/plugin.testing.js,./node_modules/@vue/babel-plugin-transform-vue-jsx/dist/plugin.js --out-file test/functional-compiled.js", "test:functional": "ava -v test/functional-compiled.js", "build": "rollup -c", "build:test": "rollup -c rollup.config.testing.js", "test": "rm -rf coverage* && yarn test:snapshot && mv coverage coverage-snapshot && yarn test:functional && mv coverage coverage-functional", "prepublish": "yarn build"}, "devDependencies": {"@babel/cli": "^7.2.0", "@babel/core": "^7.2.0", "@babel/preset-env": "^7.2.0", "@vue/test-utils": "^1.0.0-beta.26", "ava": "^0.25.0", "jsdom": "^13.0.0", "jsdom-global": "^3.0.2", "ninos": "^2.0.2", "nyc": "^13.1.0", "rollup": "^0.67.4", "rollup-plugin-babel": "4.0.3", "rollup-plugin-babel-minify": "^6.2.0", "rollup-plugin-istanbul": "^2.0.1", "vue": "^2.5.17", "vue-template-compiler": "^2.5.17"}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.2.0", "@vue/babel-plugin-transform-vue-jsx": "^1.1.2", "camelcase": "^5.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "nyc": {"exclude": ["dist", "test"]}, "gitHead": "67d6d39beab9f853118b4e0bbe901f5899ae7245"}