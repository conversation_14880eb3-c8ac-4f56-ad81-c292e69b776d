<!DOCTYPE html>
<html lang="en" data-bs-theme="light">

<head>
    <meta charset="UTF-8">
    <title>Sedentary Reminder - Healthy Office Assistant</title>
    <meta name="description"
        content="Professional sedentary reminder tool to help you develop healthy office habits and prevent health problems caused by prolonged sitting. Supports custom reminder times and desktop notifications.">

    <meta name="twitter:title" content="Sedentary Reminder - Healthy Office Assistant">
    <meta name="twitter:description"
        content="Professional sedentary reminder tool to help you develop healthy office habits and prevent health problems caused by prolonged sitting. Supports custom reminder times and desktop notifications.">
    <meta name="twitter:url" content="https://sedentaryreminder.top/">
    <meta name="twitter:image" content="https://sedentaryreminder.top/assets/sedentary-reminder.jpg">
    <meta name="twitter:card" content="summary">

    <meta property="og:type" content="website">
    <meta property="og:url" content="https://sedentaryreminder.top/">
    <meta property="og:title" content="Sedentary Reminder - Healthy Office Assistant">
    <meta property="og:description"
        content="Professional sedentary reminder tool to help you develop healthy office habits and prevent health problems caused by prolonged sitting. Supports custom reminder times and desktop notifications.">
    <meta property="og:image" content="https://sedentaryreminder.top/assets/sedentary-reminder.jpg">

    <link rel="apple-touch-icon" sizes="180x180" href="./assets/favicon.svg">
    <link rel="icon" type="image/png" sizes="32x32" href="./assets/favicon.svg">
    <link rel="icon" type="image/png" sizes="16x16" href="./assets/favicon.svg">
    <link rel="icon" type="image/svg+xml" href="./assets/favicon.svg">

    <meta name="mobile-web-app-capable" content="yes">
    <link rel="manifest" href="https://sedentaryreminder.top/public/icons/manifest.json">

    <link rel="canonical" href="https://sedentaryreminder.top/">
    <link rel="alternate" hreflang="x-default" href="https://sedentaryreminder.top/">
    <link rel="alternate" hreflang="en" href="https://sedentaryreminder.top/">
    <link rel="alternate" hreflang="zh-CN" href="https://sedentaryreminder.top/zh-cn/">
    <link rel="alternate" hreflang="da" href="https://sedentaryreminder.top/da/">
    <link rel="alternate" hreflang="de" href="https://sedentaryreminder.top/de/">
    <link rel="alternate" hreflang="es" href="https://sedentaryreminder.top/es/">
    <link rel="alternate" hreflang="fr" href="https://sedentaryreminder.top/fr/">
    <link rel="alternate" hreflang="he" href="https://sedentaryreminder.top/he/">
    <link rel="alternate" hreflang="it" href="https://sedentaryreminder.top/it/">
    <link rel="alternate" hreflang="ja" href="https://sedentaryreminder.top/ja/">
    <link rel="alternate" hreflang="ko" href="https://sedentaryreminder.top/ko/">
    <link rel="alternate" hreflang="nl" href="https://sedentaryreminder.top/nl/">
    <link rel="alternate" hreflang="pl" href="https://sedentaryreminder.top/pl/">
    <link rel="alternate" hreflang="pt" href="https://sedentaryreminder.top/pt/">
    <link rel="alternate" hreflang="ru" href="https://sedentaryreminder.top/ru/">
    <link rel="alternate" hreflang="sv" href="https://sedentaryreminder.top/sv/">
    <link rel="alternate" hreflang="uk" href="https://sedentaryreminder.top/uk/">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/style.css">

    <!-- <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-1192275768252854"
        crossorigin="anonymous"></script>
    <link rel='stylesheet' href='https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&amp;display=swap'> -->

    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css'>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=0">
    <style>
        .progress-ring {
            transform: rotate(-90deg);
        }

        .progress-ring-circle {
            transition: stroke-dashoffset 0.35s;
            transform-origin: 50% 50%;
        }



        .stat-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: none;
            transition: transform 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .harm-card {
            border-left: 4px solid #dc3545;
            background: linear-gradient(135deg, #fff5f5, #fff);
        }

        .tip-card {
            border-left: 4px solid #198754;
            background: linear-gradient(135deg, #f0fff4, #fff);
        }

        .work-card {
            border-left: 4px solid #4682B4;
            background: linear-gradient(135deg, #f0fff4, #fff);
        }

        .notification-permission {
            position: fixed;
            bottom: 20px;
            right: 20px;
            max-width: 300px;
            z-index: 1050;
        }

        .notification-permission .fas.fa-bell {
            font-size: 0.9rem;
            color: #856404;
        }

        .notification-permission .btn-close {
            font-size: 1rem;
            padding: 0;
        }

        /* Language change notification */
        .language-notification {
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }

            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Language dropdown active state */
        .dropdown-item.active {
            background-color: var(--primary-color);
            color: #4D4D4D;
        }

        .dropdown-item:hover {
            background-color: var(--primary-light);
            color: #4D4D4D;
            font-weight: 700;
        }

        #testSoundBtn {
            min-width: 70px;
            max-width: 130px;
            padding: 0.375rem 0.4rem;
            white-space: nowrap;
            flex-shrink: 0;
            text-align: center;
        }

        .text-coloring {
            border: 1px solid #dee2e6 !important;
        }

        /* 为包含按钮的容器添加样式 */
        .d-flex.gap-2 {
            align-items: stretch;
        }

        .d-flex.gap-2 .form-select {
            flex: 1;
        }

        .modal-dialog {
            margin-top: 148px;
        }
    </style>
</head>

<body class="bg">
    <!-- Header -->
    <header>
        <nav class="d-flex p-3">
            <!-- Left Section (Language Selector) -->
            <div class="d-flex m-0">
                <ul class="navbar-nav d-flex align-items-center mb-0">
                    <li class="nav-item dropdown m-0">
                        <i class="nav-link hover-effect d-flex align-items-center justify-content-center fa-solid fa-earth-americas m-0 sandal"
                            role="button" data-bs-toggle="dropdown" data-bs-auto-close="outside"
                            aria-expanded="false"></i>
                        <ul class="dropdown-menu shadow">
                            <li><a class="dropdown-item" href="#"
                                    onclick="changeLanguage('en'); return false;">English</a></li>
                            <li><a class="dropdown-item" href="#"
                                    onclick="changeLanguage('zh-cn'); return false;">中文</a></li>
                            <li><a class="dropdown-item" href="#"
                                    onclick="changeLanguage('da'); return false;">Dansk</a></li>
                            <li><a class="dropdown-item" href="#"
                                    onclick="changeLanguage('es'); return false;">Español</a></li>
                            <li><a class="dropdown-item" href="#"
                                    onclick="changeLanguage('de'); return false;">Deutsch</a></li>
                            <li><a class="dropdown-item" href="#"
                                    onclick="changeLanguage('fr'); return false;">Français</a></li>
                            <li><a class="dropdown-item" href="#"
                                    onclick="changeLanguage('he'); return false;">עברית</a></li>
                            <li><a class="dropdown-item" href="#"
                                    onclick="changeLanguage('it'); return false;">Italiano</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeLanguage('ja'); return false;">日本語</a>
                            </li>
                            <li><a class="dropdown-item" href="#" onclick="changeLanguage('ko'); return false;">한국어</a>
                            </li>
                            <li><a class="dropdown-item" href="#"
                                    onclick="changeLanguage('nl'); return false;">Nederlands</a></li>
                            <li><a class="dropdown-item" href="#"
                                    onclick="changeLanguage('pl'); return false;">Polski</a></li>
                            <li><a class="dropdown-item" href="#"
                                    onclick="changeLanguage('pt'); return false;">Português</a></li>
                            <li><a class="dropdown-item" href="#"
                                    onclick="changeLanguage('ru'); return false;">Русский</a></li>
                            <li><a class="dropdown-item" href="#"
                                    onclick="changeLanguage('sv'); return false;">Svenska</a></li>
                            <li><a class="dropdown-item" href="#"
                                    onclick="changeLanguage('uk'); return false;">Українська</a></li>
                        </ul>
                    </li>
                </ul>
            </div>

            <!-- Center Section (Logo and Title) -->
            <div class="text-center flex-grow-1 m-0">
                <h1 class="h2 responsive-h1 m-0 d-inline">
                    <svg t="1754618831215" class="icon" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="2320" id="mx_n_1754618831215" width="32" height="32">
                        <path d="M501.48 493.55m-233.03 0a233.03 233.03 0 1 0 466.06 0 233.03 233.03 0 1 0-466.06 0Z"
                            fill="#ea9518" p-id="2321"></path>
                        <path
                            d="M501.52 185.35H478.9c-8.28 0-15-6.72-15-15V87.59c0-8.28 6.72-15 15-15h22.62c8.28 0 15 6.72 15 15v82.76c0 8.28-6.72 15-15 15zM281.37 262.76l-16 16c-5.86 5.86-15.36 5.86-21.21 0l-58.52-58.52c-5.86-5.86-5.86-15.36 0-21.21l16-16c5.86-5.86 15.36-5.86 21.21 0l58.52 58.52c5.86 5.86 5.86 15.35 0 21.21zM185.76 478.48v22.62c0 8.28-6.72 15-15 15H88c-8.28 0-15-6.72-15-15v-22.62c0-8.28 6.72-15 15-15h82.76c8.28 0 15 6.72 15 15zM270.69 698.63l16 16c5.86 5.86 5.86 15.36 0 21.21l-58.52 58.52c-5.86 5.86-15.36 5.86-21.21 0l-16-16c-5.86-5.86-5.86-15.36 0-21.21l58.52-58.52c5.85-5.86 15.35-5.86 21.21 0zM486.41 794.24h22.62c8.28 0 15 6.72 15 15V892c0 8.28-6.72 15-15 15h-22.62c-8.28 0-15-6.72-15-15v-82.76c0-8.28 6.72-15 15-15zM706.56 709.31l16-16c5.86-5.86 15.36-5.86 21.21 0l58.52 58.52c5.86 5.86 5.86 15.36 0 21.21l-16 16c-5.86 5.86-15.36 5.86-21.21 0l-58.52-58.52c-5.86-5.85-5.86-15.35 0-21.21zM802.17 493.59v-22.62c0-8.28 6.72-15 15-15h82.76c8.28 0 15 6.72 15 15v22.62c0 8.28-6.72 15-15 15h-82.76c-8.28 0-15-6.72-15-15zM717.24 273.44l-16-16c-5.86-5.86-5.86-15.36 0-21.21l58.52-58.52c5.86-5.86 15.36-5.86 21.21 0l16 16c5.86 5.86 5.86 15.36 0 21.21l-58.52 58.52c-5.86 5.86-15.35 5.86-21.21 0z"
                            fill="#ea9518" p-id="2322"></path>
                    </svg>
                    <span class="align-middle" data-translate="app-title">Sedentary Reminder</span>
                </h1>
                <p class="mb-0" data-translate="app-subtitle">Make healthy office habits</p>
            </div>

            <!-- Right Section (Settings) -->
            <div class="d-flex m-0">
                <ul class="navbar-nav d-flex align-items-center mb-0">
                    <li class="nav-item m-0">
                        <i class="nav-link hover-effect d-flex align-items-center justify-content-center fa fa-cog m-0 sandal"
                            role="button" data-bs-toggle="modal" data-bs-target="#settingsModal"></i>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="container my-5">
        <!-- 提醒设置面板 -->
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <h2 class="card-title h4 mb-3 text-center">
                            <i class="fas fa-cog sandal"></i>
                            <span data-translate="reminder-settings">Reminder Settings</span>
                        </h2>

                        <!-- Time Selection -->
                        <div class="mb-4">
                            <label class="mb-4 form-label fw-semibold h6" data-translate="select-interval">Select
                                Reminder Interval</label>
                            <div class="row g-2" id="timeOptions">
                                <!-- Time options will be dynamically generated by JavaScript -->
                            </div>
                        </div>

                        <!-- Current Status -->
                        <div class="mb-4 p-3 bg-light rounded">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="fw-medium" data-translate="current-status">Current Status:</span>
                                <span id="statusText" class="text-muted" data-translate="stopped">Stopped</span>
                            </div>
                            <div id="progressContainer" class="d-none">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="small text-muted" data-translate="time-until-next">Time until next
                                        reminder:</span>
                                    <span id="remainingTime" class="font-monospace small">00:00</span>
                                </div>
                                <div class="progress h-1">
                                    <div id="progressBar" class="progress-bar" role="progressbar"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Control Buttons -->
                        <div class="d-grid gap-2 d-md-flex mb-4">
                            <button id="startBtn" class="btn btn-success flex-fill">
                                <i class="fas fa-play"></i>
                                <span data-translate="start-reminder">Start Reminder</span>
                            </button>
                            <button id="stopBtn" class="btn btn-danger flex-fill" disabled>
                                <i class="fas fa-stop"></i>
                                <span data-translate="stop-reminder">Stop Reminder</span>
                            </button>
                        </div>

                        <!-- Statistics -->
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="card stat-card text-center">
                                    <div class="card-body py-3">
                                        <div class="h3 mb-1 sandal" id="todayCount">0</div>
                                        <div class="small text-muted" data-translate="today-count">Today's Reminders
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="card stat-card text-center">
                                    <div class="card-body py-3">
                                        <div class="h3 mb-1 light-green" id="totalCount">0</div>
                                        <div class="small text-muted" data-translate="total-count">Total Reminders</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 久坐危害 -->
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8">
                <div class="card shadow-sm harm-card">
                    <div class="card-body">
                        <h3 class="card-title h5 mb-3 text-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span data-translate="sitting-harms">Harms of Prolonged Sitting</span>
                        </h3>
                        <div id="harmsList">
                            <!-- 危害列表将通过 JavaScript 动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 预防建议 -->
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8">
                <div class="card shadow-sm tip-card">
                    <div class="card-body">
                        <h3 class="card-title h5 mb-3 natural-green">
                            <i class="fas fa-lightbulb"></i>
                            <span data-translate="prevention-tips">Prevention Tips</span>
                        </h3>
                        <div id="tipsList">
                            <!-- 建议列表将通过 JavaScript 动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 久坐提醒助手如何工作 -->
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8">
                <div class="card shadow-sm work-card">
                    <div class="card-body">
                        <h3 class="h5 mb-4 text-left official-green">
                            <i class="fas fa-question-circle text-info-emphasis"></i>
                            <span data-translate="how-it-works">How Sedentary Reminder Works?</span>
                        </h3>
                        <p data-translate="how-it-works-desc">Sedentary Reminder is a professional healthy office tool
                            designed to help you develop the habit of getting up and moving regularly. According to
                            medical research, prolonged sitting can cause various health problems, including lumbar disc
                            herniation, cardiovascular disease, metabolic disorders, etc.</p>

                        <h3 class="h5 mt-4 mb-3" data-translate="why-reminders">Why Do You Need Regular Reminders?</h3>
                        <p data-translate="why-reminders-desc">In modern office environments, we can easily get immersed
                            in work and forget about time. Regular reminders can help you:</p>
                        <ul>
                            <li data-translate="benefit-1">Maintain physical vitality and prevent sedentary-related
                                diseases</li>
                            <li data-translate="benefit-2">Improve work efficiency, proper rest helps brain recovery
                            </li>
                            <li data-translate="benefit-3">Improve blood circulation and reduce fatigue</li>
                            <li data-translate="benefit-4">Protect eyesight and reduce eye strain</li>
                        </ul>

                        <h3 class="h5 mt-4 mb-3" data-translate="optimal-interval">What is the Optimal Reminder
                            Interval?</h3>
                        <p data-translate="optimal-interval-desc">According to health experts, the ideal intervals for
                            getting up and moving are:</p>
                        <table class="table table-striped">
                            <tr>
                                <th data-translate="work-type">Work Type</th>
                                <th data-translate="recommended-interval">Recommended Interval</th>
                                <th data-translate="activity-duration">Activity Duration</th>
                            </tr>
                            <tr>
                                <td data-translate="general-office">General Office Work</td>
                                <td data-translate="interval-30-60">30-60 minutes</td>
                                <td data-translate="duration-3-5">3-5 minutes</td>
                            </tr>
                            <tr>
                                <td data-translate="intensive-brain">Intensive Brain Work</td>
                                <td data-translate="interval-45-90">45-90 minutes</td>
                                <td data-translate="duration-5-10">5-10 minutes</td>
                            </tr>
                            <tr>
                                <td data-translate="creative-design">Creative Design Work</td>
                                <td data-translate="interval-60-120">60-120 minutes</td>
                                <td data-translate="duration-10-15">10-15 minutes</td>
                            </tr>
                        </table>

                        <h3 class="h5 mt-4 mb-3" data-translate="simple-exercises">Simple Office Exercises</h3>
                        <p data-translate="simple-exercises-desc">When the reminder sounds, you can do these simple
                            exercises:</p>
                        <ul>
                            <li><strong data-translate="neck-exercises">Neck Exercises</strong>: <span
                                    data-translate="neck-desc">Turn left and right, nod forward and backward</span></li>
                            <li><strong data-translate="shoulder-exercises">Shoulder Exercises</strong>: <span
                                    data-translate="shoulder-desc">Shrug, rotate shoulders, chest expansion</span></li>
                            <li><strong data-translate="waist-exercises">Waist Exercises</strong>: <span
                                    data-translate="waist-desc">Twist left and right, bend forward and backward</span>
                            </li>
                            <li><strong data-translate="leg-exercises">Leg Exercises</strong>: <span
                                    data-translate="leg-desc">March in place, calf raises</span></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Settings Modal -->
    <div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="settingsModalLabel" data-translate="settings">Settings</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reminderSound" class="form-label" data-translate="reminder-sound">Reminder
                            Sound</label>
                        <div class="d-flex gap-2">
                            <select class="form-select" id="reminderSound">
                                <option value="none" data-translate="no-sound">No Sound</option>
                                <option value="beep" data-translate="beep">Beep</option>
                                <option value="chime" data-translate="chime">Chime</option>
                            </select>
                            <button type="button" class="btn btn-outline-light text-dark-emphasis text-coloring"
                                id="testSoundBtn" data-translate="listen"> Listen </button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoStart" checked>
                            <label class="form-check-label" for="autoStart" data-translate="auto-start">
                                Auto-start on page load
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-light text-dark-emphasis text-coloring"
                        data-bs-dismiss="modal" data-translate="close">Close</button>
                    <button type="button" class="btn btn-success" data-bs-dismiss="modal"
                        data-translate="save">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Permission Tip -->
    <div id="notificationTip" class="alert alert-warning notification-permission d-none">
        <div class="d-flex align-items-start">
            <i class="fas fa-bell me-2 mt-1"></i>
            <div class="flex-grow-1">
                <div class="d-flex align-items-center justify-content-between">
                    <strong data-translate="notification-permission-title">Notification Permission Required</strong>
                </div>
                <div class="small" data-translate="notification-permission-desc">Please allow browser notifications for
                    the best experience</div>
                <button id="requestPermission" class="btn btn-sm btn-outline-warning mt-1"
                    data-translate="grant-permission">Grant Permission</button>
            </div>
            <button type="button" class="btn-close"
                onclick="document.getElementById('notificationTip').classList.add('d-none')"></button>

        </div>
    </div>

    <!-- Footer -->
    <footer class="py-4 mt-5">
        <div class="container text-center">
            <p class="mb-2 text-muted">© 2025 <span data-translate="app-title">Sedentary Reminder</span></p>
            <div class="small text-muted">
                <a href="privacy-policy.html" class="text-decoration-none me-3" data-translate="privacy-policy">Privacy
                    Policy</a>
                <a href="terms-of-service.html" class="text-decoration-none me-3"
                    data-translate="terms-of-service">Terms of Service</a>
                <a href="user-guide.html" class="text-decoration-none me-3" data-translate="direction-for-use">User
                    Guide</a>
                <a href="mailto:<EMAIL>" class="text-decoration-none"
                    data-translate="contact-us">Contact Us</a>
            </div>
        </div>
    </footer>

    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
    <script>
        // Translation System
        const translations = {
            'en': {
                'app-title': 'Sedentary Reminder',
                'app-subtitle': 'Make healthy office habits',
                'reminder-settings': 'Reminder Settings',
                'select-interval': 'Select Reminder Interval',
                'current-status': 'Current Status:',
                'stopped': 'Stopped',
                'running': 'Running',
                'time-until-next': 'Time until next reminder:',
                'start-reminder': 'Start Reminder',
                'stop-reminder': 'Stop Reminder',
                'today-count': 'Today\'s Reminders',
                'total-count': 'Total Reminders',
                'sitting-harms': 'Harms of Prolonged Sitting',
                'prevention-tips': 'Prevention Tips',
                'how-it-works': 'How Sedentary Reminder Works?',
                'minutes': 'minutes',
                'notification-title': 'Sedentary Reminder',
                'notification-body': 'You\'ve been sitting for a while, time to get up and move!',
                'notification-permission-title': 'Notification Permission Required',
                'notification-permission-desc': 'Please allow browser notifications for the best experience',
                'grant-permission': 'Grant Permission',
                'settings': 'Settings',
                'reminder-sound': 'Reminder Sound',
                'no-sound': 'No Sound',
                'beep': 'Beep',
                'chime': 'Chime',
                'auto-start': 'Auto-start on page load',
                'listen': 'Listen',
                'close': 'Close',
                'save': 'Save',
                'settings': 'Settings',
                'reminder-sound': 'Reminder Sound',
                'no-sound': 'No Sound',
                'beep': 'Beep',
                'chime': 'Chime',
                'how-it-works-desc': 'Sedentary Reminder is a professional healthy office tool designed to help you develop the habit of getting up and moving regularly. According to medical research, prolonged sitting can cause various health problems, including lumbar disc herniation, cardiovascular disease, metabolic disorders, etc.',
                'why-reminders': 'Why Do You Need Regular Reminders?',
                'why-reminders-desc': 'In modern office environments, we can easily get immersed in work and forget about time. Regular reminders can help you:',
                'benefit-1': 'Maintain physical vitality and prevent sedentary-related diseases',
                'benefit-2': 'Improve work efficiency, proper rest helps brain recovery',
                'benefit-3': 'Improve blood circulation and reduce fatigue',
                'benefit-4': 'Protect eyesight and reduce eye strain',
                'optimal-interval': 'What is the Optimal Reminder Interval?',
                'optimal-interval-desc': 'According to health experts, the ideal intervals for getting up and moving are:',
                'work-type': 'Work Type',
                'recommended-interval': 'Recommended Interval',
                'activity-duration': 'Activity Duration',
                'general-office': 'General Office Work',
                'interval-30-60': '30-60 minutes',
                'duration-3-5': '3-5 minutes',
                'intensive-brain': 'Intensive Brain Work',
                'interval-45-90': '45-90 minutes',
                'duration-5-10': '5-10 minutes',
                'creative-design': 'Creative Design Work',
                'interval-60-120': '60-120 minutes',
                'duration-10-15': '10-15 minutes',
                'simple-exercises': 'Simple Office Exercises',
                'simple-exercises-desc': 'When the reminder sounds, you can do these simple exercises:',
                'neck-exercises': 'Neck Exercises',
                'neck-desc': 'Turn left and right, nod forward and backward',
                'shoulder-exercises': 'Shoulder Exercises',
                'shoulder-desc': 'Shrug, rotate shoulders, chest expansion',
                'waist-exercises': 'Waist Exercises',
                'waist-desc': 'Twist left and right, bend forward and backward',
                'leg-exercises': 'Leg Exercises',
                'leg-desc': 'March in place, calf raises',
                'privacy-policy': 'Privacy Policy',
                'terms-of-service': 'Terms of Service',
                'user-guide': 'User Guide',
                'contact-us': 'Contact Us'
            },
            'zh-cn': {
                'app-title': '久坐提醒助手',
                'app-subtitle': '让健康办公成为习惯',
                'reminder-settings': '提醒设置',
                'select-interval': '选择提醒间隔时间',
                'current-status': '当前状态：',
                'stopped': '已停止',
                'running': '运行中',
                'time-until-next': '距离下次提醒：',
                'start-reminder': '开始提醒',
                'stop-reminder': '停止提醒',
                'today-count': '今日提醒次数',
                'total-count': '累计提醒次数',
                'sitting-harms': '久坐的危害',
                'prevention-tips': '预防建议',
                'how-it-works': '久坐提醒助手如何工作？',
                'minutes': '分钟',
                'notification-title': '久坐提醒',
                'notification-body': '您已经坐了很久了，该起身活动一下啦！',
                'notification-permission-title': '需要通知权限',
                'notification-permission-desc': '请允许浏览器发送通知以获得最佳体验',
                'grant-permission': '点击授权',
                'settings': '设置',
                'reminder-sound': '提醒声音',
                'no-sound': '无声音',
                'beep': '哔哔声',
                'chime': '铃声',
                'auto-start': '页面加载时自动开始',
                'listen': '试听',
                'close': '关闭',
                'save': '保存',
                'how-it-works-desc': '久坐提醒助手是一个专业的健康办公工具，旨在帮助您养成定期起身活动的好习惯。根据医学研究，长时间久坐会对身体造成多种危害，包括腰椎间盘突出、心血管疾病、代谢异常等。',
                'why-reminders': '为什么需要定时提醒？',
                'why-reminders-desc': '现代办公环境中，我们很容易沉浸在工作中而忘记时间。定时提醒可以帮助您：',
                'benefit-1': '保持身体活力，预防久坐相关疾病',
                'benefit-2': '提高工作效率，适当休息有助于大脑恢复',
                'benefit-3': '改善血液循环，减少疲劳感',
                'benefit-4': '保护视力，减少眼部疲劳',
                'optimal-interval': '最佳提醒间隔是多少？',
                'optimal-interval-desc': '根据健康专家建议，理想的起身活动间隔为：',
                'work-type': '工作类型',
                'recommended-interval': '推荐间隔',
                'activity-duration': '活动时长',
                'general-office': '一般办公工作',
                'interval-30-60': '30-60分钟',
                'duration-3-5': '3-5分钟',
                'intensive-brain': '高强度脑力工作',
                'interval-45-90': '45-90分钟',
                'duration-5-10': '5-10分钟',
                'creative-design': '创意设计工作',
                'interval-60-120': '60-120分钟',
                'duration-10-15': '10-15分钟',
                'simple-exercises': '简单的办公室运动',
                'simple-exercises-desc': '当提醒响起时，您可以做这些简单的运动：',
                'neck-exercises': '颈部运动',
                'neck-desc': '左右转动、前后点头',
                'shoulder-exercises': '肩膀运动',
                'shoulder-desc': '耸肩、转肩、扩胸',
                'waist-exercises': '腰部运动',
                'waist-desc': '左右扭转、前后弯曲',
                'leg-exercises': '腿部运动',
                'leg-desc': '原地踏步、提踵运动',
                'privacy-policy': '隐私政策',
                'terms-of-service': '使用条款',
                'user-guide': '使用帮助',
                'contact-us': '联系我们'
            },
            'da': {
                'app-title': 'Siddende Påmindelse',
                'app-subtitle': 'Gør sunde kontorvaner',
                'reminder-settings': 'Påmindelsesindstillinger',
                'select-interval': 'Vælg påmindelsesinterval',
                'current-status': 'Nuværende status:',
                'stopped': 'Stoppet',
                'running': 'Kører',
                'time-until-next': 'Tid til næste påmindelse:',
                'start-reminder': 'Start påmindelse',
                'stop-reminder': 'Stop påmindelse',
                'today-count': 'Dagens påmindelser',
                'total-count': 'Samlede påmindelser',
                'sitting-harms': 'Skader ved langvarig sidning',
                'prevention-tips': 'Forebyggelsestips',
                'how-it-works': 'Hvordan fungerer Siddende Påmindelse?',
                'minutes': 'minutter',
                'notification-title': 'Siddende Påmindelse',
                'notification-body': 'Du har siddet i et stykke tid, tid til at rejse dig og bevæge dig!',
                'notification-permission-title': 'Notifikationstilladelse påkrævet',
                'notification-permission-desc': 'Tillad browsernotifikationer for den bedste oplevelse',
                'grant-permission': 'Giv tilladelse',
                'how-it-works-desc': 'Siddende Påmindelse er et professionelt sundt kontorværktøj designet til at hjælpe dig med at udvikle vanen med at rejse dig og bevæge dig regelmæssigt. Ifølge medicinsk forskning kan langvarig sidning forårsage forskellige sundhedsproblemer, herunder lændehvirveldiskusprolaps, hjerte-kar-sygdomme, metaboliske forstyrrelser osv.',
                'why-reminders': 'Hvorfor har du brug for regelmæssige påmindelser?',
                'why-reminders-desc': 'I moderne kontormiljøer kan vi nemt blive opslugt af arbejdet og glemme tiden. Regelmæssige påmindelser kan hjælpe dig:',
                'benefit-1': 'Oprethold fysisk vitalitet og forebyg sidderelaterede sygdomme',
                'benefit-2': 'Forbedre arbejdseffektiviteten, ordentlig hvile hjælper hjernen med at komme sig',
                'benefit-3': 'Forbedre blodcirkulationen og reducere træthed',
                'benefit-4': 'Beskyt synet og reducere øjenbelastning',
                'optimal-interval': 'Hvad er det optimale påmindelsesinterval?',
                'optimal-interval-desc': 'Ifølge sundhedseksperter er de ideelle intervaller for at rejse sig og bevæge sig:',
                'work-type': 'Arbejdstype',
                'recommended-interval': 'Anbefalet interval',
                'activity-duration': 'Aktivitetsvarighed',
                'general-office': 'Generelt kontorarbejde',
                'interval-30-60': '30-60 minutter',
                'duration-3-5': '3-5 minutter',
                'intensive-brain': 'Intensivt hjernearbejde',
                'interval-45-90': '45-90 minutter',
                'duration-5-10': '5-10 minutter',
                'creative-design': 'Kreativt designarbejde',
                'interval-60-120': '60-120 minutter',
                'duration-10-15': '10-15 minutter',
                'simple-exercises': 'Simple kontorøvelser',
                'simple-exercises-desc': 'Når påmindelsen lyder, kan du lave disse simple øvelser:',
                'neck-exercises': 'Nakkeøvelser',
                'neck-desc': 'Drej til venstre og højre, nik frem og tilbage',
                'shoulder-exercises': 'Skulderrøvelser',
                'shoulder-desc': 'Ryst skuldre, roter skuldre, brystudvidelse',
                'waist-exercises': 'Taljeøvelser',
                'waist-desc': 'Drej til venstre og højre, bøj frem og tilbage',
                'leg-exercises': 'Benøvelser',
                'leg-desc': 'March på stedet, lægløft',
                'privacy-policy': 'Privatlivspolitik',
                'terms-of-service': 'Servicevilkår',
                'user-guide': 'Brugervejledning',
                'contact-us': 'Kontakt os'
            },
            'de': {
                'app-title': 'Sitzende Erinnerung',
                'app-subtitle': 'Gesunde Bürogewohnheiten schaffen',
                'reminder-settings': 'Erinnerungseinstellungen',
                'select-interval': 'Erinnerungsintervall auswählen',
                'current-status': 'Aktueller Status:',
                'stopped': 'Gestoppt',
                'running': 'Läuft',
                'time-until-next': 'Zeit bis zur nächsten Erinnerung:',
                'start-reminder': 'Erinnerung starten',
                'stop-reminder': 'Erinnerung stoppen',
                'today-count': 'Heutige Erinnerungen',
                'total-count': 'Gesamte Erinnerungen',
                'sitting-harms': 'Schäden durch langes Sitzen',
                'prevention-tips': 'Präventionstipps',
                'how-it-works': 'Wie funktioniert Sitzende Erinnerung?',
                'minutes': 'Minuten',
                'notification-title': 'Sitzende Erinnerung',
                'notification-body': 'Sie sitzen schon eine Weile, Zeit aufzustehen und sich zu bewegen!',
                'notification-permission-title': 'Benachrichtigungsberechtigung erforderlich',
                'notification-permission-desc': 'Bitte erlauben Sie Browser-Benachrichtigungen für die beste Erfahrung',
                'grant-permission': 'Berechtigung erteilen',
                'how-it-works-desc': 'Sitzende Erinnerung ist ein professionelles gesundes Bürowerkzeug, das Ihnen hilft, die Gewohnheit zu entwickeln, regelmäßig aufzustehen und sich zu bewegen. Laut medizinischer Forschung kann langes Sitzen verschiedene Gesundheitsprobleme verursachen, einschließlich Bandscheibenvorfall, Herz-Kreislauf-Erkrankungen, Stoffwechselstörungen usw.',
                'why-reminders': 'Warum brauchen Sie regelmäßige Erinnerungen?',
                'why-reminders-desc': 'In modernen Büroumgebungen können wir uns leicht in die Arbeit vertiefen und die Zeit vergessen. Regelmäßige Erinnerungen können Ihnen helfen:',
                'benefit-1': 'Körperliche Vitalität erhalten und sitzbedingte Krankheiten vorbeugen',
                'benefit-2': 'Arbeitseffizienz verbessern, angemessene Ruhe hilft der Gehirnerholung',
                'benefit-3': 'Blutzirkulation verbessern und Müdigkeit reduzieren',
                'benefit-4': 'Sehkraft schützen und Augenbelastung reduzieren',
                'optimal-interval': 'Was ist das optimale Erinnerungsintervall?',
                'optimal-interval-desc': 'Laut Gesundheitsexperten sind die idealen Intervalle zum Aufstehen und Bewegen:',
                'work-type': 'Arbeitstyp',
                'recommended-interval': 'Empfohlenes Intervall',
                'activity-duration': 'Aktivitätsdauer',
                'general-office': 'Allgemeine Büroarbeit',
                'interval-30-60': '30-60 Minuten',
                'duration-3-5': '3-5 Minuten',
                'intensive-brain': 'Intensive Gehirnarbeit',
                'interval-45-90': '45-90 Minuten',
                'duration-5-10': '5-10 Minuten',
                'creative-design': 'Kreative Designarbeit',
                'interval-60-120': '60-120 Minuten',
                'duration-10-15': '10-15 Minuten',
                'simple-exercises': 'Einfache Büroübungen',
                'simple-exercises-desc': 'Wenn die Erinnerung ertönt, können Sie diese einfachen Übungen machen:',
                'neck-exercises': 'Nackenübungen',
                'neck-desc': 'Links und rechts drehen, vor und zurück nicken',
                'shoulder-exercises': 'Schulterübungen',
                'shoulder-desc': 'Schultern zucken, Schultern drehen, Brust dehnen',
                'waist-exercises': 'Taillenübungen',
                'waist-desc': 'Links und rechts drehen, vor und zurück beugen',
                'leg-exercises': 'Beinübungen',
                'leg-desc': 'Auf der Stelle marschieren, Wadenheben',
                'privacy-policy': 'Datenschutzrichtlinie',
                'terms-of-service': 'Nutzungsbedingungen',
                'user-guide': 'Benutzerhandbuch',
                'contact-us': 'Kontaktieren Sie uns'
            },
            'es': {
                'app-title': 'Recordatorio Sedentario',
                'app-subtitle': 'Crear hábitos de oficina saludables',
                'reminder-settings': 'Configuración de recordatorios',
                'select-interval': 'Seleccionar intervalo de recordatorio',
                'current-status': 'Estado actual:',
                'stopped': 'Detenido',
                'running': 'Ejecutándose',
                'time-until-next': 'Tiempo hasta el próximo recordatorio:',
                'start-reminder': 'Iniciar recordatorio',
                'stop-reminder': 'Detener recordatorio',
                'today-count': 'Recordatorios de hoy',
                'total-count': 'Recordatorios totales',
                'sitting-harms': 'Daños del estar sentado prolongado',
                'prevention-tips': 'Consejos de prevención',
                'how-it-works': '¿Cómo funciona Recordatorio Sedentario?',
                'minutes': 'minutos',
                'notification-title': 'Recordatorio Sedentario',
                'notification-body': '¡Has estado sentado por un tiempo, es hora de levantarte y moverte!',
                'notification-permission-title': 'Permiso de notificación requerido',
                'notification-permission-desc': 'Por favor permite las notificaciones del navegador para la mejor experiencia',
                'grant-permission': 'Conceder permiso',
                'how-it-works-desc': 'Recordatorio Sedentario es una herramienta profesional de oficina saludable diseñada para ayudarte a desarrollar el hábito de levantarte y moverte regularmente. Según la investigación médica, estar sentado durante mucho tiempo puede causar varios problemas de salud, incluyendo hernia de disco lumbar, enfermedades cardiovasculares, trastornos metabólicos, etc.',
                'why-reminders': '¿Por qué necesitas recordatorios regulares?',
                'why-reminders-desc': 'En los entornos de oficina modernos, podemos sumergirnos fácilmente en el trabajo y olvidar el tiempo. Los recordatorios regulares pueden ayudarte:',
                'benefit-1': 'Mantener la vitalidad física y prevenir enfermedades relacionadas con estar sentado',
                'benefit-2': 'Mejorar la eficiencia del trabajo, el descanso adecuado ayuda a la recuperación del cerebro',
                'benefit-3': 'Mejorar la circulación sanguínea y reducir la fatiga',
                'benefit-4': 'Proteger la vista y reducir la fatiga ocular',
                'optimal-interval': '¿Cuál es el intervalo óptimo de recordatorio?',
                'optimal-interval-desc': 'Según los expertos en salud, los intervalos ideales para levantarse y moverse son:',
                'work-type': 'Tipo de trabajo',
                'recommended-interval': 'Intervalo recomendado',
                'activity-duration': 'Duración de la actividad',
                'general-office': 'Trabajo de oficina general',
                'interval-30-60': '30-60 minutos',
                'duration-3-5': '3-5 minutos',
                'intensive-brain': 'Trabajo cerebral intensivo',
                'interval-45-90': '45-90 minutos',
                'duration-5-10': '5-10 minutos',
                'creative-design': 'Trabajo de diseño creativo',
                'interval-60-120': '60-120 minutos',
                'duration-10-15': '10-15 minutos',
                'simple-exercises': 'Ejercicios simples de oficina',
                'simple-exercises-desc': 'Cuando suene el recordatorio, puedes hacer estos ejercicios simples:',
                'neck-exercises': 'Ejercicios de cuello',
                'neck-desc': 'Girar a izquierda y derecha, asentir hacia adelante y atrás',
                'shoulder-exercises': 'Ejercicios de hombros',
                'shoulder-desc': 'Encoger hombros, rotar hombros, expansión del pecho',
                'waist-exercises': 'Ejercicios de cintura',
                'waist-desc': 'Girar a izquierda y derecha, doblar hacia adelante y atrás',
                'leg-exercises': 'Ejercicios de piernas',
                'leg-desc': 'Marchar en el lugar, elevaciones de pantorrillas',
                'privacy-policy': 'Política de privacidad',
                'terms-of-service': 'Términos de servicio',
                'user-guide': 'Guía de Usuario',
                'contact-us': 'Contáctanos'
            },
            'fr': {
                'app-title': 'Rappel Sédentaire',
                'app-subtitle': 'Créer des habitudes de bureau saines',
                'reminder-settings': 'Paramètres de rappel',
                'select-interval': 'Sélectionner l\'intervalle de rappel',
                'current-status': 'Statut actuel:',
                'stopped': 'Arrêté',
                'running': 'En cours',
                'time-until-next': 'Temps jusqu\'au prochain rappel:',
                'start-reminder': 'Démarrer le rappel',
                'stop-reminder': 'Arrêter le rappel',
                'today-count': 'Rappels d\'aujourd\'hui',
                'total-count': 'Rappels totaux',
                'sitting-harms': 'Méfaits de la position assise prolongée',
                'prevention-tips': 'Conseils de prévention',
                'how-it-works': 'Comment fonctionne Rappel Sédentaire?',
                'minutes': 'minutes',
                'notification-title': 'Rappel Sédentaire',
                'notification-body': 'Vous êtes assis depuis un moment, il est temps de vous lever et de bouger!',
                'notification-permission-title': 'Autorisation de notification requise',
                'notification-permission-desc': 'Veuillez autoriser les notifications du navigateur pour la meilleure expérience',
                'grant-permission': 'Accorder l\'autorisation',
                'how-it-works-desc': 'Rappel Sédentaire est un outil de bureau sain professionnel conçu pour vous aider à développer l\'habitude de vous lever et de bouger régulièrement. Selon la recherche médicale, rester assis pendant de longues périodes peut causer divers problèmes de santé, y compris une hernie discale lombaire, des maladies cardiovasculaires, des troubles métaboliques, etc.',
                'why-reminders': 'Pourquoi avez-vous besoin de rappels réguliers?',
                'why-reminders-desc': 'Dans les environnements de bureau modernes, nous pouvons facilement nous immerger dans le travail et oublier le temps. Les rappels réguliers peuvent vous aider:',
                'benefit-1': 'Maintenir la vitalité physique et prévenir les maladies liées à la sédentarité',
                'benefit-2': 'Améliorer l\'efficacité du travail, un repos approprié aide à la récupération du cerveau',
                'benefit-3': 'Améliorer la circulation sanguine et réduire la fatigue',
                'benefit-4': 'Protéger la vue et réduire la fatigue oculaire',
                'optimal-interval': 'Quel est l\'intervalle de rappel optimal?',
                'optimal-interval-desc': 'Selon les experts en santé, les intervalles idéaux pour se lever et bouger sont:',
                'work-type': 'Type de travail',
                'recommended-interval': 'Intervalle recommandé',
                'activity-duration': 'Durée d\'activité',
                'general-office': 'Travail de bureau général',
                'interval-30-60': '30-60 minutes',
                'duration-3-5': '3-5 minutes',
                'intensive-brain': 'Travail cérébral intensif',
                'interval-45-90': '45-90 minutes',
                'duration-5-10': '5-10 minutes',
                'creative-design': 'Travail de conception créative',
                'interval-60-120': '60-120 minutes',
                'duration-10-15': '10-15 minutes',
                'simple-exercises': 'Exercices de bureau simples',
                'simple-exercises-desc': 'Quand le rappel sonne, vous pouvez faire ces exercices simples:',
                'neck-exercises': 'Exercices du cou',
                'neck-desc': 'Tourner à gauche et à droite, hocher la tête d\'avant en arrière',
                'shoulder-exercises': 'Exercices des épaules',
                'shoulder-desc': 'Hausser les épaules, faire tourner les épaules, expansion de la poitrine',
                'waist-exercises': 'Exercices de la taille',
                'waist-desc': 'Tourner à gauche et à droite, se pencher d\'avant en arrière',
                'leg-exercises': 'Exercices des jambes',
                'leg-desc': 'Marcher sur place, élévations des mollets',
                'privacy-policy': 'Politique de confidentialité',
                'terms-of-service': 'Conditions de service',
                'user-guide': 'Guide d\'Utilisation',
                'contact-us': 'Nous contacter'
            },
            'he': {
                'app-title': 'תזכורת ישיבה',
                'app-subtitle': 'יצירת הרגלי משרד בריאים',
                'reminder-settings': 'הגדרות תזכורת',
                'select-interval': 'בחר מרווח תזכורת',
                'current-status': 'סטטוס נוכחי:',
                'stopped': 'עצור',
                'running': 'פועל',
                'time-until-next': 'זמן עד התזכורת הבאה:',
                'start-reminder': 'התחל תזכורת',
                'stop-reminder': 'עצור תזכורת',
                'today-count': 'תזכורות היום',
                'total-count': 'סך התזכורות',
                'sitting-harms': 'נזקי ישיבה ממושכת',
                'prevention-tips': 'עצות למניעה',
                'how-it-works': 'איך עובדת תזכורת ישיבה?',
                'minutes': 'דקות',
                'notification-title': 'תזכורת ישיבה',
                'notification-body': 'ישבת זמן רב, הגיע הזמן לקום ולזוז!',
                'notification-permission-title': 'נדרש אישור התראות',
                'notification-permission-desc': 'אנא אפשר התראות דפדפן לחוויה הטובה ביותר',
                'grant-permission': 'תן אישור',
                'how-it-works-desc': 'תזכורת ישיבה הוא כלי משרדי בריאותי מקצועי שנועד לעזור לך לפתח הרגל של קימה ותנועה באופן קבוע. על פי מחקר רפואי, ישיבה ממושכת עלולה לגרום לבעיות בריאות שונות, כולל פריצת דיסק מותני, מחלות לב וכלי דם, הפרעות מטבוליות וכו\'.',
                'why-reminders': 'למה אתה צריך תזכורות קבועות?',
                'why-reminders-desc': 'בסביבות משרד מודרניות, אנחנו יכולים בקלות להיקלע לעבודה ולשכוח את הזמן. תזכורות קבועות יכולות לעזור לך:',
                'benefit-1': 'לשמור על חיוניות גופנית ולמנוע מחלות הקשורות לישיבה',
                'benefit-2': 'לשפר את יעילות העבודה, מנוחה מתאימה עוזרת להתאוששות המוח',
                'benefit-3': 'לשפר את זרימת הדם ולהפחית עייפות',
                'benefit-4': 'להגן על הראייה ולהפחית עייפות עיניים',
                'optimal-interval': 'מה המרווח האופטימלי לתזכורות?',
                'optimal-interval-desc': 'לפי מומחי בריאות, המרווחים האידיאליים לקימה ותנועה הם:',
                'work-type': 'סוג עבודה',
                'recommended-interval': 'מרווח מומלץ',
                'activity-duration': 'משך פעילות',
                'general-office': 'עבודת משרד כללית',
                'interval-30-60': '30-60 דקות',
                'duration-3-5': '3-5 דקות',
                'intensive-brain': 'עבודת מוח אינטנסיבית',
                'interval-45-90': '45-90 דקות',
                'duration-5-10': '5-10 דקות',
                'creative-design': 'עבודת עיצוב יצירתית',
                'interval-60-120': '60-120 דקות',
                'duration-10-15': '10-15 דקות',
                'simple-exercises': 'תרגילי משרד פשוטים',
                'simple-exercises-desc': 'כשהתזכורת מצלצלת, אתה יכול לעשות את התרגילים הפשוטים האלה:',
                'neck-exercises': 'תרגילי צוואר',
                'neck-desc': 'סיבוב שמאלה וימינה, הנהון קדימה ואחורה',
                'shoulder-exercises': 'תרגילי כתפיים',
                'shoulder-desc': 'הרמת כתפיים, סיבוב כתפיים, הרחבת חזה',
                'waist-exercises': 'תרגילי מותניים',
                'waist-desc': 'סיבוב שמאלה וימינה, כיפוף קדימה ואחורה',
                'leg-exercises': 'תרגילי רגליים',
                'leg-desc': 'צעידה במקום, הרמת עקבים',
                'privacy-policy': 'מדיניות פרטיות',
                'terms-of-service': 'תנאי שירות',
                'user-guide': 'מדריך למשתמש',
                'contact-us': 'צור קשר',
                'settings': 'הגדרות',
                'reminder-sound': 'צליל תזכורת',
                'no-sound': 'ללא צליל',
                'beep': 'ביפ',
                'chime': 'פעמון',
                'auto-start': 'התחלה אוטומטית בטעינת הדף',
                'listen': 'האזן',
                'close': 'סגור',
                'save': 'שמור'
            },
            'it': {
                'app-title': 'Promemoria Sedentario',
                'app-subtitle': 'Creare abitudini d\'ufficio sane',
                'reminder-settings': 'Impostazioni promemoria',
                'select-interval': 'Seleziona intervallo promemoria',
                'current-status': 'Stato attuale:',
                'stopped': 'Fermato',
                'running': 'In esecuzione',
                'time-until-next': 'Tempo al prossimo promemoria:',
                'start-reminder': 'Avvia promemoria',
                'stop-reminder': 'Ferma promemoria',
                'today-count': 'Promemoria di oggi',
                'total-count': 'Promemoria totali',
                'sitting-harms': 'Danni dello stare seduti a lungo',
                'prevention-tips': 'Consigli di prevenzione',
                'how-it-works': 'Come funziona Promemoria Sedentario?',
                'minutes': 'minuti',
                'notification-title': 'Promemoria Sedentario',
                'notification-body': 'Sei stato seduto per un po\', è ora di alzarsi e muoversi!',
                'notification-permission-title': 'Autorizzazione notifica richiesta',
                'notification-permission-desc': 'Si prega di consentire le notifiche del browser per la migliore esperienza',
                'grant-permission': 'Concedi autorizzazione',
                'how-it-works-desc': 'Promemoria Sedentario è uno strumento professionale per uffici sani progettato per aiutarti a sviluppare l\'abitudine di alzarti e muoverti regolarmente. Secondo la ricerca medica, stare seduti a lungo può causare vari problemi di salute, inclusi ernia del disco lombare, malattie cardiovascolari, disturbi metabolici, ecc.',
                'why-reminders': 'Perché hai bisogno di promemoria regolari?',
                'why-reminders-desc': 'Negli ambienti d\'ufficio moderni, possiamo facilmente immergerci nel lavoro e dimenticare il tempo. I promemoria regolari possono aiutarti:',
                'benefit-1': 'Mantenere la vitalità fisica e prevenire malattie legate al sedentarismo',
                'benefit-2': 'Migliorare l\'efficienza lavorativa, il riposo adeguato aiuta il recupero del cervello',
                'benefit-3': 'Migliorare la circolazione sanguigna e ridurre la fatica',
                'benefit-4': 'Proteggere la vista e ridurre l\'affaticamento degli occhi',
                'optimal-interval': 'Qual è l\'intervallo ottimale per i promemoria?',
                'optimal-interval-desc': 'Secondo gli esperti di salute, gli intervalli ideali per alzarsi e muoversi sono:',
                'work-type': 'Tipo di lavoro',
                'recommended-interval': 'Intervallo raccomandato',
                'activity-duration': 'Durata dell\'attività',
                'general-office': 'Lavoro d\'ufficio generale',
                'interval-30-60': '30-60 minuti',
                'duration-3-5': '3-5 minuti',
                'intensive-brain': 'Lavoro cerebrale intensivo',
                'interval-45-90': '45-90 minuti',
                'duration-5-10': '5-10 minuti',
                'creative-design': 'Lavoro di design creativo',
                'interval-60-120': '60-120 minuti',
                'duration-10-15': '10-15 minuti',
                'simple-exercises': 'Esercizi semplici da ufficio',
                'simple-exercises-desc': 'Quando suona il promemoria, puoi fare questi esercizi semplici:',
                'neck-exercises': 'Esercizi per il collo',
                'neck-desc': 'Girare a sinistra e destra, annuire avanti e indietro',
                'shoulder-exercises': 'Esercizi per le spalle',
                'shoulder-desc': 'Alzare le spalle, ruotare le spalle, espansione del petto',
                'waist-exercises': 'Esercizi per la vita',
                'waist-desc': 'Girare a sinistra e destra, piegarsi avanti e indietro',
                'leg-exercises': 'Esercizi per le gambe',
                'leg-desc': 'Marciare sul posto, sollevamenti dei polpacci',
                'privacy-policy': 'Informativa sulla privacy',
                'terms-of-service': 'Termini di servizio',
                'user-guide': 'Guida Utente',
                'contact-us': 'Contattaci',
                'settings': 'Impostazioni',
                'reminder-sound': 'Suono promemoria',
                'no-sound': 'Nessun suono',
                'beep': 'Beep',
                'chime': 'Suoneria',
                'auto-start': 'Avvio automatico al caricamento della pagina',
                'listen': 'Ascolta',
                'close': 'Chiudi',
                'save': 'Salva'
            },
            'ja': {
                'app-title': '座りすぎリマインダー',
                'app-subtitle': '健康的なオフィス習慣を作る',
                'reminder-settings': 'リマインダー設定',
                'select-interval': 'リマインダー間隔を選択',
                'current-status': '現在の状態：',
                'stopped': '停止中',
                'running': '実行中',
                'time-until-next': '次のリマインダーまで：',
                'start-reminder': 'リマインダー開始',
                'stop-reminder': 'リマインダー停止',
                'today-count': '今日のリマインダー',
                'total-count': '総リマインダー数',
                'sitting-harms': '長時間座ることの害',
                'prevention-tips': '予防のヒント',
                'how-it-works': '座りすぎリマインダーはどのように機能しますか？',
                'minutes': '分',
                'notification-title': '座りすぎリマインダー',
                'notification-body': 'しばらく座っています。立ち上がって動きましょう！',
                'notification-permission-title': '通知許可が必要',
                'notification-permission-desc': '最高の体験のためにブラウザ通知を許可してください',
                'grant-permission': '許可する',
                'how-it-works-desc': '座りすぎリマインダーは、定期的に立ち上がって動く習慣を身につけるのに役立つ専門的な健康オフィスツールです。医学研究によると、長時間座っていると腰椎椎間板ヘルニア、心血管疾患、代謝異常などの様々な健康問題を引き起こす可能性があります。',
                'why-reminders': 'なぜ定期的なリマインダーが必要なのですか？',
                'why-reminders-desc': '現代のオフィス環境では、仕事に没頭して時間を忘れがちです。定期的なリマインダーは以下の効果があります：',
                'benefit-1': '身体の活力を維持し、座りすぎに関連する病気を予防する',
                'benefit-2': '作業効率を向上させ、適切な休息は脳の回復を助ける',
                'benefit-3': '血液循環を改善し、疲労を軽減する',
                'benefit-4': '視力を保護し、眼精疲労を軽減する',
                'optimal-interval': '最適なリマインダー間隔は？',
                'optimal-interval-desc': '健康専門家によると、立ち上がって動くための理想的な間隔は：',
                'work-type': '作業タイプ',
                'recommended-interval': '推奨間隔',
                'activity-duration': '活動時間',
                'general-office': '一般的なオフィスワーク',
                'interval-30-60': '30-60分',
                'duration-3-5': '3-5分',
                'intensive-brain': '集中的な頭脳労働',
                'interval-45-90': '45-90分',
                'duration-5-10': '5-10分',
                'creative-design': 'クリエイティブデザインワーク',
                'interval-60-120': '60-120分',
                'duration-10-15': '10-15分',
                'simple-exercises': '簡単なオフィス運動',
                'simple-exercises-desc': 'リマインダーが鳴ったら、これらの簡単な運動ができます：',
                'neck-exercises': '首の運動',
                'neck-desc': '左右に回す、前後にうなずく',
                'shoulder-exercises': '肩の運動',
                'shoulder-desc': '肩をすくめる、肩を回す、胸を広げる',
                'waist-exercises': '腰の運動',
                'waist-desc': '左右にひねる、前後に曲げる',
                'leg-exercises': '脚の運動',
                'leg-desc': 'その場で足踏み、かかと上げ',
                'privacy-policy': 'プライバシーポリシー',
                'terms-of-service': '利用規約',
                'user-guide': '使用ガイド',
                'contact-us': 'お問い合わせ',
                'settings': '設定',
                'reminder-sound': 'リマインダー音',
                'no-sound': '音なし',
                'beep': 'ビープ音',
                'chime': 'チャイム',
                'auto-start': 'ページ読み込み時に自動開始',
                'listen': '聞く',
                'close': '閉じる',
                'save': '保存'
            },
            'ko': {
                'app-title': '앉아있기 알림',
                'app-subtitle': '건강한 사무실 습관 만들기',
                'reminder-settings': '알림 설정',
                'select-interval': '알림 간격 선택',
                'current-status': '현재 상태:',
                'stopped': '중지됨',
                'running': '실행 중',
                'time-until-next': '다음 알림까지:',
                'start-reminder': '알림 시작',
                'stop-reminder': '알림 중지',
                'today-count': '오늘의 알림',
                'total-count': '총 알림 수',
                'sitting-harms': '장시간 앉아있기의 해로움',
                'prevention-tips': '예방 팁',
                'how-it-works': '앉아있기 알림이 어떻게 작동하나요?',
                'minutes': '분',
                'notification-title': '앉아있기 알림',
                'notification-body': '오랫동안 앉아 계셨습니다. 일어나서 움직일 시간입니다!',
                'notification-permission-title': '알림 권한 필요',
                'notification-permission-desc': '최상의 경험을 위해 브라우저 알림을 허용해주세요',
                'grant-permission': '권한 허용',
                'how-it-works-desc': '앉아있기 알림은 정기적으로 일어나서 움직이는 습관을 기르는 데 도움이 되는 전문적인 건강 사무실 도구입니다. 의학 연구에 따르면, 장시간 앉아있으면 요추 추간판 탈출증, 심혈관 질환, 대사 장애 등 다양한 건강 문제를 일으킬 수 있습니다.',
                'why-reminders': '왜 정기적인 알림이 필요한가요?',
                'why-reminders-desc': '현대 사무실 환경에서는 업무에 몰두하여 시간을 잊기 쉽습니다. 정기적인 알림은 다음과 같은 도움을 줍니다:',
                'benefit-1': '신체 활력을 유지하고 앉아있기 관련 질병을 예방',
                'benefit-2': '업무 효율성 향상, 적절한 휴식은 뇌 회복에 도움',
                'benefit-3': '혈액 순환 개선 및 피로 감소',
                'benefit-4': '시력 보호 및 눈의 피로 감소',
                'optimal-interval': '최적의 알림 간격은?',
                'optimal-interval-desc': '건강 전문가에 따르면, 일어나서 움직이기 위한 이상적인 간격은:',
                'work-type': '업무 유형',
                'recommended-interval': '권장 간격',
                'activity-duration': '활동 시간',
                'general-office': '일반 사무업무',
                'interval-30-60': '30-60분',
                'duration-3-5': '3-5분',
                'intensive-brain': '집중적인 두뇌 업무',
                'interval-45-90': '45-90분',
                'duration-5-10': '5-10분',
                'creative-design': '창작 디자인 업무',
                'interval-60-120': '60-120분',
                'duration-10-15': '10-15분',
                'simple-exercises': '간단한 사무실 운동',
                'simple-exercises-desc': '알림이 울리면 이런 간단한 운동을 할 수 있습니다:',
                'neck-exercises': '목 운동',
                'neck-desc': '좌우로 돌리기, 앞뒤로 끄덕이기',
                'shoulder-exercises': '어깨 운동',
                'shoulder-desc': '어깨 으쓱하기, 어깨 돌리기, 가슴 펴기',
                'waist-exercises': '허리 운동',
                'waist-desc': '좌우로 비틀기, 앞뒤로 굽히기',
                'leg-exercises': '다리 운동',
                'leg-desc': '제자리 걷기, 발뒤꿈치 들기',
                'privacy-policy': '개인정보 보호정책',
                'terms-of-service': '서비스 약관',
                'user-guide': '사용 가이드',
                'contact-us': '문의하기',
                'settings': '설정',
                'reminder-sound': '알림 소리',
                'no-sound': '소리 없음',
                'beep': '비프음',
                'chime': '차임벨',
                'auto-start': '페이지 로드 시 자동 시작',
                'listen': '듣기',
                'close': '닫기',
                'save': '저장'
            },
            'nl': {
                'app-title': 'Zittende Herinnering',
                'app-subtitle': 'Gezonde kantoorgewoonten creëren',
                'reminder-settings': 'Herinneringsinstellingen',
                'select-interval': 'Selecteer herinneringsinterval',
                'current-status': 'Huidige status:',
                'stopped': 'Gestopt',
                'running': 'Actief',
                'time-until-next': 'Tijd tot volgende herinnering:',
                'start-reminder': 'Start herinnering',
                'stop-reminder': 'Stop herinnering',
                'today-count': 'Herinneringen vandaag',
                'total-count': 'Totale herinneringen',
                'sitting-harms': 'Schade van langdurig zitten',
                'prevention-tips': 'Preventietips',
                'how-it-works': 'Hoe werkt Zittende Herinnering?',
                'minutes': 'minuten',
                'notification-title': 'Zittende Herinnering',
                'notification-body': 'Je zit al een tijdje, tijd om op te staan en te bewegen!',
                'notification-permission-title': 'Notificatietoestemming vereist',
                'notification-permission-desc': 'Sta browsernotificaties toe voor de beste ervaring',
                'grant-permission': 'Toestemming verlenen',
                'how-it-works-desc': 'Zittende Herinnering is een professionele gezonde kantoortool ontworpen om je te helpen de gewoonte te ontwikkelen om regelmatig op te staan en te bewegen. Volgens medisch onderzoek kan langdurig zitten verschillende gezondheidsproblemen veroorzaken, waaronder lumbale hernia, hart- en vaatziekten, metabole stoornissen, enz.',
                'why-reminders': 'Waarom heb je regelmatige herinneringen nodig?',
                'why-reminders-desc': 'In moderne kantooromgevingen kunnen we gemakkelijk opgaan in het werk en de tijd vergeten. Regelmatige herinneringen kunnen je helpen:',
                'benefit-1': 'Fysieke vitaliteit behouden en zittegerelateerde ziekten voorkomen',
                'benefit-2': 'Werkefficiëntie verbeteren, adequate rust helpt herstel van de hersenen',
                'benefit-3': 'Bloedcirculatie verbeteren en vermoeidheid verminderen',
                'benefit-4': 'Zicht beschermen en oogvermoeidheid verminderen',
                'optimal-interval': 'Wat is het optimale herinneringsinterval?',
                'optimal-interval-desc': 'Volgens gezondheidsexperts zijn de ideale intervallen om op te staan en te bewegen:',
                'work-type': 'Werktype',
                'recommended-interval': 'Aanbevolen interval',
                'activity-duration': 'Activiteitsduur',
                'general-office': 'Algemeen kantoorwerk',
                'interval-30-60': '30-60 minuten',
                'duration-3-5': '3-5 minuten',
                'intensive-brain': 'Intensief hersenwerk',
                'interval-45-90': '45-90 minuten',
                'duration-5-10': '5-10 minuten',
                'creative-design': 'Creatief ontwerpwerk',
                'interval-60-120': '60-120 minuten',
                'duration-10-15': '10-15 minuten',
                'simple-exercises': 'Eenvoudige kantoroefeningen',
                'simple-exercises-desc': 'Wanneer de herinnering klinkt, kun je deze eenvoudige oefeningen doen:',
                'neck-exercises': 'Nekoefeningen',
                'neck-desc': 'Links en rechts draaien, voor en achter knikken',
                'shoulder-exercises': 'Schouderoefeningen',
                'shoulder-desc': 'Schouders ophalen, schouders draaien, borst uitbreiden',
                'waist-exercises': 'Tailleoefeningen',
                'waist-desc': 'Links en rechts draaien, voor en achter buigen',
                'leg-exercises': 'Beenoefeningen',
                'leg-desc': 'Op de plaats marcheren, hielverheffingen',
                'privacy-policy': 'Privacybeleid',
                'terms-of-service': 'Servicevoorwaarden',
                'user-guide': 'Gebruikershandleiding',
                'contact-us': 'Neem contact op',
                'settings': 'Instellingen',
                'reminder-sound': 'Herinneringsgeluid',
                'no-sound': 'Geen geluid',
                'beep': 'Piep',
                'chime': 'Bel',
                'auto-start': 'Automatisch starten bij laden van pagina',
                'listen': 'Luisteren',
                'close': 'Sluiten',
                'save': 'Opslaan'
            },
            'pl': {
                'app-title': 'Przypomnienie o Siedzeniu',
                'app-subtitle': 'Tworzenie zdrowych nawyków biurowych',
                'reminder-settings': 'Ustawienia przypomnień',
                'select-interval': 'Wybierz interwał przypomnień',
                'current-status': 'Aktualny status:',
                'stopped': 'Zatrzymane',
                'running': 'Działa',
                'time-until-next': 'Czas do następnego przypomnienia:',
                'start-reminder': 'Rozpocznij przypomnienie',
                'stop-reminder': 'Zatrzymaj przypomnienie',
                'today-count': 'Dzisiejsze przypomnienia',
                'total-count': 'Łączne przypomnienia',
                'sitting-harms': 'Szkody długiego siedzenia',
                'prevention-tips': 'Wskazówki profilaktyczne',
                'how-it-works': 'Jak działa Przypomnienie o Siedzeniu?',
                'minutes': 'minut',
                'notification-title': 'Przypomnienie o Siedzeniu',
                'notification-body': 'Siedzisz już jakiś czas, czas wstać i się poruszać!',
                'notification-permission-title': 'Wymagane pozwolenie na powiadomienia',
                'notification-permission-desc': 'Proszę zezwolić na powiadomienia przeglądarki dla najlepszego doświadczenia',
                'grant-permission': 'Udziel pozwolenia',
                'how-it-works-desc': 'Przypomnienie o Siedzeniu to profesjonalne narzędzie zdrowego biura zaprojektowane, aby pomóc Ci rozwinąć nawyk regularnego wstawania i poruszania się. Według badań medycznych, długie siedzenie może powodować różne problemy zdrowotne, w tym przepuklinę krążka lędźwiowego, choroby sercowo-naczyniowe, zaburzenia metaboliczne itp.',
                'why-reminders': 'Dlaczego potrzebujesz regularnych przypomnień?',
                'why-reminders-desc': 'W nowoczesnych środowiskach biurowych możemy łatwo zagłębić się w pracę i zapomnieć o czasie. Regularne przypomnienia mogą Ci pomóc:',
                'benefit-1': 'Utrzymać witalność fizyczną i zapobiegać chorobom związanym z siedzeniem',
                'benefit-2': 'Poprawić wydajność pracy, odpowiedni odpoczynek pomaga w regeneracji mózgu',
                'benefit-3': 'Poprawić krążenie krwi i zmniejszyć zmęczenie',
                'benefit-4': 'Chronić wzrok i zmniejszyć zmęczenie oczu',
                'optimal-interval': 'Jaki jest optymalny interwał przypomnień?',
                'optimal-interval-desc': 'Według ekspertów zdrowia, idealne interwały do wstawania i poruszania się to:',
                'work-type': 'Typ pracy',
                'recommended-interval': 'Zalecany interwał',
                'activity-duration': 'Czas trwania aktywności',
                'general-office': 'Ogólna praca biurowa',
                'interval-30-60': '30-60 minut',
                'duration-3-5': '3-5 minut',
                'intensive-brain': 'Intensywna praca umysłowa',
                'interval-45-90': '45-90 minut',
                'duration-5-10': '5-10 minut',
                'creative-design': 'Kreatywna praca projektowa',
                'interval-60-120': '60-120 minut',
                'duration-10-15': '10-15 minut',
                'simple-exercises': 'Proste ćwiczenia biurowe',
                'simple-exercises-desc': 'Gdy zabrzmi przypomnienie, możesz wykonać te proste ćwiczenia:',
                'neck-exercises': 'Ćwiczenia szyi',
                'neck-desc': 'Obracanie w lewo i prawo, kiwanie do przodu i do tyłu',
                'shoulder-exercises': 'Ćwiczenia ramion',
                'shoulder-desc': 'Wzruszanie ramionami, obracanie ramionami, rozszerzanie klatki piersiowej',
                'waist-exercises': 'Ćwiczenia talii',
                'waist-desc': 'Skręcanie w lewo i prawo, pochylanie do przodu i do tyłu',
                'leg-exercises': 'Ćwiczenia nóg',
                'leg-desc': 'Maszerowanie w miejscu, podnoszenie na palce',
                'privacy-policy': 'Polityka prywatności',
                'terms-of-service': 'Warunki usługi',
                'user-guide': 'Przewodnik Użytkownika',
                'contact-us': 'Skontaktuj się z nami',
                'settings': 'Ustawienia',
                'reminder-sound': 'Dźwięk przypomnienia',
                'no-sound': 'Bez dźwięku',
                'beep': 'Sygnał',
                'chime': 'Dzwonek',
                'auto-start': 'Automatyczne uruchomienie przy ładowaniu strony',
                'listen': 'Słuchaj',
                'close': 'Zamknij',
                'save': 'Zapisz'
            },
            'pt': {
                'app-title': 'Lembrete Sedentário',
                'app-subtitle': 'Criar hábitos de escritório saudáveis',
                'reminder-settings': 'Configurações de lembrete',
                'select-interval': 'Selecionar intervalo de lembrete',
                'current-status': 'Status atual:',
                'stopped': 'Parado',
                'running': 'Executando',
                'time-until-next': 'Tempo até o próximo lembrete:',
                'start-reminder': 'Iniciar lembrete',
                'stop-reminder': 'Parar lembrete',
                'today-count': 'Lembretes de hoje',
                'total-count': 'Lembretes totais',
                'sitting-harms': 'Danos do ficar sentado prolongadamente',
                'prevention-tips': 'Dicas de prevenção',
                'how-it-works': 'Como funciona o Lembrete Sedentário?',
                'minutes': 'minutos',
                'notification-title': 'Lembrete Sedentário',
                'notification-body': 'Você está sentado há um tempo, hora de se levantar e se mover!',
                'notification-permission-title': 'Permissão de notificação necessária',
                'notification-permission-desc': 'Por favor, permita notificações do navegador para a melhor experiência',
                'grant-permission': 'Conceder permissão',
                'how-it-works-desc': 'Lembrete Sedentário é uma ferramenta profissional de escritório saudável projetada para ajudá-lo a desenvolver o hábito de se levantar e se mover regularmente. De acordo com pesquisas médicas, ficar sentado por longos períodos pode causar vários problemas de saúde, incluindo hérnia de disco lombar, doenças cardiovasculares, distúrbios metabólicos, etc.',
                'why-reminders': 'Por que você precisa de lembretes regulares?',
                'why-reminders-desc': 'Em ambientes de escritório modernos, podemos facilmente nos envolver no trabalho e esquecer o tempo. Lembretes regulares podem ajudá-lo:',
                'benefit-1': 'Manter a vitalidade física e prevenir doenças relacionadas ao sedentarismo',
                'benefit-2': 'Melhorar a eficiência do trabalho, o descanso adequado ajuda na recuperação do cérebro',
                'benefit-3': 'Melhorar a circulação sanguínea e reduzir a fadiga',
                'benefit-4': 'Proteger a visão e reduzir a fadiga ocular',
                'optimal-interval': 'Qual é o intervalo ideal de lembretes?',
                'optimal-interval-desc': 'De acordo com especialistas em saúde, os intervalos ideais para se levantar e se mover são:',
                'work-type': 'Tipo de trabalho',
                'recommended-interval': 'Intervalo recomendado',
                'activity-duration': 'Duração da atividade',
                'general-office': 'Trabalho de escritório geral',
                'interval-30-60': '30-60 minutos',
                'duration-3-5': '3-5 minutos',
                'intensive-brain': 'Trabalho cerebral intensivo',
                'interval-45-90': '45-90 minutos',
                'duration-5-10': '5-10 minutos',
                'creative-design': 'Trabalho de design criativo',
                'interval-60-120': '60-120 minutos',
                'duration-10-15': '10-15 minutos',
                'simple-exercises': 'Exercícios simples de escritório',
                'simple-exercises-desc': 'Quando o lembrete tocar, você pode fazer estes exercícios simples:',
                'neck-exercises': 'Exercícios do pescoço',
                'neck-desc': 'Girar para esquerda e direita, acenar para frente e para trás',
                'shoulder-exercises': 'Exercícios dos ombros',
                'shoulder-desc': 'Encolher os ombros, girar os ombros, expansão do peito',
                'waist-exercises': 'Exercícios da cintura',
                'waist-desc': 'Girar para esquerda e direita, dobrar para frente e para trás',
                'leg-exercises': 'Exercícios das pernas',
                'leg-desc': 'Marchar no lugar, elevações da panturrilha',
                'privacy-policy': 'Política de privacidade',
                'terms-of-service': 'Termos de serviço',
                'user-guide': 'Guia do Usuário',
                'contact-us': 'Entre em contato',
                'settings': 'Configurações',
                'reminder-sound': 'Som do lembrete',
                'no-sound': 'Sem som',
                'beep': 'Bipe',
                'chime': 'Sino',
                'auto-start': 'Início automático no carregamento da página',
                'listen': 'Ouvir',
                'close': 'Fechar',
                'save': 'Salvar'
            },
            'ru': {
                'app-title': 'Напоминание о Сидении',
                'app-subtitle': 'Создание здоровых офисных привычек',
                'reminder-settings': 'Настройки напоминаний',
                'select-interval': 'Выберите интервал напоминаний',
                'current-status': 'Текущий статус:',
                'stopped': 'Остановлено',
                'running': 'Работает',
                'time-until-next': 'Время до следующего напоминания:',
                'start-reminder': 'Начать напоминание',
                'stop-reminder': 'Остановить напоминание',
                'today-count': 'Напоминания сегодня',
                'total-count': 'Всего напоминаний',
                'sitting-harms': 'Вред длительного сидения',
                'prevention-tips': 'Советы по профилактике',
                'how-it-works': 'Как работает Напоминание о Сидении?',
                'minutes': 'минут',
                'notification-title': 'Напоминание о Сидении',
                'notification-body': 'Вы сидите уже некоторое время, пора встать и подвигаться!',
                'notification-permission-title': 'Требуется разрешение на уведомления',
                'notification-permission-desc': 'Пожалуйста, разрешите уведомления браузера для лучшего опыта',
                'grant-permission': 'Предоставить разрешение',
                'how-it-works-desc': 'Напоминание о Сидении - это профессиональный инструмент здорового офиса, предназначенный для того, чтобы помочь вам выработать привычку регулярно вставать и двигаться. Согласно медицинским исследованиям, длительное сидение может вызвать различные проблемы со здоровьем, включая грыжу поясничного диска, сердечно-сосудистые заболевания, метаболические нарушения и т.д.',
                'why-reminders': 'Зачем вам нужны регулярные напоминания?',
                'why-reminders-desc': 'В современных офисных условиях мы можем легко погрузиться в работу и забыть о времени. Регулярные напоминания могут помочь вам:',
                'benefit-1': 'Поддерживать физическую жизнеспособность и предотвращать заболевания, связанные с сидением',
                'benefit-2': 'Повышать эффективность работы, адекватный отдых помогает восстановлению мозга',
                'benefit-3': 'Улучшать кровообращение и уменьшать усталость',
                'benefit-4': 'Защищать зрение и уменьшать усталость глаз',
                'optimal-interval': 'Каков оптимальный интервал напоминаний?',
                'optimal-interval-desc': 'По мнению экспертов по здоровью, идеальные интервалы для вставания и движения:',
                'work-type': 'Тип работы',
                'recommended-interval': 'Рекомендуемый интервал',
                'activity-duration': 'Продолжительность активности',
                'general-office': 'Общая офисная работа',
                'interval-30-60': '30-60 минут',
                'duration-3-5': '3-5 минут',
                'intensive-brain': 'Интенсивная умственная работа',
                'interval-45-90': '45-90 минут',
                'duration-5-10': '5-10 минут',
                'creative-design': 'Творческая дизайнерская работа',
                'interval-60-120': '60-120 минут',
                'duration-10-15': '10-15 минут',
                'simple-exercises': 'Простые офисные упражнения',
                'simple-exercises-desc': 'Когда прозвучит напоминание, вы можете выполнить эти простые упражнения:',
                'neck-exercises': 'Упражнения для шеи',
                'neck-desc': 'Поворачивать влево и вправо, кивать вперед и назад',
                'shoulder-exercises': 'Упражнения для плеч',
                'shoulder-desc': 'Пожимать плечами, вращать плечами, расширение груди',
                'waist-exercises': 'Упражнения для талии',
                'waist-desc': 'Поворачивать влево и вправо, наклоняться вперед и назад',
                'leg-exercises': 'Упражнения для ног',
                'leg-desc': 'Маршировать на месте, подъемы на носки',
                'privacy-policy': 'Политика конфиденциальности',
                'terms-of-service': 'Условия обслуживания',
                'user-guide': 'Руководство Пользователя',
                'contact-us': 'Связаться с нами',
                'settings': 'Настройки',
                'reminder-sound': 'Звук напоминания',
                'no-sound': 'Без звука',
                'beep': 'Сигнал',
                'chime': 'Звонок',
                'auto-start': 'Автозапуск при загрузке страницы',
                'listen': 'Слушать',
                'close': 'Закрыть',
                'save': 'Сохранить'
            },
            'sv': {
                'app-title': 'Sittande Påminnelse',
                'app-subtitle': 'Skapa hälsosamma kontorsvanor',
                'reminder-settings': 'Påminnelseinställningar',
                'select-interval': 'Välj påminnelseintervall',
                'current-status': 'Nuvarande status:',
                'stopped': 'Stoppad',
                'running': 'Körs',
                'time-until-next': 'Tid till nästa påminnelse:',
                'start-reminder': 'Starta påminnelse',
                'stop-reminder': 'Stoppa påminnelse',
                'today-count': 'Dagens påminnelser',
                'total-count': 'Totala påminnelser',
                'sitting-harms': 'Skador av långvarigt sittande',
                'prevention-tips': 'Förebyggande tips',
                'how-it-works': 'Hur fungerar Sittande Påminnelse?',
                'minutes': 'minuter',
                'notification-title': 'Sittande Påminnelse',
                'notification-body': 'Du har suttit en stund, dags att resa dig och röra på dig!',
                'notification-permission-title': 'Notifieringstillstånd krävs',
                'notification-permission-desc': 'Vänligen tillåt webbläsarnotifieringar för bästa upplevelse',
                'grant-permission': 'Bevilja tillstånd',
                'how-it-works-desc': 'Sittande Påminnelse är ett professionellt hälsosamt kontorsverktyg designat för att hjälpa dig utveckla vanan att regelbundet resa dig och röra dig. Enligt medicinsk forskning kan långvarigt sittande orsaka olika hälsoproblem, inklusive ländryggsbråck, hjärt-kärlsjukdomar, metabola störningar, etc.',
                'why-reminders': 'Varför behöver du regelbundna påminnelser?',
                'why-reminders-desc': 'I moderna kontorsmiljöer kan vi lätt fördjupa oss i arbetet och glömma tiden. Regelbundna påminnelser kan hjälpa dig:',
                'benefit-1': 'Behålla fysisk vitalitet och förebygga sittrelaterade sjukdomar',
                'benefit-2': 'Förbättra arbetseffektiviteten, adekvat vila hjälper hjärnans återhämtning',
                'benefit-3': 'Förbättra blodcirkulationen och minska trötthet',
                'benefit-4': 'Skydda synen och minska ögontrötthet',
                'optimal-interval': 'Vad är det optimala påminnelseintervallet?',
                'optimal-interval-desc': 'Enligt hälsoexperter är de ideala intervallen för att resa sig och röra sig:',
                'work-type': 'Arbetstyp',
                'recommended-interval': 'Rekommenderat intervall',
                'activity-duration': 'Aktivitetsvaraktighet',
                'general-office': 'Allmänt kontorsarbete',
                'interval-30-60': '30-60 minuter',
                'duration-3-5': '3-5 minuter',
                'intensive-brain': 'Intensivt hjärnarbete',
                'interval-45-90': '45-90 minuter',
                'duration-5-10': '5-10 minuter',
                'creative-design': 'Kreativt designarbete',
                'interval-60-120': '60-120 minuter',
                'duration-10-15': '10-15 minuter',
                'simple-exercises': 'Enkla kontorsövningar',
                'simple-exercises-desc': 'När påminnelsen låter kan du göra dessa enkla övningar:',
                'neck-exercises': 'Nackövningar',
                'neck-desc': 'Vrid åt vänster och höger, nicka framåt och bakåt',
                'shoulder-exercises': 'Axelövningar',
                'shoulder-desc': 'Rycka på axlarna, rotera axlarna, bröstexpansion',
                'waist-exercises': 'Midjeövningar',
                'waist-desc': 'Vrid åt vänster och höger, böj framåt och bakåt',
                'leg-exercises': 'Benövningar',
                'leg-desc': 'Marschera på plats, vadlyft',
                'privacy-policy': 'Integritetspolicy',
                'terms-of-service': 'Användarvillkor',
                'user-guide': 'Användarguide',
                'contact-us': 'Kontakta oss',
                'settings': 'Inställningar',
                'reminder-sound': 'Påminnelseljud',
                'no-sound': 'Inget ljud',
                'beep': 'Pip',
                'chime': 'Klocka',
                'auto-start': 'Automatisk start vid sidladdning',
                'listen': 'Lyssna',
                'close': 'Stäng',
                'save': 'Spara'
            },
            'uk': {
                'app-title': 'Нагадування про Сидіння',
                'app-subtitle': 'Створення здорових офісних звичок',
                'reminder-settings': 'Налаштування нагадувань',
                'select-interval': 'Оберіть інтервал нагадувань',
                'current-status': 'Поточний статус:',
                'stopped': 'Зупинено',
                'running': 'Працює',
                'time-until-next': 'Час до наступного нагадування:',
                'start-reminder': 'Почати нагадування',
                'stop-reminder': 'Зупинити нагадування',
                'today-count': 'Нагадування сьогодні',
                'total-count': 'Всього нагадувань',
                'sitting-harms': 'Шкода тривалого сидіння',
                'prevention-tips': 'Поради з профілактики',
                'how-it-works': 'Як працює Нагадування про Сидіння?',
                'minutes': 'хвилин',
                'notification-title': 'Нагадування про Сидіння',
                'notification-body': 'Ви сидите вже деякий час, час встати і порухатися!',
                'notification-permission-title': 'Потрібен дозвіл на сповіщення',
                'notification-permission-desc': 'Будь ласка, дозвольте сповіщення браузера для найкращого досвіду',
                'grant-permission': 'Надати дозвіл',
                'how-it-works-desc': 'Нагадування про Сидіння - це професійний інструмент здорового офісу, призначений для того, щоб допомогти вам виробити звичку регулярно вставати і рухатися. Згідно з медичними дослідженнями, тривале сидіння може викликати різні проблеми зі здоров\'ям, включаючи грижу поперекового диска, серцево-судинні захворювання, метаболічні порушення тощо.',
                'why-reminders': 'Навіщо вам потрібні регулярні нагадування?',
                'why-reminders-desc': 'У сучасних офісних умовах ми можемо легко поринути в роботу і забути про час. Регулярні нагадування можуть допомогти вам:',
                'benefit-1': 'Підтримувати фізичну життєздатність і запобігати захворюванням, пов\'язаним із сидінням',
                'benefit-2': 'Підвищувати ефективність роботи, адекватний відпочинок допомагає відновленню мозку',
                'benefit-3': 'Покращувати кровообіг і зменшувати втому',
                'benefit-4': 'Захищати зір і зменшувати втому очей',
                'optimal-interval': 'Який оптимальний інтервал нагадувань?',
                'optimal-interval-desc': 'На думку експертів з охорони здоров\'я, ідеальні інтервали для вставання і руху:',
                'work-type': 'Тип роботи',
                'recommended-interval': 'Рекомендований інтервал',
                'activity-duration': 'Тривалість активності',
                'general-office': 'Загальна офісна робота',
                'interval-30-60': '30-60 хвилин',
                'duration-3-5': '3-5 хвилин',
                'intensive-brain': 'Інтенсивна розумова робота',
                'interval-45-90': '45-90 хвилин',
                'duration-5-10': '5-10 хвилин',
                'creative-design': 'Творча дизайнерська робота',
                'interval-60-120': '60-120 хвилин',
                'duration-10-15': '10-15 хвилин',
                'simple-exercises': 'Прості офісні вправи',
                'simple-exercises-desc': 'Коли пролунає нагадування, ви можете виконати ці прості вправи:',
                'neck-exercises': 'Вправи для шиї',
                'neck-desc': 'Повертати ліворуч і праворуч, кивати вперед і назад',
                'shoulder-exercises': 'Вправи для плечей',
                'shoulder-desc': 'Піднімати плечі, обертати плечі, розширення грудей',
                'waist-exercises': 'Вправи для талії',
                'waist-desc': 'Повертати ліворуч і праворуч, нахилятися вперед і назад',
                'leg-exercises': 'Вправи для ніг',
                'leg-desc': 'Марширувати на місці, підйоми на носки',
                'privacy-policy': 'Політика конфіденційності',
                'terms-of-service': 'Умови обслуговування',
                'user-guide': 'Посібник Користувача',
                'contact-us': 'Зв\'яжіться з нами',
                'settings': 'Налаштування',
                'reminder-sound': 'Звук нагадування',
                'no-sound': 'Без звуку',
                'beep': 'Сигнал',
                'chime': 'Дзвінок',
                'auto-start': 'Автозапуск при завантаженні сторінки',
                'listen': 'Слухати',
                'close': 'Закрити',
                'save': 'Зберегти'
            }
        };

        // Current language (default: English)
        let currentLang = 'en';

        // Detect language from localStorage, URL or browser
        function detectLanguage() {
            // First check localStorage for saved language preference
            const savedLang = localStorage.getItem('sedentary-reminder-language');
            if (savedLang && translations[savedLang]) {
                return savedLang;
            }

            // Then check URL path
            const path = window.location.pathname;
            if (path.includes('/zh-cn/')) return 'zh-cn';
            if (path.includes('/da/')) return 'da';
            if (path.includes('/de/')) return 'de';
            if (path.includes('/es/')) return 'es';
            if (path.includes('/fr/')) return 'fr';
            if (path.includes('/he/')) return 'he';
            if (path.includes('/it/')) return 'it';
            if (path.includes('/ja/')) return 'ja';
            if (path.includes('/ko/')) return 'ko';
            if (path.includes('/nl/')) return 'nl';
            if (path.includes('/pl/')) return 'pl';
            if (path.includes('/pt/')) return 'pt';
            if (path.includes('/ru/')) return 'ru';
            if (path.includes('/sv/')) return 'sv';
            if (path.includes('/uk/')) return 'uk';

            // Finally check browser language
            const browserLang = navigator.language || navigator.userLanguage;
            if (browserLang.startsWith('zh')) return 'zh-cn';
            if (browserLang.startsWith('da')) return 'da';
            if (browserLang.startsWith('de')) return 'de';
            if (browserLang.startsWith('es')) return 'es';
            if (browserLang.startsWith('fr')) return 'fr';
            if (browserLang.startsWith('he')) return 'he';
            if (browserLang.startsWith('it')) return 'it';
            if (browserLang.startsWith('ja')) return 'ja';
            if (browserLang.startsWith('ko')) return 'ko';
            if (browserLang.startsWith('nl')) return 'nl';
            if (browserLang.startsWith('pl')) return 'pl';
            if (browserLang.startsWith('pt')) return 'pt';
            if (browserLang.startsWith('ru')) return 'ru';
            if (browserLang.startsWith('sv')) return 'sv';
            if (browserLang.startsWith('uk')) return 'uk';

            return 'en'; // Default to English
        }

        // Apply translations
        function applyTranslations(lang) {
            const langData = translations[lang] || translations['en'];
            document.querySelectorAll('[data-translate]').forEach(element => {
                const key = element.getAttribute('data-translate');
                if (langData[key]) {
                    element.textContent = langData[key];
                }
            });

            // Update time options if they exist
            if (document.getElementById('timeOptions').children.length > 0) {
                updateTimeOptionsText(lang);
            }

            // Update harms and tips lists if they exist
            if (document.getElementById('harmsList').children.length > 0) {
                initializeHarmsList();
            }
            if (document.getElementById('tipsList').children.length > 0) {
                initializeTipsList();
            }

            // Update document language attribute
            document.documentElement.lang = lang === 'zh-cn' ? 'zh-CN' : lang;
        }

        // Update time options text
        function updateTimeOptionsText(lang) {
            const langData = translations[lang] || translations['en'];
            const timeButtons = document.querySelectorAll('.time-option');
            timeButtons.forEach((button, index) => {
                const time = timeOptions[index];
                button.textContent = `${time} ${langData['minutes']}`;
            });
        }

        // Change language function
        function changeLanguage(lang) {
            if (translations[lang]) {
                currentLang = lang;
                localStorage.setItem('sedentary-reminder-language', lang);
                applyTranslations(lang);
                updateActiveLanguage(lang);

                // Show language change notification
                // showLanguageChangeNotification(lang);
            }
        }

        // Update active language in dropdown
        function updateActiveLanguage(lang) {
            // Remove active class from all language items
            document.querySelectorAll('.dropdown-item').forEach(item => {
                item.classList.remove('active');
            });

            // Add active class to current language
            const currentLangItem = document.querySelector(`[onclick="changeLanguage('${lang}')"]`);
            if (currentLangItem) {
                currentLangItem.classList.add('active');
            }
        }

        // Show language change notification
        function showLanguageChangeNotification(lang) {
            const langData = translations[lang] || translations['en'];

            // Remove existing notification if any
            const existingNotification = document.querySelector('.language-notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            // Create temporary notification
            const notification = document.createElement('div');
            notification.className = 'alert alert-success position-fixed language-notification';
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';
            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-check-circle me-2"></i>
                    <span>Language changed to <strong>${langData['app-title']}</strong></span>
                </div>
            `;

            document.body.appendChild(notification);

            // Remove after 3 seconds with fade out
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.transition = 'opacity 0.3s ease-out';
                    notification.style.opacity = '0';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }
            }, 3000);
        }

        // Initialize language
        currentLang = detectLanguage();
        document.addEventListener('DOMContentLoaded', () => {
            applyTranslations(currentLang);
            updateActiveLanguage(currentLang);
        });

        // Application state
        let selectedTime = 30; // Default 30 minutes
        let isRunning = false;
        let remainingTime = 0;
        let todayReminders = 0;
        let totalReminders = 0;
        let timer = null;
        let reminderSoundSetting = 'beep'; // Default sound setting

        // 时间选项
        const timeOptions = [0.2, 45, 60, 75, 80, 90, 100, 120];

        // Multilingual sitting harms data
        const sittingHarmsData = {
            'en': [
                {
                    icon: '🦴',
                    title: 'Musculoskeletal Problems',
                    description: 'Prolonged sitting can lead to lumbar disc herniation, cervical spondylosis, muscle atrophy and other problems'
                },
                {
                    icon: '❤️',
                    title: 'Cardiovascular Disease',
                    description: 'Sitting increases the risk of cardiovascular diseases such as heart disease, hypertension, and thrombosis'
                },
                {
                    icon: '🍎',
                    title: 'Metabolic Disorders',
                    description: 'Easy to cause metabolic diseases such as obesity, diabetes, and hyperlipidemia'
                },
                {
                    icon: '🧠',
                    title: 'Mental Health',
                    description: 'Prolonged sitting may cause anxiety, depression, difficulty concentrating and other problems'
                }
            ],
            'zh-cn': [
                {
                    icon: '🦴',
                    title: '骨骼肌肉问题',
                    description: '长期久坐会导致腰椎间盘突出、颈椎病、肌肉萎缩等问题'
                },
                {
                    icon: '❤️',
                    title: '心血管疾病',
                    description: '久坐会增加心脏病、高血压、血栓等心血管疾病的风险'
                },
                {
                    icon: '🍎',
                    title: '代谢异常',
                    description: '容易导致肥胖、糖尿病、高血脂等代谢性疾病'
                },
                {
                    icon: '🧠',
                    title: '精神健康',
                    description: '长期久坐可能引起焦虑、抑郁、注意力不集中等问题'
                }
            ],
            'da': [
                {
                    icon: '🦴',
                    title: 'Muskel-skelet problemer',
                    description: 'Langvarig sidning kan føre til lændehvirveldiskusprolaps, cervikal spondylose, muskelatrofi og andre problemer'
                },
                {
                    icon: '❤️',
                    title: 'Hjerte-kar-sygdomme',
                    description: 'Sidning øger risikoen for hjerte-kar-sygdomme som hjertesygdom, hypertension og trombose'
                },
                {
                    icon: '🍎',
                    title: 'Metaboliske forstyrrelser',
                    description: 'Let at forårsage metaboliske sygdomme som fedme, diabetes og hyperlipidæmi'
                },
                {
                    icon: '🧠',
                    title: 'Mental sundhed',
                    description: 'Langvarig sidning kan forårsage angst, depression, koncentrationsbesvær og andre problemer'
                }
            ],
            'de': [
                {
                    icon: '🦴',
                    title: 'Muskel-Skelett-Probleme',
                    description: 'Langes Sitzen kann zu Bandscheibenvorfall, Halswirbelsäulenerkrankungen, Muskelatrophie und anderen Problemen führen'
                },
                {
                    icon: '❤️',
                    title: 'Herz-Kreislauf-Erkrankungen',
                    description: 'Sitzen erhöht das Risiko für Herz-Kreislauf-Erkrankungen wie Herzerkrankungen, Bluthochdruck und Thrombose'
                },
                {
                    icon: '🍎',
                    title: 'Stoffwechselstörungen',
                    description: 'Kann leicht zu Stoffwechselkrankheiten wie Fettleibigkeit, Diabetes und Hyperlipidämie führen'
                },
                {
                    icon: '🧠',
                    title: 'Geistige Gesundheit',
                    description: 'Langes Sitzen kann Angst, Depression, Konzentrationsschwierigkeiten und andere Probleme verursachen'
                }
            ],
            'es': [
                {
                    icon: '🦴',
                    title: 'Problemas musculoesqueléticos',
                    description: 'Estar sentado durante mucho tiempo puede provocar hernia de disco lumbar, espondilosis cervical, atrofia muscular y otros problemas'
                },
                {
                    icon: '❤️',
                    title: 'Enfermedades cardiovasculares',
                    description: 'Estar sentado aumenta el riesgo de enfermedades cardiovasculares como enfermedades cardíacas, hipertensión y trombosis'
                },
                {
                    icon: '🍎',
                    title: 'Trastornos metabólicos',
                    description: 'Fácil de causar enfermedades metabólicas como obesidad, diabetes e hiperlipidemia'
                },
                {
                    icon: '🧠',
                    title: 'Salud mental',
                    description: 'Estar sentado durante mucho tiempo puede causar ansiedad, depresión, dificultad para concentrarse y otros problemas'
                }
            ],
            'fr': [
                {
                    icon: '🦴',
                    title: 'Problèmes musculo-squelettiques',
                    description: 'La position assise prolongée peut entraîner une hernie discale lombaire, une spondylose cervicale, une atrophie musculaire et d\'autres problèmes'
                },
                {
                    icon: '❤️',
                    title: 'Maladies cardiovasculaires',
                    description: 'La position assise augmente le risque de maladies cardiovasculaires telles que les maladies cardiaques, l\'hypertension et la thrombose'
                },
                {
                    icon: '🍎',
                    title: 'Troubles métaboliques',
                    description: 'Facile de causer des maladies métaboliques comme l\'obésité, le diabète et l\'hyperlipidémie'
                },
                {
                    icon: '🧠',
                    title: 'Santé mentale',
                    description: 'La position assise prolongée peut causer de l\'anxiété, de la dépression, des difficultés de concentration et d\'autres problèmes'
                }
            ],
            'he': [
                {
                    icon: '🦴',
                    title: 'בעיות שריר-שלד',
                    description: 'ישיבה ממושכת עלולה להוביל לפריצת דיסק מותני, ספונדילוזיס צווארי, ניוון שרירים ובעיות אחרות'
                },
                {
                    icon: '❤️',
                    title: 'מחלות לב וכלי דם',
                    description: 'ישיבה מגבירה את הסיכון למחלות לב וכלי דם כמו מחלות לב, יתר לחץ דם וקרישיות'
                },
                {
                    icon: '🍎',
                    title: 'הפרעות מטבוליות',
                    description: 'קל לגרום למחלות מטבוליות כמו השמנה, סוכרת והיפרליפידמיה'
                },
                {
                    icon: '🧠',
                    title: 'בריאות נפשית',
                    description: 'ישיבה ממושכת עלולה לגרום לחרדה, דיכאון, קושי בריכוז ובעיות אחרות'
                }
            ],
            'it': [
                {
                    icon: '🦴',
                    title: 'Problemi muscolo-scheletrici',
                    description: 'Stare seduti a lungo può portare a ernia del disco lombare, spondilosi cervicale, atrofia muscolare e altri problemi'
                },
                {
                    icon: '❤️',
                    title: 'Malattie cardiovascolari',
                    description: 'Stare seduti aumenta il rischio di malattie cardiovascolari come malattie cardiache, ipertensione e trombosi'
                },
                {
                    icon: '🍎',
                    title: 'Disturbi metabolici',
                    description: 'Facile causare malattie metaboliche come obesità, diabete e iperlipidemia'
                },
                {
                    icon: '🧠',
                    title: 'Salute mentale',
                    description: 'Stare seduti a lungo può causare ansia, depressione, difficoltà di concentrazione e altri problemi'
                }
            ],
            'ja': [
                {
                    icon: '🦴',
                    title: '筋骨格系の問題',
                    description: '長時間の座位は腰椎椎間板ヘルニア、頸椎症、筋萎縮などの問題を引き起こす可能性があります'
                },
                {
                    icon: '❤️',
                    title: '心血管疾患',
                    description: '座位は心疾患、高血圧、血栓症などの心血管疾患のリスクを高めます'
                },
                {
                    icon: '🍎',
                    title: '代謝異常',
                    description: '肥満、糖尿病、高脂血症などの代謝性疾患を引き起こしやすくなります'
                },
                {
                    icon: '🧠',
                    title: 'メンタルヘルス',
                    description: '長時間の座位は不安、うつ病、集中力の低下などの問題を引き起こす可能性があります'
                }
            ],
            'ko': [
                {
                    icon: '🦴',
                    title: '근골격계 문제',
                    description: '장시간 앉아있으면 요추 추간판 탈출증, 경추증, 근육 위축 등의 문제가 발생할 수 있습니다'
                },
                {
                    icon: '❤️',
                    title: '심혈관 질환',
                    description: '앉아있는 것은 심장병, 고혈압, 혈전증과 같은 심혈관 질환의 위험을 증가시킵니다'
                },
                {
                    icon: '🍎',
                    title: '대사 장애',
                    description: '비만, 당뇨병, 고지혈증과 같은 대사성 질환을 유발하기 쉽습니다'
                },
                {
                    icon: '🧠',
                    title: '정신 건강',
                    description: '장시간 앉아있으면 불안, 우울증, 집중력 저하 등의 문제가 발생할 수 있습니다'
                }
            ],
            'nl': [
                {
                    icon: '🦴',
                    title: 'Musculoskeletale problemen',
                    description: 'Lang zitten kan leiden tot lumbale hernia, cervicale spondylose, spieratrofie en andere problemen'
                },
                {
                    icon: '❤️',
                    title: 'Cardiovasculaire ziekten',
                    description: 'Zitten verhoogt het risico op cardiovasculaire ziekten zoals hartziekte, hypertensie en trombose'
                },
                {
                    icon: '🍎',
                    title: 'Metabole stoornissen',
                    description: 'Gemakkelijk metabole ziekten veroorzaken zoals obesitas, diabetes en hyperlipidemie'
                },
                {
                    icon: '🧠',
                    title: 'Geestelijke gezondheid',
                    description: 'Lang zitten kan angst, depressie, concentratieproblemen en andere problemen veroorzaken'
                }
            ],
            'pl': [
                {
                    icon: '🦴',
                    title: 'Problemy mięśniowo-szkieletowe',
                    description: 'Długie siedzenie może prowadzić do przepukliny krążka lędźwiowego, zwyrodnienia szyjnego, zaniku mięśni i innych problemów'
                },
                {
                    icon: '❤️',
                    title: 'Choroby sercowo-naczyniowe',
                    description: 'Siedzenie zwiększa ryzyko chorób sercowo-naczyniowych, takich jak choroby serca, nadciśnienie i zakrzepica'
                },
                {
                    icon: '🍎',
                    title: 'Zaburzenia metaboliczne',
                    description: 'Łatwo powoduje choroby metaboliczne, takie jak otyłość, cukrzyca i hiperlipidemia'
                },
                {
                    icon: '🧠',
                    title: 'Zdrowie psychiczne',
                    description: 'Długie siedzenie może powodować lęk, depresję, trudności z koncentracją i inne problemy'
                }
            ],
            'pt': [
                {
                    icon: '🦴',
                    title: 'Problemas musculoesqueléticos',
                    description: 'Ficar sentado por muito tempo pode levar a hérnia de disco lombar, espondilose cervical, atrofia muscular e outros problemas'
                },
                {
                    icon: '❤️',
                    title: 'Doenças cardiovasculares',
                    description: 'Ficar sentado aumenta o risco de doenças cardiovasculares como doenças cardíacas, hipertensão e trombose'
                },
                {
                    icon: '🍎',
                    title: 'Distúrbios metabólicos',
                    description: 'Fácil de causar doenças metabólicas como obesidade, diabetes e hiperlipidemia'
                },
                {
                    icon: '🧠',
                    title: 'Saúde mental',
                    description: 'Ficar sentado por muito tempo pode causar ansiedade, depressão, dificuldade de concentração e outros problemas'
                }
            ],
            'ru': [
                {
                    icon: '🦴',
                    title: 'Проблемы опорно-двигательного аппарата',
                    description: 'Длительное сидение может привести к грыже поясничного диска, шейному спондилезу, атрофии мышц и другим проблемам'
                },
                {
                    icon: '❤️',
                    title: 'Сердечно-сосудистые заболевания',
                    description: 'Сидение увеличивает риск сердечно-сосудистых заболеваний, таких как болезни сердца, гипертония и тромбоз'
                },
                {
                    icon: '🍎',
                    title: 'Метаболические нарушения',
                    description: 'Легко вызывает метаболические заболевания, такие как ожирение, диабет и гиперлипидемия'
                },
                {
                    icon: '🧠',
                    title: 'Психическое здоровье',
                    description: 'Длительное сидение может вызвать тревогу, депрессию, трудности с концентрацией и другие проблемы'
                }
            ],
            'sv': [
                {
                    icon: '🦴',
                    title: 'Muskuloskeletala problem',
                    description: 'Långvarigt sittande kan leda till ländryggsbråck, cervikal spondylos, muskelatrofi och andra problem'
                },
                {
                    icon: '❤️',
                    title: 'Hjärt-kärlsjukdomar',
                    description: 'Sittande ökar risken för hjärt-kärlsjukdomar som hjärtsjukdom, hypertension och trombos'
                },
                {
                    icon: '🍎',
                    title: 'Metabola störningar',
                    description: 'Lätt att orsaka metabola sjukdomar som fetma, diabetes och hyperlipidemi'
                },
                {
                    icon: '🧠',
                    title: 'Mental hälsa',
                    description: 'Långvarigt sittande kan orsaka ångest, depression, koncentrationssvårigheter och andra problem'
                }
            ],
            'uk': [
                {
                    icon: '🦴',
                    title: 'Проблеми опорно-рухового апарату',
                    description: 'Тривале сидіння може призвести до грижі поперекового диска, шийного спондильозу, атрофії м\'язів та інших проблем'
                },
                {
                    icon: '❤️',
                    title: 'Серцево-судинні захворювання',
                    description: 'Сидіння збільшує ризик серцево-судинних захворювань, таких як хвороби серця, гіпертонія та тромбоз'
                },
                {
                    icon: '🍎',
                    title: 'Метаболічні порушення',
                    description: 'Легко викликає метаболічні захворювання, такі як ожиріння, діабет та гіперліпідемія'
                },
                {
                    icon: '🧠',
                    title: 'Психічне здоров\'я',
                    description: 'Тривале сидіння може викликати тривогу, депресію, труднощі з концентрацією та інші проблеми'
                }
            ]
        };

        // Multilingual prevention tips data
        const preventionTipsData = {
            'en': [
                {
                    icon: '🚶',
                    title: 'Regular Movement',
                    description: 'Get up and move for 3-5 minutes every 30-60 minutes, do simple stretching exercises'
                },
                {
                    icon: '💺',
                    title: 'Maintain Correct Posture',
                    description: 'Keep your back straight, feet flat on the ground, and maintain proper distance between screen and eyes'
                },
                {
                    icon: '🤸',
                    title: 'Office Exercises',
                    description: 'Do simple exercises like neck rotation, shoulder shrugs, waist twists'
                },
                {
                    icon: '💧',
                    title: 'Drink More Water',
                    description: 'Increase water intake, which is beneficial for health and provides opportunities to get up and move'
                }
            ],
            'zh-cn': [
                {
                    icon: '🚶',
                    title: '定时起身活动',
                    description: '每30-60分钟起身活动3-5分钟，做简单的伸展运动'
                },
                {
                    icon: '💺',
                    title: '保持正确坐姿',
                    description: '背部挺直，双脚平放地面，屏幕与眼睛保持适当距离'
                },
                {
                    icon: '🤸',
                    title: '办公室运动',
                    description: '做颈部转动、肩膀耸动、腰部扭转等简单运动'
                },
                {
                    icon: '💧',
                    title: '多喝水',
                    description: '增加饮水量，既有益健康又能增加起身活动的机会'
                }
            ],
            'da': [
                {
                    icon: '🚶',
                    title: 'Regelmæssig bevægelse',
                    description: 'Stå op og bevæg dig i 3-5 minutter hver 30-60 minutter, lav simple strækøvelser'
                },
                {
                    icon: '💺',
                    title: 'Bevar korrekt holdning',
                    description: 'Hold ryggen lige, fødderne fladt på gulvet og bevar passende afstand mellem skærm og øjne'
                },
                {
                    icon: '🤸',
                    title: 'Kontorøvelser',
                    description: 'Lav simple øvelser som nakkerotation, skulderrystelser, talje drejninger'
                },
                {
                    icon: '💧',
                    title: 'Drik mere vand',
                    description: 'Øg vandindtaget, hvilket er gavnligt for sundheden og giver muligheder for at stå op og bevæge sig'
                }
            ],
            'de': [
                {
                    icon: '🚶',
                    title: 'Regelmäßige Bewegung',
                    description: 'Stehen Sie alle 30-60 Minuten für 3-5 Minuten auf und bewegen Sie sich, machen Sie einfache Dehnübungen'
                },
                {
                    icon: '💺',
                    title: 'Korrekte Haltung beibehalten',
                    description: 'Halten Sie den Rücken gerade, die Füße flach auf dem Boden und den angemessenen Abstand zwischen Bildschirm und Augen'
                },
                {
                    icon: '🤸',
                    title: 'Büroübungen',
                    description: 'Machen Sie einfache Übungen wie Nackenrotation, Schulterzucken, Taillendrehen'
                },
                {
                    icon: '💧',
                    title: 'Mehr Wasser trinken',
                    description: 'Erhöhen Sie die Wasseraufnahme, was gesundheitsfördernd ist und Gelegenheiten zum Aufstehen und Bewegen bietet'
                }
            ],
            'es': [
                {
                    icon: '🚶',
                    title: 'Movimiento regular',
                    description: 'Levántese y muévase durante 3-5 minutos cada 30-60 minutos, haga ejercicios de estiramiento simples'
                },
                {
                    icon: '💺',
                    title: 'Mantener postura correcta',
                    description: 'Mantenga la espalda recta, los pies planos en el suelo y la distancia adecuada entre la pantalla y los ojos'
                },
                {
                    icon: '🤸',
                    title: 'Ejercicios de oficina',
                    description: 'Haga ejercicios simples como rotación del cuello, encogimiento de hombros, giros de cintura'
                },
                {
                    icon: '💧',
                    title: 'Beber más agua',
                    description: 'Aumente la ingesta de agua, lo cual es beneficioso para la salud y proporciona oportunidades para levantarse y moverse'
                }
            ],
            'fr': [
                {
                    icon: '🚶',
                    title: 'Mouvement régulier',
                    description: 'Levez-vous et bougez pendant 3-5 minutes toutes les 30-60 minutes, faites des exercices d\'étirement simples'
                },
                {
                    icon: '💺',
                    title: 'Maintenir une posture correcte',
                    description: 'Gardez le dos droit, les pieds à plat sur le sol et maintenez une distance appropriée entre l\'écran et les yeux'
                },
                {
                    icon: '🤸',
                    title: 'Exercices de bureau',
                    description: 'Faites des exercices simples comme la rotation du cou, le haussement d\'épaules, les torsions de taille'
                },
                {
                    icon: '💧',
                    title: 'Boire plus d\'eau',
                    description: 'Augmentez la consommation d\'eau, ce qui est bénéfique pour la santé et offre des opportunités de se lever et bouger'
                }
            ],
            'he': [
                {
                    icon: '🚶',
                    title: 'תנועה קבועה',
                    description: 'קומו וזוזו למשך 3-5 דקות כל 30-60 דקות, עשו תרגילי מתיחה פשוטים'
                },
                {
                    icon: '💺',
                    title: 'שמירה על יציבה נכונה',
                    description: 'שמרו על הגב זקוף, כפות הרגליים שטוחות על הרצפה ומרחק מתאים בין המסך לעיניים'
                },
                {
                    icon: '🤸',
                    title: 'תרגילי משרד',
                    description: 'עשו תרגילים פשוטים כמו סיבוב צוואר, הרמת כתפיים, סיבובי מותניים'
                },
                {
                    icon: '💧',
                    title: 'שתו יותר מים',
                    description: 'הגבירו את צריכת המים, מה שמועיל לבריאות ומספק הזדמנויות לקום ולזוז'
                }
            ],
            'it': [
                {
                    icon: '🚶',
                    title: 'Movimento regolare',
                    description: 'Alzatevi e muovetevi per 3-5 minuti ogni 30-60 minuti, fate semplici esercizi di stretching'
                },
                {
                    icon: '💺',
                    title: 'Mantenere postura corretta',
                    description: 'Tenete la schiena dritta, i piedi piatti a terra e mantenete la distanza appropriata tra schermo e occhi'
                },
                {
                    icon: '🤸',
                    title: 'Esercizi da ufficio',
                    description: 'Fate semplici esercizi come rotazione del collo, alzate di spalle, torsioni della vita'
                },
                {
                    icon: '💧',
                    title: 'Bere più acqua',
                    description: 'Aumentate l\'assunzione di acqua, che è benefica per la salute e offre opportunità di alzarsi e muoversi'
                }
            ],
            'ja': [
                {
                    icon: '🚶',
                    title: '定期的な運動',
                    description: '30-60分ごとに3-5分間立ち上がって動き、簡単なストレッチ運動をしましょう'
                },
                {
                    icon: '💺',
                    title: '正しい姿勢を保つ',
                    description: '背中をまっすぐに保ち、足を床に平らに置き、画面と目の間の適切な距離を保ちましょう'
                },
                {
                    icon: '🤸',
                    title: 'オフィス運動',
                    description: '首の回転、肩すくめ、腰のひねりなどの簡単な運動をしましょう'
                },
                {
                    icon: '💧',
                    title: 'もっと水を飲む',
                    description: '水分摂取を増やすことは健康に良く、立ち上がって動く機会を提供します'
                }
            ],
            'ko': [
                {
                    icon: '🚶',
                    title: '규칙적인 움직임',
                    description: '30-60분마다 3-5분간 일어나서 움직이고, 간단한 스트레칭 운동을 하세요'
                },
                {
                    icon: '💺',
                    title: '올바른 자세 유지',
                    description: '등을 곧게 펴고, 발을 바닥에 평평하게 놓고, 화면과 눈 사이의 적절한 거리를 유지하세요'
                },
                {
                    icon: '🤸',
                    title: '사무실 운동',
                    description: '목 돌리기, 어깨 으쓱하기, 허리 비틀기 같은 간단한 운동을 하세요'
                },
                {
                    icon: '💧',
                    title: '물 더 마시기',
                    description: '물 섭취량을 늘리는 것은 건강에 좋고 일어나서 움직일 기회를 제공합니다'
                }
            ],
            'nl': [
                {
                    icon: '🚶',
                    title: 'Regelmatige beweging',
                    description: 'Sta elke 30-60 minuten 3-5 minuten op en beweeg, doe eenvoudige rekoefeningen'
                },
                {
                    icon: '💺',
                    title: 'Correcte houding behouden',
                    description: 'Houd de rug recht, voeten plat op de grond en behoud juiste afstand tussen scherm en ogen'
                },
                {
                    icon: '🤸',
                    title: 'Kantoroefeningen',
                    description: 'Doe eenvoudige oefeningen zoals nekrotatie, schouderophalen, tailledraaien'
                },
                {
                    icon: '💧',
                    title: 'Meer water drinken',
                    description: 'Verhoog de waterinname, wat gunstig is voor de gezondheid en kansen biedt om op te staan en te bewegen'
                }
            ],
            'pl': [
                {
                    icon: '🚶',
                    title: 'Regularne poruszanie się',
                    description: 'Wstawaj i poruszaj się przez 3-5 minut co 30-60 minut, rób proste ćwiczenia rozciągające'
                },
                {
                    icon: '💺',
                    title: 'Utrzymanie prawidłowej postawy',
                    description: 'Trzymaj plecy prosto, stopy płasko na podłodze i zachowaj odpowiednią odległość między ekranem a oczami'
                },
                {
                    icon: '🤸',
                    title: 'Ćwiczenia biurowe',
                    description: 'Rób proste ćwiczenia jak obroty szyi, wzruszanie ramionami, skręty talii'
                },
                {
                    icon: '💧',
                    title: 'Pij więcej wody',
                    description: 'Zwiększ spożycie wody, co jest korzystne dla zdrowia i daje okazje do wstawania i poruszania się'
                }
            ],
            'pt': [
                {
                    icon: '🚶',
                    title: 'Movimento regular',
                    description: 'Levante-se e mova-se por 3-5 minutos a cada 30-60 minutos, faça exercícios simples de alongamento'
                },
                {
                    icon: '💺',
                    title: 'Manter postura correta',
                    description: 'Mantenha as costas retas, pés planos no chão e distância adequada entre tela e olhos'
                },
                {
                    icon: '🤸',
                    title: 'Exercícios de escritório',
                    description: 'Faça exercícios simples como rotação do pescoço, encolher ombros, torções da cintura'
                },
                {
                    icon: '💧',
                    title: 'Beber mais água',
                    description: 'Aumente a ingestão de água, o que é benéfico para a saúde e oferece oportunidades para levantar e mover-se'
                }
            ],
            'ru': [
                {
                    icon: '🚶',
                    title: 'Регулярное движение',
                    description: 'Вставайте и двигайтесь в течение 3-5 минут каждые 30-60 минут, делайте простые упражнения на растяжку'
                },
                {
                    icon: '💺',
                    title: 'Поддержание правильной осанки',
                    description: 'Держите спину прямо, ноги плоско на полу и поддерживайте подходящее расстояние между экраном и глазами'
                },
                {
                    icon: '🤸',
                    title: 'Офисные упражнения',
                    description: 'Делайте простые упражнения, такие как вращение шеи, пожимание плечами, повороты талии'
                },
                {
                    icon: '💧',
                    title: 'Пить больше воды',
                    description: 'Увеличьте потребление воды, что полезно для здоровья и дает возможности вставать и двигаться'
                }
            ],
            'sv': [
                {
                    icon: '🚶',
                    title: 'Regelbunden rörelse',
                    description: 'Res dig och rör dig i 3-5 minuter var 30-60:e minut, gör enkla stretchövningar'
                },
                {
                    icon: '💺',
                    title: 'Behåll korrekt hållning',
                    description: 'Håll ryggen rak, fötterna platt på golvet och behåll lämpligt avstånd mellan skärm och ögon'
                },
                {
                    icon: '🤸',
                    title: 'Kontorsövningar',
                    description: 'Gör enkla övningar som nackrotation, axelryckning, midjevridningar'
                },
                {
                    icon: '💧',
                    title: 'Drick mer vatten',
                    description: 'Öka vattenintaget, vilket är fördelaktigt för hälsan och ger möjligheter att resa sig och röra sig'
                }
            ],
            'uk': [
                {
                    icon: '🚶',
                    title: 'Регулярний рух',
                    description: 'Вставайте і рухайтеся протягом 3-5 хвилин кожні 30-60 хвилин, робіть прості вправи на розтяжку'
                },
                {
                    icon: '💺',
                    title: 'Підтримання правильної постави',
                    description: 'Тримайте спину прямо, ноги плоско на підлозі та підтримуйте відповідну відстань між екраном та очима'
                },
                {
                    icon: '🤸',
                    title: 'Офісні вправи',
                    description: 'Робіть прості вправи, такі як обертання шиї, піднімання плечей, повороти талії'
                },
                {
                    icon: '💧',
                    title: 'Пити більше води',
                    description: 'Збільште споживання води, що корисно для здоров\'я та надає можливості вставати і рухатися'
                }
            ]
        };

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function () {
            initializeTimeOptions();
            initializeHarmsList();
            initializeTipsList();
            loadStats();
            loadSettings(); // 加载设置
            checkNotificationPermission();

            // 绑定事件
            document.getElementById('startBtn').addEventListener('click', startReminder);
            document.getElementById('stopBtn').addEventListener('click', stopReminder);
            document.getElementById('requestPermission').addEventListener('click', requestNotificationPermission);

            // 设置事件监听
            document.getElementById('reminderSound').addEventListener('change', (e) => {
                reminderSoundSetting = e.target.value;
                saveSettings();
            });

            document.getElementById('autoStart').addEventListener('change', () => {
                saveSettings();
            });

            // 测试声音按钮事件
            document.getElementById('testSoundBtn').addEventListener('click', () => {
                const currentSound = document.getElementById('reminderSound').value;
                const originalSetting = reminderSoundSetting;
                reminderSoundSetting = currentSound;
                playReminderSound();
                reminderSoundSetting = originalSetting;
            });

            // 设置模态框保存按钮事件
            const saveButton = document.querySelector('#settingsModal .btn-primary');
            if (saveButton) {
                saveButton.addEventListener('click', () => {
                    saveSettings();
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('settingsModal'));
                    if (modal) {
                        modal.hide();
                    }
                });
            }

            // 如果启用了自动开始，则自动开始
            if (document.getElementById('autoStart').checked) {
                setTimeout(() => {
                    startReminder();
                }, 1000);
            }
        });

        // Initialize time options
        function initializeTimeOptions() {
            const container = document.getElementById('timeOptions');
            const langData = translations[currentLang] || translations['en'];

            // Clear existing options
            container.innerHTML = '';

            timeOptions.forEach(time => {
                const col = document.createElement('div');
                col.className = 'col-3';

                const button = document.createElement('button');
                button.className = `btn btn-light time-option ${time === selectedTime ? 'active' : ''}`;
                button.textContent = `${time} ${langData['minutes']}`;
                button.onclick = () => selectTime(time);

                col.appendChild(button);
                container.appendChild(col);
            });
        }

        // 选择时间
        function selectTime(time) {
            selectedTime = time;
            document.querySelectorAll('.time-option').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // Initialize harms list
        function initializeHarmsList() {
            const container = document.getElementById('harmsList');
            const harms = sittingHarmsData[currentLang] || sittingHarmsData['en'];

            // Clear existing content
            container.innerHTML = '';

            harms.forEach(harm => {
                const item = document.createElement('div');
                item.className = 'd-flex align-items-start mb-3';
                item.innerHTML = `
                    <span class="me-3 fs-4">${harm.icon}</span>
                    <div>
                        <h6 class="fw-semibold mb-1">${harm.title}</h6>
                        <p class="small text-muted mb-0">${harm.description}</p>
                    </div>
                `;
                container.appendChild(item);
            });
        }

        // Initialize tips list
        function initializeTipsList() {
            const container = document.getElementById('tipsList');
            const tips = preventionTipsData[currentLang] || preventionTipsData['en'];

            // Clear existing content
            container.innerHTML = '';

            tips.forEach(tip => {
                const item = document.createElement('div');
                item.className = 'd-flex align-items-start mb-3';
                item.innerHTML = `
                    <span class="me-3 fs-4">${tip.icon}</span>
                    <div>
                        <h6 class="fw-semibold mb-1">${tip.title}</h6>
                        <p class="small text-muted mb-0">${tip.description}</p>
                    </div>
                `;
                container.appendChild(item);
            });
        }

        // 格式化时间显示
        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = seconds % 60;
            return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        // 检查通知权限
        function checkNotificationPermission() {
            if ('Notification' in window) {
                if (Notification.permission === 'default') {
                    document.getElementById('notificationTip').classList.remove('d-none');
                }
            }
        }

        // 请求通知权限
        async function requestNotificationPermission() {
            if ('Notification' in window) {
                const permission = await Notification.requestPermission();
                if (permission === 'granted') {
                    document.getElementById('notificationTip').classList.add('d-none');
                }
            }
        }

        // Play reminder sound
        function playReminderSound() {
            if (reminderSoundSetting === 'none') {
                return; // No sound mode - silent
            }

            try {
                let audioContext;
                if (window.AudioContext) {
                    audioContext = new AudioContext();
                } else if (window.webkitAudioContext) {
                    audioContext = new webkitAudioContext();
                } else {
                    console.warn('Web Audio API not supported');
                    return;
                }

                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                if (reminderSoundSetting === 'beep') {
                    // Simple beep sound
                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
                    oscillator.start(audioContext.currentTime);
                    oscillator.stop(audioContext.currentTime + 0.5);
                } else if (reminderSoundSetting === 'chime') {
                    // Chime sound - multiple tones
                    const frequencies = [523, 659, 784]; // C, E, G notes
                    frequencies.forEach((freq, index) => {
                        const osc = audioContext.createOscillator();
                        const gain = audioContext.createGain();

                        osc.connect(gain);
                        gain.connect(audioContext.destination);

                        osc.frequency.setValueAtTime(freq, audioContext.currentTime);
                        gain.gain.setValueAtTime(0.2, audioContext.currentTime + index * 0.2);
                        gain.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + index * 0.2 + 0.8);

                        osc.start(audioContext.currentTime + index * 0.2);
                        osc.stop(audioContext.currentTime + index * 0.2 + 0.8);
                    });
                }
            } catch (error) {
                console.warn('Error playing sound:', error);
            }
        }

        // Send notification
        function sendNotification() {
            // Play sound first
            playReminderSound();

            if ('Notification' in window && Notification.permission === 'granted') {
                const langData = translations[currentLang] || translations['en'];
                const notification = new Notification(langData['notification-title'], {
                    body: langData['notification-body'],
                    icon: './assets/favicon.svg',
                    badge: './assets/favicon.svg'
                });

                notification.onclick = () => {
                    window.focus();
                    notification.close();
                };

                // Auto close after 8 seconds
                setTimeout(() => {
                    notification.close();
                }, 8000);
            }
        }

        // 开始提醒
        function startReminder() {
            if (isRunning) return;

            isRunning = true;
            remainingTime = selectedTime * 60;

            // Update UI
            const langData = translations[currentLang] || translations['en'];
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            document.getElementById('statusText').textContent = langData['running'];
            document.getElementById('statusText').className = 'status-success';
            document.getElementById('progressContainer').classList.remove('d-none');

            timer = setInterval(() => {
                remainingTime--;
                updateProgress();

                if (remainingTime <= 0) {
                    // 发送通知
                    sendNotification();

                    // 更新统计
                    todayReminders++;
                    totalReminders++;
                    updateStats();

                    // 重置计时器
                    remainingTime = selectedTime * 60;

                    // 保存统计数据
                    saveStats();
                }
            }, 1000);
        }

        // 停止提醒
        function stopReminder() {
            if (timer) {
                clearInterval(timer);
                timer = null;
            }

            isRunning = false;
            remainingTime = 0;

            // Update UI
            const langData = translations[currentLang] || translations['en'];
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('statusText').textContent = langData['stopped'];
            document.getElementById('statusText').className = 'text-muted';
            document.getElementById('progressContainer').classList.add('d-none');
        }

        // 更新进度
        function updateProgress() {
            const progress = ((selectedTime * 60 - remainingTime) / (selectedTime * 60)) * 100;
            document.getElementById('progressBar').style.width = `${progress}%`;
            document.getElementById('remainingTime').textContent = formatTime(remainingTime);
        }

        // 更新统计显示
        function updateStats() {
            document.getElementById('todayCount').textContent = todayReminders;
            document.getElementById('totalCount').textContent = totalReminders;
        }

        // 保存统计数据
        function saveStats() {
            const today = new Date().toDateString();
            const stats = {
                date: today,
                todayReminders: todayReminders,
                totalReminders: totalReminders
            };
            localStorage.setItem('sedentaryReminderStats', JSON.stringify(stats));
        }

        // 加载统计数据
        function loadStats() {
            const saved = localStorage.getItem('sedentaryReminderStats');
            if (saved) {
                const stats = JSON.parse(saved);
                const today = new Date().toDateString();

                if (stats.date === today) {
                    todayReminders = stats.todayReminders || 0;
                } else {
                    todayReminders = 0;
                }

                totalReminders = stats.totalReminders || 0;
                updateStats();
            }
        }

        // 保存设置
        function saveSettings() {
            const settings = {
                reminderSound: reminderSoundSetting,
                autoStart: document.getElementById('autoStart').checked
            };
            localStorage.setItem('sedentaryReminderSettings', JSON.stringify(settings));
        }

        // 加载设置
        function loadSettings() {
            const saved = localStorage.getItem('sedentaryReminderSettings');
            if (saved) {
                const settings = JSON.parse(saved);
                reminderSoundSetting = settings.reminderSound || 'beep';

                // 更新UI
                document.getElementById('reminderSound').value = reminderSoundSetting;
                if (settings.autoStart !== undefined) {
                    document.getElementById('autoStart').checked = settings.autoStart;
                }
            }
        }



        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            if (timer) {
                clearInterval(timer);
            }
        });
    </script>
</body>

</html>