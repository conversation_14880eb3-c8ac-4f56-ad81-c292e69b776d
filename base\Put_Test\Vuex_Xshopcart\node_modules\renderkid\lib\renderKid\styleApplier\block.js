// Generated by CoffeeScript 1.9.3
var _common, blockStyleApplier, object, self;

_common = require('./_common');

object = require('utila').object;

module.exports = blockStyleApplier = self = {
  applyTo: function(el, style) {
    var config, ret;
    ret = _common.getStyleTagsFor(style);
    ret.blockConfig = config = {};
    this._margins(style, config);
    this._bullet(style, config);
    this._dims(style, config);
    return ret;
  },
  _margins: function(style, config) {
    if (style.marginLeft != null) {
      object.appendOnto(config, {
        linePrependor: {
          options: {
            amount: parseInt(style.marginLeft)
          }
        }
      });
    }
    if (style.marginRight != null) {
      object.appendOnto(config, {
        lineAppendor: {
          options: {
            amount: parseInt(style.marginRight)
          }
        }
      });
    }
    if (style.marginTop != null) {
      object.appendOnto(config, {
        blockPrependor: {
          options: {
            amount: parseInt(style.marginTop)
          }
        }
      });
    }
    if (style.marginBottom != null) {
      object.appendOnto(config, {
        blockAppendor: {
          options: {
            amount: parseInt(style.marginBottom)
          }
        }
      });
    }
  },
  _bullet: function(style, config) {
    var after, before, bullet, conf, ref;
    if ((style.bullet != null) && style.bullet.enabled) {
      bullet = style.bullet;
      conf = {};
      conf.alignment = style.bullet.alignment;
      ref = _common.getStyleTagsFor({
        color: bullet.color,
        background: bullet.background
      }), before = ref.before, after = ref.after;
      conf.char = before + bullet.char + after;
      object.appendOnto(config, {
        linePrependor: {
          options: {
            bullet: conf
          }
        }
      });
    }
  },
  _dims: function(style, config) {
    var w;
    if (style.width != null) {
      w = parseInt(style.width);
      config.width = w;
    }
  }
};
