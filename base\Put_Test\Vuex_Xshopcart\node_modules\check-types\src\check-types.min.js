!function(n){"use strict";var r,f,e,t,o,i,u,c,a,l,s,y,p,b,m;function v(n){return null!=n}function d(n){return"number"==typeof n&&l<n&&n<s}function h(n){return"number"==typeof n&&n%1==0}function g(n,t){return d(n)&&t<n}function O(n,t){return d(n)&&n<t}function j(n,t){return d(n)&&t<=n}function E(n,t){return d(n)&&n<=t}function w(n){return"string"==typeof n}function I(n){return w(n)&&""!==n}function S(n){return"[object Object]"===Object.prototype.toString.call(n)}function A(n,t){for(var r in n)if(n.hasOwnProperty(r)&&t(r,n[r]))return!0;return!1}function N(n,t){try{return n instanceof t}catch(n){return!1}}function P(n,t){var r;for(r in t)if(t.hasOwnProperty(r)){if(!1===n.hasOwnProperty(r)||typeof n[r]!=typeof t[r])return!1;if(S(n[r])&&!1===P(n[r],t[r]))return!1}return!0}function k(n){return v(n)&&0<=n.length}function T(n){return"function"==typeof n}function x(n,t){for(var r in n)n.hasOwnProperty(r)&&t(r,n[r])}function q(n,t){var r;for(r=0;r<n.length;r+=1)if(n[r]===t)return t;return!t}function L(n,t){var r,e;for(r in n)if(n.hasOwnProperty(r)){if(S(e=n[r])&&L(e,t)===t)return t;if(e===t)return t}return!t}function F(r,n){return x(n,function(n,t){r[n]=t}),r}function V(o,i){return function(){return t=arguments,r=i,e=(n=o).l||n.length,u=t[e],f=t[e+1],Y(n.apply(null,t),I(u)?u:r,T(f)?f:TypeError),t[0];var n,t,r,e,u,f}}function Y(n,t,r){if(n)return n;throw new(r||Error)(t||"Assertion failed")}function _(n){var t=function(){return z(n.apply(null,arguments))};return t.l=n.length,t}function z(n){return!n}function D(r,e,u){var n=function(){var n,t;if(n=arguments[0],"maybe"===r&&i.assigned(n))return!0;if(!e(n))return!1;n=function(n,t){switch(n){case k:return a.call(t);case S:return p(t).map(function(n){return t[n]});default:return t}}(e,n),t=a.call(arguments,1);try{n.forEach(function(n){if(("maybe"!==r||v(n))&&!u.apply(null,[n].concat(t)))throw 0})}catch(n){return!1}return!0};return n.l=u.length,n}function G(n,t){return R([n,e,t])}function R(r){var e,n,t,u;return e=r.shift(),n=r.pop(),t=r.pop(),u=n||{},x(t,function(n,t){Object.defineProperty(u,n,{configurable:!1,enumerable:!0,writable:!1,value:e.apply(null,r.concat(t,f[n]))})}),u}function B(n,t){return R([n,t,null])}function C(t,r){c.forEach(function(n){t[n].of=B(r,e[n].of)})}r={v:"value",n:"number",s:"string",b:"boolean",o:"object",t:"type",a:"array",al:"array-like",i:"iterable",d:"date",f:"function",l:"length"},f={},e={},[{n:"equal",f:function(n,t){return n===t},s:"v"},{n:"undefined",f:function(n){return void 0===n},s:"v"},{n:"null",f:function(n){return null===n},s:"v"},{n:"assigned",f:v,s:"v"},{n:"primitive",f:function(n){var t;switch(n){case null:case void 0:case!1:case!0:return!0}return"string"==(t=typeof n)||"number"===t||b&&"symbol"===t},s:"v"},{n:"includes",f:function(n,r){var t,e;if(!v(n))return!1;if(b&&n[Symbol.iterator]&&T(n.values)){t=n.values();do{if((e=t.next()).value===r)return!0}while(!e.done);return!1}return A(n,function(n,t){return t===r})},s:"v"},{n:"zero",f:function(n){return 0===n}},{n:"infinity",f:function(n){return n===l||n===s}},{n:"number",f:d},{n:"integer",f:h},{n:"even",f:function(n){return"number"==typeof n&&n%2==0}},{n:"odd",f:function(n){return h(n)&&n%2!=0}},{n:"greater",f:g},{n:"less",f:O},{n:"between",f:function(n,t,r){if(t<r)return g(n,t)&&n<r;return O(n,t)&&r<n}},{n:"greaterOrEqual",f:j},{n:"lessOrEqual",f:E},{n:"inRange",f:function(n,t,r){if(t<r)return j(n,t)&&n<=r;return E(n,t)&&r<=n}},{n:"positive",f:function(n){return g(n,0)}},{n:"negative",f:function(n){return O(n,0)}},{n:"string",f:w,s:"s"},{n:"emptyString",f:function(n){return""===n},s:"s"},{n:"nonEmptyString",f:I,s:"s"},{n:"contains",f:function(n,t){return w(n)&&-1!==n.indexOf(t)},s:"s"},{n:"match",f:function(n,t){return w(n)&&!!n.match(t)},s:"s"},{n:"boolean",f:function(n){return!1===n||!0===n},s:"b"},{n:"object",f:S,s:"o"},{n:"emptyObject",f:function(n){return S(n)&&!A(n,function(){return!0})},s:"o"},{n:"nonEmptyObject",f:function(n){return S(n)&&A(n,function(){return!0})},s:"o"},{n:"instanceStrict",f:N,s:"t"},{n:"instance",f:function(n,t){try{return N(n,t)||n.constructor.name===t.name||Object.prototype.toString.call(n)==="[object "+t.name+"]"}catch(n){return!1}},s:"t"},{n:"like",f:P,s:"t"},{n:"array",f:function(n){return y(n)},s:"a"},{n:"emptyArray",f:function(n){return y(n)&&0===n.length},s:"a"},{n:"nonEmptyArray",f:function(n){return y(n)&&0<n.length},s:"a"},{n:"arrayLike",f:k,s:"al"},{n:"iterable",f:function(n){return b?v(n)&&T(n[Symbol.iterator]):k(n)},s:"i"},{n:"date",f:function(n){return N(n,Date)&&h(n.getTime())},s:"d"},{n:"function",f:T,s:"f"},{n:"hasLength",f:function(n,t){return v(n)&&n.length===t},s:"l"}].map(function(n){var t=n.n;f[t]="Invalid "+r[n.s||"n"],e[t]=n.f}),t={map:function t(e,r){var u;u=y(e)?[]:{};if(T(r))x(e,function(n,t){u[n]=r(t)});else{y(r)||o.object(r);var f=p(e||{});x(r,function(r,n){f.some(function(n,t){return n===r&&(f.splice(t,1),!0)}),T(n)?i.assigned(e)?u[r]=!!n.m:u[r]=n(e[r]):u[r]=t(e[r],n)})}return u},all:function(n){if(y(n))return q(n,!1);return o.object(n),L(n,!1)},any:function(n){if(y(n))return q(n,!0);return o.object(n),L(n,!0)}},c=["array","arrayLike","iterable","object"],a=Array.prototype.slice,l=Number.NEGATIVE_INFINITY,s=Number.POSITIVE_INFINITY,y=Array.isArray,p=Object.keys,b="function"==typeof Symbol,t=F(t,e),o=G(V,Y),i=G(_,z),u=G(function(n){var t=function(){return!!i.assigned(arguments[0])||n.apply(null,arguments)};return t.l=n.length,t.m=!0,t},function(n){return!1===v(n)||n}),o.not=B(V,i),o.maybe=B(V,u),c.forEach(function(n){e[n].of=R([D.bind(null,null),e[n],e,null])}),C(o,V),C(i,_),c.forEach(function(n){u[n].of=R([D.bind(null,"maybe"),e[n],e,null]),o.maybe[n].of=B(V,u[n].of),o.not[n].of=B(V,i[n].of)}),m=F(t,{assert:o,not:i,maybe:u}),"function"==typeof define&&define.amd?define(function(){return m}):"undefined"!=typeof module&&null!==module&&module.exports?module.exports=m:n.check=m}(this);