{"name": "chrome-trace-event", "description": "A library to create a trace of your node app per Google's Trace Event format.", "license": "MIT", "version": "1.0.2", "author": "<PERSON>, <PERSON>", "keywords": ["trace-event", "trace", "event", "trace-viewer", "google"], "repository": {"type": "git", "url": "github.com:samccone/chrome-trace-event"}, "main": "./dist/trace-event.js", "typings": "./dist/trace-event.d.ts", "dependencies": {"tslib": "^1.9.0"}, "devDependencies": {"@types/node": "^9.6.5", "prettier": "^1.12.1", "tape": "4.8.0", "typescript": "^2.8.1"}, "engines": {"node": ">=6.0"}, "scripts": {"build": "tsc", "check_format": "prettier -l lib/** test/** examples/**", "test": "tape test/*.test.js"}}