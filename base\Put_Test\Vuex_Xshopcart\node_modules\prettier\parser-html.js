!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(((e=e||self).prettierPlugins=e.prettierPlugins||{},e.prettierPlugins.html={}))}(this,(function(e){"use strict";function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function i(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&s(e,t)}function o(e){return(o=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function s(e,t){return(s=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function l(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function c(e,t,r){return(c="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=o(e)););return e}(e,t);if(n){var i=Object.getOwnPropertyDescriptor(n,t);return i.get?i.get.call(r):i.value}})(e,t,r||e)}function u(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if(!(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e)))return;var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),!t||r.length!==t);n=!0);}catch(e){i=!0,a=e}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}return r}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}var p=/[|\\{}()[\]^$+*?.]/g,h=function(e){if("string"!=typeof e)throw new TypeError("Expected a string");return e.replace(p,"\\$&")},d={"---":"yaml","+++":"toml"};var f=function(e){var t=Object.keys(d).map(h).join("|"),r=e.match(new RegExp("^(".concat(t,")[^\\n\\S]*\\n(?:([\\s\\S]*?)\\n)?\\1[^\\n\\S]*(\\n|$)")));if(null===r)return{frontMatter:null,content:e};var n=r[0].replace(/\n$/,""),i=r[1],a=r[2];return{frontMatter:{type:d[i],value:a,raw:n},content:r[0].replace(/[^\n]/g," ")+e.slice(r[0].length)}},m={area:"none",base:"none",basefont:"none",datalist:"none",head:"none",link:"none",meta:"none",noembed:"none",noframes:"none",param:"none",rp:"none",script:"block",source:"block",style:"none",template:"inline",track:"block",title:"none",html:"block",body:"block",address:"block",blockquote:"block",center:"block",div:"block",figure:"block",figcaption:"block",footer:"block",form:"block",header:"block",hr:"block",legend:"block",listing:"block",main:"block",p:"block",plaintext:"block",pre:"block",xmp:"block",slot:"contents",ruby:"ruby",rt:"ruby-text",article:"block",aside:"block",h1:"block",h2:"block",h3:"block",h4:"block",h5:"block",h6:"block",hgroup:"block",nav:"block",section:"block",dir:"block",dd:"block",dl:"block",dt:"block",ol:"block",ul:"block",li:"list-item",table:"table",caption:"table-caption",colgroup:"table-column-group",col:"table-column",thead:"table-header-group",tbody:"table-row-group",tfoot:"table-footer-group",tr:"table-row",td:"table-cell",th:"table-cell",fieldset:"block",button:"inline-block",video:"inline-block",audio:"inline-block"},g="inline",v={listing:"pre",plaintext:"pre",pre:"pre",xmp:"pre",nobr:"nowrap",table:"initial",textarea:"pre-wrap"},y="normal",_=Object.freeze({__proto__:null,default:["a","abbr","acronym","address","applet","area","article","aside","audio","b","base","basefont","bdi","bdo","bgsound","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","command","content","data","datalist","dd","del","details","dfn","dialog","dir","div","dl","dt","element","em","embed","fieldset","figcaption","figure","font","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","image","img","input","ins","isindex","kbd","keygen","label","legend","li","link","listing","main","map","mark","marquee","math","menu","menuitem","meta","meter","multicol","nav","nextid","nobr","noembed","noframes","noscript","object","ol","optgroup","option","output","p","param","picture","plaintext","pre","progress","q","rb","rbc","rp","rt","rtc","ruby","s","samp","script","section","select","shadow","slot","small","source","spacer","span","strike","strong","style","sub","summary","sup","svg","table","tbody","td","template","textarea","tfoot","th","thead","time","title","tr","track","tt","u","ul","var","video","wbr","xmp"]}),T=["accesskey","charset","coords","download","href","hreflang","name","ping","referrerpolicy","rel","rev","shape","tabindex","target","type"],b=["title"],S=["align","alt","archive","code","codebase","height","hspace","name","object","vspace","width"],C=["accesskey","alt","coords","download","href","hreflang","nohref","ping","referrerpolicy","rel","shape","tabindex","target","type"],E=["autoplay","controls","crossorigin","loop","muted","preload","src"],k=["href","target"],A=["color","face","size"],w=["dir"],N=["cite"],x=["alink","background","bgcolor","link","text","vlink"],P=["clear"],D=["accesskey","autofocus","disabled","form","formaction","formenctype","formmethod","formnovalidate","formtarget","name","tabindex","type","value"],O=["height","width"],L=["align"],q=["align","char","charoff","span","valign","width"],R=["align","char","charoff","span","valign","width"],$=["value"],I=["cite","datetime"],M=["open"],B=["title"],U=["open"],F=["compact"],V=["align"],j=["compact"],G=["height","src","type","width"],H=["disabled","form","name"],X=["color","face","size"],z=["accept","accept-charset","action","autocomplete","enctype","method","name","novalidate","target"],W=["frameborder","longdesc","marginheight","marginwidth","name","noresize","scrolling","src"],Q=["cols","rows"],J=["align"],K=["align"],Y=["align"],Z=["align"],ee=["align"],te=["align"],re=["profile"],ne=["align","noshade","size","width"],ie=["manifest","version"],ae=["align","allow","allowfullscreen","allowpaymentrequest","allowusermedia","frameborder","height","longdesc","marginheight","marginwidth","name","referrerpolicy","sandbox","scrolling","src","srcdoc","width"],oe=["align","alt","border","crossorigin","decoding","height","hspace","ismap","longdesc","name","referrerpolicy","sizes","src","srcset","usemap","vspace","width"],se=["accept","accesskey","align","alt","autocomplete","autofocus","checked","dirname","disabled","form","formaction","formenctype","formmethod","formnovalidate","formtarget","height","ismap","list","max","maxlength","min","minlength","multiple","name","pattern","placeholder","readonly","required","size","src","step","tabindex","title","type","usemap","value","width"],le=["cite","datetime"],ce=["prompt"],ue=["accesskey","for","form"],pe=["accesskey","align"],he=["type","value"],de=["as","charset","color","crossorigin","href","hreflang","imagesizes","imagesrcset","integrity","media","nonce","referrerpolicy","rel","rev","sizes","target","title","type"],fe=["name"],me=["compact"],ge=["charset","content","http-equiv","name","scheme"],ve=["high","low","max","min","optimum","value"],ye=["align","archive","border","classid","codebase","codetype","data","declare","form","height","hspace","name","standby","tabindex","type","typemustmatch","usemap","vspace","width"],_e=["compact","reversed","start","type"],Te=["disabled","label"],be=["disabled","label","selected","value"],Se=["for","form","name"],Ce=["align"],Ee=["name","type","value","valuetype"],ke=["width"],Ae=["max","value"],we=["cite"],Ne=["async","charset","crossorigin","defer","integrity","language","nomodule","nonce","referrerpolicy","src","type"],xe=["autocomplete","autofocus","disabled","form","multiple","name","required","size","tabindex"],Pe=["name"],De=["media","sizes","src","srcset","type"],Oe=["media","nonce","title","type"],Le=["align","bgcolor","border","cellpadding","cellspacing","frame","rules","summary","width"],qe=["align","char","charoff","valign"],Re=["abbr","align","axis","bgcolor","char","charoff","colspan","headers","height","nowrap","rowspan","scope","valign","width"],$e=["accesskey","autocomplete","autofocus","cols","dirname","disabled","form","maxlength","minlength","name","placeholder","readonly","required","rows","tabindex","wrap"],Ie=["align","char","charoff","valign"],Me=["abbr","align","axis","bgcolor","char","charoff","colspan","headers","height","nowrap","rowspan","scope","valign","width"],Be=["align","char","charoff","valign"],Ue=["datetime"],Fe=["align","bgcolor","char","charoff","valign"],Ve=["default","kind","label","src","srclang"],je=["compact","type"],Ge=["autoplay","controls","crossorigin","height","loop","muted","playsinline","poster","preload","src","width"],He={"*":["accesskey","autocapitalize","autofocus","class","contenteditable","dir","draggable","enterkeyhint","hidden","id","inputmode","is","itemid","itemprop","itemref","itemscope","itemtype","lang","nonce","slot","spellcheck","style","tabindex","title","translate"],a:T,abbr:b,applet:S,area:C,audio:E,base:k,basefont:A,bdo:w,blockquote:N,body:x,br:P,button:D,canvas:O,caption:L,col:q,colgroup:R,data:$,del:I,details:M,dfn:B,dialog:U,dir:F,div:V,dl:j,embed:G,fieldset:H,font:X,form:z,frame:W,frameset:Q,h1:J,h2:K,h3:Y,h4:Z,h5:ee,h6:te,head:re,hr:ne,html:ie,iframe:ae,img:oe,input:se,ins:le,isindex:ce,label:ue,legend:pe,li:he,link:de,map:fe,menu:me,meta:ge,meter:ve,object:ye,ol:_e,optgroup:Te,option:be,output:Se,p:Ce,param:Ee,pre:ke,progress:Ae,q:we,script:Ne,select:xe,slot:Pe,source:De,style:Oe,table:Le,tbody:qe,td:Re,textarea:$e,tfoot:Ie,th:Me,thead:Be,time:Ue,tr:Fe,track:Ve,ul:je,video:Ge},Xe=Object.freeze({__proto__:null,a:T,abbr:b,applet:S,area:C,audio:E,base:k,basefont:A,bdo:w,blockquote:N,body:x,br:P,button:D,canvas:O,caption:L,col:q,colgroup:R,data:$,del:I,details:M,dfn:B,dialog:U,dir:F,div:V,dl:j,embed:G,fieldset:H,font:X,form:z,frame:W,frameset:Q,h1:J,h2:K,h3:Y,h4:Z,h5:ee,h6:te,head:re,hr:ne,html:ie,iframe:ae,img:oe,input:se,ins:le,isindex:ce,label:ue,legend:pe,li:he,link:de,map:fe,menu:me,meta:ge,meter:ve,object:ye,ol:_e,optgroup:Te,option:be,output:Se,p:Ce,param:Ee,pre:ke,progress:Ae,q:we,script:Ne,select:xe,slot:Pe,source:De,style:Oe,table:Le,tbody:qe,td:Re,textarea:$e,tfoot:Ie,th:Me,thead:Be,time:Ue,tr:Fe,track:Ve,ul:je,video:Ge,default:He});function ze(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function We(e,t){return e(t={exports:{}},t.exports),t.exports}function Qe(e){return e&&e.default||e}var Je=Qe(_),Ke=Qe(Xe),Ye=m,Ze=g,et=v,tt=y,rt=nt(Je);function nt(e){var t=Object.create(null),r=!0,n=!1,i=void 0;try{for(var a,o=e[Symbol.iterator]();!(r=(a=o.next()).done);r=!0){t[a.value]=!0}}catch(e){n=!0,i=e}finally{try{r||null==o.return||o.return()}finally{if(n)throw i}}return t}function it(e,t){return!("element"!==e.type||"template"!==e.fullName||!e.attrMap.lang||"html"===e.attrMap.lang)||(!("ieConditionalComment"!==e.type||!e.lastChild||e.lastChild.isSelfClosing||e.lastChild.endSourceSpan)||("ieConditionalComment"===e.type&&!e.complete||("vue"===t.parser&&"element"===e.type&&"root"===e.parent.type&&-1===["template","style","script","html"].indexOf(e.fullName)||!(!vt(e)||!e.children.some((function(e){return"text"!==e.type&&"interpolation"!==e.type}))))))}function at(e){return"attribute"!==e.type&&!ot(e)&&(!!e.parent&&("number"==typeof e.index&&0!==e.index&&function(e){return"comment"===e.type&&"prettier-ignore"===e.value.trim()}(e.parent.children[e.index-1])))}function ot(e){return"text"===e.type||"comment"===e.type}function st(e){return"element"===e.type&&("script"===e.fullName||"style"===e.fullName||"svg:style"===e.fullName)}function lt(e){return"yaml"===e.type||"toml"===e.type}function ct(e){return yt(e).startsWith("pre")}function ut(e){return"element"===e.type&&0!==e.children.length&&(-1!==["html","head","ul","ol","select"].indexOf(e.name)||e.cssDisplay.startsWith("table")&&"table-cell"!==e.cssDisplay)}function pt(e){return mt(e)||"element"===e.type&&"br"===e.fullName||ht(e)}function ht(e){return dt(e)&&ft(e)}function dt(e){return e.hasLeadingSpaces&&(e.prev?e.prev.sourceSpan.end.line<e.sourceSpan.start.line:"root"===e.parent.type||e.parent.startSourceSpan.end.line<e.sourceSpan.start.line)}function ft(e){return e.hasTrailingSpaces&&(e.next?e.next.sourceSpan.start.line>e.sourceSpan.end.line:"root"===e.parent.type||e.parent.endSourceSpan.start.line>e.sourceSpan.end.line)}function mt(e){switch(e.type){case"ieConditionalComment":case"comment":case"directive":return!0;case"element":return-1!==["script","select"].indexOf(e.name)}return!1}function gt(e){return"block"===e||"list-item"===e||e.startsWith("table")}function vt(e){return yt(e).startsWith("pre")}function yt(e){return"element"===e.type&&!e.namespace&&et[e.name]||tt}var _t={HTML_ELEMENT_ATTRIBUTES:function(e,t){for(var r=Object.create(null),n=0,i=Object.keys(e);n<i.length;n++){var a=i[n];r[a]=t(e[a],a)}return r}(Ke,nt),HTML_TAGS:rt,canHaveInterpolation:function(e){return e.children&&!st(e)},countChars:function(e,t){for(var r=0,n=0;n<e.length;n++)e[n]===t&&r++;return r},countParents:function(e){for(var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){return!0},n=0,i=e.stack.length-1;i>=0;i--){var a=e.stack[i];a&&"object"===t(a)&&!Array.isArray(a)&&r(a)&&n++}return n},dedentString:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(e){var t=1/0,r=!0,n=!1,i=void 0;try{for(var a,o=e.split("\n")[Symbol.iterator]();!(r=(a=o.next()).done);r=!0){var s=a.value;if(0!==s.length){if(/\S/.test(s[0]))return 0;var l=s.match(/^\s*/)[0].length;s.length!==l&&l<t&&(t=l)}}}catch(e){n=!0,i=e}finally{try{r||null==o.return||o.return()}finally{if(n)throw i}}return t===1/0?0:t}(e);return 0===t?e:e.split("\n").map((function(e){return e.slice(t)})).join("\n")},forceBreakChildren:ut,forceBreakContent:function(e){return ut(e)||"element"===e.type&&0!==e.children.length&&(-1!==["body","script","style"].indexOf(e.name)||e.children.some((function(e){return function(e){return e.children&&e.children.some((function(e){return"text"!==e.type}))}(e)})))||e.firstChild&&e.firstChild===e.lastChild&&dt(e.firstChild)&&(!e.lastChild.isTrailingSpaceSensitive||ft(e.lastChild))},forceNextEmptyLine:function(e){return lt(e)||e.next&&e.sourceSpan.end.line+1<e.next.sourceSpan.start.line},getLastDescendant:function e(t){return t.lastChild?e(t.lastChild):t},getNodeCssStyleDisplay:function(e,t){if(e.prev&&"comment"===e.prev.type){var r=e.prev.value.match(/^\s*display:\s*([a-z]+)\s*$/);if(r)return r[1]}var n=!1;if("element"===e.type&&"svg"===e.namespace){if(!function(e,t){for(var r=e;r;){if(t(r))return!0;r=r.parent}return!1}(e,(function(e){return"svg:foreignObject"===e.fullName})))return"svg"===e.name?"inline-block":"block";n=!0}switch(t.htmlWhitespaceSensitivity){case"strict":return"inline";case"ignore":return"block";default:return"element"===e.type&&(!e.namespace||n)&&Ye[e.name]||Ze}},getNodeCssStyleWhiteSpace:yt,getPrettierIgnoreAttributeCommentData:function(e){var t=e.trim().match(/^prettier-ignore-attribute(?:\s+([^]+))?$/);return!!t&&(!t[1]||t[1].split(/\s+/))},hasPrettierIgnore:at,identity:function(e){return e},inferScriptParser:function(e){if("script"===e.name&&!e.attrMap.src){if(!e.attrMap.lang&&!e.attrMap.type||"module"===e.attrMap.type||"text/javascript"===e.attrMap.type||"text/babel"===e.attrMap.type||"application/javascript"===e.attrMap.type)return"babel";if("application/x-typescript"===e.attrMap.type||"ts"===e.attrMap.lang||"tsx"===e.attrMap.lang)return"typescript";if("text/markdown"===e.attrMap.type)return"markdown";if(e.attrMap.type.endsWith("json")||e.attrMap.type.endsWith("importmap"))return"json"}if("style"===e.name){if(!e.attrMap.lang||"postcss"===e.attrMap.lang||"css"===e.attrMap.lang)return"css";if("scss"===e.attrMap.lang)return"scss";if("less"===e.attrMap.lang)return"less"}return null},isDanglingSpaceSensitiveNode:function(e){return!gt(t=e.cssDisplay)&&"inline-block"!==t&&!st(e);var t},isFrontMatterNode:lt,isIndentationSensitiveNode:ct,isLeadingSpaceSensitiveNode:function(e){var t=function(){if(lt(e))return!1;if(("text"===e.type||"interpolation"===e.type)&&e.prev&&("text"===e.prev.type||"interpolation"===e.prev.type))return!0;if(!e.parent||"none"===e.parent.cssDisplay)return!1;if(vt(e.parent))return!0;if(!e.prev&&("root"===e.parent.type||st(e.parent)||(t=e.parent.cssDisplay,gt(t)||"inline-block"===t)))return!1;var t;if(e.prev&&!function(e){return!gt(e)}(e.prev.cssDisplay))return!1;return!0}();return t&&!e.prev&&e.parent&&e.parent.tagDefinition&&e.parent.tagDefinition.ignoreFirstLf?"interpolation"===e.type:t},isPreLikeNode:vt,isScriptLikeTag:st,isTextLikeNode:ot,isTrailingSpaceSensitiveNode:function(e){return!lt(e)&&(!("text"!==e.type&&"interpolation"!==e.type||!e.next||"text"!==e.next.type&&"interpolation"!==e.next.type)||!(!e.parent||"none"===e.parent.cssDisplay)&&(!!vt(e.parent)||!(!e.next&&("root"===e.parent.type||st(e.parent)||(t=e.parent.cssDisplay,gt(t)||"inline-block"===t)))&&!(e.next&&!function(e){return!gt(e)}(e.next.cssDisplay))));var t},isWhitespaceSensitiveNode:function(e){return st(e)||"interpolation"===e.type||ct(e)},normalizeParts:function(e){for(var t=[],r=e.slice();0!==r.length;){var n=r.shift();n&&("concat"!==n.type?0===t.length||"string"!=typeof t[t.length-1]||"string"!=typeof n?t.push(n):t.push(t.pop()+n):Array.prototype.unshift.apply(r,n.parts))}return t},preferHardlineAsLeadingSpaces:function(e){return mt(e)||e.prev&&pt(e.prev)||ht(e)},preferHardlineAsTrailingSpaces:pt,shouldNotPrintClosingTag:function(e,t){return!e.isSelfClosing&&!e.endSourceSpan&&(at(e)||it(e.parent,t))},shouldPreserveContent:it,unescapeQuoteEntities:function(e){return e.replace(/&apos;/g,"'").replace(/&quot;/g,'"')}};var Tt=function(e){return/^\s*<!--\s*@(format|prettier)\s*-->/.test(e)};var bt=function(e,t){var r=new SyntaxError(e+" ("+t.start.line+":"+t.start.column+")");return r.loc=t,r},St={attrs:!0,children:!0},Ct=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};r(this,e);for(var n=0,i=Object.keys(t);n<i.length;n++){var a=i[n],o=t[a];a in St?this._setNodes(a,o):this[a]=o}}return i(e,[{key:"_setNodes",value:function(e,t){t!==this[e]&&(this[e]=kt(t,this),"attrs"===e&&At(this,{attrMap:this[e].reduce((function(e,t){return e[t.fullName]=t.value,e}),Object.create(null))}))}},{key:"map",value:function(t){var r=null;for(var n in St){var i=this[n];if(i){var a=Et(i,(function(e){return e.map(t)}));r!==i&&(r||(r=new e),r._setNodes(n,a))}}if(r){for(var o in this)o in St||(r[o]=this[o]);At(r,{index:this.index,siblings:this.siblings,prev:this.prev,next:this.next,parent:this.parent})}return t(r||this)}},{key:"clone",value:function(t){return new e(t?Object.assign({},this,t):this)}},{key:"firstChild",get:function(){return this.children&&0!==this.children.length?this.children[0]:null}},{key:"lastChild",get:function(){return this.children&&0!==this.children.length?this.children[this.children.length-1]:null}},{key:"rawName",get:function(){return this.hasExplicitNamespace?this.fullName:this.name}},{key:"fullName",get:function(){return this.namespace?this.namespace+":"+this.name:this.name}}]),e}();function Et(e,t){var r=e.map(t);return r.some((function(t,r){return t!==e[r]}))?r:e}function kt(e,t){for(var r=e.map((function(e){return e instanceof Ct?e.clone():new Ct(e)})),n=null,i=r[0],a=r[1]||null,o=0;o<r.length;o++)At(i,{index:o,siblings:r,prev:n,next:a,parent:t}),n=i,i=a,a=r[o+2]||null;return r}function At(e,t){var r=Object.keys(t).reduce((function(e,r){return e[r]={value:t[r],enumerable:!1},e}),{});Object.defineProperties(e,r)}var wt={Node:Ct},Nt=[[/^(\[if([^\]]*?)\]>)([\s\S]*?)<!\s*\[endif\]$/,function(e,t,r){var n=u(r,4),i=n[1],a=n[2],o=n[3],s="\x3c!--".length+i.length,l=e.sourceSpan.start.moveBy(s),c=l.moveBy(o.length),p=e.sourceSpan.constructor,h=u(function(){try{return[!0,t(o,l).children]}catch(e){return[!1,[{type:"text",value:o,sourceSpan:new p(l,c)}]]}}(),2),d=h[0],f=h[1];return{type:"ieConditionalComment",complete:d,children:f,condition:a.trim().replace(/\s+/g," "),sourceSpan:e.sourceSpan,startSourceSpan:new p(e.sourceSpan.start,l),endSourceSpan:new p(c,e.sourceSpan.end)}}],[/^\[if([^\]]*?)\]><!$/,function(e,t,r){return{type:"ieConditionalStartComment",condition:u(r,2)[1].trim().replace(/\s+/g," "),sourceSpan:e.sourceSpan}}],[/^<!\s*\[endif\]$/,function(e){return{type:"ieConditionalEndComment",sourceSpan:e.sourceSpan}}]];var xt=function(e,t){if(e.value){var r,n=!0,i=!1,a=void 0;try{for(var o,s=Nt[Symbol.iterator]();!(n=(o=s.next()).done);n=!0){var l=u(o.value,2),c=l[0],p=l[1];if(r=e.value.match(c))return p(e,t,r)}}catch(e){i=!0,a=e}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}}return null},Pt=We((function(e,t){function r(e){if(":"!=e[0])return[null,e];var t=e.indexOf(":",1);if(-1==t)throw new Error('Unsupported format "'.concat(e,'" expecting ":namespace:name"'));return[e.slice(1,t),e.slice(t+1)]}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0}),function(e){e[e.RAW_TEXT=0]="RAW_TEXT",e[e.ESCAPABLE_RAW_TEXT=1]="ESCAPABLE_RAW_TEXT",e[e.PARSABLE_DATA=2]="PARSABLE_DATA"}(t.TagContentType||(t.TagContentType={})),t.splitNsName=r,t.isNgContainer=function(e){return"ng-container"===r(e)[1]},t.isNgContent=function(e){return"ng-content"===r(e)[1]},t.isNgTemplate=function(e){return"ng-template"===r(e)[1]},t.getNsPrefix=function(e){return null===e?null:r(e)[0]},t.mergeNsAndName=function(e,t){return e?":".concat(e,":").concat(t):t},t.NAMED_ENTITIES={Aacute:"Á",aacute:"á",Abreve:"Ă",abreve:"ă",ac:"∾",acd:"∿",acE:"∾̳",Acirc:"Â",acirc:"â",acute:"´",Acy:"А",acy:"а",AElig:"Æ",aelig:"æ",af:"⁡",Afr:"𝔄",afr:"𝔞",Agrave:"À",agrave:"à",alefsym:"ℵ",aleph:"ℵ",Alpha:"Α",alpha:"α",Amacr:"Ā",amacr:"ā",amalg:"⨿",AMP:"&",amp:"&",And:"⩓",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"Å",angzarr:"⍼",Aogon:"Ą",aogon:"ą",Aopf:"𝔸",aopf:"𝕒",ap:"≈",apacir:"⩯",apE:"⩰",ape:"≊",apid:"≋",apos:"'",ApplyFunction:"⁡",approx:"≈",approxeq:"≊",Aring:"Å",aring:"å",Ascr:"𝒜",ascr:"𝒶",Assign:"≔",ast:"*",asymp:"≈",asympeq:"≍",Atilde:"Ã",atilde:"ã",Auml:"Ä",auml:"ä",awconint:"∳",awint:"⨑",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",Backslash:"∖",Barv:"⫧",barvee:"⊽",Barwed:"⌆",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",Bcy:"Б",bcy:"б",bdquo:"„",becaus:"∵",Because:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",Bernoullis:"ℬ",Beta:"Β",beta:"β",beth:"ℶ",between:"≬",Bfr:"𝔅",bfr:"𝔟",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bNot:"⫭",bnot:"⌐",Bopf:"𝔹",bopf:"𝕓",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxbox:"⧉",boxDL:"╗",boxDl:"╖",boxdL:"╕",boxdl:"┐",boxDR:"╔",boxDr:"╓",boxdR:"╒",boxdr:"┌",boxH:"═",boxh:"─",boxHD:"╦",boxHd:"╤",boxhD:"╥",boxhd:"┬",boxHU:"╩",boxHu:"╧",boxhU:"╨",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxUL:"╝",boxUl:"╜",boxuL:"╛",boxul:"┘",boxUR:"╚",boxUr:"╙",boxuR:"╘",boxur:"└",boxV:"║",boxv:"│",boxVH:"╬",boxVh:"╫",boxvH:"╪",boxvh:"┼",boxVL:"╣",boxVl:"╢",boxvL:"╡",boxvl:"┤",boxVR:"╠",boxVr:"╟",boxvR:"╞",boxvr:"├",bprime:"‵",Breve:"˘",breve:"˘",brvbar:"¦",Bscr:"ℬ",bscr:"𝒷",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",Bumpeq:"≎",bumpeq:"≏",Cacute:"Ć",cacute:"ć",Cap:"⋒",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",CapitalDifferentialD:"ⅅ",caps:"∩︀",caret:"⁁",caron:"ˇ",Cayleys:"ℭ",ccaps:"⩍",Ccaron:"Č",ccaron:"č",Ccedil:"Ç",ccedil:"ç",Ccirc:"Ĉ",ccirc:"ĉ",Cconint:"∰",ccups:"⩌",ccupssm:"⩐",Cdot:"Ċ",cdot:"ċ",cedil:"¸",Cedilla:"¸",cemptyv:"⦲",cent:"¢",CenterDot:"·",centerdot:"·",Cfr:"ℭ",cfr:"𝔠",CHcy:"Ч",chcy:"ч",check:"✓",checkmark:"✓",Chi:"Χ",chi:"χ",cir:"○",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",CircleDot:"⊙",circledR:"®",circledS:"Ⓢ",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",cirE:"⧃",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",clubs:"♣",clubsuit:"♣",Colon:"∷",colon:":",Colone:"⩴",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",Congruent:"≡",Conint:"∯",conint:"∮",ContourIntegral:"∮",Copf:"ℂ",copf:"𝕔",coprod:"∐",Coproduct:"∐",COPY:"©",copy:"©",copysr:"℗",CounterClockwiseContourIntegral:"∳",crarr:"↵",Cross:"⨯",cross:"✗",Cscr:"𝒞",cscr:"𝒸",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",Cup:"⋓",cup:"∪",cupbrcap:"⩈",CupCap:"≍",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"¤",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",Dagger:"‡",dagger:"†",daleth:"ℸ",Darr:"↡",dArr:"⇓",darr:"↓",dash:"‐",Dashv:"⫤",dashv:"⊣",dbkarow:"⤏",dblac:"˝",Dcaron:"Ď",dcaron:"ď",Dcy:"Д",dcy:"д",DD:"ⅅ",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",DDotrahd:"⤑",ddotseq:"⩷",deg:"°",Del:"∇",Delta:"Δ",delta:"δ",demptyv:"⦱",dfisht:"⥿",Dfr:"𝔇",dfr:"𝔡",dHar:"⥥",dharl:"⇃",dharr:"⇂",DiacriticalAcute:"´",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",diam:"⋄",Diamond:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"¨",DifferentialD:"ⅆ",digamma:"ϝ",disin:"⋲",div:"÷",divide:"÷",divideontimes:"⋇",divonx:"⋇",DJcy:"Ђ",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",Dopf:"𝔻",dopf:"𝕕",Dot:"¨",dot:"˙",DotDot:"⃜",doteq:"≐",doteqdot:"≑",DotEqual:"≐",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",DoubleContourIntegral:"∯",DoubleDot:"¨",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",Downarrow:"⇓",downarrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",Dscr:"𝒟",dscr:"𝒹",DScy:"Ѕ",dscy:"ѕ",dsol:"⧶",Dstrok:"Đ",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",DZcy:"Џ",dzcy:"џ",dzigrarr:"⟿",Eacute:"É",eacute:"é",easter:"⩮",Ecaron:"Ě",ecaron:"ě",ecir:"≖",Ecirc:"Ê",ecirc:"ê",ecolon:"≕",Ecy:"Э",ecy:"э",eDDot:"⩷",Edot:"Ė",eDot:"≑",edot:"ė",ee:"ⅇ",efDot:"≒",Efr:"𝔈",efr:"𝔢",eg:"⪚",Egrave:"È",egrave:"è",egs:"⪖",egsdot:"⪘",el:"⪙",Element:"∈",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",Emacr:"Ē",emacr:"ē",empty:"∅",emptyset:"∅",EmptySmallSquare:"◻",emptyv:"∅",EmptyVerySmallSquare:"▫",emsp:" ",emsp13:" ",emsp14:" ",ENG:"Ŋ",eng:"ŋ",ensp:" ",Eogon:"Ę",eogon:"ę",Eopf:"𝔼",eopf:"𝕖",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",Epsilon:"Ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",Equal:"⩵",equals:"=",EqualTilde:"≂",equest:"≟",Equilibrium:"⇌",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erarr:"⥱",erDot:"≓",Escr:"ℰ",escr:"ℯ",esdot:"≐",Esim:"⩳",esim:"≂",Eta:"Η",eta:"η",ETH:"Ð",eth:"ð",Euml:"Ë",euml:"ë",euro:"€",excl:"!",exist:"∃",Exists:"∃",expectation:"ℰ",ExponentialE:"ⅇ",exponentiale:"ⅇ",fallingdotseq:"≒",Fcy:"Ф",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",Ffr:"𝔉",ffr:"𝔣",filig:"ﬁ",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",Fopf:"𝔽",fopf:"𝕗",ForAll:"∀",forall:"∀",fork:"⋔",forkv:"⫙",Fouriertrf:"ℱ",fpartint:"⨍",frac12:"½",frac13:"⅓",frac14:"¼",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"¾",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",Fscr:"ℱ",fscr:"𝒻",gacute:"ǵ",Gamma:"Γ",gamma:"γ",Gammad:"Ϝ",gammad:"ϝ",gap:"⪆",Gbreve:"Ğ",gbreve:"ğ",Gcedil:"Ģ",Gcirc:"Ĝ",gcirc:"ĝ",Gcy:"Г",gcy:"г",Gdot:"Ġ",gdot:"ġ",gE:"≧",ge:"≥",gEl:"⪌",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",Gfr:"𝔊",gfr:"𝔤",Gg:"⋙",gg:"≫",ggg:"⋙",gimel:"ℷ",GJcy:"Ѓ",gjcy:"ѓ",gl:"≷",gla:"⪥",glE:"⪒",glj:"⪤",gnap:"⪊",gnapprox:"⪊",gnE:"≩",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",Gopf:"𝔾",gopf:"𝕘",grave:"`",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"𝒢",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",GT:">",Gt:"≫",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",Hacek:"ˇ",hairsp:" ",half:"½",hamilt:"ℋ",HARDcy:"Ъ",hardcy:"ъ",hArr:"⇔",harr:"↔",harrcir:"⥈",harrw:"↭",Hat:"^",hbar:"ℏ",Hcirc:"Ĥ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",Hfr:"ℌ",hfr:"𝔥",HilbertSpace:"ℋ",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",Hopf:"ℍ",hopf:"𝕙",horbar:"―",HorizontalLine:"─",Hscr:"ℋ",hscr:"𝒽",hslash:"ℏ",Hstrok:"Ħ",hstrok:"ħ",HumpDownHump:"≎",HumpEqual:"≏",hybull:"⁃",hyphen:"‐",Iacute:"Í",iacute:"í",ic:"⁣",Icirc:"Î",icirc:"î",Icy:"И",icy:"и",Idot:"İ",IEcy:"Е",iecy:"е",iexcl:"¡",iff:"⇔",Ifr:"ℑ",ifr:"𝔦",Igrave:"Ì",igrave:"ì",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",IJlig:"Ĳ",ijlig:"ĳ",Im:"ℑ",Imacr:"Ī",imacr:"ī",image:"ℑ",ImaginaryI:"ⅈ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",Implies:"⇒",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",Int:"∬",int:"∫",intcal:"⊺",integers:"ℤ",Integral:"∫",intercal:"⊺",Intersection:"⋂",intlarhk:"⨗",intprod:"⨼",InvisibleComma:"⁣",InvisibleTimes:"⁢",IOcy:"Ё",iocy:"ё",Iogon:"Į",iogon:"į",Iopf:"𝕀",iopf:"𝕚",Iota:"Ι",iota:"ι",iprod:"⨼",iquest:"¿",Iscr:"ℐ",iscr:"𝒾",isin:"∈",isindot:"⋵",isinE:"⋹",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",Itilde:"Ĩ",itilde:"ĩ",Iukcy:"І",iukcy:"і",Iuml:"Ï",iuml:"ï",Jcirc:"Ĵ",jcirc:"ĵ",Jcy:"Й",jcy:"й",Jfr:"𝔍",jfr:"𝔧",jmath:"ȷ",Jopf:"𝕁",jopf:"𝕛",Jscr:"𝒥",jscr:"𝒿",Jsercy:"Ј",jsercy:"ј",Jukcy:"Є",jukcy:"є",Kappa:"Κ",kappa:"κ",kappav:"ϰ",Kcedil:"Ķ",kcedil:"ķ",Kcy:"К",kcy:"к",Kfr:"𝔎",kfr:"𝔨",kgreen:"ĸ",KHcy:"Х",khcy:"х",KJcy:"Ќ",kjcy:"ќ",Kopf:"𝕂",kopf:"𝕜",Kscr:"𝒦",kscr:"𝓀",lAarr:"⇚",Lacute:"Ĺ",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",Lambda:"Λ",lambda:"λ",Lang:"⟪",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",Laplacetrf:"ℒ",laquo:"«",Larr:"↞",lArr:"⇐",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",lAtail:"⤛",latail:"⤙",late:"⪭",lates:"⪭︀",lBarr:"⤎",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",Lcaron:"Ľ",lcaron:"ľ",Lcedil:"Ļ",lcedil:"ļ",lceil:"⌈",lcub:"{",Lcy:"Л",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",lE:"≦",le:"≤",LeftAngleBracket:"⟨",LeftArrow:"←",Leftarrow:"⇐",leftarrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",leftarrowtail:"↢",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",LeftRightArrow:"↔",Leftrightarrow:"⇔",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",leftthreetimes:"⋋",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",lEg:"⪋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",lessgtr:"≶",LessLess:"⪡",lesssim:"≲",LessSlantEqual:"⩽",LessTilde:"≲",lfisht:"⥼",lfloor:"⌊",Lfr:"𝔏",lfr:"𝔩",lg:"≶",lgE:"⪑",lHar:"⥢",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",LJcy:"Љ",ljcy:"љ",Ll:"⋘",ll:"≪",llarr:"⇇",llcorner:"⌞",Lleftarrow:"⇚",llhard:"⥫",lltri:"◺",Lmidot:"Ŀ",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnap:"⪉",lnapprox:"⪉",lnE:"≨",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",LongLeftArrow:"⟵",Longleftarrow:"⟸",longleftarrow:"⟵",LongLeftRightArrow:"⟷",Longleftrightarrow:"⟺",longleftrightarrow:"⟷",longmapsto:"⟼",LongRightArrow:"⟶",Longrightarrow:"⟹",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",Lopf:"𝕃",lopf:"𝕝",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",LowerLeftArrow:"↙",LowerRightArrow:"↘",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",Lscr:"ℒ",lscr:"𝓁",Lsh:"↰",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",Lstrok:"Ł",lstrok:"ł",LT:"<",Lt:"≪",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltri:"◃",ltrie:"⊴",ltrif:"◂",ltrPar:"⦖",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",macr:"¯",male:"♂",malt:"✠",maltese:"✠",Map:"⤅",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",Mcy:"М",mcy:"м",mdash:"—",mDDot:"∺",measuredangle:"∡",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"𝔐",mfr:"𝔪",mho:"℧",micro:"µ",mid:"∣",midast:"*",midcir:"⫰",middot:"·",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",MinusPlus:"∓",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",Mopf:"𝕄",mopf:"𝕞",mp:"∓",Mscr:"ℳ",mscr:"𝓂",mstpos:"∾",Mu:"Μ",mu:"μ",multimap:"⊸",mumap:"⊸",nabla:"∇",Nacute:"Ń",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbsp:" ",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",Ncaron:"Ň",ncaron:"ň",Ncedil:"Ņ",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",Ncy:"Н",ncy:"н",ndash:"–",ne:"≠",nearhk:"⤤",neArr:"⇗",nearr:"↗",nearrow:"↗",nedot:"≐̸",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",nequiv:"≢",nesear:"⤨",nesim:"≂̸",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",nexist:"∄",nexists:"∄",Nfr:"𝔑",nfr:"𝔫",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",nGg:"⋙̸",ngsim:"≵",nGt:"≫⃒",ngt:"≯",ngtr:"≯",nGtv:"≫̸",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",NJcy:"Њ",njcy:"њ",nlArr:"⇍",nlarr:"↚",nldr:"‥",nlE:"≦̸",nle:"≰",nLeftarrow:"⇍",nleftarrow:"↚",nLeftrightarrow:"⇎",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nLl:"⋘̸",nlsim:"≴",nLt:"≪⃒",nlt:"≮",nltri:"⋪",nltrie:"⋬",nLtv:"≪̸",nmid:"∤",NoBreak:"⁠",NonBreakingSpace:" ",Nopf:"ℕ",nopf:"𝕟",Not:"⫬",not:"¬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",notin:"∉",notindot:"⋵̸",notinE:"⋹̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nRightarrow:"⇏",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",Nscr:"𝒩",nscr:"𝓃",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",Ntilde:"Ñ",ntilde:"ñ",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",Nu:"Ν",nu:"ν",num:"#",numero:"№",numsp:" ",nvap:"≍⃒",nVDash:"⊯",nVdash:"⊮",nvDash:"⊭",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvHarr:"⤄",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwarhk:"⤣",nwArr:"⇖",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",Oacute:"Ó",oacute:"ó",oast:"⊛",ocir:"⊚",Ocirc:"Ô",ocirc:"ô",Ocy:"О",ocy:"о",odash:"⊝",Odblac:"Ő",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",OElig:"Œ",oelig:"œ",ofcir:"⦿",Ofr:"𝔒",ofr:"𝔬",ogon:"˛",Ograve:"Ò",ograve:"ò",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",Omacr:"Ō",omacr:"ō",Omega:"Ω",omega:"ω",Omicron:"Ο",omicron:"ο",omid:"⦶",ominus:"⊖",Oopf:"𝕆",oopf:"𝕠",opar:"⦷",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",operp:"⦹",oplus:"⊕",Or:"⩔",or:"∨",orarr:"↻",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"ª",ordm:"º",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oS:"Ⓢ",Oscr:"𝒪",oscr:"ℴ",Oslash:"Ø",oslash:"ø",osol:"⊘",Otilde:"Õ",otilde:"õ",Otimes:"⨷",otimes:"⊗",otimesas:"⨶",Ouml:"Ö",ouml:"ö",ovbar:"⌽",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",par:"∥",para:"¶",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",PartialD:"∂",Pcy:"П",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",Pfr:"𝔓",pfr:"𝔭",Phi:"Φ",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",Pi:"Π",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",PlusMinus:"±",plusmn:"±",plussim:"⨦",plustwo:"⨧",pm:"±",Poincareplane:"ℌ",pointint:"⨕",Popf:"ℙ",popf:"𝕡",pound:"£",Pr:"⪻",pr:"≺",prap:"⪷",prcue:"≼",prE:"⪳",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",Prime:"″",prime:"′",primes:"ℙ",prnap:"⪹",prnE:"⪵",prnsim:"⋨",prod:"∏",Product:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",Proportion:"∷",Proportional:"∝",propto:"∝",prsim:"≾",prurel:"⊰",Pscr:"𝒫",pscr:"𝓅",Psi:"Ψ",psi:"ψ",puncsp:" ",Qfr:"𝔔",qfr:"𝔮",qint:"⨌",Qopf:"ℚ",qopf:"𝕢",qprime:"⁗",Qscr:"𝒬",qscr:"𝓆",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",QUOT:'"',quot:'"',rAarr:"⇛",race:"∽̱",Racute:"Ŕ",racute:"ŕ",radic:"√",raemptyv:"⦳",Rang:"⟫",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"»",Rarr:"↠",rArr:"⇒",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",Rarrtl:"⤖",rarrtl:"↣",rarrw:"↝",rAtail:"⤜",ratail:"⤚",ratio:"∶",rationals:"ℚ",RBarr:"⤐",rBarr:"⤏",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",Rcaron:"Ř",rcaron:"ř",Rcedil:"Ŗ",rcedil:"ŗ",rceil:"⌉",rcub:"}",Rcy:"Р",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",Re:"ℜ",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",REG:"®",reg:"®",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",rfisht:"⥽",rfloor:"⌋",Rfr:"ℜ",rfr:"𝔯",rHar:"⥤",rhard:"⇁",rharu:"⇀",rharul:"⥬",Rho:"Ρ",rho:"ρ",rhov:"ϱ",RightAngleBracket:"⟩",RightArrow:"→",Rightarrow:"⇒",rightarrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",rightarrowtail:"↣",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",rightthreetimes:"⋌",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",Ropf:"ℝ",ropf:"𝕣",roplus:"⨮",rotimes:"⨵",RoundImplies:"⥰",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",Rrightarrow:"⇛",rsaquo:"›",Rscr:"ℛ",rscr:"𝓇",Rsh:"↱",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",RuleDelayed:"⧴",ruluhar:"⥨",rx:"℞",Sacute:"Ś",sacute:"ś",sbquo:"‚",Sc:"⪼",sc:"≻",scap:"⪸",Scaron:"Š",scaron:"š",sccue:"≽",scE:"⪴",sce:"⪰",Scedil:"Ş",scedil:"ş",Scirc:"Ŝ",scirc:"ŝ",scnap:"⪺",scnE:"⪶",scnsim:"⋩",scpolint:"⨓",scsim:"≿",Scy:"С",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",searhk:"⤥",seArr:"⇘",searr:"↘",searrow:"↘",sect:"§",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",Sfr:"𝔖",sfr:"𝔰",sfrown:"⌢",sharp:"♯",SHCHcy:"Щ",shchcy:"щ",SHcy:"Ш",shcy:"ш",ShortDownArrow:"↓",ShortLeftArrow:"←",shortmid:"∣",shortparallel:"∥",ShortRightArrow:"→",ShortUpArrow:"↑",shy:"­",Sigma:"Σ",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",SmallCircle:"∘",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",SOFTcy:"Ь",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",Sopf:"𝕊",sopf:"𝕤",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",Sqrt:"√",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",Square:"□",square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",squarf:"▪",squf:"▪",srarr:"→",Sscr:"𝒮",sscr:"𝓈",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",Star:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"¯",Sub:"⋐",sub:"⊂",subdot:"⪽",subE:"⫅",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",Subset:"⋐",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",SubsetEqual:"⊆",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",SuchThat:"∋",Sum:"∑",sum:"∑",sung:"♪",Sup:"⋑",sup:"⊃",sup1:"¹",sup2:"²",sup3:"³",supdot:"⪾",supdsub:"⫘",supE:"⫆",supe:"⊇",supedot:"⫄",Superset:"⊃",SupersetEqual:"⊇",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",Supset:"⋑",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swarhk:"⤦",swArr:"⇙",swarr:"↙",swarrow:"↙",swnwar:"⤪",szlig:"ß",Tab:"\t",target:"⌖",Tau:"Τ",tau:"τ",tbrk:"⎴",Tcaron:"Ť",tcaron:"ť",Tcedil:"Ţ",tcedil:"ţ",Tcy:"Т",tcy:"т",tdot:"⃛",telrec:"⌕",Tfr:"𝔗",tfr:"𝔱",there4:"∴",Therefore:"∴",therefore:"∴",Theta:"Θ",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",ThickSpace:"  ",thinsp:" ",ThinSpace:" ",thkap:"≈",thksim:"∼",THORN:"Þ",thorn:"þ",Tilde:"∼",tilde:"˜",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",times:"×",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",Topf:"𝕋",topf:"𝕥",topfork:"⫚",tosa:"⤩",tprime:"‴",TRADE:"™",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",TripleDot:"⃛",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",Tscr:"𝒯",tscr:"𝓉",TScy:"Ц",tscy:"ц",TSHcy:"Ћ",tshcy:"ћ",Tstrok:"Ŧ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",Uacute:"Ú",uacute:"ú",Uarr:"↟",uArr:"⇑",uarr:"↑",Uarrocir:"⥉",Ubrcy:"Ў",ubrcy:"ў",Ubreve:"Ŭ",ubreve:"ŭ",Ucirc:"Û",ucirc:"û",Ucy:"У",ucy:"у",udarr:"⇅",Udblac:"Ű",udblac:"ű",udhar:"⥮",ufisht:"⥾",Ufr:"𝔘",ufr:"𝔲",Ugrave:"Ù",ugrave:"ù",uHar:"⥣",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",Umacr:"Ū",umacr:"ū",uml:"¨",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",uogon:"ų",Uopf:"𝕌",uopf:"𝕦",UpArrow:"↑",Uparrow:"⇑",uparrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",Updownarrow:"⇕",updownarrow:"↕",UpEquilibrium:"⥮",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",upsi:"υ",upsih:"ϒ",Upsilon:"Υ",upsilon:"υ",UpTee:"⊥",UpTeeArrow:"↥",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",Uring:"Ů",uring:"ů",urtri:"◹",Uscr:"𝒰",uscr:"𝓊",utdot:"⋰",Utilde:"Ũ",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",Uuml:"Ü",uuml:"ü",uwangle:"⦧",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",vArr:"⇕",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",Vbar:"⫫",vBar:"⫨",vBarv:"⫩",Vcy:"В",vcy:"в",VDash:"⊫",Vdash:"⊩",vDash:"⊨",vdash:"⊢",Vdashl:"⫦",Vee:"⋁",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",Verbar:"‖",verbar:"|",Vert:"‖",vert:"|",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"𝔙",vfr:"𝔳",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",Vopf:"𝕍",vopf:"𝕧",vprop:"∝",vrtri:"⊳",Vscr:"𝒱",vscr:"𝓋",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",Vvdash:"⊪",vzigzag:"⦚",Wcirc:"Ŵ",wcirc:"ŵ",wedbar:"⩟",Wedge:"⋀",wedge:"∧",wedgeq:"≙",weierp:"℘",Wfr:"𝔚",wfr:"𝔴",Wopf:"𝕎",wopf:"𝕨",wp:"℘",wr:"≀",wreath:"≀",Wscr:"𝒲",wscr:"𝓌",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",Xfr:"𝔛",xfr:"𝔵",xhArr:"⟺",xharr:"⟷",Xi:"Ξ",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",Xopf:"𝕏",xopf:"𝕩",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",Xscr:"𝒳",xscr:"𝓍",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",Yacute:"Ý",yacute:"ý",YAcy:"Я",yacy:"я",Ycirc:"Ŷ",ycirc:"ŷ",Ycy:"Ы",ycy:"ы",yen:"¥",Yfr:"𝔜",yfr:"𝔶",YIcy:"Ї",yicy:"ї",Yopf:"𝕐",yopf:"𝕪",Yscr:"𝒴",yscr:"𝓎",YUcy:"Ю",yucy:"ю",Yuml:"Ÿ",yuml:"ÿ",Zacute:"Ź",zacute:"ź",Zcaron:"Ž",zcaron:"ž",Zcy:"З",zcy:"з",Zdot:"Ż",zdot:"ż",zeetrf:"ℨ",ZeroWidthSpace:"​",Zeta:"Ζ",zeta:"ζ",Zfr:"ℨ",zfr:"𝔷",ZHcy:"Ж",zhcy:"ж",zigrarr:"⇝",Zopf:"ℤ",zopf:"𝕫",Zscr:"𝒵",zscr:"𝓏",zwj:"‍",zwnj:"‌"},t.NGSP_UNICODE="",t.NAMED_ENTITIES.ngsp=t.NGSP_UNICODE}));ze(Pt);Pt.TagContentType,Pt.splitNsName,Pt.isNgContainer,Pt.isNgContent,Pt.isNgTemplate,Pt.getNsPrefix,Pt.mergeNsAndName,Pt.NAMED_ENTITIES,Pt.NGSP_UNICODE;var Dt=We((function(e,t){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0});var n,a,o=function(){function e(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=n.closedByChildren,a=n.requiredParents,o=n.implicitNamespacePrefix,s=n.contentType,l=void 0===s?Pt.TagContentType.PARSABLE_DATA:s,c=n.closedByParent,u=void 0!==c&&c,p=n.isVoid,h=void 0!==p&&p,d=n.ignoreFirstLf,f=void 0!==d&&d;r(this,e),this.closedByChildren={},this.closedByParent=!1,this.canSelfClose=!1,i&&i.length>0&&i.forEach((function(e){return t.closedByChildren[e]=!0})),this.isVoid=h,this.closedByParent=u||h,a&&a.length>0&&(this.requiredParents={},this.parentToAdd=a[0],a.forEach((function(e){return t.requiredParents[e]=!0}))),this.implicitNamespacePrefix=o||null,this.contentType=l,this.ignoreFirstLf=f}return i(e,[{key:"requireExtraParent",value:function(e){if(!this.requiredParents)return!1;if(!e)return!0;var t=e.toLowerCase();return!("template"===t||"ng-template"===e)&&1!=this.requiredParents[t]}},{key:"isClosedByChild",value:function(e){return this.isVoid||e.toLowerCase()in this.closedByChildren}}]),e}();t.HtmlTagDefinition=o,t.getHtmlTagDefinition=function(e){return a||(n=new o,a={base:new o({isVoid:!0}),meta:new o({isVoid:!0}),area:new o({isVoid:!0}),embed:new o({isVoid:!0}),link:new o({isVoid:!0}),img:new o({isVoid:!0}),input:new o({isVoid:!0}),param:new o({isVoid:!0}),hr:new o({isVoid:!0}),br:new o({isVoid:!0}),source:new o({isVoid:!0}),track:new o({isVoid:!0}),wbr:new o({isVoid:!0}),p:new o({closedByChildren:["address","article","aside","blockquote","div","dl","fieldset","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","main","nav","ol","p","pre","section","table","ul"],closedByParent:!0}),thead:new o({closedByChildren:["tbody","tfoot"]}),tbody:new o({closedByChildren:["tbody","tfoot"],closedByParent:!0}),tfoot:new o({closedByChildren:["tbody"],closedByParent:!0}),tr:new o({closedByChildren:["tr"],requiredParents:["tbody","tfoot","thead"],closedByParent:!0}),td:new o({closedByChildren:["td","th"],closedByParent:!0}),th:new o({closedByChildren:["td","th"],closedByParent:!0}),col:new o({requiredParents:["colgroup"],isVoid:!0}),svg:new o({implicitNamespacePrefix:"svg"}),math:new o({implicitNamespacePrefix:"math"}),li:new o({closedByChildren:["li"],closedByParent:!0}),dt:new o({closedByChildren:["dt","dd"]}),dd:new o({closedByChildren:["dt","dd"],closedByParent:!0}),rb:new o({closedByChildren:["rb","rt","rtc","rp"],closedByParent:!0}),rt:new o({closedByChildren:["rb","rt","rtc","rp"],closedByParent:!0}),rtc:new o({closedByChildren:["rb","rtc","rp"],closedByParent:!0}),rp:new o({closedByChildren:["rb","rt","rtc","rp"],closedByParent:!0}),optgroup:new o({closedByChildren:["optgroup"],closedByParent:!0}),option:new o({closedByChildren:["option","optgroup"],closedByParent:!0}),pre:new o({ignoreFirstLf:!0}),listing:new o({ignoreFirstLf:!0}),style:new o({contentType:Pt.TagContentType.RAW_TEXT}),script:new o({contentType:Pt.TagContentType.RAW_TEXT}),title:new o({contentType:Pt.TagContentType.ESCAPABLE_RAW_TEXT}),textarea:new o({contentType:Pt.TagContentType.ESCAPABLE_RAW_TEXT,ignoreFirstLf:!0})}),a[e]||n}}));ze(Dt);Dt.HtmlTagDefinition,Dt.getHtmlTagDefinition;var Ot=We((function(e,t){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0}),t.assertArrayOfStrings=function(e,t){if(null!=t){if(!Array.isArray(t))throw new Error("Expected '".concat(e,"' to be an array of strings."));for(var r=0;r<t.length;r+=1)if("string"!=typeof t[r])throw new Error("Expected '".concat(e,"' to be an array of strings."))}};var r=[/^\s*$/,/[<>]/,/^[{}]$/,/&(#|[a-z])/i,/^\/\//];t.assertInterpolationSymbols=function(e,t){if(!(null==t||Array.isArray(t)&&2==t.length))throw new Error("Expected '".concat(e,"' to be an array, [start, end]."));if(null!=t){var n=t[0],i=t[1];r.forEach((function(e){if(e.test(n)||e.test(i))throw new Error("['".concat(n,"', '").concat(i,"'] contains unusable interpolation symbol."))}))}}}));ze(Ot);Ot.assertArrayOfStrings,Ot.assertInterpolationSymbols;var Lt=We((function(e,t){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(t,n){r(this,e),this.start=t,this.end=n}return i(e,null,[{key:"fromArray",value:function(r){return r?(Ot.assertInterpolationSymbols("interpolation",r),new e(r[0],r[1])):t.DEFAULT_INTERPOLATION_CONFIG}}]),e}();t.InterpolationConfig=n,t.DEFAULT_INTERPOLATION_CONFIG=new n("{{","}}")}));ze(Lt);Lt.InterpolationConfig,Lt.DEFAULT_INTERPOLATION_CONFIG;var qt=We((function(e,t){function r(e){return t.$0<=e&&e<=t.$9}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0}),t.$EOF=0,t.$TAB=9,t.$LF=10,t.$VTAB=11,t.$FF=12,t.$CR=13,t.$SPACE=32,t.$BANG=33,t.$DQ=34,t.$HASH=35,t.$$=36,t.$PERCENT=37,t.$AMPERSAND=38,t.$SQ=39,t.$LPAREN=40,t.$RPAREN=41,t.$STAR=42,t.$PLUS=43,t.$COMMA=44,t.$MINUS=45,t.$PERIOD=46,t.$SLASH=47,t.$COLON=58,t.$SEMICOLON=59,t.$LT=60,t.$EQ=61,t.$GT=62,t.$QUESTION=63,t.$0=48,t.$9=57,t.$A=65,t.$E=69,t.$F=70,t.$X=88,t.$Z=90,t.$LBRACKET=91,t.$BACKSLASH=92,t.$RBRACKET=93,t.$CARET=94,t.$_=95,t.$a=97,t.$e=101,t.$f=102,t.$n=110,t.$r=114,t.$t=116,t.$u=117,t.$v=118,t.$x=120,t.$z=122,t.$LBRACE=123,t.$BAR=124,t.$RBRACE=125,t.$NBSP=160,t.$PIPE=124,t.$TILDA=126,t.$AT=64,t.$BT=96,t.isWhitespace=function(e){return e>=t.$TAB&&e<=t.$SPACE||e==t.$NBSP},t.isDigit=r,t.isAsciiLetter=function(e){return e>=t.$a&&e<=t.$z||e>=t.$A&&e<=t.$Z},t.isAsciiHexDigit=function(e){return e>=t.$a&&e<=t.$f||e>=t.$A&&e<=t.$F||r(e)}}));ze(qt);qt.$EOF,qt.$TAB,qt.$LF,qt.$VTAB,qt.$FF,qt.$CR,qt.$SPACE,qt.$BANG,qt.$DQ,qt.$HASH,qt.$$,qt.$PERCENT,qt.$AMPERSAND,qt.$SQ,qt.$LPAREN,qt.$RPAREN,qt.$STAR,qt.$PLUS,qt.$COMMA,qt.$MINUS,qt.$PERIOD,qt.$SLASH,qt.$COLON,qt.$SEMICOLON,qt.$LT,qt.$EQ,qt.$GT,qt.$QUESTION,qt.$0,qt.$9,qt.$A,qt.$E,qt.$F,qt.$X,qt.$Z,qt.$LBRACKET,qt.$BACKSLASH,qt.$RBRACKET,qt.$CARET,qt.$_,qt.$a,qt.$e,qt.$f,qt.$n,qt.$r,qt.$t,qt.$u,qt.$v,qt.$x,qt.$z,qt.$LBRACE,qt.$BAR,qt.$RBRACE,qt.$NBSP,qt.$PIPE,qt.$TILDA,qt.$AT,qt.$BT,qt.isWhitespace,qt.isDigit,qt.isAsciiLetter,qt.isAsciiHexDigit;var Rt=We((function(e,t){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(t,n,i){r(this,e),this.filePath=t,this.name=n,this.members=i}return i(e,[{key:"assertNoMembers",value:function(){if(this.members.length)throw new Error("Illegal state: symbol without members expected, but got ".concat(JSON.stringify(this),"."))}}]),e}();t.StaticSymbol=n;var a=function(){function e(){r(this,e),this.cache=new Map}return i(e,[{key:"get",value:function(e,t,r){var i=(r=r||[]).length?".".concat(r.join(".")):"",a='"'.concat(e,'".').concat(t).concat(i),o=this.cache.get(a);return o||(o=new n(e,t,r),this.cache.set(a,o)),o}}]),e}();t.StaticSymbolCache=a}));ze(Rt);Rt.StaticSymbol,Rt.StaticSymbolCache;var $t=We((function(e,n){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(n,"__esModule",{value:!0});var a=/-+([a-z0-9])/g;function o(e,t,r){var n=e.indexOf(t);return-1==n?r:[e.slice(0,n).trim(),e.slice(n+1).trim()]}function s(e,r,n){return Array.isArray(e)?r.visitArray(e,n):"object"===t(i=e)&&null!==i&&Object.getPrototypeOf(i)===p?r.visitStringMap(e,n):null==e||"string"==typeof e||"number"==typeof e||"boolean"==typeof e?r.visitPrimitive(e,n):r.visitOther(e,n);var i}n.dashCaseToCamelCase=function(e){return e.replace(a,(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return t[1].toUpperCase()}))},n.splitAtColon=function(e,t){return o(e,":",t)},n.splitAtPeriod=function(e,t){return o(e,".",t)},n.visitValue=s,n.isDefined=function(e){return null!=e},n.noUndefined=function(e){return void 0===e?null:e};var l=function(){function e(){r(this,e)}return i(e,[{key:"visitArray",value:function(e,t){var r=this;return e.map((function(e){return s(e,r,t)}))}},{key:"visitStringMap",value:function(e,t){var r=this,n={};return Object.keys(e).forEach((function(i){n[i]=s(e[i],r,t)})),n}},{key:"visitPrimitive",value:function(e,t){return e}},{key:"visitOther",value:function(e,t){return e}}]),e}();n.ValueTransformer=l,n.SyncAsync={assertSync:function(e){if(h(e))throw new Error("Illegal state: value cannot be a promise");return e},then:function(e,t){return h(e)?e.then(t):t(e)},all:function(e){return e.some(h)?Promise.all(e):e}},n.error=function(e){throw new Error("Internal Error: ".concat(e))},n.syntaxError=function(e,t){var r=Error(e);return r[c]=!0,t&&(r[u]=t),r};var c="ngSyntaxError",u="ngParseErrors";n.isSyntaxError=function(e){return e[c]},n.getParseErrors=function(e){return e[u]||[]},n.escapeRegExp=function(e){return e.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")};var p=Object.getPrototypeOf({});function h(e){return!!e&&"function"==typeof e.then}n.utf8Encode=function(e){for(var t="",r=0;r<e.length;r++){var n=e.charCodeAt(r);if(n>=55296&&n<=56319&&e.length>r+1){var i=e.charCodeAt(r+1);i>=56320&&i<=57343&&(r++,n=(n-55296<<10)+i-56320+65536)}n<=127?t+=String.fromCharCode(n):n<=2047?t+=String.fromCharCode(n>>6&31|192,63&n|128):n<=65535?t+=String.fromCharCode(n>>12|224,n>>6&63|128,63&n|128):n<=2097151&&(t+=String.fromCharCode(n>>18&7|240,n>>12&63|128,n>>6&63|128,63&n|128))}return t},n.stringify=function e(t){if("string"==typeof t)return t;if(t instanceof Array)return"["+t.map(e).join(", ")+"]";if(null==t)return""+t;if(t.overriddenName)return"".concat(t.overriddenName);if(t.name)return"".concat(t.name);var r=t.toString();if(null==r)return""+r;var n=r.indexOf("\n");return-1===n?r:r.substring(0,n)},n.resolveForwardRef=function(e){return"function"==typeof e&&e.hasOwnProperty("__forward_ref__")?e():e},n.isPromise=h;n.Version=function e(t){r(this,e),this.full=t;var n=t.split(".");this.major=n[0],this.minor=n[1],this.patch=n.slice(2).join(".")}}));ze($t);$t.dashCaseToCamelCase,$t.splitAtColon,$t.splitAtPeriod,$t.visitValue,$t.isDefined,$t.noUndefined,$t.ValueTransformer,$t.SyncAsync,$t.error,$t.syntaxError,$t.isSyntaxError,$t.getParseErrors,$t.escapeRegExp,$t.utf8Encode,$t.stringify,$t.resolveForwardRef,$t.isPromise,$t.Version;var It=We((function(e,t){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0});var n=/^(?:(?:\[([^\]]+)\])|(?:\(([^\)]+)\)))|(\@[-\w]+)$/;function a(e){return e.replace(/\W/g,"_")}t.sanitizeIdentifier=a;var o,s=0;function l(e){if(!e||!e.reference)return null;var t=e.reference;if(t instanceof Rt.StaticSymbol)return t.name;if(t.__anonymousType)return t.__anonymousType;var r=$t.stringify(t);return r.indexOf("(")>=0?(r="anonymous_".concat(s++),t.__anonymousType=r):r=a(r),r}t.identifierName=l,t.identifierModuleUrl=function(e){var t=e.reference;return t instanceof Rt.StaticSymbol?t.filePath:"./".concat($t.stringify(t))},t.viewClassName=function(e,t){return"View_".concat(l({reference:e}),"_").concat(t)},t.rendererTypeName=function(e){return"RenderType_".concat(l({reference:e}))},t.hostViewClassName=function(e){return"HostView_".concat(l({reference:e}))},t.componentFactoryName=function(e){return"".concat(l({reference:e}),"NgFactory")},function(e){e[e.Pipe=0]="Pipe",e[e.Directive=1]="Directive",e[e.NgModule=2]="NgModule",e[e.Injectable=3]="Injectable"}(o=t.CompileSummaryKind||(t.CompileSummaryKind={})),t.tokenName=function(e){return null!=e.value?a(e.value):l(e.identifier)},t.tokenReference=function(e){return null!=e.identifier?e.identifier.reference:e.value};t.CompileStylesheetMetadata=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.moduleUrl,i=t.styles,a=t.styleUrls;r(this,e),this.moduleUrl=n||null,this.styles=f(i),this.styleUrls=f(a)};var c=function(){function e(t){var n=t.encapsulation,i=t.template,a=t.templateUrl,o=t.htmlAst,s=t.styles,l=t.styleUrls,c=t.externalStylesheets,u=t.animations,p=t.ngContentSelectors,h=t.interpolation,d=t.isInline,g=t.preserveWhitespaces;if(r(this,e),this.encapsulation=n,this.template=i,this.templateUrl=a,this.htmlAst=o,this.styles=f(s),this.styleUrls=f(l),this.externalStylesheets=f(c),this.animations=u?m(u):[],this.ngContentSelectors=p||[],h&&2!=h.length)throw new Error("'interpolation' should have a start and an end symbol.");this.interpolation=h,this.isInline=d,this.preserveWhitespaces=g}return i(e,[{key:"toSummary",value:function(){return{ngContentSelectors:this.ngContentSelectors,encapsulation:this.encapsulation,styles:this.styles,animations:this.animations}}}]),e}();t.CompileTemplateMetadata=c;var u=function(){function e(t){var n=t.isHost,i=t.type,a=t.isComponent,o=t.selector,s=t.exportAs,l=t.changeDetection,c=t.inputs,u=t.outputs,p=t.hostListeners,h=t.hostProperties,d=t.hostAttributes,m=t.providers,g=t.viewProviders,v=t.queries,y=t.guards,_=t.viewQueries,T=t.entryComponents,b=t.template,S=t.componentViewType,C=t.rendererType,E=t.componentFactory;r(this,e),this.isHost=!!n,this.type=i,this.isComponent=a,this.selector=o,this.exportAs=s,this.changeDetection=l,this.inputs=c,this.outputs=u,this.hostListeners=p,this.hostProperties=h,this.hostAttributes=d,this.providers=f(m),this.viewProviders=f(g),this.queries=f(v),this.guards=y,this.viewQueries=f(_),this.entryComponents=f(T),this.template=b,this.componentViewType=S,this.rendererType=C,this.componentFactory=E}return i(e,null,[{key:"create",value:function(t){var r=t.isHost,i=t.type,a=t.isComponent,o=t.selector,s=t.exportAs,l=t.changeDetection,c=t.inputs,u=t.outputs,p=t.host,h=t.providers,d=t.viewProviders,f=t.queries,m=t.guards,g=t.viewQueries,v=t.entryComponents,y=t.template,_=t.componentViewType,T=t.rendererType,b=t.componentFactory,S={},C={},E={};null!=p&&Object.keys(p).forEach((function(e){var t=p[e],r=e.match(n);null===r?E[e]=t:null!=r[1]?C[r[1]]=t:null!=r[2]&&(S[r[2]]=t)}));var k={};null!=c&&c.forEach((function(e){var t=$t.splitAtColon(e,[e,e]);k[t[0]]=t[1]}));var A={};return null!=u&&u.forEach((function(e){var t=$t.splitAtColon(e,[e,e]);A[t[0]]=t[1]})),new e({isHost:r,type:i,isComponent:!!a,selector:o,exportAs:s,changeDetection:l,inputs:k,outputs:A,hostListeners:S,hostProperties:C,hostAttributes:E,providers:h,viewProviders:d,queries:f,guards:m,viewQueries:g,entryComponents:v,template:y,componentViewType:_,rendererType:T,componentFactory:b})}}]),i(e,[{key:"toSummary",value:function(){return{summaryKind:o.Directive,type:this.type,isComponent:this.isComponent,selector:this.selector,exportAs:this.exportAs,inputs:this.inputs,outputs:this.outputs,hostListeners:this.hostListeners,hostProperties:this.hostProperties,hostAttributes:this.hostAttributes,providers:this.providers,viewProviders:this.viewProviders,queries:this.queries,guards:this.guards,viewQueries:this.viewQueries,entryComponents:this.entryComponents,changeDetection:this.changeDetection,template:this.template&&this.template.toSummary(),componentViewType:this.componentViewType,rendererType:this.rendererType,componentFactory:this.componentFactory}}}]),e}();t.CompileDirectiveMetadata=u;var p=function(){function e(t){var n=t.type,i=t.name,a=t.pure;r(this,e),this.type=n,this.name=i,this.pure=!!a}return i(e,[{key:"toSummary",value:function(){return{summaryKind:o.Pipe,type:this.type,name:this.name,pure:this.pure}}}]),e}();t.CompilePipeMetadata=p;t.CompileShallowModuleMetadata=function e(){r(this,e)};var h=function(){function e(t){var n=t.type,i=t.providers,a=t.declaredDirectives,o=t.exportedDirectives,s=t.declaredPipes,l=t.exportedPipes,c=t.entryComponents,u=t.bootstrapComponents,p=t.importedModules,h=t.exportedModules,d=t.schemas,m=t.transitiveModule,g=t.id;r(this,e),this.type=n||null,this.declaredDirectives=f(a),this.exportedDirectives=f(o),this.declaredPipes=f(s),this.exportedPipes=f(l),this.providers=f(i),this.entryComponents=f(c),this.bootstrapComponents=f(u),this.importedModules=f(p),this.exportedModules=f(h),this.schemas=f(d),this.id=g||null,this.transitiveModule=m||null}return i(e,[{key:"toSummary",value:function(){var e=this.transitiveModule;return{summaryKind:o.NgModule,type:this.type,entryComponents:e.entryComponents,providers:e.providers,modules:e.modules,exportedDirectives:e.exportedDirectives,exportedPipes:e.exportedPipes}}}]),e}();t.CompileNgModuleMetadata=h;var d=function(){function e(){r(this,e),this.directivesSet=new Set,this.directives=[],this.exportedDirectivesSet=new Set,this.exportedDirectives=[],this.pipesSet=new Set,this.pipes=[],this.exportedPipesSet=new Set,this.exportedPipes=[],this.modulesSet=new Set,this.modules=[],this.entryComponentsSet=new Set,this.entryComponents=[],this.providers=[]}return i(e,[{key:"addProvider",value:function(e,t){this.providers.push({provider:e,module:t})}},{key:"addDirective",value:function(e){this.directivesSet.has(e.reference)||(this.directivesSet.add(e.reference),this.directives.push(e))}},{key:"addExportedDirective",value:function(e){this.exportedDirectivesSet.has(e.reference)||(this.exportedDirectivesSet.add(e.reference),this.exportedDirectives.push(e))}},{key:"addPipe",value:function(e){this.pipesSet.has(e.reference)||(this.pipesSet.add(e.reference),this.pipes.push(e))}},{key:"addExportedPipe",value:function(e){this.exportedPipesSet.has(e.reference)||(this.exportedPipesSet.add(e.reference),this.exportedPipes.push(e))}},{key:"addModule",value:function(e){this.modulesSet.has(e.reference)||(this.modulesSet.add(e.reference),this.modules.push(e))}},{key:"addEntryComponent",value:function(e){this.entryComponentsSet.has(e.componentType)||(this.entryComponentsSet.add(e.componentType),this.entryComponents.push(e))}}]),e}();function f(e){return e||[]}t.TransitiveCompileNgModuleMetadata=d;function m(e){return e.reduce((function(e,t){var r=Array.isArray(t)?m(t):t;return e.concat(r)}),[])}function g(e){return e.replace(/(\w+:\/\/[\w:-]+)?(\/+)?/,"ng:///")}t.ProviderMeta=function e(t,n){var i=n.useClass,a=n.useValue,o=n.useExisting,s=n.useFactory,l=n.deps,c=n.multi;r(this,e),this.token=t,this.useClass=i||null,this.useValue=a,this.useExisting=o,this.useFactory=s||null,this.dependencies=l||null,this.multi=!!c},t.flatten=m,t.templateSourceUrl=function(e,t,r){var n;return n=r.isInline?t.type.reference instanceof Rt.StaticSymbol?"".concat(t.type.reference.filePath,".").concat(t.type.reference.name,".html"):"".concat(l(e),"/").concat(l(t.type),".html"):r.templateUrl,t.type.reference instanceof Rt.StaticSymbol?n:g(n)},t.sharedStylesheetJitUrl=function(e,t){var r=e.moduleUrl.split(/\/\\/g),n=r[r.length-1];return g("css/".concat(t).concat(n,".ngstyle.js"))},t.ngModuleJitUrl=function(e){return g("".concat(l(e.type),"/module.ngfactory.js"))},t.templateJitUrl=function(e,t){return g("".concat(l(e),"/").concat(l(t.type),".ngfactory.js"))}}));ze(It);It.sanitizeIdentifier,It.identifierName,It.identifierModuleUrl,It.viewClassName,It.rendererTypeName,It.hostViewClassName,It.componentFactoryName,It.CompileSummaryKind,It.tokenName,It.tokenReference,It.CompileStylesheetMetadata,It.CompileTemplateMetadata,It.CompileDirectiveMetadata,It.CompilePipeMetadata,It.CompileShallowModuleMetadata,It.CompileNgModuleMetadata,It.TransitiveCompileNgModuleMetadata,It.ProviderMeta,It.flatten,It.templateSourceUrl,It.sharedStylesheetJitUrl,It.ngModuleJitUrl,It.templateJitUrl;var Mt=We((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var n=function(){function e(t,n,i,a){r(this,e),this.file=t,this.offset=n,this.line=i,this.col=a}return i(e,[{key:"toString",value:function(){return null!=this.offset?"".concat(this.file.url,"@").concat(this.line,":").concat(this.col):this.file.url}},{key:"moveBy",value:function(t){for(var r=this.file.content,n=r.length,i=this.offset,a=this.line,o=this.col;i>0&&t<0;){if(i--,t++,r.charCodeAt(i)==qt.$LF){a--;var s=r.substr(0,i-1).lastIndexOf(String.fromCharCode(qt.$LF));o=s>0?i-s:i}else o--}for(;i<n&&t>0;){var l=r.charCodeAt(i);i++,t--,l==qt.$LF?(a++,o=0):o++}return new e(this.file,i,a,o)}},{key:"getContext",value:function(e,t){var r=this.file.content,n=this.offset;if(null!=n){n>r.length-1&&(n=r.length-1);for(var i=n,a=0,o=0;a<e&&n>0&&(a++,"\n"!=r[--n]||++o!=t););for(a=0,o=0;a<e&&i<r.length-1&&(a++,"\n"!=r[++i]||++o!=t););return{before:r.substring(n,this.offset),after:r.substring(this.offset,i+1)}}return null}}]),e}();t.ParseLocation=n;var a=function e(t,n){r(this,e),this.content=t,this.url=n};t.ParseSourceFile=a;var o,s=function(){function e(t,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;r(this,e),this.start=t,this.end=n,this.details=i}return i(e,[{key:"toString",value:function(){return this.start.file.content.substring(this.start.offset,this.end.offset)}}]),e}();t.ParseSourceSpan=s,function(e){e[e.WARNING=0]="WARNING",e[e.ERROR=1]="ERROR"}(o=t.ParseErrorLevel||(t.ParseErrorLevel={}));var l=function(){function e(t,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:o.ERROR;r(this,e),this.span=t,this.msg=n,this.level=i}return i(e,[{key:"contextualMessage",value:function(){var e=this.span.start.getContext(100,3);return e?"".concat(this.msg,' ("').concat(e.before,"[").concat(o[this.level]," ->]").concat(e.after,'")'):this.msg}},{key:"toString",value:function(){var e=this.span.details?", ".concat(this.span.details):"";return"".concat(this.contextualMessage(),": ").concat(this.span.start).concat(e)}}]),e}();t.ParseError=l,t.typeSourceSpan=function(e,t){var r=It.identifierModuleUrl(t),i=null!=r?"in ".concat(e," ").concat(It.identifierName(t)," in ").concat(r):"in ".concat(e," ").concat(It.identifierName(t)),o=new a("",i);return new s(new n(o,-1,-1,-1),new n(o,-1,-1,-1))}}));ze(Mt);Mt.ParseLocation,Mt.ParseSourceFile,Mt.ParseSourceSpan,Mt.ParseErrorLevel,Mt.ParseError,Mt.typeSourceSpan;var Bt=We((function(e,t){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1;r(this,e),this.path=t,this.position=n}return i(e,[{key:"parentOf",value:function(e){return e&&this.path[this.path.indexOf(e)-1]}},{key:"childOf",value:function(e){return this.path[this.path.indexOf(e)+1]}},{key:"first",value:function(e){for(var t=this.path.length-1;t>=0;t--){var r=this.path[t];if(r instanceof e)return r}}},{key:"push",value:function(e){this.path.push(e)}},{key:"pop",value:function(){return this.path.pop()}},{key:"empty",get:function(){return!this.path||!this.path.length}},{key:"head",get:function(){return this.path[0]}},{key:"tail",get:function(){return this.path[this.path.length-1]}}]),e}();t.AstPath=n}));ze(Bt);Bt.AstPath;var Ut=We((function(e,t){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(t,n){r(this,e),this.value=t,this.sourceSpan=n}return i(e,[{key:"visit",value:function(e,t){return e.visitText(this,t)}}]),e}();t.Text=n;var s=function(){function e(t,n){r(this,e),this.value=t,this.sourceSpan=n}return i(e,[{key:"visit",value:function(e,t){return e.visitCdata(this,t)}}]),e}();t.CDATA=s;var c=function(){function e(t,n,i,a,o){r(this,e),this.switchValue=t,this.type=n,this.cases=i,this.sourceSpan=a,this.switchValueSourceSpan=o}return i(e,[{key:"visit",value:function(e,t){return e.visitExpansion(this,t)}}]),e}();t.Expansion=c;var u=function(){function e(t,n,i,a,o){r(this,e),this.value=t,this.expression=n,this.sourceSpan=i,this.valueSourceSpan=a,this.expSourceSpan=o}return i(e,[{key:"visit",value:function(e,t){return e.visitExpansionCase(this,t)}}]),e}();t.ExpansionCase=u;var p=function(){function e(t,n,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;r(this,e),this.name=t,this.value=n,this.sourceSpan=i,this.valueSpan=a,this.nameSpan=o}return i(e,[{key:"visit",value:function(e,t){return e.visitAttribute(this,t)}}]),e}();t.Attribute=p;var h=function(){function e(t,n,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:null,l=arguments.length>6&&void 0!==arguments[6]?arguments[6]:null;r(this,e),this.name=t,this.attrs=n,this.children=i,this.sourceSpan=a,this.startSourceSpan=o,this.endSourceSpan=s,this.nameSpan=l}return i(e,[{key:"visit",value:function(e,t){return e.visitElement(this,t)}}]),e}();t.Element=h;var d=function(){function e(t,n){r(this,e),this.value=t,this.sourceSpan=n}return i(e,[{key:"visit",value:function(e,t){return e.visitComment(this,t)}}]),e}();t.Comment=d;var f=function(){function e(t,n){r(this,e),this.value=t,this.sourceSpan=n}return i(e,[{key:"visit",value:function(e,t){return e.visitDocType(this,t)}}]),e}();function m(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=[],i=e.visit?function(t){return e.visit(t,r)||t.visit(e,r)}:function(t){return t.visit(e,r)};return t.forEach((function(e){var t=i(e);t&&n.push(t)})),n}t.DocType=f,t.visitAll=m;var g=function(){function e(){r(this,e)}return i(e,[{key:"visitElement",value:function(e,t){this.visitChildren(t,(function(t){t(e.attrs),t(e.children)}))}},{key:"visitAttribute",value:function(e,t){}},{key:"visitText",value:function(e,t){}},{key:"visitCdata",value:function(e,t){}},{key:"visitComment",value:function(e,t){}},{key:"visitDocType",value:function(e,t){}},{key:"visitExpansion",value:function(e,t){return this.visitChildren(t,(function(t){t(e.cases)}))}},{key:"visitExpansionCase",value:function(e,t){}},{key:"visitChildren",value:function(e,t){var r=[],n=this;return t((function(t){t&&r.push(m(n,t,e))})),[].concat.apply([],r)}}]),e}();t.RecursiveVisitor=g,t.findNode=function(e,t){var n=[];return m(new(function(e){function s(){return r(this,s),l(this,o(s).apply(this,arguments))}return a(s,e),i(s,[{key:"visit",value:function(e,r){var i=function e(t){var r=t.sourceSpan.start.offset,n=t.sourceSpan.end.offset;return t instanceof h&&(t.endSourceSpan?n=t.endSourceSpan.end.offset:t.children&&t.children.length&&(n=e(t.children[t.children.length-1]).end)),{start:r,end:n}}(e);if(!(i.start<=t&&t<i.end))return!0;n.push(e)}}]),s}(g)),e),new Bt.AstPath(n,t)}}));ze(Ut);Ut.Text,Ut.CDATA,Ut.Expansion,Ut.ExpansionCase,Ut.Attribute,Ut.Element,Ut.Comment,Ut.DocType,Ut.visitAll,Ut.RecursiveVisitor,Ut.findNode;var Ft=We((function(e,t){var n;
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0}),function(e){e[e.TAG_OPEN_START=0]="TAG_OPEN_START",e[e.TAG_OPEN_END=1]="TAG_OPEN_END",e[e.TAG_OPEN_END_VOID=2]="TAG_OPEN_END_VOID",e[e.TAG_CLOSE=3]="TAG_CLOSE",e[e.TEXT=4]="TEXT",e[e.ESCAPABLE_RAW_TEXT=5]="ESCAPABLE_RAW_TEXT",e[e.RAW_TEXT=6]="RAW_TEXT",e[e.COMMENT_START=7]="COMMENT_START",e[e.COMMENT_END=8]="COMMENT_END",e[e.CDATA_START=9]="CDATA_START",e[e.CDATA_END=10]="CDATA_END",e[e.ATTR_NAME=11]="ATTR_NAME",e[e.ATTR_VALUE=12]="ATTR_VALUE",e[e.DOC_TYPE_START=13]="DOC_TYPE_START",e[e.DOC_TYPE_END=14]="DOC_TYPE_END",e[e.EXPANSION_FORM_START=15]="EXPANSION_FORM_START",e[e.EXPANSION_CASE_VALUE=16]="EXPANSION_CASE_VALUE",e[e.EXPANSION_CASE_EXP_START=17]="EXPANSION_CASE_EXP_START",e[e.EXPANSION_CASE_EXP_END=18]="EXPANSION_CASE_EXP_END",e[e.EXPANSION_FORM_END=19]="EXPANSION_FORM_END",e[e.EOF=20]="EOF"}(n=t.TokenType||(t.TokenType={}));var s=function e(t,n,i){r(this,e),this.type=t,this.parts=n,this.sourceSpan=i};t.Token=s;var c=function(e){function t(e,n,i){var a;return r(this,t),(a=l(this,o(t).call(this,i,e))).tokenType=n,a}return a(t,e),t}(Mt.ParseError);t.TokenError=c;var u=function e(t,n){r(this,e),this.tokens=t,this.errors=n};t.TokenizeResult=u,t.tokenize=function(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:Lt.DEFAULT_INTERPOLATION_CONFIG,a=arguments.length>5&&void 0!==arguments[5]&&arguments[5],o=arguments.length>6&&void 0!==arguments[6]&&arguments[6];return new m(new Mt.ParseSourceFile(e,t),r,n,i,a,o).tokenize()};var p=/\r\n?/g;function h(e){var t=e===qt.$EOF?"EOF":String.fromCharCode(e);return'Unexpected character "'.concat(t,'"')}function d(e){return'Unknown entity "'.concat(e,'" - use the "&#<decimal>;" or  "&#x<hex>;" syntax')}var f=function e(t){r(this,e),this.error=t},m=function(){function e(t,n,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Lt.DEFAULT_INTERPOLATION_CONFIG,o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],s=arguments.length>5&&void 0!==arguments[5]&&arguments[5];r(this,e),this._file=t,this._getTagDefinition=n,this._tokenizeIcu=i,this._interpolationConfig=a,this.canSelfClose=o,this.allowHtmComponentClosingTags=s,this._peek=-1,this._nextPeek=-1,this._index=-1,this._line=0,this._column=-1,this._expansionCaseStack=[],this._inInterpolation=!1,this.tokens=[],this.errors=[],this._input=t.content,this._length=t.content.length,this._advance()}return i(e,[{key:"_processCarriageReturns",value:function(e){return e.replace(p,"\n")}},{key:"tokenize",value:function(){for(;this._peek!==qt.$EOF;){var e=this._getLocation();try{if(this._attemptCharCode(qt.$LT))if(this._attemptCharCode(qt.$BANG))this._attemptStr("[CDATA[")?this._consumeCdata(e):this._attemptStr("--")?this._consumeComment(e):this._attemptStrCaseInsensitive("doctype")?this._consumeDocType(e):this._consumeBogusComment(e);else if(this._attemptCharCode(qt.$SLASH))this._consumeTagClose(e);else{var t=this._savePosition();this._attemptCharCode(qt.$QUESTION)?(this._restorePosition(t),this._consumeBogusComment(e)):this._consumeTagOpen(e)}else this._tokenizeIcu&&this._tokenizeExpansionForm()||this._consumeText()}catch(e){if(!(e instanceof f))throw e;this.errors.push(e.error)}}return this._beginToken(n.EOF),this._endToken([]),new u(function(e){for(var t=[],r=void 0,i=0;i<e.length;i++){var a=e[i];r&&r.type==n.TEXT&&a.type==n.TEXT?(r.parts[0]+=a.parts[0],r.sourceSpan.end=a.sourceSpan.end):(r=a,t.push(r))}return t}(this.tokens),this.errors)}},{key:"_tokenizeExpansionForm",value:function(){if(T(this._input,this._index,this._interpolationConfig))return this._consumeExpansionFormStart(),!0;if(((e=this._peek)===qt.$EQ||qt.isAsciiLetter(e)||qt.isDigit(e))&&this._isInExpansionForm())return this._consumeExpansionCaseStart(),!0;var e;if(this._peek===qt.$RBRACE){if(this._isInExpansionCase())return this._consumeExpansionCaseEnd(),!0;if(this._isInExpansionForm())return this._consumeExpansionFormEnd(),!0}return!1}},{key:"_getLocation",value:function(){return new Mt.ParseLocation(this._file,this._index,this._line,this._column)}},{key:"_getSpan",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._getLocation(),t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._getLocation();return new Mt.ParseSourceSpan(e,t)}},{key:"_beginToken",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._getLocation();this._currentTokenStart=t,this._currentTokenType=e}},{key:"_endToken",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._getLocation(),r=new s(this._currentTokenType,e,new Mt.ParseSourceSpan(this._currentTokenStart,t));return this.tokens.push(r),this._currentTokenStart=null,this._currentTokenType=null,r}},{key:"_createError",value:function(e,t){this._isInExpansionForm()&&(e+=' (Do you have an unescaped "{" in your template? Use "{{ \'{\' }}") to escape it.)');var r=new c(e,this._currentTokenType,t);return this._currentTokenStart=null,this._currentTokenType=null,new f(r)}},{key:"_advance",value:function(){if(this._index>=this._length)throw this._createError(h(qt.$EOF),this._getSpan());this._peek===qt.$LF?(this._line++,this._column=0):this._peek!==qt.$LF&&this._peek!==qt.$CR&&this._column++,this._index++,this._peek=this._index>=this._length?qt.$EOF:this._input.charCodeAt(this._index),this._nextPeek=this._index+1>=this._length?qt.$EOF:this._input.charCodeAt(this._index+1)}},{key:"_attemptCharCode",value:function(e){return this._peek===e&&(this._advance(),!0)}},{key:"_attemptCharCodeCaseInsensitive",value:function(e){return t=this._peek,r=e,b(t)==b(r)&&(this._advance(),!0);var t,r}},{key:"_requireCharCode",value:function(e){var t=this._getLocation();if(!this._attemptCharCode(e))throw this._createError(h(this._peek),this._getSpan(t,t))}},{key:"_attemptStr",value:function(e){var t=e.length;if(this._index+t>this._length)return!1;for(var r=this._savePosition(),n=0;n<t;n++)if(!this._attemptCharCode(e.charCodeAt(n)))return this._restorePosition(r),!1;return!0}},{key:"_attemptStrCaseInsensitive",value:function(e){for(var t=0;t<e.length;t++)if(!this._attemptCharCodeCaseInsensitive(e.charCodeAt(t)))return!1;return!0}},{key:"_requireStr",value:function(e){var t=this._getLocation();if(!this._attemptStr(e))throw this._createError(h(this._peek),this._getSpan(t))}},{key:"_requireStrCaseInsensitive",value:function(e){var t=this._getLocation();if(!this._attemptStrCaseInsensitive(e))throw this._createError(h(this._peek),this._getSpan(t))}},{key:"_attemptCharCodeUntilFn",value:function(e){for(;!e(this._peek);)this._advance()}},{key:"_requireCharCodeUntilFn",value:function(e,t){var r=this._getLocation();if(this._attemptCharCodeUntilFn(e),this._index-r.offset<t)throw this._createError(h(this._peek),this._getSpan(r,r))}},{key:"_attemptUntilChar",value:function(e){for(;this._peek!==e;)this._advance()}},{key:"_readChar",value:function(e){if(e&&this._peek===qt.$AMPERSAND)return this._decodeEntity();var t=this._index;return this._advance(),this._input[t]}},{key:"_decodeEntity",value:function(){var e=this._getLocation();if(this._advance(),!this._attemptCharCode(qt.$HASH)){var t=this._savePosition();if(this._attemptCharCodeUntilFn(_),this._peek!=qt.$SEMICOLON)return this._restorePosition(t),"&";this._advance();var r=this._input.substring(e.offset+1,this._index-1),n=Pt.NAMED_ENTITIES[r];if(!n)throw this._createError(d(r),this._getSpan(e));return n}var i=this._attemptCharCode(qt.$x)||this._attemptCharCode(qt.$X),a=this._getLocation().offset;if(this._attemptCharCodeUntilFn(y),this._peek!=qt.$SEMICOLON)throw this._createError(h(this._peek),this._getSpan());this._advance();var o=this._input.substring(a,this._index-1);try{var s=parseInt(o,i?16:10);return String.fromCharCode(s)}catch(t){var l=this._input.substring(e.offset+1,this._index-1);throw this._createError(d(l),this._getSpan(e))}}},{key:"_consumeRawText",value:function(e,t,r){var i,a=this._getLocation();this._beginToken(e?n.ESCAPABLE_RAW_TEXT:n.RAW_TEXT,a);for(var o=[];i=this._getLocation(),!this._attemptCharCode(t)||!r();)for(this._index>i.offset&&o.push(this._input.substring(i.offset,this._index));this._peek!==t;)o.push(this._readChar(e));return this._endToken([this._processCarriageReturns(o.join(""))],i)}},{key:"_consumeComment",value:function(e){var t=this;this._beginToken(n.COMMENT_START,e),this._endToken([]);var r=this._consumeRawText(!1,qt.$MINUS,(function(){return t._attemptStr("->")}));this._beginToken(n.COMMENT_END,r.sourceSpan.end),this._endToken([])}},{key:"_consumeBogusComment",value:function(e){this._beginToken(n.COMMENT_START,e),this._endToken([]);var t=this._consumeRawText(!1,qt.$GT,(function(){return!0}));this._beginToken(n.COMMENT_END,t.sourceSpan.end),this._endToken([])}},{key:"_consumeCdata",value:function(e){var t=this;this._beginToken(n.CDATA_START,e),this._endToken([]);var r=this._consumeRawText(!1,qt.$RBRACKET,(function(){return t._attemptStr("]>")}));this._beginToken(n.CDATA_END,r.sourceSpan.end),this._endToken([])}},{key:"_consumeDocType",value:function(e){this._beginToken(n.DOC_TYPE_START,e),this._endToken([]);var t=this._consumeRawText(!1,qt.$GT,(function(){return!0}));this._beginToken(n.DOC_TYPE_END,t.sourceSpan.end),this._endToken([])}},{key:"_consumePrefixAndName",value:function(){for(var e,t,r=this._index,n=null;this._peek!==qt.$COLON&&!(((e=this._peek)<qt.$a||qt.$z<e)&&(e<qt.$A||qt.$Z<e)&&(e<qt.$0||e>qt.$9));)this._advance();return this._peek===qt.$COLON?(this._advance(),n=this._input.substring(r,this._index-1),t=this._index):t=r,this._requireCharCodeUntilFn(v,this._index===t?1:0),[n,this._input.substring(t,this._index)]}},{key:"_consumeTagOpen",value:function(e){var t,r,i=this._savePosition();try{if(!qt.isAsciiLetter(this._peek))throw this._createError(h(this._peek),this._getSpan());var a=this._index;for(this._consumeTagOpenStart(e),r=(t=this._input.substring(a,this._index)).toLowerCase(),this._attemptCharCodeUntilFn(g);this._peek!==qt.$SLASH&&this._peek!==qt.$GT;)this._consumeAttributeName(),this._attemptCharCodeUntilFn(g),this._attemptCharCode(qt.$EQ)&&(this._attemptCharCodeUntilFn(g),this._consumeAttributeValue()),this._attemptCharCodeUntilFn(g);this._consumeTagOpenEnd()}catch(t){if(t instanceof f)return this._restorePosition(i),this._beginToken(n.TEXT,e),void this._endToken(["<"]);throw t}if(!this.canSelfClose||this.tokens[this.tokens.length-1].type!==n.TAG_OPEN_END_VOID){var o=this._getTagDefinition(t).contentType;o===Pt.TagContentType.RAW_TEXT?this._consumeRawTextWithTagClose(r,!1):o===Pt.TagContentType.ESCAPABLE_RAW_TEXT&&this._consumeRawTextWithTagClose(r,!0)}}},{key:"_consumeRawTextWithTagClose",value:function(e,t){var r=this,i=this._consumeRawText(t,qt.$LT,(function(){return!!r._attemptCharCode(qt.$SLASH)&&(r._attemptCharCodeUntilFn(g),!!r._attemptStrCaseInsensitive(e)&&(r._attemptCharCodeUntilFn(g),r._attemptCharCode(qt.$GT)))}));this._beginToken(n.TAG_CLOSE,i.sourceSpan.end),this._endToken([null,e])}},{key:"_consumeTagOpenStart",value:function(e){this._beginToken(n.TAG_OPEN_START,e);var t=this._consumePrefixAndName();this._endToken(t)}},{key:"_consumeAttributeName",value:function(){this._beginToken(n.ATTR_NAME);var e=this._consumePrefixAndName();this._endToken(e)}},{key:"_consumeAttributeValue",value:function(){var e;if(this._beginToken(n.ATTR_VALUE),this._peek===qt.$SQ||this._peek===qt.$DQ){var t=this._peek;this._advance();for(var r=[];this._peek!==t;)r.push(this._readChar(!0));e=r.join(""),this._advance()}else{var i=this._index;this._requireCharCodeUntilFn(v,1),e=this._input.substring(i,this._index)}this._endToken([this._processCarriageReturns(e)])}},{key:"_consumeTagOpenEnd",value:function(){var e=this._attemptCharCode(qt.$SLASH)?n.TAG_OPEN_END_VOID:n.TAG_OPEN_END;this._beginToken(e),this._requireCharCode(qt.$GT),this._endToken([])}},{key:"_consumeTagClose",value:function(e){if(this._beginToken(n.TAG_CLOSE,e),this._attemptCharCodeUntilFn(g),this.allowHtmComponentClosingTags&&this._attemptCharCode(qt.$SLASH))this._attemptCharCodeUntilFn(g),this._requireCharCode(qt.$GT),this._endToken([]);else{var t=this._consumePrefixAndName();this._attemptCharCodeUntilFn(g),this._requireCharCode(qt.$GT),this._endToken(t)}}},{key:"_consumeExpansionFormStart",value:function(){this._beginToken(n.EXPANSION_FORM_START,this._getLocation()),this._requireCharCode(qt.$LBRACE),this._endToken([]),this._expansionCaseStack.push(n.EXPANSION_FORM_START),this._beginToken(n.RAW_TEXT,this._getLocation());var e=this._readUntil(qt.$COMMA);this._endToken([e],this._getLocation()),this._requireCharCode(qt.$COMMA),this._attemptCharCodeUntilFn(g),this._beginToken(n.RAW_TEXT,this._getLocation());var t=this._readUntil(qt.$COMMA);this._endToken([t],this._getLocation()),this._requireCharCode(qt.$COMMA),this._attemptCharCodeUntilFn(g)}},{key:"_consumeExpansionCaseStart",value:function(){this._beginToken(n.EXPANSION_CASE_VALUE,this._getLocation());var e=this._readUntil(qt.$LBRACE).trim();this._endToken([e],this._getLocation()),this._attemptCharCodeUntilFn(g),this._beginToken(n.EXPANSION_CASE_EXP_START,this._getLocation()),this._requireCharCode(qt.$LBRACE),this._endToken([],this._getLocation()),this._attemptCharCodeUntilFn(g),this._expansionCaseStack.push(n.EXPANSION_CASE_EXP_START)}},{key:"_consumeExpansionCaseEnd",value:function(){this._beginToken(n.EXPANSION_CASE_EXP_END,this._getLocation()),this._requireCharCode(qt.$RBRACE),this._endToken([],this._getLocation()),this._attemptCharCodeUntilFn(g),this._expansionCaseStack.pop()}},{key:"_consumeExpansionFormEnd",value:function(){this._beginToken(n.EXPANSION_FORM_END,this._getLocation()),this._requireCharCode(qt.$RBRACE),this._endToken([]),this._expansionCaseStack.pop()}},{key:"_consumeText",value:function(){var e=this._getLocation();this._beginToken(n.TEXT,e);var t=[];do{this._interpolationConfig&&this._attemptStr(this._interpolationConfig.start)?(t.push(this._interpolationConfig.start),this._inInterpolation=!0):this._interpolationConfig&&this._inInterpolation&&this._attemptStr(this._interpolationConfig.end)?(t.push(this._interpolationConfig.end),this._inInterpolation=!1):t.push(this._readChar(!0))}while(!this._isTextEnd());this._endToken([this._processCarriageReturns(t.join(""))])}},{key:"_isTextEnd",value:function(){if(this._peek===qt.$LT||this._peek===qt.$EOF)return!0;if(this._tokenizeIcu&&!this._inInterpolation){if(T(this._input,this._index,this._interpolationConfig))return!0;if(this._peek===qt.$RBRACE&&this._isInExpansionCase())return!0}return!1}},{key:"_savePosition",value:function(){return[this._peek,this._index,this._column,this._line,this.tokens.length]}},{key:"_readUntil",value:function(e){var t=this._index;return this._attemptUntilChar(e),this._input.substring(t,this._index)}},{key:"_restorePosition",value:function(e){this._peek=e[0],this._index=e[1],this._column=e[2],this._line=e[3];var t=e[4];t<this.tokens.length&&(this.tokens=this.tokens.slice(0,t))}},{key:"_isInExpansionCase",value:function(){return this._expansionCaseStack.length>0&&this._expansionCaseStack[this._expansionCaseStack.length-1]===n.EXPANSION_CASE_EXP_START}},{key:"_isInExpansionForm",value:function(){return this._expansionCaseStack.length>0&&this._expansionCaseStack[this._expansionCaseStack.length-1]===n.EXPANSION_FORM_START}}]),e}();function g(e){return!qt.isWhitespace(e)||e===qt.$EOF}function v(e){return qt.isWhitespace(e)||e===qt.$GT||e===qt.$SLASH||e===qt.$SQ||e===qt.$DQ||e===qt.$EQ}function y(e){return e==qt.$SEMICOLON||e==qt.$EOF||!qt.isAsciiHexDigit(e)}function _(e){return e==qt.$SEMICOLON||e==qt.$EOF||!qt.isAsciiLetter(e)}function T(e,t,r){var n=!!r&&e.indexOf(r.start,t)==t;return e.charCodeAt(t)==qt.$LBRACE&&!n}function b(e){return e>=qt.$a&&e<=qt.$z?e-qt.$a+qt.$A:e}}));ze(Ft);Ft.TokenType,Ft.Token,Ft.TokenError,Ft.TokenizeResult,Ft.tokenize;var Vt=We((function(e,t){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0});var n=function(e){function t(e,n,i){var a;return r(this,t),(a=l(this,o(t).call(this,n,i))).elementName=e,a}return a(t,e),i(t,null,[{key:"create",value:function(e,r,n){return new t(e,r,n)}}]),t}(Mt.ParseError);t.TreeError=n;var s=function e(t,n){r(this,e),this.rootNodes=t,this.errors=n};t.ParseTreeResult=s;var c=function(){function e(t){r(this,e),this.getTagDefinition=t}return i(e,[{key:"parse",value:function(e,t){var r=this,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Lt.DEFAULT_INTERPOLATION_CONFIG,a=arguments.length>4&&void 0!==arguments[4]&&arguments[4],o=arguments.length>5&&void 0!==arguments[5]&&arguments[5],l=arguments.length>6&&void 0!==arguments[6]&&arguments[6],c=l?this.getTagDefinition:function(e){return r.getTagDefinition(e.toLowerCase())},p=Ft.tokenize(e,t,c,n,i,a,o),h=new u(p.tokens,c,a,o,l).build();return new s(h.rootNodes,p.errors.concat(h.errors))}}]),e}();t.Parser=c;var u=function(){function e(t,n,i,a,o){r(this,e),this.tokens=t,this.getTagDefinition=n,this.canSelfClose=i,this.allowHtmComponentClosingTags=a,this.isTagNameCaseSensitive=o,this._index=-1,this._rootNodes=[],this._errors=[],this._elementStack=[],this._advance()}return i(e,[{key:"build",value:function(){for(;this._peek.type!==Ft.TokenType.EOF;)this._peek.type===Ft.TokenType.TAG_OPEN_START?this._consumeStartTag(this._advance()):this._peek.type===Ft.TokenType.TAG_CLOSE?this._consumeEndTag(this._advance()):this._peek.type===Ft.TokenType.CDATA_START?(this._closeVoidElement(),this._consumeCdata(this._advance())):this._peek.type===Ft.TokenType.COMMENT_START?(this._closeVoidElement(),this._consumeComment(this._advance())):this._peek.type===Ft.TokenType.TEXT||this._peek.type===Ft.TokenType.RAW_TEXT||this._peek.type===Ft.TokenType.ESCAPABLE_RAW_TEXT?(this._closeVoidElement(),this._consumeText(this._advance())):this._peek.type===Ft.TokenType.EXPANSION_FORM_START?this._consumeExpansion(this._advance()):this._peek.type===Ft.TokenType.DOC_TYPE_START?this._consumeDocType(this._advance()):this._advance();return new s(this._rootNodes,this._errors)}},{key:"_advance",value:function(){var e=this._peek;return this._index<this.tokens.length-1&&this._index++,this._peek=this.tokens[this._index],e}},{key:"_advanceIf",value:function(e){return this._peek.type===e?this._advance():null}},{key:"_consumeCdata",value:function(e){var t=this._advance(),r=this._getText(t),n=this._advanceIf(Ft.TokenType.CDATA_END);this._addToParent(new Ut.CDATA(r,new Mt.ParseSourceSpan(e.sourceSpan.start,(n||t).sourceSpan.end)))}},{key:"_consumeComment",value:function(e){var t=this._advanceIf(Ft.TokenType.RAW_TEXT),r=this._advanceIf(Ft.TokenType.COMMENT_END),n=null!=t?t.parts[0].trim():null,i=new Mt.ParseSourceSpan(e.sourceSpan.start,(r||t||e).sourceSpan.end);this._addToParent(new Ut.Comment(n,i))}},{key:"_consumeDocType",value:function(e){var t=this._advanceIf(Ft.TokenType.RAW_TEXT),r=this._advanceIf(Ft.TokenType.DOC_TYPE_END),n=null!=t?t.parts[0].trim():null,i=new Mt.ParseSourceSpan(e.sourceSpan.start,(r||t||e).sourceSpan.end);this._addToParent(new Ut.DocType(n,i))}},{key:"_consumeExpansion",value:function(e){for(var t=this._advance(),r=this._advance(),i=[];this._peek.type===Ft.TokenType.EXPANSION_CASE_VALUE;){var a=this._parseExpansionCase();if(!a)return;i.push(a)}if(this._peek.type===Ft.TokenType.EXPANSION_FORM_END){var o=new Mt.ParseSourceSpan(e.sourceSpan.start,this._peek.sourceSpan.end);this._addToParent(new Ut.Expansion(t.parts[0],r.parts[0],i,o,t.sourceSpan)),this._advance()}else this._errors.push(n.create(null,this._peek.sourceSpan,"Invalid ICU message. Missing '}'."))}},{key:"_parseExpansionCase",value:function(){var t=this._advance();if(this._peek.type!==Ft.TokenType.EXPANSION_CASE_EXP_START)return this._errors.push(n.create(null,this._peek.sourceSpan,"Invalid ICU message. Missing '{'.")),null;var r=this._advance(),i=this._collectExpansionExpTokens(r);if(!i)return null;var a=this._advance();i.push(new Ft.Token(Ft.TokenType.EOF,[],a.sourceSpan));var o=new e(i,this.getTagDefinition,this.canSelfClose,this.allowHtmComponentClosingTags,this.isTagNameCaseSensitive).build();if(o.errors.length>0)return this._errors=this._errors.concat(o.errors),null;var s=new Mt.ParseSourceSpan(t.sourceSpan.start,a.sourceSpan.end),l=new Mt.ParseSourceSpan(r.sourceSpan.start,a.sourceSpan.end);return new Ut.ExpansionCase(t.parts[0],o.rootNodes,s,t.sourceSpan,l)}},{key:"_collectExpansionExpTokens",value:function(e){for(var t=[],r=[Ft.TokenType.EXPANSION_CASE_EXP_START];;){if(this._peek.type!==Ft.TokenType.EXPANSION_FORM_START&&this._peek.type!==Ft.TokenType.EXPANSION_CASE_EXP_START||r.push(this._peek.type),this._peek.type===Ft.TokenType.EXPANSION_CASE_EXP_END){if(!p(r,Ft.TokenType.EXPANSION_CASE_EXP_START))return this._errors.push(n.create(null,e.sourceSpan,"Invalid ICU message. Missing '}'.")),null;if(r.pop(),0==r.length)return t}if(this._peek.type===Ft.TokenType.EXPANSION_FORM_END){if(!p(r,Ft.TokenType.EXPANSION_FORM_START))return this._errors.push(n.create(null,e.sourceSpan,"Invalid ICU message. Missing '}'.")),null;r.pop()}if(this._peek.type===Ft.TokenType.EOF)return this._errors.push(n.create(null,e.sourceSpan,"Invalid ICU message. Missing '}'.")),null;t.push(this._advance())}}},{key:"_getText",value:function(e){var t=e.parts[0];if(t.length>0&&"\n"==t[0]){var r=this._getParentElement();null!=r&&0==r.children.length&&this.getTagDefinition(r.name).ignoreFirstLf&&(t=t.substring(1))}return t}},{key:"_consumeText",value:function(e){var t=this._getText(e);t.length>0&&this._addToParent(new Ut.Text(t,e.sourceSpan))}},{key:"_closeVoidElement",value:function(){var e=this._getParentElement();e&&this.getTagDefinition(e.name).isVoid&&this._elementStack.pop()}},{key:"_consumeStartTag",value:function(e){for(var t=e.parts[0],r=e.parts[1],i=[];this._peek.type===Ft.TokenType.ATTR_NAME;)i.push(this._consumeAttr(this._advance()));var a=this._getElementFullName(t,r,this._getParentElement()),o=!1;if(this._peek.type===Ft.TokenType.TAG_OPEN_END_VOID){this._advance(),o=!0;var s=this.getTagDefinition(a);this.canSelfClose||s.canSelfClose||null!==Pt.getNsPrefix(a)||s.isVoid||this._errors.push(n.create(a,e.sourceSpan,'Only void and foreign elements can be self closed "'.concat(e.parts[1],'"')))}else this._peek.type===Ft.TokenType.TAG_OPEN_END&&(this._advance(),o=!1);var l=this._peek.sourceSpan.start,c=new Mt.ParseSourceSpan(e.sourceSpan.start,l),u=new Mt.ParseSourceSpan(e.sourceSpan.start.moveBy(1),e.sourceSpan.end),p=new Ut.Element(a,i,[],c,c,void 0,u);this._pushElement(p),o&&(this._popElement(a),p.endSourceSpan=c)}},{key:"_pushElement",value:function(e){var t=this._getParentElement();t&&this.getTagDefinition(t.name).isClosedByChild(e.name)&&this._elementStack.pop();var r=this.getTagDefinition(e.name),n=this._getParentElementSkippingContainers(),i=n.parent,a=n.container;if(i&&r.requireExtraParent(i.name)){var o=new Ut.Element(r.parentToAdd,[],[],e.sourceSpan,e.startSourceSpan,e.endSourceSpan);this._insertBeforeContainer(i,a,o)}this._addToParent(e),this._elementStack.push(e)}},{key:"_consumeEndTag",value:function(e){var t=this.allowHtmComponentClosingTags&&0===e.parts.length?null:this._getElementFullName(e.parts[0],e.parts[1],this._getParentElement());if(this._getParentElement()&&(this._getParentElement().endSourceSpan=e.sourceSpan),t&&this.getTagDefinition(t).isVoid)this._errors.push(n.create(t,e.sourceSpan,'Void elements do not have end tags "'.concat(e.parts[1],'"')));else if(!this._popElement(t)){var r='Unexpected closing tag "'.concat(t,'". It may happen when the tag has already been closed by another tag. For more info see https://www.w3.org/TR/html5/syntax.html#closing-elements-that-have-implied-end-tags');this._errors.push(n.create(t,e.sourceSpan,r))}}},{key:"_popElement",value:function(e){for(var t=this._elementStack.length-1;t>=0;t--){var r=this._elementStack[t];if(!e||(Pt.getNsPrefix(r.name)?r.name==e:r.name.toLowerCase()==e.toLowerCase()))return this._elementStack.splice(t,this._elementStack.length-t),!0;if(!this.getTagDefinition(r.name).closedByParent)return!1}return!1}},{key:"_consumeAttr",value:function(e){var t=Pt.mergeNsAndName(e.parts[0],e.parts[1]),r=e.sourceSpan.end,n="",i=void 0;if(this._peek.type===Ft.TokenType.ATTR_VALUE){var a=this._advance();n=a.parts[0],r=a.sourceSpan.end,i=a.sourceSpan}return new Ut.Attribute(t,n,new Mt.ParseSourceSpan(e.sourceSpan.start,r),i,e.sourceSpan)}},{key:"_getParentElement",value:function(){return this._elementStack.length>0?this._elementStack[this._elementStack.length-1]:null}},{key:"_getParentElementSkippingContainers",value:function(){for(var e=null,t=this._elementStack.length-1;t>=0;t--){if(!Pt.isNgContainer(this._elementStack[t].name))return{parent:this._elementStack[t],container:e};e=this._elementStack[t]}return{parent:null,container:e}}},{key:"_addToParent",value:function(e){var t=this._getParentElement();null!=t?t.children.push(e):this._rootNodes.push(e)}},{key:"_insertBeforeContainer",value:function(e,t,r){if(t){if(e){var n=e.children.indexOf(t);e.children[n]=r}else this._rootNodes.push(r);r.children.push(t),this._elementStack.splice(this._elementStack.indexOf(t),0,r)}else this._addToParent(r),this._elementStack.push(r)}},{key:"_getElementFullName",value:function(e,t,r){return null==e&&null==(e=this.getTagDefinition(t).implicitNamespacePrefix)&&null!=r&&(e=Pt.getNsPrefix(r.name)),Pt.mergeNsAndName(e,t)}}]),e}();function p(e,t){return e.length>0&&e[e.length-1]===t}}));ze(Vt);Vt.TreeError,Vt.ParseTreeResult,Vt.Parser;var jt=We((function(e,t){
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Object.defineProperty(t,"__esModule",{value:!0});var n=Vt;t.ParseTreeResult=n.ParseTreeResult,t.TreeError=n.TreeError;var s=function(e){function t(){return r(this,t),l(this,o(t).call(this,Dt.getHtmlTagDefinition))}return a(t,e),i(t,[{key:"parse",value:function(e,r){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Lt.DEFAULT_INTERPOLATION_CONFIG,a=arguments.length>4&&void 0!==arguments[4]&&arguments[4],s=arguments.length>5&&void 0!==arguments[5]&&arguments[5],l=arguments.length>6&&void 0!==arguments[6]&&arguments[6];return c(o(t.prototype),"parse",this).call(this,e,r,n,i,a,s,l)}}]),t}(Vt.Parser);t.HtmlParser=s}));ze(jt);jt.ParseTreeResult,jt.TreeError,jt.HtmlParser;var Gt=We((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var r=null,n=function(){return r||(r=new jt.HtmlParser),r};t.parse=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.canSelfClose,i=void 0!==r&&r,a=t.allowHtmComponentClosingTags,o=void 0!==a&&a,s=t.isTagNameCaseSensitive,l=void 0!==s&&s;return n().parse(e,"angular-html-parser",!1,void 0,i,o,l)}}));ze(Gt);Gt.parse;var Ht=_t.HTML_ELEMENT_ATTRIBUTES,Xt=_t.HTML_TAGS,zt=Tt,Wt=wt.Node,Qt=xt;function Jt(e,t){var n=t.recognizeSelfClosing,s=t.normalizeTagName,c=t.normalizeAttributeName,u=t.allowHtmComponentClosingTags,p=t.isTagNameCaseSensitive,h=Gt,d=Ut.RecursiveVisitor,f=Ut.visitAll,m=Ut.Attribute,g=Ut.CDATA,v=Ut.Comment,y=Ut.DocType,_=Ut.Element,T=Ut.Text,b=Mt.ParseSourceSpan,S=Dt.getHtmlTagDefinition,C=h.parse(e,{canSelfClose:n,allowHtmComponentClosingTags:u,isTagNameCaseSensitive:p}),E=C.rootNodes,k=C.errors;if(0!==k.length){var A=k[0],w=A.msg,N=A.span.start,x=N.line,P=N.col;throw bt(w,{start:{line:x+1,column:P+1}})}var D=function(e){var t=e.name.startsWith(":")?e.name.slice(1).split(":")[0]:null,r=e.nameSpan?e.nameSpan.toString():e.name,n=r.startsWith("".concat(t,":")),i=n?r.slice(t.length+1):r;e.name=i,e.namespace=t,e.hasExplicitNamespace=n},O=function(e,t){var r=e.toLowerCase();return t(r)?r:e};return f(new(function(e){function t(){return r(this,t),l(this,o(t).apply(this,arguments))}return a(t,e),i(t,[{key:"visit",value:function(e){!function(e){if(e instanceof m)e.type="attribute";else if(e instanceof g)e.type="cdata";else if(e instanceof v)e.type="comment";else if(e instanceof y)e.type="docType";else if(e instanceof _)e.type="element";else{if(!(e instanceof T))throw new Error("Unexpected node ".concat(JSON.stringify(e)));e.type="text"}}(e),function(e){e instanceof _?(D(e),e.attrs.forEach((function(e){D(e),e.valueSpan?(e.value=e.valueSpan.toString(),/['"]/.test(e.value[0])&&(e.value=e.value.slice(1,-1))):e.value=null}))):e instanceof v?e.value=e.sourceSpan.toString().slice("\x3c!--".length,-"--\x3e".length):e instanceof T&&(e.value=e.sourceSpan.toString())}(e),function(e){if(e instanceof _){var t=S(p?e.name:e.name.toLowerCase());e.namespace&&e.namespace!==t.implicitNamespacePrefix?e.tagDefinition=S(""):e.tagDefinition=t}}(e),function(e){if(e instanceof _&&(!s||e.namespace&&e.namespace!==e.tagDefinition.implicitNamespacePrefix||(e.name=O(e.name,(function(e){return e in Xt}))),c)){var t=Ht[e.name]||Object.create(null);e.attrs.forEach((function(r){r.namespace||(r.name=O(r.name,(function(r){return e.name in Ht&&(r in Ht["*"]||r in t)})))}))}}(e),function(e){e.sourceSpan&&e.endSourceSpan&&(e.sourceSpan=new b(e.sourceSpan.start,e.endSourceSpan.end))}(e)}}]),t}(d)),E),E}function Kt(e){return e.sourceSpan.start.offset}function Yt(e){return e.sourceSpan.end.offset}function Zt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.recognizeSelfClosing,r=void 0!==t&&t,n=e.normalizeTagName,i=void 0!==n&&n,a=e.normalizeAttributeName,o=void 0!==a&&a,s=e.allowHtmComponentClosingTags,l=void 0!==s&&s,c=e.isTagNameCaseSensitive,u=void 0!==c&&c;return{parse:function(e,t,n){return function e(t,r,n){var i=!(arguments.length>3&&void 0!==arguments[3])||arguments[3]?f(t):{frontMatter:null,content:t},a=i.frontMatter,o=i.content,s={type:"root",sourceSpan:{start:{offset:0},end:{offset:t.length}},children:Jt(o,n)};a&&s.children.unshift(a);var l=new Wt(s),c=function(i,a){var o=a.offset,s=e(t.slice(0,o).replace(/[^\r\n]/g," ")+i,r,n,!1),l=s.children[0].sourceSpan.constructor;s.sourceSpan=new l(a,s.children[s.children.length-1].sourceSpan.end);var c=s.children[0];return c.length===o?s.children.shift():(c.sourceSpan=new l(c.sourceSpan.start.moveBy(o),c.sourceSpan.end),c.value=c.value.slice(o)),s},u=function(e){return"element"===e.type&&!e.nameSpan};return l.map((function(e){if(e.children&&e.children.some(u)){var t=[],r=!0,n=!1,i=void 0;try{for(var a,o=e.children[Symbol.iterator]();!(r=(a=o.next()).done);r=!0){var s=a.value;u(s)?Array.prototype.push.apply(t,s.children):t.push(s)}}catch(e){n=!0,i=e}finally{try{r||null==o.return||o.return()}finally{if(n)throw i}}return e.clone({children:t})}if("comment"===e.type){var l=Qt(e,c);if(l)return l}return e}))}(e,n,{recognizeSelfClosing:r,normalizeTagName:i,normalizeAttributeName:o,allowHtmComponentClosingTags:l,isTagNameCaseSensitive:u})},hasPragma:zt,astFormat:"html",locStart:Kt,locEnd:Yt}}var er={parsers:{html:Zt({recognizeSelfClosing:!0,normalizeTagName:!0,normalizeAttributeName:!0,allowHtmComponentClosingTags:!0}),angular:Zt(),vue:Zt({recognizeSelfClosing:!0,isTagNameCaseSensitive:!0}),lwc:Zt()}},tr=er.parsers;e.default=er,e.parsers=tr,Object.defineProperty(e,"__esModule",{value:!0})}));
