!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).esquery=t()}(this,(function(){"use strict";function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}function t(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var r=[],n=!0,o=!1,a=void 0;try{for(var s,i=e[Symbol.iterator]();!(n=(s=i.next()).done)&&(r.push(s.value),!t||r.length!==t);n=!0);}catch(e){o=!0,a=e}finally{try{n||null==i.return||i.return()}finally{if(o)throw a}}return r}(e,t)||n(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||n(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(e,t){if(e){if("string"==typeof e)return o(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(r):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(e,t):void 0}}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;function a(e,t){return e(t={exports:{}},t.exports),t.exports}var s=a((function(e,t){!function e(t){var r,n,o,a,s,i;function l(e){var t,r,n={};for(t in e)e.hasOwnProperty(t)&&(r=e[t],n[t]="object"==typeof r&&null!==r?l(r):r);return n}function u(e,t){this.parent=e,this.key=t}function c(e,t,r,n){this.node=e,this.path=t,this.wrap=r,this.ref=n}function f(){}function p(e){return null!=e&&("object"==typeof e&&"string"==typeof e.type)}function h(e,t){return(e===r.ObjectExpression||e===r.ObjectPattern)&&"properties"===t}function d(e,t){for(var r=e.length-1;r>=0;--r)if(e[r].node===t)return!0;return!1}function y(e,t){return(new f).traverse(e,t)}function m(e,t){var r;return r=function(e,t){var r,n,o,a;for(n=e.length,o=0;n;)t(e[a=o+(r=n>>>1)])?n=r:(o=a+1,n-=r+1);return o}(t,(function(t){return t.range[0]>e.range[0]})),e.extendedRange=[e.range[0],e.range[1]],r!==t.length&&(e.extendedRange[1]=t[r].range[0]),(r-=1)>=0&&(e.extendedRange[0]=t[r].range[1]),e}return r={AssignmentExpression:"AssignmentExpression",AssignmentPattern:"AssignmentPattern",ArrayExpression:"ArrayExpression",ArrayPattern:"ArrayPattern",ArrowFunctionExpression:"ArrowFunctionExpression",AwaitExpression:"AwaitExpression",BlockStatement:"BlockStatement",BinaryExpression:"BinaryExpression",BreakStatement:"BreakStatement",CallExpression:"CallExpression",CatchClause:"CatchClause",ClassBody:"ClassBody",ClassDeclaration:"ClassDeclaration",ClassExpression:"ClassExpression",ComprehensionBlock:"ComprehensionBlock",ComprehensionExpression:"ComprehensionExpression",ConditionalExpression:"ConditionalExpression",ContinueStatement:"ContinueStatement",DebuggerStatement:"DebuggerStatement",DirectiveStatement:"DirectiveStatement",DoWhileStatement:"DoWhileStatement",EmptyStatement:"EmptyStatement",ExportAllDeclaration:"ExportAllDeclaration",ExportDefaultDeclaration:"ExportDefaultDeclaration",ExportNamedDeclaration:"ExportNamedDeclaration",ExportSpecifier:"ExportSpecifier",ExpressionStatement:"ExpressionStatement",ForStatement:"ForStatement",ForInStatement:"ForInStatement",ForOfStatement:"ForOfStatement",FunctionDeclaration:"FunctionDeclaration",FunctionExpression:"FunctionExpression",GeneratorExpression:"GeneratorExpression",Identifier:"Identifier",IfStatement:"IfStatement",ImportExpression:"ImportExpression",ImportDeclaration:"ImportDeclaration",ImportDefaultSpecifier:"ImportDefaultSpecifier",ImportNamespaceSpecifier:"ImportNamespaceSpecifier",ImportSpecifier:"ImportSpecifier",Literal:"Literal",LabeledStatement:"LabeledStatement",LogicalExpression:"LogicalExpression",MemberExpression:"MemberExpression",MetaProperty:"MetaProperty",MethodDefinition:"MethodDefinition",ModuleSpecifier:"ModuleSpecifier",NewExpression:"NewExpression",ObjectExpression:"ObjectExpression",ObjectPattern:"ObjectPattern",Program:"Program",Property:"Property",RestElement:"RestElement",ReturnStatement:"ReturnStatement",SequenceExpression:"SequenceExpression",SpreadElement:"SpreadElement",Super:"Super",SwitchStatement:"SwitchStatement",SwitchCase:"SwitchCase",TaggedTemplateExpression:"TaggedTemplateExpression",TemplateElement:"TemplateElement",TemplateLiteral:"TemplateLiteral",ThisExpression:"ThisExpression",ThrowStatement:"ThrowStatement",TryStatement:"TryStatement",UnaryExpression:"UnaryExpression",UpdateExpression:"UpdateExpression",VariableDeclaration:"VariableDeclaration",VariableDeclarator:"VariableDeclarator",WhileStatement:"WhileStatement",WithStatement:"WithStatement",YieldExpression:"YieldExpression"},o={AssignmentExpression:["left","right"],AssignmentPattern:["left","right"],ArrayExpression:["elements"],ArrayPattern:["elements"],ArrowFunctionExpression:["params","body"],AwaitExpression:["argument"],BlockStatement:["body"],BinaryExpression:["left","right"],BreakStatement:["label"],CallExpression:["callee","arguments"],CatchClause:["param","body"],ClassBody:["body"],ClassDeclaration:["id","superClass","body"],ClassExpression:["id","superClass","body"],ComprehensionBlock:["left","right"],ComprehensionExpression:["blocks","filter","body"],ConditionalExpression:["test","consequent","alternate"],ContinueStatement:["label"],DebuggerStatement:[],DirectiveStatement:[],DoWhileStatement:["body","test"],EmptyStatement:[],ExportAllDeclaration:["source"],ExportDefaultDeclaration:["declaration"],ExportNamedDeclaration:["declaration","specifiers","source"],ExportSpecifier:["exported","local"],ExpressionStatement:["expression"],ForStatement:["init","test","update","body"],ForInStatement:["left","right","body"],ForOfStatement:["left","right","body"],FunctionDeclaration:["id","params","body"],FunctionExpression:["id","params","body"],GeneratorExpression:["blocks","filter","body"],Identifier:[],IfStatement:["test","consequent","alternate"],ImportExpression:["source"],ImportDeclaration:["specifiers","source"],ImportDefaultSpecifier:["local"],ImportNamespaceSpecifier:["local"],ImportSpecifier:["imported","local"],Literal:[],LabeledStatement:["label","body"],LogicalExpression:["left","right"],MemberExpression:["object","property"],MetaProperty:["meta","property"],MethodDefinition:["key","value"],ModuleSpecifier:[],NewExpression:["callee","arguments"],ObjectExpression:["properties"],ObjectPattern:["properties"],Program:["body"],Property:["key","value"],RestElement:["argument"],ReturnStatement:["argument"],SequenceExpression:["expressions"],SpreadElement:["argument"],Super:[],SwitchStatement:["discriminant","cases"],SwitchCase:["test","consequent"],TaggedTemplateExpression:["tag","quasi"],TemplateElement:[],TemplateLiteral:["quasis","expressions"],ThisExpression:[],ThrowStatement:["argument"],TryStatement:["block","handler","finalizer"],UnaryExpression:["argument"],UpdateExpression:["argument"],VariableDeclaration:["declarations"],VariableDeclarator:["id","init"],WhileStatement:["test","body"],WithStatement:["object","body"],YieldExpression:["argument"]},n={Break:a={},Skip:s={},Remove:i={}},u.prototype.replace=function(e){this.parent[this.key]=e},u.prototype.remove=function(){return Array.isArray(this.parent)?(this.parent.splice(this.key,1),!0):(this.replace(null),!1)},f.prototype.path=function(){var e,t,r,n,o;function a(e,t){if(Array.isArray(t))for(r=0,n=t.length;r<n;++r)e.push(t[r]);else e.push(t)}if(!this.__current.path)return null;for(o=[],e=2,t=this.__leavelist.length;e<t;++e)a(o,this.__leavelist[e].path);return a(o,this.__current.path),o},f.prototype.type=function(){return this.current().type||this.__current.wrap},f.prototype.parents=function(){var e,t,r;for(r=[],e=1,t=this.__leavelist.length;e<t;++e)r.push(this.__leavelist[e].node);return r},f.prototype.current=function(){return this.__current.node},f.prototype.__execute=function(e,t){var r,n;return n=void 0,r=this.__current,this.__current=t,this.__state=null,e&&(n=e.call(this,t.node,this.__leavelist[this.__leavelist.length-1].node)),this.__current=r,n},f.prototype.notify=function(e){this.__state=e},f.prototype.skip=function(){this.notify(s)},f.prototype.break=function(){this.notify(a)},f.prototype.remove=function(){this.notify(i)},f.prototype.__initialize=function(e,t){this.visitor=t,this.root=e,this.__worklist=[],this.__leavelist=[],this.__current=null,this.__state=null,this.__fallback=null,"iteration"===t.fallback?this.__fallback=Object.keys:"function"==typeof t.fallback&&(this.__fallback=t.fallback),this.__keys=o,t.keys&&(this.__keys=Object.assign(Object.create(this.__keys),t.keys))},f.prototype.traverse=function(e,t){var r,n,o,i,l,u,f,y,m,x,g,v;for(this.__initialize(e,t),v={},r=this.__worklist,n=this.__leavelist,r.push(new c(e,null,null,null)),n.push(new c(null,null,null,null));r.length;)if((o=r.pop())!==v){if(o.node){if(u=this.__execute(t.enter,o),this.__state===a||u===a)return;if(r.push(v),n.push(o),this.__state===s||u===s)continue;if(l=(i=o.node).type||o.wrap,!(x=this.__keys[l])){if(!this.__fallback)throw new Error("Unknown node type "+l+".");x=this.__fallback(i)}for(y=x.length;(y-=1)>=0;)if(g=i[f=x[y]])if(Array.isArray(g)){for(m=g.length;(m-=1)>=0;)if(g[m]&&!d(n,g[m])){if(h(l,x[y]))o=new c(g[m],[f,m],"Property",null);else{if(!p(g[m]))continue;o=new c(g[m],[f,m],null,null)}r.push(o)}}else if(p(g)){if(d(n,g))continue;r.push(new c(g,f,null,null))}}}else if(o=n.pop(),u=this.__execute(t.leave,o),this.__state===a||u===a)return},f.prototype.replace=function(e,t){var r,n,o,l,f,d,y,m,x,g,v,A,b;function E(e){var t,n,o,a;if(e.ref.remove())for(n=e.ref.key,a=e.ref.parent,t=r.length;t--;)if((o=r[t]).ref&&o.ref.parent===a){if(o.ref.key<n)break;--o.ref.key}}for(this.__initialize(e,t),v={},r=this.__worklist,n=this.__leavelist,d=new c(e,null,null,new u(A={root:e},"root")),r.push(d),n.push(d);r.length;)if((d=r.pop())!==v){if(void 0!==(f=this.__execute(t.enter,d))&&f!==a&&f!==s&&f!==i&&(d.ref.replace(f),d.node=f),this.__state!==i&&f!==i||(E(d),d.node=null),this.__state===a||f===a)return A.root;if((o=d.node)&&(r.push(v),n.push(d),this.__state!==s&&f!==s)){if(l=o.type||d.wrap,!(x=this.__keys[l])){if(!this.__fallback)throw new Error("Unknown node type "+l+".");x=this.__fallback(o)}for(y=x.length;(y-=1)>=0;)if(g=o[b=x[y]])if(Array.isArray(g)){for(m=g.length;(m-=1)>=0;)if(g[m]){if(h(l,x[y]))d=new c(g[m],[b,m],"Property",new u(g,m));else{if(!p(g[m]))continue;d=new c(g[m],[b,m],null,new u(g,m))}r.push(d)}}else p(g)&&r.push(new c(g,b,null,new u(o,b)))}}else if(d=n.pop(),void 0!==(f=this.__execute(t.leave,d))&&f!==a&&f!==s&&f!==i&&d.ref.replace(f),this.__state!==i&&f!==i||E(d),this.__state===a||f===a)return A.root;return A.root},t.Syntax=r,t.traverse=y,t.replace=function(e,t){return(new f).replace(e,t)},t.attachComments=function(e,t,r){var o,a,s,i,u=[];if(!e.range)throw new Error("attachComments needs range information");if(!r.length){if(t.length){for(s=0,a=t.length;s<a;s+=1)(o=l(t[s])).extendedRange=[0,e.range[0]],u.push(o);e.leadingComments=u}return e}for(s=0,a=t.length;s<a;s+=1)u.push(m(l(t[s]),r));return i=0,y(e,{enter:function(e){for(var t;i<u.length&&!((t=u[i]).extendedRange[1]>e.range[0]);)t.extendedRange[1]===e.range[0]?(e.leadingComments||(e.leadingComments=[]),e.leadingComments.push(t),u.splice(i,1)):i+=1;return i===u.length?n.Break:u[i].extendedRange[0]>e.range[1]?n.Skip:void 0}}),i=0,y(e,{leave:function(e){for(var t;i<u.length&&(t=u[i],!(e.range[1]<t.extendedRange[0]));)e.range[1]===t.extendedRange[0]?(e.trailingComments||(e.trailingComments=[]),e.trailingComments.push(t),u.splice(i,1)):i+=1;return i===u.length?n.Break:u[i].extendedRange[0]>e.range[1]?n.Skip:void 0}}),e},t.VisitorKeys=o,t.VisitorOption=n,t.Controller=f,t.cloneEnvironment=function(){return e({})},t}(t)})),i=a((function(e){e.exports&&(e.exports=function(){function e(t,r,n,o){this.message=t,this.expected=r,this.found=n,this.location=o,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,e)}return function(e,t){function r(){this.constructor=e}r.prototype=t.prototype,e.prototype=new r}(e,Error),e.buildMessage=function(e,t){var r={literal:function(e){return'"'+o(e.text)+'"'},class:function(e){var t,r="";for(t=0;t<e.parts.length;t++)r+=e.parts[t]instanceof Array?a(e.parts[t][0])+"-"+a(e.parts[t][1]):a(e.parts[t]);return"["+(e.inverted?"^":"")+r+"]"},any:function(e){return"any character"},end:function(e){return"end of input"},other:function(e){return e.description}};function n(e){return e.charCodeAt(0).toString(16).toUpperCase()}function o(e){return e.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(e){return"\\x0"+n(e)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(e){return"\\x"+n(e)}))}function a(e){return e.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(e){return"\\x0"+n(e)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(e){return"\\x"+n(e)}))}return"Expected "+function(e){var t,n,o,a=new Array(e.length);for(t=0;t<e.length;t++)a[t]=(o=e[t],r[o.type](o));if(a.sort(),a.length>0){for(t=1,n=1;t<a.length;t++)a[t-1]!==a[t]&&(a[n]=a[t],n++);a.length=n}switch(a.length){case 1:return a[0];case 2:return a[0]+" or "+a[1];default:return a.slice(0,-1).join(", ")+", or "+a[a.length-1]}}(e)+" but "+function(e){return e?'"'+o(e)+'"':"end of input"}(t)+" found."},{SyntaxError:e,parse:function(t,r){r=void 0!==r?r:{};var n,o,a,s,i={},l={start:be},u=be,c=me(" ",!1),f=/^[^ [\],():#!=><~+.]/,p=xe([" ","[","]",",","(",")",":","#","!","=",">","<","~","+","."],!0,!1),h=function(e){return e.join("")},d=me(">",!1),y=me("~",!1),m=me("+",!1),x=me(",",!1),g=me("!",!1),v=me("*",!1),A=me("#",!1),b=me("[",!1),E=me("]",!1),S=/^[><!]/,_=xe([">","<","!"],!1,!1),w=me("=",!1),C=function(e){return(e||"")+"="},P=/^[><]/,k=xe([">","<"],!1,!1),D=me(".",!1),I=function(e,t,r){return{type:"attribute",name:e,operator:t,value:r}},j=me('"',!1),F=/^[^\\"]/,T=xe(["\\",'"'],!0,!1),L=me("\\",!1),R={type:"any"},O=function(e,t){return e+t},B=function(e){return{type:"literal",value:(t=e.join(""),t.replace(/\\(.)/g,(function(e,t){switch(t){case"b":return"\b";case"f":return"\f";case"n":return"\n";case"r":return"\r";case"t":return"\t";case"v":return"\v";default:return t}})))};var t},M=me("'",!1),U=/^[^\\']/,V=xe(["\\","'"],!0,!1),q=/^[0-9]/,N=xe([["0","9"]],!1,!1),W=me("type(",!1),G=/^[^ )]/,z=xe([" ",")"],!0,!1),K=me(")",!1),H=/^[imsu]/,Y=xe(["i","m","s","u"],!1,!1),$=me("/",!1),J=/^[^\/]/,Q=xe(["/"],!0,!1),X=me(":not(",!1),Z=me(":matches(",!1),ee=me(":has(",!1),te=me(":first-child",!1),re=me(":last-child",!1),ne=me(":nth-child(",!1),oe=me(":nth-last-child(",!1),ae=me(":",!1),se=me("statement",!0),ie=me("expression",!0),le=me("declaration",!0),ue=me("function",!0),ce=me("pattern",!0),fe=0,pe=[{line:1,column:1}],he=0,de=[],ye={};if("startRule"in r){if(!(r.startRule in l))throw new Error("Can't start parsing from rule \""+r.startRule+'".');u=l[r.startRule]}function me(e,t){return{type:"literal",text:e,ignoreCase:t}}function xe(e,t,r){return{type:"class",parts:e,inverted:t,ignoreCase:r}}function ge(e){var r,n=pe[e];if(n)return n;for(r=e-1;!pe[r];)r--;for(n={line:(n=pe[r]).line,column:n.column};r<e;)10===t.charCodeAt(r)?(n.line++,n.column=1):n.column++,r++;return pe[e]=n,n}function ve(e,t){var r=ge(e),n=ge(t);return{start:{offset:e,line:r.line,column:r.column},end:{offset:t,line:n.line,column:n.column}}}function Ae(e){fe<he||(fe>he&&(he=fe,de=[]),de.push(e))}function be(){var e,t,r,n,o=30*fe+0,a=ye[o];return a?(fe=a.nextPos,a.result):(e=fe,(t=Ee())!==i&&(r=we())!==i&&Ee()!==i?e=t=1===(n=r).length?n[0]:{type:"matches",selectors:n}:(fe=e,e=i),e===i&&(e=fe,(t=Ee())!==i&&(t=void 0),e=t),ye[o]={nextPos:fe,result:e},e)}function Ee(){var e,r,n=30*fe+1,o=ye[n];if(o)return fe=o.nextPos,o.result;for(e=[],32===t.charCodeAt(fe)?(r=" ",fe++):(r=i,Ae(c));r!==i;)e.push(r),32===t.charCodeAt(fe)?(r=" ",fe++):(r=i,Ae(c));return ye[n]={nextPos:fe,result:e},e}function Se(){var e,r,n,o=30*fe+2,a=ye[o];if(a)return fe=a.nextPos,a.result;if(r=[],f.test(t.charAt(fe))?(n=t.charAt(fe),fe++):(n=i,Ae(p)),n!==i)for(;n!==i;)r.push(n),f.test(t.charAt(fe))?(n=t.charAt(fe),fe++):(n=i,Ae(p));else r=i;return r!==i&&(r=h(r)),e=r,ye[o]={nextPos:fe,result:e},e}function _e(){var e,r,n,o=30*fe+3,a=ye[o];return a?(fe=a.nextPos,a.result):(e=fe,(r=Ee())!==i?(62===t.charCodeAt(fe)?(n=">",fe++):(n=i,Ae(d)),n!==i&&Ee()!==i?e=r="child":(fe=e,e=i)):(fe=e,e=i),e===i&&(e=fe,(r=Ee())!==i?(126===t.charCodeAt(fe)?(n="~",fe++):(n=i,Ae(y)),n!==i&&Ee()!==i?e=r="sibling":(fe=e,e=i)):(fe=e,e=i),e===i&&(e=fe,(r=Ee())!==i?(43===t.charCodeAt(fe)?(n="+",fe++):(n=i,Ae(m)),n!==i&&Ee()!==i?e=r="adjacent":(fe=e,e=i)):(fe=e,e=i),e===i&&(e=fe,32===t.charCodeAt(fe)?(r=" ",fe++):(r=i,Ae(c)),r!==i&&(n=Ee())!==i?e=r="descendant":(fe=e,e=i)))),ye[o]={nextPos:fe,result:e},e)}function we(){var e,r,n,o,a,s,l,u,c=30*fe+4,f=ye[c];if(f)return fe=f.nextPos,f.result;if(e=fe,(r=Ce())!==i){for(n=[],o=fe,(a=Ee())!==i?(44===t.charCodeAt(fe)?(s=",",fe++):(s=i,Ae(x)),s!==i&&(l=Ee())!==i&&(u=Ce())!==i?o=a=[a,s,l,u]:(fe=o,o=i)):(fe=o,o=i);o!==i;)n.push(o),o=fe,(a=Ee())!==i?(44===t.charCodeAt(fe)?(s=",",fe++):(s=i,Ae(x)),s!==i&&(l=Ee())!==i&&(u=Ce())!==i?o=a=[a,s,l,u]:(fe=o,o=i)):(fe=o,o=i);n!==i?e=r=[r].concat(n.map((function(e){return e[3]}))):(fe=e,e=i)}else fe=e,e=i;return ye[c]={nextPos:fe,result:e},e}function Ce(){var e,t,r,n,o,a,s,l=30*fe+5,u=ye[l];if(u)return fe=u.nextPos,u.result;if(e=fe,(t=Pe())!==i){for(r=[],n=fe,(o=_e())!==i&&(a=Pe())!==i?n=o=[o,a]:(fe=n,n=i);n!==i;)r.push(n),n=fe,(o=_e())!==i&&(a=Pe())!==i?n=o=[o,a]:(fe=n,n=i);r!==i?(s=t,e=t=r.reduce((function(e,t){return{type:t[0],left:e,right:t[1]}}),s)):(fe=e,e=i)}else fe=e,e=i;return ye[l]={nextPos:fe,result:e},e}function Pe(){var e,r,n,o,a,s,l,u=30*fe+6,c=ye[u];if(c)return fe=c.nextPos,c.result;if(e=fe,33===t.charCodeAt(fe)?(r="!",fe++):(r=i,Ae(g)),r===i&&(r=null),r!==i){if(n=[],(o=ke())!==i)for(;o!==i;)n.push(o),o=ke();else n=i;n!==i?(a=r,l=1===(s=n).length?s[0]:{type:"compound",selectors:s},a&&(l.subject=!0),e=r=l):(fe=e,e=i)}else fe=e,e=i;return ye[u]={nextPos:fe,result:e},e}function ke(){var e,r=30*fe+7,n=ye[r];return n?(fe=n.nextPos,n.result):((e=function(){var e,r,n=30*fe+8,o=ye[n];return o?(fe=o.nextPos,o.result):(42===t.charCodeAt(fe)?(r="*",fe++):(r=i,Ae(v)),r!==i&&(r={type:"wildcard",value:r}),e=r,ye[n]={nextPos:fe,result:e},e)}())===i&&(e=function(){var e,r,n,o=30*fe+9,a=ye[o];return a?(fe=a.nextPos,a.result):(e=fe,35===t.charCodeAt(fe)?(r="#",fe++):(r=i,Ae(A)),r===i&&(r=null),r!==i&&(n=Se())!==i?e=r={type:"identifier",value:n}:(fe=e,e=i),ye[o]={nextPos:fe,result:e},e)}())===i&&(e=function(){var e,r,n,o,a=30*fe+10,s=ye[a];return s?(fe=s.nextPos,s.result):(e=fe,91===t.charCodeAt(fe)?(r="[",fe++):(r=i,Ae(b)),r!==i&&Ee()!==i&&(n=function(){var e,r,n,o,a=30*fe+14,s=ye[a];return s?(fe=s.nextPos,s.result):(e=fe,(r=De())!==i&&Ee()!==i&&(n=function(){var e,r,n,o=30*fe+12,a=ye[o];return a?(fe=a.nextPos,a.result):(e=fe,33===t.charCodeAt(fe)?(r="!",fe++):(r=i,Ae(g)),r===i&&(r=null),r!==i?(61===t.charCodeAt(fe)?(n="=",fe++):(n=i,Ae(w)),n!==i?(r=C(r),e=r):(fe=e,e=i)):(fe=e,e=i),ye[o]={nextPos:fe,result:e},e)}())!==i&&Ee()!==i?((o=function(){var e,r,n,o,a,s=30*fe+18,l=ye[s];if(l)return fe=l.nextPos,l.result;if(e=fe,"type("===t.substr(fe,5)?(r="type(",fe+=5):(r=i,Ae(W)),r!==i)if(Ee()!==i){if(n=[],G.test(t.charAt(fe))?(o=t.charAt(fe),fe++):(o=i,Ae(z)),o!==i)for(;o!==i;)n.push(o),G.test(t.charAt(fe))?(o=t.charAt(fe),fe++):(o=i,Ae(z));else n=i;n!==i&&(o=Ee())!==i?(41===t.charCodeAt(fe)?(a=")",fe++):(a=i,Ae(K)),a!==i?(r={type:"type",value:n.join("")},e=r):(fe=e,e=i)):(fe=e,e=i)}else fe=e,e=i;else fe=e,e=i;return ye[s]={nextPos:fe,result:e},e}())===i&&(o=function(){var e,r,n,o,a,s,l=30*fe+20,u=ye[l];if(u)return fe=u.nextPos,u.result;if(e=fe,47===t.charCodeAt(fe)?(r="/",fe++):(r=i,Ae($)),r!==i){if(n=[],J.test(t.charAt(fe))?(o=t.charAt(fe),fe++):(o=i,Ae(Q)),o!==i)for(;o!==i;)n.push(o),J.test(t.charAt(fe))?(o=t.charAt(fe),fe++):(o=i,Ae(Q));else n=i;n!==i?(47===t.charCodeAt(fe)?(o="/",fe++):(o=i,Ae($)),o!==i?((a=function(){var e,r,n=30*fe+19,o=ye[n];if(o)return fe=o.nextPos,o.result;if(e=[],H.test(t.charAt(fe))?(r=t.charAt(fe),fe++):(r=i,Ae(Y)),r!==i)for(;r!==i;)e.push(r),H.test(t.charAt(fe))?(r=t.charAt(fe),fe++):(r=i,Ae(Y));else e=i;return ye[n]={nextPos:fe,result:e},e}())===i&&(a=null),a!==i?(s=a,r={type:"regexp",value:new RegExp(n.join(""),s?s.join(""):"")},e=r):(fe=e,e=i)):(fe=e,e=i)):(fe=e,e=i)}else fe=e,e=i;return ye[l]={nextPos:fe,result:e},e}()),o!==i?(r=I(r,n,o),e=r):(fe=e,e=i)):(fe=e,e=i),e===i&&(e=fe,(r=De())!==i&&Ee()!==i&&(n=function(){var e,r,n,o=30*fe+11,a=ye[o];return a?(fe=a.nextPos,a.result):(e=fe,S.test(t.charAt(fe))?(r=t.charAt(fe),fe++):(r=i,Ae(_)),r===i&&(r=null),r!==i?(61===t.charCodeAt(fe)?(n="=",fe++):(n=i,Ae(w)),n!==i?(r=C(r),e=r):(fe=e,e=i)):(fe=e,e=i),e===i&&(P.test(t.charAt(fe))?(e=t.charAt(fe),fe++):(e=i,Ae(k))),ye[o]={nextPos:fe,result:e},e)}())!==i&&Ee()!==i?((o=function(){var e,r,n,o,a,s,l=30*fe+15,u=ye[l];if(u)return fe=u.nextPos,u.result;if(e=fe,34===t.charCodeAt(fe)?(r='"',fe++):(r=i,Ae(j)),r!==i){for(n=[],F.test(t.charAt(fe))?(o=t.charAt(fe),fe++):(o=i,Ae(T)),o===i&&(o=fe,92===t.charCodeAt(fe)?(a="\\",fe++):(a=i,Ae(L)),a!==i?(t.length>fe?(s=t.charAt(fe),fe++):(s=i,Ae(R)),s!==i?(a=O(a,s),o=a):(fe=o,o=i)):(fe=o,o=i));o!==i;)n.push(o),F.test(t.charAt(fe))?(o=t.charAt(fe),fe++):(o=i,Ae(T)),o===i&&(o=fe,92===t.charCodeAt(fe)?(a="\\",fe++):(a=i,Ae(L)),a!==i?(t.length>fe?(s=t.charAt(fe),fe++):(s=i,Ae(R)),s!==i?(a=O(a,s),o=a):(fe=o,o=i)):(fe=o,o=i));n!==i?(34===t.charCodeAt(fe)?(o='"',fe++):(o=i,Ae(j)),o!==i?(r=B(n),e=r):(fe=e,e=i)):(fe=e,e=i)}else fe=e,e=i;if(e===i)if(e=fe,39===t.charCodeAt(fe)?(r="'",fe++):(r=i,Ae(M)),r!==i){for(n=[],U.test(t.charAt(fe))?(o=t.charAt(fe),fe++):(o=i,Ae(V)),o===i&&(o=fe,92===t.charCodeAt(fe)?(a="\\",fe++):(a=i,Ae(L)),a!==i?(t.length>fe?(s=t.charAt(fe),fe++):(s=i,Ae(R)),s!==i?(a=O(a,s),o=a):(fe=o,o=i)):(fe=o,o=i));o!==i;)n.push(o),U.test(t.charAt(fe))?(o=t.charAt(fe),fe++):(o=i,Ae(V)),o===i&&(o=fe,92===t.charCodeAt(fe)?(a="\\",fe++):(a=i,Ae(L)),a!==i?(t.length>fe?(s=t.charAt(fe),fe++):(s=i,Ae(R)),s!==i?(a=O(a,s),o=a):(fe=o,o=i)):(fe=o,o=i));n!==i?(39===t.charCodeAt(fe)?(o="'",fe++):(o=i,Ae(M)),o!==i?(r=B(n),e=r):(fe=e,e=i)):(fe=e,e=i)}else fe=e,e=i;return ye[l]={nextPos:fe,result:e},e}())===i&&(o=function(){var e,r,n,o,a,s,l,u=30*fe+16,c=ye[u];if(c)return fe=c.nextPos,c.result;for(e=fe,r=fe,n=[],q.test(t.charAt(fe))?(o=t.charAt(fe),fe++):(o=i,Ae(N));o!==i;)n.push(o),q.test(t.charAt(fe))?(o=t.charAt(fe),fe++):(o=i,Ae(N));if(n!==i?(46===t.charCodeAt(fe)?(o=".",fe++):(o=i,Ae(D)),o!==i?r=n=[n,o]:(fe=r,r=i)):(fe=r,r=i),r===i&&(r=null),r!==i){if(n=[],q.test(t.charAt(fe))?(o=t.charAt(fe),fe++):(o=i,Ae(N)),o!==i)for(;o!==i;)n.push(o),q.test(t.charAt(fe))?(o=t.charAt(fe),fe++):(o=i,Ae(N));else n=i;n!==i?(s=n,l=(a=r)?[].concat.apply([],a).join(""):"",r={type:"literal",value:parseFloat(l+s.join(""))},e=r):(fe=e,e=i)}else fe=e,e=i;return ye[u]={nextPos:fe,result:e},e}())===i&&(o=function(){var e,t,r=30*fe+17,n=ye[r];return n?(fe=n.nextPos,n.result):((t=Se())!==i&&(t={type:"literal",value:t}),e=t,ye[r]={nextPos:fe,result:e},e)}()),o!==i?(r=I(r,n,o),e=r):(fe=e,e=i)):(fe=e,e=i),e===i&&(e=fe,(r=De())!==i&&(r={type:"attribute",name:r}),e=r)),ye[a]={nextPos:fe,result:e},e)}())!==i&&Ee()!==i?(93===t.charCodeAt(fe)?(o="]",fe++):(o=i,Ae(E)),o!==i?e=r=n:(fe=e,e=i)):(fe=e,e=i),ye[a]={nextPos:fe,result:e},e)}())===i&&(e=function(){var e,r,n,o,a,s,l,u,c=30*fe+21,f=ye[c];if(f)return fe=f.nextPos,f.result;if(e=fe,46===t.charCodeAt(fe)?(r=".",fe++):(r=i,Ae(D)),r!==i)if((n=Se())!==i){for(o=[],a=fe,46===t.charCodeAt(fe)?(s=".",fe++):(s=i,Ae(D)),s!==i&&(l=Se())!==i?a=s=[s,l]:(fe=a,a=i);a!==i;)o.push(a),a=fe,46===t.charCodeAt(fe)?(s=".",fe++):(s=i,Ae(D)),s!==i&&(l=Se())!==i?a=s=[s,l]:(fe=a,a=i);o!==i?(u=n,r={type:"field",name:o.reduce((function(e,t){return e+t[0]+t[1]}),u)},e=r):(fe=e,e=i)}else fe=e,e=i;else fe=e,e=i;return ye[c]={nextPos:fe,result:e},e}())===i&&(e=function(){var e,r,n,o,a=30*fe+22,s=ye[a];return s?(fe=s.nextPos,s.result):(e=fe,":not("===t.substr(fe,5)?(r=":not(",fe+=5):(r=i,Ae(X)),r!==i&&Ee()!==i&&(n=we())!==i&&Ee()!==i?(41===t.charCodeAt(fe)?(o=")",fe++):(o=i,Ae(K)),o!==i?e=r={type:"not",selectors:n}:(fe=e,e=i)):(fe=e,e=i),ye[a]={nextPos:fe,result:e},e)}())===i&&(e=function(){var e,r,n,o,a=30*fe+23,s=ye[a];return s?(fe=s.nextPos,s.result):(e=fe,":matches("===t.substr(fe,9)?(r=":matches(",fe+=9):(r=i,Ae(Z)),r!==i&&Ee()!==i&&(n=we())!==i&&Ee()!==i?(41===t.charCodeAt(fe)?(o=")",fe++):(o=i,Ae(K)),o!==i?e=r={type:"matches",selectors:n}:(fe=e,e=i)):(fe=e,e=i),ye[a]={nextPos:fe,result:e},e)}())===i&&(e=function(){var e,r,n,o,a=30*fe+24,s=ye[a];return s?(fe=s.nextPos,s.result):(e=fe,":has("===t.substr(fe,5)?(r=":has(",fe+=5):(r=i,Ae(ee)),r!==i&&Ee()!==i&&(n=we())!==i&&Ee()!==i?(41===t.charCodeAt(fe)?(o=")",fe++):(o=i,Ae(K)),o!==i?e=r={type:"has",selectors:n}:(fe=e,e=i)):(fe=e,e=i),ye[a]={nextPos:fe,result:e},e)}())===i&&(e=function(){var e,r,n=30*fe+25,o=ye[n];return o?(fe=o.nextPos,o.result):(":first-child"===t.substr(fe,12)?(r=":first-child",fe+=12):(r=i,Ae(te)),r!==i&&(r=Ie(1)),e=r,ye[n]={nextPos:fe,result:e},e)}())===i&&(e=function(){var e,r,n=30*fe+26,o=ye[n];return o?(fe=o.nextPos,o.result):(":last-child"===t.substr(fe,11)?(r=":last-child",fe+=11):(r=i,Ae(re)),r!==i&&(r=je(1)),e=r,ye[n]={nextPos:fe,result:e},e)}())===i&&(e=function(){var e,r,n,o,a,s=30*fe+27,l=ye[s];if(l)return fe=l.nextPos,l.result;if(e=fe,":nth-child("===t.substr(fe,11)?(r=":nth-child(",fe+=11):(r=i,Ae(ne)),r!==i)if(Ee()!==i){if(n=[],q.test(t.charAt(fe))?(o=t.charAt(fe),fe++):(o=i,Ae(N)),o!==i)for(;o!==i;)n.push(o),q.test(t.charAt(fe))?(o=t.charAt(fe),fe++):(o=i,Ae(N));else n=i;n!==i&&(o=Ee())!==i?(41===t.charCodeAt(fe)?(a=")",fe++):(a=i,Ae(K)),a!==i?(r=Ie(parseInt(n.join(""),10)),e=r):(fe=e,e=i)):(fe=e,e=i)}else fe=e,e=i;else fe=e,e=i;return ye[s]={nextPos:fe,result:e},e}())===i&&(e=function(){var e,r,n,o,a,s=30*fe+28,l=ye[s];if(l)return fe=l.nextPos,l.result;if(e=fe,":nth-last-child("===t.substr(fe,16)?(r=":nth-last-child(",fe+=16):(r=i,Ae(oe)),r!==i)if(Ee()!==i){if(n=[],q.test(t.charAt(fe))?(o=t.charAt(fe),fe++):(o=i,Ae(N)),o!==i)for(;o!==i;)n.push(o),q.test(t.charAt(fe))?(o=t.charAt(fe),fe++):(o=i,Ae(N));else n=i;n!==i&&(o=Ee())!==i?(41===t.charCodeAt(fe)?(a=")",fe++):(a=i,Ae(K)),a!==i?(r=je(parseInt(n.join(""),10)),e=r):(fe=e,e=i)):(fe=e,e=i)}else fe=e,e=i;else fe=e,e=i;return ye[s]={nextPos:fe,result:e},e}())===i&&(e=function(){var e,r,n,o=30*fe+29,a=ye[o];return a?(fe=a.nextPos,a.result):(e=fe,58===t.charCodeAt(fe)?(r=":",fe++):(r=i,Ae(ae)),r!==i?("statement"===t.substr(fe,9).toLowerCase()?(n=t.substr(fe,9),fe+=9):(n=i,Ae(se)),n===i&&("expression"===t.substr(fe,10).toLowerCase()?(n=t.substr(fe,10),fe+=10):(n=i,Ae(ie)),n===i&&("declaration"===t.substr(fe,11).toLowerCase()?(n=t.substr(fe,11),fe+=11):(n=i,Ae(le)),n===i&&("function"===t.substr(fe,8).toLowerCase()?(n=t.substr(fe,8),fe+=8):(n=i,Ae(ue)),n===i&&("pattern"===t.substr(fe,7).toLowerCase()?(n=t.substr(fe,7),fe+=7):(n=i,Ae(ce)))))),n!==i?e=r={type:"class",name:n}:(fe=e,e=i)):(fe=e,e=i),ye[o]={nextPos:fe,result:e},e)}()),ye[r]={nextPos:fe,result:e},e)}function De(){var e,r,n,o=30*fe+13,a=ye[o];if(a)return fe=a.nextPos,a.result;if(r=[],(n=Se())===i&&(46===t.charCodeAt(fe)?(n=".",fe++):(n=i,Ae(D))),n!==i)for(;n!==i;)r.push(n),(n=Se())===i&&(46===t.charCodeAt(fe)?(n=".",fe++):(n=i,Ae(D)));else r=i;return r!==i&&(r=h(r)),e=r,ye[o]={nextPos:fe,result:e},e}function Ie(e){return{type:"nth-child",index:{type:"literal",value:e}}}function je(e){return{type:"nth-last-child",index:{type:"literal",value:e}}}if((n=u())!==i&&fe===t.length)return n;throw n!==i&&fe<t.length&&Ae({type:"end"}),o=de,a=he<t.length?t.charAt(he):null,s=he<t.length?ve(he,he+1):ve(he,he),new e(e.buildMessage(o,a),o,a,s)}}}())}));function l(t,r,n){if(!r)return!0;if(!t)return!1;switch(n||(n=[]),r.type){case"wildcard":return!0;case"identifier":return r.value.toLowerCase()===t.type.toLowerCase();case"field":var o=r.name.split("."),a=n[o.length-1];return function e(t,r,n){if(0===n.length)return t===r;if(null==r)return!1;var o=r[n[0]],a=n.slice(1);if(Array.isArray(o)){for(var s=0,i=o.length;s<i;++s)if(e(t,o[s],a))return!0;return!1}return e(t,o,a)}(t,a,o);case"matches":for(var i=0,p=r.selectors.length;i<p;++i)if(l(t,r.selectors[i],n))return!0;return!1;case"compound":for(var h=0,d=r.selectors.length;h<d;++h)if(!l(t,r.selectors[h],n))return!1;return!0;case"not":for(var y=0,m=r.selectors.length;y<m;++y)if(l(t,r.selectors[y],n))return!1;return!0;case"has":var x=function(){for(var e=[],n=function(n,o){var a=[];s.traverse(t,{enter:function(t,o){null!=o&&a.unshift(o),l(t,r.selectors[n],a)&&e.push(t)},leave:function(){a.shift()},fallback:"iteration"})},o=0,a=r.selectors.length;o<a;++o)n(o);return{v:0!==e.length}}();if("object"===e(x))return x.v;case"child":return!!l(t,r.right,n)&&l(n[0],r.left,n.slice(1));case"descendant":if(l(t,r.right,n))for(var g=0,v=n.length;g<v;++g)if(l(n[g],r.left,n.slice(g+1)))return!0;return!1;case"attribute":var A=function(e,t){for(var r=t.split("."),n=0;n<r.length;n++){if(null==e)return e;e=e[r[n]]}return e}(t,r.name);switch(r.operator){case void 0:return null!=A;case"=":switch(r.value.type){case"regexp":return"string"==typeof A&&r.value.value.test(A);case"literal":return"".concat(r.value.value)==="".concat(A);case"type":return r.value.value===e(A)}throw new Error("Unknown selector value type: ".concat(r.value.type));case"!=":switch(r.value.type){case"regexp":return!r.value.value.test(A);case"literal":return"".concat(r.value.value)!=="".concat(A);case"type":return r.value.value!==e(A)}throw new Error("Unknown selector value type: ".concat(r.value.type));case"<=":return A<=r.value.value;case"<":return A<r.value.value;case">":return A>r.value.value;case">=":return A>=r.value.value}throw new Error("Unknown operator: ".concat(r.operator));case"sibling":return l(t,r.right,n)&&u(t,r.left,n,"LEFT_SIDE")||r.left.subject&&l(t,r.left,n)&&u(t,r.right,n,"RIGHT_SIDE");case"adjacent":return l(t,r.right,n)&&c(t,r.left,n,"LEFT_SIDE")||r.right.subject&&l(t,r.left,n)&&c(t,r.right,n,"RIGHT_SIDE");case"nth-child":return l(t,r.right,n)&&f(t,n,(function(){return r.index.value-1}));case"nth-last-child":return l(t,r.right,n)&&f(t,n,(function(e){return e-r.index.value}));case"class":switch(r.name.toLowerCase()){case"statement":if("Statement"===t.type.slice(-9))return!0;case"declaration":return"Declaration"===t.type.slice(-11);case"pattern":if("Pattern"===t.type.slice(-7))return!0;case"expression":return"Expression"===t.type.slice(-10)||"Literal"===t.type.slice(-7)||"Identifier"===t.type&&(0===n.length||"MetaProperty"!==n[0].type)||"MetaProperty"===t.type;case"function":return"FunctionDeclaration"===t.type||"FunctionExpression"===t.type||"ArrowFunctionExpression"===t.type}throw new Error("Unknown class name: ".concat(r.name))}throw new Error("Unknown selector type: ".concat(r.type))}function u(e,r,n,o){var a=t(n,1)[0];if(!a)return!1;for(var i=s.VisitorKeys[a.type],u=0,c=i.length;u<c;++u){var f=a[i[u]];if(Array.isArray(f)){var p=f.indexOf(e);if(p<0)continue;var h=void 0,d=void 0;"LEFT_SIDE"===o?(h=0,d=p):(h=p+1,d=f.length);for(var y=h;y<d;++y)if(l(f[y],r,n))return!0}}return!1}function c(e,r,n,o){var a=t(n,1)[0];if(!a)return!1;for(var i=s.VisitorKeys[a.type],u=0,c=i.length;u<c;++u){var f=a[i[u]];if(Array.isArray(f)){var p=f.indexOf(e);if(p<0)continue;if("LEFT_SIDE"===o&&p>0&&l(f[p-1],r,n))return!0;if("RIGHT_SIDE"===o&&p<f.length-1&&l(f[p+1],r,n))return!0}}return!1}function f(e,r,n){var o=t(r,1)[0];if(!o)return!1;for(var a=s.VisitorKeys[o.type],i=0,l=a.length;i<l;++i){var u=o[a[i]];if(Array.isArray(u)){var c=u.indexOf(e);if(c>=0&&c===n(u.length))return!0}}return!1}function p(n,o){if(null==n||"object"!=e(n))return[];null==o&&(o=n);for(var a=n.subject?[o]:[],s=0,i=function(e){for(var t=[],r=Object.keys(e),n=0;n<r.length;n++)t.push([r[n],e[r[n]]]);return t}(n);s<i.length;s++){var l=t(i[s],2),u=l[0],c=l[1];a.push.apply(a,r(p(c,"left"===u?c:o)))}return a}function h(e,t,r){if(t){var n=[],o=p(t);s.traverse(e,{enter:function(e,a){if(null!=a&&n.unshift(a),l(e,t,n))if(o.length)for(var s=0,i=o.length;s<i;++s){l(e,o[s],n)&&r(e,a,n);for(var u=0,c=n.length;u<c;++u){var f=n.slice(u+1);l(n[u],o[s],f)&&r(n[u],a,f)}}else r(e,a,n)},leave:function(){n.shift()},fallback:"iteration"})}}function d(e,t){var r=[];return h(e,t,(function(e){r.push(e)})),r}function y(e){return i.parse(e)}function m(e,t){return d(e,y(t))}return m.parse=y,m.match=d,m.traverse=h,m.matches=l,m.query=m,m}));
//# sourceMappingURL=esquery.min.js.map
