// Generated by CoffeeScript 1.9.3
var Background, _Declaration,
  extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
  hasProp = {}.hasOwnProperty;

_Declaration = require('./_Declaration');

module.exports = Background = (function(superClass) {
  extend(Background, superClass);

  function Background() {
    return Background.__super__.constructor.apply(this, arguments);
  }

  return Background;

})(_Declaration);
