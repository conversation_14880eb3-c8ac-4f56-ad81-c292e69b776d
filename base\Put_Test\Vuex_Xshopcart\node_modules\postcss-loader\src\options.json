{"type": "object", "properties": {"config": {"type": "object", "properties": {"path": {"type": "string"}, "ctx": {"type": "object"}}, "errorMessage": {"properties": {"ctx": "should be {Object} (https://github.com/postcss/postcss-loader#context-ctx)", "path": "should be {String} (https://github.com/postcss/postcss-loader#path)"}}, "additionalProperties": false}, "exec": {"type": "boolean"}, "parser": {"type": ["string", "object"]}, "syntax": {"type": ["string", "object"]}, "stringifier": {"type": ["string", "object"]}, "plugins": {"anyOf": [{"type": "array"}, {"type": "object"}, {"instanceof": "Function"}]}, "sourceMap": {"type": ["string", "boolean"]}}, "errorMessage": {"properties": {"exec": "should be {<PERSON><PERSON><PERSON>} (https://github.com/postcss/postcss-loader#exec)", "config": "should be {Object} (https://github.com/postcss/postcss-loader#config)", "parser": "should be {String|Object} (https://github.com/postcss/postcss-loader#parser)", "syntax": "should be {String|Object} (https://github.com/postcss/postcss-loader#syntax)", "stringifier": "should be {String|Object} (https://github.com/postcss/postcss-loader#stringifier)", "plugins": "should be {Array|Object|Function} (https://github.com/postcss/postcss-loader#plugins)", "sourceMap": "should be {String|Boolean} (https://github.com/postcss/postcss-loader#sourcemap)"}}, "additionalProperties": true}