{"name": "is-svg", "version": "3.0.0", "description": "Check if a string or buffer is SVG", "license": "MIT", "repository": "sindresorhus/is-svg", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["svg", "vector", "graphics", "image", "img", "pic", "picture", "type", "detect", "check", "is", "string", "str", "buffer"], "dependencies": {"html-comment-regex": "^1.1.0"}, "devDependencies": {"ava": "*", "xo": "*"}}