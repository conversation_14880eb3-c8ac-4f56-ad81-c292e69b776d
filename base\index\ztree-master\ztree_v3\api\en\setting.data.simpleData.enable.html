<div class="apiDetail">
<div>
	<h2><span>Boolean</span><span class="path">setting.data.simpleData.</span>enable</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Set zTree's node to accept the simple data format, when zTree is initialized or when ajax get / or when use <span class="highlight_red">addNodes</span> method.</p>
			<p>Don't have to generate the complex nested data.</p>
			<p>Default: false</p>
		</div>
	</div>
	<h3>Boolean Format</h3>
	<div class="desc">
	<p> true means: z<PERSON><PERSON>'s node accept the simple data format.</p>
	<p> false means: zTree's node only accept the nested data format.</p>
	<p class="highlight_red">If set it is true, you must set the other attributes in 'setting.data.simpleData'. (e.g., id<PERSON><PERSON>, pId<PERSON><PERSON>, rootPId)  And let the data satisfy the parent-child relationship.</p>
	</div>
	<h3>Examples of setting</h3>
	<h4>1. use the simple data format</h4>
	<pre xmlns=""><code>var setting = {
	data: {
		simpleData: {
			enable: true,
			idKey: "id",
			pIdKey: "pId",
			rootPId: 0,
		}
	}
};
var treeNodes = [
    {"id":1, "pId":0, "name":"test1"},
    {"id":11, "pId":1, "name":"test11"},
    {"id":12, "pId":1, "name":"test12"},
    {"id":111, "pId":11, "name":"test111"}
];
......</code></pre>
</div>
</div>