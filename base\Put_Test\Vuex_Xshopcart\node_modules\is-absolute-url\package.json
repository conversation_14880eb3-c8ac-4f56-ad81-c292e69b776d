{"name": "is-absolute-url", "version": "2.1.0", "description": "Check if an URL is absolute", "license": "MIT", "repository": "sindresorhus/is-absolute-url", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["url", "absolute", "relative", "uri", "is", "check"], "devDependencies": {"mocha": "*"}}