<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <style>
        ul li {
           cursor: pointer;
           list-style: none;
           float: left;
           margin-right: 20px;
           padding:10px;
       }
       .active{/*激活样式*/
            color: red;
        }
    </style>
</head>
<body>
    <div id="app">
        <div @click="rest" style="cursor: pointer;">重置</div>
        <ul>
            <li
                v-for="(item,index) in nav"
                :class="{ active: index == current }"
                @click="go(index)">
                {{ item }}
            </li>
        </ul>
        
    </div>
    <script src="https://cdn.staticfile.org/vue/2.2.2/vue.min.js"></script>
    <script>
        const vm = new Vue({
           el:'#app',
           data(){
            return {
                current: 0,//切换标识
                nav: [//导航栏数据
                    '首页',
                    '新闻中心',
                    '要闻资讯',
                    '联系我们'
                    ]
                }
            },
           methods:{
                // 导航栏切换
                go(index) {
                    console.log("%c [ index ]", "font-size:13px; background:#00ffff; color:red;", index)
                    this.current = index//激活样式
                },
                rest(){
                    this.current = 0
                    console.log("%c [  this.current  ]", "font-size:13px; background:#00ffff; color:red;",  this.current )
                }
           }
        });
    </script>
</body>

</html>