2018-01-03: Version 0.3.0
  * Add support for [named capturing groups](https://github.com/tc39/proposal-regexp-named-groups) (issue #83)
  * Add polyfill for `fromCodePoint` for Nodejs < 4 (issue #86)

2017-02-23: Version 0.2.1
  * Correctly specify license in `package.json`

2016-05-24: Version 0.2.0
  * Introduce a third argument to the `parse` function for toggling (experimental) features
  * Add experimental support for parsing "\p{…} and \P{…} in Unicode mode" (issue #78)
  * Add `-v`/`--version` and `-f`/`--flags` arguments to the cli command `regjsparser` (issue #79)

2015-08-16: Version 0.1.5
  * Add better error message on parser errors (issues #74 and #76)

2015-02-28: Version 0.1.4
  * Fix parsing of backreferences, which are sometimes parsed as octal escapes (issue #70)

2014-11-25: Version 0.1.3
  * Remove the upper limit of hex digits in Unicode code point escapes (issue #69)

2014-08-31: Version 0.1.2
  * Change the field ref to matchIndex on the type=reference node (issue #67)

2014-08-30: Version 0.1.1
  * Only handle unicode code point escapes if 'u' flag is set (issue #56)
  * Remove `matchIdx` from the AST
  * Fix references like /\1/ (issue #57)
  * Rename type `ref` to `reference` in the AST
  * Update regex to match identifier and include script to generate regex

2014-06-29: Version 0.1.0
  * first tagged release
