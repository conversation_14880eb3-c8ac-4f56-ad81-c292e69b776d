<!DOCTYPE html>
<html>
 
    <head lang="en">
        <meta charset="UTF-8">
        <title>原生JS实现手风琴特效</title>
        <style>
            * {
                margin: 0;
                padding: 0;
            }
 
            ul {
                list-style: none
            }
 
            div {
                width: 1150px;
                height: 400px;
                margin: 50px auto;
                border: 1px solid red;
                overflow: hidden;
            }
 
            div ul {
                width: 1300px;
            }
 
            div li {
                width: 240px;
                height: 400px;
                float: left;
            }
        </style>
    </head>
 
    <body>
 
        <div id="box">
            <ul>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
            </ul>
        </div>
 
 
        <!-- 运动函数 -->
        <script>
            function animate(tag, obj, callback) {
 
                clearInterval(tag.timer);
 
                tag.timer = setInterval(function () {
 
                    // 假设当前这一次定时器代码执行可以清除，即每个样式都到达了指定位置
                    var flag = true;
 
                    for (var k in obj) {
 
                        // 特殊属性单独处理
                        if (k == "opacity") {
 
                            // 将透明度当前值和目标值都设置为扩大后的倍数，设置时除以相应倍数即可
                            var target = obj[k] * 100;
                            var leader = getStyle(tag, k) * 100 || 0;
                            var step = (target - leader) / 10;
 
                            // 设置取整
                            step = step > 0 ? Math.ceil(step) : Math.floor(step);
                            leader = leader + step;
 
                            // 透明度的设置需要去掉单位
                            tag.style[k] = leader / 100;
 
                        } else if (k == "zIndex") {
 
                            // 不需要渐变，直接设置即可
                            tag.style.zIndex = obj[k];
 
                        } else {
 
                            var target = obj[k];
                            var leader = parseInt(getStyle(tag, k)) || 0;
                            var step = (target - leader) / 10;
 
                            step = step > 0 ? Math.ceil(step) : Math.floor(step);
                            leader = leader + step;
                            tag.style[k] = leader + "px";
                        };
 
                        // 未达到目标值
                        if (leader != target) {
                            flag = false;
                        }
                    };
                    if (flag) {
                        clearInterval(tag.timer);
                        callback && callback();
                    }
 
                }, 20);
 
            }
 
            function getStyle(tag, attr) {
                if (tag.currentStyle) {
                    return tag.currentStyle[attr];
                } else {
                    return getComputedStyle(tag, null)[attr];
                }
            }
 
        </script>
 
 
        <script>
 
            // 获取元素
            var box = document.getElementById("box");
            var ul = box.children[0];
            var list = ul.children;
 
            // 设置背景
            for (var i = 0; i < list.length; i++) {
 
                list[i].style.backgroundImage = "url(image/" + i + ".jpg)";
 
                // 移入时
                list[i].onmouseover = function () {
                    for (var i = 0; i < list.length; i++) {
                        animate(list[i], { "width": 100 });
                    };
                    animate(this, { "width": 800 });
                };
                // 移出时
                list[i].onmouseout = function () {
                    for (var i = 0; i < list.length; i++) {
                        animate(list[i],{"width":240});
                    };
                };
            }
        </script>
 
 
    </body>
 
</html>