{"name": "http-proxy-middleware", "version": "0.19.1", "description": "The one-liner node.js proxy middleware for connect, express and browser-sync", "main": "index.js", "files": ["index.js", "lib"], "scripts": {"clean": "rm -rf coverage", "lint": "prettier \"**/*.{js,md}\" --list-different", "lint:fix": "prettier \"**/*.{js,md}\" --write", "test": "npm run lint && mocha --recursive --colors --reporter spec", "cover": "npm run clean && istanbul cover ./node_modules/mocha/bin/_mocha -- --recursive", "coveralls": "istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --recursive --reporter spec && istanbul-coveralls && npm run clean"}, "repository": {"type": "git", "url": "https://github.com/chimurai/http-proxy-middleware.git"}, "keywords": ["reverse", "proxy", "middleware", "http", "https", "connect", "express", "browser-sync", "gulp", "grunt-contrib-connect", "websocket", "ws", "cors"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/chimurai/http-proxy-middleware/issues"}, "homepage": "https://github.com/chimurai/http-proxy-middleware", "devDependencies": {"@commitlint/cli": "^7.2.1", "@commitlint/config-conventional": "^7.1.2", "browser-sync": "^2.26.3", "chai": "^4.2.0", "connect": "^3.6.6", "coveralls": "^3.0.2", "express": "^4.16.4", "husky": "^1.2.0", "istanbul": "^0.4.5", "istanbul-coveralls": "^1.0.3", "mocha": "^5.2.0", "mocha-lcov-reporter": "1.3.0", "opn": "^5.4.0", "precise-commits": "^1.0.2", "prettier": "^1.15.2", "ws": "^6.1.2"}, "dependencies": {"http-proxy": "^1.17.0", "is-glob": "^4.0.0", "lodash": "^4.17.11", "micromatch": "^3.1.10"}, "engines": {"node": ">=4.0.0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "precise-commits"}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}