interface Style {
  (string: string): string
}

export const options: {
  enabled: boolean
}

export const reset: Style
export const bold: Style
export const dim: Style
export const italic: Style
export const underline: Style
export const inverse: Style
export const hidden: Style
export const strikethrough: Style
export const black: Style
export const red: Style
export const green: Style
export const yellow: Style
export const blue: Style
export const magenta: Style
export const cyan: Style
export const white: Style
export const gray: Style
export const bgBlack: Style
export const bgRed: Style
export const bgGreen: Style
export const bgYellow: Style
export const bgBlue: Style
export const bgMagenta: Style
export const bgCyan: Style
export const bgWhite: Style
export const blackBright: Style
export const redBright: Style
export const greenBright: Style
export const yellowBright: Style
export const blueBright: Style
export const magentaBright: Style
export const cyanBright: Style
export const whiteBright: Style
export const bgBlackBright: Style
export const bgRedBright: Style
export const bgGreenBright: Style
export const bgYellowBright: Style
export const bgBlueBright: Style
export const bgMagentaBright: Style
export const bgCyanBright: Style
export const bgWhiteBright: Style
