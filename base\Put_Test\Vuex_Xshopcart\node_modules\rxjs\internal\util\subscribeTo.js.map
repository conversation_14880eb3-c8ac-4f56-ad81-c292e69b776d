{"version": 3, "file": "subscribeTo.js", "sources": ["../../src/internal/util/subscribeTo.ts"], "names": [], "mappings": ";;AACA,uDAAsD;AACtD,2DAA0D;AAC1D,6DAA4D;AAC5D,iEAAgE;AAChE,6CAA4C;AAC5C,yCAAwC;AACxC,uCAAsC;AACtC,+CAAiE;AACjE,mDAAuE;AAI1D,QAAA,WAAW,GAAG,UAAI,MAA0B;IACvD,IAAI,CAAC,CAAC,MAAM,IAAI,OAAO,MAAM,CAAC,uBAAiB,CAAC,KAAK,UAAU,EAAE;QAC/D,OAAO,6CAAqB,CAAC,MAAa,CAAC,CAAC;KAC7C;SAAM,IAAI,yBAAW,CAAC,MAAM,CAAC,EAAE;QAC9B,OAAO,mCAAgB,CAAC,MAAM,CAAC,CAAC;KACjC;SAAM,IAAI,qBAAS,CAAC,MAAM,CAAC,EAAE;QAC5B,OAAO,uCAAkB,CAAC,MAAsB,CAAC,CAAC;KACnD;SAAM,IAAI,CAAC,CAAC,MAAM,IAAI,OAAO,MAAM,CAAC,mBAAe,CAAC,KAAK,UAAU,EAAE;QACpE,OAAO,yCAAmB,CAAC,MAAa,CAAC,CAAC;KAC3C;SAAM;QACL,IAAM,KAAK,GAAG,mBAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,MAAI,MAAM,MAAG,CAAC;QACrE,IAAM,GAAG,GAAG,kBAAgB,KAAK,kCAA+B;cAC5D,8DAA8D,CAAC;QACnE,MAAM,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC;KAC1B;AACH,CAAC,CAAC"}