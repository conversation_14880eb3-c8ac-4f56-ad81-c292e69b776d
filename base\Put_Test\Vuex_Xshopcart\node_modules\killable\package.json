{"name": "killable", "version": "1.0.1", "description": "Keeps track of a server's open sockets so they can be destroyed at a moment's notice.", "main": "index.js", "repository": {"type": "git", "url": "https://github.com/marten-de-vries/killable.git"}, "keywords": ["express", "http", "server", "socket", "kill", "truncate", "destroy", "restart", "shutdown", "immeadiately"], "author": "<PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/marten-de-vries/killable/issues"}}