{"version": 3, "file": "stringify.js", "sourceRoot": "", "sources": ["../src/stringify.ts"], "names": [], "mappings": ";;AAAA,mCAAsC;AAEtC,qCAA0C;AAC1C,yCAA8C;AAE9C;;GAEG;AACH,MAAM,eAAe,GAA6B;IAChD,MAAM,EAAE,mBAAW;IACnB,MAAM,EAAE,CAAC,KAAa,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACxE,OAAO,EAAE,MAAM;IACf,MAAM,EAAE,CAAC,KAAa,EAAE,KAAa,EAAE,IAAU,EAAE,EAAE;QACnD,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAEjC,IAAI,GAAG,KAAK,SAAS;YAAE,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QAEzD,+BAA+B;QAC/B,OAAO,UAAU,IAAI,CAAE,KAAa,CAAC,WAAW,CAAC,GAAG,CAAC;IACvD,CAAC;IACD,MAAM,EAAE,CAAC,KAAa,EAAE,KAAa,EAAE,IAAU,EAAE,EAAE;QACnD,OAAO,UAAU,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;IAC1C,CAAC;IACD,SAAS,EAAE,MAAM;IACjB,MAAM,EAAE,uBAAc;IACtB,QAAQ,EAAE,2BAAgB;CAC3B,CAAC;AAEF;;GAEG;AACU,QAAA,QAAQ,GAAa,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE;IAC5D,IAAI,KAAK,KAAK,IAAI;QAAE,OAAO,MAAM,CAAC;IAElC,OAAO,eAAe,CAAC,OAAO,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;AAChE,CAAC,CAAC", "sourcesContent": ["import { quoteString } from \"./quote\";\nimport { Next, ToString } from \"./types\";\nimport { objectToString } from \"./object\";\nimport { functionToString } from \"./function\";\n\n/**\n * Stringify primitive values.\n */\nconst PRIMITIVE_TYPES: Record<string, ToString> = {\n  string: quoteString,\n  number: (value: number) => (Object.is(value, -0) ? \"-0\" : String(value)),\n  boolean: String,\n  symbol: (value: symbol, space: string, next: Next) => {\n    const key = Symbol.keyFor(value);\n\n    if (key !== undefined) return `Symbol.for(${next(key)})`;\n\n    // ES2018 `Symbol.description`.\n    return `Symbol(${next((value as any).description)})`;\n  },\n  bigint: (value: bigint, space: string, next: Next) => {\n    return `BigInt(${next(String(value))})`;\n  },\n  undefined: String,\n  object: objectToString,\n  function: functionToString\n};\n\n/**\n * Stringify a value recursively.\n */\nexport const toString: ToString = (value, space, next, key) => {\n  if (value === null) return \"null\";\n\n  return PRIMITIVE_TYPES[typeof value](value, space, next, key);\n};\n"]}