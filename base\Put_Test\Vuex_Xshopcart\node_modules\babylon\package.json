{"name": "babylon", "version": "6.18.0", "description": "A JavaScript parser", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "keywords": ["babel", "javascript", "parser", "babylon"], "repository": "https://github.com/babel/babylon", "main": "lib/index.js", "files": ["bin", "lib"], "devDependencies": {"ava": "^0.17.0", "babel-cli": "^6.14.0", "babel-eslint": "^7.0.0", "babel-helper-fixtures": "^6.9.0", "babel-plugin-external-helpers": "^6.18.0", "babel-plugin-istanbul": "^3.0.0", "babel-plugin-transform-flow-strip-types": "^6.14.0", "babel-preset-es2015": "^6.14.0", "babel-preset-stage-0": "^6.5.0", "chalk": "^1.1.3", "codecov": "^1.0.1", "cross-env": "^2.0.0", "eslint": "^3.7.1", "eslint-config-babel": "^6.0.0", "eslint-plugin-flowtype": "^2.20.0", "flow-bin": "^0.42.0", "nyc": "^10.0.0", "rimraf": "^2.5.4", "rollup": "^0.41.0", "rollup-plugin-babel": "^2.6.1", "rollup-plugin-node-resolve": "^2.0.0", "rollup-watch": "^3.2.2", "unicode-9.0.0": "~0.7.0"}, "bin": {"babylon": "./bin/babylon.js"}, "scripts": {"build": "npm run clean && rollup -c", "coverage": "nyc report --reporter=json && codecov -f coverage/coverage-final.json", "lint": "eslint src bin", "clean": "<PERSON><PERSON><PERSON> lib", "flow": "flow", "prepublish": "cross-env BABEL_ENV=production npm run build", "preversion": "npm run test && npm run changelog", "test": "npm run lint && npm run flow && npm run build -- -m && npm run test-only", "test-only": "ava", "test-ci": "nyc npm run test-only", "changelog": "git log `git describe --tags --abbrev=0`..HEAD --pretty=format:' * %s (%an)' | grep -v 'Merge pull request'", "watch": "npm run clean && rollup -c --watch"}, "nyc": {"include": ["src/**/*.js", "bin/**/*.js"], "sourceMap": false, "instrument": false}, "ava": {"files": ["test/*.js"], "source": ["src/**/*.js", "bin/**/*.js"]}, "greenkeeper": {"ignore": ["cross-env"]}}