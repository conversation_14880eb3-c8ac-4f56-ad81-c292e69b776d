{"name": "@vue/cli-plugin-pwa", "version": "4.5.6", "description": "pwa plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-pwa"}, "keywords": ["vue", "cli", "pwa"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-pwa#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^4.5.6", "webpack": "^4.0.0", "workbox-webpack-plugin": "^4.3.1"}, "devDependencies": {"register-service-worker": "^1.7.1"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0"}, "gitHead": "6cac3af2dffbb3a770c8d89f1ac1c9b5f84f7fdb"}