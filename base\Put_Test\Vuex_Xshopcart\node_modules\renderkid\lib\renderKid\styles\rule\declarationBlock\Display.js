// Generated by CoffeeScript 1.9.3
var Display, _Declaration,
  extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
  hasProp = {}.hasOwnProperty,
  indexOf = [].indexOf || function(item) { for (var i = 0, l = this.length; i < l; i++) { if (i in this && this[i] === item) return i; } return -1; };

_Declaration = require('./_Declaration');

module.exports = Display = (function(superClass) {
  var self;

  extend(Display, superClass);

  function Display() {
    return Display.__super__.constructor.apply(this, arguments);
  }

  self = Display;

  Display._allowed = ['inline', 'block', 'none'];

  Display.prototype._set = function(val) {
    val = String(val).toLowerCase();
    if (indexOf.call(self._allowed, val) < 0) {
      throw Error("Unrecognizable value `" + val + "` for `" + this.prop + "`");
    }
    return this.val = val;
  };

  return Display;

})(_Declaration);
