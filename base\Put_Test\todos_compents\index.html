<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1">
		<title>Template • TodoMVC</title>
		<link rel="stylesheet" href="node_modules/todomvc-common/base.css">
		<link rel="stylesheet" href="node_modules/todomvc-app-css/index.css">
		<!-- CSS overrides - remove if you don't need it -->
		<link rel="stylesheet" href="css/app.css">
	</head>
	<body>
		<section class="todoapp">
			<todos-header @zadd="fa_add"></todos-header>
			<!-- 2. 父组件渲染子组件的时候，添加一个自定义属性 -->
			<todos-main :z-list="fa_list" @z_del="fa_del"></todos-main>
			<todos-footer></todos-footer>
		</section>
		<script src="node_modules/vue/dist/vue.js"></script>
		<script src="js/TodosHeader.js"></script>
		<script src="js/TodosMain.js"></script>
		<script src="js/TodosFooter.js"></script>
		<script src="js/app.js"></script>

	</body>
</html>
