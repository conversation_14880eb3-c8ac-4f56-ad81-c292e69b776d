{"name": "eslint", "version": "6.8.0", "author": "<PERSON> <<EMAIL>>", "description": "An AST-based pattern checker for JavaScript.", "bin": {"eslint": "./bin/eslint.js"}, "main": "./lib/api.js", "scripts": {"test": "node Makefile.js test", "test:cli": "mocha", "lint": "node Makefile.js lint", "fix": "node Makefile.js lint -- fix", "fuzz": "node Makefile.js fuzz", "generate-release": "node Makefile.js generateRelease", "generate-alpharelease": "node Makefile.js generatePrerelease -- alpha", "generate-betarelease": "node Makefile.js generatePrerelease -- beta", "generate-rcrelease": "node Makefile.js generatePrerelease -- rc", "publish-release": "node Makefile.js publishRelease", "docs": "node Makefile.js docs", "gensite": "node Makefile.js gensite", "webpack": "node Makefile.js webpack", "perf": "node Makefile.js perf"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.js": ["eslint --fix", "git add"], "*.md": "markdownlint"}, "files": ["LICENSE", "README.md", "bin", "conf", "lib", "messages"], "repository": "eslint/eslint", "funding": "https://opencollective.com/eslint", "homepage": "https://eslint.org", "bugs": "https://github.com/eslint/eslint/issues/", "dependencies": {"@babel/code-frame": "^7.0.0", "ajv": "^6.10.0", "chalk": "^2.1.0", "cross-spawn": "^6.0.5", "debug": "^4.0.1", "doctrine": "^3.0.0", "eslint-scope": "^5.0.0", "eslint-utils": "^1.4.3", "eslint-visitor-keys": "^1.1.0", "espree": "^6.1.2", "esquery": "^1.0.1", "esutils": "^2.0.2", "file-entry-cache": "^5.0.1", "functional-red-black-tree": "^1.0.1", "glob-parent": "^5.0.0", "globals": "^12.1.0", "ignore": "^4.0.6", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "inquirer": "^7.0.0", "is-glob": "^4.0.0", "js-yaml": "^3.13.1", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.3.0", "lodash": "^4.17.14", "minimatch": "^3.0.4", "mkdirp": "^0.5.1", "natural-compare": "^1.4.0", "optionator": "^0.8.3", "progress": "^2.0.0", "regexpp": "^2.0.1", "semver": "^6.1.2", "strip-ansi": "^5.2.0", "strip-json-comments": "^3.0.1", "table": "^5.2.3", "text-table": "^0.2.0", "v8-compile-cache": "^2.0.3"}, "devDependencies": {"@babel/core": "^7.4.3", "@babel/preset-env": "^7.4.3", "acorn": "^7.1.0", "babel-loader": "^8.0.5", "chai": "^4.0.1", "cheerio": "^0.22.0", "common-tags": "^1.8.0", "core-js": "^3.1.3", "dateformat": "^3.0.3", "ejs": "^2.6.1", "eslint": "file:.", "eslint-config-eslint": "file:packages/eslint-config-eslint", "eslint-plugin-eslint-plugin": "^2.0.1", "eslint-plugin-internal-rules": "file:tools/internal-rules", "eslint-plugin-jsdoc": "^15.9.5", "eslint-plugin-node": "^9.0.0", "eslint-release": "^1.2.0", "eslump": "^2.0.0", "esprima": "^4.0.1", "glob": "^7.1.3", "jsdoc": "^3.5.5", "karma": "^4.0.1", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.3", "karma-webpack": "^4.0.0-rc.6", "leche": "^2.2.3", "lint-staged": "^8.1.5", "load-perf": "^0.2.0", "markdownlint": "^0.15.0", "markdownlint-cli": "^0.17.0", "metro-memory-fs": "^0.54.1", "mocha": "^6.1.2", "mocha-junit-reporter": "^1.23.0", "npm-license": "^0.3.3", "nyc": "^14.1.1", "proxyquire": "^2.0.1", "puppeteer": "^1.18.0", "recast": "^0.18.1", "regenerator-runtime": "^0.13.2", "shelljs": "^0.8.2", "sinon": "^7.3.2", "temp": "^0.9.0", "webpack": "^4.35.0", "webpack-cli": "^3.3.5", "yorkie": "^2.0.0"}, "keywords": ["ast", "lint", "javascript", "ecmascript", "espree"], "license": "MIT", "engines": {"node": "^8.10.0 || ^10.13.0 || >=11.10.1"}}