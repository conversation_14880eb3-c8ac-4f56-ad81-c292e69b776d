'use strict';

module.exports = {
	IsPropertyDescriptor: 'https://ecma-international.org/ecma-262/6.0/#sec-property-descriptor-specification-type', // not actually an abstract op

	abs: 'https://ecma-international.org/ecma-262/8.0/#eqn-abs',
	'Abstract Equality Comparison': 'https://ecma-international.org/ecma-262/8.0/#sec-abstract-equality-comparison',
	'Abstract Relational Comparison': 'https://ecma-international.org/ecma-262/8.0/#sec-abstract-relational-comparison',
	AddRestrictedFunctionProperties: 'https://ecma-international.org/ecma-262/8.0/#sec-addrestrictedfunctionproperties',
	AddWaiter: 'https://ecma-international.org/ecma-262/8.0/#sec-addwaiter',
	AdvanceStringIndex: 'https://ecma-international.org/ecma-262/8.0/#sec-advancestringindex',
	'agent-order': 'https://ecma-international.org/ecma-262/8.0/#sec-agent-order',
	AgentCanSuspend: 'https://ecma-international.org/ecma-262/8.0/#sec-agentcansuspend',
	AgentSignifier: 'https://ecma-international.org/ecma-262/8.0/#sec-agentsignifier',
	AllocateArrayBuffer: 'https://ecma-international.org/ecma-262/8.0/#sec-allocatearraybuffer',
	AllocateSharedArrayBuffer: 'https://ecma-international.org/ecma-262/8.0/#sec-allocatesharedarraybuffer',
	AllocateTypedArray: 'https://ecma-international.org/ecma-262/8.0/#sec-allocatetypedarray',
	AllocateTypedArrayBuffer: 'https://ecma-international.org/ecma-262/8.0/#sec-allocatetypedarraybuffer',
	ArrayCreate: 'https://ecma-international.org/ecma-262/8.0/#sec-arraycreate',
	ArraySetLength: 'https://ecma-international.org/ecma-262/8.0/#sec-arraysetlength',
	ArraySpeciesCreate: 'https://ecma-international.org/ecma-262/8.0/#sec-arrayspeciescreate',
	AsyncFunctionAwait: 'https://ecma-international.org/ecma-262/8.0/#sec-async-functions-abstract-operations-async-function-await',
	AsyncFunctionCreate: 'https://ecma-international.org/ecma-262/8.0/#sec-async-functions-abstract-operations-async-function-create',
	AsyncFunctionStart: 'https://ecma-international.org/ecma-262/8.0/#sec-async-functions-abstract-operations-async-function-start',
	AtomicLoad: 'https://ecma-international.org/ecma-262/8.0/#sec-atomicload',
	AtomicReadModifyWrite: 'https://ecma-international.org/ecma-262/8.0/#sec-atomicreadmodifywrite',
	BlockDeclarationInstantiation: 'https://ecma-international.org/ecma-262/8.0/#sec-blockdeclarationinstantiation',
	BoundFunctionCreate: 'https://ecma-international.org/ecma-262/8.0/#sec-boundfunctioncreate',
	Call: 'https://ecma-international.org/ecma-262/8.0/#sec-call',
	Canonicalize: 'https://ecma-international.org/ecma-262/8.0/#sec-runtime-semantics-canonicalize-ch',
	CanonicalNumericIndexString: 'https://ecma-international.org/ecma-262/8.0/#sec-canonicalnumericindexstring',
	CharacterRange: 'https://ecma-international.org/ecma-262/8.0/#sec-runtime-semantics-characterrange-abstract-operation',
	CharacterRangeOrUnion: 'https://ecma-international.org/ecma-262/8.0/#sec-runtime-semantics-characterrangeorunion-abstract-operation',
	CharacterSetMatcher: 'https://ecma-international.org/ecma-262/8.0/#sec-runtime-semantics-charactersetmatcher-abstract-operation',
	CloneArrayBuffer: 'https://ecma-international.org/ecma-262/8.0/#sec-clonearraybuffer',
	CompletePropertyDescriptor: 'https://ecma-international.org/ecma-262/8.0/#sec-completepropertydescriptor',
	Completion: 'https://ecma-international.org/ecma-262/8.0/#sec-completion-record-specification-type',
	ComposeWriteEventBytes: 'https://ecma-international.org/ecma-262/8.0/#sec-composewriteeventbytes',
	Construct: 'https://ecma-international.org/ecma-262/8.0/#sec-construct',
	CopyDataBlockBytes: 'https://ecma-international.org/ecma-262/8.0/#sec-copydatablockbytes',
	CreateArrayFromList: 'https://ecma-international.org/ecma-262/8.0/#sec-createarrayfromlist',
	CreateArrayIterator: 'https://ecma-international.org/ecma-262/8.0/#sec-createarrayiterator',
	CreateBuiltinFunction: 'https://ecma-international.org/ecma-262/8.0/#sec-createbuiltinfunction',
	CreateByteDataBlock: 'https://ecma-international.org/ecma-262/8.0/#sec-createbytedatablock',
	CreateDataProperty: 'https://ecma-international.org/ecma-262/8.0/#sec-createdataproperty',
	CreateDataPropertyOrThrow: 'https://ecma-international.org/ecma-262/8.0/#sec-createdatapropertyorthrow',
	CreateDynamicFunction: 'https://ecma-international.org/ecma-262/8.0/#sec-createdynamicfunction',
	CreateHTML: 'https://ecma-international.org/ecma-262/8.0/#sec-createhtml',
	CreateIntrinsics: 'https://ecma-international.org/ecma-262/8.0/#sec-createintrinsics',
	CreateIterResultObject: 'https://ecma-international.org/ecma-262/8.0/#sec-createiterresultobject',
	CreateListFromArrayLike: 'https://ecma-international.org/ecma-262/8.0/#sec-createlistfromarraylike',
	CreateListIterator: 'https://ecma-international.org/ecma-262/8.0/#sec-createlistiterator',
	CreateMapIterator: 'https://ecma-international.org/ecma-262/8.0/#sec-createmapiterator',
	CreateMappedArgumentsObject: 'https://ecma-international.org/ecma-262/8.0/#sec-createmappedargumentsobject',
	CreateMethodProperty: 'https://ecma-international.org/ecma-262/8.0/#sec-createmethodproperty',
	CreatePerIterationEnvironment: 'https://ecma-international.org/ecma-262/8.0/#sec-createperiterationenvironment',
	CreateRealm: 'https://ecma-international.org/ecma-262/8.0/#sec-createrealm',
	CreateResolvingFunctions: 'https://ecma-international.org/ecma-262/8.0/#sec-createresolvingfunctions',
	CreateSetIterator: 'https://ecma-international.org/ecma-262/8.0/#sec-createsetiterator',
	CreateSharedByteDataBlock: 'https://ecma-international.org/ecma-262/8.0/#sec-createsharedbytedatablock',
	CreateStringIterator: 'https://ecma-international.org/ecma-262/8.0/#sec-createstringiterator',
	CreateUnmappedArgumentsObject: 'https://ecma-international.org/ecma-262/8.0/#sec-createunmappedargumentsobject',
	DateFromTime: 'https://ecma-international.org/ecma-262/8.0/#sec-date-number',
	Day: 'https://ecma-international.org/ecma-262/8.0/#eqn-Day',
	DayFromYear: 'https://ecma-international.org/ecma-262/8.0/#eqn-DaysFromYear',
	DaysInYear: 'https://ecma-international.org/ecma-262/8.0/#eqn-DaysInYear',
	DayWithinYear: 'https://ecma-international.org/ecma-262/8.0/#eqn-DayWithinYear',
	Decode: 'https://ecma-international.org/ecma-262/8.0/#sec-decode',
	DefinePropertyOrThrow: 'https://ecma-international.org/ecma-262/8.0/#sec-definepropertyorthrow',
	DeletePropertyOrThrow: 'https://ecma-international.org/ecma-262/8.0/#sec-deletepropertyorthrow',
	DetachArrayBuffer: 'https://ecma-international.org/ecma-262/8.0/#sec-detacharraybuffer',
	Encode: 'https://ecma-international.org/ecma-262/8.0/#sec-encode',
	EnqueueJob: 'https://ecma-international.org/ecma-262/8.0/#sec-enqueuejob',
	EnterCriticalSection: 'https://ecma-international.org/ecma-262/8.0/#sec-entercriticalsection',
	EnumerableOwnProperties: 'https://ecma-international.org/ecma-262/8.0/#sec-enumerableownproperties',
	EnumerateObjectProperties: 'https://ecma-international.org/ecma-262/8.0/#sec-enumerate-object-properties',
	EscapeRegExpPattern: 'https://ecma-international.org/ecma-262/8.0/#sec-escaperegexppattern',
	EvalDeclarationInstantiation: 'https://ecma-international.org/ecma-262/8.0/#sec-evaldeclarationinstantiation',
	EvaluateCall: 'https://ecma-international.org/ecma-262/8.0/#sec-evaluatecall',
	EvaluateDirectCall: 'https://ecma-international.org/ecma-262/8.0/#sec-evaluatedirectcall',
	EvaluateNew: 'https://ecma-international.org/ecma-262/8.0/#sec-evaluatenew',
	EventSet: 'https://ecma-international.org/ecma-262/8.0/#sec-event-set',
	floor: 'https://ecma-international.org/ecma-262/8.0/#eqn-floor',
	ForBodyEvaluation: 'https://ecma-international.org/ecma-262/8.0/#sec-forbodyevaluation',
	'ForIn/OfBodyEvaluation': 'https://ecma-international.org/ecma-262/8.0/#sec-runtime-semantics-forin-div-ofbodyevaluation-lhs-stmt-iterator-lhskind-labelset',
	'ForIn/OfHeadEvaluation': 'https://ecma-international.org/ecma-262/8.0/#sec-runtime-semantics-forin-div-ofheadevaluation-tdznames-expr-iterationkind',
	FromPropertyDescriptor: 'https://ecma-international.org/ecma-262/8.0/#sec-frompropertydescriptor',
	FulfillPromise: 'https://ecma-international.org/ecma-262/8.0/#sec-fulfillpromise',
	FunctionAllocate: 'https://ecma-international.org/ecma-262/8.0/#sec-functionallocate',
	FunctionCreate: 'https://ecma-international.org/ecma-262/8.0/#sec-functioncreate',
	FunctionDeclarationInstantiation: 'https://ecma-international.org/ecma-262/8.0/#sec-functiondeclarationinstantiation',
	FunctionInitialize: 'https://ecma-international.org/ecma-262/8.0/#sec-functioninitialize',
	GeneratorFunctionCreate: 'https://ecma-international.org/ecma-262/8.0/#sec-generatorfunctioncreate',
	GeneratorResume: 'https://ecma-international.org/ecma-262/8.0/#sec-generatorresume',
	GeneratorResumeAbrupt: 'https://ecma-international.org/ecma-262/8.0/#sec-generatorresumeabrupt',
	GeneratorStart: 'https://ecma-international.org/ecma-262/8.0/#sec-generatorstart',
	GeneratorValidate: 'https://ecma-international.org/ecma-262/8.0/#sec-generatorvalidate',
	GeneratorYield: 'https://ecma-international.org/ecma-262/8.0/#sec-generatoryield',
	Get: 'https://ecma-international.org/ecma-262/8.0/#sec-get-o-p',
	GetActiveScriptOrModule: 'https://ecma-international.org/ecma-262/8.0/#sec-getactivescriptormodule',
	GetBase: 'https://ecma-international.org/ecma-262/8.0/#ao-getbase',
	GetFunctionRealm: 'https://ecma-international.org/ecma-262/8.0/#sec-getfunctionrealm',
	GetGlobalObject: 'https://ecma-international.org/ecma-262/8.0/#sec-getglobalobject',
	GetIdentifierReference: 'https://ecma-international.org/ecma-262/8.0/#sec-getidentifierreference',
	GetIterator: 'https://ecma-international.org/ecma-262/8.0/#sec-getiterator',
	GetMethod: 'https://ecma-international.org/ecma-262/8.0/#sec-getmethod',
	GetModifySetValueInBuffer: 'https://ecma-international.org/ecma-262/8.0/#sec-getmodifysetvalueinbuffer',
	GetModuleNamespace: 'https://ecma-international.org/ecma-262/8.0/#sec-getmodulenamespace',
	GetNewTarget: 'https://ecma-international.org/ecma-262/8.0/#sec-getnewtarget',
	GetOwnPropertyKeys: 'https://ecma-international.org/ecma-262/8.0/#sec-getownpropertykeys',
	GetPrototypeFromConstructor: 'https://ecma-international.org/ecma-262/8.0/#sec-getprototypefromconstructor',
	GetReferencedName: 'https://ecma-international.org/ecma-262/8.0/#ao-getreferencedname',
	GetSubstitution: 'https://ecma-international.org/ecma-262/8.0/#sec-getsubstitution',
	GetSuperConstructor: 'https://ecma-international.org/ecma-262/8.0/#sec-getsuperconstructor',
	GetTemplateObject: 'https://ecma-international.org/ecma-262/8.0/#sec-gettemplateobject',
	GetThisEnvironment: 'https://ecma-international.org/ecma-262/8.0/#sec-getthisenvironment',
	GetThisValue: 'https://ecma-international.org/ecma-262/8.0/#sec-getthisvalue',
	GetV: 'https://ecma-international.org/ecma-262/8.0/#sec-getv',
	GetValue: 'https://ecma-international.org/ecma-262/8.0/#sec-getvalue',
	GetValueFromBuffer: 'https://ecma-international.org/ecma-262/8.0/#sec-getvaluefrombuffer',
	GetViewValue: 'https://ecma-international.org/ecma-262/8.0/#sec-getviewvalue',
	GetWaiterList: 'https://ecma-international.org/ecma-262/8.0/#sec-getwaiterlist',
	GlobalDeclarationInstantiation: 'https://ecma-international.org/ecma-262/8.0/#sec-globaldeclarationinstantiation',
	'happens-before': 'https://ecma-international.org/ecma-262/8.0/#sec-happens-before',
	HasOwnProperty: 'https://ecma-international.org/ecma-262/8.0/#sec-hasownproperty',
	HasPrimitiveBase: 'https://ecma-international.org/ecma-262/8.0/#ao-hasprimitivebase',
	HasProperty: 'https://ecma-international.org/ecma-262/8.0/#sec-hasproperty',
	'host-synchronizes-with': 'https://ecma-international.org/ecma-262/8.0/#sec-host-synchronizes-with',
	HostEnsureCanCompileStrings: 'https://ecma-international.org/ecma-262/8.0/#sec-hostensurecancompilestrings',
	HostEventSet: 'https://ecma-international.org/ecma-262/8.0/#sec-hosteventset',
	HostPromiseRejectionTracker: 'https://ecma-international.org/ecma-262/8.0/#sec-host-promise-rejection-tracker',
	HostReportErrors: 'https://ecma-international.org/ecma-262/8.0/#sec-host-report-errors',
	HostResolveImportedModule: 'https://ecma-international.org/ecma-262/8.0/#sec-hostresolveimportedmodule',
	HourFromTime: 'https://ecma-international.org/ecma-262/8.0/#eqn-HourFromTime',
	IfAbruptRejectPromise: 'https://ecma-international.org/ecma-262/8.0/#sec-ifabruptrejectpromise',
	ImportedLocalNames: 'https://ecma-international.org/ecma-262/8.0/#sec-importedlocalnames',
	InitializeBoundName: 'https://ecma-international.org/ecma-262/8.0/#sec-initializeboundname',
	InitializeHostDefinedRealm: 'https://ecma-international.org/ecma-262/8.0/#sec-initializehostdefinedrealm',
	InitializeReferencedBinding: 'https://ecma-international.org/ecma-262/8.0/#sec-initializereferencedbinding',
	InLeapYear: 'https://ecma-international.org/ecma-262/8.0/#eqn-InLeapYear',
	InstanceofOperator: 'https://ecma-international.org/ecma-262/8.0/#sec-instanceofoperator',
	IntegerIndexedElementGet: 'https://ecma-international.org/ecma-262/8.0/#sec-integerindexedelementget',
	IntegerIndexedElementSet: 'https://ecma-international.org/ecma-262/8.0/#sec-integerindexedelementset',
	IntegerIndexedObjectCreate: 'https://ecma-international.org/ecma-262/8.0/#sec-integerindexedobjectcreate',
	InternalizeJSONProperty: 'https://ecma-international.org/ecma-262/8.0/#sec-internalizejsonproperty',
	Invoke: 'https://ecma-international.org/ecma-262/8.0/#sec-invoke',
	IsAccessorDescriptor: 'https://ecma-international.org/ecma-262/8.0/#sec-isaccessordescriptor',
	IsAnonymousFunctionDefinition: 'https://ecma-international.org/ecma-262/8.0/#sec-isanonymousfunctiondefinition',
	IsArray: 'https://ecma-international.org/ecma-262/8.0/#sec-isarray',
	IsCallable: 'https://ecma-international.org/ecma-262/8.0/#sec-iscallable',
	IsCompatiblePropertyDescriptor: 'https://ecma-international.org/ecma-262/8.0/#sec-iscompatiblepropertydescriptor',
	IsConcatSpreadable: 'https://ecma-international.org/ecma-262/8.0/#sec-isconcatspreadable',
	IsConstructor: 'https://ecma-international.org/ecma-262/8.0/#sec-isconstructor',
	IsDataDescriptor: 'https://ecma-international.org/ecma-262/8.0/#sec-isdatadescriptor',
	IsDetachedBuffer: 'https://ecma-international.org/ecma-262/8.0/#sec-isdetachedbuffer',
	IsExtensible: 'https://ecma-international.org/ecma-262/8.0/#sec-isextensible-o',
	IsGenericDescriptor: 'https://ecma-international.org/ecma-262/8.0/#sec-isgenericdescriptor',
	IsInTailPosition: 'https://ecma-international.org/ecma-262/8.0/#sec-isintailposition',
	IsInteger: 'https://ecma-international.org/ecma-262/8.0/#sec-isinteger',
	IsLabelledFunction: 'https://ecma-international.org/ecma-262/8.0/#sec-islabelledfunction',
	IsPromise: 'https://ecma-international.org/ecma-262/8.0/#sec-ispromise',
	IsPropertyKey: 'https://ecma-international.org/ecma-262/8.0/#sec-ispropertykey',
	IsPropertyReference: 'https://ecma-international.org/ecma-262/8.0/#ao-ispropertyreference',
	IsRegExp: 'https://ecma-international.org/ecma-262/8.0/#sec-isregexp',
	IsSharedArrayBuffer: 'https://ecma-international.org/ecma-262/8.0/#sec-issharedarraybuffer',
	IsStrictReference: 'https://ecma-international.org/ecma-262/8.0/#ao-isstrictreference',
	IsSuperReference: 'https://ecma-international.org/ecma-262/8.0/#ao-issuperreference',
	IsUnresolvableReference: 'https://ecma-international.org/ecma-262/8.0/#ao-isunresolvablereference',
	IsWordChar: 'https://ecma-international.org/ecma-262/8.0/#sec-runtime-semantics-iswordchar-abstract-operation',
	IterableToList: 'https://ecma-international.org/ecma-262/8.0/#sec-iterabletolist',
	IteratorClose: 'https://ecma-international.org/ecma-262/8.0/#sec-iteratorclose',
	IteratorComplete: 'https://ecma-international.org/ecma-262/8.0/#sec-iteratorcomplete',
	IteratorNext: 'https://ecma-international.org/ecma-262/8.0/#sec-iteratornext',
	IteratorStep: 'https://ecma-international.org/ecma-262/8.0/#sec-iteratorstep',
	IteratorValue: 'https://ecma-international.org/ecma-262/8.0/#sec-iteratorvalue',
	LeaveCriticalSection: 'https://ecma-international.org/ecma-262/8.0/#sec-leavecriticalsection',
	LocalTime: 'https://ecma-international.org/ecma-262/8.0/#sec-localtime',
	LoopContinues: 'https://ecma-international.org/ecma-262/8.0/#sec-loopcontinues',
	MakeArgGetter: 'https://ecma-international.org/ecma-262/8.0/#sec-makearggetter',
	MakeArgSetter: 'https://ecma-international.org/ecma-262/8.0/#sec-makeargsetter',
	MakeClassConstructor: 'https://ecma-international.org/ecma-262/8.0/#sec-makeclassconstructor',
	MakeConstructor: 'https://ecma-international.org/ecma-262/8.0/#sec-makeconstructor',
	MakeDate: 'https://ecma-international.org/ecma-262/8.0/#sec-makedate',
	MakeDay: 'https://ecma-international.org/ecma-262/8.0/#sec-makeday',
	MakeMethod: 'https://ecma-international.org/ecma-262/8.0/#sec-makemethod',
	MakeSuperPropertyReference: 'https://ecma-international.org/ecma-262/8.0/#sec-makesuperpropertyreference',
	MakeTime: 'https://ecma-international.org/ecma-262/8.0/#sec-maketime',
	max: 'https://ecma-international.org/ecma-262/8.0/#eqn-max',
	'memory-order': 'https://ecma-international.org/ecma-262/8.0/#sec-memory-order',
	min: 'https://ecma-international.org/ecma-262/8.0/#eqn-min',
	MinFromTime: 'https://ecma-international.org/ecma-262/8.0/#eqn-MinFromTime',
	ModuleNamespaceCreate: 'https://ecma-international.org/ecma-262/8.0/#sec-modulenamespacecreate',
	modulo: 'https://ecma-international.org/ecma-262/8.0/#eqn-modulo',
	MonthFromTime: 'https://ecma-international.org/ecma-262/8.0/#eqn-MonthFromTime',
	msFromTime: 'https://ecma-international.org/ecma-262/8.0/#eqn-msFromTime',
	NewDeclarativeEnvironment: 'https://ecma-international.org/ecma-262/8.0/#sec-newdeclarativeenvironment',
	NewFunctionEnvironment: 'https://ecma-international.org/ecma-262/8.0/#sec-newfunctionenvironment',
	NewGlobalEnvironment: 'https://ecma-international.org/ecma-262/8.0/#sec-newglobalenvironment',
	NewModuleEnvironment: 'https://ecma-international.org/ecma-262/8.0/#sec-newmoduleenvironment',
	NewObjectEnvironment: 'https://ecma-international.org/ecma-262/8.0/#sec-newobjectenvironment',
	NewPromiseCapability: 'https://ecma-international.org/ecma-262/8.0/#sec-newpromisecapability',
	NormalCompletion: 'https://ecma-international.org/ecma-262/8.0/#sec-normalcompletion',
	NumberToRawBytes: 'https://ecma-international.org/ecma-262/8.0/#sec-numbertorawbytes',
	ObjectCreate: 'https://ecma-international.org/ecma-262/8.0/#sec-objectcreate',
	ObjectDefineProperties: 'https://ecma-international.org/ecma-262/8.0/#sec-objectdefineproperties',
	OrdinaryCallBindThis: 'https://ecma-international.org/ecma-262/8.0/#sec-ordinarycallbindthis',
	OrdinaryCallEvaluateBody: 'https://ecma-international.org/ecma-262/8.0/#sec-ordinarycallevaluatebody',
	OrdinaryCreateFromConstructor: 'https://ecma-international.org/ecma-262/8.0/#sec-ordinarycreatefromconstructor',
	OrdinaryDefineOwnProperty: 'https://ecma-international.org/ecma-262/8.0/#sec-ordinarydefineownproperty',
	OrdinaryDelete: 'https://ecma-international.org/ecma-262/8.0/#sec-ordinarydelete',
	OrdinaryGet: 'https://ecma-international.org/ecma-262/8.0/#sec-ordinaryget',
	OrdinaryGetOwnProperty: 'https://ecma-international.org/ecma-262/8.0/#sec-ordinarygetownproperty',
	OrdinaryGetPrototypeOf: 'https://ecma-international.org/ecma-262/8.0/#sec-ordinarygetprototypeof',
	OrdinaryHasInstance: 'https://ecma-international.org/ecma-262/8.0/#sec-ordinaryhasinstance',
	OrdinaryHasProperty: 'https://ecma-international.org/ecma-262/8.0/#sec-ordinaryhasproperty',
	OrdinaryIsExtensible: 'https://ecma-international.org/ecma-262/8.0/#sec-ordinaryisextensible',
	OrdinaryOwnPropertyKeys: 'https://ecma-international.org/ecma-262/8.0/#sec-ordinaryownpropertykeys',
	OrdinaryPreventExtensions: 'https://ecma-international.org/ecma-262/8.0/#sec-ordinarypreventextensions',
	OrdinarySet: 'https://ecma-international.org/ecma-262/8.0/#sec-ordinaryset',
	OrdinarySetPrototypeOf: 'https://ecma-international.org/ecma-262/8.0/#sec-ordinarysetprototypeof',
	OrdinaryToPrimitive: 'https://ecma-international.org/ecma-262/8.0/#sec-ordinarytoprimitive',
	ParseModule: 'https://ecma-international.org/ecma-262/8.0/#sec-parsemodule',
	ParseScript: 'https://ecma-international.org/ecma-262/8.0/#sec-parse-script',
	PerformEval: 'https://ecma-international.org/ecma-262/8.0/#sec-performeval',
	PerformPromiseAll: 'https://ecma-international.org/ecma-262/8.0/#sec-performpromiseall',
	PerformPromiseRace: 'https://ecma-international.org/ecma-262/8.0/#sec-performpromiserace',
	PerformPromiseThen: 'https://ecma-international.org/ecma-262/8.0/#sec-performpromisethen',
	PrepareForOrdinaryCall: 'https://ecma-international.org/ecma-262/8.0/#sec-prepareforordinarycall',
	PrepareForTailCall: 'https://ecma-international.org/ecma-262/8.0/#sec-preparefortailcall',
	PromiseReactionJob: 'https://ecma-international.org/ecma-262/8.0/#sec-promisereactionjob',
	PromiseResolveThenableJob: 'https://ecma-international.org/ecma-262/8.0/#sec-promiseresolvethenablejob',
	ProxyCreate: 'https://ecma-international.org/ecma-262/8.0/#sec-proxycreate',
	PutValue: 'https://ecma-international.org/ecma-262/8.0/#sec-putvalue',
	QuoteJSONString: 'https://ecma-international.org/ecma-262/8.0/#sec-quotejsonstring',
	RawBytesToNumber: 'https://ecma-international.org/ecma-262/8.0/#sec-rawbytestonumber',
	'reads-bytes-from': 'https://ecma-international.org/ecma-262/8.0/#sec-reads-bytes-from',
	'reads-from': 'https://ecma-international.org/ecma-262/8.0/#sec-reads-from',
	RegExpAlloc: 'https://ecma-international.org/ecma-262/8.0/#sec-regexpalloc',
	RegExpBuiltinExec: 'https://ecma-international.org/ecma-262/8.0/#sec-regexpbuiltinexec',
	RegExpCreate: 'https://ecma-international.org/ecma-262/8.0/#sec-regexpcreate',
	RegExpExec: 'https://ecma-international.org/ecma-262/8.0/#sec-regexpexec',
	RegExpInitialize: 'https://ecma-international.org/ecma-262/8.0/#sec-regexpinitialize',
	RejectPromise: 'https://ecma-international.org/ecma-262/8.0/#sec-rejectpromise',
	RemoveWaiter: 'https://ecma-international.org/ecma-262/8.0/#sec-removewaiter',
	RemoveWaiters: 'https://ecma-international.org/ecma-262/8.0/#sec-removewaiters',
	RepeatMatcher: 'https://ecma-international.org/ecma-262/8.0/#sec-runtime-semantics-repeatmatcher-abstract-operation',
	RequireObjectCoercible: 'https://ecma-international.org/ecma-262/8.0/#sec-requireobjectcoercible',
	ResolveBinding: 'https://ecma-international.org/ecma-262/8.0/#sec-resolvebinding',
	ResolveThisBinding: 'https://ecma-international.org/ecma-262/8.0/#sec-resolvethisbinding',
	ReturnIfAbrupt: 'https://ecma-international.org/ecma-262/8.0/#sec-returnifabrupt',
	RunJobs: 'https://ecma-international.org/ecma-262/8.0/#sec-runjobs',
	SameValue: 'https://ecma-international.org/ecma-262/8.0/#sec-samevalue',
	SameValueNonNumber: 'https://ecma-international.org/ecma-262/8.0/#sec-samevaluenonnumber',
	SameValueZero: 'https://ecma-international.org/ecma-262/8.0/#sec-samevaluezero',
	ScriptEvaluation: 'https://ecma-international.org/ecma-262/8.0/#sec-runtime-semantics-scriptevaluation',
	ScriptEvaluationJob: 'https://ecma-international.org/ecma-262/8.0/#sec-scriptevaluationjob',
	SecFromTime: 'https://ecma-international.org/ecma-262/8.0/#eqn-SecFromTime',
	SerializeJSONArray: 'https://ecma-international.org/ecma-262/8.0/#sec-serializejsonarray',
	SerializeJSONObject: 'https://ecma-international.org/ecma-262/8.0/#sec-serializejsonobject',
	SerializeJSONProperty: 'https://ecma-international.org/ecma-262/8.0/#sec-serializejsonproperty',
	Set: 'https://ecma-international.org/ecma-262/8.0/#sec-set-o-p-v-throw',
	SetDefaultGlobalBindings: 'https://ecma-international.org/ecma-262/8.0/#sec-setdefaultglobalbindings',
	SetFunctionName: 'https://ecma-international.org/ecma-262/8.0/#sec-setfunctionname',
	SetImmutablePrototype: 'https://ecma-international.org/ecma-262/8.0/#sec-set-immutable-prototype',
	SetIntegrityLevel: 'https://ecma-international.org/ecma-262/8.0/#sec-setintegritylevel',
	SetRealmGlobalObject: 'https://ecma-international.org/ecma-262/8.0/#sec-setrealmglobalobject',
	SetValueInBuffer: 'https://ecma-international.org/ecma-262/8.0/#sec-setvalueinbuffer',
	SetViewValue: 'https://ecma-international.org/ecma-262/8.0/#sec-setviewvalue',
	SharedDataBlockEventSet: 'https://ecma-international.org/ecma-262/8.0/#sec-sharedatablockeventset',
	SortCompare: 'https://ecma-international.org/ecma-262/8.0/#sec-sortcompare',
	SpeciesConstructor: 'https://ecma-international.org/ecma-262/8.0/#sec-speciesconstructor',
	SplitMatch: 'https://ecma-international.org/ecma-262/8.0/#sec-splitmatch',
	'Strict Equality Comparison': 'https://ecma-international.org/ecma-262/8.0/#sec-strict-equality-comparison',
	StringCreate: 'https://ecma-international.org/ecma-262/8.0/#sec-stringcreate',
	StringGetOwnProperty: 'https://ecma-international.org/ecma-262/8.0/#sec-stringgetownproperty',
	Suspend: 'https://ecma-international.org/ecma-262/8.0/#sec-suspend',
	SymbolDescriptiveString: 'https://ecma-international.org/ecma-262/8.0/#sec-symboldescriptivestring',
	'synchronizes-with': 'https://ecma-international.org/ecma-262/8.0/#sec-synchronizes-with',
	TestIntegrityLevel: 'https://ecma-international.org/ecma-262/8.0/#sec-testintegritylevel',
	thisBooleanValue: 'https://ecma-international.org/ecma-262/8.0/#sec-thisbooleanvalue',
	thisNumberValue: 'https://ecma-international.org/ecma-262/8.0/#sec-thisnumbervalue',
	thisStringValue: 'https://ecma-international.org/ecma-262/8.0/#sec-thisstringvalue',
	thisTimeValue: 'https://ecma-international.org/ecma-262/8.0/#sec-thistimevalue',
	TimeClip: 'https://ecma-international.org/ecma-262/8.0/#sec-timeclip',
	TimeFromYear: 'https://ecma-international.org/ecma-262/8.0/#eqn-TimeFromYear',
	TimeWithinDay: 'https://ecma-international.org/ecma-262/8.0/#eqn-TimeWithinDay',
	ToBoolean: 'https://ecma-international.org/ecma-262/8.0/#sec-toboolean',
	ToDateString: 'https://ecma-international.org/ecma-262/8.0/#sec-todatestring',
	ToIndex: 'https://ecma-international.org/ecma-262/8.0/#sec-toindex',
	ToInt16: 'https://ecma-international.org/ecma-262/8.0/#sec-toint16',
	ToInt32: 'https://ecma-international.org/ecma-262/8.0/#sec-toint32',
	ToInt8: 'https://ecma-international.org/ecma-262/8.0/#sec-toint8',
	ToInteger: 'https://ecma-international.org/ecma-262/8.0/#sec-tointeger',
	ToLength: 'https://ecma-international.org/ecma-262/8.0/#sec-tolength',
	ToNumber: 'https://ecma-international.org/ecma-262/8.0/#sec-tonumber',
	ToObject: 'https://ecma-international.org/ecma-262/8.0/#sec-toobject',
	TopLevelModuleEvaluationJob: 'https://ecma-international.org/ecma-262/8.0/#sec-toplevelmoduleevaluationjob',
	ToPrimitive: 'https://ecma-international.org/ecma-262/8.0/#sec-toprimitive',
	ToPropertyDescriptor: 'https://ecma-international.org/ecma-262/8.0/#sec-topropertydescriptor',
	ToPropertyKey: 'https://ecma-international.org/ecma-262/8.0/#sec-topropertykey',
	ToString: 'https://ecma-international.org/ecma-262/8.0/#sec-tostring',
	'ToString Applied to the Number Type': 'https://ecma-international.org/ecma-262/8.0/#sec-tostring-applied-to-the-number-type',
	ToUint16: 'https://ecma-international.org/ecma-262/8.0/#sec-touint16',
	ToUint32: 'https://ecma-international.org/ecma-262/8.0/#sec-touint32',
	ToUint8: 'https://ecma-international.org/ecma-262/8.0/#sec-touint8',
	ToUint8Clamp: 'https://ecma-international.org/ecma-262/8.0/#sec-touint8clamp',
	TriggerPromiseReactions: 'https://ecma-international.org/ecma-262/8.0/#sec-triggerpromisereactions',
	Type: 'https://ecma-international.org/ecma-262/8.0/#sec-ecmascript-data-types-and-values',
	TypedArrayCreate: 'https://ecma-international.org/ecma-262/8.0/#typedarray-create',
	TypedArraySpeciesCreate: 'https://ecma-international.org/ecma-262/8.0/#typedarray-species-create',
	UpdateEmpty: 'https://ecma-international.org/ecma-262/8.0/#sec-updateempty',
	UTC: 'https://ecma-international.org/ecma-262/8.0/#sec-utc-t',
	UTF16Decode: 'https://ecma-international.org/ecma-262/8.0/#sec-utf16decode',
	UTF16Encoding: 'https://ecma-international.org/ecma-262/8.0/#sec-utf16encoding',
	ValidateAndApplyPropertyDescriptor: 'https://ecma-international.org/ecma-262/8.0/#sec-validateandapplypropertydescriptor',
	ValidateAtomicAccess: 'https://ecma-international.org/ecma-262/8.0/#sec-validateatomicaccess',
	ValidateSharedIntegerTypedArray: 'https://ecma-international.org/ecma-262/8.0/#sec-validatesharedintegertypedarray',
	ValidateTypedArray: 'https://ecma-international.org/ecma-262/8.0/#sec-validatetypedarray',
	ValueOfReadEvent: 'https://ecma-international.org/ecma-262/8.0/#sec-valueofreadevent',
	WakeWaiter: 'https://ecma-international.org/ecma-262/8.0/#sec-wakewaiter',
	WeekDay: 'https://ecma-international.org/ecma-262/8.0/#sec-week-day',
	WordCharacters: 'https://ecma-international.org/ecma-262/8.0/#sec-runtime-semantics-wordcharacters-abstract-operation',
	YearFromTime: 'https://ecma-international.org/ecma-262/8.0/#eqn-YearFromTime'
};
