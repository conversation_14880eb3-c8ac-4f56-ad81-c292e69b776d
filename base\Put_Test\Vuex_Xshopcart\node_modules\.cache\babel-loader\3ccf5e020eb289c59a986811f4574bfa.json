{"remainingRequest": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\store\\index.js", "dependencies": [{"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\store\\index.js", "mtime": 1649424516404}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1657986293299}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1657986298727}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\eslint-loader\\index.js", "mtime": 1657986295398}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgVnVleCBmcm9tICd2dWV4JzsKaW1wb3J0IGNhcnQgZnJvbSAnLi9jYXJ0L2luZGV4JzsKVnVlLnVzZShWdWV4KTsKZXhwb3J0IGRlZmF1bHQgbmV3IFZ1ZXguU3RvcmUoewogIG1vZHVsZXM6IHsKICAgIGNhcnQ6IGNhcnQKICB9Cn0pOw=="}, {"version": 3, "sources": ["D:/No.1 folder/No.2 folder/dev/dev2/vue/base/Vuex_Xshopcart/src/store/index.js"], "names": ["<PERSON><PERSON>", "Vuex", "cart", "use", "Store", "modules"], "mappings": "AAAA,OAAOA,GAAP,MAAgB,KAAhB;AACA,OAAOC,IAAP,MAAiB,MAAjB;AACA,OAAOC,IAAP,MAAiB,cAAjB;AACAF,GAAG,CAACG,GAAJ,CAAQF,IAAR;AAEA,eAAe,IAAIA,IAAI,CAACG,KAAT,CAAe;AAC5BC,EAAAA,OAAO,EAAE;AACPH,IAAAA,IAAI,EAAJA;AADO;AADmB,CAAf,CAAf", "sourcesContent": ["import Vue from 'vue'\nimport Vuex from 'vuex'\nimport cart from './cart/index'\nVue.use(Vuex)\n\nexport default new Vuex.Store({\n  modules: {\n    cart\n  }\n})\n"]}]}