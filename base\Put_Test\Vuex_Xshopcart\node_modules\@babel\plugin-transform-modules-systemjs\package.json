{"name": "@babel/plugin-transform-modules-systemjs", "version": "7.10.5", "description": "This plugin transforms ES2015 modules to SystemJS", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-hoist-variables": "^7.10.4", "@babel/helper-module-transforms": "^7.10.5", "@babel/helper-plugin-utils": "^7.10.4", "babel-plugin-dynamic-import-node": "^2.3.3"}, "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.5", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.0"}, "gitHead": "f7964a9ac51356f7df6404a25b27ba1cffba1ba7"}