<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
       #box{
            width:100px;
            height:100px;
            background:yellow
        } 
        #child{
            width: 20px;
            height: 20px;
            background:yellowgreen;
        }
    </style>
    
</head>
<body>
    <button onclick="click()">点击</button>
    <div id="box">
        <div id="child"></div>
        <div id="child"> </div>
    </div>
    <script>
        function click(){
            console.log('123 :>> ', 123);
            let box = document.getElementById(box)
            let child = document.getElementById(child)
            box.removeChild(box.child) //父节点删除子节点时应该是box.child
        }
       
    </script>
</body>
</html>