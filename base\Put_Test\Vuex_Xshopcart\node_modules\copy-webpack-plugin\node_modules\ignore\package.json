{"name": "ignore", "version": "3.3.10", "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "main": "./ignore.js", "files": ["ignore.js", "index.d.ts"], "scripts": {"prepublish": "npm run build", "build": "babel -o ignore.js index.js", "tsc": "tsc ./test/ts/simple.ts", "test": "npm run tsc && npm run build && istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec ./test/ignore.js && codecov", "test-no-cov": "npm run tsc && npm run build && mocha --reporter spec ./test/ignore.js", "cov-report": "istanbul report"}, "repository": {"type": "git", "url": "**************:kaelzhang/node-ignore.git"}, "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "author": "kael", "license": "MIT", "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-es2015": "^6.24.1", "chai": "~1.7.2", "codecov": "^3.0.2", "istanbul": "^0.4.5", "mkdirp": "^0.5.1", "mocha": "~1.13.0", "pre-suf": "^1.0.4", "rimraf": "^2.6.2", "spawn-sync": "^1.0.15", "tmp": "0.0.33", "typescript": "^2.9.2"}}