{"remainingRequest": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\main.js", "dependencies": [{"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\main.js", "mtime": 1649424514338}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1657986293299}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1657986298727}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\eslint-loader\\index.js", "mtime": 1657986295398}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJEOlxcTm8uMSBmb2xkZXJcXE5vLjIgZm9sZGVyXFxkZXZcXGRldjJcXHZ1ZVxcYmFzZVxcVnVleF9Yc2hvcGNhcnRcXG5vZGVfbW9kdWxlc1xcY29yZS1qc1xcbW9kdWxlc1xcZXMuYXJyYXkuaXRlcmF0b3IuanMiOwppbXBvcnQgIkQ6XFxOby4xIGZvbGRlclxcTm8uMiBmb2xkZXJcXGRldlxcZGV2MlxcdnVlXFxiYXNlXFxWdWV4X1hzaG9wY2FydFxcbm9kZV9tb2R1bGVzXFxjb3JlLWpzXFxtb2R1bGVzXFxlcy5wcm9taXNlLmpzIjsKaW1wb3J0ICJEOlxcTm8uMSBmb2xkZXJcXE5vLjIgZm9sZGVyXFxkZXZcXGRldjJcXHZ1ZVxcYmFzZVxcVnVleF9Yc2hvcGNhcnRcXG5vZGVfbW9kdWxlc1xcY29yZS1qc1xcbW9kdWxlc1xcZXMub2JqZWN0LmFzc2lnbi5qcyI7CmltcG9ydCAiRDpcXE5vLjEgZm9sZGVyXFxOby4yIGZvbGRlclxcZGV2XFxkZXYyXFx2dWVcXGJhc2VcXFZ1ZXhfWHNob3BjYXJ0XFxub2RlX21vZHVsZXNcXGNvcmUtanNcXG1vZHVsZXNcXGVzLnByb21pc2UuZmluYWxseS5qcyI7CmltcG9ydCBWdWUgZnJvbSAndnVlJzsKaW1wb3J0IEFwcCBmcm9tICcuL0FwcC52dWUnOwppbXBvcnQgJy4vcmVnaXN0ZXJTZXJ2aWNlV29ya2VyJzsKaW1wb3J0IHN0b3JlIGZyb20gJy4vc3RvcmUnOwpWdWUuY29uZmlnLnByb2R1Y3Rpb25UaXAgPSBmYWxzZTsKbmV3IFZ1ZSh7CiAgc3RvcmU6IHN0b3JlLAogIHJlbmRlcjogZnVuY3Rpb24gcmVuZGVyKGgpIHsKICAgIHJldHVybiBoKEFwcCk7CiAgfQp9KS4kbW91bnQoJyNhcHAnKTs="}, {"version": 3, "sources": ["D:/No.1 folder/No.2 folder/dev/dev2/vue/base/Vuex_Xshopcart/src/main.js"], "names": ["<PERSON><PERSON>", "App", "store", "config", "productionTip", "render", "h", "$mount"], "mappings": ";;;;AAAA,OAAOA,GAAP,MAAgB,KAAhB;AACA,OAAOC,GAAP,MAAgB,WAAhB;AACA,OAAO,yBAAP;AACA,OAAOC,KAAP,MAAkB,SAAlB;AAEAF,GAAG,CAACG,MAAJ,CAAWC,aAAX,GAA2B,KAA3B;AAEA,IAAIJ,GAAJ,CAAQ;AACNE,EAAAA,KAAK,EAALA,KADM;AAENG,EAAAA,MAAM,EAAE,gBAAAC,CAAC;AAAA,WAAIA,CAAC,CAACL,GAAD,CAAL;AAAA;AAFH,CAAR,EAGGM,MAHH,CAGU,MAHV", "sourcesContent": ["import Vue from 'vue'\nimport App from './App.vue'\nimport './registerServiceWorker'\nimport store from './store'\n\nVue.config.productionTip = false\n\nnew Vue({\n  store,\n  render: h => h(App)\n}).$mount('#app')\n"]}]}