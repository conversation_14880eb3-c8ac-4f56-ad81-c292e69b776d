<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Style Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        #testSoundBtn {
            min-width: 70px;
            max-width: 120px;
            padding: 0.375rem 0.5rem;
            white-space: nowrap;
            font-size: 0.875rem;
            flex-shrink: 0;
        }
        
        .d-flex.gap-2 {
            align-items: stretch;
        }
        
        .d-flex.gap-2 .form-select {
            flex: 1;
        }
        
        .language-test {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Listen Button Style Test</h1>
    
    <div class="test-section">
        <h2>Current Button Layout</h2>
        <p>This shows how the button looks with the current styling:</p>
        
        <div class="mb-3">
            <label for="reminderSound" class="form-label">Reminder Sound</label>
            <div class="d-flex gap-2">
                <select class="form-select" id="reminderSound">
                    <option value="none">No Sound</option>
                    <option value="beep" selected>Beep</option>
                    <option value="chime">Chime</option>
                </select>
                <button type="button" class="btn btn-outline-primary" id="testSoundBtn">Listen</button>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Different Language Tests</h2>
        <p>Test how the button looks with different language texts:</p>
        
        <div class="language-test">
            <strong>English:</strong>
            <div class="d-flex gap-2 mt-2">
                <select class="form-select">
                    <option>No Sound</option>
                </select>
                <button type="button" class="btn btn-outline-primary" style="min-width: 70px; max-width: 120px; padding: 0.375rem 0.5rem; white-space: nowrap; font-size: 0.875rem; flex-shrink: 0;">Listen</button>
            </div>
        </div>
        
        <div class="language-test">
            <strong>Chinese:</strong>
            <div class="d-flex gap-2 mt-2">
                <select class="form-select">
                    <option>无声音</option>
                </select>
                <button type="button" class="btn btn-outline-primary" style="min-width: 70px; max-width: 120px; padding: 0.375rem 0.5rem; white-space: nowrap; font-size: 0.875rem; flex-shrink: 0;">试听</button>
            </div>
        </div>
        
        <div class="language-test">
            <strong>Ukrainian:</strong>
            <div class="d-flex gap-2 mt-2">
                <select class="form-select">
                    <option>Без звуку</option>
                </select>
                <button type="button" class="btn btn-outline-primary" style="min-width: 70px; max-width: 120px; padding: 0.375rem 0.5rem; white-space: nowrap; font-size: 0.875rem; flex-shrink: 0;">Слухати</button>
            </div>
        </div>
        
        <div class="language-test">
            <strong>Russian:</strong>
            <div class="d-flex gap-2 mt-2">
                <select class="form-select">
                    <option>Без звука</option>
                </select>
                <button type="button" class="btn btn-outline-primary" style="min-width: 70px; max-width: 120px; padding: 0.375rem 0.5rem; white-space: nowrap; font-size: 0.875rem; flex-shrink: 0;">Слушать</button>
            </div>
        </div>
        
        <div class="language-test">
            <strong>Dutch:</strong>
            <div class="d-flex gap-2 mt-2">
                <select class="form-select">
                    <option>Geen geluid</option>
                </select>
                <button type="button" class="btn btn-outline-primary" style="min-width: 70px; max-width: 120px; padding: 0.375rem 0.5rem; white-space: nowrap; font-size: 0.875rem; flex-shrink: 0;">Luisteren</button>
            </div>
        </div>
        
        <div class="language-test">
            <strong>Polish:</strong>
            <div class="d-flex gap-2 mt-2">
                <select class="form-select">
                    <option>Bez dźwięku</option>
                </select>
                <button type="button" class="btn btn-outline-primary" style="min-width: 70px; max-width: 120px; padding: 0.375rem 0.5rem; white-space: nowrap; font-size: 0.875rem; flex-shrink: 0;">Słuchaj</button>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Style Analysis</h2>
        <ul>
            <li><strong>min-width: 70px</strong> - 确保按钮有最小宽度</li>
            <li><strong>max-width: 120px</strong> - 限制最大宽度，避免过宽</li>
            <li><strong>padding: 0.375rem 0.5rem</strong> - 适中的内边距</li>
            <li><strong>white-space: nowrap</strong> - 防止文字换行</li>
            <li><strong>font-size: 0.875rem</strong> - 稍小的字体，节省空间</li>
            <li><strong>flex-shrink: 0</strong> - 防止按钮被压缩</li>
        </ul>
    </div>

    <script>
        document.getElementById('testSoundBtn').addEventListener('click', function() {
            alert('Button clicked! Current text: ' + this.textContent);
        });
    </script>
</body>
</html>
