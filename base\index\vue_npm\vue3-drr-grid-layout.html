<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>vue3-drr-grid-layout 只能点击SPAN标签才能拖动</title>
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, minimal-ui">
    <script src="https://www.itxst.com/package/vue3/vue.global.prod.js"></script>
    <script src="https://www.itxst.com/package/vue3-drr-grid/vue3-drr-grid-layout.umd.js">
    </script>
    <link href="https://www.itxst.com/package/vue3-drr-grid/style.css" rel="stylesheet" type="text/css" media="all" />
</head>
<body style="padding:10px;" id="app">
    <div>只能点击SPAN标签才能拖动{{isDraggable}}</div>
    <div id="content" style="width: 50%;">
        <grid-layout v-model:layout="layout" :col-num="6" :row-height="30" :is-resizable="true" :margin="[10, 10]"
            style="padding: 10px;">
            <template #default="{ gridItemProps }">
                <grid-item v-for="item in layout" :key="item.i" v-bind="gridItemProps" :is-draggable="isDraggable"
                    :x="item.x" :y="item.y" :w="item.w" :h="item.h" :i="item.i" :auto-size="false" class="item">
                    <div>
                        <span class="span" @mouseenter="onDraggable" @mouseleave="onDraggable">SPAN标签</span>
                    </div>
                </grid-item>
            </template>
        </grid-layout>
    </div>
    <script>
        Vue.createApp({
            components: {
                'grid-layout': window.Vue3DRRGridLayout.GridLayout,//当前页面注册组件
                'grid-item': window.Vue3DRRGridLayout.GridItem
            },
            data() {
                return {
                    message: 'www.itxst.com',
                    layout: [
                        { x: 0, y: 0, w: 2, h: 2, i: 0 },
                        { x: 2, y: 0, w: 2, h: 2, i: 1 },
                        { x: 4, y: 0, w: 2, h: 2, i: 2 },
                        { x: 0, y: 1, w: 6, h: 2, i: 3 }
                    ],
                    isDraggable: false
                }
            },
            methods: {
                onDraggable() {
                    this.isDraggable = !this.isDraggable;
                }
            },
        }).mount('#app')
    </script>
    <style>
        .droppable-element {
            width: 120px;
            text-align: center;
            background: #fdd;
            border: 1px solid black;
            margin: 10px 0;
            padding: 10px;
        }

        .item {
            display: flex;
            justify-content: space-between;
        }

        .item .span {
            padding: 10px;
            border: solid 1px #ddd;
            background-color: rgb(171, 171, 171);
            color: #000;
            font-size: 11px;
        }
    </style>
</body>
</html>