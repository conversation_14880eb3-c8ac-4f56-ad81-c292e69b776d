{"name": "async-each", "description": "No-bullshit, ultra-simple, 35-lines-of-code async parallel forEach / map function for JavaScript.", "version": "1.0.3", "license": "MIT", "keywords": ["async", "for<PERSON>ach", "each", "map", "asynchronous", "iteration", "iterate", "loop", "parallel", "concurrent", "array", "flow", "control flow"], "files": ["index.js"], "homepage": "https://github.com/paulmillr/async-each/", "author": "<PERSON> (https://paulmillr.com/)", "repository": "git://github.com/paulmillr/async-each.git", "main": "index.js", "dependencies": {}}