{"version": 3, "file": "bufferToggle.js", "sources": ["../../src/internal/operators/bufferToggle.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAGA,gDAA+C;AAC/C,+DAA8D;AAC9D,sDAAqD;AAkDrD,SAAgB,YAAY,CAC1B,QAAkC,EAClC,eAAyD;IAEzD,OAAO,SAAS,4BAA4B,CAAC,MAAqB;QAChE,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,oBAAoB,CAAO,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;IAChF,CAAC,CAAC;AACJ,CAAC;AAPD,oCAOC;AAED;IAEE,8BAAoB,QAAkC,EAClC,eAAyD;QADzD,aAAQ,GAAR,QAAQ,CAA0B;QAClC,oBAAe,GAAf,eAAe,CAA0C;IAC7E,CAAC;IAED,mCAAI,GAAJ,UAAK,UAA2B,EAAE,MAAW;QAC3C,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;IACvG,CAAC;IACH,2BAAC;AAAD,CAAC,AATD,IASC;AAYD;IAA2C,0CAAqB;IAG9D,gCAAY,WAA4B,EAC5B,QAAkC,EAC1B,eAAgE;QAFpF,YAGE,kBAAM,WAAW,CAAC,SAEnB;QAHmB,qBAAe,GAAf,eAAe,CAAiD;QAJ5E,cAAQ,GAA4B,EAAE,CAAC;QAM7C,KAAI,CAAC,GAAG,CAAC,qCAAiB,CAAC,KAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;;IAC9C,CAAC;IAES,sCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAChC;IACH,CAAC;IAES,uCAAM,GAAhB,UAAiB,GAAQ;QACvB,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,IAAM,SAAO,GAAG,QAAQ,CAAC,KAAK,EAAG,CAAC;YAClC,SAAO,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;YACnC,SAAO,CAAC,MAAM,GAAG,IAAK,CAAC;YACvB,SAAO,CAAC,YAAY,GAAG,IAAK,CAAC;SAC9B;QACD,IAAI,CAAC,QAAQ,GAAG,IAAK,CAAC;QACtB,iBAAM,MAAM,YAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IAES,0CAAS,GAAnB;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,IAAM,SAAO,GAAG,QAAQ,CAAC,KAAK,EAAG,CAAC;YAClC,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,SAAO,CAAC,MAAM,CAAC,CAAC;YACvC,SAAO,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;YACnC,SAAO,CAAC,MAAM,GAAG,IAAK,CAAC;YACvB,SAAO,CAAC,YAAY,GAAG,IAAK,CAAC;SAC9B;QACD,IAAI,CAAC,QAAQ,GAAG,IAAK,CAAC;QACtB,iBAAM,SAAS,WAAE,CAAC;IACpB,CAAC;IAED,2CAAU,GAAV,UAAW,UAAe,EAAE,UAAa;QACvC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;IAC1E,CAAC;IAED,+CAAc,GAAd,UAAe,QAA+B;QAC5C,IAAI,CAAC,WAAW,CAAQ,QAAS,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;IAEO,2CAAU,GAAlB,UAAmB,KAAQ;QACzB,IAAI;YACF,IAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;YAC7C,IAAM,eAAe,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC1D,IAAI,eAAe,EAAE;gBACnB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;aACpC;SACF;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SAClB;IACH,CAAC;IAEO,4CAAW,GAAnB,UAAoB,OAAyB;QAC3C,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE/B,IAAI,QAAQ,IAAI,OAAO,EAAE;YACf,IAAA,uBAAM,EAAE,mCAAY,CAAa;YACzC,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,MAAM,CAAC,CAAC;YAC/B,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC1B,YAAY,CAAC,WAAW,EAAE,CAAC;SAC5B;IACH,CAAC;IAEO,6CAAY,GAApB,UAAqB,eAAoB;QACvC,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE/B,IAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAM,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;QACxC,IAAM,OAAO,GAAG,EAAE,MAAM,QAAA,EAAE,YAAY,cAAA,EAAE,CAAC;QACzC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEvB,IAAM,iBAAiB,GAAG,qCAAiB,CAAC,IAAI,EAAE,eAAe,EAAE,OAAc,CAAC,CAAC;QAEnF,IAAI,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,EAAE;YAClD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAC3B;aAAM;YACJ,iBAAyB,CAAC,OAAO,GAAG,OAAO,CAAC;YAE7C,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAC5B,YAAY,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;SACrC;IACH,CAAC;IACH,6BAAC;AAAD,CAAC,AA9FD,CAA2C,iCAAe,GA8FzD"}