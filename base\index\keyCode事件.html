<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body onkeyup="jump(event)">
    <div>
        <input type="text" >
        <!-- οnkeydοwn -->
    </div>
    <script>
        function jump(e){
            let code = e.keyCode
            console.log("%c [ code ]", "font-size:13px; background:#00ffff; color:red;", code)
            if(e.keyCode==34){
                let scrollTop = document.body.scrollTop||document.documentElement.scrollTop
                console.log('scrollTop :>> ', scrollTop);
            }
        }
    </script>
</body>
</html>