{"name": "strip-ansi", "version": "3.0.1", "description": "Strip ANSI escape codes", "license": "MIT", "repository": "chalk/strip-ansi", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": ["Sindre Sorhus <<EMAIL>> (sindresorhus.com)", "<PERSON> <<EMAIL>> (jbna.nl)", "<PERSON><PERSON> <<EMAIL>> (github.com/qix-)"], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^2.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}}