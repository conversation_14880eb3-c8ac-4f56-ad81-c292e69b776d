<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>抖音文案摇一摇</title>
    <!-- 引入外部资源 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">

    <!-- 配置Tailwind自定义主题 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        douyin: '#FE2C55', // 抖音红色
                    },
                    fontFamily: {
                        sans: ['PingFang SC', 'Helvetica Neue', 'Arial', 'sans-serif'],
                    },
                },
            }
        }
    </script>

    <!-- 自定义工具类 -->
    <style type="text/tailwindcss">
        @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .text-shadow {
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
      }
      .backdrop-blur {
        backdrop-filter: blur(8px);
      }
      .shake {
        animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
      }
      @keyframes shake {
        10%, 90% { transform: translate3d(-1px, 0, 0); }
        20%, 80% { transform: translate3d(2px, 0, 0); }
        30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
        40%, 60% { transform: translate3d(4px, 0, 0); }
      }
    }
  </style>
</head>

<body class="bg-black min-h-screen overflow-hidden font-sans">
    <!-- 主容器 -->
    <div id="app" class="relative w-full h-screen flex flex-col items-center justify-center overflow-hidden">

        <!-- 背景图容器 -->
        <div id="background" class="absolute inset-0 z-0 transition-opacity duration-700">
            <img src="https://picsum.photos/id/1005/1080/1920" alt="默认背景图" class="w-full h-full object-cover">
        </div>

        <!-- 渐变遮罩，增强文字可读性 -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-black/50 z-10"></div>

        <!-- 内容区 -->
        <div class="relative z-20 w-full h-full flex flex-col items-center justify-between p-6">

            <!-- 标题区 -->
            <div class="text-center pt-8">
                <h1
                    class="text-white text-[clamp(1.5rem,5vw,2.2rem)] font-bold text-shadow flex items-center justify-center">
                    <i class="fa fa-music mr-2 text-douyin animate-pulse"></i>
                    <!-- 抖音文案摇一摇 -->
                </h1>
                <p id="instructions" class="text-white/90 mt-2 text-[clamp(0.9rem,3vw,1.1rem)]">
                    摇一摇手机，获取一句精彩文案
                </p>
            </div>

            <!-- 文案显示区 -->
            <div id="quote-container"
                class="w-full max-w-md mx-auto bg-white/10 backdrop-blur rounded-2xl p-6 my-4 transform transition-all duration-500 shadow-xl">
                <div id="quote"
                    class="text-white text-[clamp(1.2rem,4vw,1.8rem)] text-center leading-tight min-h-[120px] flex items-center justify-center text-shadow">
                    准备好摇一摇了吗？
                </div>
            </div>

            <!-- 底部操作区 -->
            <div class="text-center pb-12">
                <button id="refresh-btn"
                    class="bg-douyin hover:bg-douyin/90 text-white rounded-full w-16 h-16 flex items-center justify-center mx-auto shadow-lg transform transition-transform hover:scale-110 active:scale-95">
                    <i class="fa fa-refresh text-2xl"></i>
                </button>
                <p class="text-white/80 mt-3 text-sm">或者点击按钮刷新</p>
            </div>
        </div>

        <!-- 摇动动画提示 -->
        <div id="shake-animation"
            class="fixed inset-0 z-30 flex items-center justify-center bg-black/50 opacity-0 pointer-events-none transition-opacity duration-300">
            <div class="text-white text-5xl animate-bounce">
                <i class="fa fa-random"></i>
            </div>
        </div>
    </div>

    <script>
        // 抖音风格文案库，每条包含文案和对应的背景图
        const quotes = [
            {
                text: "生活不是电影，没有那么多不期而遇。",
                image: "https://picsum.photos/id/1011/1080/1920"
            },
            {
                text: "努力的意义，在于可以选择自己想要的生活。",
                image: "https://picsum.photos/id/1012/1080/1920"
            },
            {
                text: "别让未来的你，讨厌现在不努力的自己。",
                image: "https://picsum.photos/id/1013/1080/1920"
            },
            {
                text: "时间会治愈一切，但首先你得给它机会。",
                image: "https://picsum.photos/id/1014/1080/1920"
            },
            {
                text: "人生没有白走的路，每一步都算数。",
                image: "https://picsum.photos/id/1015/1080/1920"
            },
            {
                text: "你若盛开，清风自来。",
                image: "https://picsum.photos/id/1016/1080/1920"
            },
            {
                text: "简单的喜欢最长远，平凡的陪伴最心安。",
                image: "https://picsum.photos/id/1018/1080/1920"
            },
            {
                text: "与其追星星，不如成为像星星一样的人。",
                image: "https://picsum.photos/id/1019/1080/1920"
            },
            {
                text: "生活本来就是一场意外接着另一场意外。",
                image: "https://picsum.photos/id/1022/1080/1920"
            },
            {
                text: "愿你遍历山河，觉得人间值得。",
                image: "https://picsum.photos/id/1023/1080/1920"
            },
            {
                text: "所有的为时已晚，其实都是恰逢其时。",
                image: "https://picsum.photos/id/1024/1080/1920"
            },
            {
                text: "努力只能及格，拼命才能优秀。",
                image: "https://picsum.photos/id/1025/1080/1920"
            },
            {
                text: "把不开心的事藏起来，过几天就找不到了。",
                image: "https://picsum.photos/id/1026/1080/1920"
            },
            {
                text: "人生没有彩排，每天都是现场直播。",
                image: "https://picsum.photos/id/1027/1080/1920"
            },
            {
                text: "先变成更喜欢的自己，再遇到一个不需要取悦的人。",
                image: "https://picsum.photos/id/1028/1080/1920"
            }
        ];

        // DOM元素引用
        const quoteElement = document.getElementById('quote');
        const backgroundElement = document.querySelector('#background img');
        const quoteContainer = document.getElementById('quote-container');
        const refreshBtn = document.getElementById('refresh-btn');
        const shakeAnimation = document.getElementById('shake-animation');
        const instructions = document.getElementById('instructions');

        // 摇一摇检测相关变量
        let lastTime = 0;
        let lastX = 0, lastY = 0, lastZ = 0;
        const SHAKE_THRESHOLD = 18; // 摇动检测阈值
        let isProcessing = false; // 防止重复处理
        let lastQuoteIndex = -1; // 记录上一次显示的文案索引，避免重复

        // 显示新文案和对应背景图
        function showNewQuote() {
            // 确保不重复显示上一条文案
            let randomIndex;
            do {
                randomIndex = Math.floor(Math.random() * quotes.length);
            } while (randomIndex === lastQuoteIndex && quotes.length > 1);

            lastQuoteIndex = randomIndex;
            const quote = quotes[randomIndex];

            // 添加过渡动画效果
            quoteContainer.classList.add('scale-90', 'opacity-0');

            setTimeout(() => {
                // 更新文案内容
                quoteElement.textContent = quote.text;

                // 平滑更新背景图
                backgroundElement.style.opacity = '0';

                setTimeout(() => {
                    backgroundElement.src = quote.image;
                    backgroundElement.style.opacity = '1';

                    // 恢复容器显示状态，带动画效果
                    quoteContainer.classList.remove('scale-90', 'opacity-0');
                    quoteContainer.classList.add('shake');

                    setTimeout(() => {
                        quoteContainer.classList.remove('shake');
                    }, 500);
                }, 300);
            }, 300);
        }

        // 显示摇动动画提示
        function showShakeAnimation() {
            shakeAnimation.style.opacity = '1';
            setTimeout(() => {
                shakeAnimation.style.opacity = '0';
            }, 1000);
        }

        // 处理设备摇动事件
        function handleDeviceMotion(event) {
            if (isProcessing) return;

            const acceleration = event.accelerationIncludingGravity;
            const currentTime = new Date().getTime();

            // 限制检测频率，避免过度触发
            if ((currentTime - lastTime) > 100) {
                const timeDifference = currentTime - lastTime;
                lastTime = currentTime;

                const x = acceleration.x;
                const y = acceleration.y;
                const z = acceleration.z;

                // 计算摇动速度
                const speed = Math.abs(x + y + z - lastX - lastY - lastZ) / timeDifference * 10000;

                // 当摇动强度超过阈值时触发
                if (speed > SHAKE_THRESHOLD) {
                    isProcessing = true;
                    instructions.textContent = "正在生成精彩文案...";
                    // showShakeAnimation();

                    // 延迟显示新文案，增强用户体验
                    setTimeout(() => {
                        showNewQuote();
                        instructions.textContent = "摇一摇手机，获取一句精彩文案";
                        isProcessing = false;
                    }, 800);
                }

                // 更新坐标值
                lastX = x;
                lastY = y;
                lastZ = z;
            }
        }

        // 绑定刷新按钮事件
        refreshBtn.addEventListener('click', () => {
            if (isProcessing) return;

            isProcessing = true;
            // showShakeAnimation();
            instructions.textContent = "正在生成精彩文案...";

            setTimeout(() => {
                showNewQuote();
                instructions.textContent = "摇一摇手机，获取一句精彩文案";
                isProcessing = false;
            }, 800);
        });

        // 初始化摇一摇检测
        if (window.DeviceMotionEvent) {
            window.addEventListener('devicemotion', handleDeviceMotion, false);
        } else {
            // 对不支持摇一摇功能的设备显示提示
            instructions.textContent = "您的设备不支持摇一摇，请点击按钮刷新";
        }

        // 页面加载完成后显示第一条随机文案
        window.addEventListener('load', () => {
            setTimeout(showNewQuote, 600);
        });
    </script>
</body>

</html>