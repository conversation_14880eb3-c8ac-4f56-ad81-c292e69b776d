{"name": "@types/mini-css-extract-plugin", "version": "0.9.1", "description": "TypeScript definitions for mini-css-extract-plugin", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/dobogo", "githubUsername": "dobogo"}, {"name": "<PERSON>", "url": "https://github.com/skovy", "githubUsername": "skovy"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mini-css-extract-plugin"}, "scripts": {}, "dependencies": {"@types/webpack": "*"}, "typesPublisherContentHash": "e73296c487f7655cb2aaa786721de6056dada5e2698cfca5a5e55236d37d9fac", "typeScriptVersion": "2.8"}