{"name": "pinkie-promise", "version": "2.0.1", "description": "ES2015 Promise ponyfill", "license": "MIT", "repository": "floatdrop/pinkie-promise", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["promise", "promises", "es2015", "es6", "polyfill", "ponyfill"], "dependencies": {"pinkie": "^2.0.0"}, "devDependencies": {"mocha": "*"}}