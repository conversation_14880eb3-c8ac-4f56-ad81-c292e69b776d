language: node_js
node_js:
  - "0.10"
  - "0.12"
  - "4"
  - "6"
  - "8"
sudo: false
# use g++-4.8 for karma
cache:
  - apt
addons:
  apt:
    sources:
      - ubuntu-toolchain-r-test
    packages:
      - g++-4.8
      - libstdc++-4.8-dev
  firefox: "39.0"
before_install:
  - export CXX="g++-4.8" CC="gcc-4.8"
  - export "PATH=./node_modules/.bin:$PATH"
install: npm install
# start X server for firefox
before_script:
  - "export DISPLAY=:99.0"
  - "sh -e /etc/init.d/xvfb start"
  - sleep 3 # give xvfb some time to start
script:
  - gulp lint
  - gulp dist
  - gulp test karma
after_success:
  - gulp coveralls

