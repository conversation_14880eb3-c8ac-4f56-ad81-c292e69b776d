// Generated by CoffeeScript 1.9.3
var MarginTop, _Length,
  extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
  hasProp = {}.hasOwnProperty;

_Length = require('./_Length');

module.exports = MarginTop = (function(superClass) {
  extend(MarginTop, superClass);

  function MarginTop() {
    return MarginTop.__super__.constructor.apply(this, arguments);
  }

  return MarginTop;

})(_Length);
