{"name": "object.values", "version": "1.1.1", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ES2017 spec-compliant Object.values shim.", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "posttest": "npx aud", "tests-only": "es-shim-api && npm run test:shimmed && npm run test:module", "test:shimmed": "node test/shimmed", "test:module": "node test/index", "coverage": "covert test/*.js", "lint": "eslint ."}, "repository": {"type": "git", "url": "git://github.com/es-shims/Object.values.git"}, "keywords": ["Object.values", "Object.keys", "Object.entries", "values", "ES7", "ES8", "ES2017", "shim", "object", "keys", "entries", "polyfill", "es-shim API"], "dependencies": {"define-properties": "^1.1.3", "es-abstract": "^1.17.0-next.1", "function-bind": "^1.1.1", "has": "^1.0.3"}, "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^15.0.2", "array-map": "^0.0.0", "covert": "^1.1.1", "eslint": "^6.7.2", "functions-have-names": "^1.2.0", "object-keys": "^1.1.1", "tape": "^4.11.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}}