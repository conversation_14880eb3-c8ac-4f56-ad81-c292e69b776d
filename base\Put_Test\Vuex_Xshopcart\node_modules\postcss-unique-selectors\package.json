{"name": "postcss-unique-selectors", "version": "4.0.1", "description": "Ensure CSS selectors are unique.", "main": "dist/index.js", "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "files": ["LICENSE-MIT", "dist"], "keywords": ["css", "postcss", "postcss-plugin"], "license": "MIT", "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0"}, "dependencies": {"alphanum-sort": "^1.0.0", "postcss": "^7.0.0", "uniqs": "^2.0.0"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": ">=6.9.0"}}