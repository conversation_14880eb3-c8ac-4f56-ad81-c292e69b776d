{"name": "flush-write-stream", "version": "1.1.1", "description": "A write stream constructor that supports a flush function that is called before finish is emitted", "main": "index.js", "dependencies": {"inherits": "^2.0.3", "readable-stream": "^2.3.6"}, "devDependencies": {"tape": "^4.2.2"}, "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/flush-write-stream.git"}, "author": "<PERSON> (@mafintosh)", "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/flush-write-stream/issues"}, "homepage": "https://github.com/mafintosh/flush-write-stream"}