# Installation
> `npm install --save @types/webpack-dev-server`

# Summary
This package contains type definitions for webpack-dev-server (https://github.com/webpack/webpack-dev-server).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webpack-dev-server.

### Additional Details
 * Last updated: Thu, 21 May 2020 22:54:55 GMT
 * Dependencies: [@types/webpack](https://npmjs.com/package/@types/webpack), [@types/http-proxy-middleware](https://npmjs.com/package/@types/http-proxy-middleware), [@types/express](https://npmjs.com/package/@types/express), [@types/serve-static](https://npmjs.com/package/@types/serve-static), [@types/connect-history-api-fallback](https://npmjs.com/package/@types/connect-history-api-fallback)
 * Global values: none

# Credits
These definitions were written by [maestro<PERSON>](https://github.com/maestroh), [<PERSON>](https://github.com/daveparslow), [<PERSON><PERSON><PERSON>](https://github.com/Zheyang<PERSON>ong), [<PERSON> Agius](https://github.com/alan-agius4), [Artur Androsovych](https://github.com/arturovt), [Dave Cardwell](https://github.com/davecardwell), [Katsuya Hino](https://github.com/dobogo), [Billy Le](https://github.com/billy-le), [Chris Paterson](https://github.com/chrispaterson), [Piotr Błażejewicz](https://github.com/peterblazejewicz), and [William Artero](https://github.com/wwmoraes).
