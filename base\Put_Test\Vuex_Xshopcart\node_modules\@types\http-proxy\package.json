{"name": "@types/http-proxy", "version": "1.17.4", "description": "TypeScript definitions for node-http-proxy", "license": "MIT", "contributors": [{"name": "Maxime LUCE", "url": "https://github.com/SomaticIT", "githubUsername": "SomaticIT"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Raigen", "githubUsername": "Raigen"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>", "githubUsername": "<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jabreu610", "githubUsername": "jabreu610"}, {"name": "<PERSON>", "url": "https://github.com/bodinsamuel", "githubUsername": "bod<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/http-proxy"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "6243dacd88efaba343583f5f44980cbb3be6ae00cee1b436ba747611e13e08bf", "typeScriptVersion": "2.8"}