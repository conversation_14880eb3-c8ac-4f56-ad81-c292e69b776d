{"version": 3, "file": "takeWhile.js", "sources": ["../../src/internal/operators/takeWhile.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAEA,4CAA2C;AAmD3C,SAAgB,SAAS,CACrB,SAA+C,EAC/C,SAAiB;IAAjB,0BAAA,EAAA,iBAAiB;IACnB,OAAO,UAAC,MAAqB;QAClB,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IAAxD,CAAwD,CAAC;AACtE,CAAC;AALD,8BAKC;AAED;IACE,2BACY,SAA+C,EAC/C,SAAkB;QADlB,cAAS,GAAT,SAAS,CAAsC;QAC/C,cAAS,GAAT,SAAS,CAAS;IAAG,CAAC;IAElC,gCAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CACnB,IAAI,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IACH,wBAAC;AAAD,CAAC,AATD,IASC;AAOD;IAAqC,uCAAa;IAGhD,6BACI,WAA0B,EAClB,SAA+C,EAC/C,SAAkB;QAH9B,YAIE,kBAAM,WAAW,CAAC,SACnB;QAHW,eAAS,GAAT,SAAS,CAAsC;QAC/C,eAAS,GAAT,SAAS,CAAS;QALtB,WAAK,GAAW,CAAC,CAAC;;IAO1B,CAAC;IAES,mCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,MAAe,CAAC;QACpB,IAAI;YACF,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;SAC9C;QAAC,OAAO,GAAG,EAAE;YACZ,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACvB,OAAO;SACR;QACD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACrC,CAAC;IAEO,4CAAc,GAAtB,UAAuB,KAAQ,EAAE,eAAwB;QACvD,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,OAAO,CAAC,eAAe,CAAC,EAAE;YAC5B,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACzB;aAAM;YACL,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACzB;YACD,WAAW,CAAC,QAAQ,EAAE,CAAC;SACxB;IACH,CAAC;IACH,0BAAC;AAAD,CAAC,AAjCD,CAAqC,uBAAU,GAiC9C"}