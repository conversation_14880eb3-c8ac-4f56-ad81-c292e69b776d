<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <style>
     .name{
        color:red;
      }
    </style>
</head>

<body>
    <div id="app">
        <input type="text" v-model="searchName">
        <ul v-for="(p,index) in filterPersons" :key="index">
            {{index}}--{{p.name}}--{{p.age}}
        </ul>
    
        <button @click="setOrderType(1)">年龄升序</button>
        <button @click="setOrderType(2)">年龄降序</button>
        <button @click="setOrderType(0)">原本顺序</button>
    </div>
    <script src="https://cdn.staticfile.org/vue/2.2.2/vue.min.js"></script>
    <script>
    new Vue({
        el: '#app',
        data: {
            searchName: '',
            orderType: 0,//0代表原来、、1.代表升序，2代表降序
            persons: [//vue本身只是监视了persons的改变，没有监视数组内部数据的变化
                {name: "Tom", age: 18},
                {name: "Tom1", age: 181},
                {name: "Tom2", age: 182},
                {name: "Tom3", age: 183},
            ]
        },
        //计算属性
        computed: {
            filterPersons() {
                const {searchName, persons,orderType} = this
                console.log('this :>> ', this);
                let fPersions;
                fPersions = persons.filter(p => p.name.indexOf(searchName) !== -1)
                // console.log('fPersions :>> ', fPersions);
                if (orderType != 0) {
                    fPersions.sort(function (p1, p2) {
                        console.log('p1,p2 :>> ', p2);
                        if (orderType === 2) {
                            return p2.age - p1.age;
                        } else {
                            return p1.age - p2.age;
                        }
                    })
                }
                return fPersions
            }
        },
        methods: {
            setOrderType(orderType) {
                this.orderType = orderType;
            }
        }
    })
    </script>
</body>

</html>