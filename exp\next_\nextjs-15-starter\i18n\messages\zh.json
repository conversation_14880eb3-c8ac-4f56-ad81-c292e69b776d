{"LanguageDetection": {"title": "语言建议", "description": "检测到你的浏览器语言和当前语言不一样，你随时都可切换语言。", "countdown": "将在 {countdown} 秒后关闭", "switchTo": "切换到"}, "Header": {"links": [{"name": "博客", "href": "/blog"}, {"name": "关于", "href": "/about"}, {"name": "源码", "href": "https://github.com/weijunext/nextjs-15-starter", "target": "_blank", "rel": "noopener noreferrer nofollow"}, {"name": "高级版", "href": "https://nexty.dev", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}]}, "Footer": {"Copyright": "版权所有 © {year} {name} 保留所有权利。", "PrivacyPolicy": "隐私政策", "TermsOfService": "服务条款", "Links": {"groups": [{"title": "语言", "links": [{"href": "/en", "name": "English", "useA": true}, {"href": "/zh", "name": "中文", "useA": true}, {"href": "/ja", "name": "日本語", "useA": true}]}, {"title": "开源项目", "links": [{"href": "https://github.com/weijunext/nextjs-15-starter", "name": "Next Forge", "rel": "noopener noreferrer nofollow", "target": "_blank"}, {"href": "https://github.com/weijunext/landing-page-boilerplate", "name": "Landing Page Boilerplate", "rel": "noopener noreferrer nofollow", "target": "_blank"}, {"href": "https://github.com/weijunext/weekly-boilerplate", "name": "Blog Boilerplate", "rel": "noopener noreferrer nofollow", "target": "_blank"}]}, {"title": "其他产品", "links": [{"href": "https://nexty.dev/", "name": "Nexty - SaaS Template", "rel": "noopener nor<PERSON><PERSON><PERSON>", "target": "_blank"}, {"href": "https://ogimage.click/", "name": "OG Image Generator", "rel": "noopener nor<PERSON><PERSON><PERSON>", "target": "_blank"}, {"href": "https://ntab.dev/", "name": "nTab", "rel": "noopener nor<PERSON><PERSON><PERSON>", "target": "_blank"}]}]}, "Newsletter": {"title": "订阅我们的邮件", "description": "获取最新的 Next.js 资讯和教程", "defaultErrorMessage": "请输入有效的邮箱地址", "successMessage": "订阅成功", "errorMessage": "订阅失败", "errorMessage2": "订阅失败，请稍后再试", "subscribe": "订阅", "subscribing": "订阅中", "subscribed": "订阅成功！感谢您的关注。"}}, "Home": {"title": "Next Forge", "tagLine": "Next.js 多语言启动模板", "description": "内置多语言支持的 Next.js 15 启动模板，助您快速构建面向全球的出海网站。简洁高效，开箱即用，完全优化的SEO基础架构。", "whoIsUsing": "谁在使用此模板"}, "Blog": {"title": "博客列表", "description": "博客列表"}, "About": {"title": "关于", "description": "关于网站"}, "TermsOfService": {"title": "服务条款", "description": "服务条款"}, "PrivacyPolicy": {"title": "隐私政策", "description": "隐私政策"}}