{"name": "html-webpack-plugin", "version": "3.2.0", "license": "MIT", "description": "Simplifies creation of HTML files to serve your webpack bundles", "author": "<PERSON> <<EMAIL>> (https://github.com/ampedandwired)", "main": "index.js", "files": ["lib/", "index.js", "default_index.ejs"], "scripts": {"pretest": "semistandard", "commit": "git-cz", "build-examples": "node examples/build-examples.js", "test": "jasmine", "release": "standard-version"}, "semistandard": {"ignore": ["examples/*/dist/**/*.*"]}, "devDependencies": {"appcache-webpack-plugin": "^1.3.0", "commitizen": "2.9.6", "css-loader": "^0.26.1", "cz-conventional-changelog": "2.1.0", "dir-compare": "1.3.0", "es6-promise": "^4.0.5", "extract-text-webpack-plugin": "^1.0.1", "file-loader": "^0.9.0", "html-loader": "^0.4.4", "jade": "^1.11.0", "jade-loader": "^0.8.0", "jasmine": "^2.5.2", "jasmine-diff-matchers": "^2.0.0", "rimraf": "^2.5.4", "semistandard": "8.0.0", "standard-version": "^4.3.0", "style-loader": "^0.13.1", "underscore-template-loader": "^0.7.3", "url-loader": "^0.5.7", "webpack": "^1.14.0", "webpack-recompilation-simulator": "^1.3.0"}, "dependencies": {"html-minifier": "^3.2.3", "loader-utils": "^0.2.16", "lodash": "^4.17.3", "pretty-error": "^2.0.2", "tapable": "^1.0.0", "toposort": "^1.0.0", "util.promisify": "1.0.0"}, "peerDependencies": {"webpack": "^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0"}, "keywords": ["webpack", "plugin", "html", "html-webpack-plugin"], "bugs": "https://github.com/jantimon/html-webpack-plugin/issues", "homepage": "https://github.com/jantimon/html-webpack-plugin", "repository": "https://github.com/jantimon/html-webpack-plugin.git", "engines": {"node": ">=6.9"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}