<!-- <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <style>
        #box{
            height: 100%;
            width: 100%;
            overflow-y: scroll;
            background-color: blueviolet;
            height: 200px;
        }
        #box div{
            height: 50px;
            background-color: aqua;
        }
    </style>
</head>

<body>
    <div id="app">
        <section id="box">
            <div>盒子</div>
            <div>盒子</div>
            <div>盒子</div>
            <div>盒子</div>
            <div>盒子</div>
            <div>盒子</div>
            <div>盒子</div>
            <div>盒子</div>
            <div>盒子</div>
            <div>盒子</div>
            <div>盒子</div>
            <div>盒子</div>
            <div>盒子</div>
            <div>盒子</div>
            <div>盒子</div>
            <div>盒子</div>
            <div>盒子</div>
            <div>盒子</div>
            <div>盒子</div>
            <div>盒子</div>
        </section>

        <button @click="clickBtn()">点击</button>
    </div>
    <script src="https://cdn.staticfile.org/vue/2.2.2/vue.min.js"></script>
    <script>
    new Vue({
        el: '#app',
        data: {
          
         
        },
        methods: {
            clickBtn() {
                document.querySelector('#box').scrollTop = 120
            }
        }
    })
    </script>
</body>

</html> -->



<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        *{
            margin: 0;
            padding: 0;
        }
        .content{
            height: 1000px;
            background-color: #ccc;
        }
        .box{
            width: 300px;
            height: 200px;
            background-color: pink;
        }
    </style>
</head>
<body>
    <div class="content">content1</div>
    <div class="box">box</div>
    <div class="content">contetn2</div>
    <script>
        //页面一加载出来就滚到到我们想要的位置去(操作整个窗口的滚动)
        //注意点:window.scroll(0,200)表示:页面一打开就滚到到指定位置,所以不能在页面内刷新,必须每次刷新都要重新打开一个页面
        //在页面内刷新实现不了 页面一打开  这个功能,所以也就 window.scroll(0,200)没有效果
        //window.scroll(0,200); //水平滚动距离x,垂直滚动距离y; 
        //window.scrollTo(0,200); //也是操作页面(窗口)滚动,与 window.scroll(0,200)类似
        // window.scrollBy(0,200);//也是操作页面滚动,但是与前两个有差异
        let box = document.querySelector('.box');
        box.onclick = function(){
            // window.scroll(0,200);
            window.scrollBy(0,200);
        }
    </script>
</body>
</html>