'use strict';

module.exports = {
	IsPropertyDescriptor: 'https://ecma-international.org/ecma-262/6.0/#sec-property-descriptor-specification-type', // not actually an abstract op

	abs: 'https://ecma-international.org/ecma-262/7.0/#sec-algorithm-conventions',
	'Abstract Equality Comparison': 'https://ecma-international.org/ecma-262/7.0/#sec-abstract-equality-comparison',
	'Abstract Relational Comparison': 'https://ecma-international.org/ecma-262/7.0/#sec-abstract-relational-comparison',
	AddRestrictedFunctionProperties: 'https://ecma-international.org/ecma-262/7.0/#sec-addrestrictedfunctionproperties',
	AdvanceStringIndex: 'https://ecma-international.org/ecma-262/7.0/#sec-advancestringindex',
	AllocateArrayBuffer: 'https://ecma-international.org/ecma-262/7.0/#sec-allocatearraybuffer',
	AllocateTypedArray: 'https://ecma-international.org/ecma-262/7.0/#sec-allocatetypedarray',
	AllocateTypedArrayBuffer: 'https://ecma-international.org/ecma-262/7.0/#sec-allocatetypedarraybuffer',
	ArrayCreate: 'https://ecma-international.org/ecma-262/7.0/#sec-arraycreate',
	ArraySetLength: 'https://ecma-international.org/ecma-262/7.0/#sec-arraysetlength',
	ArraySpeciesCreate: 'https://ecma-international.org/ecma-262/7.0/#sec-arrayspeciescreate',
	BlockDeclarationInstantiation: 'https://ecma-international.org/ecma-262/7.0/#sec-blockdeclarationinstantiation',
	BoundFunctionCreate: 'https://ecma-international.org/ecma-262/7.0/#sec-boundfunctioncreate',
	Call: 'https://ecma-international.org/ecma-262/7.0/#sec-call',
	Canonicalize: 'https://ecma-international.org/ecma-262/7.0/#sec-runtime-semantics-canonicalize-ch',
	CanonicalNumericIndexString: 'https://ecma-international.org/ecma-262/7.0/#sec-canonicalnumericindexstring',
	CharacterRange: 'https://ecma-international.org/ecma-262/7.0/#sec-runtime-semantics-characterrange-abstract-operation',
	CharacterRangeOrUnion: 'https://ecma-international.org/ecma-262/7.0/#sec-runtime-semantics-characterrangeorunion-abstract-operation',
	CharacterSetMatcher: 'https://ecma-international.org/ecma-262/7.0/#sec-runtime-semantics-charactersetmatcher-abstract-operation',
	CloneArrayBuffer: 'https://ecma-international.org/ecma-262/7.0/#sec-clonearraybuffer',
	CompletePropertyDescriptor: 'https://ecma-international.org/ecma-262/7.0/#sec-completepropertydescriptor',
	Completion: 'https://ecma-international.org/ecma-262/7.0/#sec-completion-record-specification-type',
	Construct: 'https://ecma-international.org/ecma-262/7.0/#sec-construct',
	CopyDataBlockBytes: 'https://ecma-international.org/ecma-262/7.0/#sec-copydatablockbytes',
	CreateArrayFromList: 'https://ecma-international.org/ecma-262/7.0/#sec-createarrayfromlist',
	CreateArrayIterator: 'https://ecma-international.org/ecma-262/7.0/#sec-createarrayiterator',
	CreateBuiltinFunction: 'https://ecma-international.org/ecma-262/7.0/#sec-createbuiltinfunction',
	CreateByteDataBlock: 'https://ecma-international.org/ecma-262/7.0/#sec-createbytedatablock',
	CreateDataProperty: 'https://ecma-international.org/ecma-262/7.0/#sec-createdataproperty',
	CreateDataPropertyOrThrow: 'https://ecma-international.org/ecma-262/7.0/#sec-createdatapropertyorthrow',
	CreateDynamicFunction: 'https://ecma-international.org/ecma-262/7.0/#sec-createdynamicfunction',
	CreateHTML: 'https://ecma-international.org/ecma-262/7.0/#sec-createhtml',
	CreateIntrinsics: 'https://ecma-international.org/ecma-262/7.0/#sec-createintrinsics',
	CreateIterResultObject: 'https://ecma-international.org/ecma-262/7.0/#sec-createiterresultobject',
	CreateListFromArrayLike: 'https://ecma-international.org/ecma-262/7.0/#sec-createlistfromarraylike',
	CreateListIterator: 'https://ecma-international.org/ecma-262/7.0/#sec-createlistiterator',
	CreateMapIterator: 'https://ecma-international.org/ecma-262/7.0/#sec-createmapiterator',
	CreateMappedArgumentsObject: 'https://ecma-international.org/ecma-262/7.0/#sec-createmappedargumentsobject',
	CreateMethodProperty: 'https://ecma-international.org/ecma-262/7.0/#sec-createmethodproperty',
	CreatePerIterationEnvironment: 'https://ecma-international.org/ecma-262/7.0/#sec-createperiterationenvironment',
	CreateRealm: 'https://ecma-international.org/ecma-262/7.0/#sec-createrealm',
	CreateResolvingFunctions: 'https://ecma-international.org/ecma-262/7.0/#sec-createresolvingfunctions',
	CreateSetIterator: 'https://ecma-international.org/ecma-262/7.0/#sec-createsetiterator',
	CreateStringIterator: 'https://ecma-international.org/ecma-262/7.0/#sec-createstringiterator',
	CreateUnmappedArgumentsObject: 'https://ecma-international.org/ecma-262/7.0/#sec-createunmappedargumentsobject',
	DateFromTime: 'https://ecma-international.org/ecma-262/7.0/#sec-date-number',
	Day: 'https://ecma-international.org/ecma-262/7.0/#sec-day-number-and-time-within-day',
	DayFromYear: 'https://ecma-international.org/ecma-262/7.0/#sec-year-number',
	DaysInYear: 'https://ecma-international.org/ecma-262/7.0/#sec-year-number',
	DayWithinYear: 'https://ecma-international.org/ecma-262/7.0/#sec-month-number',
	Decode: 'https://ecma-international.org/ecma-262/7.0/#sec-decode',
	DefinePropertyOrThrow: 'https://ecma-international.org/ecma-262/7.0/#sec-definepropertyorthrow',
	DeletePropertyOrThrow: 'https://ecma-international.org/ecma-262/7.0/#sec-deletepropertyorthrow',
	DetachArrayBuffer: 'https://ecma-international.org/ecma-262/7.0/#sec-detacharraybuffer',
	Encode: 'https://ecma-international.org/ecma-262/7.0/#sec-encode',
	EnqueueJob: 'https://ecma-international.org/ecma-262/7.0/#sec-enqueuejob',
	EnumerableOwnNames: 'https://ecma-international.org/ecma-262/7.0/#sec-enumerableownnames',
	EnumerateObjectProperties: 'https://ecma-international.org/ecma-262/7.0/#sec-enumerate-object-properties',
	EscapeRegExpPattern: 'https://ecma-international.org/ecma-262/7.0/#sec-escaperegexppattern',
	EvalDeclarationInstantiation: 'https://ecma-international.org/ecma-262/7.0/#sec-evaldeclarationinstantiation',
	EvaluateCall: 'https://ecma-international.org/ecma-262/7.0/#sec-evaluatecall',
	EvaluateDirectCall: 'https://ecma-international.org/ecma-262/7.0/#sec-evaluatedirectcall',
	EvaluateNew: 'https://ecma-international.org/ecma-262/7.0/#sec-evaluatenew',
	floor: 'https://ecma-international.org/ecma-262/7.0/#sec-algorithm-conventions',
	ForBodyEvaluation: 'https://ecma-international.org/ecma-262/7.0/#sec-forbodyevaluation',
	'ForIn/OfBodyEvaluation': 'https://ecma-international.org/ecma-262/7.0/#sec-runtime-semantics-forin-div-ofbodyevaluation-lhs-stmt-iterator-lhskind-labelset',
	'ForIn/OfHeadEvaluation': 'https://ecma-international.org/ecma-262/7.0/#sec-runtime-semantics-forin-div-ofheadevaluation-tdznames-expr-iterationkind',
	FromPropertyDescriptor: 'https://ecma-international.org/ecma-262/7.0/#sec-frompropertydescriptor',
	FulfillPromise: 'https://ecma-international.org/ecma-262/7.0/#sec-fulfillpromise',
	FunctionAllocate: 'https://ecma-international.org/ecma-262/7.0/#sec-functionallocate',
	FunctionCreate: 'https://ecma-international.org/ecma-262/7.0/#sec-functioncreate',
	FunctionDeclarationInstantiation: 'https://ecma-international.org/ecma-262/7.0/#sec-functiondeclarationinstantiation',
	FunctionInitialize: 'https://ecma-international.org/ecma-262/7.0/#sec-functioninitialize',
	GeneratorFunctionCreate: 'https://ecma-international.org/ecma-262/7.0/#sec-generatorfunctioncreate',
	GeneratorResume: 'https://ecma-international.org/ecma-262/7.0/#sec-generatorresume',
	GeneratorResumeAbrupt: 'https://ecma-international.org/ecma-262/7.0/#sec-generatorresumeabrupt',
	GeneratorStart: 'https://ecma-international.org/ecma-262/7.0/#sec-generatorstart',
	GeneratorValidate: 'https://ecma-international.org/ecma-262/7.0/#sec-generatorvalidate',
	GeneratorYield: 'https://ecma-international.org/ecma-262/7.0/#sec-generatoryield',
	Get: 'https://ecma-international.org/ecma-262/7.0/#sec-get-o-p',
	GetActiveScriptOrModule: 'https://ecma-international.org/ecma-262/7.0/#sec-getactivescriptormodule',
	GetFunctionRealm: 'https://ecma-international.org/ecma-262/7.0/#sec-getfunctionrealm',
	GetGlobalObject: 'https://ecma-international.org/ecma-262/7.0/#sec-getglobalobject',
	GetIdentifierReference: 'https://ecma-international.org/ecma-262/7.0/#sec-getidentifierreference',
	GetIterator: 'https://ecma-international.org/ecma-262/7.0/#sec-getiterator',
	GetMethod: 'https://ecma-international.org/ecma-262/7.0/#sec-getmethod',
	GetModuleNamespace: 'https://ecma-international.org/ecma-262/7.0/#sec-getmodulenamespace',
	GetNewTarget: 'https://ecma-international.org/ecma-262/7.0/#sec-getnewtarget',
	GetOwnPropertyKeys: 'https://ecma-international.org/ecma-262/7.0/#sec-getownpropertykeys',
	GetPrototypeFromConstructor: 'https://ecma-international.org/ecma-262/7.0/#sec-getprototypefromconstructor',
	GetSubstitution: 'https://ecma-international.org/ecma-262/7.0/#sec-getsubstitution',
	GetSuperConstructor: 'https://ecma-international.org/ecma-262/7.0/#sec-getsuperconstructor',
	GetTemplateObject: 'https://ecma-international.org/ecma-262/7.0/#sec-gettemplateobject',
	GetThisEnvironment: 'https://ecma-international.org/ecma-262/7.0/#sec-getthisenvironment',
	GetThisValue: 'https://ecma-international.org/ecma-262/7.0/#sec-getthisvalue',
	GetV: 'https://ecma-international.org/ecma-262/7.0/#sec-getv',
	GetValue: 'https://ecma-international.org/ecma-262/7.0/#sec-getvalue',
	GetValueFromBuffer: 'https://ecma-international.org/ecma-262/7.0/#sec-getvaluefrombuffer',
	GetViewValue: 'https://ecma-international.org/ecma-262/7.0/#sec-getviewvalue',
	GlobalDeclarationInstantiation: 'https://ecma-international.org/ecma-262/7.0/#sec-globaldeclarationinstantiation',
	HasOwnProperty: 'https://ecma-international.org/ecma-262/7.0/#sec-hasownproperty',
	HasProperty: 'https://ecma-international.org/ecma-262/7.0/#sec-hasproperty',
	HostPromiseRejectionTracker: 'https://ecma-international.org/ecma-262/7.0/#sec-host-promise-rejection-tracker',
	HostReportErrors: 'https://ecma-international.org/ecma-262/7.0/#sec-host-report-errors',
	HostResolveImportedModule: 'https://ecma-international.org/ecma-262/7.0/#sec-hostresolveimportedmodule',
	HourFromTime: 'https://ecma-international.org/ecma-262/7.0/#sec-hours-minutes-second-and-milliseconds',
	IfAbruptRejectPromise: 'https://ecma-international.org/ecma-262/7.0/#sec-ifabruptrejectpromise',
	ImportedLocalNames: 'https://ecma-international.org/ecma-262/7.0/#sec-importedlocalnames',
	InitializeBoundName: 'https://ecma-international.org/ecma-262/7.0/#sec-initializeboundname',
	InitializeHostDefinedRealm: 'https://ecma-international.org/ecma-262/7.0/#sec-initializehostdefinedrealm',
	InitializeReferencedBinding: 'https://ecma-international.org/ecma-262/7.0/#sec-initializereferencedbinding',
	InLeapYear: 'https://ecma-international.org/ecma-262/7.0/#sec-year-number',
	InstanceofOperator: 'https://ecma-international.org/ecma-262/7.0/#sec-instanceofoperator',
	IntegerIndexedElementGet: 'https://ecma-international.org/ecma-262/7.0/#sec-integerindexedelementget',
	IntegerIndexedElementSet: 'https://ecma-international.org/ecma-262/7.0/#sec-integerindexedelementset',
	IntegerIndexedObjectCreate: 'https://ecma-international.org/ecma-262/7.0/#sec-integerindexedobjectcreate',
	InternalizeJSONProperty: 'https://ecma-international.org/ecma-262/7.0/#sec-internalizejsonproperty',
	Invoke: 'https://ecma-international.org/ecma-262/7.0/#sec-invoke',
	IsAccessorDescriptor: 'https://ecma-international.org/ecma-262/7.0/#sec-isaccessordescriptor',
	IsAnonymousFunctionDefinition: 'https://ecma-international.org/ecma-262/7.0/#sec-isanonymousfunctiondefinition',
	IsArray: 'https://ecma-international.org/ecma-262/7.0/#sec-isarray',
	IsCallable: 'https://ecma-international.org/ecma-262/7.0/#sec-iscallable',
	IsCompatiblePropertyDescriptor: 'https://ecma-international.org/ecma-262/7.0/#sec-iscompatiblepropertydescriptor',
	IsConcatSpreadable: 'https://ecma-international.org/ecma-262/7.0/#sec-isconcatspreadable',
	IsConstructor: 'https://ecma-international.org/ecma-262/7.0/#sec-isconstructor',
	IsDataDescriptor: 'https://ecma-international.org/ecma-262/7.0/#sec-isdatadescriptor',
	IsDetachedBuffer: 'https://ecma-international.org/ecma-262/7.0/#sec-isdetachedbuffer',
	IsExtensible: 'https://ecma-international.org/ecma-262/7.0/#sec-isextensible-o',
	IsGenericDescriptor: 'https://ecma-international.org/ecma-262/7.0/#sec-isgenericdescriptor',
	IsInTailPosition: 'https://ecma-international.org/ecma-262/7.0/#sec-isintailposition',
	IsInteger: 'https://ecma-international.org/ecma-262/7.0/#sec-isinteger',
	IsLabelledFunction: 'https://ecma-international.org/ecma-262/7.0/#sec-islabelledfunction',
	IsPromise: 'https://ecma-international.org/ecma-262/7.0/#sec-ispromise',
	IsPropertyKey: 'https://ecma-international.org/ecma-262/7.0/#sec-ispropertykey',
	IsRegExp: 'https://ecma-international.org/ecma-262/7.0/#sec-isregexp',
	IsWordChar: 'https://ecma-international.org/ecma-262/7.0/#sec-runtime-semantics-iswordchar-abstract-operation',
	IterableToArrayLike: 'https://ecma-international.org/ecma-262/7.0/#sec-iterabletoarraylike',
	IteratorClose: 'https://ecma-international.org/ecma-262/7.0/#sec-iteratorclose',
	IteratorComplete: 'https://ecma-international.org/ecma-262/7.0/#sec-iteratorcomplete',
	IteratorNext: 'https://ecma-international.org/ecma-262/7.0/#sec-iteratornext',
	IteratorStep: 'https://ecma-international.org/ecma-262/7.0/#sec-iteratorstep',
	IteratorValue: 'https://ecma-international.org/ecma-262/7.0/#sec-iteratorvalue',
	LocalTime: 'https://ecma-international.org/ecma-262/7.0/#sec-localtime',
	LoopContinues: 'https://ecma-international.org/ecma-262/7.0/#sec-loopcontinues',
	MakeArgGetter: 'https://ecma-international.org/ecma-262/7.0/#sec-makearggetter',
	MakeArgSetter: 'https://ecma-international.org/ecma-262/7.0/#sec-makeargsetter',
	MakeClassConstructor: 'https://ecma-international.org/ecma-262/7.0/#sec-makeclassconstructor',
	MakeConstructor: 'https://ecma-international.org/ecma-262/7.0/#sec-makeconstructor',
	MakeDate: 'https://ecma-international.org/ecma-262/7.0/#sec-makedate',
	MakeDay: 'https://ecma-international.org/ecma-262/7.0/#sec-makeday',
	MakeMethod: 'https://ecma-international.org/ecma-262/7.0/#sec-makemethod',
	MakeSuperPropertyReference: 'https://ecma-international.org/ecma-262/7.0/#sec-makesuperpropertyreference',
	MakeTime: 'https://ecma-international.org/ecma-262/7.0/#sec-maketime',
	max: 'https://ecma-international.org/ecma-262/7.0/#sec-algorithm-conventions',
	min: 'https://ecma-international.org/ecma-262/7.0/#sec-algorithm-conventions',
	MinFromTime: 'https://ecma-international.org/ecma-262/7.0/#sec-hours-minutes-second-and-milliseconds',
	ModuleNamespaceCreate: 'https://ecma-international.org/ecma-262/7.0/#sec-modulenamespacecreate',
	modulo: 'https://ecma-international.org/ecma-262/7.0/#sec-algorithm-conventions',
	MonthFromTime: 'https://ecma-international.org/ecma-262/7.0/#sec-month-number',
	msFromTime: 'https://ecma-international.org/ecma-262/7.0/#sec-hours-minutes-second-and-milliseconds',
	NewDeclarativeEnvironment: 'https://ecma-international.org/ecma-262/7.0/#sec-newdeclarativeenvironment',
	NewFunctionEnvironment: 'https://ecma-international.org/ecma-262/7.0/#sec-newfunctionenvironment',
	NewGlobalEnvironment: 'https://ecma-international.org/ecma-262/7.0/#sec-newglobalenvironment',
	NewModuleEnvironment: 'https://ecma-international.org/ecma-262/7.0/#sec-newmoduleenvironment',
	NewObjectEnvironment: 'https://ecma-international.org/ecma-262/7.0/#sec-newobjectenvironment',
	NewPromiseCapability: 'https://ecma-international.org/ecma-262/7.0/#sec-newpromisecapability',
	NextJob: 'https://ecma-international.org/ecma-262/7.0/#sec-nextjob-result',
	NormalCompletion: 'https://ecma-international.org/ecma-262/7.0/#sec-normalcompletion',
	ObjectCreate: 'https://ecma-international.org/ecma-262/7.0/#sec-objectcreate',
	ObjectDefineProperties: 'https://ecma-international.org/ecma-262/7.0/#sec-objectdefineproperties',
	OrdinaryCallBindThis: 'https://ecma-international.org/ecma-262/7.0/#sec-ordinarycallbindthis',
	OrdinaryCallEvaluateBody: 'https://ecma-international.org/ecma-262/7.0/#sec-ordinarycallevaluatebody',
	OrdinaryCreateFromConstructor: 'https://ecma-international.org/ecma-262/7.0/#sec-ordinarycreatefromconstructor',
	OrdinaryDefineOwnProperty: 'https://ecma-international.org/ecma-262/7.0/#sec-ordinarydefineownproperty',
	OrdinaryDelete: 'https://ecma-international.org/ecma-262/7.0/#sec-ordinarydelete',
	OrdinaryGet: 'https://ecma-international.org/ecma-262/7.0/#sec-ordinaryget',
	OrdinaryGetOwnProperty: 'https://ecma-international.org/ecma-262/7.0/#sec-ordinarygetownproperty',
	OrdinaryGetPrototypeOf: 'https://ecma-international.org/ecma-262/7.0/#sec-ordinarygetprototypeof',
	OrdinaryHasInstance: 'https://ecma-international.org/ecma-262/7.0/#sec-ordinaryhasinstance',
	OrdinaryHasProperty: 'https://ecma-international.org/ecma-262/7.0/#sec-ordinaryhasproperty',
	OrdinaryIsExtensible: 'https://ecma-international.org/ecma-262/7.0/#sec-ordinaryisextensible',
	OrdinaryOwnPropertyKeys: 'https://ecma-international.org/ecma-262/7.0/#sec-ordinaryownpropertykeys',
	OrdinaryPreventExtensions: 'https://ecma-international.org/ecma-262/7.0/#sec-ordinarypreventextensions',
	OrdinarySet: 'https://ecma-international.org/ecma-262/7.0/#sec-ordinaryset',
	OrdinarySetPrototypeOf: 'https://ecma-international.org/ecma-262/7.0/#sec-ordinarysetprototypeof',
	ParseModule: 'https://ecma-international.org/ecma-262/7.0/#sec-parsemodule',
	ParseScript: 'https://ecma-international.org/ecma-262/7.0/#sec-parse-script',
	PerformEval: 'https://ecma-international.org/ecma-262/7.0/#sec-performeval',
	PerformPromiseAll: 'https://ecma-international.org/ecma-262/7.0/#sec-performpromiseall',
	PerformPromiseRace: 'https://ecma-international.org/ecma-262/7.0/#sec-performpromiserace',
	PerformPromiseThen: 'https://ecma-international.org/ecma-262/7.0/#sec-performpromisethen',
	PrepareForOrdinaryCall: 'https://ecma-international.org/ecma-262/7.0/#sec-prepareforordinarycall',
	PrepareForTailCall: 'https://ecma-international.org/ecma-262/7.0/#sec-preparefortailcall',
	PromiseReactionJob: 'https://ecma-international.org/ecma-262/7.0/#sec-promisereactionjob',
	PromiseResolveThenableJob: 'https://ecma-international.org/ecma-262/7.0/#sec-promiseresolvethenablejob',
	ProxyCreate: 'https://ecma-international.org/ecma-262/7.0/#sec-proxycreate',
	PutValue: 'https://ecma-international.org/ecma-262/7.0/#sec-putvalue',
	QuoteJSONString: 'https://ecma-international.org/ecma-262/7.0/#sec-quotejsonstring',
	RegExpAlloc: 'https://ecma-international.org/ecma-262/7.0/#sec-regexpalloc',
	RegExpBuiltinExec: 'https://ecma-international.org/ecma-262/7.0/#sec-regexpbuiltinexec',
	RegExpCreate: 'https://ecma-international.org/ecma-262/7.0/#sec-regexpcreate',
	RegExpExec: 'https://ecma-international.org/ecma-262/7.0/#sec-regexpexec',
	RegExpInitialize: 'https://ecma-international.org/ecma-262/7.0/#sec-regexpinitialize',
	RejectPromise: 'https://ecma-international.org/ecma-262/7.0/#sec-rejectpromise',
	RepeatMatcher: 'https://ecma-international.org/ecma-262/7.0/#sec-runtime-semantics-repeatmatcher-abstract-operation',
	RequireObjectCoercible: 'https://ecma-international.org/ecma-262/7.0/#sec-requireobjectcoercible',
	ResolveBinding: 'https://ecma-international.org/ecma-262/7.0/#sec-resolvebinding',
	ResolveThisBinding: 'https://ecma-international.org/ecma-262/7.0/#sec-resolvethisbinding',
	ReturnIfAbrupt: 'https://ecma-international.org/ecma-262/7.0/#sec-returnifabrupt',
	SameValue: 'https://ecma-international.org/ecma-262/7.0/#sec-samevalue',
	SameValueNonNumber: 'https://ecma-international.org/ecma-262/7.0/#sec-samevaluenonnumber',
	SameValueZero: 'https://ecma-international.org/ecma-262/7.0/#sec-samevaluezero',
	ScriptEvaluation: 'https://ecma-international.org/ecma-262/7.0/#sec-runtime-semantics-scriptevaluation',
	ScriptEvaluationJob: 'https://ecma-international.org/ecma-262/7.0/#sec-scriptevaluationjob',
	SecFromTime: 'https://ecma-international.org/ecma-262/7.0/#sec-hours-minutes-second-and-milliseconds',
	SerializeJSONArray: 'https://ecma-international.org/ecma-262/7.0/#sec-serializejsonarray',
	SerializeJSONObject: 'https://ecma-international.org/ecma-262/7.0/#sec-serializejsonobject',
	SerializeJSONProperty: 'https://ecma-international.org/ecma-262/7.0/#sec-serializejsonproperty',
	Set: 'https://ecma-international.org/ecma-262/7.0/#sec-set-o-p-v-throw',
	SetDefaultGlobalBindings: 'https://ecma-international.org/ecma-262/7.0/#sec-setdefaultglobalbindings',
	SetFunctionName: 'https://ecma-international.org/ecma-262/7.0/#sec-setfunctionname',
	SetIntegrityLevel: 'https://ecma-international.org/ecma-262/7.0/#sec-setintegritylevel',
	SetRealmGlobalObject: 'https://ecma-international.org/ecma-262/7.0/#sec-setrealmglobalobject',
	SetValueInBuffer: 'https://ecma-international.org/ecma-262/7.0/#sec-setvalueinbuffer',
	SetViewValue: 'https://ecma-international.org/ecma-262/7.0/#sec-setviewvalue',
	SortCompare: 'https://ecma-international.org/ecma-262/7.0/#sec-sortcompare',
	SpeciesConstructor: 'https://ecma-international.org/ecma-262/7.0/#sec-speciesconstructor',
	SplitMatch: 'https://ecma-international.org/ecma-262/7.0/#sec-splitmatch',
	'Strict Equality Comparison': 'https://ecma-international.org/ecma-262/7.0/#sec-strict-equality-comparison',
	StringCreate: 'https://ecma-international.org/ecma-262/7.0/#sec-stringcreate',
	SymbolDescriptiveString: 'https://ecma-international.org/ecma-262/7.0/#sec-symboldescriptivestring',
	TestIntegrityLevel: 'https://ecma-international.org/ecma-262/7.0/#sec-testintegritylevel',
	thisBooleanValue: 'https://ecma-international.org/ecma-262/7.0/#sec-thisbooleanvalue',
	thisNumberValue: 'https://ecma-international.org/ecma-262/7.0/#sec-properties-of-the-number-prototype-object',
	thisStringValue: 'https://ecma-international.org/ecma-262/7.0/#sec-properties-of-the-string-prototype-object',
	thisTimeValue: 'https://ecma-international.org/ecma-262/7.0/#sec-properties-of-the-date-prototype-object',
	TimeClip: 'https://ecma-international.org/ecma-262/7.0/#sec-timeclip',
	TimeFromYear: 'https://ecma-international.org/ecma-262/7.0/#sec-year-number',
	TimeWithinDay: 'https://ecma-international.org/ecma-262/7.0/#sec-day-number-and-time-within-day',
	ToBoolean: 'https://ecma-international.org/ecma-262/7.0/#sec-toboolean',
	ToDateString: 'https://ecma-international.org/ecma-262/7.0/#sec-todatestring',
	ToInt16: 'https://ecma-international.org/ecma-262/7.0/#sec-toint16',
	ToInt32: 'https://ecma-international.org/ecma-262/7.0/#sec-toint32',
	ToInt8: 'https://ecma-international.org/ecma-262/7.0/#sec-toint8',
	ToInteger: 'https://ecma-international.org/ecma-262/7.0/#sec-tointeger',
	ToLength: 'https://ecma-international.org/ecma-262/7.0/#sec-tolength',
	ToNumber: 'https://ecma-international.org/ecma-262/7.0/#sec-tonumber',
	ToObject: 'https://ecma-international.org/ecma-262/7.0/#sec-toobject',
	TopLevelModuleEvaluationJob: 'https://ecma-international.org/ecma-262/7.0/#sec-toplevelmoduleevaluationjob',
	ToPrimitive: 'https://ecma-international.org/ecma-262/7.0/#sec-toprimitive',
	ToPropertyDescriptor: 'https://ecma-international.org/ecma-262/7.0/#sec-topropertydescriptor',
	ToPropertyKey: 'https://ecma-international.org/ecma-262/7.0/#sec-topropertykey',
	ToString: 'https://ecma-international.org/ecma-262/7.0/#sec-tostring',
	'ToString Applied to the Number Type': 'https://ecma-international.org/ecma-262/7.0/#sec-tostring-applied-to-the-number-type',
	ToUint16: 'https://ecma-international.org/ecma-262/7.0/#sec-touint16',
	ToUint32: 'https://ecma-international.org/ecma-262/7.0/#sec-touint32',
	ToUint8: 'https://ecma-international.org/ecma-262/7.0/#sec-touint8',
	ToUint8Clamp: 'https://ecma-international.org/ecma-262/7.0/#sec-touint8clamp',
	TriggerPromiseReactions: 'https://ecma-international.org/ecma-262/7.0/#sec-triggerpromisereactions',
	Type: 'https://ecma-international.org/ecma-262/7.0/#sec-ecmascript-data-types-and-values',
	TypedArrayCreate: 'https://ecma-international.org/ecma-262/7.0/#typedarray-create',
	TypedArraySpeciesCreate: 'https://ecma-international.org/ecma-262/7.0/#typedarray-species-create',
	UpdateEmpty: 'https://ecma-international.org/ecma-262/7.0/#sec-updateempty',
	UTC: 'https://ecma-international.org/ecma-262/7.0/#sec-utc-t',
	UTF16Decode: 'https://ecma-international.org/ecma-262/7.0/#sec-utf16decode',
	UTF16Encoding: 'https://ecma-international.org/ecma-262/7.0/#sec-utf16encoding',
	ValidateAndApplyPropertyDescriptor: 'https://ecma-international.org/ecma-262/7.0/#sec-validateandapplypropertydescriptor',
	ValidateTypedArray: 'https://ecma-international.org/ecma-262/7.0/#sec-validatetypedarray',
	WeekDay: 'https://ecma-international.org/ecma-262/7.0/#sec-week-day',
	YearFromTime: 'https://ecma-international.org/ecma-262/7.0/#sec-year-number'
};
