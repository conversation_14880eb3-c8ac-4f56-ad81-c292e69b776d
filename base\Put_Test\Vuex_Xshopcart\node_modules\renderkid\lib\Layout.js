// Generated by CoffeeScript 1.9.3
var Block, Layout, SpecialString, fn, i, len, object, prop, ref, terminalWidth;

Block = require('./layout/Block');

object = require('utila').object;

SpecialString = require('./layout/SpecialString');

terminalWidth = require('./tools').getCols();

module.exports = Layout = (function() {
  var self;

  self = Layout;

  Layout._rootBlockDefaultConfig = {
    linePrependor: {
      options: {
        amount: 0
      }
    },
    lineAppendor: {
      options: {
        amount: 0
      }
    },
    blockPrependor: {
      options: {
        amount: 0
      }
    },
    blockAppendor: {
      options: {
        amount: 0
      }
    }
  };

  Layout._defaultConfig = {
    terminalWidth: terminalWidth
  };

  function Layout(config, rootBlockConfig) {
    var rootConfig;
    if (config == null) {
      config = {};
    }
    if (rootBlockConfig == null) {
      rootBlockConfig = {};
    }
    this._written = [];
    this._activeBlock = null;
    this._config = object.append(self._defaultConfig, config);
    rootConfig = object.append(self._rootBlockDefaultConfig, rootBlockConfig);
    this._root = new Block(this, null, rootConfig, '__root');
    this._root._open();
  }

  Layout.prototype.getRootBlock = function() {
    return this._root;
  };

  Layout.prototype._append = function(text) {
    return this._written.push(text);
  };

  Layout.prototype._appendLine = function(text) {
    var s;
    this._append(text);
    s = SpecialString(text);
    if (s.length < this._config.terminalWidth) {
      this._append('<none>\n</none>');
    }
    return this;
  };

  Layout.prototype.get = function() {
    this._ensureClosed();
    if (this._written[this._written.length - 1] === '<none>\n</none>') {
      this._written.pop();
    }
    return this._written.join("");
  };

  Layout.prototype._ensureClosed = function() {
    if (this._activeBlock !== this._root) {
      throw Error("Not all the blocks have been closed. Please call block.close() on all open blocks.");
    }
    if (this._root.isOpen()) {
      this._root.close();
    }
  };

  return Layout;

})();

ref = ['openBlock', 'write'];
fn = function() {
  var method;
  method = prop;
  return Layout.prototype[method] = function() {
    return this._root[method].apply(this._root, arguments);
  };
};
for (i = 0, len = ref.length; i < len; i++) {
  prop = ref[i];
  fn();
}
