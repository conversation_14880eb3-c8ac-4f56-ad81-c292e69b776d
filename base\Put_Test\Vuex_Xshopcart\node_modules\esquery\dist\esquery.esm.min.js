function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}function t(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var r=[],n=!0,o=!1,a=void 0;try{for(var s,i=e[Symbol.iterator]();!(n=(s=i.next()).done)&&(r.push(s.value),!t||r.length!==t);n=!0);}catch(e){o=!0,a=e}finally{try{n||null==i.return||i.return()}finally{if(o)throw a}}return r}(e,t)||n(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||n(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(e,t){if(e){if("string"==typeof e)return o(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(r):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(e,t):void 0}}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;function a(e,t){return e(t={exports:{}},t.exports),t.exports}var s=a((function(e,t){!function e(t){var r,n,o,a,s,i;function l(e){var t,r,n={};for(t in e)e.hasOwnProperty(t)&&(r=e[t],n[t]="object"==typeof r&&null!==r?l(r):r);return n}function u(e,t){this.parent=e,this.key=t}function c(e,t,r,n){this.node=e,this.path=t,this.wrap=r,this.ref=n}function p(){}function f(e){return null!=e&&("object"==typeof e&&"string"==typeof e.type)}function h(e,t){return(e===r.ObjectExpression||e===r.ObjectPattern)&&"properties"===t}function d(e,t){for(var r=e.length-1;r>=0;--r)if(e[r].node===t)return!0;return!1}function y(e,t){return(new p).traverse(e,t)}function m(e,t){var r;return r=function(e,t){var r,n,o,a;for(n=e.length,o=0;n;)t(e[a=o+(r=n>>>1)])?n=r:(o=a+1,n-=r+1);return o}(t,(function(t){return t.range[0]>e.range[0]})),e.extendedRange=[e.range[0],e.range[1]],r!==t.length&&(e.extendedRange[1]=t[r].range[0]),(r-=1)>=0&&(e.extendedRange[0]=t[r].range[1]),e}return r={AssignmentExpression:"AssignmentExpression",AssignmentPattern:"AssignmentPattern",ArrayExpression:"ArrayExpression",ArrayPattern:"ArrayPattern",ArrowFunctionExpression:"ArrowFunctionExpression",AwaitExpression:"AwaitExpression",BlockStatement:"BlockStatement",BinaryExpression:"BinaryExpression",BreakStatement:"BreakStatement",CallExpression:"CallExpression",CatchClause:"CatchClause",ClassBody:"ClassBody",ClassDeclaration:"ClassDeclaration",ClassExpression:"ClassExpression",ComprehensionBlock:"ComprehensionBlock",ComprehensionExpression:"ComprehensionExpression",ConditionalExpression:"ConditionalExpression",ContinueStatement:"ContinueStatement",DebuggerStatement:"DebuggerStatement",DirectiveStatement:"DirectiveStatement",DoWhileStatement:"DoWhileStatement",EmptyStatement:"EmptyStatement",ExportAllDeclaration:"ExportAllDeclaration",ExportDefaultDeclaration:"ExportDefaultDeclaration",ExportNamedDeclaration:"ExportNamedDeclaration",ExportSpecifier:"ExportSpecifier",ExpressionStatement:"ExpressionStatement",ForStatement:"ForStatement",ForInStatement:"ForInStatement",ForOfStatement:"ForOfStatement",FunctionDeclaration:"FunctionDeclaration",FunctionExpression:"FunctionExpression",GeneratorExpression:"GeneratorExpression",Identifier:"Identifier",IfStatement:"IfStatement",ImportExpression:"ImportExpression",ImportDeclaration:"ImportDeclaration",ImportDefaultSpecifier:"ImportDefaultSpecifier",ImportNamespaceSpecifier:"ImportNamespaceSpecifier",ImportSpecifier:"ImportSpecifier",Literal:"Literal",LabeledStatement:"LabeledStatement",LogicalExpression:"LogicalExpression",MemberExpression:"MemberExpression",MetaProperty:"MetaProperty",MethodDefinition:"MethodDefinition",ModuleSpecifier:"ModuleSpecifier",NewExpression:"NewExpression",ObjectExpression:"ObjectExpression",ObjectPattern:"ObjectPattern",Program:"Program",Property:"Property",RestElement:"RestElement",ReturnStatement:"ReturnStatement",SequenceExpression:"SequenceExpression",SpreadElement:"SpreadElement",Super:"Super",SwitchStatement:"SwitchStatement",SwitchCase:"SwitchCase",TaggedTemplateExpression:"TaggedTemplateExpression",TemplateElement:"TemplateElement",TemplateLiteral:"TemplateLiteral",ThisExpression:"ThisExpression",ThrowStatement:"ThrowStatement",TryStatement:"TryStatement",UnaryExpression:"UnaryExpression",UpdateExpression:"UpdateExpression",VariableDeclaration:"VariableDeclaration",VariableDeclarator:"VariableDeclarator",WhileStatement:"WhileStatement",WithStatement:"WithStatement",YieldExpression:"YieldExpression"},o={AssignmentExpression:["left","right"],AssignmentPattern:["left","right"],ArrayExpression:["elements"],ArrayPattern:["elements"],ArrowFunctionExpression:["params","body"],AwaitExpression:["argument"],BlockStatement:["body"],BinaryExpression:["left","right"],BreakStatement:["label"],CallExpression:["callee","arguments"],CatchClause:["param","body"],ClassBody:["body"],ClassDeclaration:["id","superClass","body"],ClassExpression:["id","superClass","body"],ComprehensionBlock:["left","right"],ComprehensionExpression:["blocks","filter","body"],ConditionalExpression:["test","consequent","alternate"],ContinueStatement:["label"],DebuggerStatement:[],DirectiveStatement:[],DoWhileStatement:["body","test"],EmptyStatement:[],ExportAllDeclaration:["source"],ExportDefaultDeclaration:["declaration"],ExportNamedDeclaration:["declaration","specifiers","source"],ExportSpecifier:["exported","local"],ExpressionStatement:["expression"],ForStatement:["init","test","update","body"],ForInStatement:["left","right","body"],ForOfStatement:["left","right","body"],FunctionDeclaration:["id","params","body"],FunctionExpression:["id","params","body"],GeneratorExpression:["blocks","filter","body"],Identifier:[],IfStatement:["test","consequent","alternate"],ImportExpression:["source"],ImportDeclaration:["specifiers","source"],ImportDefaultSpecifier:["local"],ImportNamespaceSpecifier:["local"],ImportSpecifier:["imported","local"],Literal:[],LabeledStatement:["label","body"],LogicalExpression:["left","right"],MemberExpression:["object","property"],MetaProperty:["meta","property"],MethodDefinition:["key","value"],ModuleSpecifier:[],NewExpression:["callee","arguments"],ObjectExpression:["properties"],ObjectPattern:["properties"],Program:["body"],Property:["key","value"],RestElement:["argument"],ReturnStatement:["argument"],SequenceExpression:["expressions"],SpreadElement:["argument"],Super:[],SwitchStatement:["discriminant","cases"],SwitchCase:["test","consequent"],TaggedTemplateExpression:["tag","quasi"],TemplateElement:[],TemplateLiteral:["quasis","expressions"],ThisExpression:[],ThrowStatement:["argument"],TryStatement:["block","handler","finalizer"],UnaryExpression:["argument"],UpdateExpression:["argument"],VariableDeclaration:["declarations"],VariableDeclarator:["id","init"],WhileStatement:["test","body"],WithStatement:["object","body"],YieldExpression:["argument"]},n={Break:a={},Skip:s={},Remove:i={}},u.prototype.replace=function(e){this.parent[this.key]=e},u.prototype.remove=function(){return Array.isArray(this.parent)?(this.parent.splice(this.key,1),!0):(this.replace(null),!1)},p.prototype.path=function(){var e,t,r,n,o;function a(e,t){if(Array.isArray(t))for(r=0,n=t.length;r<n;++r)e.push(t[r]);else e.push(t)}if(!this.__current.path)return null;for(o=[],e=2,t=this.__leavelist.length;e<t;++e)a(o,this.__leavelist[e].path);return a(o,this.__current.path),o},p.prototype.type=function(){return this.current().type||this.__current.wrap},p.prototype.parents=function(){var e,t,r;for(r=[],e=1,t=this.__leavelist.length;e<t;++e)r.push(this.__leavelist[e].node);return r},p.prototype.current=function(){return this.__current.node},p.prototype.__execute=function(e,t){var r,n;return n=void 0,r=this.__current,this.__current=t,this.__state=null,e&&(n=e.call(this,t.node,this.__leavelist[this.__leavelist.length-1].node)),this.__current=r,n},p.prototype.notify=function(e){this.__state=e},p.prototype.skip=function(){this.notify(s)},p.prototype.break=function(){this.notify(a)},p.prototype.remove=function(){this.notify(i)},p.prototype.__initialize=function(e,t){this.visitor=t,this.root=e,this.__worklist=[],this.__leavelist=[],this.__current=null,this.__state=null,this.__fallback=null,"iteration"===t.fallback?this.__fallback=Object.keys:"function"==typeof t.fallback&&(this.__fallback=t.fallback),this.__keys=o,t.keys&&(this.__keys=Object.assign(Object.create(this.__keys),t.keys))},p.prototype.traverse=function(e,t){var r,n,o,i,l,u,p,y,m,x,g,v;for(this.__initialize(e,t),v={},r=this.__worklist,n=this.__leavelist,r.push(new c(e,null,null,null)),n.push(new c(null,null,null,null));r.length;)if((o=r.pop())!==v){if(o.node){if(u=this.__execute(t.enter,o),this.__state===a||u===a)return;if(r.push(v),n.push(o),this.__state===s||u===s)continue;if(l=(i=o.node).type||o.wrap,!(x=this.__keys[l])){if(!this.__fallback)throw new Error("Unknown node type "+l+".");x=this.__fallback(i)}for(y=x.length;(y-=1)>=0;)if(g=i[p=x[y]])if(Array.isArray(g)){for(m=g.length;(m-=1)>=0;)if(g[m]&&!d(n,g[m])){if(h(l,x[y]))o=new c(g[m],[p,m],"Property",null);else{if(!f(g[m]))continue;o=new c(g[m],[p,m],null,null)}r.push(o)}}else if(f(g)){if(d(n,g))continue;r.push(new c(g,p,null,null))}}}else if(o=n.pop(),u=this.__execute(t.leave,o),this.__state===a||u===a)return},p.prototype.replace=function(e,t){var r,n,o,l,p,d,y,m,x,g,v,A,b;function E(e){var t,n,o,a;if(e.ref.remove())for(n=e.ref.key,a=e.ref.parent,t=r.length;t--;)if((o=r[t]).ref&&o.ref.parent===a){if(o.ref.key<n)break;--o.ref.key}}for(this.__initialize(e,t),v={},r=this.__worklist,n=this.__leavelist,d=new c(e,null,null,new u(A={root:e},"root")),r.push(d),n.push(d);r.length;)if((d=r.pop())!==v){if(void 0!==(p=this.__execute(t.enter,d))&&p!==a&&p!==s&&p!==i&&(d.ref.replace(p),d.node=p),this.__state!==i&&p!==i||(E(d),d.node=null),this.__state===a||p===a)return A.root;if((o=d.node)&&(r.push(v),n.push(d),this.__state!==s&&p!==s)){if(l=o.type||d.wrap,!(x=this.__keys[l])){if(!this.__fallback)throw new Error("Unknown node type "+l+".");x=this.__fallback(o)}for(y=x.length;(y-=1)>=0;)if(g=o[b=x[y]])if(Array.isArray(g)){for(m=g.length;(m-=1)>=0;)if(g[m]){if(h(l,x[y]))d=new c(g[m],[b,m],"Property",new u(g,m));else{if(!f(g[m]))continue;d=new c(g[m],[b,m],null,new u(g,m))}r.push(d)}}else f(g)&&r.push(new c(g,b,null,new u(o,b)))}}else if(d=n.pop(),void 0!==(p=this.__execute(t.leave,d))&&p!==a&&p!==s&&p!==i&&d.ref.replace(p),this.__state!==i&&p!==i||E(d),this.__state===a||p===a)return A.root;return A.root},t.Syntax=r,t.traverse=y,t.replace=function(e,t){return(new p).replace(e,t)},t.attachComments=function(e,t,r){var o,a,s,i,u=[];if(!e.range)throw new Error("attachComments needs range information");if(!r.length){if(t.length){for(s=0,a=t.length;s<a;s+=1)(o=l(t[s])).extendedRange=[0,e.range[0]],u.push(o);e.leadingComments=u}return e}for(s=0,a=t.length;s<a;s+=1)u.push(m(l(t[s]),r));return i=0,y(e,{enter:function(e){for(var t;i<u.length&&!((t=u[i]).extendedRange[1]>e.range[0]);)t.extendedRange[1]===e.range[0]?(e.leadingComments||(e.leadingComments=[]),e.leadingComments.push(t),u.splice(i,1)):i+=1;return i===u.length?n.Break:u[i].extendedRange[0]>e.range[1]?n.Skip:void 0}}),i=0,y(e,{leave:function(e){for(var t;i<u.length&&(t=u[i],!(e.range[1]<t.extendedRange[0]));)e.range[1]===t.extendedRange[0]?(e.trailingComments||(e.trailingComments=[]),e.trailingComments.push(t),u.splice(i,1)):i+=1;return i===u.length?n.Break:u[i].extendedRange[0]>e.range[1]?n.Skip:void 0}}),e},t.VisitorKeys=o,t.VisitorOption=n,t.Controller=p,t.cloneEnvironment=function(){return e({})},t}(t)})),i=a((function(e){e.exports&&(e.exports=function(){function e(t,r,n,o){this.message=t,this.expected=r,this.found=n,this.location=o,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,e)}return function(e,t){function r(){this.constructor=e}r.prototype=t.prototype,e.prototype=new r}(e,Error),e.buildMessage=function(e,t){var r={literal:function(e){return'"'+o(e.text)+'"'},class:function(e){var t,r="";for(t=0;t<e.parts.length;t++)r+=e.parts[t]instanceof Array?a(e.parts[t][0])+"-"+a(e.parts[t][1]):a(e.parts[t]);return"["+(e.inverted?"^":"")+r+"]"},any:function(e){return"any character"},end:function(e){return"end of input"},other:function(e){return e.description}};function n(e){return e.charCodeAt(0).toString(16).toUpperCase()}function o(e){return e.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(e){return"\\x0"+n(e)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(e){return"\\x"+n(e)}))}function a(e){return e.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(e){return"\\x0"+n(e)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(e){return"\\x"+n(e)}))}return"Expected "+function(e){var t,n,o,a=new Array(e.length);for(t=0;t<e.length;t++)a[t]=(o=e[t],r[o.type](o));if(a.sort(),a.length>0){for(t=1,n=1;t<a.length;t++)a[t-1]!==a[t]&&(a[n]=a[t],n++);a.length=n}switch(a.length){case 1:return a[0];case 2:return a[0]+" or "+a[1];default:return a.slice(0,-1).join(", ")+", or "+a[a.length-1]}}(e)+" but "+function(e){return e?'"'+o(e)+'"':"end of input"}(t)+" found."},{SyntaxError:e,parse:function(t,r){r=void 0!==r?r:{};var n,o,a,s,i={},l={start:be},u=be,c=me(" ",!1),p=/^[^ [\],():#!=><~+.]/,f=xe([" ","[","]",",","(",")",":","#","!","=",">","<","~","+","."],!0,!1),h=function(e){return e.join("")},d=me(">",!1),y=me("~",!1),m=me("+",!1),x=me(",",!1),g=me("!",!1),v=me("*",!1),A=me("#",!1),b=me("[",!1),E=me("]",!1),S=/^[><!]/,_=xe([">","<","!"],!1,!1),w=me("=",!1),C=function(e){return(e||"")+"="},P=/^[><]/,k=xe([">","<"],!1,!1),D=me(".",!1),I=function(e,t,r){return{type:"attribute",name:e,operator:t,value:r}},j=me('"',!1),F=/^[^\\"]/,T=xe(["\\",'"'],!0,!1),L=me("\\",!1),R={type:"any"},O=function(e,t){return e+t},B=function(e){return{type:"literal",value:(t=e.join(""),t.replace(/\\(.)/g,(function(e,t){switch(t){case"b":return"\b";case"f":return"\f";case"n":return"\n";case"r":return"\r";case"t":return"\t";case"v":return"\v";default:return t}})))};var t},M=me("'",!1),U=/^[^\\']/,V=xe(["\\","'"],!0,!1),q=/^[0-9]/,N=xe([["0","9"]],!1,!1),W=me("type(",!1),G=/^[^ )]/,z=xe([" ",")"],!0,!1),K=me(")",!1),H=/^[imsu]/,Y=xe(["i","m","s","u"],!1,!1),$=me("/",!1),J=/^[^\/]/,Q=xe(["/"],!0,!1),X=me(":not(",!1),Z=me(":matches(",!1),ee=me(":has(",!1),te=me(":first-child",!1),re=me(":last-child",!1),ne=me(":nth-child(",!1),oe=me(":nth-last-child(",!1),ae=me(":",!1),se=me("statement",!0),ie=me("expression",!0),le=me("declaration",!0),ue=me("function",!0),ce=me("pattern",!0),pe=0,fe=[{line:1,column:1}],he=0,de=[],ye={};if("startRule"in r){if(!(r.startRule in l))throw new Error("Can't start parsing from rule \""+r.startRule+'".');u=l[r.startRule]}function me(e,t){return{type:"literal",text:e,ignoreCase:t}}function xe(e,t,r){return{type:"class",parts:e,inverted:t,ignoreCase:r}}function ge(e){var r,n=fe[e];if(n)return n;for(r=e-1;!fe[r];)r--;for(n={line:(n=fe[r]).line,column:n.column};r<e;)10===t.charCodeAt(r)?(n.line++,n.column=1):n.column++,r++;return fe[e]=n,n}function ve(e,t){var r=ge(e),n=ge(t);return{start:{offset:e,line:r.line,column:r.column},end:{offset:t,line:n.line,column:n.column}}}function Ae(e){pe<he||(pe>he&&(he=pe,de=[]),de.push(e))}function be(){var e,t,r,n,o=30*pe+0,a=ye[o];return a?(pe=a.nextPos,a.result):(e=pe,(t=Ee())!==i&&(r=we())!==i&&Ee()!==i?e=t=1===(n=r).length?n[0]:{type:"matches",selectors:n}:(pe=e,e=i),e===i&&(e=pe,(t=Ee())!==i&&(t=void 0),e=t),ye[o]={nextPos:pe,result:e},e)}function Ee(){var e,r,n=30*pe+1,o=ye[n];if(o)return pe=o.nextPos,o.result;for(e=[],32===t.charCodeAt(pe)?(r=" ",pe++):(r=i,Ae(c));r!==i;)e.push(r),32===t.charCodeAt(pe)?(r=" ",pe++):(r=i,Ae(c));return ye[n]={nextPos:pe,result:e},e}function Se(){var e,r,n,o=30*pe+2,a=ye[o];if(a)return pe=a.nextPos,a.result;if(r=[],p.test(t.charAt(pe))?(n=t.charAt(pe),pe++):(n=i,Ae(f)),n!==i)for(;n!==i;)r.push(n),p.test(t.charAt(pe))?(n=t.charAt(pe),pe++):(n=i,Ae(f));else r=i;return r!==i&&(r=h(r)),e=r,ye[o]={nextPos:pe,result:e},e}function _e(){var e,r,n,o=30*pe+3,a=ye[o];return a?(pe=a.nextPos,a.result):(e=pe,(r=Ee())!==i?(62===t.charCodeAt(pe)?(n=">",pe++):(n=i,Ae(d)),n!==i&&Ee()!==i?e=r="child":(pe=e,e=i)):(pe=e,e=i),e===i&&(e=pe,(r=Ee())!==i?(126===t.charCodeAt(pe)?(n="~",pe++):(n=i,Ae(y)),n!==i&&Ee()!==i?e=r="sibling":(pe=e,e=i)):(pe=e,e=i),e===i&&(e=pe,(r=Ee())!==i?(43===t.charCodeAt(pe)?(n="+",pe++):(n=i,Ae(m)),n!==i&&Ee()!==i?e=r="adjacent":(pe=e,e=i)):(pe=e,e=i),e===i&&(e=pe,32===t.charCodeAt(pe)?(r=" ",pe++):(r=i,Ae(c)),r!==i&&(n=Ee())!==i?e=r="descendant":(pe=e,e=i)))),ye[o]={nextPos:pe,result:e},e)}function we(){var e,r,n,o,a,s,l,u,c=30*pe+4,p=ye[c];if(p)return pe=p.nextPos,p.result;if(e=pe,(r=Ce())!==i){for(n=[],o=pe,(a=Ee())!==i?(44===t.charCodeAt(pe)?(s=",",pe++):(s=i,Ae(x)),s!==i&&(l=Ee())!==i&&(u=Ce())!==i?o=a=[a,s,l,u]:(pe=o,o=i)):(pe=o,o=i);o!==i;)n.push(o),o=pe,(a=Ee())!==i?(44===t.charCodeAt(pe)?(s=",",pe++):(s=i,Ae(x)),s!==i&&(l=Ee())!==i&&(u=Ce())!==i?o=a=[a,s,l,u]:(pe=o,o=i)):(pe=o,o=i);n!==i?e=r=[r].concat(n.map((function(e){return e[3]}))):(pe=e,e=i)}else pe=e,e=i;return ye[c]={nextPos:pe,result:e},e}function Ce(){var e,t,r,n,o,a,s,l=30*pe+5,u=ye[l];if(u)return pe=u.nextPos,u.result;if(e=pe,(t=Pe())!==i){for(r=[],n=pe,(o=_e())!==i&&(a=Pe())!==i?n=o=[o,a]:(pe=n,n=i);n!==i;)r.push(n),n=pe,(o=_e())!==i&&(a=Pe())!==i?n=o=[o,a]:(pe=n,n=i);r!==i?(s=t,e=t=r.reduce((function(e,t){return{type:t[0],left:e,right:t[1]}}),s)):(pe=e,e=i)}else pe=e,e=i;return ye[l]={nextPos:pe,result:e},e}function Pe(){var e,r,n,o,a,s,l,u=30*pe+6,c=ye[u];if(c)return pe=c.nextPos,c.result;if(e=pe,33===t.charCodeAt(pe)?(r="!",pe++):(r=i,Ae(g)),r===i&&(r=null),r!==i){if(n=[],(o=ke())!==i)for(;o!==i;)n.push(o),o=ke();else n=i;n!==i?(a=r,l=1===(s=n).length?s[0]:{type:"compound",selectors:s},a&&(l.subject=!0),e=r=l):(pe=e,e=i)}else pe=e,e=i;return ye[u]={nextPos:pe,result:e},e}function ke(){var e,r=30*pe+7,n=ye[r];return n?(pe=n.nextPos,n.result):((e=function(){var e,r,n=30*pe+8,o=ye[n];return o?(pe=o.nextPos,o.result):(42===t.charCodeAt(pe)?(r="*",pe++):(r=i,Ae(v)),r!==i&&(r={type:"wildcard",value:r}),e=r,ye[n]={nextPos:pe,result:e},e)}())===i&&(e=function(){var e,r,n,o=30*pe+9,a=ye[o];return a?(pe=a.nextPos,a.result):(e=pe,35===t.charCodeAt(pe)?(r="#",pe++):(r=i,Ae(A)),r===i&&(r=null),r!==i&&(n=Se())!==i?e=r={type:"identifier",value:n}:(pe=e,e=i),ye[o]={nextPos:pe,result:e},e)}())===i&&(e=function(){var e,r,n,o,a=30*pe+10,s=ye[a];return s?(pe=s.nextPos,s.result):(e=pe,91===t.charCodeAt(pe)?(r="[",pe++):(r=i,Ae(b)),r!==i&&Ee()!==i&&(n=function(){var e,r,n,o,a=30*pe+14,s=ye[a];return s?(pe=s.nextPos,s.result):(e=pe,(r=De())!==i&&Ee()!==i&&(n=function(){var e,r,n,o=30*pe+12,a=ye[o];return a?(pe=a.nextPos,a.result):(e=pe,33===t.charCodeAt(pe)?(r="!",pe++):(r=i,Ae(g)),r===i&&(r=null),r!==i?(61===t.charCodeAt(pe)?(n="=",pe++):(n=i,Ae(w)),n!==i?(r=C(r),e=r):(pe=e,e=i)):(pe=e,e=i),ye[o]={nextPos:pe,result:e},e)}())!==i&&Ee()!==i?((o=function(){var e,r,n,o,a,s=30*pe+18,l=ye[s];if(l)return pe=l.nextPos,l.result;if(e=pe,"type("===t.substr(pe,5)?(r="type(",pe+=5):(r=i,Ae(W)),r!==i)if(Ee()!==i){if(n=[],G.test(t.charAt(pe))?(o=t.charAt(pe),pe++):(o=i,Ae(z)),o!==i)for(;o!==i;)n.push(o),G.test(t.charAt(pe))?(o=t.charAt(pe),pe++):(o=i,Ae(z));else n=i;n!==i&&(o=Ee())!==i?(41===t.charCodeAt(pe)?(a=")",pe++):(a=i,Ae(K)),a!==i?(r={type:"type",value:n.join("")},e=r):(pe=e,e=i)):(pe=e,e=i)}else pe=e,e=i;else pe=e,e=i;return ye[s]={nextPos:pe,result:e},e}())===i&&(o=function(){var e,r,n,o,a,s,l=30*pe+20,u=ye[l];if(u)return pe=u.nextPos,u.result;if(e=pe,47===t.charCodeAt(pe)?(r="/",pe++):(r=i,Ae($)),r!==i){if(n=[],J.test(t.charAt(pe))?(o=t.charAt(pe),pe++):(o=i,Ae(Q)),o!==i)for(;o!==i;)n.push(o),J.test(t.charAt(pe))?(o=t.charAt(pe),pe++):(o=i,Ae(Q));else n=i;n!==i?(47===t.charCodeAt(pe)?(o="/",pe++):(o=i,Ae($)),o!==i?((a=function(){var e,r,n=30*pe+19,o=ye[n];if(o)return pe=o.nextPos,o.result;if(e=[],H.test(t.charAt(pe))?(r=t.charAt(pe),pe++):(r=i,Ae(Y)),r!==i)for(;r!==i;)e.push(r),H.test(t.charAt(pe))?(r=t.charAt(pe),pe++):(r=i,Ae(Y));else e=i;return ye[n]={nextPos:pe,result:e},e}())===i&&(a=null),a!==i?(s=a,r={type:"regexp",value:new RegExp(n.join(""),s?s.join(""):"")},e=r):(pe=e,e=i)):(pe=e,e=i)):(pe=e,e=i)}else pe=e,e=i;return ye[l]={nextPos:pe,result:e},e}()),o!==i?(r=I(r,n,o),e=r):(pe=e,e=i)):(pe=e,e=i),e===i&&(e=pe,(r=De())!==i&&Ee()!==i&&(n=function(){var e,r,n,o=30*pe+11,a=ye[o];return a?(pe=a.nextPos,a.result):(e=pe,S.test(t.charAt(pe))?(r=t.charAt(pe),pe++):(r=i,Ae(_)),r===i&&(r=null),r!==i?(61===t.charCodeAt(pe)?(n="=",pe++):(n=i,Ae(w)),n!==i?(r=C(r),e=r):(pe=e,e=i)):(pe=e,e=i),e===i&&(P.test(t.charAt(pe))?(e=t.charAt(pe),pe++):(e=i,Ae(k))),ye[o]={nextPos:pe,result:e},e)}())!==i&&Ee()!==i?((o=function(){var e,r,n,o,a,s,l=30*pe+15,u=ye[l];if(u)return pe=u.nextPos,u.result;if(e=pe,34===t.charCodeAt(pe)?(r='"',pe++):(r=i,Ae(j)),r!==i){for(n=[],F.test(t.charAt(pe))?(o=t.charAt(pe),pe++):(o=i,Ae(T)),o===i&&(o=pe,92===t.charCodeAt(pe)?(a="\\",pe++):(a=i,Ae(L)),a!==i?(t.length>pe?(s=t.charAt(pe),pe++):(s=i,Ae(R)),s!==i?(a=O(a,s),o=a):(pe=o,o=i)):(pe=o,o=i));o!==i;)n.push(o),F.test(t.charAt(pe))?(o=t.charAt(pe),pe++):(o=i,Ae(T)),o===i&&(o=pe,92===t.charCodeAt(pe)?(a="\\",pe++):(a=i,Ae(L)),a!==i?(t.length>pe?(s=t.charAt(pe),pe++):(s=i,Ae(R)),s!==i?(a=O(a,s),o=a):(pe=o,o=i)):(pe=o,o=i));n!==i?(34===t.charCodeAt(pe)?(o='"',pe++):(o=i,Ae(j)),o!==i?(r=B(n),e=r):(pe=e,e=i)):(pe=e,e=i)}else pe=e,e=i;if(e===i)if(e=pe,39===t.charCodeAt(pe)?(r="'",pe++):(r=i,Ae(M)),r!==i){for(n=[],U.test(t.charAt(pe))?(o=t.charAt(pe),pe++):(o=i,Ae(V)),o===i&&(o=pe,92===t.charCodeAt(pe)?(a="\\",pe++):(a=i,Ae(L)),a!==i?(t.length>pe?(s=t.charAt(pe),pe++):(s=i,Ae(R)),s!==i?(a=O(a,s),o=a):(pe=o,o=i)):(pe=o,o=i));o!==i;)n.push(o),U.test(t.charAt(pe))?(o=t.charAt(pe),pe++):(o=i,Ae(V)),o===i&&(o=pe,92===t.charCodeAt(pe)?(a="\\",pe++):(a=i,Ae(L)),a!==i?(t.length>pe?(s=t.charAt(pe),pe++):(s=i,Ae(R)),s!==i?(a=O(a,s),o=a):(pe=o,o=i)):(pe=o,o=i));n!==i?(39===t.charCodeAt(pe)?(o="'",pe++):(o=i,Ae(M)),o!==i?(r=B(n),e=r):(pe=e,e=i)):(pe=e,e=i)}else pe=e,e=i;return ye[l]={nextPos:pe,result:e},e}())===i&&(o=function(){var e,r,n,o,a,s,l,u=30*pe+16,c=ye[u];if(c)return pe=c.nextPos,c.result;for(e=pe,r=pe,n=[],q.test(t.charAt(pe))?(o=t.charAt(pe),pe++):(o=i,Ae(N));o!==i;)n.push(o),q.test(t.charAt(pe))?(o=t.charAt(pe),pe++):(o=i,Ae(N));if(n!==i?(46===t.charCodeAt(pe)?(o=".",pe++):(o=i,Ae(D)),o!==i?r=n=[n,o]:(pe=r,r=i)):(pe=r,r=i),r===i&&(r=null),r!==i){if(n=[],q.test(t.charAt(pe))?(o=t.charAt(pe),pe++):(o=i,Ae(N)),o!==i)for(;o!==i;)n.push(o),q.test(t.charAt(pe))?(o=t.charAt(pe),pe++):(o=i,Ae(N));else n=i;n!==i?(s=n,l=(a=r)?[].concat.apply([],a).join(""):"",r={type:"literal",value:parseFloat(l+s.join(""))},e=r):(pe=e,e=i)}else pe=e,e=i;return ye[u]={nextPos:pe,result:e},e}())===i&&(o=function(){var e,t,r=30*pe+17,n=ye[r];return n?(pe=n.nextPos,n.result):((t=Se())!==i&&(t={type:"literal",value:t}),e=t,ye[r]={nextPos:pe,result:e},e)}()),o!==i?(r=I(r,n,o),e=r):(pe=e,e=i)):(pe=e,e=i),e===i&&(e=pe,(r=De())!==i&&(r={type:"attribute",name:r}),e=r)),ye[a]={nextPos:pe,result:e},e)}())!==i&&Ee()!==i?(93===t.charCodeAt(pe)?(o="]",pe++):(o=i,Ae(E)),o!==i?e=r=n:(pe=e,e=i)):(pe=e,e=i),ye[a]={nextPos:pe,result:e},e)}())===i&&(e=function(){var e,r,n,o,a,s,l,u,c=30*pe+21,p=ye[c];if(p)return pe=p.nextPos,p.result;if(e=pe,46===t.charCodeAt(pe)?(r=".",pe++):(r=i,Ae(D)),r!==i)if((n=Se())!==i){for(o=[],a=pe,46===t.charCodeAt(pe)?(s=".",pe++):(s=i,Ae(D)),s!==i&&(l=Se())!==i?a=s=[s,l]:(pe=a,a=i);a!==i;)o.push(a),a=pe,46===t.charCodeAt(pe)?(s=".",pe++):(s=i,Ae(D)),s!==i&&(l=Se())!==i?a=s=[s,l]:(pe=a,a=i);o!==i?(u=n,r={type:"field",name:o.reduce((function(e,t){return e+t[0]+t[1]}),u)},e=r):(pe=e,e=i)}else pe=e,e=i;else pe=e,e=i;return ye[c]={nextPos:pe,result:e},e}())===i&&(e=function(){var e,r,n,o,a=30*pe+22,s=ye[a];return s?(pe=s.nextPos,s.result):(e=pe,":not("===t.substr(pe,5)?(r=":not(",pe+=5):(r=i,Ae(X)),r!==i&&Ee()!==i&&(n=we())!==i&&Ee()!==i?(41===t.charCodeAt(pe)?(o=")",pe++):(o=i,Ae(K)),o!==i?e=r={type:"not",selectors:n}:(pe=e,e=i)):(pe=e,e=i),ye[a]={nextPos:pe,result:e},e)}())===i&&(e=function(){var e,r,n,o,a=30*pe+23,s=ye[a];return s?(pe=s.nextPos,s.result):(e=pe,":matches("===t.substr(pe,9)?(r=":matches(",pe+=9):(r=i,Ae(Z)),r!==i&&Ee()!==i&&(n=we())!==i&&Ee()!==i?(41===t.charCodeAt(pe)?(o=")",pe++):(o=i,Ae(K)),o!==i?e=r={type:"matches",selectors:n}:(pe=e,e=i)):(pe=e,e=i),ye[a]={nextPos:pe,result:e},e)}())===i&&(e=function(){var e,r,n,o,a=30*pe+24,s=ye[a];return s?(pe=s.nextPos,s.result):(e=pe,":has("===t.substr(pe,5)?(r=":has(",pe+=5):(r=i,Ae(ee)),r!==i&&Ee()!==i&&(n=we())!==i&&Ee()!==i?(41===t.charCodeAt(pe)?(o=")",pe++):(o=i,Ae(K)),o!==i?e=r={type:"has",selectors:n}:(pe=e,e=i)):(pe=e,e=i),ye[a]={nextPos:pe,result:e},e)}())===i&&(e=function(){var e,r,n=30*pe+25,o=ye[n];return o?(pe=o.nextPos,o.result):(":first-child"===t.substr(pe,12)?(r=":first-child",pe+=12):(r=i,Ae(te)),r!==i&&(r=Ie(1)),e=r,ye[n]={nextPos:pe,result:e},e)}())===i&&(e=function(){var e,r,n=30*pe+26,o=ye[n];return o?(pe=o.nextPos,o.result):(":last-child"===t.substr(pe,11)?(r=":last-child",pe+=11):(r=i,Ae(re)),r!==i&&(r=je(1)),e=r,ye[n]={nextPos:pe,result:e},e)}())===i&&(e=function(){var e,r,n,o,a,s=30*pe+27,l=ye[s];if(l)return pe=l.nextPos,l.result;if(e=pe,":nth-child("===t.substr(pe,11)?(r=":nth-child(",pe+=11):(r=i,Ae(ne)),r!==i)if(Ee()!==i){if(n=[],q.test(t.charAt(pe))?(o=t.charAt(pe),pe++):(o=i,Ae(N)),o!==i)for(;o!==i;)n.push(o),q.test(t.charAt(pe))?(o=t.charAt(pe),pe++):(o=i,Ae(N));else n=i;n!==i&&(o=Ee())!==i?(41===t.charCodeAt(pe)?(a=")",pe++):(a=i,Ae(K)),a!==i?(r=Ie(parseInt(n.join(""),10)),e=r):(pe=e,e=i)):(pe=e,e=i)}else pe=e,e=i;else pe=e,e=i;return ye[s]={nextPos:pe,result:e},e}())===i&&(e=function(){var e,r,n,o,a,s=30*pe+28,l=ye[s];if(l)return pe=l.nextPos,l.result;if(e=pe,":nth-last-child("===t.substr(pe,16)?(r=":nth-last-child(",pe+=16):(r=i,Ae(oe)),r!==i)if(Ee()!==i){if(n=[],q.test(t.charAt(pe))?(o=t.charAt(pe),pe++):(o=i,Ae(N)),o!==i)for(;o!==i;)n.push(o),q.test(t.charAt(pe))?(o=t.charAt(pe),pe++):(o=i,Ae(N));else n=i;n!==i&&(o=Ee())!==i?(41===t.charCodeAt(pe)?(a=")",pe++):(a=i,Ae(K)),a!==i?(r=je(parseInt(n.join(""),10)),e=r):(pe=e,e=i)):(pe=e,e=i)}else pe=e,e=i;else pe=e,e=i;return ye[s]={nextPos:pe,result:e},e}())===i&&(e=function(){var e,r,n,o=30*pe+29,a=ye[o];return a?(pe=a.nextPos,a.result):(e=pe,58===t.charCodeAt(pe)?(r=":",pe++):(r=i,Ae(ae)),r!==i?("statement"===t.substr(pe,9).toLowerCase()?(n=t.substr(pe,9),pe+=9):(n=i,Ae(se)),n===i&&("expression"===t.substr(pe,10).toLowerCase()?(n=t.substr(pe,10),pe+=10):(n=i,Ae(ie)),n===i&&("declaration"===t.substr(pe,11).toLowerCase()?(n=t.substr(pe,11),pe+=11):(n=i,Ae(le)),n===i&&("function"===t.substr(pe,8).toLowerCase()?(n=t.substr(pe,8),pe+=8):(n=i,Ae(ue)),n===i&&("pattern"===t.substr(pe,7).toLowerCase()?(n=t.substr(pe,7),pe+=7):(n=i,Ae(ce)))))),n!==i?e=r={type:"class",name:n}:(pe=e,e=i)):(pe=e,e=i),ye[o]={nextPos:pe,result:e},e)}()),ye[r]={nextPos:pe,result:e},e)}function De(){var e,r,n,o=30*pe+13,a=ye[o];if(a)return pe=a.nextPos,a.result;if(r=[],(n=Se())===i&&(46===t.charCodeAt(pe)?(n=".",pe++):(n=i,Ae(D))),n!==i)for(;n!==i;)r.push(n),(n=Se())===i&&(46===t.charCodeAt(pe)?(n=".",pe++):(n=i,Ae(D)));else r=i;return r!==i&&(r=h(r)),e=r,ye[o]={nextPos:pe,result:e},e}function Ie(e){return{type:"nth-child",index:{type:"literal",value:e}}}function je(e){return{type:"nth-last-child",index:{type:"literal",value:e}}}if((n=u())!==i&&pe===t.length)return n;throw n!==i&&pe<t.length&&Ae({type:"end"}),o=de,a=he<t.length?t.charAt(he):null,s=he<t.length?ve(he,he+1):ve(he,he),new e(e.buildMessage(o,a),o,a,s)}}}())}));function l(t,r,n){if(!r)return!0;if(!t)return!1;switch(n||(n=[]),r.type){case"wildcard":return!0;case"identifier":return r.value.toLowerCase()===t.type.toLowerCase();case"field":var o=r.name.split("."),a=n[o.length-1];return function e(t,r,n){if(0===n.length)return t===r;if(null==r)return!1;var o=r[n[0]],a=n.slice(1);if(Array.isArray(o)){for(var s=0,i=o.length;s<i;++s)if(e(t,o[s],a))return!0;return!1}return e(t,o,a)}(t,a,o);case"matches":for(var i=0,f=r.selectors.length;i<f;++i)if(l(t,r.selectors[i],n))return!0;return!1;case"compound":for(var h=0,d=r.selectors.length;h<d;++h)if(!l(t,r.selectors[h],n))return!1;return!0;case"not":for(var y=0,m=r.selectors.length;y<m;++y)if(l(t,r.selectors[y],n))return!1;return!0;case"has":var x=function(){for(var e=[],n=function(n,o){var a=[];s.traverse(t,{enter:function(t,o){null!=o&&a.unshift(o),l(t,r.selectors[n],a)&&e.push(t)},leave:function(){a.shift()},fallback:"iteration"})},o=0,a=r.selectors.length;o<a;++o)n(o);return{v:0!==e.length}}();if("object"===e(x))return x.v;case"child":return!!l(t,r.right,n)&&l(n[0],r.left,n.slice(1));case"descendant":if(l(t,r.right,n))for(var g=0,v=n.length;g<v;++g)if(l(n[g],r.left,n.slice(g+1)))return!0;return!1;case"attribute":var A=function(e,t){for(var r=t.split("."),n=0;n<r.length;n++){if(null==e)return e;e=e[r[n]]}return e}(t,r.name);switch(r.operator){case void 0:return null!=A;case"=":switch(r.value.type){case"regexp":return"string"==typeof A&&r.value.value.test(A);case"literal":return"".concat(r.value.value)==="".concat(A);case"type":return r.value.value===e(A)}throw new Error("Unknown selector value type: ".concat(r.value.type));case"!=":switch(r.value.type){case"regexp":return!r.value.value.test(A);case"literal":return"".concat(r.value.value)!=="".concat(A);case"type":return r.value.value!==e(A)}throw new Error("Unknown selector value type: ".concat(r.value.type));case"<=":return A<=r.value.value;case"<":return A<r.value.value;case">":return A>r.value.value;case">=":return A>=r.value.value}throw new Error("Unknown operator: ".concat(r.operator));case"sibling":return l(t,r.right,n)&&u(t,r.left,n,"LEFT_SIDE")||r.left.subject&&l(t,r.left,n)&&u(t,r.right,n,"RIGHT_SIDE");case"adjacent":return l(t,r.right,n)&&c(t,r.left,n,"LEFT_SIDE")||r.right.subject&&l(t,r.left,n)&&c(t,r.right,n,"RIGHT_SIDE");case"nth-child":return l(t,r.right,n)&&p(t,n,(function(){return r.index.value-1}));case"nth-last-child":return l(t,r.right,n)&&p(t,n,(function(e){return e-r.index.value}));case"class":switch(r.name.toLowerCase()){case"statement":if("Statement"===t.type.slice(-9))return!0;case"declaration":return"Declaration"===t.type.slice(-11);case"pattern":if("Pattern"===t.type.slice(-7))return!0;case"expression":return"Expression"===t.type.slice(-10)||"Literal"===t.type.slice(-7)||"Identifier"===t.type&&(0===n.length||"MetaProperty"!==n[0].type)||"MetaProperty"===t.type;case"function":return"FunctionDeclaration"===t.type||"FunctionExpression"===t.type||"ArrowFunctionExpression"===t.type}throw new Error("Unknown class name: ".concat(r.name))}throw new Error("Unknown selector type: ".concat(r.type))}function u(e,r,n,o){var a=t(n,1)[0];if(!a)return!1;for(var i=s.VisitorKeys[a.type],u=0,c=i.length;u<c;++u){var p=a[i[u]];if(Array.isArray(p)){var f=p.indexOf(e);if(f<0)continue;var h=void 0,d=void 0;"LEFT_SIDE"===o?(h=0,d=f):(h=f+1,d=p.length);for(var y=h;y<d;++y)if(l(p[y],r,n))return!0}}return!1}function c(e,r,n,o){var a=t(n,1)[0];if(!a)return!1;for(var i=s.VisitorKeys[a.type],u=0,c=i.length;u<c;++u){var p=a[i[u]];if(Array.isArray(p)){var f=p.indexOf(e);if(f<0)continue;if("LEFT_SIDE"===o&&f>0&&l(p[f-1],r,n))return!0;if("RIGHT_SIDE"===o&&f<p.length-1&&l(p[f+1],r,n))return!0}}return!1}function p(e,r,n){var o=t(r,1)[0];if(!o)return!1;for(var a=s.VisitorKeys[o.type],i=0,l=a.length;i<l;++i){var u=o[a[i]];if(Array.isArray(u)){var c=u.indexOf(e);if(c>=0&&c===n(u.length))return!0}}return!1}function f(n,o){if(null==n||"object"!=e(n))return[];null==o&&(o=n);for(var a=n.subject?[o]:[],s=0,i=function(e){for(var t=[],r=Object.keys(e),n=0;n<r.length;n++)t.push([r[n],e[r[n]]]);return t}(n);s<i.length;s++){var l=t(i[s],2),u=l[0],c=l[1];a.push.apply(a,r(f(c,"left"===u?c:o)))}return a}function h(e,t,r){if(t){var n=[],o=f(t);s.traverse(e,{enter:function(e,a){if(null!=a&&n.unshift(a),l(e,t,n))if(o.length)for(var s=0,i=o.length;s<i;++s){l(e,o[s],n)&&r(e,a,n);for(var u=0,c=n.length;u<c;++u){var p=n.slice(u+1);l(n[u],o[s],p)&&r(n[u],a,p)}}else r(e,a,n)},leave:function(){n.shift()},fallback:"iteration"})}}function d(e,t){var r=[];return h(e,t,(function(e){r.push(e)})),r}function y(e){return i.parse(e)}function m(e,t){return d(e,y(t))}m.parse=y,m.match=d,m.traverse=h,m.matches=l,m.query=m;export default m;
//# sourceMappingURL=esquery.esm.min.js.map
