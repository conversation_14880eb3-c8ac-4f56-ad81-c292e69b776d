require('../proposals/array-last');
require('../proposals/collection-methods');
require('../proposals/collection-of-from');
require('../proposals/keys-composition');
require('../proposals/math-extensions');
require('../proposals/math-signbit');
require('../proposals/number-from-string');
require('../proposals/object-iteration');
require('../proposals/observable');
require('../proposals/pattern-matching');
require('../proposals/promise-try');
require('../proposals/seeded-random');
require('../proposals/string-code-points');
var parent = require('./2');

module.exports = parent;
