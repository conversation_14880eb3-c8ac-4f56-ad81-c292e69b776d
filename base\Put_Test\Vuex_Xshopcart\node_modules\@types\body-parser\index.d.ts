// Type definitions for body-parser 1.19
// Project: https://github.com/expressjs/body-parser
// Definitions by: <PERSON><PERSON> <https://github.com/santialbo>
//                 Vili<PERSON> <https://github.com/vilic>
//                 <PERSON> <https://github.com/dreampulse>
//                 <PERSON><PERSON><PERSON> <https://github.com/blendsdk>
//                 <PERSON><PERSON> <https://github.com/tlazi<PERSON>>
//                 <PERSON> <https://github.com/jwalton>
//                 <PERSON><PERSON><PERSON> <https://github.com/peter<PERSON><PERSON><PERSON><PERSON>>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped

/// <reference types="node" />

import { NextHandleFunction } from 'connect';
import * as http from 'http';

// for docs go to https://github.com/expressjs/body-parser/tree/1.19.0#body-parser

/** @deprecated */
declare function bodyParser(
    options?: bodyParser.OptionsJson & bodyParser.OptionsText & bodyParser.OptionsUrlencoded,
): NextHandleFunction;

declare namespace bodyParser {
    interface Options {
        /** When set to true, then deflated (compressed) bodies will be inflated; when false, deflated bodies are rejected. Defaults to true. */
        inflate?: boolean;
        /**
         * Controls the maximum request body size. If this is a number,
         * then the value specifies the number of bytes; if it is a string,
         * the value is passed to the bytes library for parsing. Defaults to '100kb'.
         */
        limit?: number | string;
        /**
         * The type option is used to determine what media type the middleware will parse
         */
        type?: string | string[] | ((req: http.IncomingMessage) => any);
        /**
         * The verify option, if supplied, is called as verify(req, res, buf, encoding),
         * where buf is a Buffer of the raw request body and encoding is the encoding of the request.
         */
        verify?(req: http.IncomingMessage, res: http.ServerResponse, buf: Buffer, encoding: string): void;
    }

    interface OptionsJson extends Options {
        /**
         *
         * The reviver option is passed directly to JSON.parse as the second argument.
         */
        reviver?(key: string, value: any): any;
        /**
         * When set to `true`, will only accept arrays and objects;
         * when `false` will accept anything JSON.parse accepts. Defaults to `true`.
         */
        strict?: boolean;
    }

    interface OptionsText extends Options {
        /**
         * Specify the default character set for the text content if the charset
         * is not specified in the Content-Type header of the request.
         * Defaults to `utf-8`.
         */
        defaultCharset?: string;
    }

    interface OptionsUrlencoded extends Options {
        /**
         * The extended option allows to choose between parsing the URL-encoded data
         * with the querystring library (when `false`) or the qs library (when `true`).
         */
        extended?: boolean;
        /**
         * The parameterLimit option controls the maximum number of parameters
         * that are allowed in the URL-encoded data. If a request contains more parameters than this value,
         * a 413 will be returned to the client. Defaults to 1000.
         */
        parameterLimit?: number;
    }

    /**
     * Returns middleware that only parses json and only looks at requests
     * where the Content-Type header matches the type option.
     */
    function json(options?: OptionsJson): NextHandleFunction;
    /**
     * Returns middleware that parses all bodies as a Buffer and only looks at requests
     * where the Content-Type header matches the type option.
     */
    function raw(options?: Options): NextHandleFunction;

    /**
     * Returns middleware that parses all bodies as a string and only looks at requests
     * where the Content-Type header matches the type option.
     */
    function text(options?: OptionsText): NextHandleFunction;
    /**
     * Returns middleware that only parses urlencoded bodies and only looks at requests
     * where the Content-Type header matches the type option
     */
    function urlencoded(options?: OptionsUrlencoded): NextHandleFunction;
}

export = bodyParser;
