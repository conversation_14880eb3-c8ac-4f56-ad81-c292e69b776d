<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <script>
    let arr = [1,2,3,4,2,6,8,1]
    if(arr.indexOf(3) > -1){  // 找到 3
        console.log(90909090)
    }
    if(~arr.indexOf(6)){
        console.log('找到6') // 找到 6
    }
    if(!~arr.indexOf(7)){
        console.log('未找到7') // 未找到 7
    }
    // let f1 = arr.indexOf(3) == -1  // 未找到 3 | false
    let f1 = arr.indexOf(0) == -1  // 未找到 0 | true
    console.log("%c [ f1 ]", "font-size:13px; background:#00ffff; color:red;", f1)

    </script>
</body>
</html>