module.exports={A:{A:{"2":"I F E D kB","33":"A B"},B:{"33":"C O P H J K L","132":"M V N WB KB"},C:{"1":"0 1 2 3 4 5 6 7 8 9 v w x Q z AB YB CB JB EB FB GB HB DB BB y T S LB MB NB OB PB QB IB SB M V N iB","2":"tB RB G W jB rB","33":"I F E D A B C O P H J K L X Y Z a b c d e f g h i j k l m n o p q r s t u"},D:{"2":"0 1 2 3 4 5 6 G W I F E D A B C O P H J K L X Y Z a b c d e f g h i j k l m n o p q r s t u v w x Q z","132":"7 8 9 AB YB CB JB EB FB GB HB DB BB y T S LB MB NB OB PB QB IB SB M V N WB KB TB uB aB bB"},E:{"2":"G W cB UB","33":"I F E D A B C O P eB fB gB hB VB R U lB mB"},F:{"2":"D B C H J K L X Y Z a b c d e f g h i j k l m n o p q r s t nB oB pB qB R XB sB U","132":"0 1 2 3 4 5 6 7 8 9 u v w x Q z AB CB EB FB GB HB DB BB y T S"},G:{"2":"UB TC","33":"E ZB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC"},H:{"2":"CC"},I:{"2":"RB G DC EC FC GC ZB HC IC","132":"N"},J:{"2":"F A"},K:{"2":"A B C Q R XB U"},L:{"132":"TB"},M:{"1":"M"},N:{"2":"A B"},O:{"4":"JC"},P:{"1":"LC MC NC OC VB PC QC","2":"G","132":"KC"},Q:{"2":"RC"},R:{"132":"SC"},S:{"1":"dB"}},B:5,C:"CSS Hyphenation"};
