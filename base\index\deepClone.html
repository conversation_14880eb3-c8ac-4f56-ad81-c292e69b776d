<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>deepClone深拷贝</title>
    <script src="http://libs.baidu.com/jquery/1.11.1/jquery.min.js"></script>
</head>
<body>
<script>
        function deepClone(obj){
            let objClone = Array.isArray(obj)?[]:{};
            if(obj && typeof obj==="object"){
                for(key in obj){
                    if(obj.hasOwnProperty(key)){
                        //判断ojb子元素是否为对象，如果是，递归复制
                        if(obj[key] && typeof obj[key] ==="object"){
                            objClone[key] = deepClone(obj[key]);
                        }else{
                            //如果不是，简单复制
                            objClone[key] = obj[key];
                        }
                    }
                }
            }
            return objClone;
        }    
        let a=[1,2,3,4],
        b=deepClone(a);
        a[0]=2;
        console.log('a=>', a); //[2, 2, 3, 4]
        console.log('b=>', b); //[1, 2, 3, 4]



        const obj1 = { a: 1, b: 2 }
        const obj2 = { b: 4, c: 5 }

        const obj3 = Object.assign(obj1, obj2) // { a: 1, b: 4, c: 5 }
        console.log("%c [ obj3 ]", "font-size:13px; background:#00ffff; color:red;", obj3)
</script>
</body>
</html>