{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\n * Call `next()` every time you want to stringify a new value.\n */\nexport type Next = (value: any, key?: PropertyKey) => string | undefined;\n\n/**\n * Stringify a value.\n */\nexport type ToString = (\n  value: any,\n  space: string,\n  next: Next,\n  key: PropertyKey | undefined\n) => string | undefined;\n"]}