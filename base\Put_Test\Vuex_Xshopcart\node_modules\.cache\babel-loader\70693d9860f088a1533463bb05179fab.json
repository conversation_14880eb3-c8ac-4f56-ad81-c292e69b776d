{"remainingRequest": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\store\\cart\\mutations.js", "dependencies": [{"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\store\\cart\\mutations.js", "mtime": 1649424517871}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1657986293299}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1657986298727}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\eslint-loader\\index.js", "mtime": 1657986295398}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9kZWZpbmVQcm9wZXJ0eSBmcm9tICJEOi9Oby4xIGZvbGRlci9Oby4yIGZvbGRlci9kZXYvZGV2Mi92dWUvYmFzZS9WdWV4X1hzaG9wY2FydC9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZGVmaW5lUHJvcGVydHkiOwoKdmFyIF9BREQkUkVEVUNFOwoKaW1wb3J0IHsgQURELCBSRURVQ0UgfSBmcm9tICcuL211dGF0aW9ucy10eXBlJzsKZXhwb3J0IGRlZmF1bHQgKF9BREQkUkVEVUNFID0ge30sIF9kZWZpbmVQcm9wZXJ0eShfQUREJFJFRFVDRSwgQURELCBmdW5jdGlvbiAoc3RhdGUsIHBheWxvYWQpIHsKICAvLyBjb25zb2xlLmxvZyhwYXlsb2FkKQogIC8vIGNvbnNvbGUubG9nKHBheWxvYWQpCiAgc3RhdGUuZ29vZHNbcGF5bG9hZC5pbmRleF0ubnVtKys7CiAgc3RhdGUudG90YWxOdW0rKzsKICBzdGF0ZS50b3RhbFByaWNlICs9IHN0YXRlLmdvb2RzW3BheWxvYWQuaW5kZXhdLnByaWNlOwp9KSwgX2RlZmluZVByb3BlcnR5KF9BREQkUkVEVUNFLCBSRURVQ0UsIGZ1bmN0aW9uIChzdGF0ZSwgcGF5bG9hZCkgewogIGlmIChzdGF0ZS5nb29kc1twYXlsb2FkLmluZGV4XS5udW0gPiAwKSB7CiAgICBzdGF0ZS5nb29kc1twYXlsb2FkLmluZGV4XS5udW0tLTsKICAgIHN0YXRlLnRvdGFsTnVtLS07CiAgICBzdGF0ZS50b3RhbFByaWNlIC09IHN0YXRlLmdvb2RzW3BheWxvYWQuaW5kZXhdLnByaWNlOwogIH0KfSksIF9BREQkUkVEVUNFKTs="}, {"version": 3, "sources": ["D:/No.1 folder/No.2 folder/dev/dev2/vue/base/Vuex_Xshopcart/src/store/cart/mutations.js"], "names": ["ADD", "REDUCE", "state", "payload", "goods", "index", "num", "totalNum", "totalPrice", "price"], "mappings": ";;;;AAAA,SAASA,GAAT,EAAcC,MAAd,QAA4B,kBAA5B;AAEA,+DAEGD,GAFH,YAESE,KAFT,EAEgBC,OAFhB,EAEyB;AACrB;AACA;AACAD,EAAAA,KAAK,CAACE,KAAN,CAAYD,OAAO,CAACE,KAApB,EAA2BC,GAA3B;AACAJ,EAAAA,KAAK,CAACK,QAAN;AACAL,EAAAA,KAAK,CAACM,UAAN,IAAoBN,KAAK,CAACE,KAAN,CAAYD,OAAO,CAACE,KAApB,EAA2BI,KAA/C;AAED,CATH,gCAYGR,MAZH,YAYYC,KAZZ,EAYmBC,OAZnB,EAY4B;AACxB,MAAID,KAAK,CAACE,KAAN,CAAYD,OAAO,CAACE,KAApB,EAA2BC,GAA3B,GAAiC,CAArC,EAAwC;AACtCJ,IAAAA,KAAK,CAACE,KAAN,CAAYD,OAAO,CAACE,KAApB,EAA2BC,GAA3B;AACAJ,IAAAA,KAAK,CAACK,QAAN;AACAL,IAAAA,KAAK,CAACM,UAAN,IAAoBN,KAAK,CAACE,KAAN,CAAYD,OAAO,CAACE,KAApB,EAA2BI,KAA/C;AACD;AACF,CAlBH", "sourcesContent": ["import { ADD, REDUCE } from './mutations-type'\n\nexport default {\n  // 购物车添加\n  [ADD] (state, payload) {\n    // console.log(payload)\n    // console.log(payload)\n    state.goods[payload.index].num++\n    state.totalNum++\n    state.totalPrice += state.goods[payload.index].price\n\n  },\n\n  // 购物车减少\n  [REDUCE] (state, payload) {\n    if (state.goods[payload.index].num > 0) {\n      state.goods[payload.index].num--\n      state.totalNum--\n      state.totalPrice -= state.goods[payload.index].price\n    }\n  }\n}"]}]}