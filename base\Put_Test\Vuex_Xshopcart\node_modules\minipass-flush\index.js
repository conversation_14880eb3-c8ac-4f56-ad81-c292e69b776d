const Minipass = require('minipass')
const _flush = Symbol('_flush')
const _flushed = Symbol('_flushed')
const _flushing = Symbol('_flushing')
class Flush extends Minipass {
  constructor (opt = {}) {
    if (typeof opt === 'function')
      opt = { flush: opt }

    super(opt)

    // or extend this class and provide a 'flush' method in your subclass
    if (typeof opt.flush !== 'function' && typeof this.flush !== 'function')
      throw new TypeError('must provide flush function in options')

    this[_flush] = opt.flush || this.flush
  }

  emit (ev, ...data) {
    if ((ev !== 'end' && ev !== 'finish') || this[_flushed])
      return super.emit(ev, ...data)

    if (this[_flushing])
      return

    this[_flushing] = true

    const afterFlush = er => {
      this[_flushed] = true
      er ? super.emit('error', er) : super.emit('end')
    }

    const ret = this[_flush](afterFlush)
    if (ret && ret.then)
      ret.then(() => afterFlush(), er => afterFlush(er))
  }
}

module.exports = Flush
