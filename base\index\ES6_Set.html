<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <script>
        // const setA = new Set();
        // let arr = [2,3,4,5,2,2]
        // arr.forEach(x=>setA.add(x)); 
        // //Set(4) {2, 3, 4, 5}
        // // 0: 2
        // // 1: 3
        // // 2: 4
        // // 3: 5

        // let arr2 = Array.from(new Set(arr)) // [2, 3, 4, 5]
        
        
        
    </script>
</body>
</html>