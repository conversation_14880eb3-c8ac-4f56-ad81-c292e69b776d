{"name": "@types/express", "version": "4.17.8", "description": "TypeScript definitions for Express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "*", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "96005d6f6692d0f0137502480774a884f89e10f2b3d6fc74e5f9e69501336a5f", "typeScriptVersion": "3.1"}