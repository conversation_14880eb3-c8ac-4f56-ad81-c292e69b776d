<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
<title>教你制作three.js酷炫Web全景</title>
    <meta name="viewport" id="viewport" content="width=device-width,initial-scale=1,minimum-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover">
</head>
<body>
<!-- <div id="wrap" style="position: absolute;z-index: 0;top: 0;bottom: 0;left: 0;right: 0;width: 100%;height: 100%;overflow: hidden;">
</div>
<script src="https://cdn.bootcdn.net/ajax/libs/three.js/r128/three.js"></script>
<script>
    const width = window.innerWidth
    const height = window.innerHeight
    const radius = 500 // 球体半径

    // 第一步：创建场景
    const scene = new THREE.Scene()  //scene: 镜头,场景

    // 第二步：绘制一个球体
    const geometry = new THREE.SphereBufferGeometry(radius, 32, 32)  // SphereBufferGeometry: 球体缓冲区几何
    const material = new THREE.MeshBasicMaterial({                   // MeshBasicMaterial: 网格基础材料
        map: new THREE.TextureLoader().load('https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg.zcool.cn%2Fcommunity%2F01a5555e708712a8012165187ca0e4.jpg%403000w_1l_0o_100sh.jpg&refer=http%3A%2F%2Fimg.zcool.cn&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1642302788&t=45233dffd33d56f9e0253afa93489e69')          // TextureLoader: 纹理加载器  上面的全景图片  
    })
    const mesh = new THREE.Mesh(geometry, material)
    scene.add(mesh)

    // 第三步：创建相机，并确定相机位置
    const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 100) // PerspectiveCamera: 透视相机
    camera.position.x = 0  // 确定相机位置
    camera.position.y = 0
    camera.position.z = 100
    
    camera.target = new THREE.Vector3(radius, 0, 0) // 设定对焦点
    

    // 第四步：拍照并绘制到 canvas
    const renderer = new THREE.WebGLRenderer()   // WebGLRenderer: WebGL渲染器
    renderer.setSize(width, height) // 设置照片大小

    document.querySelector('#wrap').appendChild(renderer.domElement) // 绘制到canvas

    function render() {
        camera.lookAt(camera.target)   // 对焦
        renderer.render(scene, camera) // 拍照
        
        // 不断渲染，因为图片加载和处理需要时间，不确定何时拍照合适
        requestAnimationFrame(render)  //requestAnimationFrame: 请求动画帧
    }
    render()
</script>
</body>

</html>
 -->





 <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>手把手教你制作酷炫Web全景</title>
    <meta name="viewport" id="viewport" content="width=device-width,initial-scale=1,minimum-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover">
</head>
<body>
    <div id="wrap" style="position: absolute;z-index: 0;top: 0;bottom: 0;left: 0;right: 0;width: 100%;height: 100%;overflow: hidden;">
    </div>
    <script src="https://cdn.bootcdn.net/ajax/libs/three.js/r128/three.js"></script>
    <script>
        const width = window.innerWidth, height = window.innerHeight // 屏幕宽高
        const radius = 50 // 球体半径

        // 第一步：创建场景
        const scene = new THREE.Scene()

        // 第二步：绘制一个球体
        const geometry = new THREE.SphereBufferGeometry(radius, 32, 32)
        geometry.scale(-1, 1, 1) // 球面反转，由外表面改成内表面贴图
        const material = new THREE.MeshBasicMaterial({
            // TextureLoader: 纹理加载器  上面的全景图片 
            map: new THREE.TextureLoader().load('https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg.zcool.cn%2Fcommunity%2F01a5555e708712a8012165187ca0e4.jpg%403000w_1l_0o_100sh.jpg&refer=http%3A%2F%2Fimg.zcool.cn&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1642302788&t=45233dffd33d56f9e0253afa93489e69')
        })
        const mesh = new THREE.Mesh(geometry, material)
        scene.add(mesh)

        // 第三步：创建相机，并确定相机位置
        const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 100)
        camera.position.x = 0  // 确定相机位置移到球心
        camera.position.y = 0
        camera.position.z = 0

        camera.target = new THREE.Vector3(radius, 0, 0) // 设置一个对焦点
      

        // 第四步：拍照并绘制到canvas
        const renderer = new THREE.WebGLRenderer()
        renderer.setPixelRatio(window.devicePixelRatio)
        renderer.setSize(width, height) // 设置照片大小

        document.querySelector('#wrap').appendChild(renderer.domElement) // 绘制到canvas
        renderer.render(scene, camera)

        let lat = 0, lon = 0

        function render() {
            lon += 0.003 // 每帧加一个偏移量
            // 改变相机的对焦点，计算公式参考：2.2.2章节
            camera.target.x = radius * Math.cos(lat) * Math.cos(lon);
            camera.target.y = radius * Math.sin(lat);
            camera.target.z = radius * Math.cos(lat) * Math.sin(lon)
            camera.lookAt(camera.target) // 对焦

            renderer.render(scene, camera)
            requestAnimationFrame(render)
        }
        render()
    </script>
</body>

</html>
