{"name": "dns-equal", "version": "1.0.0", "description": "Compare DNS record strings for equality", "main": "index.js", "dependencies": {}, "devDependencies": {"standard": "^5.4.1"}, "scripts": {"test": "standard && node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/watson/dns-equal.git"}, "keywords": ["dns", "compare", "comparing", "equal", "equality", "match", "downcase", "lowercase", "case-insensitive"], "author": "<PERSON> <<EMAIL>> (https://twitter.com/wa7son)", "license": "MIT", "bugs": {"url": "https://github.com/watson/dns-equal/issues"}, "homepage": "https://github.com/watson/dns-equal#readme", "coordinates": [56.010004025953165, 11.961870541375674]}