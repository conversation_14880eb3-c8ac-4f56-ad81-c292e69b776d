{"remainingRequest": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\store\\cart\\getters.js", "dependencies": [{"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\store\\cart\\getters.js", "mtime": 1658035215248}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1657986293299}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1657986298727}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\eslint-loader\\index.js", "mtime": 1657986295398}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyIjsKZXhwb3J0IGRlZmF1bHQgewogIGdvb2RzT2JqOiBmdW5jdGlvbiBnb29kc09iaihzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLmdvb2RzOwogIH0sCiAgZ2V0QnlJZDogZnVuY3Rpb24gZ2V0QnlJZChzdGF0ZSkgewogICAgcmV0dXJuIGZ1bmN0aW9uIChpZCkgewogICAgICByZXR1cm4gc3RhdGUuZ29vZHMuZmlsdGVyKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0uaWQgLSAwID09PSBpZDsKICAgICAgfSk7CiAgICB9OwogIH0gLy8gZ2V0QnlJZDE6KHN0YXRlKSA9PiAoaWQpID0+IHsKICAvLyAgIHJldHVybiBzdGF0ZS5nb29kcy5maWx0ZXIoIGl0ZW0gPT4gaXRlbS5pZD09PWlkICkKICAvLyB9Cgp9Ow=="}, {"version": 3, "sources": ["D:/No.1 folder/No.2 folder/dev/dev2/vue/base/Vuex_Xshopcart/src/store/cart/getters.js"], "names": ["goodsObj", "state", "goods", "getById", "id", "filter", "item"], "mappings": ";AAAA,eAAe;AACbA,EAAAA,QAAQ,EAAE,kBAAAC,KAAK,EAAI;AACjB,WAAOA,KAAK,CAACC,KAAb;AACD,GAHY;AAIbC,EAAAA,OAAO,EAAE,iBAACF,KAAD;AAAA,WAAW,UAACG,EAAD,EAAQ;AAC1B,aAAOH,KAAK,CAACC,KAAN,CAAYG,MAAZ,CAAmB,UAACC,IAAD;AAAA,eAAYA,IAAI,CAACF,EAAL,GAAU,CAAX,KAAkBA,EAA7B;AAAA,OAAnB,CAAP;AACD,KAFQ;AAAA,GAJI,CAOb;AACA;AACA;;AATa,CAAf", "sourcesContent": ["export default {\n  goodsObj: state => {\n    return state.goods\n  },\n  getById: (state) => (id) => {\n    return state.goods.filter((item) => ((item.id - 0) === id))\n  }\n  // getById1:(state) => (id) => {\n  //   return state.goods.filter( item => item.id===id )\n  // }\n}"]}]}