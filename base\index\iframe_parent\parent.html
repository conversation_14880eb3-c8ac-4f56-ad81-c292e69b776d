<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title> parent.html </title>
</head>
<body>
<iframe
  id='testIframe'
  name='test'
  src="http://127.0.0.1:3000/base/index/iframe_child/child.html"
  frameborder='0'
  scrolling='no'>
</iframe>
<div>我是parent.html</div>
<script type="text/javascript">
    // 调用户信息接口成功==> 👇
    var iframeDom = document.getElementById('testIframe');
    var flag = false;
    // 父页面发送信息
    iframeDom.onload = function () {
        flag = true;
        // iframeDom.contentWindow.childConsole(data);
        test.window.postMessage(flag, 'http://127.0.0.1:3000/base/index/iframe_child/child.html');//我们项目前端首页地址
    }
</script>
</body>
</html>