{"curly": true, "globalstrict": true, "eqeqeq": true, "immed": true, "latedef": true, "newcap": true, "noarg": true, "sub": true, "undef": true, "boss": true, "eqnull": true, "es3": true, "globals": {"window": true, "console": true, "define": false, "require": false, "exports": false, "_": false, "afterEach": false, "beforeEach": false, "confirm": false, "context": false, "describe": false, "xdescribe": false, "expect": false, "it": false, "jasmine": false, "waitsFor": false, "runs": false, "Symbol": false}}