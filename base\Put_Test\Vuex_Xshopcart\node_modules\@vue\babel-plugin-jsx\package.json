{"name": "@vue/babel-plugin-jsx", "version": "1.0.0-rc.2", "description": "Babel plugin for Vue 3.0 JSX", "author": "Amour1688 <<EMAIL>>", "homepage": "https://github.com/vueComponent/jsx/tree/master/packages/babel-plugin-jsx#readme", "license": "MIT", "main": "dist/index.js", "repository": {"type": "git", "url": "git+https://github.com/vueComponent/jsx.git"}, "scripts": {"dev": "npm run build && webpack-dev-server", "build": "tsc", "lint": "eslint 'src/*.ts'", "test": "npm run build && jest --coverage"}, "bugs": {"url": "https://github.com/vueComponent/jsx/issues"}, "files": ["dist"], "dependencies": {"@babel/helper-module-imports": "^7.0.0", "@babel/plugin-syntax-jsx": "^7.0.0", "@babel/traverse": "^7.0.0", "@babel/types": "^7.0.0", "@vue/babel-helper-vue-transform-on": "^1.0.0-rc.2", "camelcase": "^6.0.0", "html-tags": "^3.1.0", "svg-tags": "^1.0.0"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/preset-typescript": "^7.10.4", "@rollup/plugin-babel": "^5.0.3", "@types/jest": "^26.0.7", "@types/svg-tags": "^1.0.0", "@typescript-eslint/eslint-plugin": "^3.6.1", "@typescript-eslint/parser": "^3.6.1", "@vue/compiler-dom": "3.0.0-rc.9", "@vue/test-utils": "2.0.0-beta.2", "babel-jest": "^26.0.1", "babel-loader": "^8.1.0", "jest": "^26.0.1", "regenerator-runtime": "^0.13.5", "rollup": "^2.13.1", "ts-jest": "^26.1.3", "typescript": "^3.9.6", "vue": "3.0.0-rc.7", "webpack": "^4.43.0", "webpack-cli": "^3.3.11", "webpack-dev-server": "^3.10.3"}}