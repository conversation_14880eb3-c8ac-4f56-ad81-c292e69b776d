{"name": "browserslist", "version": "4.14.2", "description": "Share target browsers between different front-end tools, like Autoprefixer, Stylelint and babel-env-preset", "keywords": ["caniuse", "browsers", "target"], "funding": {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": "browserslist/browserslist", "dependencies": {"caniuse-lite": "^1.0.30001125", "electron-to-chromium": "^1.3.564", "escalade": "^3.0.2", "node-releases": "^1.1.61"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "bin": "./cli.js", "browser": {"./node.js": "./browser.js", "path": false}}