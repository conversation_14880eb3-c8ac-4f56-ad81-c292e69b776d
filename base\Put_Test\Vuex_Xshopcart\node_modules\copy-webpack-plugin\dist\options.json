{"definitions": {"ObjectPattern": {"type": "object", "properties": {"from": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "object"}]}, "to": {"type": "string"}, "context": {"type": "string"}, "toType": {"enum": ["dir", "file", "template"]}, "test": {"anyOf": [{"type": "string"}, {"instanceof": "RegExp"}]}, "force": {"type": "boolean"}, "ignore": {"type": "array", "items": {"anyOf": [{"type": "string"}, {"type": "object"}]}}, "flatten": {"type": "boolean"}, "cache": {"anyOf": [{"type": "boolean"}, {"type": "object"}]}, "transform": {"instanceof": "Function"}, "transformPath": {"instanceof": "Function"}}, "required": ["from"]}, "StringPattern": {"type": "string", "minLength": 1}}, "type": "array", "items": {"anyOf": [{"$ref": "#/definitions/StringPattern"}, {"$ref": "#/definitions/ObjectPattern"}]}}