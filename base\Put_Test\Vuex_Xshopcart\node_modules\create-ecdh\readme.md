createECDH [![Build Status](https://travis-ci.org/crypto-browserify/createECDH.svg)](https://travis-ci.org/crypto-browserify/createECDH)
====

In io.js or node >= 0.11 this module is just a shortcut to crypto.createECDH.  In node <= 0.11 or the browser this is a pure JavaScript implimentation, more specifically a wrapper around [elliptic](https://github.com/indutny/elliptic), to give it the same API as node. `secp256k1`, `secp224r1` (aka p224), `prime256v1` (aka p256, secp256r1), `prime192v1` (aka p192, secp192r1), `secp384r1` (aka p384), `secp521r1` (aka p521)  curves all work in both this library and node (though only the highlighted name will work in node).
