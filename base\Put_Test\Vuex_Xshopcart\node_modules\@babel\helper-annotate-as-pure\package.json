{"name": "@babel/helper-annotate-as-pure", "version": "7.10.4", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-annotate-as-pure"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/types": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df"}