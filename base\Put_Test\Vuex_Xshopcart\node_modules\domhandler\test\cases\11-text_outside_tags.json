{"name": "Text outside tags", "options": {}, "html": "Line one\n<br>\nline two", "expected": [{"data": "Line one\n", "type": "text", "prev": null, "next": {"type": "tag", "name": "br", "attribs": {}}}, {"type": "tag", "name": "br", "attribs": {}, "prev": {"data": "Line one\n", "type": "text"}, "next": {"data": "\nline two", "type": "text"}}, {"data": "\nline two", "type": "text", "prev": {"type": "tag", "name": "br", "attribs": {}}, "next": null}]}