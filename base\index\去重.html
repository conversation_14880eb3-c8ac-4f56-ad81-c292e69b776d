<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <script>


        var arr = [1,2,4,5,2,5,3]
        // var arr1= new Set(arr)
        // console.log('arr2 :>> ', ...new Set(arr));
        var arr3 = Array.from(...new Set(arr))
        console.log("%c [ arr3 ]", "font-size:13px; background:#00ffff; color:red;", arr3)
        // arr.filter(item=>{
        //     console.log('item :>> ', item);
        // })
    </script>
</body>
</html>