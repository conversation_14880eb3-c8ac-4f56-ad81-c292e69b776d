{"remainingRequest": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\App.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\App.vue", "mtime": 1649424514352}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1657986293299}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1657986298727}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1657986293299}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1657986322488}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBIZWxsb1dvcmxkIGZyb20gJy4vY29tcG9uZW50cy9IZWxsb1dvcmxkLnZ1ZScKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQXBwJywKICBjb21wb25lbnRzOiB7CiAgICBIZWxsb1dvcmxkCiAgfQp9Cg=="}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";;;;;;;AAOA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\n<div id=\"app\">\n  <HelloWorld msg=\"Welcome to Your Vue.js App\" />\n</div>\n</template>\n\n<script>\nimport HelloWorld from './components/HelloWorld.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    HelloWorld\n  }\n}\n</script>\n\n<style lang=\"scss\">\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-align: center;\n  color: #2c3e50;\n  margin-top: 60px;\n}\n</style>\n"]}]}