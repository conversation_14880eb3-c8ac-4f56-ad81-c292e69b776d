{"name": "decamelize", "version": "1.2.0", "description": "Convert a camelized string into a lowercased one with a custom separator: unicorn<PERSON>ain<PERSON> → unicorn_rainbow", "license": "MIT", "repository": "sindresorhus/decamelize", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["decamelize", "decamelcase", "camelcase", "lowercase", "case", "dash", "hyphen", "string", "str", "text", "convert"], "devDependencies": {"ava": "*", "xo": "*"}}