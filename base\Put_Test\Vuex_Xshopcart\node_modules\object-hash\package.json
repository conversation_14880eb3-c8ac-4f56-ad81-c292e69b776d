{"name": "object-hash", "version": "1.3.1", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"browserify": "^13.0.0", "gulp": "^3.9.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^2.1.2", "gulp-istanbul": "^0.10.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^2.2.0", "gulp-rename": "^1.2.0", "gulp-uglify": "^1.5.1", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^0.13.15", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "^0.2.1", "karma-phantomjs-launcher": "^0.2.1", "mocha": "^2.3.4", "phantomjs": "^1.9.19"}, "engines": {"node": ">= 0.10.0"}, "main": "./index.js", "browser": "./dist/object_hash.js"}