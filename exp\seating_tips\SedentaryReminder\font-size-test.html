<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Size 测试</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        .test-box {
            border: 2px solid #007bff;
            margin: 10px 0;
            padding: 10px;
        }
        .size-display {
            background: #f8f9fa;
            padding: 5px;
            font-family: monospace;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body class="container mt-4">
    <h1>Font Size 测试页面</h1>
    
    <div class="test-box">
        <h3>根元素字体大小</h3>
        <div class="size-display" id="rootFontSize"></div>
    </div>

    <div class="test-box">
        <h3>Bootstrap rem 值测试</h3>
        <div class="me-2 p-3 bg-primary text-white d-inline-block">
            me-2 p-3 (margin-right: 0.5rem, padding: 1rem)
        </div>
        <div class="size-display" id="bootstrapSizes"></div>
    </div>

    <div class="test-box">
        <h3>实际像素值计算</h3>
        <div class="size-display" id="calculations"></div>
    </div>

    <script>
        // 获取根字体大小
        const rootFontSize = getComputedStyle(document.documentElement).fontSize;
        document.getElementById('rootFontSize').innerHTML = `
            <strong>html font-size:</strong> ${rootFontSize}
        `;

        // 获取 Bootstrap 元素的实际大小
        const testElement = document.querySelector('.me-2.p-3');
        const marginRight = getComputedStyle(testElement).marginRight;
        const padding = getComputedStyle(testElement).padding;
        
        document.getElementById('bootstrapSizes').innerHTML = `
            <strong>margin-right (me-2):</strong> ${marginRight}<br>
            <strong>padding (p-3):</strong> ${padding}
        `;

        // 计算 rem 到 px 的转换
        const rootSizeNum = parseFloat(rootFontSize);
        document.getElementById('calculations').innerHTML = `
            <strong>根字体大小:</strong> ${rootSizeNum}px<br>
            <strong>0.5rem (me-2):</strong> ${0.5 * rootSizeNum}px<br>
            <strong>1rem (p-3):</strong> ${1 * rootSizeNum}px<br>
            <strong>1.5rem (p-4):</strong> ${1.5 * rootSizeNum}px
        `;
    </script>
</body>
</html>
