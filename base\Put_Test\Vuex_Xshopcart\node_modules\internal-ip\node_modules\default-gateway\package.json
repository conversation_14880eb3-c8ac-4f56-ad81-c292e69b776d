{"name": "default-gateway", "version": "4.2.0", "description": "Get the default network gateway, cross-platform.", "author": "silverwind <<EMAIL>>", "repository": "silverwind/default-gateway", "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "eslint *.js && node --pending-deprecation --trace-deprecation --throw-deprecation --trace-warnings test.js"}, "engines": {"node": ">=6"}, "dependencies": {"execa": "^1.0.0", "ip-regex": "^2.1.0"}, "devDependencies": {"eslint": "^5.15.1", "eslint-config-silverwind": "^2.1.0", "updates": "^7.2.0", "ver": "4.0.1"}, "files": ["index.js", "android.js", "darwin.js", "freebsd.js", "linux.js", "openbsd.js", "sunos.js", "win32.js", "ibmi.js"], "keywords": ["default gateway", "network", "default", "gateway", "routing", "route"]}