{"name": "@babel/types", "version": "7.11.5", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-types"}, "main": "lib/index.js", "types": "lib/index.d.ts", "dependencies": {"@babel/helper-validator-identifier": "^7.10.4", "lodash": "^4.17.19", "to-fast-properties": "^2.0.0"}, "devDependencies": {"@babel/generator": "^7.11.5", "@babel/parser": "^7.11.5", "chalk": "^4.1.0"}, "gitHead": "af64ccb2b00bc7574943674996c2f0507cdbfb6f"}