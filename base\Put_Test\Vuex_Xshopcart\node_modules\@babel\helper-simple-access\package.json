{"name": "@babel/helper-simple-access", "version": "7.10.4", "description": "Babel helper for ensuring that access to a given value is performed through simple accesses", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-simple-access"}, "main": "lib/index.js", "dependencies": {"@babel/template": "^7.10.4", "@babel/types": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df"}