{"remainingRequest": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\store\\cart\\mutations-type.js", "dependencies": [{"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\store\\cart\\mutations-type.js", "mtime": 1649424517987}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1657986293299}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1657986298727}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\eslint-loader\\index.js", "mtime": 1657986295398}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8g6L+Z5piv6LSt54mp6L2m55qE5re75YqgCmV4cG9ydCB2YXIgQUREID0gJ0FERCc7IC8vIOi/meaYr+i0reeJqei9pueahOWHj+WwkQoKZXhwb3J0IHZhciBSRURVQ0UgPSAnUkVEVUNFJzs="}, {"version": 3, "sources": ["D:/No.1 folder/No.2 folder/dev/dev2/vue/base/Vuex_Xshopcart/src/store/cart/mutations-type.js"], "names": ["ADD", "REDUCE"], "mappings": "AAAA;AACA,OAAO,IAAMA,GAAG,GAAG,KAAZ,C,CAEP;;AACA,OAAO,IAAMC,MAAM,GAAG,QAAf", "sourcesContent": ["// 这是购物车的添加\nexport const ADD = 'ADD'\n\n// 这是购物车的减少\nexport const REDUCE = 'REDUCE'\n\n\n"]}]}