{"version": 3, "sources": ["node_modules/stackframe/stackframe.js", "error-stack-parser.js"], "names": ["root", "factory", "define", "amd", "exports", "module", "StackFrame", "this", "_capitalize", "str", "char<PERSON>t", "toUpperCase", "substring", "_getter", "p", "booleanProps", "numericProps", "stringProps", "props", "concat", "obj", "i", "length", "undefined", "prototype", "getArgs", "args", "set<PERSON>rgs", "v", "Object", "toString", "call", "TypeError", "getEval<PERSON><PERSON>in", "eval<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fileName", "getFileName", "lineNumber", "getLineNumber", "columnNumber", "getColumnNumber", "functionName", "getFunctionName", "getIsEval", "fromString", "argsStartIndex", "indexOf", "argsEndIndex", "lastIndexOf", "split", "locationString", "parts", "exec", "Boolean", "j", "n", "isNaN", "parseFloat", "isFinite", "Number", "k", "String", "require", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FIREFOX_SAFARI_STACK_REGEXP", "CHROME_IE_STACK_REGEXP", "SAFARI_NATIVE_CODE_REGEXP", "parse", "error", "stacktrace", "parseOpera", "stack", "match", "parseV8OrIE", "parseFFOr<PERSON><PERSON><PERSON>", "Error", "extractLocation", "urlLike", "replace", "filter", "line", "map", "sanitizedLine", "location", "tokens", "slice", "locationParts", "pop", "join", "source", "functionNameRegex", "matches", "e", "message", "parseOpera9", "parseOpera11", "parseOpera10", "lineRE", "lines", "result", "len", "push", "argsRaw", "functionCall", "shift"], "mappings": "CAAC,SAASA,EAAMC,GACZ,aAIsB,mBAAXC,QAAyBA,OAAOC,IACvCD,OAAO,gBAAkBD,GACC,iBAAZG,QACdC,OAAOD,QAAUH,IAEjBD,EAAKM,WAAaL,IAV1B,CAYEM,KAAM,WACJ,aAKA,SAASC,EAAYC,GACjB,OAAOA,EAAIC,OAAO,GAAGC,cAAgBF,EAAIG,UAAU,GAGvD,SAASC,EAAQC,GACb,OAAO,WACH,OAAOP,KAAKO,IAIpB,IAAIC,GAAgB,gBAAiB,SAAU,WAAY,cACvDC,GAAgB,eAAgB,cAChCC,GAAe,WAAY,eAAgB,UAG3CC,EAAQH,EAAaI,OAAOH,EAAcC,GAF5B,SAIlB,SAASX,EAAWc,GAChB,GAAKA,EACL,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAMI,OAAQD,SACRE,IAAlBH,EAAIF,EAAMG,KACVd,KAAK,MAAQC,EAAYU,EAAMG,KAAKD,EAAIF,EAAMG,KAK1Df,EAAWkB,WACPC,QAAS,WACL,OAAOlB,KAAKmB,MAEhBC,QAAS,SAASC,GACd,GAA0C,mBAAtCC,OAAOL,UAAUM,SAASC,KAAKH,GAC/B,MAAM,IAAII,UAAU,yBAExBzB,KAAKmB,KAAOE,GAGhBK,cAAe,WACX,OAAO1B,KAAK2B,YAEhBC,cAAe,SAASP,GACpB,GAAIA,aAAatB,EACbC,KAAK2B,WAAaN,MACf,CAAA,KAAIA,aAAaC,QAGpB,MAAM,IAAIG,UAAU,+CAFpBzB,KAAK2B,WAAa,IAAI5B,EAAWsB,KAMzCE,SAAU,WACN,IAAIM,EAAW7B,KAAK8B,eAAiB,GACjCC,EAAa/B,KAAKgC,iBAAmB,GACrCC,EAAejC,KAAKkC,mBAAqB,GACzCC,EAAenC,KAAKoC,mBAAqB,GAC7C,OAAIpC,KAAKqC,YACDR,EACO,WAAaA,EAAW,IAAME,EAAa,IAAME,EAAe,IAEpE,UAAYF,EAAa,IAAME,EAEtCE,EACOA,EAAe,KAAON,EAAW,IAAME,EAAa,IAAME,EAAe,IAE7EJ,EAAW,IAAME,EAAa,IAAME,IAInDlC,EAAWuC,WAAa,SAAgCpC,GACpD,IAAIqC,EAAiBrC,EAAIsC,QAAQ,KAC7BC,EAAevC,EAAIwC,YAAY,KAE/BP,EAAejC,EAAIG,UAAU,EAAGkC,GAChCpB,EAAOjB,EAAIG,UAAUkC,EAAiB,EAAGE,GAAcE,MAAM,KAC7DC,EAAiB1C,EAAIG,UAAUoC,EAAe,GAElD,GAAoC,IAAhCG,EAAeJ,QAAQ,KACvB,IAAIK,EAAQ,gCAAgCC,KAAKF,EAAgB,IAC7Df,EAAWgB,EAAM,GACjBd,EAAac,EAAM,GACnBZ,EAAeY,EAAM,GAG7B,OAAO,IAAI9C,GACPoC,aAAcA,EACdhB,KAAMA,QAAQH,EACda,SAAUA,EACVE,WAAYA,QAAcf,EAC1BiB,aAAcA,QAAgBjB,KAItC,IAAK,IAAIF,EAAI,EAAGA,EAAIN,EAAaO,OAAQD,IACrCf,EAAWkB,UAAU,MAAQhB,EAAYO,EAAaM,KAAOR,EAAQE,EAAaM,IAClFf,EAAWkB,UAAU,MAAQhB,EAAYO,EAAaM,KAAO,SAAUP,GACnE,OAAO,SAASc,GACZrB,KAAKO,GAAKwC,QAAQ1B,IAFmC,CAI1Db,EAAaM,IAGpB,IAAK,IAAIkC,EAAI,EAAGA,EAAIvC,EAAaM,OAAQiC,IACrCjD,EAAWkB,UAAU,MAAQhB,EAAYQ,EAAauC,KAAO1C,EAAQG,EAAauC,IAClFjD,EAAWkB,UAAU,MAAQhB,EAAYQ,EAAauC,KAAO,SAAUzC,GACnE,OAAO,SAASc,GACZ,GA7GO4B,EA6GQ5B,EA5Gf6B,MAAMC,WAAWF,MAAOG,SAASH,GA6G7B,MAAM,IAAIxB,UAAUlB,EAAI,qBA9GxC,IAAmB0C,EAgHPjD,KAAKO,GAAK8C,OAAOhC,IALoC,CAO1DZ,EAAauC,IAGpB,IAAK,IAAIM,EAAI,EAAGA,EAAI5C,EAAYK,OAAQuC,IACpCvD,EAAWkB,UAAU,MAAQhB,EAAYS,EAAY4C,KAAOhD,EAAQI,EAAY4C,IAChFvD,EAAWkB,UAAU,MAAQhB,EAAYS,EAAY4C,KAAO,SAAU/C,GAClE,OAAO,SAASc,GACZrB,KAAKO,GAAKgD,OAAOlC,IAFmC,CAIzDX,EAAY4C,IAGnB,OAAOvD,IC5IV,SAASN,EAAMC,GACZ,aAIsB,mBAAXC,QAAyBA,OAAOC,IACvCD,OAAO,sBAAuB,cAAeD,GACnB,iBAAZG,QACdC,OAAOD,QAAUH,EAAQ8D,QAAQ,eAEjC/D,EAAKgE,iBAAmB/D,EAAQD,EAAKM,YAV7C,CAYEC,KAAM,SAA0BD,GAC9B,aAEA,IAAI2D,EAA8B,eAC9BC,EAAyB,iCACzBC,EAA4B,8BAEhC,OAOIC,MAAO,SAAiCC,GACpC,QAAgC,IAArBA,EAAMC,iBAAkE,IAA7BD,EAAM,mBACxD,OAAO9D,KAAKgE,WAAWF,GACpB,GAAIA,EAAMG,OAASH,EAAMG,MAAMC,MAAMP,GACxC,OAAO3D,KAAKmE,YAAYL,GACrB,GAAIA,EAAMG,MACb,OAAOjE,KAAKoE,gBAAgBN,GAE5B,MAAM,IAAIO,MAAM,oCAKxBC,gBAAiB,SAA2CC,GAExD,IAA8B,IAA1BA,EAAQ/B,QAAQ,KAChB,OAAQ+B,GAGZ,IACI1B,EADS,+BACMC,KAAKyB,EAAQC,QAAQ,QAAS,KACjD,OAAQ3B,EAAM,GAAIA,EAAM,SAAM7B,EAAW6B,EAAM,SAAM7B,IAGzDmD,YAAa,SAAuCL,GAKhD,OAJeA,EAAMG,MAAMtB,MAAM,MAAM8B,OAAO,SAASC,GACnD,QAASA,EAAKR,MAAMP,IACrB3D,MAEa2E,IAAI,SAASD,GACrBA,EAAKlC,QAAQ,WAAa,IAE1BkC,EAAOA,EAAKF,QAAQ,aAAc,QAAQA,QAAQ,+BAAgC,KAEtF,IAAII,EAAgBF,EAAKF,QAAQ,OAAQ,IAAIA,QAAQ,eAAgB,KAIjEK,EAAWD,EAAcV,MAAM,4BAK/BY,GAFJF,EAAgBC,EAAWD,EAAcJ,QAAQK,EAAS,GAAI,IAAMD,GAEzCjC,MAAM,OAAOoC,MAAM,GAE1CC,EAAgBhF,KAAKsE,gBAAgBO,EAAWA,EAAS,GAAKC,EAAOG,OACrE9C,EAAe2C,EAAOI,KAAK,WAAQlE,EACnCa,GAAY,OAAQ,eAAeW,QAAQwC,EAAc,KAAO,OAAIhE,EAAYgE,EAAc,GAElG,OAAO,IAAIjF,GACPoC,aAAcA,EACdN,SAAUA,EACVE,WAAYiD,EAAc,GAC1B/C,aAAc+C,EAAc,GAC5BG,OAAQT,KAEb1E,OAGPoE,gBAAiB,SAA2CN,GAKxD,OAJeA,EAAMG,MAAMtB,MAAM,MAAM8B,OAAO,SAASC,GACnD,OAAQA,EAAKR,MAAMN,IACpB5D,MAEa2E,IAAI,SAASD,GAMzB,GAJIA,EAAKlC,QAAQ,YAAc,IAC3BkC,EAAOA,EAAKF,QAAQ,mDAAoD,SAGjD,IAAvBE,EAAKlC,QAAQ,OAAsC,IAAvBkC,EAAKlC,QAAQ,KAEzC,OAAO,IAAIzC,GACPoC,aAAcuC,IAGlB,IAAIU,EAAoB,6BACpBC,EAAUX,EAAKR,MAAMkB,GACrBjD,EAAekD,GAAWA,EAAQ,GAAKA,EAAQ,QAAKrE,EACpDgE,EAAgBhF,KAAKsE,gBAAgBI,EAAKF,QAAQY,EAAmB,KAEzE,OAAO,IAAIrF,GACPoC,aAAcA,EACdN,SAAUmD,EAAc,GACxBjD,WAAYiD,EAAc,GAC1B/C,aAAc+C,EAAc,GAC5BG,OAAQT,KAGjB1E,OAGPgE,WAAY,SAAsCsB,GAC9C,OAAKA,EAAEvB,YAAeuB,EAAEC,QAAQ/C,QAAQ,OAAS,GAC7C8C,EAAEC,QAAQ5C,MAAM,MAAM5B,OAASuE,EAAEvB,WAAWpB,MAAM,MAAM5B,OACjDf,KAAKwF,YAAYF,GAChBA,EAAErB,MAGHjE,KAAKyF,aAAaH,GAFlBtF,KAAK0F,aAAaJ,IAMjCE,YAAa,SAAuCF,GAKhD,IAJA,IAAIK,EAAS,oCACTC,EAAQN,EAAEC,QAAQ5C,MAAM,MACxBkD,KAEK/E,EAAI,EAAGgF,EAAMF,EAAM7E,OAAQD,EAAIgF,EAAKhF,GAAK,EAAG,CACjD,IAAIoD,EAAQyB,EAAO7C,KAAK8C,EAAM9E,IAC1BoD,GACA2B,EAAOE,KAAK,IAAIhG,GACZ8B,SAAUqC,EAAM,GAChBnC,WAAYmC,EAAM,GAClBiB,OAAQS,EAAM9E,MAK1B,OAAO+E,GAGXH,aAAc,SAAwCJ,GAKlD,IAJA,IAAIK,EAAS,6DACTC,EAAQN,EAAEvB,WAAWpB,MAAM,MAC3BkD,KAEK/E,EAAI,EAAGgF,EAAMF,EAAM7E,OAAQD,EAAIgF,EAAKhF,GAAK,EAAG,CACjD,IAAIoD,EAAQyB,EAAO7C,KAAK8C,EAAM9E,IAC1BoD,GACA2B,EAAOE,KACH,IAAIhG,GACAoC,aAAc+B,EAAM,SAAMlD,EAC1Ba,SAAUqC,EAAM,GAChBnC,WAAYmC,EAAM,GAClBiB,OAAQS,EAAM9E,MAM9B,OAAO+E,GAIXJ,aAAc,SAAwC3B,GAKlD,OAJeA,EAAMG,MAAMtB,MAAM,MAAM8B,OAAO,SAASC,GACnD,QAASA,EAAKR,MAAMR,KAAiCgB,EAAKR,MAAM,sBACjElE,MAEa2E,IAAI,SAASD,GACzB,IAMIsB,EANAlB,EAASJ,EAAK/B,MAAM,KACpBqC,EAAgBhF,KAAKsE,gBAAgBQ,EAAOG,OAC5CgB,EAAgBnB,EAAOoB,SAAW,GAClC/D,EAAe8D,EACdzB,QAAQ,iCAAkC,MAC1CA,QAAQ,aAAc,UAAOxD,EAE9BiF,EAAa/B,MAAM,iBACnB8B,EAAUC,EAAazB,QAAQ,qBAAsB,OAEzD,IAAIrD,OAAoBH,IAAZgF,GAAqC,8BAAZA,OACjChF,EAAYgF,EAAQrD,MAAM,KAE9B,OAAO,IAAI5C,GACPoC,aAAcA,EACdhB,KAAMA,EACNU,SAAUmD,EAAc,GACxBjD,WAAYiD,EAAc,GAC1B/C,aAAc+C,EAAc,GAC5BG,OAAQT,KAEb1E"}