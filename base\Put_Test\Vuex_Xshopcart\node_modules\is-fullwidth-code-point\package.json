{"name": "is-fullwidth-code-point", "version": "2.0.0", "description": "Check if the character represented by a given Unicode code point is fullwidth", "license": "MIT", "repository": "sindresorhus/is-fullwidth-code-point", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["fullwidth", "full-width", "full", "width", "unicode", "character", "char", "string", "str", "codepoint", "code", "point", "is", "detect", "check"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}}