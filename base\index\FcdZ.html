<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <style>
     .name{
        color:red;
      }
    </style>
</head>

<body>
    <div id="app">
        <child :fatherdata="name"></child>
    </div>
    <script src="https://cdn.staticfile.org/vue/2.2.2/vue.min.js"></script>
    <script>
        Vue.component('child',{
            template:`<div>父的信息：{{fatherdata}}</div>`,
            props:['fatherdata']
        })
        const vm = new Vue({
           el:'#app',
           data() {
               return {
                  name: '張三'
               }
           }
        });
    </script>
</body>

</html>