{"$schema": "http://json-schema.org/draft-01/hyper-schema#", "id": "http://json-schema.org/draft-01/hyper-schema#", "properties": {"links": {"type": "array", "items": {"$ref": "http://json-schema.org/draft-01/links#"}, "optional": true}, "fragmentResolution": {"type": "string", "optional": true, "default": "dot-delimited"}, "root": {"type": "boolean", "optional": true, "default": false}, "readonly": {"type": "boolean", "optional": true, "default": false}, "pathStart": {"type": "string", "optional": true, "format": "uri"}, "mediaType": {"type": "string", "optional": true, "format": "media-type"}, "alternate": {"type": "array", "items": {"$ref": "#"}, "optional": true}}, "links": [{"href": "{$ref}", "rel": "full"}, {"href": "{$schema}", "rel": "<PERSON><PERSON>"}, {"href": "{id}", "rel": "self"}], "fragmentResolution": "dot-delimited", "extends": {"$ref": "http://json-schema.org/draft-01/schema#"}}