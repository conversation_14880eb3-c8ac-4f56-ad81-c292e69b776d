<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <style>
     .name{
        color:red;
      }
    </style>
</head>

<body>
    <div id="app">
       <button class="name" @click="change">{{name}}</button>
       <component :is="who" ></component>
    </div>
    <script src="./vue_npm/vue_min/vue.min.js"></script>
    <script>
        const vm = new Vue({
           el:'#app',
           data() {
               return {
                  name: '点击更换',
                  jsonA: {
                    template: `<p>启用第一个模板<p/>`
                  },
                  jsonB: {
                    template: `<p>启用第二个模板<p/>`
                  },
                  jsonC: {
                    template: `<p>启用第三个模板<p/>`
                  },
                  who: 'jsonA'
               }
           },
           components:{
                jsonA: this.jsonA,
                jsonB: this.jsonB,
                jsonC: this.jsonC
           },
           methods:{
               change() {
                    if(this.who === 'jsonA'){
                        this.who = this.jsonB
                    }else if(this.who === this.jsonB) {
                        this.who = this.jsonC
                    }else if(this.who === this.jsonC) {
                        this.who = this.jsonA
                    }else if(this.who === this.jsonA) {
                        this.who = this.jsonB
                    }
               }
           }
        });
    </script>
</body>

</html>