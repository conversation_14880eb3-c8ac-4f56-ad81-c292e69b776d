{"name": "@babel/helper-function-name", "version": "7.10.4", "description": "Helper function to change the property 'name' of every function", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-function-name"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-get-function-arity": "^7.10.4", "@babel/template": "^7.10.4", "@babel/types": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df"}