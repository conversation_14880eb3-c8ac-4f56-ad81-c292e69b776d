{"name": "for-in", "description": "Iterate over the own and inherited enumerable properties of an object, and return an object with properties that evaluate to true from the callback. Exit early by returning `false`. JavaScript/Node.js", "version": "1.0.2", "homepage": "https://github.com/jonschlinkert/for-in", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON> <<EMAIL>> (http://twitter.com/jonschlinkert)", "<PERSON> (http://paulirish.com)"], "repository": "jonschlinkert/for-in", "bugs": {"url": "https://github.com/jonschlinkert/for-in/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "keywords": ["for", "for-in", "for-own", "has", "has-own", "hasOwn", "in", "key", "keys", "object", "own", "value"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["arr-flatten", "collection-map", "for-own"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}}