# [postcss][postcss]-minify-selectors

> Minify selectors with PostCSS.

## Install

With [npm](https://www.npmjs.com/package/postcss-minify-selectors) do:

```
npm install postcss-minify-selectors --save
```

## Example

### Input

```css
h1 + p, h2, h3, h2{color:blue}
```

### Output

```css
h1+p,h2,h3{color:blue}
```

For more examples see the [tests](test.js).

## Usage

See the [PostCSS documentation](https://github.com/postcss/postcss#usage) for
examples for your environment.

## Contributors

See [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).

## License

MIT © [<PERSON>](http://beneb.info)

[postcss]: https://github.com/postcss/postcss
