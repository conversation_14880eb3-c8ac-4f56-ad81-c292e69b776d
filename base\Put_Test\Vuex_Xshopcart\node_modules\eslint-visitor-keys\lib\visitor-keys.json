{"AssignmentExpression": ["left", "right"], "AssignmentPattern": ["left", "right"], "ArrayExpression": ["elements"], "ArrayPattern": ["elements"], "ArrowFunctionExpression": ["params", "body"], "AwaitExpression": ["argument"], "BlockStatement": ["body"], "BinaryExpression": ["left", "right"], "BreakStatement": ["label"], "CallExpression": ["callee", "arguments"], "CatchClause": ["param", "body"], "ChainExpression": ["expression"], "ClassBody": ["body"], "ClassDeclaration": ["id", "superClass", "body"], "ClassExpression": ["id", "superClass", "body"], "ConditionalExpression": ["test", "consequent", "alternate"], "ContinueStatement": ["label"], "DebuggerStatement": [], "DoWhileStatement": ["body", "test"], "EmptyStatement": [], "ExportAllDeclaration": ["exported", "source"], "ExportDefaultDeclaration": ["declaration"], "ExportNamedDeclaration": ["declaration", "specifiers", "source"], "ExportSpecifier": ["exported", "local"], "ExpressionStatement": ["expression"], "ExperimentalRestProperty": ["argument"], "ExperimentalSpreadProperty": ["argument"], "ForStatement": ["init", "test", "update", "body"], "ForInStatement": ["left", "right", "body"], "ForOfStatement": ["left", "right", "body"], "FunctionDeclaration": ["id", "params", "body"], "FunctionExpression": ["id", "params", "body"], "Identifier": [], "IfStatement": ["test", "consequent", "alternate"], "ImportDeclaration": ["specifiers", "source"], "ImportDefaultSpecifier": ["local"], "ImportExpression": ["source"], "ImportNamespaceSpecifier": ["local"], "ImportSpecifier": ["imported", "local"], "JSXAttribute": ["name", "value"], "JSXClosingElement": ["name"], "JSXElement": ["openingElement", "children", "closingElement"], "JSXEmptyExpression": [], "JSXExpressionContainer": ["expression"], "JSXIdentifier": [], "JSXMemberExpression": ["object", "property"], "JSXNamespacedName": ["namespace", "name"], "JSXOpeningElement": ["name", "attributes"], "JSXSpreadAttribute": ["argument"], "JSXText": [], "JSXFragment": ["openingFragment", "children", "closingFragment"], "Literal": [], "LabeledStatement": ["label", "body"], "LogicalExpression": ["left", "right"], "MemberExpression": ["object", "property"], "MetaProperty": ["meta", "property"], "MethodDefinition": ["key", "value"], "NewExpression": ["callee", "arguments"], "ObjectExpression": ["properties"], "ObjectPattern": ["properties"], "Program": ["body"], "Property": ["key", "value"], "RestElement": ["argument"], "ReturnStatement": ["argument"], "SequenceExpression": ["expressions"], "SpreadElement": ["argument"], "Super": [], "SwitchStatement": ["discriminant", "cases"], "SwitchCase": ["test", "consequent"], "TaggedTemplateExpression": ["tag", "quasi"], "TemplateElement": [], "TemplateLiteral": ["quasis", "expressions"], "ThisExpression": [], "ThrowStatement": ["argument"], "TryStatement": ["block", "handler", "finalizer"], "UnaryExpression": ["argument"], "UpdateExpression": ["argument"], "VariableDeclaration": ["declarations"], "VariableDeclarator": ["id", "init"], "WhileStatement": ["test", "body"], "WithStatement": ["object", "body"], "YieldExpression": ["argument"]}