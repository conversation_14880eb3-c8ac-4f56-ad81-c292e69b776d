{"name": "@types/webpack-sources", "version": "1.4.2", "description": "TypeScript definitions for webpack-sources", "license": "MIT", "contributors": [{"name": "e-cloud", "url": "https://github.com/e-cloud", "githubUsername": "e-cloud"}, {"name": "<PERSON>", "url": "https://github.com/chrise<PERSON>tein", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/webpack-sources"}, "scripts": {}, "dependencies": {"@types/node": "*", "@types/source-list-map": "*", "source-map": "^0.7.3"}, "typesPublisherContentHash": "fdd917e71a3009915811c057da70108f4ae12f0c656466a04a2f60095dc2a593", "typeScriptVersion": "3.0"}