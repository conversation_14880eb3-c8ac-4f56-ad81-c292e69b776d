<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <div>search:
        <input type="text" id="input">
    </div>
    <script>
        document.querySelector("#input").addEventListener('input',ajax)
        function ajax(e){
            console.log('e.target.value :>> ', e.target.value);
        }
    </script>
</body>
</html> 