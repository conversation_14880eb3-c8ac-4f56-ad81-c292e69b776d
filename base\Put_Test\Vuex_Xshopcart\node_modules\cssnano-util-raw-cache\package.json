{"name": "cssnano-util-raw-cache", "version": "4.0.1", "repository": "cssnano/cssnano", "main": "dist/index.js", "description": "Manages the raw value formatting for generated AST nodes.", "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "homepage": "https://github.com/cssnano/cssnano", "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": ">=6.9.0"}, "files": ["LICENSE-MIT", "dist"], "license": "MIT", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0"}, "dependencies": {"postcss": "^7.0.0"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}}