{"name": "@babel/plugin-transform-block-scoping", "version": "7.11.1", "description": "Compile ES2015 block scoping (const and let) to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-block-scoping"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.11.1", "@babel/helper-plugin-test-runner": "^7.10.4"}, "gitHead": "953ae82159b67b4487f837a17a1b8d5e305a8e5c"}