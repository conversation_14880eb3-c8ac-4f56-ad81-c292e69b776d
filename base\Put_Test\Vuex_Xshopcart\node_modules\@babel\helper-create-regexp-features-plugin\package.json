{"name": "@babel/helper-create-regexp-features-plugin", "version": "7.10.4", "author": "The Babel Team (https://babeljs.io/team)", "license": "MIT", "description": "Compile ESNext Regular Expressions to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "main": "lib/index.js", "publishConfig": {"access": "public"}, "keywords": ["babel", "babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.10.4", "@babel/helper-regex": "^7.10.4", "regexpu-core": "^4.7.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df"}