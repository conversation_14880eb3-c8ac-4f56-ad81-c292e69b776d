"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var plugin_syntax_jsx_1 = __importDefault(require("@babel/plugin-syntax-jsx"));
var t = __importStar(require("@babel/types"));
var transform_vue_jsx_1 = __importDefault(require("./transform-vue-jsx"));
var sugar_fragment_1 = __importDefault(require("./sugar-fragment"));
var utils_1 = require("./utils");
exports.default = (function () { return ({
    name: 'babel-plugin-jsx',
    inherits: plugin_syntax_jsx_1.default,
    visitor: __assign(__assign({ Program: {
            exit: function (path, state) {
                var helpers = state.get(utils_1.JSX_HELPER_KEY);
                if (!helpers) {
                    return;
                }
                var body = path.get('body');
                var specifierNames = new Set();
                // const importedLocalMap = new Map<string, string>();
                body
                    .filter(function (nodePath) { return t.isImportDeclaration(nodePath.node)
                    && nodePath.node.source.value === 'vue'; })
                    .forEach(function (nodePath) {
                    var shouldRemove = true;
                    var newSpecifiers = nodePath.node.specifiers
                        .filter(function (specifier) {
                        if (t.isImportSpecifier(specifier)) {
                            var imported = specifier.imported, local = specifier.local;
                            specifierNames.add(imported.name);
                            return local.name !== imported.name;
                        }
                        if (t.isImportNamespaceSpecifier(specifier)) {
                            // should keep when `import * as Vue from 'vue'`
                            shouldRemove = false;
                        }
                        return false;
                    });
                    if (newSpecifiers.length) {
                        nodePath.replaceWith(t.importDeclaration(newSpecifiers, t.stringLiteral('vue')));
                    }
                    else if (shouldRemove) {
                        nodePath.remove();
                    }
                });
                var importedHelperKeys = new Set(__spread(specifierNames, helpers));
                var importDeclaration = __spread(importedHelperKeys).map(function (imported) { return t.importSpecifier(t.identifier(imported), t.identifier(imported)); });
                var expression = t.importDeclaration(importDeclaration, t.stringLiteral('vue'));
                path.unshiftContainer('body', expression);
            },
        } }, transform_vue_jsx_1.default()), sugar_fragment_1.default()),
}); });
