!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(((t=t||self).prettierPlugins=t.prettierPlugins||{},t.prettierPlugins.babylon={}))}(this,(function(t){"use strict";var e=function(t,e){var s=new SyntaxError(t+" ("+e.start.line+":"+e.start.column+")");return s.loc=e,s};function s(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function i(t,e){return t(e={exports:{}},e.exports),e.exports}var a,r=Object.freeze({__proto__:null,default:{EOL:"\n"}}),n=i((function(t){t.exports=function(t){if("string"!=typeof t)throw new TypeError("Expected a string");var e=t.match(/(?:\r?\n)/g)||[];if(0===e.length)return null;var s=e.filter((function(t){return"\r\n"===t})).length;return s>e.length-s?"\r\n":"\n"},t.exports.graceful=function(e){return t.exports(e)||"\n"}})),o=(n.graceful,(a=r)&&a.default||a),h=i((function(t,e){function s(){var t=o;return s=function(){return t},t}function i(){var t,e=(t=n)&&t.__esModule?t:{default:t};return i=function(){return e},e}Object.defineProperty(e,"__esModule",{value:!0}),e.extract=function(t){var e=t.match(h);return e?e[0].trimLeft():""},e.strip=function(t){var e=t.match(h);return e&&e[0]?t.substring(e[0].length):t},e.parse=function(t){return f(t).pragmas},e.parseWithComments=f,e.print=function(t){var e=t.comments,a=void 0===e?"":e,r=t.pragmas,n=void 0===r?{}:r,o=(0,i().default)(a)||s().EOL,h=Object.keys(n),u=h.map((function(t){return m(t,n[t])})).reduce((function(t,e){return t.concat(e)}),[]).map((function(t){return" * "+t+o})).join("");if(!a){if(0===h.length)return"";if(1===h.length&&!Array.isArray(n[h[0]])){var l=n[h[0]];return"".concat("/**"," ").concat(m(h[0],l)[0]).concat(" */")}}var c=a.split(o).map((function(t){return"".concat(" *"," ").concat(t)})).join(o)+o;return"/**"+o+(a?c:"")+(a&&h.length?" *"+o:"")+u+" */"};var a=/\*\/$/,r=/^\/\*\*/,h=/^\s*(\/\*\*?(.|\r?\n)*?\*\/)/,u=/(^|\s+)\/\/([^\r\n]*)/g,l=/^(\r?\n)+/,c=/(?:^|\r?\n) *(@[^\r\n]*?) *\r?\n *(?![^@\r\n]*\/\/[^]*)([^@\r\n\s][^@\r\n]+?) *\r?\n/g,p=/(?:^|\r?\n) *@(\S+) *([^\r\n]*)/g,d=/(\r?\n|^) *\* ?/g;function f(t){var e=(0,i().default)(t)||s().EOL;t=t.replace(r,"").replace(a,"").replace(d,"$1");for(var n="";n!==t;)n=t,t=t.replace(c,"".concat(e,"$1 $2").concat(e));t=t.replace(l,"").trimRight();for(var o,h=Object.create(null),f=t.replace(p,"").replace(l,"").trimRight();o=p.exec(t);){var m=o[2].replace(u,"");"string"==typeof h[o[1]]||Array.isArray(h[o[1]])?h[o[1]]=[].concat(h[o[1]],m):h[o[1]]=m}return{comments:f,pragmas:h}}function m(t,e){return[].concat(e).map((function(e){return"@".concat(t," ").concat(e).trim()}))}}));s(h);h.extract,h.strip,h.parse,h.parseWithComments,h.print;var u=function(t){var e=Object.keys(h.parse(h.extract(t)));return-1!==e.indexOf("prettier")||-1!==e.indexOf("format")},l=function(t){return t.length>0?t[t.length-1]:null};var c={locStart:function t(e,s){return!(s=s||{}).ignoreDecorators&&e.declaration&&e.declaration.decorators&&e.declaration.decorators.length>0?t(e.declaration.decorators[0]):!s.ignoreDecorators&&e.decorators&&e.decorators.length>0?t(e.decorators[0]):e.__location?e.__location.startOffset:e.range?e.range[0]:"number"==typeof e.start?e.start:e.loc?e.loc.start:null},locEnd:function t(e){var s=e.nodes&&l(e.nodes);if(s&&e.source&&!e.source.end&&(e=s),e.__location)return e.__location.endOffset;var i=e.range?e.range[1]:"number"==typeof e.end?e.end:null;return e.typeAnnotation?Math.max(i,t(e.typeAnnotation)):e.loc&&!i?e.loc.end:i}};function p(t){return(p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function d(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function f(t,e){for(var s=0;s<e.length;s++){var i=e[s];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function m(t,e,s){return e&&f(t.prototype,e),s&&f(t,s),t}function y(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&v(t,e)}function D(t){return(D=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function v(t,e){return(v=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function x(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function g(t,e){return!e||"object"!=typeof e&&"function"!=typeof e?x(t):e}function k(t,e,s){return(k="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,s){var i=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=D(t)););return t}(t,e);if(i){var a=Object.getOwnPropertyDescriptor(i,e);return a.get?a.get.call(s):a.value}})(t,e,s||t)}function P(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if(!(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t)))return;var s=[],i=!0,a=!1,r=void 0;try{for(var n,o=t[Symbol.iterator]();!(i=(n=o.next()).done)&&(s.push(n.value),!e||s.length!==e);i=!0);}catch(t){a=!0,r=t}finally{try{i||null==o.return||o.return()}finally{if(a)throw r}}return s}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function b(t){return function(t){if(Array.isArray(t)){for(var e=0,s=new Array(t.length);e<t.length;e++)s[e]=t[e];return s}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var E=function(t){return"string"==typeof t?t.replace(function(t){t=Object.assign({onlyFirst:!1},t);var e=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:[a-zA-Z\\d]*(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(e,t.onlyFirst?void 0:"g")}(),""):t},C=E,A=E;C.default=A;var w=function(t){return!Number.isNaN(t)&&(t>=4352&&(t<=4447||9001===t||9002===t||11904<=t&&t<=12871&&12351!==t||12880<=t&&t<=19903||19968<=t&&t<=42182||43360<=t&&t<=43388||44032<=t&&t<=55203||63744<=t&&t<=64255||65040<=t&&t<=65049||65072<=t&&t<=65131||65281<=t&&t<=65376||65504<=t&&t<=65510||110592<=t&&t<=110593||127488<=t&&t<=127569||131072<=t&&t<=262141))},T=w,F=w;T.default=F;var N=function(t){if("string"!=typeof(t=t.replace(/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F|\uD83D\uDC68(?:\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68\uD83C\uDFFB|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|[\u2695\u2696\u2708]\uFE0F|\uD83D[\uDC66\uDC67]|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708])\uFE0F|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C[\uDFFB-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)\uD83C\uDFFB|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB\uDFFC])|\uD83D\uDC69(?:\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB-\uDFFD])|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|(?:(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)\uFE0F|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\u200D[\u2640\u2642])|\uD83C\uDFF4\u200D\u2620)\uFE0F|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF4\uD83C\uDDF2|\uD83C\uDDF6\uD83C\uDDE6|[#\*0-9]\uFE0F\u20E3|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83D\uDC69(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270A-\u270D]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC70\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDCAA\uDD74\uDD7A\uDD90\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD36\uDDB5\uDDB6\uDDBB\uDDD2-\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5\uDEEB\uDEEC\uDEF4-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g,"  "))||0===t.length)return 0;t=C(t);for(var e=0,s=0;s<t.length;s++){var i=t.codePointAt(s);i<=31||i>=127&&i<=159||(i>=768&&i<=879||(i>65535&&s++,e+=T(i)?2:1))}return e},S=N,I=N;S.default=I;var L=/[|\\{}()[\]^$+*?.]/g,B=function(t){if("string"!=typeof t)throw new TypeError("Expected a string");return t.replace(L,"\\$&")},O=/[^\x20-\x7F]/;function M(t){if(t)switch(t.type){case"ExportDefaultDeclaration":case"ExportDefaultSpecifier":case"DeclareExportDeclaration":case"ExportNamedDeclaration":case"ExportAllDeclaration":return!0}return!1}function R(t){return function(e,s,i){var a=i&&i.backwards;if(!1===s)return!1;for(var r=e.length,n=s;n>=0&&n<r;){var o=e.charAt(n);if(t instanceof RegExp){if(!t.test(o))return n}else if(-1===t.indexOf(o))return n;a?n--:n++}return(-1===n||n===r)&&n}}var _=R(/\s/),j=R(" \t"),q=R(",; \t"),U=R(/[^\r\n]/);function V(t,e){if(!1===e)return!1;if("/"===t.charAt(e)&&"*"===t.charAt(e+1))for(var s=e+2;s<t.length;++s)if("*"===t.charAt(s)&&"/"===t.charAt(s+1))return s+2;return e}function z(t,e){return!1!==e&&("/"===t.charAt(e)&&"/"===t.charAt(e+1)?U(t,e):e)}function W(t,e,s){var i=s&&s.backwards;if(!1===e)return!1;var a=t.charAt(e);if(i){if("\r"===t.charAt(e-1)&&"\n"===a)return e-2;if("\n"===a||"\r"===a||"\u2028"===a||"\u2029"===a)return e-1}else{if("\r"===a&&"\n"===t.charAt(e+1))return e+2;if("\n"===a||"\r"===a||"\u2028"===a||"\u2029"===a)return e+1}return e}function K(t,e,s){var i=j(t,(s=s||{}).backwards?e-1:e,s);return i!==W(t,i,s)}function X(t,e){for(var s=null,i=e;i!==s;)s=i,i=V(t,i=q(t,i)),i=j(t,i);return!1!==(i=W(t,i=z(t,i)))&&K(t,i)}function J(t,e){for(var s=null,i=e;i!==s;)s=i,i=W(t,i=z(t,i=V(t,i=j(t,i))));return i}function H(t,e,s){return J(t,s(e))}var G={};function Q(t){return G[t]}[["|>"],["??"],["||"],["&&"],["|"],["^"],["&"],["==","===","!=","!=="],["<",">","<=",">=","in","instanceof"],[">>","<<",">>>"],["+","-"],["*","/","%"],["**"]].forEach((function(t,e){t.forEach((function(t){G[t]=e}))}));var $={"==":!0,"!=":!0,"===":!0,"!==":!0},Y={"*":!0,"/":!0,"%":!0},Z={">>":!0,">>>":!0,"<<":!0};function tt(t,e,s){for(var i=0,a=s=s||0;a<t.length;++a)"\t"===t[a]?i=i+e-i%e:i++;return i}function et(t,e){var s=t.slice(1,-1),i={quote:'"',regex:/"/g},a={quote:"'",regex:/'/g},r="'"===e?a:i,n=r===a?i:a,o=r.quote;(s.includes(r.quote)||s.includes(n.quote))&&(o=(s.match(r.regex)||[]).length>(s.match(n.regex)||[]).length?n.quote:r.quote);return o}function st(t,e,s){var i='"'===e?"'":'"',a=t.replace(/\\([\s\S])|(['"])/g,(function(t,a,r){return a===i?a:r===e?"\\"+r:r||(s&&/^[^\\nrvtbfux\r\n\u2028\u2029"'0-7]$/.test(a)?a:"\\"+a)}));return e+a+e}function it(t){return t&&t.comments&&t.comments.length>0&&t.comments.some((function(t){return"prettier-ignore"===t.value.trim()}))}function at(t,e){(t.comments||(t.comments=[])).push(e),e.printed=!1,"JSXText"===t.type&&(e.printed=!0)}var rt={replaceEndOfLineWith:function(t,e){var s=[],i=!0,a=!1,r=void 0;try{for(var n,o=t.split("\n")[Symbol.iterator]();!(i=(n=o.next()).done);i=!0){var h=n.value;0!==s.length&&s.push(e),s.push(h)}}catch(t){a=!0,r=t}finally{try{i||null==o.return||o.return()}finally{if(a)throw r}}return s},getStringWidth:function(t){return t?O.test(t)?S(t):t.length:0},getMaxContinuousCount:function(t,e){var s=t.match(new RegExp("(".concat(B(e),")+"),"g"));return null===s?0:s.reduce((function(t,s){return Math.max(t,s.length/e.length)}),0)},getMinNotPresentContinuousCount:function(t,e){var s=t.match(new RegExp("(".concat(B(e),")+"),"g"));if(null===s)return 0;var i=new Map,a=0,r=!0,n=!1,o=void 0;try{for(var h,u=s[Symbol.iterator]();!(r=(h=u.next()).done);r=!0){var l=h.value.length/e.length;i.set(l,!0),l>a&&(a=l)}}catch(t){n=!0,o=t}finally{try{r||null==u.return||u.return()}finally{if(n)throw o}}for(var c=1;c<a;c++)if(!i.get(c))return c;return a+1},getPrecedence:Q,shouldFlatten:function(t,e){return Q(e)===Q(t)&&("**"!==t&&((!$[t]||!$[e])&&(!("%"===e&&Y[t]||"%"===t&&Y[e])&&((e===t||!Y[e]||!Y[t])&&(!Z[t]||!Z[e])))))},isBitwiseOperator:function(t){return!!Z[t]||"|"===t||"^"===t||"&"===t},isExportDeclaration:M,getParentExportDeclaration:function(t){var e=t.getParentNode();return"declaration"===t.getName()&&M(e)?e:null},getPenultimate:function(t){return t.length>1?t[t.length-2]:null},getLast:l,getNextNonSpaceNonCommentCharacterIndexWithStartIndex:J,getNextNonSpaceNonCommentCharacterIndex:H,getNextNonSpaceNonCommentCharacter:function(t,e,s){return t.charAt(H(t,e,s))},skip:R,skipWhitespace:_,skipSpaces:j,skipToLineEnd:q,skipEverythingButNewLine:U,skipInlineComment:V,skipTrailingComment:z,skipNewline:W,isNextLineEmptyAfterIndex:X,isNextLineEmpty:function(t,e,s){return X(t,s(e))},isPreviousLineEmpty:function(t,e,s){var i=s(e)-1;return i=W(t,i=j(t,i,{backwards:!0}),{backwards:!0}),(i=j(t,i,{backwards:!0}))!==W(t,i,{backwards:!0})},hasNewline:K,hasNewlineInRange:function(t,e,s){for(var i=e;i<s;++i)if("\n"===t.charAt(i))return!0;return!1},hasSpaces:function(t,e,s){return j(t,(s=s||{}).backwards?e-1:e,s)!==e},setLocStart:function(t,e){t.range?t.range[0]=e:t.start=e},setLocEnd:function(t,e){t.range?t.range[1]=e:t.end=e},startsWithNoLookaheadToken:function t(e,s){switch((e=function t(e){if(e.left)return t(e.left);return e}(e)).type){case"FunctionExpression":case"ClassExpression":case"DoExpression":return s;case"ObjectExpression":return!0;case"MemberExpression":case"OptionalMemberExpression":return t(e.object,s);case"TaggedTemplateExpression":return"FunctionExpression"!==e.tag.type&&t(e.tag,s);case"CallExpression":case"OptionalCallExpression":return"FunctionExpression"!==e.callee.type&&t(e.callee,s);case"ConditionalExpression":return t(e.test,s);case"UpdateExpression":return!e.prefix&&t(e.argument,s);case"BindExpression":return e.object&&t(e.object,s);case"SequenceExpression":return t(e.expressions[0],s);case"TSAsExpression":return t(e.expression,s);default:return!1}},getAlignmentSize:tt,getIndentSize:function(t,e){var s=t.lastIndexOf("\n");return-1===s?0:tt(t.slice(s+1).match(/^[ \t]*/)[0],e)},getPreferredQuote:et,printString:function(t,e,s){var i=t.slice(1,-1),a=!i.includes('"')&&!i.includes("'"),r="json"===e.parser?'"':e.__isInHtmlAttribute?"'":et(t,e.singleQuote?"'":'"');return s?a?r+i+r:t:st(i,r,!("css"===e.parser||"less"===e.parser||"scss"===e.parser||e.embeddedInHtml))},printNumber:function(t){return t.toLowerCase().replace(/^([+-]?[\d.]+e)(?:\+|(-))?0*(\d)/,"$1$2$3").replace(/^([+-]?[\d.]+)e[+-]?0+$/,"$1").replace(/^([+-])?\./,"$10.").replace(/(\.\d+?)0+(?=e|$)/,"$1").replace(/\.(?=e|$)/,"")},hasIgnoreComment:function(t){return it(t.getValue())},hasNodeIgnoreComment:it,makeString:st,matchAncestorTypes:function(t,e,s){for(s=s||0,e=e.slice();e.length;){var i=t.getParentNode(s),a=e.shift();if(!i||i.type!==a)return!1;s++}return!0},addLeadingComment:function(t,e){e.leading=!0,e.trailing=!1,at(t,e)},addDanglingComment:function(t,e){e.leading=!1,e.trailing=!1,at(t,e)},addTrailingComment:function(t,e){e.leading=!1,e.trailing=!0,at(t,e)},isWithinParentArrayProperty:function(t,e){var s=t.getValue(),i=t.getParentNode();if(null==i)return!1;if(!Array.isArray(i[e]))return!1;var a=t.getName();return i[e][a]===s}}.getLast;var nt=function(t,e){return function t(e,s,i,a){if(!e||"object"!==p(e))return;if(Array.isArray(e)){for(var r=0;r<e.length;r++)t(e[r],s,e,r);return}if("string"!=typeof e.type)return;for(var n=0,o=Object.keys(e);n<o.length;n++){var h=o[n];t(e[h],s,e,h)}var u=s(e);u&&(i[a]=u)}(t,(function(t){switch(t.type){case"VariableDeclaration":var s=rt(t.declarations);s&&s.init&&function(t,s){if(";"===e.originalText[(i=s,"flow"===e.parser?i.range[1]:i.end)])return;var i;"flow"===e.parser?t.range=[t.range[0],s.range[1]]:t.end=s.end;t.loc=Object.assign({},t.loc,{end:t.loc.end})}(t,s);break;case"TSParenthesizedType":return t.typeAnnotation;case"TSUnionType":case"TSIntersectionType":if(1===t.types.length)return Object.assign({},t.types[0],{loc:t.loc,range:t.range});break;case"EnumDeclaration":"flow"===e.parser&&t.body.range[0]===t.range[0]&&t.body.range[1]===t.range[1]&&(t.body.range=[t.id.range[1],t.range[1]-1]),"babel-flow"===e.parser&&(t.body.start=t.id.end,t.body.end=t.end-1)}})),t},ot=i((function(t,e){Object.defineProperty(e,"__esModule",{value:!0});var s=!0,i=function t(e){var s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};d(this,t),this.label=e,this.keyword=s.keyword,this.beforeExpr=!!s.beforeExpr,this.startsExpr=!!s.startsExpr,this.rightAssociative=!!s.rightAssociative,this.isLoop=!!s.isLoop,this.isAssign=!!s.isAssign,this.prefix=!!s.prefix,this.postfix=!!s.postfix,this.binop=null!=s.binop?s.binop:null,this.updateContext=null},a=new Map;function r(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e.keyword=t;var s=new i(t,e);return a.set(t,s),s}function n(t,e){return new i(t,{beforeExpr:s,binop:e})}var o={num:new i("num",{startsExpr:!0}),bigint:new i("bigint",{startsExpr:!0}),regexp:new i("regexp",{startsExpr:!0}),string:new i("string",{startsExpr:!0}),name:new i("name",{startsExpr:!0}),eof:new i("eof"),bracketL:new i("[",{beforeExpr:s,startsExpr:!0}),bracketR:new i("]"),braceL:new i("{",{beforeExpr:s,startsExpr:!0}),braceBarL:new i("{|",{beforeExpr:s,startsExpr:!0}),braceR:new i("}"),braceBarR:new i("|}"),parenL:new i("(",{beforeExpr:s,startsExpr:!0}),parenR:new i(")"),comma:new i(",",{beforeExpr:s}),semi:new i(";",{beforeExpr:s}),colon:new i(":",{beforeExpr:s}),doubleColon:new i("::",{beforeExpr:s}),dot:new i("."),question:new i("?",{beforeExpr:s}),questionDot:new i("?."),arrow:new i("=>",{beforeExpr:s}),template:new i("template"),ellipsis:new i("...",{beforeExpr:s}),backQuote:new i("`",{startsExpr:!0}),dollarBraceL:new i("${",{beforeExpr:s,startsExpr:!0}),at:new i("@"),hash:new i("#",{startsExpr:!0}),interpreterDirective:new i("#!..."),eq:new i("=",{beforeExpr:s,isAssign:!0}),assign:new i("_=",{beforeExpr:s,isAssign:!0}),incDec:new i("++/--",{prefix:!0,postfix:!0,startsExpr:!0}),bang:new i("!",{beforeExpr:s,prefix:!0,startsExpr:!0}),tilde:new i("~",{beforeExpr:s,prefix:!0,startsExpr:!0}),pipeline:n("|>",0),nullishCoalescing:n("??",1),logicalOR:n("||",2),logicalAND:n("&&",3),bitwiseOR:n("|",4),bitwiseXOR:n("^",5),bitwiseAND:n("&",6),equality:n("==/!=/===/!==",7),relational:n("</>/<=/>=",8),bitShift:n("<</>>/>>>",9),plusMin:new i("+/-",{beforeExpr:s,binop:10,prefix:!0,startsExpr:!0}),modulo:new i("%",{beforeExpr:s,binop:11,startsExpr:!0}),star:n("*",11),slash:n("/",11),exponent:new i("**",{beforeExpr:s,binop:12,rightAssociative:!0}),_break:r("break"),_case:r("case",{beforeExpr:s}),_catch:r("catch"),_continue:r("continue"),_debugger:r("debugger"),_default:r("default",{beforeExpr:s}),_do:r("do",{isLoop:!0,beforeExpr:s}),_else:r("else",{beforeExpr:s}),_finally:r("finally"),_for:r("for",{isLoop:!0}),_function:r("function",{startsExpr:!0}),_if:r("if"),_return:r("return",{beforeExpr:s}),_switch:r("switch"),_throw:r("throw",{beforeExpr:s,prefix:!0,startsExpr:!0}),_try:r("try"),_var:r("var"),_const:r("const"),_while:r("while",{isLoop:!0}),_with:r("with"),_new:r("new",{beforeExpr:s,startsExpr:!0}),_this:r("this",{startsExpr:!0}),_super:r("super",{startsExpr:!0}),_class:r("class",{startsExpr:!0}),_extends:r("extends",{beforeExpr:s}),_export:r("export"),_import:r("import",{startsExpr:!0}),_null:r("null",{startsExpr:!0}),_true:r("true",{startsExpr:!0}),_false:r("false",{startsExpr:!0}),_in:r("in",{beforeExpr:s,binop:8}),_instanceof:r("instanceof",{beforeExpr:s,binop:8}),_typeof:r("typeof",{beforeExpr:s,prefix:!0,startsExpr:!0}),_void:r("void",{beforeExpr:s,prefix:!0,startsExpr:!0}),_delete:r("delete",{beforeExpr:s,prefix:!0,startsExpr:!0})},h=2,u=4,l=8,c=513|h;function p(t,e){return h|(t?u:0)|(e?l:0)}function f(t){return null!=t&&"Property"===t.type&&"init"===t.kind&&!1===t.method}var v=/\r\n?|[\n\u2028\u2029]/,E=new RegExp(v.source,"g");function C(t){switch(t){case 10:case 13:case 8232:case 8233:return!0;default:return!1}}var A=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g;function w(t){switch(t){case 9:case 11:case 12:case 32:case 160:case 5760:case 8192:case 8193:case 8194:case 8195:case 8196:case 8197:case 8198:case 8199:case 8200:case 8201:case 8202:case 8239:case 8287:case 12288:case 65279:return!0;default:return!1}}var T=function t(e,s,i,a){d(this,t),this.token=e,this.isExpr=!!s,this.preserveSpace=!!i,this.override=a},F={braceStatement:new T("{",!1),braceExpression:new T("{",!0),templateQuasi:new T("${",!1),parenStatement:new T("(",!1),parenExpression:new T("(",!0),template:new T("`",!0,!0,(function(t){return t.readTmplToken()})),functionExpression:new T("function",!0),functionStatement:new T("function",!1)};o.parenR.updateContext=o.braceR.updateContext=function(){if(1!==this.state.context.length){var t=this.state.context.pop();t===F.braceStatement&&"function"===this.curContext().token&&(t=this.state.context.pop()),this.state.exprAllowed=!t.isExpr}else this.state.exprAllowed=!0},o.name.updateContext=function(t){var e=!1;t!==o.dot&&("of"===this.state.value&&!this.state.exprAllowed||"yield"===this.state.value&&this.scope.inGenerator)&&(e=!0),this.state.exprAllowed=e,this.state.isIterator&&(this.state.isIterator=!1)},o.braceL.updateContext=function(t){this.state.context.push(this.braceIsBlock(t)?F.braceStatement:F.braceExpression),this.state.exprAllowed=!0},o.dollarBraceL.updateContext=function(){this.state.context.push(F.templateQuasi),this.state.exprAllowed=!0},o.parenL.updateContext=function(t){var e=t===o._if||t===o._for||t===o._with||t===o._while;this.state.context.push(e?F.parenStatement:F.parenExpression),this.state.exprAllowed=!0},o.incDec.updateContext=function(){},o._function.updateContext=o._class.updateContext=function(t){!t.beforeExpr||t===o.semi||t===o._else||t===o._return&&v.test(this.input.slice(this.state.lastTokEnd,this.state.start))||(t===o.colon||t===o.braceL)&&this.curContext()===F.b_stat?this.state.context.push(F.functionStatement):this.state.context.push(F.functionExpression),this.state.exprAllowed=!1},o.backQuote.updateContext=function(){this.curContext()===F.template?this.state.context.pop():this.state.context.push(F.template),this.state.exprAllowed=!1};var N=["eval","arguments"],S=new Set(["implements","interface","let","package","private","protected","public","static","yield"]),I=new Set(N),L=function(t,e){return e&&"await"===t||"enum"===t};function B(t,e){return L(t,e)||S.has(t)}function O(t){return I.has(t)}function M(t,e){return B(t,e)||O(t)}var R=/^in(stanceof)?$/,_="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࢠ-ࢴࢶ-ࢽऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഅ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆺㇰ-ㇿ㐀-䶵一-鿯ꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞿꟂ-Ᶎꟷ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭧꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",j="‌‍·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࣓-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍ୖୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ංඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-ໍ໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜔ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠐-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷹᷻-᷿‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿",q=new RegExp("["+_+"]"),U=new RegExp("["+_+j+"]");_=j=null;var V=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,157,310,10,21,11,7,153,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,477,28,11,0,9,21,155,22,13,52,76,44,33,24,27,35,30,0,12,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,85,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,0,33,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,230,43,117,63,32,0,161,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,35,56,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,270,921,103,110,18,195,2749,1070,4050,582,8634,568,8,30,114,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,754,9486,286,50,2,18,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,2357,44,11,6,17,0,370,43,1301,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42710,42,4148,12,221,3,5761,15,7472,3104,541],z=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,525,10,176,2,54,14,32,9,16,3,46,10,54,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,4,9,83,11,7,0,161,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,232,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,19306,9,135,4,60,6,26,9,1014,0,2,54,8,3,19723,1,5319,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,262,6,10,9,419,13,1495,6,110,6,6,9,792487,239];function W(t,e){for(var s=65536,i=0,a=e.length;i<a;i+=2){if((s+=e[i])>t)return!1;if((s+=e[i+1])>=t)return!0}return!1}function K(t){return t<65?36===t:t<=90||(t<97?95===t:t<=122||(t<=65535?t>=170&&q.test(String.fromCharCode(t)):W(t,V)))}function X(t){return t<48?36===t:t<58||!(t<65)&&(t<=90||(t<97?95===t:t<=122||(t<=65535?t>=170&&U.test(String.fromCharCode(t)):W(t,V)||W(t,z))))}var J=["any","bool","boolean","empty","false","mixed","null","number","static","string","true","typeof","void","interface","extends","_"];function H(t){return"type"===t.importKind||"typeof"===t.importKind}function G(t){return(t.type===o.name||!!t.type.keyword)&&"from"!==t.value}var Q={const:"declare export var",let:"declare export var",type:"export type",interface:"export interface"};var $=/\*?\s*@((?:no)?flow)\b/,Y={quot:'"',amp:"&",apos:"'",lt:"<",gt:">",nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",fnof:"ƒ",circ:"ˆ",tilde:"˜",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",bull:"•",hellip:"…",permil:"‰",prime:"′",Prime:"″",lsaquo:"‹",rsaquo:"›",oline:"‾",frasl:"⁄",euro:"€",image:"ℑ",weierp:"℘",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦"},Z=/^[\da-fA-F]+$/,tt=/^\d+$/;function et(t){return!!t&&("JSXOpeningFragment"===t.type||"JSXClosingFragment"===t.type)}function st(t){if("JSXIdentifier"===t.type)return t.name;if("JSXNamespacedName"===t.type)return t.namespace.name+":"+t.name.name;if("JSXMemberExpression"===t.type)return st(t.object)+"."+st(t.property);throw new Error("Node had unexpected type: "+t.type)}F.j_oTag=new T("<tag",!1),F.j_cTag=new T("</tag",!1),F.j_expr=new T("<tag>...</tag>",!0,!0),o.jsxName=new i("jsxName"),o.jsxText=new i("jsxText",{beforeExpr:!0}),o.jsxTagStart=new i("jsxTagStart",{startsExpr:!0}),o.jsxTagEnd=new i("jsxTagEnd"),o.jsxTagStart.updateContext=function(){this.state.context.push(F.j_expr),this.state.context.push(F.j_oTag),this.state.exprAllowed=!1},o.jsxTagEnd.updateContext=function(t){var e=this.state.context.pop();e===F.j_oTag&&t===o.slash||e===F.j_cTag?(this.state.context.pop(),this.state.exprAllowed=this.curContext()===F.j_expr):this.state.exprAllowed=!0};var it=function t(e){d(this,t),this.var=[],this.lexical=[],this.functions=[],this.flags=e},at=function(){function t(e,s){d(this,t),this.scopeStack=[],this.undefinedExports=new Map,this.raise=e,this.inModule=s}return m(t,[{key:"createScope",value:function(t){return new it(t)}},{key:"enter",value:function(t){this.scopeStack.push(this.createScope(t))}},{key:"exit",value:function(){this.scopeStack.pop()}},{key:"treatFunctionsAsVarInScope",value:function(t){return!!(t.flags&h||!this.inModule&&1&t.flags)}},{key:"declareName",value:function(t,e,s){var i=this.currentScope();if(8&e||16&e)this.checkRedeclarationInScope(i,t,e,s),16&e?i.functions.push(t):i.lexical.push(t),8&e&&this.maybeExportDefined(i,t);else if(4&e)for(var a=this.scopeStack.length-1;a>=0&&(i=this.scopeStack[a],this.checkRedeclarationInScope(i,t,e,s),i.var.push(t),this.maybeExportDefined(i,t),!(i.flags&c));--a);this.inModule&&1&i.flags&&this.undefinedExports.delete(t)}},{key:"maybeExportDefined",value:function(t,e){this.inModule&&1&t.flags&&this.undefinedExports.delete(e)}},{key:"checkRedeclarationInScope",value:function(t,e,s,i){this.isRedeclaredInScope(t,e,s)&&this.raise(i,"Identifier '".concat(e,"' has already been declared"))}},{key:"isRedeclaredInScope",value:function(t,e,s){return!!(1&s)&&(8&s?t.lexical.indexOf(e)>-1||t.functions.indexOf(e)>-1||t.var.indexOf(e)>-1:16&s?t.lexical.indexOf(e)>-1||!this.treatFunctionsAsVarInScope(t)&&t.var.indexOf(e)>-1:t.lexical.indexOf(e)>-1&&!(32&t.flags&&t.lexical[0]===e)||!this.treatFunctionsAsVarInScope(t)&&t.functions.indexOf(e)>-1)}},{key:"checkLocalExport",value:function(t){-1===this.scopeStack[0].lexical.indexOf(t.name)&&-1===this.scopeStack[0].var.indexOf(t.name)&&-1===this.scopeStack[0].functions.indexOf(t.name)&&this.undefinedExports.set(t.name,t.start)}},{key:"currentScope",value:function(){return this.scopeStack[this.scopeStack.length-1]}},{key:"currentVarScope",value:function(){for(var t=this.scopeStack.length-1;;t--){var e=this.scopeStack[t];if(e.flags&c)return e}}},{key:"currentThisScope",value:function(){for(var t=this.scopeStack.length-1;;t--){var e=this.scopeStack[t];if((e.flags&c||256&e.flags)&&!(16&e.flags))return e}}},{key:"inFunction",get:function(){return(this.currentVarScope().flags&h)>0}},{key:"inGenerator",get:function(){return(this.currentVarScope().flags&l)>0}},{key:"inAsync",get:function(){return(this.currentVarScope().flags&u)>0}},{key:"allowSuper",get:function(){return(64&this.currentThisScope().flags)>0}},{key:"allowDirectSuper",get:function(){return(128&this.currentThisScope().flags)>0}},{key:"inNonArrowFunction",get:function(){return(this.currentThisScope().flags&h)>0}},{key:"treatFunctionsAsVar",get:function(){return this.treatFunctionsAsVarInScope(this.currentScope())}}]),t}(),rt=function(t){function e(){var t,s;d(this,e);for(var i=arguments.length,a=new Array(i),r=0;r<i;r++)a[r]=arguments[r];return(s=g(this,(t=D(e)).call.apply(t,[this].concat(a)))).types=[],s.enums=[],s.constEnums=[],s.classes=[],s.exportOnlyBindings=[],s}return y(e,t),e}(it),nt=function(t){function e(){return d(this,e),g(this,D(e).apply(this,arguments))}return y(e,t),m(e,[{key:"createScope",value:function(t){return new rt(t)}},{key:"declareName",value:function(t,s,i){var a=this.currentScope();if(1024&s)return this.maybeExportDefined(a,t),void a.exportOnlyBindings.push(t);k(D(e.prototype),"declareName",this).apply(this,arguments),2&s&&(1&s||(this.checkRedeclarationInScope(a,t,s,i),this.maybeExportDefined(a,t)),a.types.push(t)),256&s&&a.enums.push(t),512&s&&a.constEnums.push(t),128&s&&a.classes.push(t)}},{key:"isRedeclaredInScope",value:function(t,s,i){if(t.enums.indexOf(s)>-1){if(256&i){var a=!!(512&i),r=t.constEnums.indexOf(s)>-1;return a!==r}return!0}return 128&i&&t.classes.indexOf(s)>-1?t.lexical.indexOf(s)>-1&&!!(1&i):!!(2&i&&t.types.indexOf(s)>-1)||k(D(e.prototype),"isRedeclaredInScope",this).apply(this,arguments)}},{key:"checkLocalExport",value:function(t){-1===this.scopeStack[0].types.indexOf(t.name)&&-1===this.scopeStack[0].exportOnlyBindings.indexOf(t.name)&&k(D(e.prototype),"checkLocalExport",this).call(this,t)}}]),e}(at);function ot(t){if(null==t)throw new Error("Unexpected ".concat(t," value."));return t}function ht(t){if(!t)throw new Error("Assert fail")}o.placeholder=new i("%%",{startsExpr:!0});function ut(t,e){return t.some((function(t){return Array.isArray(t)?t[0]===e:t===e}))}function lt(t,e,s){var i=t.find((function(t){return Array.isArray(t)?t[0]===e:t===e}));return i&&Array.isArray(i)?i[1][s]:null}var ct=["minimal","smart","fsharp"];var pt={estree:function(t){return function(t){function e(){return d(this,e),g(this,D(e).apply(this,arguments))}return y(e,t),m(e,[{key:"estreeParseRegExpLiteral",value:function(t){var e=t.pattern,s=t.flags,i=null;try{i=new RegExp(e,s)}catch(t){}var a=this.estreeParseLiteral(i);return a.regex={pattern:e,flags:s},a}},{key:"estreeParseLiteral",value:function(t){return this.parseLiteral(t,"Literal")}},{key:"directiveToStmt",value:function(t){var e=t.value,s=this.startNodeAt(t.start,t.loc.start),i=this.startNodeAt(e.start,e.loc.start);return i.value=e.value,i.raw=e.extra.raw,s.expression=this.finishNodeAt(i,"Literal",e.end,e.loc.end),s.directive=e.extra.raw.slice(1,-1),this.finishNodeAt(s,"ExpressionStatement",t.end,t.loc.end)}},{key:"initFunction",value:function(t,s){k(D(e.prototype),"initFunction",this).call(this,t,s),t.expression=!1}},{key:"checkDeclaration",value:function(t){f(t)?this.checkDeclaration(t.value):k(D(e.prototype),"checkDeclaration",this).call(this,t)}},{key:"checkGetterSetterParams",value:function(t){var e=t,s="get"===e.kind?0:1,i=e.start;e.value.params.length!==s?"get"===e.kind?this.raise(i,"getter must not have any formal parameters"):this.raise(i,"setter must have exactly one formal parameter"):"set"===e.kind&&"RestElement"===e.value.params[0].type&&this.raise(i,"setter function argument must not be a rest parameter")}},{key:"checkLVal",value:function(t){var s=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:64,a=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,n=arguments.length>4?arguments[4]:void 0;switch(t.type){case"ObjectPattern":t.properties.forEach((function(t){s.checkLVal("Property"===t.type?t.value:t,i,a,"object destructuring pattern",n)}));break;default:k(D(e.prototype),"checkLVal",this).call(this,t,i,a,r,n)}}},{key:"checkDuplicatedProto",value:function(t,e){if(!("SpreadElement"===t.type||t.computed||t.method||t.shorthand)){var s=t.key;"__proto__"===("Identifier"===s.type?s.name:String(s.value))&&"init"===t.kind&&(e.used&&!e.start&&(e.start=s.start),e.used=!0)}}},{key:"isStrictBody",value:function(t){if("BlockStatement"===t.body.type&&t.body.body.length>0)for(var e=0,s=t.body.body;e<s.length;e++){var i=s[e];if("ExpressionStatement"!==i.type||"Literal"!==i.expression.type)break;if("use strict"===i.expression.value)return!0}return!1}},{key:"isValidDirective",value:function(t){return!("ExpressionStatement"!==t.type||"Literal"!==t.expression.type||"string"!=typeof t.expression.value||t.expression.extra&&t.expression.extra.parenthesized)}},{key:"stmtToDirective",value:function(t){var s=k(D(e.prototype),"stmtToDirective",this).call(this,t),i=t.expression.value;return s.value.value=i,s}},{key:"parseBlockBody",value:function(t,s,i,a){var r=this;k(D(e.prototype),"parseBlockBody",this).call(this,t,s,i,a);var n=t.directives.map((function(t){return r.directiveToStmt(t)}));t.body=n.concat(t.body),delete t.directives}},{key:"pushClassMethod",value:function(t,e,s,i,a,r){this.parseMethod(e,s,i,a,r,"ClassMethod",!0),e.typeParameters&&(e.value.typeParameters=e.typeParameters,delete e.typeParameters),t.body.push(e)}},{key:"parseExprAtom",value:function(t){switch(this.state.type){case o.regexp:return this.estreeParseRegExpLiteral(this.state.value);case o.num:case o.string:return this.estreeParseLiteral(this.state.value);case o._null:return this.estreeParseLiteral(null);case o._true:return this.estreeParseLiteral(!0);case o._false:return this.estreeParseLiteral(!1);default:return k(D(e.prototype),"parseExprAtom",this).call(this,t)}}},{key:"parseLiteral",value:function(t,s,i,a){var r=k(D(e.prototype),"parseLiteral",this).call(this,t,s,i,a);return r.raw=r.extra.raw,delete r.extra,r}},{key:"parseFunctionBody",value:function(t,s){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];k(D(e.prototype),"parseFunctionBody",this).call(this,t,s,i),t.expression="BlockStatement"!==t.body.type}},{key:"parseMethod",value:function(t,s,i,a,r,n){var o=arguments.length>6&&void 0!==arguments[6]&&arguments[6],h=this.startNode();return h.kind=t.kind,(h=k(D(e.prototype),"parseMethod",this).call(this,h,s,i,a,r,n,o)).type="FunctionExpression",delete h.kind,t.value=h,n="ClassMethod"===n?"MethodDefinition":n,this.finishNode(t,n)}},{key:"parseObjectMethod",value:function(t,s,i,a,r){var n=k(D(e.prototype),"parseObjectMethod",this).call(this,t,s,i,a,r);return n&&(n.type="Property","method"===n.kind&&(n.kind="init"),n.shorthand=!1),n}},{key:"parseObjectProperty",value:function(t,s,i,a,r){var n=k(D(e.prototype),"parseObjectProperty",this).call(this,t,s,i,a,r);return n&&(n.kind="init",n.type="Property"),n}},{key:"toAssignable",value:function(t,s,i){return f(t)?(this.toAssignable(t.value,s,i),t):k(D(e.prototype),"toAssignable",this).call(this,t,s,i)}},{key:"toAssignableObjectExpressionProp",value:function(t,s,i){if("get"===t.kind||"set"===t.kind)throw this.raise(t.key.start,"Object pattern can't contain getter or setter");if(t.method)throw this.raise(t.key.start,"Object pattern can't contain methods");k(D(e.prototype),"toAssignableObjectExpressionProp",this).call(this,t,s,i)}}]),e}(t)},jsx:function(t){return function(t){function e(){return d(this,e),g(this,D(e).apply(this,arguments))}return y(e,t),m(e,[{key:"jsxReadToken",value:function(){for(var t="",s=this.state.pos;;){if(this.state.pos>=this.length)throw this.raise(this.state.start,"Unterminated JSX contents");var i=this.input.charCodeAt(this.state.pos);switch(i){case 60:case 123:return this.state.pos===this.state.start?60===i&&this.state.exprAllowed?(++this.state.pos,this.finishToken(o.jsxTagStart)):k(D(e.prototype),"getTokenFromCode",this).call(this,i):(t+=this.input.slice(s,this.state.pos),this.finishToken(o.jsxText,t));case 38:t+=this.input.slice(s,this.state.pos),t+=this.jsxReadEntity(),s=this.state.pos;break;default:C(i)?(t+=this.input.slice(s,this.state.pos),t+=this.jsxReadNewLine(!0),s=this.state.pos):++this.state.pos}}}},{key:"jsxReadNewLine",value:function(t){var e,s=this.input.charCodeAt(this.state.pos);return++this.state.pos,13===s&&10===this.input.charCodeAt(this.state.pos)?(++this.state.pos,e=t?"\n":"\r\n"):e=String.fromCharCode(s),++this.state.curLine,this.state.lineStart=this.state.pos,e}},{key:"jsxReadString",value:function(t){for(var e="",s=++this.state.pos;;){if(this.state.pos>=this.length)throw this.raise(this.state.start,"Unterminated string constant");var i=this.input.charCodeAt(this.state.pos);if(i===t)break;38===i?(e+=this.input.slice(s,this.state.pos),e+=this.jsxReadEntity(),s=this.state.pos):C(i)?(e+=this.input.slice(s,this.state.pos),e+=this.jsxReadNewLine(!1),s=this.state.pos):++this.state.pos}return e+=this.input.slice(s,this.state.pos++),this.finishToken(o.string,e)}},{key:"jsxReadEntity",value:function(){for(var t,e="",s=0,i=this.input[this.state.pos],a=++this.state.pos;this.state.pos<this.length&&s++<10;){if(";"===(i=this.input[this.state.pos++])){"#"===e[0]?"x"===e[1]?(e=e.substr(2),Z.test(e)&&(t=String.fromCodePoint(parseInt(e,16)))):(e=e.substr(1),tt.test(e)&&(t=String.fromCodePoint(parseInt(e,10)))):t=Y[e];break}e+=i}return t||(this.state.pos=a,"&")}},{key:"jsxReadWord",value:function(){var t,e=this.state.pos;do{t=this.input.charCodeAt(++this.state.pos)}while(X(t)||45===t);return this.finishToken(o.jsxName,this.input.slice(e,this.state.pos))}},{key:"jsxParseIdentifier",value:function(){var t=this.startNode();return this.match(o.jsxName)?t.name=this.state.value:this.state.type.keyword?t.name=this.state.type.keyword:this.unexpected(),this.next(),this.finishNode(t,"JSXIdentifier")}},{key:"jsxParseNamespacedName",value:function(){var t=this.state.start,e=this.state.startLoc,s=this.jsxParseIdentifier();if(!this.eat(o.colon))return s;var i=this.startNodeAt(t,e);return i.namespace=s,i.name=this.jsxParseIdentifier(),this.finishNode(i,"JSXNamespacedName")}},{key:"jsxParseElementName",value:function(){var t=this.state.start,e=this.state.startLoc,s=this.jsxParseNamespacedName();if("JSXNamespacedName"===s.type)return s;for(;this.eat(o.dot);){var i=this.startNodeAt(t,e);i.object=s,i.property=this.jsxParseIdentifier(),s=this.finishNode(i,"JSXMemberExpression")}return s}},{key:"jsxParseAttributeValue",value:function(){var t;switch(this.state.type){case o.braceL:return t=this.startNode(),this.next(),"JSXEmptyExpression"===(t=this.jsxParseExpressionContainer(t)).expression.type&&this.raise(t.start,"JSX attributes must only be assigned a non-empty expression"),t;case o.jsxTagStart:case o.string:return this.parseExprAtom();default:throw this.raise(this.state.start,"JSX value should be either an expression or a quoted JSX text")}}},{key:"jsxParseEmptyExpression",value:function(){var t=this.startNodeAt(this.state.lastTokEnd,this.state.lastTokEndLoc);return this.finishNodeAt(t,"JSXEmptyExpression",this.state.start,this.state.startLoc)}},{key:"jsxParseSpreadChild",value:function(t){return this.next(),t.expression=this.parseExpression(),this.expect(o.braceR),this.finishNode(t,"JSXSpreadChild")}},{key:"jsxParseExpressionContainer",value:function(t){return this.match(o.braceR)?t.expression=this.jsxParseEmptyExpression():t.expression=this.parseExpression(),this.expect(o.braceR),this.finishNode(t,"JSXExpressionContainer")}},{key:"jsxParseAttribute",value:function(){var t=this.startNode();return this.eat(o.braceL)?(this.expect(o.ellipsis),t.argument=this.parseMaybeAssign(),this.expect(o.braceR),this.finishNode(t,"JSXSpreadAttribute")):(t.name=this.jsxParseNamespacedName(),t.value=this.eat(o.eq)?this.jsxParseAttributeValue():null,this.finishNode(t,"JSXAttribute"))}},{key:"jsxParseOpeningElementAt",value:function(t,e){var s=this.startNodeAt(t,e);return this.match(o.jsxTagEnd)?(this.expect(o.jsxTagEnd),this.finishNode(s,"JSXOpeningFragment")):(s.name=this.jsxParseElementName(),this.jsxParseOpeningElementAfterName(s))}},{key:"jsxParseOpeningElementAfterName",value:function(t){for(var e=[];!this.match(o.slash)&&!this.match(o.jsxTagEnd);)e.push(this.jsxParseAttribute());return t.attributes=e,t.selfClosing=this.eat(o.slash),this.expect(o.jsxTagEnd),this.finishNode(t,"JSXOpeningElement")}},{key:"jsxParseClosingElementAt",value:function(t,e){var s=this.startNodeAt(t,e);return this.match(o.jsxTagEnd)?(this.expect(o.jsxTagEnd),this.finishNode(s,"JSXClosingFragment")):(s.name=this.jsxParseElementName(),this.expect(o.jsxTagEnd),this.finishNode(s,"JSXClosingElement"))}},{key:"jsxParseElementAt",value:function(t,e){var s=this.startNodeAt(t,e),i=[],a=this.jsxParseOpeningElementAt(t,e),r=null;if(!a.selfClosing){t:for(;;)switch(this.state.type){case o.jsxTagStart:if(t=this.state.start,e=this.state.startLoc,this.next(),this.eat(o.slash)){r=this.jsxParseClosingElementAt(t,e);break t}i.push(this.jsxParseElementAt(t,e));break;case o.jsxText:i.push(this.parseExprAtom());break;case o.braceL:var n=this.startNode();this.next(),this.match(o.ellipsis)?i.push(this.jsxParseSpreadChild(n)):i.push(this.jsxParseExpressionContainer(n));break;default:throw this.unexpected()}et(a)&&!et(r)?this.raise(r.start,"Expected corresponding JSX closing tag for <>"):!et(a)&&et(r)?this.raise(r.start,"Expected corresponding JSX closing tag for <"+st(a.name)+">"):et(a)||et(r)||st(r.name)!==st(a.name)&&this.raise(r.start,"Expected corresponding JSX closing tag for <"+st(a.name)+">")}if(et(a)?(s.openingFragment=a,s.closingFragment=r):(s.openingElement=a,s.closingElement=r),s.children=i,this.isRelational("<"))throw this.raise(this.state.start,"Adjacent JSX elements must be wrapped in an enclosing tag. Did you want a JSX fragment <>...</>?");return et(a)?this.finishNode(s,"JSXFragment"):this.finishNode(s,"JSXElement")}},{key:"jsxParseElement",value:function(){var t=this.state.start,e=this.state.startLoc;return this.next(),this.jsxParseElementAt(t,e)}},{key:"parseExprAtom",value:function(t){return this.match(o.jsxText)?this.parseLiteral(this.state.value,"JSXText"):this.match(o.jsxTagStart)?this.jsxParseElement():this.isRelational("<")&&33!==this.input.charCodeAt(this.state.pos)?(this.finishToken(o.jsxTagStart),this.jsxParseElement()):k(D(e.prototype),"parseExprAtom",this).call(this,t)}},{key:"getTokenFromCode",value:function(t){if(this.state.inPropertyName)return k(D(e.prototype),"getTokenFromCode",this).call(this,t);var s=this.curContext();if(s===F.j_expr)return this.jsxReadToken();if(s===F.j_oTag||s===F.j_cTag){if(K(t))return this.jsxReadWord();if(62===t)return++this.state.pos,this.finishToken(o.jsxTagEnd);if((34===t||39===t)&&s===F.j_oTag)return this.jsxReadString(t)}return 60===t&&this.state.exprAllowed&&33!==this.input.charCodeAt(this.state.pos+1)?(++this.state.pos,this.finishToken(o.jsxTagStart)):k(D(e.prototype),"getTokenFromCode",this).call(this,t)}},{key:"updateContext",value:function(t){if(this.match(o.braceL)){var s=this.curContext();s===F.j_oTag?this.state.context.push(F.braceExpression):s===F.j_expr?this.state.context.push(F.templateQuasi):k(D(e.prototype),"updateContext",this).call(this,t),this.state.exprAllowed=!0}else{if(!this.match(o.slash)||t!==o.jsxTagStart)return k(D(e.prototype),"updateContext",this).call(this,t);this.state.context.length-=2,this.state.context.push(F.j_cTag),this.state.exprAllowed=!1}}}]),e}(t)},flow:function(t){return function(t){function e(t,s){var i;return d(this,e),(i=g(this,D(e).call(this,t,s))).flowPragma=void 0,i}return y(e,t),m(e,[{key:"shouldParseTypes",value:function(){return this.getPluginOption("flow","all")||"flow"===this.flowPragma}},{key:"shouldParseEnums",value:function(){return!!this.getPluginOption("flow","enums")}},{key:"finishToken",value:function(t,s){return t!==o.string&&t!==o.semi&&t!==o.interpreterDirective&&void 0===this.flowPragma&&(this.flowPragma=null),k(D(e.prototype),"finishToken",this).call(this,t,s)}},{key:"addComment",value:function(t){if(void 0===this.flowPragma){var s=$.exec(t.value);if(s)if("flow"===s[1])this.flowPragma="flow";else{if("noflow"!==s[1])throw new Error("Unexpected flow pragma");this.flowPragma="noflow"}else;}return k(D(e.prototype),"addComment",this).call(this,t)}},{key:"flowParseTypeInitialiser",value:function(t){var e=this.state.inType;this.state.inType=!0,this.expect(t||o.colon);var s=this.flowParseType();return this.state.inType=e,s}},{key:"flowParsePredicate",value:function(){var t=this.startNode(),e=this.state.startLoc,s=this.state.start;this.expect(o.modulo);var i=this.state.startLoc;return this.expectContextual("checks"),e.line===i.line&&e.column===i.column-1||this.raise(s,"Spaces between ´%´ and ´checks´ are not allowed here."),this.eat(o.parenL)?(t.value=this.parseExpression(),this.expect(o.parenR),this.finishNode(t,"DeclaredPredicate")):this.finishNode(t,"InferredPredicate")}},{key:"flowParseTypeAndPredicateInitialiser",value:function(){var t=this.state.inType;this.state.inType=!0,this.expect(o.colon);var e=null,s=null;return this.match(o.modulo)?(this.state.inType=t,s=this.flowParsePredicate()):(e=this.flowParseType(),this.state.inType=t,this.match(o.modulo)&&(s=this.flowParsePredicate())),[e,s]}},{key:"flowParseDeclareClass",value:function(t){return this.next(),this.flowParseInterfaceish(t,!0),this.finishNode(t,"DeclareClass")}},{key:"flowParseDeclareFunction",value:function(t){this.next();var e=t.id=this.parseIdentifier(),s=this.startNode(),i=this.startNode();this.isRelational("<")?s.typeParameters=this.flowParseTypeParameterDeclaration():s.typeParameters=null,this.expect(o.parenL);var a=this.flowParseFunctionTypeParams();s.params=a.params,s.rest=a.rest,this.expect(o.parenR);var r=P(this.flowParseTypeAndPredicateInitialiser(),2);return s.returnType=r[0],t.predicate=r[1],i.typeAnnotation=this.finishNode(s,"FunctionTypeAnnotation"),e.typeAnnotation=this.finishNode(i,"TypeAnnotation"),this.resetEndLocation(e),this.semicolon(),this.finishNode(t,"DeclareFunction")}},{key:"flowParseDeclare",value:function(t,e){if(this.match(o._class))return this.flowParseDeclareClass(t);if(this.match(o._function))return this.flowParseDeclareFunction(t);if(this.match(o._var))return this.flowParseDeclareVariable(t);if(this.eatContextual("module"))return this.match(o.dot)?this.flowParseDeclareModuleExports(t):(e&&this.raise(this.state.lastTokStart,"`declare module` cannot be used inside another `declare module`"),this.flowParseDeclareModule(t));if(this.isContextual("type"))return this.flowParseDeclareTypeAlias(t);if(this.isContextual("opaque"))return this.flowParseDeclareOpaqueType(t);if(this.isContextual("interface"))return this.flowParseDeclareInterface(t);if(this.match(o._export))return this.flowParseDeclareExportDeclaration(t,e);throw this.unexpected()}},{key:"flowParseDeclareVariable",value:function(t){return this.next(),t.id=this.flowParseTypeAnnotatableIdentifier(!0),this.scope.declareName(t.id.name,5,t.id.start),this.semicolon(),this.finishNode(t,"DeclareVariable")}},{key:"flowParseDeclareModule",value:function(t){var e=this;this.scope.enter(0),this.match(o.string)?t.id=this.parseExprAtom():t.id=this.parseIdentifier();var s=t.body=this.startNode(),i=s.body=[];for(this.expect(o.braceL);!this.match(o.braceR);){var a=this.startNode();this.match(o._import)?(this.next(),this.isContextual("type")||this.match(o._typeof)||this.raise(this.state.lastTokStart,"Imports within a `declare module` body must always be `import type` or `import typeof`"),this.parseImport(a)):(this.expectContextual("declare","Only declares and type imports are allowed inside declare module"),a=this.flowParseDeclare(a,!0)),i.push(a)}this.scope.exit(),this.expect(o.braceR),this.finishNode(s,"BlockStatement");var r=null,n=!1,h="Found both `declare module.exports` and `declare export` in the same module. Modules can only have 1 since they are either an ES module or they are a CommonJS module";return i.forEach((function(t){!function(t){return"DeclareExportAllDeclaration"===t.type||"DeclareExportDeclaration"===t.type&&(!t.declaration||"TypeAlias"!==t.declaration.type&&"InterfaceDeclaration"!==t.declaration.type)}(t)?"DeclareModuleExports"===t.type&&(n&&e.raise(t.start,"Duplicate `declare module.exports` statement"),"ES"===r&&e.raise(t.start,h),r="CommonJS",n=!0):("CommonJS"===r&&e.raise(t.start,h),r="ES")})),t.kind=r||"CommonJS",this.finishNode(t,"DeclareModule")}},{key:"flowParseDeclareExportDeclaration",value:function(t,e){if(this.expect(o._export),this.eat(o._default))return this.match(o._function)||this.match(o._class)?t.declaration=this.flowParseDeclare(this.startNode()):(t.declaration=this.flowParseType(),this.semicolon()),t.default=!0,this.finishNode(t,"DeclareExportDeclaration");if(this.match(o._const)||this.isLet()||(this.isContextual("type")||this.isContextual("interface"))&&!e){var s=this.state.value,i=Q[s];this.unexpected(this.state.start,"`declare export ".concat(s,"` is not supported. Use `").concat(i,"` instead"))}if(this.match(o._var)||this.match(o._function)||this.match(o._class)||this.isContextual("opaque"))return t.declaration=this.flowParseDeclare(this.startNode()),t.default=!1,this.finishNode(t,"DeclareExportDeclaration");if(this.match(o.star)||this.match(o.braceL)||this.isContextual("interface")||this.isContextual("type")||this.isContextual("opaque"))return"ExportNamedDeclaration"===(t=this.parseExport(t)).type&&(t.type="ExportDeclaration",t.default=!1,delete t.exportKind),t.type="Declare"+t.type,t;throw this.unexpected()}},{key:"flowParseDeclareModuleExports",value:function(t){return this.next(),this.expectContextual("exports"),t.typeAnnotation=this.flowParseTypeAnnotation(),this.semicolon(),this.finishNode(t,"DeclareModuleExports")}},{key:"flowParseDeclareTypeAlias",value:function(t){return this.next(),this.flowParseTypeAlias(t),t.type="DeclareTypeAlias",t}},{key:"flowParseDeclareOpaqueType",value:function(t){return this.next(),this.flowParseOpaqueType(t,!0),t.type="DeclareOpaqueType",t}},{key:"flowParseDeclareInterface",value:function(t){return this.next(),this.flowParseInterfaceish(t),this.finishNode(t,"DeclareInterface")}},{key:"flowParseInterfaceish",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t.id=this.flowParseRestrictedIdentifier(!e),this.scope.declareName(t.id.name,e?17:9,t.id.start),this.isRelational("<")?t.typeParameters=this.flowParseTypeParameterDeclaration():t.typeParameters=null,t.extends=[],t.implements=[],t.mixins=[],this.eat(o._extends))do{t.extends.push(this.flowParseInterfaceExtends())}while(!e&&this.eat(o.comma));if(this.isContextual("mixins")){this.next();do{t.mixins.push(this.flowParseInterfaceExtends())}while(this.eat(o.comma))}if(this.isContextual("implements")){this.next();do{t.implements.push(this.flowParseInterfaceExtends())}while(this.eat(o.comma))}t.body=this.flowParseObjectType({allowStatic:e,allowExact:!1,allowSpread:!1,allowProto:e,allowInexact:!1})}},{key:"flowParseInterfaceExtends",value:function(){var t=this.startNode();return t.id=this.flowParseQualifiedTypeIdentifier(),this.isRelational("<")?t.typeParameters=this.flowParseTypeParameterInstantiation():t.typeParameters=null,this.finishNode(t,"InterfaceExtends")}},{key:"flowParseInterface",value:function(t){return this.flowParseInterfaceish(t),this.finishNode(t,"InterfaceDeclaration")}},{key:"checkNotUnderscore",value:function(t){"_"===t&&this.raise(this.state.start,"`_` is only allowed as a type argument to call or new")}},{key:"checkReservedType",value:function(t,e){J.indexOf(t)>-1&&this.raise(e,"Cannot overwrite reserved type ".concat(t))}},{key:"flowParseRestrictedIdentifier",value:function(t){return this.checkReservedType(this.state.value,this.state.start),this.parseIdentifier(t)}},{key:"flowParseTypeAlias",value:function(t){return t.id=this.flowParseRestrictedIdentifier(),this.scope.declareName(t.id.name,9,t.id.start),this.isRelational("<")?t.typeParameters=this.flowParseTypeParameterDeclaration():t.typeParameters=null,t.right=this.flowParseTypeInitialiser(o.eq),this.semicolon(),this.finishNode(t,"TypeAlias")}},{key:"flowParseOpaqueType",value:function(t,e){return this.expectContextual("type"),t.id=this.flowParseRestrictedIdentifier(!0),this.scope.declareName(t.id.name,9,t.id.start),this.isRelational("<")?t.typeParameters=this.flowParseTypeParameterDeclaration():t.typeParameters=null,t.supertype=null,this.match(o.colon)&&(t.supertype=this.flowParseTypeInitialiser(o.colon)),t.impltype=null,e||(t.impltype=this.flowParseTypeInitialiser(o.eq)),this.semicolon(),this.finishNode(t,"OpaqueType")}},{key:"flowParseTypeParameter",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=this.state.start,s=this.startNode(),i=this.flowParseVariance(),a=this.flowParseTypeAnnotatableIdentifier();return s.name=a.name,s.variance=i,s.bound=a.typeAnnotation,this.match(o.eq)?(this.eat(o.eq),s.default=this.flowParseType()):t&&this.raise(e,"Type parameter declaration needs a default, since a preceding type parameter declaration has a default."),this.finishNode(s,"TypeParameter")}},{key:"flowParseTypeParameterDeclaration",value:function(){var t=this.state.inType,e=this.startNode();e.params=[],this.state.inType=!0,this.isRelational("<")||this.match(o.jsxTagStart)?this.next():this.unexpected();var s=!1;do{var i=this.flowParseTypeParameter(s);e.params.push(i),i.default&&(s=!0),this.isRelational(">")||this.expect(o.comma)}while(!this.isRelational(">"));return this.expectRelational(">"),this.state.inType=t,this.finishNode(e,"TypeParameterDeclaration")}},{key:"flowParseTypeParameterInstantiation",value:function(){var t=this.startNode(),e=this.state.inType;t.params=[],this.state.inType=!0,this.expectRelational("<");var s=this.state.noAnonFunctionType;for(this.state.noAnonFunctionType=!1;!this.isRelational(">");)t.params.push(this.flowParseType()),this.isRelational(">")||this.expect(o.comma);return this.state.noAnonFunctionType=s,this.expectRelational(">"),this.state.inType=e,this.finishNode(t,"TypeParameterInstantiation")}},{key:"flowParseTypeParameterInstantiationCallOrNew",value:function(){var t=this.startNode(),e=this.state.inType;for(t.params=[],this.state.inType=!0,this.expectRelational("<");!this.isRelational(">");)t.params.push(this.flowParseTypeOrImplicitInstantiation()),this.isRelational(">")||this.expect(o.comma);return this.expectRelational(">"),this.state.inType=e,this.finishNode(t,"TypeParameterInstantiation")}},{key:"flowParseInterfaceType",value:function(){var t=this.startNode();if(this.expectContextual("interface"),t.extends=[],this.eat(o._extends))do{t.extends.push(this.flowParseInterfaceExtends())}while(this.eat(o.comma));return t.body=this.flowParseObjectType({allowStatic:!1,allowExact:!1,allowSpread:!1,allowProto:!1,allowInexact:!1}),this.finishNode(t,"InterfaceTypeAnnotation")}},{key:"flowParseObjectPropertyKey",value:function(){return this.match(o.num)||this.match(o.string)?this.parseExprAtom():this.parseIdentifier(!0)}},{key:"flowParseObjectTypeIndexer",value:function(t,e,s){return t.static=e,this.lookahead().type===o.colon?(t.id=this.flowParseObjectPropertyKey(),t.key=this.flowParseTypeInitialiser()):(t.id=null,t.key=this.flowParseType()),this.expect(o.bracketR),t.value=this.flowParseTypeInitialiser(),t.variance=s,this.finishNode(t,"ObjectTypeIndexer")}},{key:"flowParseObjectTypeInternalSlot",value:function(t,e){return t.static=e,t.id=this.flowParseObjectPropertyKey(),this.expect(o.bracketR),this.expect(o.bracketR),this.isRelational("<")||this.match(o.parenL)?(t.method=!0,t.optional=!1,t.value=this.flowParseObjectTypeMethodish(this.startNodeAt(t.start,t.loc.start))):(t.method=!1,this.eat(o.question)&&(t.optional=!0),t.value=this.flowParseTypeInitialiser()),this.finishNode(t,"ObjectTypeInternalSlot")}},{key:"flowParseObjectTypeMethodish",value:function(t){for(t.params=[],t.rest=null,t.typeParameters=null,this.isRelational("<")&&(t.typeParameters=this.flowParseTypeParameterDeclaration()),this.expect(o.parenL);!this.match(o.parenR)&&!this.match(o.ellipsis);)t.params.push(this.flowParseFunctionTypeParam()),this.match(o.parenR)||this.expect(o.comma);return this.eat(o.ellipsis)&&(t.rest=this.flowParseFunctionTypeParam()),this.expect(o.parenR),t.returnType=this.flowParseTypeInitialiser(),this.finishNode(t,"FunctionTypeAnnotation")}},{key:"flowParseObjectTypeCallProperty",value:function(t,e){var s=this.startNode();return t.static=e,t.value=this.flowParseObjectTypeMethodish(s),this.finishNode(t,"ObjectTypeCallProperty")}},{key:"flowParseObjectType",value:function(t){var e=t.allowStatic,s=t.allowExact,i=t.allowSpread,a=t.allowProto,r=t.allowInexact,n=this.state.inType;this.state.inType=!0;var h,u,l=this.startNode();l.callProperties=[],l.properties=[],l.indexers=[],l.internalSlots=[];var c=!1;for(s&&this.match(o.braceBarL)?(this.expect(o.braceBarL),h=o.braceBarR,u=!0):(this.expect(o.braceL),h=o.braceR,u=!1),l.exact=u;!this.match(h);){var p=!1,d=null,f=null,m=this.startNode();if(a&&this.isContextual("proto")){var y=this.lookahead();y.type!==o.colon&&y.type!==o.question&&(this.next(),d=this.state.start,e=!1)}if(e&&this.isContextual("static")){var D=this.lookahead();D.type!==o.colon&&D.type!==o.question&&(this.next(),p=!0)}var v=this.flowParseVariance();if(this.eat(o.bracketL))null!=d&&this.unexpected(d),this.eat(o.bracketL)?(v&&this.unexpected(v.start),l.internalSlots.push(this.flowParseObjectTypeInternalSlot(m,p))):l.indexers.push(this.flowParseObjectTypeIndexer(m,p,v));else if(this.match(o.parenL)||this.isRelational("<"))null!=d&&this.unexpected(d),v&&this.unexpected(v.start),l.callProperties.push(this.flowParseObjectTypeCallProperty(m,p));else{var x,g="init";if(this.isContextual("get")||this.isContextual("set")){var k=this.lookahead();k.type!==o.name&&k.type!==o.string&&k.type!==o.num||(g=this.state.value,this.next())}var P=this.flowParseObjectTypeProperty(m,p,d,v,g,i,null!==(x=r)&&void 0!==x?x:!u);null===P?(c=!0,f=this.state.lastTokStart):l.properties.push(P)}this.flowObjectTypeSemicolon(),!f||this.match(o.braceR)||this.match(o.braceBarR)||this.raise(f,"Explicit inexact syntax must appear at the end of an inexact object")}this.expect(h),i&&(l.inexact=c);var b=this.finishNode(l,"ObjectTypeAnnotation");return this.state.inType=n,b}},{key:"flowParseObjectTypeProperty",value:function(t,e,s,i,a,r,n){if(this.eat(o.ellipsis))return this.match(o.comma)||this.match(o.semi)||this.match(o.braceR)||this.match(o.braceBarR)?(r?n||this.raise(this.state.lastTokStart,"Explicit inexact syntax cannot appear inside an explicit exact object type"):this.raise(this.state.lastTokStart,"Explicit inexact syntax cannot appear in class or interface definitions"),i&&this.raise(i.start,"Explicit inexact syntax cannot have variance"),null):(r||this.raise(this.state.lastTokStart,"Spread operator cannot appear in class or interface definitions"),null!=s&&this.unexpected(s),i&&this.raise(i.start,"Spread properties cannot have variance"),t.argument=this.flowParseType(),this.finishNode(t,"ObjectTypeSpreadProperty"));t.key=this.flowParseObjectPropertyKey(),t.static=e,t.proto=null!=s,t.kind=a;var h=!1;return this.isRelational("<")||this.match(o.parenL)?(t.method=!0,null!=s&&this.unexpected(s),i&&this.unexpected(i.start),t.value=this.flowParseObjectTypeMethodish(this.startNodeAt(t.start,t.loc.start)),"get"!==a&&"set"!==a||this.flowCheckGetterSetterParams(t)):("init"!==a&&this.unexpected(),t.method=!1,this.eat(o.question)&&(h=!0),t.value=this.flowParseTypeInitialiser(),t.variance=i),t.optional=h,this.finishNode(t,"ObjectTypeProperty")}},{key:"flowCheckGetterSetterParams",value:function(t){var e="get"===t.kind?0:1,s=t.start;t.value.params.length+(t.value.rest?1:0)!==e&&("get"===t.kind?this.raise(s,"getter must not have any formal parameters"):this.raise(s,"setter must have exactly one formal parameter")),"set"===t.kind&&t.value.rest&&this.raise(s,"setter function argument must not be a rest parameter")}},{key:"flowObjectTypeSemicolon",value:function(){this.eat(o.semi)||this.eat(o.comma)||this.match(o.braceR)||this.match(o.braceBarR)||this.unexpected()}},{key:"flowParseQualifiedTypeIdentifier",value:function(t,e,s){t=t||this.state.start,e=e||this.state.startLoc;for(var i=s||this.parseIdentifier();this.eat(o.dot);){var a=this.startNodeAt(t,e);a.qualification=i,a.id=this.parseIdentifier(),i=this.finishNode(a,"QualifiedTypeIdentifier")}return i}},{key:"flowParseGenericType",value:function(t,e,s){var i=this.startNodeAt(t,e);return i.typeParameters=null,i.id=this.flowParseQualifiedTypeIdentifier(t,e,s),this.isRelational("<")&&(i.typeParameters=this.flowParseTypeParameterInstantiation()),this.finishNode(i,"GenericTypeAnnotation")}},{key:"flowParseTypeofType",value:function(){var t=this.startNode();return this.expect(o._typeof),t.argument=this.flowParsePrimaryType(),this.finishNode(t,"TypeofTypeAnnotation")}},{key:"flowParseTupleType",value:function(){var t=this.startNode();for(t.types=[],this.expect(o.bracketL);this.state.pos<this.length&&!this.match(o.bracketR)&&(t.types.push(this.flowParseType()),!this.match(o.bracketR));)this.expect(o.comma);return this.expect(o.bracketR),this.finishNode(t,"TupleTypeAnnotation")}},{key:"flowParseFunctionTypeParam",value:function(){var t=null,e=!1,s=null,i=this.startNode(),a=this.lookahead();return a.type===o.colon||a.type===o.question?(t=this.parseIdentifier(),this.eat(o.question)&&(e=!0),s=this.flowParseTypeInitialiser()):s=this.flowParseType(),i.name=t,i.optional=e,i.typeAnnotation=s,this.finishNode(i,"FunctionTypeParam")}},{key:"reinterpretTypeAsFunctionTypeParam",value:function(t){var e=this.startNodeAt(t.start,t.loc.start);return e.name=null,e.optional=!1,e.typeAnnotation=t,this.finishNode(e,"FunctionTypeParam")}},{key:"flowParseFunctionTypeParams",value:function(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=null;!this.match(o.parenR)&&!this.match(o.ellipsis);)t.push(this.flowParseFunctionTypeParam()),this.match(o.parenR)||this.expect(o.comma);return this.eat(o.ellipsis)&&(e=this.flowParseFunctionTypeParam()),{params:t,rest:e}}},{key:"flowIdentToTypeAnnotation",value:function(t,e,s,i){switch(i.name){case"any":return this.finishNode(s,"AnyTypeAnnotation");case"bool":case"boolean":return this.finishNode(s,"BooleanTypeAnnotation");case"mixed":return this.finishNode(s,"MixedTypeAnnotation");case"empty":return this.finishNode(s,"EmptyTypeAnnotation");case"number":return this.finishNode(s,"NumberTypeAnnotation");case"string":return this.finishNode(s,"StringTypeAnnotation");default:return this.checkNotUnderscore(i.name),this.flowParseGenericType(t,e,i)}}},{key:"flowParsePrimaryType",value:function(){var t,s,i=this.state.start,a=this.state.startLoc,r=this.startNode(),n=!1,h=this.state.noAnonFunctionType;switch(this.state.type){case o.name:return this.isContextual("interface")?this.flowParseInterfaceType():this.flowIdentToTypeAnnotation(i,a,r,this.parseIdentifier());case o.braceL:return this.flowParseObjectType({allowStatic:!1,allowExact:!1,allowSpread:!0,allowProto:!1,allowInexact:!0});case o.braceBarL:return this.flowParseObjectType({allowStatic:!1,allowExact:!0,allowSpread:!0,allowProto:!1,allowInexact:!1});case o.bracketL:return this.state.noAnonFunctionType=!1,s=this.flowParseTupleType(),this.state.noAnonFunctionType=h,s;case o.relational:if("<"===this.state.value)return r.typeParameters=this.flowParseTypeParameterDeclaration(),this.expect(o.parenL),t=this.flowParseFunctionTypeParams(),r.params=t.params,r.rest=t.rest,this.expect(o.parenR),this.expect(o.arrow),r.returnType=this.flowParseType(),this.finishNode(r,"FunctionTypeAnnotation");break;case o.parenL:if(this.next(),!this.match(o.parenR)&&!this.match(o.ellipsis))if(this.match(o.name)){var u=this.lookahead().type;n=u!==o.question&&u!==o.colon}else n=!0;if(n){if(this.state.noAnonFunctionType=!1,s=this.flowParseType(),this.state.noAnonFunctionType=h,this.state.noAnonFunctionType||!(this.match(o.comma)||this.match(o.parenR)&&this.lookahead().type===o.arrow))return this.expect(o.parenR),s;this.eat(o.comma)}return t=s?this.flowParseFunctionTypeParams([this.reinterpretTypeAsFunctionTypeParam(s)]):this.flowParseFunctionTypeParams(),r.params=t.params,r.rest=t.rest,this.expect(o.parenR),this.expect(o.arrow),r.returnType=this.flowParseType(),r.typeParameters=null,this.finishNode(r,"FunctionTypeAnnotation");case o.string:return this.parseLiteral(this.state.value,"StringLiteralTypeAnnotation");case o._true:case o._false:return r.value=this.match(o._true),this.next(),this.finishNode(r,"BooleanLiteralTypeAnnotation");case o.plusMin:if("-"===this.state.value){if(this.next(),this.match(o.num))return this.parseLiteral(-this.state.value,"NumberLiteralTypeAnnotation",r.start,r.loc.start);if(this.match(o.bigint))return this.parseLiteral(-this.state.value,"BigIntLiteralTypeAnnotation",r.start,r.loc.start);throw this.raise(this.state.start,'Unexpected token, expected "number" or "bigint"')}this.unexpected();case o.num:return this.parseLiteral(this.state.value,"NumberLiteralTypeAnnotation");case o.bigint:return this.parseLiteral(this.state.value,"BigIntLiteralTypeAnnotation");case o._void:return this.next(),this.finishNode(r,"VoidTypeAnnotation");case o._null:return this.next(),this.finishNode(r,"NullLiteralTypeAnnotation");case o._this:return this.next(),this.finishNode(r,"ThisTypeAnnotation");case o.star:return this.next(),this.finishNode(r,"ExistsTypeAnnotation");default:if("typeof"===this.state.type.keyword)return this.flowParseTypeofType();if(this.state.type.keyword){var l=this.state.type.label;return this.next(),k(D(e.prototype),"createIdentifier",this).call(this,r,l)}}throw this.unexpected()}},{key:"flowParsePostfixType",value:function(){for(var t=this.state.start,e=this.state.startLoc,s=this.flowParsePrimaryType();this.match(o.bracketL)&&!this.canInsertSemicolon();){var i=this.startNodeAt(t,e);i.elementType=s,this.expect(o.bracketL),this.expect(o.bracketR),s=this.finishNode(i,"ArrayTypeAnnotation")}return s}},{key:"flowParsePrefixType",value:function(){var t=this.startNode();return this.eat(o.question)?(t.typeAnnotation=this.flowParsePrefixType(),this.finishNode(t,"NullableTypeAnnotation")):this.flowParsePostfixType()}},{key:"flowParseAnonFunctionWithoutParens",value:function(){var t=this.flowParsePrefixType();if(!this.state.noAnonFunctionType&&this.eat(o.arrow)){var e=this.startNodeAt(t.start,t.loc.start);return e.params=[this.reinterpretTypeAsFunctionTypeParam(t)],e.rest=null,e.returnType=this.flowParseType(),e.typeParameters=null,this.finishNode(e,"FunctionTypeAnnotation")}return t}},{key:"flowParseIntersectionType",value:function(){var t=this.startNode();this.eat(o.bitwiseAND);var e=this.flowParseAnonFunctionWithoutParens();for(t.types=[e];this.eat(o.bitwiseAND);)t.types.push(this.flowParseAnonFunctionWithoutParens());return 1===t.types.length?e:this.finishNode(t,"IntersectionTypeAnnotation")}},{key:"flowParseUnionType",value:function(){var t=this.startNode();this.eat(o.bitwiseOR);var e=this.flowParseIntersectionType();for(t.types=[e];this.eat(o.bitwiseOR);)t.types.push(this.flowParseIntersectionType());return 1===t.types.length?e:this.finishNode(t,"UnionTypeAnnotation")}},{key:"flowParseType",value:function(){var t=this.state.inType;this.state.inType=!0;var e=this.flowParseUnionType();return this.state.inType=t,this.state.exprAllowed=this.state.exprAllowed||this.state.noAnonFunctionType,e}},{key:"flowParseTypeOrImplicitInstantiation",value:function(){if(this.state.type===o.name&&"_"===this.state.value){var t=this.state.start,e=this.state.startLoc,s=this.parseIdentifier();return this.flowParseGenericType(t,e,s)}return this.flowParseType()}},{key:"flowParseTypeAnnotation",value:function(){var t=this.startNode();return t.typeAnnotation=this.flowParseTypeInitialiser(),this.finishNode(t,"TypeAnnotation")}},{key:"flowParseTypeAnnotatableIdentifier",value:function(t){var e=t?this.parseIdentifier():this.flowParseRestrictedIdentifier();return this.match(o.colon)&&(e.typeAnnotation=this.flowParseTypeAnnotation(),this.resetEndLocation(e)),e}},{key:"typeCastToParameter",value:function(t){return t.expression.typeAnnotation=t.typeAnnotation,this.resetEndLocation(t.expression,t.typeAnnotation.end,t.typeAnnotation.loc.end),t.expression}},{key:"flowParseVariance",value:function(){var t=null;return this.match(o.plusMin)&&(t=this.startNode(),"+"===this.state.value?t.kind="plus":t.kind="minus",this.next(),this.finishNode(t,"Variance")),t}},{key:"parseFunctionBody",value:function(t,s){var i=this,a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return s?this.forwardNoArrowParamsConversionAt(t,(function(){return k(D(e.prototype),"parseFunctionBody",i).call(i,t,!0,a)})):k(D(e.prototype),"parseFunctionBody",this).call(this,t,!1,a)}},{key:"parseFunctionBodyAndFinish",value:function(t,s){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.match(o.colon)){var a=this.startNode(),r=this.flowParseTypeAndPredicateInitialiser(),n=P(r,2);a.typeAnnotation=n[0],t.predicate=n[1],t.returnType=a.typeAnnotation?this.finishNode(a,"TypeAnnotation"):null}k(D(e.prototype),"parseFunctionBodyAndFinish",this).call(this,t,s,i)}},{key:"parseStatement",value:function(t,s){if(this.state.strict&&this.match(o.name)&&"interface"===this.state.value){var i=this.startNode();return this.next(),this.flowParseInterface(i)}if(this.shouldParseEnums()&&this.isContextual("enum")){var a=this.startNode();return this.next(),this.flowParseEnumDeclaration(a)}var r=k(D(e.prototype),"parseStatement",this).call(this,t,s);return void 0!==this.flowPragma||this.isValidDirective(r)||(this.flowPragma=null),r}},{key:"parseExpressionStatement",value:function(t,s){if("Identifier"===s.type)if("declare"===s.name){if(this.match(o._class)||this.match(o.name)||this.match(o._function)||this.match(o._var)||this.match(o._export))return this.flowParseDeclare(t)}else if(this.match(o.name)){if("interface"===s.name)return this.flowParseInterface(t);if("type"===s.name)return this.flowParseTypeAlias(t);if("opaque"===s.name)return this.flowParseOpaqueType(t,!1)}return k(D(e.prototype),"parseExpressionStatement",this).call(this,t,s)}},{key:"shouldParseExportDeclaration",value:function(){return this.isContextual("type")||this.isContextual("interface")||this.isContextual("opaque")||this.shouldParseEnums()&&this.isContextual("enum")||k(D(e.prototype),"shouldParseExportDeclaration",this).call(this)}},{key:"isExportDefaultSpecifier",value:function(){return(!this.match(o.name)||!("type"===this.state.value||"interface"===this.state.value||"opaque"===this.state.value||this.shouldParseEnums()&&"enum"===this.state.value))&&k(D(e.prototype),"isExportDefaultSpecifier",this).call(this)}},{key:"parseExportDefaultExpression",value:function(){if(this.shouldParseEnums()&&this.isContextual("enum")){var t=this.startNode();return this.next(),this.flowParseEnumDeclaration(t)}return k(D(e.prototype),"parseExportDefaultExpression",this).call(this)}},{key:"parseConditional",value:function(t,s,i,a,r){var n=this;if(!this.match(o.question))return t;if(r){var h=this.tryParse((function(){return k(D(e.prototype),"parseConditional",n).call(n,t,s,i,a)}));return h.node?(h.error&&(this.state=h.failState),h.node):(r.start=h.error.pos||this.state.start,t)}this.expect(o.question);var u=this.state.clone(),l=this.state.noArrowAt,c=this.startNodeAt(i,a),p=this.tryParseConditionalConsequent(),d=p.consequent,f=p.failed,m=P(this.getArrowLikeExpressions(d),2),y=m[0],v=m[1];if(f||v.length>0){var x=b(l);if(v.length>0){this.state=u,this.state.noArrowAt=x;for(var g=0;g<v.length;g++)x.push(v[g].start);var E=this.tryParseConditionalConsequent();d=E.consequent,f=E.failed;var C=P(this.getArrowLikeExpressions(d),2);y=C[0],v=C[1]}if(f&&y.length>1&&this.raise(u.start,"Ambiguous expression: wrap the arrow functions in parentheses to disambiguate."),f&&1===y.length){this.state=u,this.state.noArrowAt=x.concat(y[0].start);var A=this.tryParseConditionalConsequent();d=A.consequent,f=A.failed}}return this.getArrowLikeExpressions(d,!0),this.state.noArrowAt=l,this.expect(o.colon),c.test=t,c.consequent=d,c.alternate=this.forwardNoArrowParamsConversionAt(c,(function(){return n.parseMaybeAssign(s,void 0,void 0,void 0)})),this.finishNode(c,"ConditionalExpression")}},{key:"tryParseConditionalConsequent",value:function(){this.state.noArrowParamsConversionAt.push(this.state.start);var t=this.parseMaybeAssign(),e=!this.match(o.colon);return this.state.noArrowParamsConversionAt.pop(),{consequent:t,failed:e}}},{key:"getArrowLikeExpressions",value:function(t,e){for(var s=this,i=[t],a=[];0!==i.length;){var r=i.pop();"ArrowFunctionExpression"===r.type?(r.typeParameters||!r.returnType?this.finishArrowValidation(r):a.push(r),i.push(r.body)):"ConditionalExpression"===r.type&&(i.push(r.consequent),i.push(r.alternate))}return e?(a.forEach((function(t){return s.finishArrowValidation(t)})),[a,[]]):function(t,e){for(var s=[],i=[],a=0;a<t.length;a++)(e(t[a],a,t)?s:i).push(t[a]);return[s,i]}(a,(function(t){return t.params.every((function(t){return s.isAssignable(t,!0)}))}))}},{key:"finishArrowValidation",value:function(t){var s;this.toAssignableList(t.params,!0,"arrow function parameters",null===(s=t.extra)||void 0===s?void 0:s.trailingComma),this.scope.enter(16|p(!1,!1)),k(D(e.prototype),"checkParams",this).call(this,t,!1,!0),this.scope.exit()}},{key:"forwardNoArrowParamsConversionAt",value:function(t,e){var s;return-1!==this.state.noArrowParamsConversionAt.indexOf(t.start)?(this.state.noArrowParamsConversionAt.push(this.state.start),s=e(),this.state.noArrowParamsConversionAt.pop()):s=e(),s}},{key:"parseParenItem",value:function(t,s,i){if(t=k(D(e.prototype),"parseParenItem",this).call(this,t,s,i),this.eat(o.question)&&(t.optional=!0,this.resetEndLocation(t)),this.match(o.colon)){var a=this.startNodeAt(s,i);return a.expression=t,a.typeAnnotation=this.flowParseTypeAnnotation(),this.finishNode(a,"TypeCastExpression")}return t}},{key:"assertModuleNodeAllowed",value:function(t){"ImportDeclaration"===t.type&&("type"===t.importKind||"typeof"===t.importKind)||"ExportNamedDeclaration"===t.type&&"type"===t.exportKind||"ExportAllDeclaration"===t.type&&"type"===t.exportKind||k(D(e.prototype),"assertModuleNodeAllowed",this).call(this,t)}},{key:"parseExport",value:function(t){var s=k(D(e.prototype),"parseExport",this).call(this,t);return"ExportNamedDeclaration"!==s.type&&"ExportAllDeclaration"!==s.type||(s.exportKind=s.exportKind||"value"),s}},{key:"parseExportDeclaration",value:function(t){if(this.isContextual("type")){t.exportKind="type";var s=this.startNode();return this.next(),this.match(o.braceL)?(t.specifiers=this.parseExportSpecifiers(),this.parseExportFrom(t),null):this.flowParseTypeAlias(s)}if(this.isContextual("opaque")){t.exportKind="type";var i=this.startNode();return this.next(),this.flowParseOpaqueType(i,!1)}if(this.isContextual("interface")){t.exportKind="type";var a=this.startNode();return this.next(),this.flowParseInterface(a)}if(this.shouldParseEnums()&&this.isContextual("enum")){t.exportKind="value";var r=this.startNode();return this.next(),this.flowParseEnumDeclaration(r)}return k(D(e.prototype),"parseExportDeclaration",this).call(this,t)}},{key:"eatExportStar",value:function(t){return!!k(D(e.prototype),"eatExportStar",this).apply(this,arguments)||!(!this.isContextual("type")||this.lookahead().type!==o.star)&&(t.exportKind="type",this.next(),this.next(),!0)}},{key:"maybeParseExportNamespaceSpecifier",value:function(t){var s=this.state.start,i=k(D(e.prototype),"maybeParseExportNamespaceSpecifier",this).call(this,t);return i&&"type"===t.exportKind&&this.unexpected(s),i}},{key:"parseClassId",value:function(t,s,i){k(D(e.prototype),"parseClassId",this).call(this,t,s,i),this.isRelational("<")&&(t.typeParameters=this.flowParseTypeParameterDeclaration())}},{key:"getTokenFromCode",value:function(t){var s=this.input.charCodeAt(this.state.pos+1);return 123===t&&124===s?this.finishOp(o.braceBarL,2):!this.state.inType||62!==t&&60!==t?function(t,e){return 64===t&&64===e}(t,s)?(this.state.isIterator=!0,k(D(e.prototype),"readWord",this).call(this)):k(D(e.prototype),"getTokenFromCode",this).call(this,t):this.finishOp(o.relational,1)}},{key:"isAssignable",value:function(t,e){var s=this;switch(t.type){case"Identifier":case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":return!0;case"ObjectExpression":var i=t.properties.length-1;return t.properties.every((function(t,e){return"ObjectMethod"!==t.type&&(e===i||"SpreadElement"===t.type)&&s.isAssignable(t)}));case"ObjectProperty":return this.isAssignable(t.value);case"SpreadElement":return this.isAssignable(t.argument);case"ArrayExpression":return t.elements.every((function(t){return s.isAssignable(t)}));case"AssignmentExpression":return"="===t.operator;case"ParenthesizedExpression":case"TypeCastExpression":return this.isAssignable(t.expression);case"MemberExpression":case"OptionalMemberExpression":return!e;default:return!1}}},{key:"toAssignable",value:function(t,s,i){return"TypeCastExpression"===t.type?k(D(e.prototype),"toAssignable",this).call(this,this.typeCastToParameter(t),s,i):k(D(e.prototype),"toAssignable",this).call(this,t,s,i)}},{key:"toAssignableList",value:function(t,s,i,a){for(var r=0;r<t.length;r++){var n=t[r];n&&"TypeCastExpression"===n.type&&(t[r]=this.typeCastToParameter(n))}return k(D(e.prototype),"toAssignableList",this).call(this,t,s,i,a)}},{key:"toReferencedList",value:function(t,e){for(var s=0;s<t.length;s++){var i=t[s];!i||"TypeCastExpression"!==i.type||i.extra&&i.extra.parenthesized||!(t.length>1)&&e||this.raise(i.typeAnnotation.start,"The type cast expression is expected to be wrapped with parenthesis")}return t}},{key:"checkLVal",value:function(t){var s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:64,i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0;if("TypeCastExpression"!==t.type)return k(D(e.prototype),"checkLVal",this).call(this,t,s,i,a)}},{key:"parseClassProperty",value:function(t){return this.match(o.colon)&&(t.typeAnnotation=this.flowParseTypeAnnotation()),k(D(e.prototype),"parseClassProperty",this).call(this,t)}},{key:"parseClassPrivateProperty",value:function(t){return this.match(o.colon)&&(t.typeAnnotation=this.flowParseTypeAnnotation()),k(D(e.prototype),"parseClassPrivateProperty",this).call(this,t)}},{key:"isClassMethod",value:function(){return this.isRelational("<")||k(D(e.prototype),"isClassMethod",this).call(this)}},{key:"isClassProperty",value:function(){return this.match(o.colon)||k(D(e.prototype),"isClassProperty",this).call(this)}},{key:"isNonstaticConstructor",value:function(t){return!this.match(o.colon)&&k(D(e.prototype),"isNonstaticConstructor",this).call(this,t)}},{key:"pushClassMethod",value:function(t,s,i,a,r,n){s.variance&&this.unexpected(s.variance.start),delete s.variance,this.isRelational("<")&&(s.typeParameters=this.flowParseTypeParameterDeclaration()),k(D(e.prototype),"pushClassMethod",this).call(this,t,s,i,a,r,n)}},{key:"pushClassPrivateMethod",value:function(t,s,i,a){s.variance&&this.unexpected(s.variance.start),delete s.variance,this.isRelational("<")&&(s.typeParameters=this.flowParseTypeParameterDeclaration()),k(D(e.prototype),"pushClassPrivateMethod",this).call(this,t,s,i,a)}},{key:"parseClassSuper",value:function(t){if(k(D(e.prototype),"parseClassSuper",this).call(this,t),t.superClass&&this.isRelational("<")&&(t.superTypeParameters=this.flowParseTypeParameterInstantiation()),this.isContextual("implements")){this.next();var s=t.implements=[];do{var i=this.startNode();i.id=this.flowParseRestrictedIdentifier(!0),this.isRelational("<")?i.typeParameters=this.flowParseTypeParameterInstantiation():i.typeParameters=null,s.push(this.finishNode(i,"ClassImplements"))}while(this.eat(o.comma))}}},{key:"parsePropertyName",value:function(t){var s=this.flowParseVariance(),i=k(D(e.prototype),"parsePropertyName",this).call(this,t);return t.variance=s,i}},{key:"parseObjPropValue",value:function(t,s,i,a,r,n,h,u){var l;t.variance&&this.unexpected(t.variance.start),delete t.variance,this.isRelational("<")&&(l=this.flowParseTypeParameterDeclaration(),this.match(o.parenL)||this.unexpected()),k(D(e.prototype),"parseObjPropValue",this).call(this,t,s,i,a,r,n,h,u),l&&((t.value||t).typeParameters=l)}},{key:"parseAssignableListItemTypes",value:function(t){return this.eat(o.question)&&("Identifier"!==t.type&&this.raise(t.start,"A binding pattern parameter cannot be optional in an implementation signature."),t.optional=!0),this.match(o.colon)&&(t.typeAnnotation=this.flowParseTypeAnnotation()),this.resetEndLocation(t),t}},{key:"parseMaybeDefault",value:function(t,s,i){var a=k(D(e.prototype),"parseMaybeDefault",this).call(this,t,s,i);return"AssignmentPattern"===a.type&&a.typeAnnotation&&a.right.start<a.typeAnnotation.start&&this.raise(a.typeAnnotation.start,"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`"),a}},{key:"shouldParseDefaultImport",value:function(t){return H(t)?G(this.state):k(D(e.prototype),"shouldParseDefaultImport",this).call(this,t)}},{key:"parseImportSpecifierLocal",value:function(t,e,s,i){e.local=H(t)?this.flowParseRestrictedIdentifier(!0):this.parseIdentifier(),this.checkLVal(e.local,9,void 0,i),t.specifiers.push(this.finishNode(e,s))}},{key:"maybeParseDefaultImportSpecifier",value:function(t){t.importKind="value";var s=null;if(this.match(o._typeof)?s="typeof":this.isContextual("type")&&(s="type"),s){var i=this.lookahead();"type"===s&&i.type===o.star&&this.unexpected(i.start),(G(i)||i.type===o.braceL||i.type===o.star)&&(this.next(),t.importKind=s)}return k(D(e.prototype),"maybeParseDefaultImportSpecifier",this).call(this,t)}},{key:"parseImportSpecifier",value:function(t){var e=this.startNode(),s=this.state.start,i=this.parseIdentifier(!0),a=null;"type"===i.name?a="type":"typeof"===i.name&&(a="typeof");var r=!1;if(this.isContextual("as")&&!this.isLookaheadContextual("as")){var n=this.parseIdentifier(!0);null===a||this.match(o.name)||this.state.type.keyword?(e.imported=i,e.importKind=null,e.local=this.parseIdentifier()):(e.imported=n,e.importKind=a,e.local=n.__clone())}else null!==a&&(this.match(o.name)||this.state.type.keyword)?(e.imported=this.parseIdentifier(!0),e.importKind=a,this.eatContextual("as")?e.local=this.parseIdentifier():(r=!0,e.local=e.imported.__clone())):(r=!0,e.imported=i,e.importKind=null,e.local=e.imported.__clone());var h=H(t),u=H(e);h&&u&&this.raise(s,"The `type` and `typeof` keywords on named imports can only be used on regular `import` statements. It cannot be used with `import type` or `import typeof` statements"),(h||u)&&this.checkReservedType(e.local.name,e.local.start),!r||h||u||this.checkReservedWord(e.local.name,e.start,!0,!0),this.checkLVal(e.local,9,void 0,"import specifier"),t.specifiers.push(this.finishNode(e,"ImportSpecifier"))}},{key:"parseFunctionParams",value:function(t,s){var i=t.kind;"get"!==i&&"set"!==i&&this.isRelational("<")&&(t.typeParameters=this.flowParseTypeParameterDeclaration()),k(D(e.prototype),"parseFunctionParams",this).call(this,t,s)}},{key:"parseVarId",value:function(t,s){k(D(e.prototype),"parseVarId",this).call(this,t,s),this.match(o.colon)&&(t.id.typeAnnotation=this.flowParseTypeAnnotation(),this.resetEndLocation(t.id))}},{key:"parseAsyncArrowFromCallExpression",value:function(t,s){if(this.match(o.colon)){var i=this.state.noAnonFunctionType;this.state.noAnonFunctionType=!0,t.returnType=this.flowParseTypeAnnotation(),this.state.noAnonFunctionType=i}return k(D(e.prototype),"parseAsyncArrowFromCallExpression",this).call(this,t,s)}},{key:"shouldParseAsyncArrow",value:function(){return this.match(o.colon)||k(D(e.prototype),"shouldParseAsyncArrow",this).call(this)}},{key:"parseMaybeAssign",value:function(t,s,i,a){var r,n=this,h=null;if(this.hasPlugin("jsx")&&(this.match(o.jsxTagStart)||this.isRelational("<"))){if(h=this.state.clone(),!(r=this.tryParse((function(){return k(D(e.prototype),"parseMaybeAssign",n).call(n,t,s,i,a)}),h)).error)return r.node;var u=this.state.context;u[u.length-1]===F.j_oTag?u.length-=2:u[u.length-1]===F.j_expr&&(u.length-=1)}if(r&&r.error||this.isRelational("<")){var l;h=h||this.state.clone();var c=this.tryParse((function(){l=n.flowParseTypeParameterDeclaration();var r=n.forwardNoArrowParamsConversionAt(l,(function(){return k(D(e.prototype),"parseMaybeAssign",n).call(n,t,s,i,a)}));return r.typeParameters=l,n.resetStartLocationFromNode(r,l),r}),h),p=c.node&&"ArrowFunctionExpression"===c.node.type?c.node:null;if(!c.error&&p)return p;if(r&&r.node)return this.state=r.failState,r.node;if(p)return this.state=c.failState,p;if(r&&r.thrown)throw r.error;if(c.thrown)throw c.error;throw this.raise(l.start,"Expected an arrow function after this type parameter declaration")}return k(D(e.prototype),"parseMaybeAssign",this).call(this,t,s,i,a)}},{key:"parseArrow",value:function(t){var s=this;if(this.match(o.colon)){var i=this.tryParse((function(){var e=s.state.noAnonFunctionType;s.state.noAnonFunctionType=!0;var i=s.startNode(),a=P(s.flowParseTypeAndPredicateInitialiser(),2);return i.typeAnnotation=a[0],t.predicate=a[1],s.state.noAnonFunctionType=e,s.canInsertSemicolon()&&s.unexpected(),s.match(o.arrow)||s.unexpected(),i}));if(i.thrown)return null;i.error&&(this.state=i.failState),t.returnType=i.node.typeAnnotation?this.finishNode(i.node,"TypeAnnotation"):null}return k(D(e.prototype),"parseArrow",this).call(this,t)}},{key:"shouldParseArrow",value:function(){return this.match(o.colon)||k(D(e.prototype),"shouldParseArrow",this).call(this)}},{key:"setArrowFunctionParameters",value:function(t,s){-1!==this.state.noArrowParamsConversionAt.indexOf(t.start)?t.params=s:k(D(e.prototype),"setArrowFunctionParameters",this).call(this,t,s)}},{key:"checkParams",value:function(t,s,i){if(!i||-1===this.state.noArrowParamsConversionAt.indexOf(t.start))return k(D(e.prototype),"checkParams",this).apply(this,arguments)}},{key:"parseParenAndDistinguishExpression",value:function(t){return k(D(e.prototype),"parseParenAndDistinguishExpression",this).call(this,t&&-1===this.state.noArrowAt.indexOf(this.state.start))}},{key:"parseSubscripts",value:function(t,s,i,a){var r=this;if("Identifier"===t.type&&"async"===t.name&&-1!==this.state.noArrowAt.indexOf(s)){this.next();var n=this.startNodeAt(s,i);n.callee=t,n.arguments=this.parseCallExpressionArguments(o.parenR,!1),t=this.finishNode(n,"CallExpression")}else if("Identifier"===t.type&&"async"===t.name&&this.isRelational("<")){var h=this.state.clone(),u=this.tryParse((function(t){return r.parseAsyncArrowWithTypeParameters(s,i)||t()}),h);if(!u.error&&!u.aborted)return u.node;var l=this.tryParse((function(){return k(D(e.prototype),"parseSubscripts",r).call(r,t,s,i,a)}),h);if(l.node&&!l.error)return l.node;if(u.node)return this.state=u.failState,u.node;if(l.node)return this.state=l.failState,l.node;throw u.error||l.error}return k(D(e.prototype),"parseSubscripts",this).call(this,t,s,i,a)}},{key:"parseSubscript",value:function(t,s,i,a,r){var n=this;if(this.match(o.questionDot)&&this.isLookaheadRelational("<")){if(this.expectPlugin("optionalChaining"),r.optionalChainMember=!0,a)return r.stop=!0,t;this.next();var h=this.startNodeAt(s,i);return h.callee=t,h.typeArguments=this.flowParseTypeParameterInstantiation(),this.expect(o.parenL),h.arguments=this.parseCallExpressionArguments(o.parenR,!1),h.optional=!0,this.finishCallExpression(h,!0)}if(!a&&this.shouldParseTypes()&&this.isRelational("<")){var u=this.startNodeAt(s,i);u.callee=t;var l=this.tryParse((function(){return u.typeArguments=n.flowParseTypeParameterInstantiationCallOrNew(),n.expect(o.parenL),u.arguments=n.parseCallExpressionArguments(o.parenR,!1),r.optionalChainMember&&(u.optional=!1),n.finishCallExpression(u,r.optionalChainMember)}));if(l.node)return l.error&&(this.state=l.failState),l.node}return k(D(e.prototype),"parseSubscript",this).call(this,t,s,i,a,r)}},{key:"parseNewArguments",value:function(t){var s=this,i=null;this.shouldParseTypes()&&this.isRelational("<")&&(i=this.tryParse((function(){return s.flowParseTypeParameterInstantiationCallOrNew()})).node),t.typeArguments=i,k(D(e.prototype),"parseNewArguments",this).call(this,t)}},{key:"parseAsyncArrowWithTypeParameters",value:function(t,e){var s=this.startNodeAt(t,e);if(this.parseFunctionParams(s),this.parseArrow(s))return this.parseArrowExpression(s,void 0,!0)}},{key:"readToken_mult_modulo",value:function(t){var s=this.input.charCodeAt(this.state.pos+1);if(42===t&&47===s&&this.state.hasFlowComment)return this.state.hasFlowComment=!1,this.state.pos+=2,void this.nextToken();k(D(e.prototype),"readToken_mult_modulo",this).call(this,t)}},{key:"readToken_pipe_amp",value:function(t){var s=this.input.charCodeAt(this.state.pos+1);124!==t||125!==s?k(D(e.prototype),"readToken_pipe_amp",this).call(this,t):this.finishOp(o.braceBarR,2)}},{key:"parseTopLevel",value:function(t,s){var i=k(D(e.prototype),"parseTopLevel",this).call(this,t,s);return this.state.hasFlowComment&&this.raise(this.state.pos,"Unterminated flow-comment"),i}},{key:"skipBlockComment",value:function(){if(this.hasPlugin("flowComments")&&this.skipFlowComment())return this.state.hasFlowComment&&this.unexpected(null,"Cannot have a flow comment inside another flow comment"),this.hasFlowCommentCompletion(),this.state.pos+=this.skipFlowComment(),void(this.state.hasFlowComment=!0);if(this.state.hasFlowComment){var t=this.input.indexOf("*-/",this.state.pos+=2);if(-1===t)throw this.raise(this.state.pos-2,"Unterminated comment");this.state.pos=t+3}else k(D(e.prototype),"skipBlockComment",this).call(this)}},{key:"skipFlowComment",value:function(){for(var t=this.state.pos,e=2;-1!==[32,9].indexOf(this.input.charCodeAt(t+e));)e++;var s=this.input.charCodeAt(e+t),i=this.input.charCodeAt(e+t+1);return 58===s&&58===i?e+2:"flow-include"===this.input.slice(e+t,e+t+12)?e+12:58===s&&58!==i&&e}},{key:"hasFlowCommentCompletion",value:function(){if(-1===this.input.indexOf("*/",this.state.pos))throw this.raise(this.state.pos,"Unterminated comment")}},{key:"flowEnumErrorBooleanMemberNotInitialized",value:function(t,e){var s=e.enumName,i=e.memberName;this.raise(t,"Boolean enum members need to be initialized. Use either `".concat(i," = true,` ")+"or `".concat(i," = false,` in enum `").concat(s,"`."))}},{key:"flowEnumErrorInvalidMemberName",value:function(t,e){var s=e.enumName,i=e.memberName,a=i[0].toUpperCase()+i.slice(1);this.raise(t,"Enum member names cannot start with lowercase 'a' through 'z'. Instead of using "+"`".concat(i,"`, consider using `").concat(a,"`, in enum `").concat(s,"`."))}},{key:"flowEnumErrorDuplicateMemberName",value:function(t,e){var s=e.enumName,i=e.memberName;this.raise(t,"Enum member names need to be unique, but the name `".concat(i,"` has already been used ")+"before in enum `".concat(s,"`."))}},{key:"flowEnumErrorInconsistentMemberValues",value:function(t,e){var s=e.enumName;this.raise(t,"Enum `".concat(s,"` has inconsistent member initializers. Either use no initializers, or ")+"consistently use literals (either booleans, numbers, or strings) for all member initializers.")}},{key:"flowEnumErrorInvalidExplicitType",value:function(t,e){var s=e.enumName,i=e.suppliedType,a="Use one of `boolean`, `number`, `string`, or `symbol` in "+"enum `".concat(s,"`."),r=null===i?"Supplied enum type is not valid. ".concat(a):"Enum type `".concat(i,"` is not valid. ").concat(a);return this.raise(t,r)}},{key:"flowEnumErrorInvalidMemberInitializer",value:function(t,e){var s=e.enumName,i=e.explicitType,a=e.memberName,r=null;switch(i){case"boolean":case"number":case"string":r="Enum `".concat(s,"` has type `").concat(i,"`, so the initializer of ")+"`".concat(a,"` needs to be a ").concat(i," literal.");break;case"symbol":r="Symbol enum members cannot be initialized. Use `".concat(a,",` in ")+"enum `".concat(s,"`.");break;default:r="The enum member initializer for `".concat(a,"` needs to be a literal (either ")+"a boolean, number, or string) in enum `".concat(s,"`.")}return this.raise(t,r)}},{key:"flowEnumErrorNumberMemberNotInitialized",value:function(t,e){var s=e.enumName,i=e.memberName;this.raise(t,"Number enum members need to be initialized, e.g. `".concat(i," = 1` in enum `").concat(s,"`."))}},{key:"flowEnumErrorStringMemberInconsistentlyInitailized",value:function(t,e){var s=e.enumName;this.raise(t,"String enum members need to consistently either all use initializers, or use no initializers, "+"in enum `".concat(s,"`."))}},{key:"flowEnumMemberInit",value:function(){var t=this,e=this.state.start,s=function(){return t.match(o.comma)||t.match(o.braceR)};switch(this.state.type){case o.num:var i=this.parseLiteral(this.state.value,"NumericLiteral");return s()?{type:"number",pos:i.start,value:i}:{type:"invalid",pos:e};case o.string:var a=this.parseLiteral(this.state.value,"StringLiteral");return s()?{type:"string",pos:a.start,value:a}:{type:"invalid",pos:e};case o._true:case o._false:var r=this.parseBooleanLiteral();return s()?{type:"boolean",pos:r.start,value:r}:{type:"invalid",pos:e};default:return{type:"invalid",pos:e}}}},{key:"flowEnumMemberRaw",value:function(){var t=this.state.start;return{id:this.parseIdentifier(!0),init:this.eat(o.eq)?this.flowEnumMemberInit():{type:"none",pos:t}}}},{key:"flowEnumCheckExplicitTypeMismatch",value:function(t,e,s){var i=e.explicitType;null!==i&&i!==s&&this.flowEnumErrorInvalidMemberInitializer(t,e)}},{key:"flowEnumMembers",value:function(t){for(var e=t.enumName,s=t.explicitType,i=new Set,a={booleanMembers:[],numberMembers:[],stringMembers:[],defaultedMembers:[]};!this.match(o.braceR);){var r=this.startNode(),n=this.flowEnumMemberRaw(),h=n.id,u=n.init,l=h.name;if(""!==l){/^[a-z]/.test(l)&&this.flowEnumErrorInvalidMemberName(h.start,{enumName:e,memberName:l}),i.has(l)&&this.flowEnumErrorDuplicateMemberName(h.start,{enumName:e,memberName:l}),i.add(l);var c={enumName:e,explicitType:s,memberName:l};switch(r.id=h,u.type){case"boolean":this.flowEnumCheckExplicitTypeMismatch(u.pos,c,"boolean"),r.init=u.value,a.booleanMembers.push(this.finishNode(r,"EnumBooleanMember"));break;case"number":this.flowEnumCheckExplicitTypeMismatch(u.pos,c,"number"),r.init=u.value,a.numberMembers.push(this.finishNode(r,"EnumNumberMember"));break;case"string":this.flowEnumCheckExplicitTypeMismatch(u.pos,c,"string"),r.init=u.value,a.stringMembers.push(this.finishNode(r,"EnumStringMember"));break;case"invalid":throw this.flowEnumErrorInvalidMemberInitializer(u.pos,c);case"none":switch(s){case"boolean":this.flowEnumErrorBooleanMemberNotInitialized(u.pos,c);break;case"number":this.flowEnumErrorNumberMemberNotInitialized(u.pos,c);break;default:a.defaultedMembers.push(this.finishNode(r,"EnumDefaultedMember"))}}this.match(o.braceR)||this.expect(o.comma)}}return a}},{key:"flowEnumStringBody",value:function(t,e,s,i){var a=i.enumName;if(0===e.length)t.members=s;else if(0===s.length)t.members=e;else if(s.length>e.length){t.members=s;for(var r=0;r<e.length;r++){var n=e[r];this.flowEnumErrorStringMemberInconsistentlyInitailized(n.start,{enumName:a})}}else{t.members=e;for(var o=0;o<s.length;o++){var h=s[o];this.flowEnumErrorStringMemberInconsistentlyInitailized(h.start,{enumName:a})}}return this.finishNode(t,"EnumStringBody")}},{key:"flowEnumParseExplicitType",value:function(t){var e=t.enumName;if(this.eatContextual("of")){if(!this.match(o.name))throw this.flowEnumErrorInvalidExplicitType(this.state.start,{enumName:e,suppliedType:null});var s=this.state.value;return this.next(),"boolean"!==s&&"number"!==s&&"string"!==s&&"symbol"!==s&&this.flowEnumErrorInvalidExplicitType(this.state.start,{enumName:e,suppliedType:s}),s}return null}},{key:"flowParseEnumDeclaration",value:function(t){var e=this,s=this.parseIdentifier();t.id=s;var i=s.name,a=this.flowEnumParseExplicitType({enumName:i});this.expect(o.braceL);var r=this.startNode(),n=this.flowEnumMembers({enumName:i,explicitType:a});switch(a){case"boolean":r.explicitType=!0,r.members=n.booleanMembers,t.body=this.finishNode(r,"EnumBooleanBody");break;case"number":r.explicitType=!0,r.members=n.numberMembers,t.body=this.finishNode(r,"EnumNumberBody");break;case"string":r.explicitType=!0,t.body=this.flowEnumStringBody(r,n.stringMembers,n.defaultedMembers,{enumName:i});break;case"symbol":r.members=n.defaultedMembers,t.body=this.finishNode(r,"EnumSymbolBody");break;default:var h=function(){return r.members=[],e.finishNode(r,"EnumStringBody")};r.explicitType=!1;var u=n.booleanMembers.length,l=n.numberMembers.length,c=n.stringMembers.length,p=n.defaultedMembers.length;if(u||l||c||p)if(u||l)if(!l&&!c&&u>=p){r.members=n.booleanMembers,t.body=this.finishNode(r,"EnumBooleanBody");for(var d=0,f=n.defaultedMembers;d<f.length;d++){var m=f[d];this.flowEnumErrorBooleanMemberNotInitialized(m.start,{enumName:i,memberName:m.id.name})}}else if(!u&&!c&&l>=p){r.members=n.numberMembers,t.body=this.finishNode(r,"EnumNumberBody");for(var y=0,D=n.defaultedMembers;y<D.length;y++){var v=D[y];this.flowEnumErrorNumberMemberNotInitialized(v.start,{enumName:i,memberName:v.id.name})}}else t.body=h(),this.flowEnumErrorInconsistentMemberValues(s.start,{enumName:i});else t.body=this.flowEnumStringBody(r,n.stringMembers,n.defaultedMembers,{enumName:i});else t.body=h()}return this.expect(o.braceR),this.finishNode(t,"EnumDeclaration")}}]),e}(t)},typescript:function(t){return function(t){function e(){return d(this,e),g(this,D(e).apply(this,arguments))}return y(e,t),m(e,[{key:"getScopeHandler",value:function(){return nt}},{key:"tsIsIdentifier",value:function(){return this.match(o.name)}},{key:"tsNextTokenCanFollowModifier",value:function(){return this.next(),!(this.hasPrecedingLineBreak()||this.match(o.parenL)||this.match(o.parenR)||this.match(o.colon)||this.match(o.eq)||this.match(o.question)||this.match(o.bang))}},{key:"tsParseModifier",value:function(t){if(this.match(o.name)){var e=this.state.value;return-1!==t.indexOf(e)&&this.tsTryParse(this.tsNextTokenCanFollowModifier.bind(this))?e:void 0}}},{key:"tsParseModifiers",value:function(t){for(var e=Object.create(null);;){var s=this.state.start,i=this.tsParseModifier(t);if(!i)break;Object.hasOwnProperty.call(e,i)&&this.raise(s,"Duplicate modifier: '".concat(i,"'")),e[i]=!0}return e}},{key:"tsIsListTerminator",value:function(t){switch(t){case"EnumMembers":case"TypeMembers":return this.match(o.braceR);case"HeritageClauseElement":return this.match(o.braceL);case"TupleElementTypes":return this.match(o.bracketR);case"TypeParametersOrArguments":return this.isRelational(">")}throw new Error("Unreachable")}},{key:"tsParseList",value:function(t,e){for(var s=[];!this.tsIsListTerminator(t);)s.push(e());return s}},{key:"tsParseDelimitedList",value:function(t,e){return ot(this.tsParseDelimitedListWorker(t,e,!0))}},{key:"tsParseDelimitedListWorker",value:function(t,e,s){for(var i=[];!this.tsIsListTerminator(t);){var a=e();if(null==a)return;if(i.push(a),!this.eat(o.comma)){if(this.tsIsListTerminator(t))break;return void(s&&this.expect(o.comma))}}return i}},{key:"tsParseBracketedList",value:function(t,e,s,i){i||(s?this.expect(o.bracketL):this.expectRelational("<"));var a=this.tsParseDelimitedList(t,e);return s?this.expect(o.bracketR):this.expectRelational(">"),a}},{key:"tsParseImportType",value:function(){var t=this.startNode();return this.expect(o._import),this.expect(o.parenL),this.match(o.string)||this.raise(this.state.start,"Argument in a type import must be a string literal"),t.argument=this.parseExprAtom(),this.expect(o.parenR),this.eat(o.dot)&&(t.qualifier=this.tsParseEntityName(!0)),this.isRelational("<")&&(t.typeParameters=this.tsParseTypeArguments()),this.finishNode(t,"TSImportType")}},{key:"tsParseEntityName",value:function(t){for(var e=this.parseIdentifier();this.eat(o.dot);){var s=this.startNodeAtNode(e);s.left=e,s.right=this.parseIdentifier(t),e=this.finishNode(s,"TSQualifiedName")}return e}},{key:"tsParseTypeReference",value:function(){var t=this.startNode();return t.typeName=this.tsParseEntityName(!1),!this.hasPrecedingLineBreak()&&this.isRelational("<")&&(t.typeParameters=this.tsParseTypeArguments()),this.finishNode(t,"TSTypeReference")}},{key:"tsParseThisTypePredicate",value:function(t){this.next();var e=this.startNodeAtNode(t);return e.parameterName=t,e.typeAnnotation=this.tsParseTypeAnnotation(!1),this.finishNode(e,"TSTypePredicate")}},{key:"tsParseThisTypeNode",value:function(){var t=this.startNode();return this.next(),this.finishNode(t,"TSThisType")}},{key:"tsParseTypeQuery",value:function(){var t=this.startNode();return this.expect(o._typeof),this.match(o._import)?t.exprName=this.tsParseImportType():t.exprName=this.tsParseEntityName(!0),this.finishNode(t,"TSTypeQuery")}},{key:"tsParseTypeParameter",value:function(){var t=this.startNode();return t.name=this.parseIdentifierName(t.start),t.constraint=this.tsEatThenParseType(o._extends),t.default=this.tsEatThenParseType(o.eq),this.finishNode(t,"TSTypeParameter")}},{key:"tsTryParseTypeParameters",value:function(){if(this.isRelational("<"))return this.tsParseTypeParameters()}},{key:"tsParseTypeParameters",value:function(){var t=this.startNode();return this.isRelational("<")||this.match(o.jsxTagStart)?this.next():this.unexpected(),t.params=this.tsParseBracketedList("TypeParametersOrArguments",this.tsParseTypeParameter.bind(this),!1,!0),this.finishNode(t,"TSTypeParameterDeclaration")}},{key:"tsTryNextParseConstantContext",value:function(){return this.lookahead().type===o._const?(this.next(),this.tsParseTypeReference()):null}},{key:"tsFillSignature",value:function(t,e){var s=t===o.arrow;e.typeParameters=this.tsTryParseTypeParameters(),this.expect(o.parenL),e.parameters=this.tsParseBindingListForSignature(),s?e.typeAnnotation=this.tsParseTypeOrTypePredicateAnnotation(t):this.match(t)&&(e.typeAnnotation=this.tsParseTypeOrTypePredicateAnnotation(t))}},{key:"tsParseBindingListForSignature",value:function(){var t=this;return this.parseBindingList(o.parenR,41).map((function(e){return"Identifier"!==e.type&&"RestElement"!==e.type&&"ObjectPattern"!==e.type&&"ArrayPattern"!==e.type&&t.raise(e.start,"Name in a signature must be an Identifier, ObjectPattern or ArrayPattern,"+"instead got ".concat(e.type)),e}))}},{key:"tsParseTypeMemberSemicolon",value:function(){this.eat(o.comma)||this.semicolon()}},{key:"tsParseSignatureMember",value:function(t,e){return this.tsFillSignature(o.colon,e),this.tsParseTypeMemberSemicolon(),this.finishNode(e,t)}},{key:"tsIsUnambiguouslyIndexSignature",value:function(){return this.next(),this.eat(o.name)&&this.match(o.colon)}},{key:"tsTryParseIndexSignature",value:function(t){if(this.match(o.bracketL)&&this.tsLookAhead(this.tsIsUnambiguouslyIndexSignature.bind(this))){this.expect(o.bracketL);var e=this.parseIdentifier();e.typeAnnotation=this.tsParseTypeAnnotation(),this.resetEndLocation(e),this.expect(o.bracketR),t.parameters=[e];var s=this.tsTryParseTypeAnnotation();return s&&(t.typeAnnotation=s),this.tsParseTypeMemberSemicolon(),this.finishNode(t,"TSIndexSignature")}}},{key:"tsParsePropertyOrMethodSignature",value:function(t,e){this.eat(o.question)&&(t.optional=!0);var s=t;if(e||!this.match(o.parenL)&&!this.isRelational("<")){var i=s;e&&(i.readonly=!0);var a=this.tsTryParseTypeAnnotation();return a&&(i.typeAnnotation=a),this.tsParseTypeMemberSemicolon(),this.finishNode(i,"TSPropertySignature")}var r=s;return this.tsFillSignature(o.colon,r),this.tsParseTypeMemberSemicolon(),this.finishNode(r,"TSMethodSignature")}},{key:"tsParseTypeMember",value:function(){var t=this.startNode();if(this.match(o.parenL)||this.isRelational("<"))return this.tsParseSignatureMember("TSCallSignatureDeclaration",t);if(this.match(o._new)){var e=this.startNode();return this.next(),this.match(o.parenL)||this.isRelational("<")?this.tsParseSignatureMember("TSConstructSignatureDeclaration",t):(t.key=this.createIdentifier(e,"new"),this.tsParsePropertyOrMethodSignature(t,!1))}var s=!!this.tsParseModifier(["readonly"]),i=this.tsTryParseIndexSignature(t);return i?(s&&(t.readonly=!0),i):(this.parsePropertyName(t),this.tsParsePropertyOrMethodSignature(t,s))}},{key:"tsParseTypeLiteral",value:function(){var t=this.startNode();return t.members=this.tsParseObjectTypeMembers(),this.finishNode(t,"TSTypeLiteral")}},{key:"tsParseObjectTypeMembers",value:function(){this.expect(o.braceL);var t=this.tsParseList("TypeMembers",this.tsParseTypeMember.bind(this));return this.expect(o.braceR),t}},{key:"tsIsStartOfMappedType",value:function(){return this.next(),this.eat(o.plusMin)?this.isContextual("readonly"):(this.isContextual("readonly")&&this.next(),!!this.match(o.bracketL)&&(this.next(),!!this.tsIsIdentifier()&&(this.next(),this.match(o._in))))}},{key:"tsParseMappedTypeParameter",value:function(){var t=this.startNode();return t.name=this.parseIdentifierName(t.start),t.constraint=this.tsExpectThenParseType(o._in),this.finishNode(t,"TSTypeParameter")}},{key:"tsParseMappedType",value:function(){var t=this.startNode();return this.expect(o.braceL),this.match(o.plusMin)?(t.readonly=this.state.value,this.next(),this.expectContextual("readonly")):this.eatContextual("readonly")&&(t.readonly=!0),this.expect(o.bracketL),t.typeParameter=this.tsParseMappedTypeParameter(),this.expect(o.bracketR),this.match(o.plusMin)?(t.optional=this.state.value,this.next(),this.expect(o.question)):this.eat(o.question)&&(t.optional=!0),t.typeAnnotation=this.tsTryParseType(),this.semicolon(),this.expect(o.braceR),this.finishNode(t,"TSMappedType")}},{key:"tsParseTupleType",value:function(){var t=this,e=this.startNode();e.elementTypes=this.tsParseBracketedList("TupleElementTypes",this.tsParseTupleElementType.bind(this),!0,!1);var s=!1;return e.elementTypes.forEach((function(e){"TSOptionalType"===e.type?s=!0:s&&"TSRestType"!==e.type&&t.raise(e.start,"A required element cannot follow an optional element.")})),this.finishNode(e,"TSTupleType")}},{key:"tsParseTupleElementType",value:function(){if(this.match(o.ellipsis)){var t=this.startNode();return this.next(),t.typeAnnotation=this.tsParseType(),this.checkCommaAfterRest(93),this.finishNode(t,"TSRestType")}var e=this.tsParseType();if(this.eat(o.question)){var s=this.startNodeAtNode(e);return s.typeAnnotation=e,this.finishNode(s,"TSOptionalType")}return e}},{key:"tsParseParenthesizedType",value:function(){var t=this.startNode();return this.expect(o.parenL),t.typeAnnotation=this.tsParseType(),this.expect(o.parenR),this.finishNode(t,"TSParenthesizedType")}},{key:"tsParseFunctionOrConstructorType",value:function(t){var e=this.startNode();return"TSConstructorType"===t&&this.expect(o._new),this.tsFillSignature(o.arrow,e),this.finishNode(e,t)}},{key:"tsParseLiteralTypeNode",value:function(){var t=this,e=this.startNode();return e.literal=function(){switch(t.state.type){case o.num:case o.string:case o._true:case o._false:return t.parseExprAtom();default:throw t.unexpected()}}(),this.finishNode(e,"TSLiteralType")}},{key:"tsParseTemplateLiteralType",value:function(){var t=this.startNode(),e=this.parseTemplate(!1);return e.expressions.length>0&&this.raise(e.expressions[0].start,"Template literal types cannot have any substitution"),t.literal=e,this.finishNode(t,"TSLiteralType")}},{key:"tsParseNonArrayType",value:function(){switch(this.state.type){case o.name:case o._void:case o._null:var t=this.match(o._void)?"TSVoidKeyword":this.match(o._null)?"TSNullKeyword":function(t){switch(t){case"any":return"TSAnyKeyword";case"boolean":return"TSBooleanKeyword";case"bigint":return"TSBigIntKeyword";case"never":return"TSNeverKeyword";case"number":return"TSNumberKeyword";case"object":return"TSObjectKeyword";case"string":return"TSStringKeyword";case"symbol":return"TSSymbolKeyword";case"undefined":return"TSUndefinedKeyword";case"unknown":return"TSUnknownKeyword";default:return}}(this.state.value);if(void 0!==t&&46!==this.lookaheadCharCode()){var e=this.startNode();return this.next(),this.finishNode(e,t)}return this.tsParseTypeReference();case o.string:case o.num:case o._true:case o._false:return this.tsParseLiteralTypeNode();case o.plusMin:if("-"===this.state.value){var s=this.startNode();if(this.lookahead().type!==o.num)throw this.unexpected();return s.literal=this.parseMaybeUnary(),this.finishNode(s,"TSLiteralType")}break;case o._this:var i=this.tsParseThisTypeNode();return this.isContextual("is")&&!this.hasPrecedingLineBreak()?this.tsParseThisTypePredicate(i):i;case o._typeof:return this.tsParseTypeQuery();case o._import:return this.tsParseImportType();case o.braceL:return this.tsLookAhead(this.tsIsStartOfMappedType.bind(this))?this.tsParseMappedType():this.tsParseTypeLiteral();case o.bracketL:return this.tsParseTupleType();case o.parenL:return this.tsParseParenthesizedType();case o.backQuote:return this.tsParseTemplateLiteralType()}throw this.unexpected()}},{key:"tsParseArrayTypeOrHigher",value:function(){for(var t=this.tsParseNonArrayType();!this.hasPrecedingLineBreak()&&this.eat(o.bracketL);)if(this.match(o.bracketR)){var e=this.startNodeAtNode(t);e.elementType=t,this.expect(o.bracketR),t=this.finishNode(e,"TSArrayType")}else{var s=this.startNodeAtNode(t);s.objectType=t,s.indexType=this.tsParseType(),this.expect(o.bracketR),t=this.finishNode(s,"TSIndexedAccessType")}return t}},{key:"tsParseTypeOperator",value:function(t){var e=this.startNode();return this.expectContextual(t),e.operator=t,e.typeAnnotation=this.tsParseTypeOperatorOrHigher(),"readonly"===t&&this.tsCheckTypeAnnotationForReadOnly(e),this.finishNode(e,"TSTypeOperator")}},{key:"tsCheckTypeAnnotationForReadOnly",value:function(t){switch(t.typeAnnotation.type){case"TSTupleType":case"TSArrayType":return;default:this.raise(t.start,"'readonly' type modifier is only permitted on array and tuple literal types.")}}},{key:"tsParseInferType",value:function(){var t=this.startNode();this.expectContextual("infer");var e=this.startNode();return e.name=this.parseIdentifierName(e.start),t.typeParameter=this.finishNode(e,"TSTypeParameter"),this.finishNode(t,"TSInferType")}},{key:"tsParseTypeOperatorOrHigher",value:function(){var t=this,e=["keyof","unique","readonly"].find((function(e){return t.isContextual(e)}));return e?this.tsParseTypeOperator(e):this.isContextual("infer")?this.tsParseInferType():this.tsParseArrayTypeOrHigher()}},{key:"tsParseUnionOrIntersectionType",value:function(t,e,s){this.eat(s);var i=e();if(this.match(s)){for(var a=[i];this.eat(s);)a.push(e());var r=this.startNodeAtNode(i);r.types=a,i=this.finishNode(r,t)}return i}},{key:"tsParseIntersectionTypeOrHigher",value:function(){return this.tsParseUnionOrIntersectionType("TSIntersectionType",this.tsParseTypeOperatorOrHigher.bind(this),o.bitwiseAND)}},{key:"tsParseUnionTypeOrHigher",value:function(){return this.tsParseUnionOrIntersectionType("TSUnionType",this.tsParseIntersectionTypeOrHigher.bind(this),o.bitwiseOR)}},{key:"tsIsStartOfFunctionType",value:function(){return!!this.isRelational("<")||this.match(o.parenL)&&this.tsLookAhead(this.tsIsUnambiguouslyStartOfFunctionType.bind(this))}},{key:"tsSkipParameterStart",value:function(){if(this.match(o.name)||this.match(o._this))return this.next(),!0;if(this.match(o.braceL)){var t=1;for(this.next();t>0;)this.match(o.braceL)?++t:this.match(o.braceR)&&--t,this.next();return!0}if(this.match(o.bracketL)){var e=1;for(this.next();e>0;)this.match(o.bracketL)?++e:this.match(o.bracketR)&&--e,this.next();return!0}return!1}},{key:"tsIsUnambiguouslyStartOfFunctionType",value:function(){if(this.next(),this.match(o.parenR)||this.match(o.ellipsis))return!0;if(this.tsSkipParameterStart()){if(this.match(o.colon)||this.match(o.comma)||this.match(o.question)||this.match(o.eq))return!0;if(this.match(o.parenR)&&(this.next(),this.match(o.arrow)))return!0}return!1}},{key:"tsParseTypeOrTypePredicateAnnotation",value:function(t){var e=this;return this.tsInType((function(){var s=e.startNode();e.expect(t);var i=e.tsTryParse(e.tsParseTypePredicateAsserts.bind(e)),a=e.tsIsIdentifier()&&e.tsTryParse(e.tsParseTypePredicatePrefix.bind(e));if(!a){if(!i)return e.tsParseTypeAnnotation(!1,s);var r=e.startNodeAtNode(s);return r.parameterName=e.parseIdentifier(),r.asserts=i,s.typeAnnotation=e.finishNode(r,"TSTypePredicate"),e.finishNode(s,"TSTypeAnnotation")}var n=e.tsParseTypeAnnotation(!1),o=e.startNodeAtNode(s);return o.parameterName=a,o.typeAnnotation=n,o.asserts=i,s.typeAnnotation=e.finishNode(o,"TSTypePredicate"),e.finishNode(s,"TSTypeAnnotation")}))}},{key:"tsTryParseTypeOrTypePredicateAnnotation",value:function(){return this.match(o.colon)?this.tsParseTypeOrTypePredicateAnnotation(o.colon):void 0}},{key:"tsTryParseTypeAnnotation",value:function(){return this.match(o.colon)?this.tsParseTypeAnnotation():void 0}},{key:"tsTryParseType",value:function(){return this.tsEatThenParseType(o.colon)}},{key:"tsParseTypePredicatePrefix",value:function(){var t=this.parseIdentifier();if(this.isContextual("is")&&!this.hasPrecedingLineBreak())return this.next(),t}},{key:"tsParseTypePredicateAsserts",value:function(){return!!this.tsIsIdentifier()&&!("asserts"!==this.parseIdentifier().name||this.hasPrecedingLineBreak()||!this.tsIsIdentifier())}},{key:"tsParseTypeAnnotation",value:function(){var t=this,e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.startNode();return this.tsInType((function(){e&&t.expect(o.colon),s.typeAnnotation=t.tsParseType()})),this.finishNode(s,"TSTypeAnnotation")}},{key:"tsParseType",value:function(){ht(this.state.inType);var t=this.tsParseNonConditionalType();if(this.hasPrecedingLineBreak()||!this.eat(o._extends))return t;var e=this.startNodeAtNode(t);return e.checkType=t,e.extendsType=this.tsParseNonConditionalType(),this.expect(o.question),e.trueType=this.tsParseType(),this.expect(o.colon),e.falseType=this.tsParseType(),this.finishNode(e,"TSConditionalType")}},{key:"tsParseNonConditionalType",value:function(){return this.tsIsStartOfFunctionType()?this.tsParseFunctionOrConstructorType("TSFunctionType"):this.match(o._new)?this.tsParseFunctionOrConstructorType("TSConstructorType"):this.tsParseUnionTypeOrHigher()}},{key:"tsParseTypeAssertion",value:function(){var t=this.startNode(),e=this.tsTryNextParseConstantContext();return t.typeAnnotation=e||this.tsNextThenParseType(),this.expectRelational(">"),t.expression=this.parseMaybeUnary(),this.finishNode(t,"TSTypeAssertion")}},{key:"tsParseHeritageClause",value:function(t){var e=this.state.start,s=this.tsParseDelimitedList("HeritageClauseElement",this.tsParseExpressionWithTypeArguments.bind(this));return s.length||this.raise(e,"'".concat(t,"' list cannot be empty.")),s}},{key:"tsParseExpressionWithTypeArguments",value:function(){var t=this.startNode();return t.expression=this.tsParseEntityName(!1),this.isRelational("<")&&(t.typeParameters=this.tsParseTypeArguments()),this.finishNode(t,"TSExpressionWithTypeArguments")}},{key:"tsParseInterfaceDeclaration",value:function(t){t.id=this.parseIdentifier(),this.checkLVal(t.id,130,void 0,"typescript interface declaration"),t.typeParameters=this.tsTryParseTypeParameters(),this.eat(o._extends)&&(t.extends=this.tsParseHeritageClause("extends"));var e=this.startNode();return e.body=this.tsInType(this.tsParseObjectTypeMembers.bind(this)),t.body=this.finishNode(e,"TSInterfaceBody"),this.finishNode(t,"TSInterfaceDeclaration")}},{key:"tsParseTypeAliasDeclaration",value:function(t){return t.id=this.parseIdentifier(),this.checkLVal(t.id,2,void 0,"typescript type alias"),t.typeParameters=this.tsTryParseTypeParameters(),t.typeAnnotation=this.tsExpectThenParseType(o.eq),this.semicolon(),this.finishNode(t,"TSTypeAliasDeclaration")}},{key:"tsInNoContext",value:function(t){var e=this.state.context;this.state.context=[e[0]];try{return t()}finally{this.state.context=e}}},{key:"tsInType",value:function(t){var e=this.state.inType;this.state.inType=!0;try{return t()}finally{this.state.inType=e}}},{key:"tsEatThenParseType",value:function(t){return this.match(t)?this.tsNextThenParseType():void 0}},{key:"tsExpectThenParseType",value:function(t){var e=this;return this.tsDoThenParseType((function(){return e.expect(t)}))}},{key:"tsNextThenParseType",value:function(){var t=this;return this.tsDoThenParseType((function(){return t.next()}))}},{key:"tsDoThenParseType",value:function(t){var e=this;return this.tsInType((function(){return t(),e.tsParseType()}))}},{key:"tsParseEnumMember",value:function(){var t=this.startNode();return t.id=this.match(o.string)?this.parseExprAtom():this.parseIdentifier(!0),this.eat(o.eq)&&(t.initializer=this.parseMaybeAssign()),this.finishNode(t,"TSEnumMember")}},{key:"tsParseEnumDeclaration",value:function(t,e){return e&&(t.const=!0),t.id=this.parseIdentifier(),this.checkLVal(t.id,e?779:267,void 0,"typescript enum declaration"),this.expect(o.braceL),t.members=this.tsParseDelimitedList("EnumMembers",this.tsParseEnumMember.bind(this)),this.expect(o.braceR),this.finishNode(t,"TSEnumDeclaration")}},{key:"tsParseModuleBlock",value:function(){var t=this.startNode();return this.scope.enter(0),this.expect(o.braceL),this.parseBlockOrModuleBlockBody(t.body=[],void 0,!0,o.braceR),this.scope.exit(),this.finishNode(t,"TSModuleBlock")}},{key:"tsParseModuleOrNamespaceDeclaration",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t.id=this.parseIdentifier(),e||this.checkLVal(t.id,1024,null,"module or namespace declaration"),this.eat(o.dot)){var s=this.startNode();this.tsParseModuleOrNamespaceDeclaration(s,!0),t.body=s}else this.scope.enter(512),t.body=this.tsParseModuleBlock(),this.scope.exit();return this.finishNode(t,"TSModuleDeclaration")}},{key:"tsParseAmbientExternalModuleDeclaration",value:function(t){return this.isContextual("global")?(t.global=!0,t.id=this.parseIdentifier()):this.match(o.string)?t.id=this.parseExprAtom():this.unexpected(),this.match(o.braceL)?(this.scope.enter(512),t.body=this.tsParseModuleBlock(),this.scope.exit()):this.semicolon(),this.finishNode(t,"TSModuleDeclaration")}},{key:"tsParseImportEqualsDeclaration",value:function(t,e){return t.isExport=e||!1,t.id=this.parseIdentifier(),this.expect(o.eq),t.moduleReference=this.tsParseModuleReference(),this.semicolon(),this.finishNode(t,"TSImportEqualsDeclaration")}},{key:"tsIsExternalModuleReference",value:function(){return this.isContextual("require")&&40===this.lookaheadCharCode()}},{key:"tsParseModuleReference",value:function(){return this.tsIsExternalModuleReference()?this.tsParseExternalModuleReference():this.tsParseEntityName(!1)}},{key:"tsParseExternalModuleReference",value:function(){var t=this.startNode();if(this.expectContextual("require"),this.expect(o.parenL),!this.match(o.string))throw this.unexpected();return t.expression=this.parseExprAtom(),this.expect(o.parenR),this.finishNode(t,"TSExternalModuleReference")}},{key:"tsLookAhead",value:function(t){var e=this.state.clone(),s=t();return this.state=e,s}},{key:"tsTryParseAndCatch",value:function(t){var e=this.tryParse((function(e){return t()||e()}));if(!e.aborted&&e.node)return e.error&&(this.state=e.failState),e.node}},{key:"tsTryParse",value:function(t){var e=this.state.clone(),s=t();return void 0!==s&&!1!==s?s:void(this.state=e)}},{key:"tsTryParseDeclare",value:function(t){if(!this.isLineTerminator()){var e,s=this.state.type;switch(this.isContextual("let")&&(s=o._var,e="let"),s){case o._function:return this.parseFunctionStatement(t,!1,!0);case o._class:return t.declare=!0,this.parseClass(t,!0,!1);case o._const:if(this.match(o._const)&&this.isLookaheadContextual("enum"))return this.expect(o._const),this.expectContextual("enum"),this.tsParseEnumDeclaration(t,!0);case o._var:return e=e||this.state.value,this.parseVarStatement(t,e);case o.name:var i=this.state.value;return"global"===i?this.tsParseAmbientExternalModuleDeclaration(t):this.tsParseDeclaration(t,i,!0)}}}},{key:"tsTryParseExportDeclaration",value:function(){return this.tsParseDeclaration(this.startNode(),this.state.value,!0)}},{key:"tsParseExpressionStatement",value:function(t,e){switch(e.name){case"declare":var s=this.tsTryParseDeclare(t);if(s)return s.declare=!0,s;break;case"global":if(this.match(o.braceL)){this.scope.enter(512);var i=t;return i.global=!0,i.id=e,i.body=this.tsParseModuleBlock(),this.scope.exit(),this.finishNode(i,"TSModuleDeclaration")}break;default:return this.tsParseDeclaration(t,e.name,!1)}}},{key:"tsParseDeclaration",value:function(t,e,s){switch(e){case"abstract":if(this.tsCheckLineTerminatorAndMatch(o._class,s)){var i=t;return i.abstract=!0,s&&(this.next(),this.match(o._class)||this.unexpected(null,o._class)),this.parseClass(i,!0,!1)}break;case"enum":if(s||this.match(o.name))return s&&this.next(),this.tsParseEnumDeclaration(t,!1);break;case"interface":if(this.tsCheckLineTerminatorAndMatch(o.name,s))return s&&this.next(),this.tsParseInterfaceDeclaration(t);break;case"module":if(s&&this.next(),this.match(o.string))return this.tsParseAmbientExternalModuleDeclaration(t);if(this.tsCheckLineTerminatorAndMatch(o.name,s))return this.tsParseModuleOrNamespaceDeclaration(t);break;case"namespace":if(this.tsCheckLineTerminatorAndMatch(o.name,s))return s&&this.next(),this.tsParseModuleOrNamespaceDeclaration(t);break;case"type":if(this.tsCheckLineTerminatorAndMatch(o.name,s))return s&&this.next(),this.tsParseTypeAliasDeclaration(t)}}},{key:"tsCheckLineTerminatorAndMatch",value:function(t,e){return(e||this.match(t))&&!this.isLineTerminator()}},{key:"tsTryParseGenericAsyncArrowFunction",value:function(t,s){var i=this;if(this.isRelational("<")){var a=this.tsTryParseAndCatch((function(){var a=i.startNodeAt(t,s);return a.typeParameters=i.tsParseTypeParameters(),k(D(e.prototype),"parseFunctionParams",i).call(i,a),a.returnType=i.tsTryParseTypeOrTypePredicateAnnotation(),i.expect(o.arrow),a}));if(a)return this.parseArrowExpression(a,null,!0)}}},{key:"tsParseTypeArguments",value:function(){var t=this,e=this.startNode();return e.params=this.tsInType((function(){return t.tsInNoContext((function(){return t.expectRelational("<"),t.tsParseDelimitedList("TypeParametersOrArguments",t.tsParseType.bind(t))}))})),this.state.exprAllowed=!1,this.expectRelational(">"),this.finishNode(e,"TSTypeParameterInstantiation")}},{key:"tsIsDeclarationStart",value:function(){if(this.match(o.name))switch(this.state.value){case"abstract":case"declare":case"enum":case"interface":case"module":case"namespace":case"type":return!0}return!1}},{key:"isExportDefaultSpecifier",value:function(){return!this.tsIsDeclarationStart()&&k(D(e.prototype),"isExportDefaultSpecifier",this).call(this)}},{key:"parseAssignableListItem",value:function(t,e){var s,i=this.state.start,a=this.state.startLoc,r=!1;t&&(s=this.parseAccessModifier(),r=!!this.tsParseModifier(["readonly"]));var n=this.parseMaybeDefault();this.parseAssignableListItemTypes(n);var o=this.parseMaybeDefault(n.start,n.loc.start,n);if(s||r){var h=this.startNodeAt(i,a);return e.length&&(h.decorators=e),s&&(h.accessibility=s),r&&(h.readonly=r),"Identifier"!==o.type&&"AssignmentPattern"!==o.type&&this.raise(h.start,"A parameter property may not be declared using a binding pattern."),h.parameter=o,this.finishNode(h,"TSParameterProperty")}return e.length&&(n.decorators=e),o}},{key:"parseFunctionBodyAndFinish",value:function(t,s){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.match(o.colon)&&(t.returnType=this.tsParseTypeOrTypePredicateAnnotation(o.colon));var a="FunctionDeclaration"===s?"TSDeclareFunction":"ClassMethod"===s?"TSDeclareMethod":void 0;a&&!this.match(o.braceL)&&this.isLineTerminator()?this.finishNode(t,a):k(D(e.prototype),"parseFunctionBodyAndFinish",this).call(this,t,s,i)}},{key:"registerFunctionStatementId",value:function(t){!t.body&&t.id?this.checkLVal(t.id,1024,null,"function name"):k(D(e.prototype),"registerFunctionStatementId",this).apply(this,arguments)}},{key:"parseSubscript",value:function(t,s,i,a,r){var n=this;if(!this.hasPrecedingLineBreak()&&this.match(o.bang)){this.state.exprAllowed=!1,this.next();var h=this.startNodeAt(s,i);return h.expression=t,this.finishNode(h,"TSNonNullExpression")}if(this.isRelational("<")){var u=this.tsTryParseAndCatch((function(){if(!a&&n.atPossibleAsync(t)){var e=n.tsTryParseGenericAsyncArrowFunction(s,i);if(e)return e}var h=n.startNodeAt(s,i);h.callee=t;var u=n.tsParseTypeArguments();if(u){if(!a&&n.eat(o.parenL))return h.arguments=n.parseCallExpressionArguments(o.parenR,!1),h.typeParameters=u,n.finishCallExpression(h,r.optionalChainMember);if(n.match(o.backQuote))return n.parseTaggedTemplateExpression(s,i,t,r,u)}n.unexpected()}));if(u)return u}return k(D(e.prototype),"parseSubscript",this).call(this,t,s,i,a,r)}},{key:"parseNewArguments",value:function(t){var s=this;if(this.isRelational("<")){var i=this.tsTryParseAndCatch((function(){var t=s.tsParseTypeArguments();return s.match(o.parenL)||s.unexpected(),t}));i&&(t.typeParameters=i)}k(D(e.prototype),"parseNewArguments",this).call(this,t)}},{key:"parseExprOp",value:function(t,s,i,a,r){if(ot(o._in.binop)>a&&!this.hasPrecedingLineBreak()&&this.isContextual("as")){var n=this.startNodeAt(s,i);n.expression=t;var h=this.tsTryNextParseConstantContext();return n.typeAnnotation=h||this.tsNextThenParseType(),this.finishNode(n,"TSAsExpression"),this.parseExprOp(n,s,i,a,r)}return k(D(e.prototype),"parseExprOp",this).call(this,t,s,i,a,r)}},{key:"checkReservedWord",value:function(t,e,s,i){}},{key:"checkDuplicateExports",value:function(){}},{key:"parseImport",value:function(t){return this.match(o.name)&&this.lookahead().type===o.eq?this.tsParseImportEqualsDeclaration(t):k(D(e.prototype),"parseImport",this).call(this,t)}},{key:"parseExport",value:function(t){if(this.match(o._import))return this.expect(o._import),this.tsParseImportEqualsDeclaration(t,!0);if(this.eat(o.eq)){var s=t;return s.expression=this.parseExpression(),this.semicolon(),this.finishNode(s,"TSExportAssignment")}if(this.eatContextual("as")){var i=t;return this.expectContextual("namespace"),i.id=this.parseIdentifier(),this.semicolon(),this.finishNode(i,"TSNamespaceExportDeclaration")}return k(D(e.prototype),"parseExport",this).call(this,t)}},{key:"isAbstractClass",value:function(){return this.isContextual("abstract")&&this.lookahead().type===o._class}},{key:"parseExportDefaultExpression",value:function(){if(this.isAbstractClass()){var t=this.startNode();return this.next(),this.parseClass(t,!0,!0),t.abstract=!0,t}if("interface"===this.state.value){var s=this.tsParseDeclaration(this.startNode(),this.state.value,!0);if(s)return s}return k(D(e.prototype),"parseExportDefaultExpression",this).call(this)}},{key:"parseStatementContent",value:function(t,s){if(this.state.type===o._const){var i=this.lookahead();if(i.type===o.name&&"enum"===i.value){var a=this.startNode();return this.expect(o._const),this.expectContextual("enum"),this.tsParseEnumDeclaration(a,!0)}}return k(D(e.prototype),"parseStatementContent",this).call(this,t,s)}},{key:"parseAccessModifier",value:function(){return this.tsParseModifier(["public","protected","private"])}},{key:"parseClassMember",value:function(t,s,i,a){var r=this.parseAccessModifier();r&&(s.accessibility=r),k(D(e.prototype),"parseClassMember",this).call(this,t,s,i,a)}},{key:"parseClassMemberWithIsStatic",value:function(t,s,i,a,r){var n=this.tsParseModifiers(["abstract","readonly","declare"]);Object.assign(s,n);var o=this.tsTryParseIndexSignature(s);if(o)return t.body.push(o),n.abstract&&this.raise(s.start,"Index signatures cannot have the 'abstract' modifier"),a&&this.raise(s.start,"Index signatures cannot have the 'static' modifier"),void(s.accessibility&&this.raise(s.start,"Index signatures cannot have an accessibility modifier ('".concat(s.accessibility,"')")));k(D(e.prototype),"parseClassMemberWithIsStatic",this).call(this,t,s,i,a,r)}},{key:"parsePostMemberNameModifiers",value:function(t){this.eat(o.question)&&(t.optional=!0),t.readonly&&this.match(o.parenL)&&this.raise(t.start,"Class methods cannot have the 'readonly' modifier"),t.declare&&this.match(o.parenL)&&this.raise(t.start,"Class methods cannot have the 'declare' modifier")}},{key:"parseExpressionStatement",value:function(t,s){return("Identifier"===s.type?this.tsParseExpressionStatement(t,s):void 0)||k(D(e.prototype),"parseExpressionStatement",this).call(this,t,s)}},{key:"shouldParseExportDeclaration",value:function(){return!!this.tsIsDeclarationStart()||k(D(e.prototype),"shouldParseExportDeclaration",this).call(this)}},{key:"parseConditional",value:function(t,s,i,a,r){var n=this;if(!r||!this.match(o.question))return k(D(e.prototype),"parseConditional",this).call(this,t,s,i,a,r);var h=this.tryParse((function(){return k(D(e.prototype),"parseConditional",n).call(n,t,s,i,a)}));return h.node?(h.error&&(this.state=h.failState),h.node):(r.start=h.error.pos||this.state.start,t)}},{key:"parseParenItem",value:function(t,s,i){if(t=k(D(e.prototype),"parseParenItem",this).call(this,t,s,i),this.eat(o.question)&&(t.optional=!0,this.resetEndLocation(t)),this.match(o.colon)){var a=this.startNodeAt(s,i);return a.expression=t,a.typeAnnotation=this.tsParseTypeAnnotation(),this.finishNode(a,"TSTypeCastExpression")}return t}},{key:"parseExportDeclaration",value:function(t){var s,i=this.state.start,a=this.state.startLoc,r=this.eatContextual("declare");return this.match(o.name)&&(s=this.tsTryParseExportDeclaration()),s||(s=k(D(e.prototype),"parseExportDeclaration",this).call(this,t)),s&&r&&(this.resetStartLocation(s,i,a),s.declare=!0),s}},{key:"parseClassId",value:function(t,s,i){if(s&&!i||!this.isContextual("implements")){k(D(e.prototype),"parseClassId",this).call(this,t,s,i,t.declare?1024:139);var a=this.tsTryParseTypeParameters();a&&(t.typeParameters=a)}}},{key:"parseClassPropertyAnnotation",value:function(t){!t.optional&&this.eat(o.bang)&&(t.definite=!0);var e=this.tsTryParseTypeAnnotation();e&&(t.typeAnnotation=e)}},{key:"parseClassProperty",value:function(t){return this.parseClassPropertyAnnotation(t),t.declare&&this.match(o.equal)&&this.raise(this.state.start,"'declare' class fields cannot have an initializer"),k(D(e.prototype),"parseClassProperty",this).call(this,t)}},{key:"parseClassPrivateProperty",value:function(t){return t.abstract&&this.raise(t.start,"Private elements cannot have the 'abstract' modifier."),t.accessibility&&this.raise(t.start,"Private elements cannot have an accessibility modifier ('".concat(t.accessibility,"')")),this.parseClassPropertyAnnotation(t),k(D(e.prototype),"parseClassPrivateProperty",this).call(this,t)}},{key:"pushClassMethod",value:function(t,s,i,a,r,n){var o=this.tsTryParseTypeParameters();o&&(s.typeParameters=o),k(D(e.prototype),"pushClassMethod",this).call(this,t,s,i,a,r,n)}},{key:"pushClassPrivateMethod",value:function(t,s,i,a){var r=this.tsTryParseTypeParameters();r&&(s.typeParameters=r),k(D(e.prototype),"pushClassPrivateMethod",this).call(this,t,s,i,a)}},{key:"parseClassSuper",value:function(t){k(D(e.prototype),"parseClassSuper",this).call(this,t),t.superClass&&this.isRelational("<")&&(t.superTypeParameters=this.tsParseTypeArguments()),this.eatContextual("implements")&&(t.implements=this.tsParseHeritageClause("implements"))}},{key:"parseObjPropValue",value:function(t){var s,i=this.tsTryParseTypeParameters();i&&(t.typeParameters=i);for(var a=arguments.length,r=new Array(a>1?a-1:0),n=1;n<a;n++)r[n-1]=arguments[n];(s=k(D(e.prototype),"parseObjPropValue",this)).call.apply(s,[this,t].concat(r))}},{key:"parseFunctionParams",value:function(t,s){var i=this.tsTryParseTypeParameters();i&&(t.typeParameters=i),k(D(e.prototype),"parseFunctionParams",this).call(this,t,s)}},{key:"parseVarId",value:function(t,s){k(D(e.prototype),"parseVarId",this).call(this,t,s),"Identifier"===t.id.type&&this.eat(o.bang)&&(t.definite=!0);var i=this.tsTryParseTypeAnnotation();i&&(t.id.typeAnnotation=i,this.resetEndLocation(t.id))}},{key:"parseAsyncArrowFromCallExpression",value:function(t,s){return this.match(o.colon)&&(t.returnType=this.tsParseTypeAnnotation()),k(D(e.prototype),"parseAsyncArrowFromCallExpression",this).call(this,t,s)}},{key:"parseMaybeAssign",value:function(){for(var t,s,i,a,r,n=this,h=arguments.length,u=new Array(h),l=0;l<h;l++)u[l]=arguments[l];if(this.match(o.jsxTagStart)){if(t=this.state.clone(),!(s=this.tryParse((function(){var t;return(t=k(D(e.prototype),"parseMaybeAssign",n)).call.apply(t,[n].concat(u))}),t)).error)return s.node;var c=this.state.context;c[c.length-1]===F.j_oTag?c.length-=2:c[c.length-1]===F.j_expr&&(c.length-=1)}if(!(s&&s.error||this.isRelational("<")))return(a=k(D(e.prototype),"parseMaybeAssign",this)).call.apply(a,[this].concat(u));t=t||this.state.clone();var p=this.tryParse((function(t){var s;r=n.tsParseTypeParameters();var i=(s=k(D(e.prototype),"parseMaybeAssign",n)).call.apply(s,[n].concat(u));return("ArrowFunctionExpression"!==i.type||i.extra&&i.extra.parenthesized)&&t(),r&&0!==r.params.length&&n.resetStartLocationFromNode(i,r),i.typeParameters=r,i}),t);if(!p.error&&!p.aborted)return p.node;if(!s&&(ht(!this.hasPlugin("jsx")),!(i=this.tryParse((function(){var t;return(t=k(D(e.prototype),"parseMaybeAssign",n)).call.apply(t,[n].concat(u))}),t)).error))return i.node;if(s&&s.node)return this.state=s.failState,s.node;if(p.node)return this.state=p.failState,p.node;if(i&&i.node)return this.state=i.failState,i.node;if(s&&s.thrown)throw s.error;if(p.thrown)throw p.error;if(i&&i.thrown)throw i.error;throw s&&s.error||p.error||i&&i.error}},{key:"parseMaybeUnary",value:function(t){return!this.hasPlugin("jsx")&&this.isRelational("<")?this.tsParseTypeAssertion():k(D(e.prototype),"parseMaybeUnary",this).call(this,t)}},{key:"parseArrow",value:function(t){var s=this;if(this.match(o.colon)){var i=this.tryParse((function(t){var e=s.tsParseTypeOrTypePredicateAnnotation(o.colon);return!s.canInsertSemicolon()&&s.match(o.arrow)||t(),e}));if(i.aborted)return;i.thrown||(i.error&&(this.state=i.failState),t.returnType=i.node)}return k(D(e.prototype),"parseArrow",this).call(this,t)}},{key:"parseAssignableListItemTypes",value:function(t){this.eat(o.question)&&("Identifier"!==t.type&&this.raise(t.start,"A binding pattern parameter cannot be optional in an implementation signature."),t.optional=!0);var e=this.tsTryParseTypeAnnotation();return e&&(t.typeAnnotation=e),this.resetEndLocation(t),t}},{key:"toAssignable",value:function(t,s,i){switch(t.type){case"TSTypeCastExpression":return k(D(e.prototype),"toAssignable",this).call(this,this.typeCastToParameter(t),s,i);case"TSParameterProperty":return k(D(e.prototype),"toAssignable",this).call(this,t,s,i);case"TSAsExpression":case"TSNonNullExpression":case"TSTypeAssertion":return t.expression=this.toAssignable(t.expression,s,i),t;default:return k(D(e.prototype),"toAssignable",this).call(this,t,s,i)}}},{key:"checkLVal",value:function(t){var s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:64,i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0;switch(t.type){case"TSTypeCastExpression":return;case"TSParameterProperty":return void this.checkLVal(t.parameter,s,i,"parameter property");case"TSAsExpression":case"TSNonNullExpression":case"TSTypeAssertion":return void this.checkLVal(t.expression,s,i,a);default:return void k(D(e.prototype),"checkLVal",this).call(this,t,s,i,a)}}},{key:"parseBindingAtom",value:function(){switch(this.state.type){case o._this:return this.parseIdentifier(!0);default:return k(D(e.prototype),"parseBindingAtom",this).call(this)}}},{key:"parseMaybeDecoratorArguments",value:function(t){if(this.isRelational("<")){var s=this.tsParseTypeArguments();if(this.match(o.parenL)){var i=k(D(e.prototype),"parseMaybeDecoratorArguments",this).call(this,t);return i.typeParameters=s,i}this.unexpected(this.state.start,o.parenL)}return k(D(e.prototype),"parseMaybeDecoratorArguments",this).call(this,t)}},{key:"isClassMethod",value:function(){return this.isRelational("<")||k(D(e.prototype),"isClassMethod",this).call(this)}},{key:"isClassProperty",value:function(){return this.match(o.bang)||this.match(o.colon)||k(D(e.prototype),"isClassProperty",this).call(this)}},{key:"parseMaybeDefault",value:function(){for(var t,s=arguments.length,i=new Array(s),a=0;a<s;a++)i[a]=arguments[a];var r=(t=k(D(e.prototype),"parseMaybeDefault",this)).call.apply(t,[this].concat(i));return"AssignmentPattern"===r.type&&r.typeAnnotation&&r.right.start<r.typeAnnotation.start&&this.raise(r.typeAnnotation.start,"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`"),r}},{key:"getTokenFromCode",value:function(t){return!this.state.inType||62!==t&&60!==t?k(D(e.prototype),"getTokenFromCode",this).call(this,t):this.finishOp(o.relational,1)}},{key:"toAssignableList",value:function(t){for(var s=0;s<t.length;s++){var i=t[s];if(i)switch(i.type){case"TSTypeCastExpression":t[s]=this.typeCastToParameter(i);break;case"TSAsExpression":case"TSTypeAssertion":this.raise(i.start,"Unexpected type cast in parameter position.")}}return k(D(e.prototype),"toAssignableList",this).apply(this,arguments)}},{key:"typeCastToParameter",value:function(t){return t.expression.typeAnnotation=t.typeAnnotation,this.resetEndLocation(t.expression,t.typeAnnotation.end,t.typeAnnotation.loc.end),t.expression}},{key:"toReferencedList",value:function(t,e){for(var s=0;s<t.length;s++){var i=t[s];i&&i._exprListItem&&"TsTypeCastExpression"===i.type&&this.raise(i.start,"Did not expect a type annotation here.")}return t}},{key:"shouldParseArrow",value:function(){return this.match(o.colon)||k(D(e.prototype),"shouldParseArrow",this).call(this)}},{key:"shouldParseAsyncArrow",value:function(){return this.match(o.colon)||k(D(e.prototype),"shouldParseAsyncArrow",this).call(this)}},{key:"canHaveLeadingDecorator",value:function(){return k(D(e.prototype),"canHaveLeadingDecorator",this).call(this)||this.isAbstractClass()}},{key:"jsxParseOpeningElementAfterName",value:function(t){var s=this;if(this.isRelational("<")){var i=this.tsTryParseAndCatch((function(){return s.tsParseTypeArguments()}));i&&(t.typeParameters=i)}return k(D(e.prototype),"jsxParseOpeningElementAfterName",this).call(this,t)}},{key:"getGetterSetterExpectedParamCount",value:function(t){var s=k(D(e.prototype),"getGetterSetterExpectedParamCount",this).call(this,t),i=t.params[0];return i&&"Identifier"===i.type&&"this"===i.name?s+1:s}}]),e}(t)},v8intrinsic:function(t){return function(t){function e(){return d(this,e),g(this,D(e).apply(this,arguments))}return y(e,t),m(e,[{key:"parseV8Intrinsic",value:function(){if(this.match(o.modulo)){var t=this.state.start,e=this.startNode();if(this.eat(o.modulo),this.match(o.name)){var s=this.parseIdentifierName(this.state.start),i=this.createIdentifier(e,s);if(i.type="V8IntrinsicIdentifier",this.match(o.parenL))return i}this.unexpected(t)}}},{key:"parseExprAtom",value:function(){return this.parseV8Intrinsic()||k(D(e.prototype),"parseExprAtom",this).apply(this,arguments)}}]),e}(t)},placeholders:function(t){return function(t){function e(){return d(this,e),g(this,D(e).apply(this,arguments))}return y(e,t),m(e,[{key:"parsePlaceholder",value:function(t){if(this.match(o.placeholder)){var s=this.startNode();return this.next(),this.assertNoSpace("Unexpected space in placeholder."),s.name=k(D(e.prototype),"parseIdentifier",this).call(this,!0),this.assertNoSpace("Unexpected space in placeholder."),this.expect(o.placeholder),this.finishPlaceholder(s,t)}}},{key:"finishPlaceholder",value:function(t,e){var s=!(!t.expectedNode||"Placeholder"!==t.type);return t.expectedNode=e,s?t:this.finishNode(t,"Placeholder")}},{key:"getTokenFromCode",value:function(t){return 37===t&&37===this.input.charCodeAt(this.state.pos+1)?this.finishOp(o.placeholder,2):k(D(e.prototype),"getTokenFromCode",this).apply(this,arguments)}},{key:"parseExprAtom",value:function(){return this.parsePlaceholder("Expression")||k(D(e.prototype),"parseExprAtom",this).apply(this,arguments)}},{key:"parseIdentifier",value:function(){return this.parsePlaceholder("Identifier")||k(D(e.prototype),"parseIdentifier",this).apply(this,arguments)}},{key:"checkReservedWord",value:function(t){void 0!==t&&k(D(e.prototype),"checkReservedWord",this).apply(this,arguments)}},{key:"parseBindingAtom",value:function(){return this.parsePlaceholder("Pattern")||k(D(e.prototype),"parseBindingAtom",this).apply(this,arguments)}},{key:"checkLVal",value:function(t){"Placeholder"!==t.type&&k(D(e.prototype),"checkLVal",this).apply(this,arguments)}},{key:"toAssignable",value:function(t){return t&&"Placeholder"===t.type&&"Expression"===t.expectedNode?(t.expectedNode="Pattern",t):k(D(e.prototype),"toAssignable",this).apply(this,arguments)}},{key:"verifyBreakContinue",value:function(t){t.label&&"Placeholder"===t.label.type||k(D(e.prototype),"verifyBreakContinue",this).apply(this,arguments)}},{key:"parseExpressionStatement",value:function(t,s){if("Placeholder"!==s.type||s.extra&&s.extra.parenthesized)return k(D(e.prototype),"parseExpressionStatement",this).apply(this,arguments);if(this.match(o.colon)){var i=t;return i.label=this.finishPlaceholder(s,"Identifier"),this.next(),i.body=this.parseStatement("label"),this.finishNode(i,"LabeledStatement")}return this.semicolon(),t.name=s.name,this.finishPlaceholder(t,"Statement")}},{key:"parseBlock",value:function(){return this.parsePlaceholder("BlockStatement")||k(D(e.prototype),"parseBlock",this).apply(this,arguments)}},{key:"parseFunctionId",value:function(){return this.parsePlaceholder("Identifier")||k(D(e.prototype),"parseFunctionId",this).apply(this,arguments)}},{key:"parseClass",value:function(t,e,s){var i=e?"ClassDeclaration":"ClassExpression";this.next(),this.takeDecorators(t);var a=this.parsePlaceholder("Identifier");if(a)if(this.match(o._extends)||this.match(o.placeholder)||this.match(o.braceL))t.id=a;else{if(s||!e)return t.id=null,t.body=this.finishPlaceholder(a,"ClassBody"),this.finishNode(t,i);this.unexpected(null,"A class name is required")}else this.parseClassId(t,e,s);return this.parseClassSuper(t),t.body=this.parsePlaceholder("ClassBody")||this.parseClassBody(!!t.superClass),this.finishNode(t,i)}},{key:"parseExport",value:function(t){var s=this.parsePlaceholder("Identifier");if(!s)return k(D(e.prototype),"parseExport",this).apply(this,arguments);if(!this.isContextual("from")&&!this.match(o.comma))return t.specifiers=[],t.source=null,t.declaration=this.finishPlaceholder(s,"Declaration"),this.finishNode(t,"ExportNamedDeclaration");this.expectPlugin("exportDefaultFrom");var i=this.startNode();return i.exported=s,t.specifiers=[this.finishNode(i,"ExportDefaultSpecifier")],k(D(e.prototype),"parseExport",this).call(this,t)}},{key:"maybeParseExportDefaultSpecifier",value:function(t){return!!(t.specifiers&&t.specifiers.length>0)||k(D(e.prototype),"maybeParseExportDefaultSpecifier",this).apply(this,arguments)}},{key:"checkExport",value:function(t){var s=t.specifiers;s&&s.length&&(t.specifiers=s.filter((function(t){return"Placeholder"===t.exported.type}))),k(D(e.prototype),"checkExport",this).call(this,t),t.specifiers=s}},{key:"parseImport",value:function(t){var s=this.parsePlaceholder("Identifier");if(!s)return k(D(e.prototype),"parseImport",this).apply(this,arguments);if(t.specifiers=[],!this.isContextual("from")&&!this.match(o.comma))return t.source=this.finishPlaceholder(s,"StringLiteral"),this.semicolon(),this.finishNode(t,"ImportDeclaration");var i=this.startNodeAtNode(s);if(i.local=s,this.finishNode(i,"ImportDefaultSpecifier"),t.specifiers.push(i),this.eat(o.comma)){var a=this.maybeParseStarImportSpecifier(t);a||this.parseNamedImportSpecifiers(t)}return this.expectContextual("from"),t.source=this.parseImportSource(),this.semicolon(),this.finishNode(t,"ImportDeclaration")}},{key:"parseImportSource",value:function(){return this.parsePlaceholder("StringLiteral")||k(D(e.prototype),"parseImportSource",this).apply(this,arguments)}}]),e}(t)}},dt=Object.keys(pt),ft={sourceType:"script",sourceFilename:void 0,startLine:1,allowAwaitOutsideFunction:!1,allowReturnOutsideFunction:!1,allowImportExportEverywhere:!1,allowSuperOutsideMethod:!1,allowUndeclaredExports:!1,plugins:[],strictMode:null,ranges:!1,tokens:!1,createParenthesizedExpressions:!1,errorRecovery:!1};var mt=function t(e,s){d(this,t),this.line=e,this.column=s},yt=function t(e,s){d(this,t),this.start=e,this.end=s};function Dt(t){return t[t.length-1]}var vt=function(t){function e(){return d(this,e),g(this,D(e).apply(this,arguments))}return y(e,t),m(e,[{key:"getLocationForPosition",value:function(t){return t===this.state.start?this.state.startLoc:t===this.state.lastTokStart?this.state.lastTokStartLoc:t===this.state.end?this.state.endLoc:t===this.state.lastTokEnd?this.state.lastTokEndLoc:function(t,e){var s,i=1,a=0;for(E.lastIndex=0;(s=E.exec(t))&&s.index<e;)i++,a=E.lastIndex;return new mt(i,e-a)}(this.input,t)}},{key:"raise",value:function(t,e){var s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=s.missingPluginNames,a=s.code,r=this.getLocationForPosition(t);e+=" (".concat(r.line,":").concat(r.column,")");var n=new SyntaxError(e);if(n.pos=t,n.loc=r,i&&(n.missingPlugin=i),void 0!==a&&(n.code=a),this.options.errorRecovery)return this.isLookahead||this.state.errors.push(n),n;throw n}}]),e}(function(t){function e(){return d(this,e),g(this,D(e).apply(this,arguments))}return y(e,t),m(e,[{key:"addComment",value:function(t){this.filename&&(t.loc.filename=this.filename),this.state.trailingComments.push(t),this.state.leadingComments.push(t)}},{key:"adjustCommentsAfterTrailingComma",value:function(t,e,s){if(0!==this.state.leadingComments.length){for(var i=null,a=e.length;null===i&&a>0;)i=e[--a];if(null!==i){for(var r=0;r<this.state.leadingComments.length;r++)this.state.leadingComments[r].end<this.state.commentPreviousNode.end&&(this.state.leadingComments.splice(r,1),r--);for(var n=[],o=0;o<this.state.leadingComments.length;o++){var h=this.state.leadingComments[o];h.end<t.end?(n.push(h),s||(this.state.leadingComments.splice(o,1),o--)):(void 0===t.trailingComments&&(t.trailingComments=[]),t.trailingComments.push(h))}s&&(this.state.leadingComments=[]),n.length>0?i.trailingComments=n:void 0!==i.trailingComments&&(i.trailingComments=[])}}}},{key:"processComment",value:function(t){if(!("Program"===t.type&&t.body.length>0)){var e,s,i,a,r,n=this.state.commentStack;if(this.state.trailingComments.length>0)this.state.trailingComments[0].start>=t.end?(i=this.state.trailingComments,this.state.trailingComments=[]):this.state.trailingComments.length=0;else if(n.length>0){var o=Dt(n);o.trailingComments&&o.trailingComments[0].start>=t.end&&(i=o.trailingComments,delete o.trailingComments)}for(n.length>0&&Dt(n).start>=t.start&&(e=n.pop());n.length>0&&Dt(n).start>=t.start;)s=n.pop();if(!s&&e&&(s=e),e)switch(t.type){case"ObjectExpression":this.adjustCommentsAfterTrailingComma(t,t.properties);break;case"ObjectPattern":this.adjustCommentsAfterTrailingComma(t,t.properties,!0);break;case"CallExpression":this.adjustCommentsAfterTrailingComma(t,t.arguments);break;case"ArrayExpression":this.adjustCommentsAfterTrailingComma(t,t.elements);break;case"ArrayPattern":this.adjustCommentsAfterTrailingComma(t,t.elements,!0)}else this.state.commentPreviousNode&&("ImportSpecifier"===this.state.commentPreviousNode.type&&"ImportSpecifier"!==t.type||"ExportSpecifier"===this.state.commentPreviousNode.type&&"ExportSpecifier"!==t.type)&&this.adjustCommentsAfterTrailingComma(t,[this.state.commentPreviousNode],!0);if(s){if(s.leadingComments)if(s!==t&&s.leadingComments.length>0&&Dt(s.leadingComments).end<=t.start)t.leadingComments=s.leadingComments,delete s.leadingComments;else for(a=s.leadingComments.length-2;a>=0;--a)if(s.leadingComments[a].end<=t.start){t.leadingComments=s.leadingComments.splice(0,a+1);break}}else if(this.state.leadingComments.length>0)if(Dt(this.state.leadingComments).end<=t.start){if(this.state.commentPreviousNode)for(r=0;r<this.state.leadingComments.length;r++)this.state.leadingComments[r].end<this.state.commentPreviousNode.end&&(this.state.leadingComments.splice(r,1),r--);this.state.leadingComments.length>0&&(t.leadingComments=this.state.leadingComments,this.state.leadingComments=[])}else{for(a=0;a<this.state.leadingComments.length&&!(this.state.leadingComments[a].end>t.start);a++);var h=this.state.leadingComments.slice(0,a);h.length&&(t.leadingComments=h),0===(i=this.state.leadingComments.slice(a)).length&&(i=null)}this.state.commentPreviousNode=t,i&&(i.length&&i[0].start>=t.start&&Dt(i).end<=t.end?t.innerComments=i:t.trailingComments=i),n.push(t)}}}]),e}(function(){function t(){d(this,t),this.sawUnambiguousESM=!1,this.ambiguousScriptDifferentAst=!1}return m(t,[{key:"hasPlugin",value:function(t){return this.plugins.has(t)}},{key:"getPluginOption",value:function(t,e){if(this.hasPlugin(t))return this.plugins.get(t)[e]}}]),t}())),xt=function(){function t(){d(this,t),this.errors=[],this.potentialArrowAt=-1,this.noArrowAt=[],this.noArrowParamsConversionAt=[],this.inParameters=!1,this.maybeInArrowParameters=!1,this.inPipeline=!1,this.inType=!1,this.noAnonFunctionType=!1,this.inPropertyName=!1,this.inClassProperty=!1,this.hasFlowComment=!1,this.isIterator=!1,this.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null},this.soloAwait=!1,this.inFSharpPipelineDirectBody=!1,this.classLevel=0,this.labels=[],this.decoratorStack=[[]],this.yieldPos=-1,this.awaitPos=-1,this.tokens=[],this.comments=[],this.trailingComments=[],this.leadingComments=[],this.commentStack=[],this.commentPreviousNode=null,this.pos=0,this.lineStart=0,this.type=o.eof,this.value=null,this.start=0,this.end=0,this.lastTokEndLoc=null,this.lastTokStartLoc=null,this.lastTokStart=0,this.lastTokEnd=0,this.context=[F.braceStatement],this.exprAllowed=!0,this.containsEsc=!1,this.containsOctal=!1,this.octalPosition=null,this.exportedIdentifiers=[],this.invalidTemplateEscapePosition=null}return m(t,[{key:"init",value:function(t){this.strict=!1!==t.strictMode&&"module"===t.sourceType,this.curLine=t.startLine,this.startLoc=this.endLoc=this.curPosition()}},{key:"curPosition",value:function(){return new mt(this.curLine,this.pos-this.lineStart)}},{key:"clone",value:function(e){for(var s=new t,i=Object.keys(this),a=0,r=i.length;a<r;a++){var n=i[a],o=this[n];!e&&Array.isArray(o)&&(o=o.slice()),s[n]=o}return s}}]),t}(),gt=function(t){return t>=48&&t<=57},kt=new Set(["g","m","s","i","y","u"]),Pt={decBinOct:[46,66,69,79,95,98,101,111],hex:[46,88,95,120]},bt={bin:[48,49]};bt.oct=[].concat(b(bt.bin),[50,51,52,53,54,55]),bt.dec=[].concat(b(bt.oct),[56,57]),bt.hex=[].concat(b(bt.dec),[65,66,67,68,69,70,97,98,99,100,101,102]);var Et=function t(e){d(this,t),this.type=e.type,this.value=e.value,this.start=e.start,this.end=e.end,this.loc=new yt(e.startLoc,e.endLoc)},Ct=/^('|")((?:\\?.)*?)\1/,At=function(t){function e(){return d(this,e),g(this,D(e).apply(this,arguments))}return y(e,t),m(e,[{key:"addExtra",value:function(t,e,s){t&&((t.extra=t.extra||{})[e]=s)}},{key:"isRelational",value:function(t){return this.match(o.relational)&&this.state.value===t}},{key:"isLookaheadRelational",value:function(t){var e=this.nextTokenStart();if(this.input.charAt(e)===t){if(e+1===this.input.length)return!0;var s=this.input.charCodeAt(e+1);return s!==t.charCodeAt(0)&&61!==s}return!1}},{key:"expectRelational",value:function(t){this.isRelational(t)?this.next():this.unexpected(null,o.relational)}},{key:"eatRelational",value:function(t){return!!this.isRelational(t)&&(this.next(),!0)}},{key:"isContextual",value:function(t){return this.match(o.name)&&this.state.value===t&&!this.state.containsEsc}},{key:"isUnparsedContextual",value:function(t,e){var s=t+e.length;return this.input.slice(t,s)===e&&(s===this.input.length||!X(this.input.charCodeAt(s)))}},{key:"isLookaheadContextual",value:function(t){var e=this.nextTokenStart();return this.isUnparsedContextual(e,t)}},{key:"eatContextual",value:function(t){return this.isContextual(t)&&this.eat(o.name)}},{key:"expectContextual",value:function(t,e){this.eatContextual(t)||this.unexpected(null,e)}},{key:"canInsertSemicolon",value:function(){return this.match(o.eof)||this.match(o.braceR)||this.hasPrecedingLineBreak()}},{key:"hasPrecedingLineBreak",value:function(){return v.test(this.input.slice(this.state.lastTokEnd,this.state.start))}},{key:"isLineTerminator",value:function(){return this.eat(o.semi)||this.canInsertSemicolon()}},{key:"semicolon",value:function(){this.isLineTerminator()||this.unexpected(null,o.semi)}},{key:"expect",value:function(t,e){this.eat(t)||this.unexpected(e,t)}},{key:"assertNoSpace",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Unexpected space.";this.state.start>this.state.lastTokEnd&&this.raise(this.state.lastTokEnd,t)}},{key:"unexpected",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Unexpected token";throw"string"!=typeof e&&(e='Unexpected token, expected "'.concat(e.label,'"')),this.raise(null!=t?t:this.state.start,e)}},{key:"expectPlugin",value:function(t,e){if(!this.hasPlugin(t))throw this.raise(null!=e?e:this.state.start,"This experimental syntax requires enabling the parser plugin: '".concat(t,"'"),{missingPluginNames:[t]});return!0}},{key:"expectOnePlugin",value:function(t,e){var s=this;if(!t.some((function(t){return s.hasPlugin(t)})))throw this.raise(null!=e?e:this.state.start,"This experimental syntax requires enabling one of the following parser plugin(s): '".concat(t.join(", "),"'"),{missingPluginNames:t})}},{key:"checkYieldAwaitInDefaultParams",value:function(){-1!==this.state.yieldPos&&(-1===this.state.awaitPos||this.state.yieldPos<this.state.awaitPos)&&this.raise(this.state.yieldPos,"Yield cannot be used as name inside a generator function"),-1!==this.state.awaitPos&&this.raise(this.state.awaitPos,"Await cannot be used as name inside an async function")}},{key:"strictDirective",value:function(t){for(;;){A.lastIndex=t,t+=A.exec(this.input)[0].length;var e=Ct.exec(this.input.slice(t));if(!e)break;if("use strict"===e[2])return!0;t+=e[0].length,A.lastIndex=t,t+=A.exec(this.input)[0].length,";"===this.input[t]&&t++}return!1}},{key:"tryParse",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state.clone(),s={node:null};try{var i=t((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;throw s.node=t,s}));if(this.state.errors.length>e.errors.length){var a=this.state;return this.state=e,{node:i,error:a.errors[e.errors.length],thrown:!1,aborted:!1,failState:a}}return{node:i,error:null,thrown:!1,aborted:!1,failState:null}}catch(t){var r=this.state;if(this.state=e,t instanceof SyntaxError)return{node:null,error:t,thrown:!0,aborted:!1,failState:r};if(t===s)return{node:s.node,error:null,thrown:!1,aborted:!0,failState:r};throw t}}}]),e}(function(t){function e(t,s){var i;return d(this,e),(i=g(this,D(e).call(this))).state=new xt,i.state.init(t),i.input=s,i.length=s.length,i.isLookahead=!1,i}return y(e,t),m(e,[{key:"next",value:function(){this.isLookahead||(this.checkKeywordEscapes(),this.options.tokens&&this.state.tokens.push(new Et(this.state))),this.state.lastTokEnd=this.state.end,this.state.lastTokStart=this.state.start,this.state.lastTokEndLoc=this.state.endLoc,this.state.lastTokStartLoc=this.state.startLoc,this.nextToken()}},{key:"eat",value:function(t){return!!this.match(t)&&(this.next(),!0)}},{key:"match",value:function(t){return this.state.type===t}},{key:"lookahead",value:function(){var t=this.state;this.state=t.clone(!0),this.isLookahead=!0,this.next(),this.isLookahead=!1;var e=this.state;return this.state=t,e}},{key:"nextTokenStart",value:function(){var t=this.state.pos;return A.lastIndex=t,t+A.exec(this.input)[0].length}},{key:"lookaheadCharCode",value:function(){return this.input.charCodeAt(this.nextTokenStart())}},{key:"setStrict",value:function(t){if(this.state.strict=t,this.match(o.num)||this.match(o.string)){for(this.state.pos=this.state.start;this.state.pos<this.state.lineStart;)this.state.lineStart=this.input.lastIndexOf("\n",this.state.lineStart-2)+1,--this.state.curLine;this.nextToken()}}},{key:"curContext",value:function(){return this.state.context[this.state.context.length-1]}},{key:"nextToken",value:function(){var t=this.curContext();t&&t.preserveSpace||this.skipSpace(),this.state.containsOctal=!1,this.state.octalPosition=null,this.state.start=this.state.pos,this.state.startLoc=this.state.curPosition(),this.state.pos>=this.length?this.finishToken(o.eof):t.override?t.override(this):this.getTokenFromCode(this.input.codePointAt(this.state.pos))}},{key:"pushComment",value:function(t,e,s,i,a,r){var n={type:t?"CommentBlock":"CommentLine",value:e,start:s,end:i,loc:new yt(a,r)};this.options.tokens&&this.state.tokens.push(n),this.state.comments.push(n),this.addComment(n)}},{key:"skipBlockComment",value:function(){var t,e=this.state.curPosition(),s=this.state.pos,i=this.input.indexOf("*/",this.state.pos+2);if(-1===i)throw this.raise(s,"Unterminated comment");for(this.state.pos=i+2,E.lastIndex=s;(t=E.exec(this.input))&&t.index<this.state.pos;)++this.state.curLine,this.state.lineStart=t.index+t[0].length;this.isLookahead||this.pushComment(!0,this.input.slice(s+2,i),s,this.state.pos,e,this.state.curPosition())}},{key:"skipLineComment",value:function(t){var e=this.state.pos,s=this.state.curPosition(),i=this.input.charCodeAt(this.state.pos+=t);if(this.state.pos<this.length)for(;!C(i)&&++this.state.pos<this.length;)i=this.input.charCodeAt(this.state.pos);this.isLookahead||this.pushComment(!1,this.input.slice(e+t,this.state.pos),e,this.state.pos,s,this.state.curPosition())}},{key:"skipSpace",value:function(){t:for(;this.state.pos<this.length;){var t=this.input.charCodeAt(this.state.pos);switch(t){case 32:case 160:case 9:++this.state.pos;break;case 13:10===this.input.charCodeAt(this.state.pos+1)&&++this.state.pos;case 10:case 8232:case 8233:++this.state.pos,++this.state.curLine,this.state.lineStart=this.state.pos;break;case 47:switch(this.input.charCodeAt(this.state.pos+1)){case 42:this.skipBlockComment();break;case 47:this.skipLineComment(2);break;default:break t}break;default:if(!w(t))break t;++this.state.pos}}}},{key:"finishToken",value:function(t,e){this.state.end=this.state.pos,this.state.endLoc=this.state.curPosition();var s=this.state.type;this.state.type=t,this.state.value=e,this.isLookahead||this.updateContext(s)}},{key:"readToken_numberSign",value:function(){if(0!==this.state.pos||!this.readToken_interpreter()){var t=this.state.pos+1,e=this.input.charCodeAt(t);if(e>=48&&e<=57)throw this.raise(this.state.pos,"Unexpected digit after hash token");if((this.hasPlugin("classPrivateProperties")||this.hasPlugin("classPrivateMethods"))&&this.state.classLevel>0)return++this.state.pos,void this.finishToken(o.hash);if("smart"!==this.getPluginOption("pipelineOperator","proposal"))throw this.raise(this.state.pos,"Unexpected character '#'");this.finishOp(o.hash,1)}}},{key:"readToken_dot",value:function(){var t=this.input.charCodeAt(this.state.pos+1);t>=48&&t<=57?this.readNumber(!0):46===t&&46===this.input.charCodeAt(this.state.pos+2)?(this.state.pos+=3,this.finishToken(o.ellipsis)):(++this.state.pos,this.finishToken(o.dot))}},{key:"readToken_slash",value:function(){if(this.state.exprAllowed&&!this.state.inType)return++this.state.pos,void this.readRegexp();61===this.input.charCodeAt(this.state.pos+1)?this.finishOp(o.assign,2):this.finishOp(o.slash,1)}},{key:"readToken_interpreter",value:function(){if(0!==this.state.pos||this.length<2)return!1;var t=this.state.pos;this.state.pos+=1;var e=this.input.charCodeAt(this.state.pos);if(33!==e)return!1;for(;!C(e)&&++this.state.pos<this.length;)e=this.input.charCodeAt(this.state.pos);var s=this.input.slice(t+2,this.state.pos);return this.finishToken(o.interpreterDirective,s),!0}},{key:"readToken_mult_modulo",value:function(t){var e=42===t?o.star:o.modulo,s=1,i=this.input.charCodeAt(this.state.pos+1),a=this.state.exprAllowed;42===t&&42===i&&(s++,i=this.input.charCodeAt(this.state.pos+2),e=o.exponent),61!==i||a||(s++,e=o.assign),this.finishOp(e,s)}},{key:"readToken_pipe_amp",value:function(t){var e=this.input.charCodeAt(this.state.pos+1);e!==t?124!==t||62!==e?61!==e?this.finishOp(124===t?o.bitwiseOR:o.bitwiseAND,1):this.finishOp(o.assign,2):this.finishOp(o.pipeline,2):61===this.input.charCodeAt(this.state.pos+2)?this.finishOp(o.assign,3):this.finishOp(124===t?o.logicalOR:o.logicalAND,2)}},{key:"readToken_caret",value:function(){61===this.input.charCodeAt(this.state.pos+1)?this.finishOp(o.assign,2):this.finishOp(o.bitwiseXOR,1)}},{key:"readToken_plus_min",value:function(t){var e=this.input.charCodeAt(this.state.pos+1);if(e===t)return 45!==e||this.inModule||62!==this.input.charCodeAt(this.state.pos+2)||0!==this.state.lastTokEnd&&!v.test(this.input.slice(this.state.lastTokEnd,this.state.pos))?void this.finishOp(o.incDec,2):(this.skipLineComment(3),this.skipSpace(),void this.nextToken());61===e?this.finishOp(o.assign,2):this.finishOp(o.plusMin,1)}},{key:"readToken_lt_gt",value:function(t){var e=this.input.charCodeAt(this.state.pos+1),s=1;return e===t?(s=62===t&&62===this.input.charCodeAt(this.state.pos+2)?3:2,61===this.input.charCodeAt(this.state.pos+s)?void this.finishOp(o.assign,s+1):void this.finishOp(o.bitShift,s)):33!==e||60!==t||this.inModule||45!==this.input.charCodeAt(this.state.pos+2)||45!==this.input.charCodeAt(this.state.pos+3)?(61===e&&(s=2),void this.finishOp(o.relational,s)):(this.skipLineComment(4),this.skipSpace(),void this.nextToken())}},{key:"readToken_eq_excl",value:function(t){var e=this.input.charCodeAt(this.state.pos+1);if(61!==e)return 61===t&&62===e?(this.state.pos+=2,void this.finishToken(o.arrow)):void this.finishOp(61===t?o.eq:o.bang,1);this.finishOp(o.equality,61===this.input.charCodeAt(this.state.pos+2)?3:2)}},{key:"readToken_question",value:function(){var t=this.input.charCodeAt(this.state.pos+1),e=this.input.charCodeAt(this.state.pos+2);63!==t||this.state.inType?46!==t||e>=48&&e<=57?(++this.state.pos,this.finishToken(o.question)):(this.state.pos+=2,this.finishToken(o.questionDot)):61===e?this.finishOp(o.assign,3):this.finishOp(o.nullishCoalescing,2)}},{key:"getTokenFromCode",value:function(t){switch(t){case 46:return void this.readToken_dot();case 40:return++this.state.pos,void this.finishToken(o.parenL);case 41:return++this.state.pos,void this.finishToken(o.parenR);case 59:return++this.state.pos,void this.finishToken(o.semi);case 44:return++this.state.pos,void this.finishToken(o.comma);case 91:return++this.state.pos,void this.finishToken(o.bracketL);case 93:return++this.state.pos,void this.finishToken(o.bracketR);case 123:return++this.state.pos,void this.finishToken(o.braceL);case 125:return++this.state.pos,void this.finishToken(o.braceR);case 58:return void(this.hasPlugin("functionBind")&&58===this.input.charCodeAt(this.state.pos+1)?this.finishOp(o.doubleColon,2):(++this.state.pos,this.finishToken(o.colon)));case 63:return void this.readToken_question();case 96:return++this.state.pos,void this.finishToken(o.backQuote);case 48:var e=this.input.charCodeAt(this.state.pos+1);if(120===e||88===e)return void this.readRadixNumber(16);if(111===e||79===e)return void this.readRadixNumber(8);if(98===e||66===e)return void this.readRadixNumber(2);case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return void this.readNumber(!1);case 34:case 39:return void this.readString(t);case 47:return void this.readToken_slash();case 37:case 42:return void this.readToken_mult_modulo(t);case 124:case 38:return void this.readToken_pipe_amp(t);case 94:return void this.readToken_caret();case 43:case 45:return void this.readToken_plus_min(t);case 60:case 62:return void this.readToken_lt_gt(t);case 61:case 33:return void this.readToken_eq_excl(t);case 126:return void this.finishOp(o.tilde,1);case 64:return++this.state.pos,void this.finishToken(o.at);case 35:return void this.readToken_numberSign();case 92:return void this.readWord();default:if(K(t))return void this.readWord()}throw this.raise(this.state.pos,"Unexpected character '".concat(String.fromCodePoint(t),"'"))}},{key:"finishOp",value:function(t,e){var s=this.input.slice(this.state.pos,this.state.pos+e);this.state.pos+=e,this.finishToken(t,s)}},{key:"readRegexp",value:function(){for(var t,e,s=this.state.pos;;){if(this.state.pos>=this.length)throw this.raise(s,"Unterminated regular expression");var i=this.input.charAt(this.state.pos);if(v.test(i))throw this.raise(s,"Unterminated regular expression");if(t)t=!1;else{if("["===i)e=!0;else if("]"===i&&e)e=!1;else if("/"===i&&!e)break;t="\\"===i}++this.state.pos}var a=this.input.slice(s,this.state.pos);++this.state.pos;for(var r="";this.state.pos<this.length;){var n=this.input[this.state.pos],h=this.input.codePointAt(this.state.pos);if(kt.has(n))r.indexOf(n)>-1&&this.raise(this.state.pos+1,"Duplicate regular expression flag");else{if(!X(h)&&92!==h)break;this.raise(this.state.pos+1,"Invalid regular expression flag")}++this.state.pos,r+=n}this.finishToken(o.regexp,{pattern:a,flags:r})}},{key:"readInt",value:function(t,e,s){for(var i=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],a=this.state.pos,r=16===t?Pt.hex:Pt.decBinOct,n=16===t?bt.hex:10===t?bt.dec:8===t?bt.oct:bt.bin,o=!1,h=0,u=0,l=null==e?1/0:e;u<l;++u){var c=this.input.charCodeAt(this.state.pos),p=void 0;if(this.hasPlugin("numericSeparator")&&95===c){var d=this.input.charCodeAt(this.state.pos-1),f=this.input.charCodeAt(this.state.pos+1);-1===n.indexOf(f)?this.raise(this.state.pos,"A numeric separator is only allowed between two digits"):(r.indexOf(d)>-1||r.indexOf(f)>-1||Number.isNaN(f))&&this.raise(this.state.pos,"A numeric separator is only allowed between two digits"),i||this.raise(this.state.pos,"Numeric separators are not allowed inside unicode escape sequences or hex escape sequences"),++this.state.pos}else{if((p=c>=97?c-97+10:c>=65?c-65+10:gt(c)?c-48:1/0)>=t)if(this.options.errorRecovery&&p<=9)p=0,this.raise(this.state.start+u+2,"Expected number in radix "+t);else{if(!s)break;p=0,o=!0}++this.state.pos,h=h*t+p}}return this.state.pos===a||null!=e&&this.state.pos-a!==e||o?null:h}},{key:"readRadixNumber",value:function(t){var e=this.state.pos,s=!1;this.state.pos+=2;var i=this.readInt(t);if(null==i&&this.raise(this.state.start+2,"Expected number in radix "+t),this.hasPlugin("bigInt")&&110===this.input.charCodeAt(this.state.pos)&&(++this.state.pos,s=!0),K(this.input.codePointAt(this.state.pos)))throw this.raise(this.state.pos,"Identifier directly after number");if(s){var a=this.input.slice(e,this.state.pos).replace(/[_n]/g,"");this.finishToken(o.bigint,a)}else this.finishToken(o.num,i)}},{key:"readNumber",value:function(t){var e=this.state.pos,s=!1,i=!1,a=!1;t||null!==this.readInt(10)||this.raise(e,"Invalid number");var r=this.state.pos-e>=2&&48===this.input.charCodeAt(e);r&&(this.state.strict&&this.raise(e,"Legacy octal literals are not allowed in strict mode"),/[89]/.test(this.input.slice(e,this.state.pos))&&(r=!1,a=!0));var n=this.input.charCodeAt(this.state.pos);if(46!==n||r||(++this.state.pos,this.readInt(10),s=!0,n=this.input.charCodeAt(this.state.pos)),69!==n&&101!==n||r||(43!==(n=this.input.charCodeAt(++this.state.pos))&&45!==n||++this.state.pos,null===this.readInt(10)&&this.raise(e,"Invalid number"),s=!0,n=this.input.charCodeAt(this.state.pos)),this.hasPlugin("numericSeparator")&&(r||a)){var h=this.input.slice(e,this.state.pos).indexOf("_");h>0&&this.raise(h+e,"Numeric separator can not be used after leading 0")}if(this.hasPlugin("bigInt")&&110===n&&((s||r||a)&&this.raise(e,"Invalid BigIntLiteral"),++this.state.pos,i=!0),K(this.input.codePointAt(this.state.pos)))throw this.raise(this.state.pos,"Identifier directly after number");var u=this.input.slice(e,this.state.pos).replace(/[_n]/g,"");if(i)this.finishToken(o.bigint,u);else{var l=r?parseInt(u,8):parseFloat(u);this.finishToken(o.num,l)}}},{key:"readCodePoint",value:function(t){var e;if(123===this.input.charCodeAt(this.state.pos)){var s=++this.state.pos;if(e=this.readHexChar(this.input.indexOf("}",this.state.pos)-this.state.pos,!0,t),++this.state.pos,null===e)--this.state.invalidTemplateEscapePosition;else if(e>1114111){if(!t)return this.state.invalidTemplateEscapePosition=s-2,null;this.raise(s,"Code point out of bounds")}}else e=this.readHexChar(4,!1,t);return e}},{key:"readString",value:function(t){for(var e="",s=++this.state.pos;;){if(this.state.pos>=this.length)throw this.raise(this.state.start,"Unterminated string constant");var i=this.input.charCodeAt(this.state.pos);if(i===t)break;if(92===i)e+=this.input.slice(s,this.state.pos),e+=this.readEscapedChar(!1),s=this.state.pos;else if(8232===i||8233===i)++this.state.pos,++this.state.curLine;else{if(C(i))throw this.raise(this.state.start,"Unterminated string constant");++this.state.pos}}e+=this.input.slice(s,this.state.pos++),this.finishToken(o.string,e)}},{key:"readTmplToken",value:function(){for(var t="",e=this.state.pos,s=!1;;){if(this.state.pos>=this.length)throw this.raise(this.state.start,"Unterminated template");var i=this.input.charCodeAt(this.state.pos);if(96===i||36===i&&123===this.input.charCodeAt(this.state.pos+1))return this.state.pos===this.state.start&&this.match(o.template)?36===i?(this.state.pos+=2,void this.finishToken(o.dollarBraceL)):(++this.state.pos,void this.finishToken(o.backQuote)):(t+=this.input.slice(e,this.state.pos),void this.finishToken(o.template,s?null:t));if(92===i){t+=this.input.slice(e,this.state.pos);var a=this.readEscapedChar(!0);null===a?s=!0:t+=a,e=this.state.pos}else if(C(i)){switch(t+=this.input.slice(e,this.state.pos),++this.state.pos,i){case 13:10===this.input.charCodeAt(this.state.pos)&&++this.state.pos;case 10:t+="\n";break;default:t+=String.fromCharCode(i)}++this.state.curLine,this.state.lineStart=this.state.pos,e=this.state.pos}else++this.state.pos}}},{key:"readEscapedChar",value:function(t){var e=!t,s=this.input.charCodeAt(++this.state.pos);switch(++this.state.pos,s){case 110:return"\n";case 114:return"\r";case 120:var i=this.readHexChar(2,!1,e);return null===i?null:String.fromCharCode(i);case 117:var a=this.readCodePoint(e);return null===a?null:String.fromCodePoint(a);case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:10===this.input.charCodeAt(this.state.pos)&&++this.state.pos;case 10:this.state.lineStart=this.state.pos,++this.state.curLine;case 8232:case 8233:return"";case 56:case 57:if(t){var r=this.state.pos-1;return this.state.invalidTemplateEscapePosition=r,null}default:if(s>=48&&s<=55){var n=this.state.pos-1,o=this.input.substr(this.state.pos-1,3).match(/^[0-7]+/)[0],h=parseInt(o,8);h>255&&(o=o.slice(0,-1),h=parseInt(o,8)),this.state.pos+=o.length-1;var u=this.input.charCodeAt(this.state.pos);if("0"!==o||56===u||57===u){if(t)return this.state.invalidTemplateEscapePosition=n,null;this.state.strict?this.raise(n,"Octal literal in strict mode"):this.state.containsOctal||(this.state.containsOctal=!0,this.state.octalPosition=n)}return String.fromCharCode(h)}return String.fromCharCode(s)}}},{key:"readHexChar",value:function(t,e,s){var i=this.state.pos,a=this.readInt(16,t,e,!1);return null===a&&(s?this.raise(i,"Bad character escape sequence"):(this.state.pos=i-1,this.state.invalidTemplateEscapePosition=i-1)),a}},{key:"readWord1",value:function(){var t="";this.state.containsEsc=!1;for(var e=this.state.pos,s=this.state.pos;this.state.pos<this.length;){var i=this.input.codePointAt(this.state.pos);if(X(i))this.state.pos+=i<=65535?1:2;else if(this.state.isIterator&&64===i)++this.state.pos;else{if(92!==i)break;this.state.containsEsc=!0,t+=this.input.slice(s,this.state.pos);var a=this.state.pos,r=this.state.pos===e?K:X;if(117!==this.input.charCodeAt(++this.state.pos)){this.raise(this.state.pos,"Expecting Unicode escape sequence \\uXXXX");continue}++this.state.pos;var n=this.readCodePoint(!0);null!==n&&(r(n)||this.raise(a,"Invalid Unicode escape"),t+=String.fromCodePoint(n)),s=this.state.pos}}return t+this.input.slice(s,this.state.pos)}},{key:"isIterator",value:function(t){return"@@iterator"===t||"@@asyncIterator"===t}},{key:"readWord",value:function(){var t=this.readWord1(),e=a.get(t)||o.name;!this.state.isIterator||this.isIterator(t)&&this.state.inType||this.raise(this.state.pos,"Invalid identifier ".concat(t)),this.finishToken(e,t)}},{key:"checkKeywordEscapes",value:function(){var t=this.state.type.keyword;t&&this.state.containsEsc&&this.raise(this.state.start,"Escape sequence in keyword ".concat(t))}},{key:"braceIsBlock",value:function(t){var e=this.curContext();return e===F.functionExpression||e===F.functionStatement||(t!==o.colon||e!==F.braceStatement&&e!==F.braceExpression?t===o._return||t===o.name&&this.state.exprAllowed?v.test(this.input.slice(this.state.lastTokEnd,this.state.start)):t===o._else||t===o.semi||t===o.eof||t===o.parenR||t===o.arrow||(t===o.braceL?e===F.braceStatement:t!==o._var&&t!==o._const&&t!==o.name&&(t===o.relational||!this.state.exprAllowed)):!e.isExpr)}},{key:"updateContext",value:function(t){var e,s=this.state.type;!s.keyword||t!==o.dot&&t!==o.questionDot?(e=s.updateContext)?e.call(this,t):this.state.exprAllowed=s.beforeExpr:this.state.exprAllowed=!1}}]),e}(vt)),wt=function(){function t(e,s,i){d(this,t),this.type="",this.start=s,this.end=0,this.loc=new yt(i),e&&e.options.ranges&&(this.range=[s,0]),e&&e.filename&&(this.loc.filename=e.filename)}return m(t,[{key:"__clone",value:function(){for(var e=new t,s=Object.keys(this),i=0,a=s.length;i<a;i++){var r=s[i];"leadingComments"!==r&&"trailingComments"!==r&&"innerComments"!==r&&(e[r]=this[r])}return e}}]),t}(),Tt={kind:"loop"},Ft={kind:"switch"},Nt=function(t){function e(t,s){var i;d(this,e),t=function(t){for(var e={},s=0,i=Object.keys(ft);s<i.length;s++){var a=i[s];e[a]=t&&null!=t[a]?t[a]:ft[a]}return e}(t);var a=(i=g(this,D(e).call(this,t,s))).getScopeHandler();return i.options=t,i.inModule="module"===i.options.sourceType,i.scope=new a(i.raise.bind(x(i)),i.inModule),i.plugins=function(t){for(var e=new Map,s=0;s<t.length;s++){var i=t[s],a=P(Array.isArray(i)?i:[i,{}],2),r=a[0],n=a[1];e.has(r)||e.set(r,n||{})}return e}(i.options.plugins),i.filename=t.sourceFilename,i}return y(e,t),m(e,[{key:"getScopeHandler",value:function(){return at}},{key:"parse",value:function(){this.scope.enter(1);var t=this.startNode(),e=this.startNode();return this.nextToken(),t.errors=null,this.parseTopLevel(t,e),t.errors=this.state.errors,t}}]),e}(function(t){function e(){return d(this,e),g(this,D(e).apply(this,arguments))}return y(e,t),m(e,[{key:"parseTopLevel",value:function(t,e){if(e.sourceType=this.options.sourceType,e.interpreter=this.parseInterpreterDirective(),this.parseBlockBody(e,!0,!0,o.eof),this.inModule&&!this.options.allowUndeclaredExports&&this.scope.undefinedExports.size>0)for(var s=0,i=Array.from(this.scope.undefinedExports);s<i.length;s++){var a=P(i[s],1)[0],r=this.scope.undefinedExports.get(a);this.raise(r,"Export '".concat(a,"' is not defined"))}return t.program=this.finishNode(e,"Program"),t.comments=this.state.comments,this.options.tokens&&(t.tokens=this.state.tokens),this.finishNode(t,"File")}},{key:"stmtToDirective",value:function(t){var e=t.expression,s=this.startNodeAt(e.start,e.loc.start),i=this.startNodeAt(t.start,t.loc.start),a=this.input.slice(e.start,e.end),r=s.value=a.slice(1,-1);return this.addExtra(s,"raw",a),this.addExtra(s,"rawValue",r),i.value=this.finishNodeAt(s,"DirectiveLiteral",e.end,e.loc.end),this.finishNodeAt(i,"Directive",t.end,t.loc.end)}},{key:"parseInterpreterDirective",value:function(){if(!this.match(o.interpreterDirective))return null;var t=this.startNode();return t.value=this.state.value,this.next(),this.finishNode(t,"InterpreterDirective")}},{key:"isLet",value:function(t){if(!this.isContextual("let"))return!1;var e=this.nextTokenStart(),s=this.input.charCodeAt(e);if(91===s)return!0;if(t)return!1;if(123===s)return!0;if(K(s)){for(var i=e+1;X(this.input.charCodeAt(i));)++i;var a=this.input.slice(e,i);if(!R.test(a))return!0}return!1}},{key:"parseStatement",value:function(t,e){return this.match(o.at)&&this.parseDecorators(!0),this.parseStatementContent(t,e)}},{key:"parseStatementContent",value:function(t,e){var s,i=this.state.type,a=this.startNode();switch(this.isLet(t)&&(i=o._var,s="let"),i){case o._break:case o._continue:return this.parseBreakContinueStatement(a,i.keyword);case o._debugger:return this.parseDebuggerStatement(a);case o._do:return this.parseDoStatement(a);case o._for:return this.parseForStatement(a);case o._function:if(46===this.lookaheadCharCode())break;return t&&(this.state.strict?this.raise(this.state.start,"In strict mode code, functions can only be declared at top level or inside a block"):"if"!==t&&"label"!==t&&this.raise(this.state.start,"In non-strict mode code, functions can only be declared at top level, inside a block, or as the body of an if statement")),this.parseFunctionStatement(a,!1,!t);case o._class:return t&&this.unexpected(),this.parseClass(a,!0);case o._if:return this.parseIfStatement(a);case o._return:return this.parseReturnStatement(a);case o._switch:return this.parseSwitchStatement(a);case o._throw:return this.parseThrowStatement(a);case o._try:return this.parseTryStatement(a);case o._const:case o._var:return s=s||this.state.value,t&&"var"!==s&&this.raise(this.state.start,"Lexical declaration cannot appear in a single-statement context"),this.parseVarStatement(a,s);case o._while:return this.parseWhileStatement(a);case o._with:return this.parseWithStatement(a);case o.braceL:return this.parseBlock();case o.semi:return this.parseEmptyStatement(a);case o._export:case o._import:var r,n=this.lookaheadCharCode();if(40===n||46===n)break;return this.options.allowImportExportEverywhere||e||this.raise(this.state.start,"'import' and 'export' may only appear at the top level"),this.next(),i===o._import?"ImportDeclaration"!==(r=this.parseImport(a)).type||r.importKind&&"value"!==r.importKind||(this.sawUnambiguousESM=!0):("ExportNamedDeclaration"!==(r=this.parseExport(a)).type||r.exportKind&&"value"!==r.exportKind)&&("ExportAllDeclaration"!==r.type||r.exportKind&&"value"!==r.exportKind)&&"ExportDefaultDeclaration"!==r.type||(this.sawUnambiguousESM=!0),this.assertModuleNodeAllowed(a),r;default:if(this.isAsyncFunction())return t&&this.raise(this.state.start,"Async functions can only be declared at the top level or inside a block"),this.next(),this.parseFunctionStatement(a,!0,!t)}var h=this.state.value,u=this.parseExpression();return i===o.name&&"Identifier"===u.type&&this.eat(o.colon)?this.parseLabeledStatement(a,h,u,t):this.parseExpressionStatement(a,u)}},{key:"assertModuleNodeAllowed",value:function(t){this.options.allowImportExportEverywhere||this.inModule||this.raise(t.start,"'import' and 'export' may appear only with 'sourceType: \"module\"'",{code:"BABEL_PARSER_SOURCETYPE_MODULE_REQUIRED"})}},{key:"takeDecorators",value:function(t){var e=this.state.decoratorStack[this.state.decoratorStack.length-1];e.length&&(t.decorators=e,this.resetStartLocationFromNode(t,e[0]),this.state.decoratorStack[this.state.decoratorStack.length-1]=[])}},{key:"canHaveLeadingDecorator",value:function(){return this.match(o._class)}},{key:"parseDecorators",value:function(t){for(var e=this.state.decoratorStack[this.state.decoratorStack.length-1];this.match(o.at);){var s=this.parseDecorator();e.push(s)}if(this.match(o._export))t||this.unexpected(),this.hasPlugin("decorators")&&!this.getPluginOption("decorators","decoratorsBeforeExport")&&this.raise(this.state.start,"Using the export keyword between a decorator and a class is not allowed. Please use `export @dec class` instead.");else if(!this.canHaveLeadingDecorator())throw this.raise(this.state.start,"Leading decorators must be attached to a class declaration")}},{key:"parseDecorator",value:function(){this.expectOnePlugin(["decorators-legacy","decorators"]);var t=this.startNode();if(this.next(),this.hasPlugin("decorators")){this.state.decoratorStack.push([]);var e,s=this.state.start,i=this.state.startLoc;if(this.eat(o.parenL))e=this.parseExpression(),this.expect(o.parenR);else for(e=this.parseIdentifier(!1);this.eat(o.dot);){var a=this.startNodeAt(s,i);a.object=e,a.property=this.parseIdentifier(!0),a.computed=!1,e=this.finishNode(a,"MemberExpression")}t.expression=this.parseMaybeDecoratorArguments(e),this.state.decoratorStack.pop()}else t.expression=this.parseExprSubscripts();return this.finishNode(t,"Decorator")}},{key:"parseMaybeDecoratorArguments",value:function(t){if(this.eat(o.parenL)){var e=this.startNodeAtNode(t);return e.callee=t,e.arguments=this.parseCallExpressionArguments(o.parenR,!1),this.toReferencedList(e.arguments),this.finishNode(e,"CallExpression")}return t}},{key:"parseBreakContinueStatement",value:function(t,e){var s="break"===e;return this.next(),this.isLineTerminator()?t.label=null:(t.label=this.parseIdentifier(),this.semicolon()),this.verifyBreakContinue(t,e),this.finishNode(t,s?"BreakStatement":"ContinueStatement")}},{key:"verifyBreakContinue",value:function(t,e){var s,i="break"===e;for(s=0;s<this.state.labels.length;++s){var a=this.state.labels[s];if(null==t.label||a.name===t.label.name){if(null!=a.kind&&(i||"loop"===a.kind))break;if(t.label&&i)break}}s===this.state.labels.length&&this.raise(t.start,"Unsyntactic "+e)}},{key:"parseDebuggerStatement",value:function(t){return this.next(),this.semicolon(),this.finishNode(t,"DebuggerStatement")}},{key:"parseHeaderExpression",value:function(){this.expect(o.parenL);var t=this.parseExpression();return this.expect(o.parenR),t}},{key:"parseDoStatement",value:function(t){var e=this;return this.next(),this.state.labels.push(Tt),t.body=this.withTopicForbiddingContext((function(){return e.parseStatement("do")})),this.state.labels.pop(),this.expect(o._while),t.test=this.parseHeaderExpression(),this.eat(o.semi),this.finishNode(t,"DoWhileStatement")}},{key:"parseForStatement",value:function(t){this.next(),this.state.labels.push(Tt);var e=-1;if(this.isAwaitAllowed()&&this.eatContextual("await")&&(e=this.state.lastTokStart),this.scope.enter(0),this.expect(o.parenL),this.match(o.semi))return e>-1&&this.unexpected(e),this.parseFor(t,null);var s=this.isLet();if(this.match(o._var)||this.match(o._const)||s){var i=this.startNode(),a=s?"let":this.state.value;return this.next(),this.parseVar(i,!0,a),this.finishNode(i,"VariableDeclaration"),(this.match(o._in)||this.isContextual("of"))&&1===i.declarations.length?this.parseForIn(t,i,e):(e>-1&&this.unexpected(e),this.parseFor(t,i))}var r={start:0},n=this.parseExpression(!0,r);if(this.match(o._in)||this.isContextual("of")){var h=this.isContextual("of")?"for-of statement":"for-in statement";return this.toAssignable(n,void 0,h),this.checkLVal(n,void 0,void 0,h),this.parseForIn(t,n,e)}return r.start&&this.unexpected(r.start),e>-1&&this.unexpected(e),this.parseFor(t,n)}},{key:"parseFunctionStatement",value:function(t,e,s){return this.next(),this.parseFunction(t,1|(s?0:2),e)}},{key:"parseIfStatement",value:function(t){return this.next(),t.test=this.parseHeaderExpression(),t.consequent=this.parseStatement("if"),t.alternate=this.eat(o._else)?this.parseStatement("if"):null,this.finishNode(t,"IfStatement")}},{key:"parseReturnStatement",value:function(t){return this.scope.inFunction||this.options.allowReturnOutsideFunction||this.raise(this.state.start,"'return' outside of function"),this.next(),this.isLineTerminator()?t.argument=null:(t.argument=this.parseExpression(),this.semicolon()),this.finishNode(t,"ReturnStatement")}},{key:"parseSwitchStatement",value:function(t){this.next(),t.discriminant=this.parseHeaderExpression();var e,s,i=t.cases=[];for(this.expect(o.braceL),this.state.labels.push(Ft),this.scope.enter(0);!this.match(o.braceR);)if(this.match(o._case)||this.match(o._default)){var a=this.match(o._case);e&&this.finishNode(e,"SwitchCase"),i.push(e=this.startNode()),e.consequent=[],this.next(),a?e.test=this.parseExpression():(s&&this.raise(this.state.lastTokStart,"Multiple default clauses"),s=!0,e.test=null),this.expect(o.colon)}else e?e.consequent.push(this.parseStatement(null)):this.unexpected();return this.scope.exit(),e&&this.finishNode(e,"SwitchCase"),this.next(),this.state.labels.pop(),this.finishNode(t,"SwitchStatement")}},{key:"parseThrowStatement",value:function(t){return this.next(),v.test(this.input.slice(this.state.lastTokEnd,this.state.start))&&this.raise(this.state.lastTokEnd,"Illegal newline after throw"),t.argument=this.parseExpression(),this.semicolon(),this.finishNode(t,"ThrowStatement")}},{key:"parseTryStatement",value:function(t){var e=this;if(this.next(),t.block=this.parseBlock(),t.handler=null,this.match(o._catch)){var s=this.startNode();if(this.next(),this.match(o.parenL)){this.expect(o.parenL),s.param=this.parseBindingAtom();var i="Identifier"===s.param.type;this.scope.enter(i?32:0),this.checkLVal(s.param,9,null,"catch clause"),this.expect(o.parenR)}else s.param=null,this.scope.enter(0);s.body=this.withTopicForbiddingContext((function(){return e.parseBlock(!1,!1)})),this.scope.exit(),t.handler=this.finishNode(s,"CatchClause")}return t.finalizer=this.eat(o._finally)?this.parseBlock():null,t.handler||t.finalizer||this.raise(t.start,"Missing catch or finally clause"),this.finishNode(t,"TryStatement")}},{key:"parseVarStatement",value:function(t,e){return this.next(),this.parseVar(t,!1,e),this.semicolon(),this.finishNode(t,"VariableDeclaration")}},{key:"parseWhileStatement",value:function(t){var e=this;return this.next(),t.test=this.parseHeaderExpression(),this.state.labels.push(Tt),t.body=this.withTopicForbiddingContext((function(){return e.parseStatement("while")})),this.state.labels.pop(),this.finishNode(t,"WhileStatement")}},{key:"parseWithStatement",value:function(t){var e=this;return this.state.strict&&this.raise(this.state.start,"'with' in strict mode"),this.next(),t.object=this.parseHeaderExpression(),t.body=this.withTopicForbiddingContext((function(){return e.parseStatement("with")})),this.finishNode(t,"WithStatement")}},{key:"parseEmptyStatement",value:function(t){return this.next(),this.finishNode(t,"EmptyStatement")}},{key:"parseLabeledStatement",value:function(t,e,s,i){for(var a=0,r=this.state.labels;a<r.length;a++){r[a].name===e&&this.raise(s.start,"Label '".concat(e,"' is already declared"))}for(var n=this.state.type.isLoop?"loop":this.match(o._switch)?"switch":null,h=this.state.labels.length-1;h>=0;h--){var u=this.state.labels[h];if(u.statementStart!==t.start)break;u.statementStart=this.state.start,u.kind=n}return this.state.labels.push({name:e,kind:n,statementStart:this.state.start}),t.body=this.parseStatement(i?-1===i.indexOf("label")?i+"label":i:"label"),this.state.labels.pop(),t.label=s,this.finishNode(t,"LabeledStatement")}},{key:"parseExpressionStatement",value:function(t,e){return t.expression=e,this.semicolon(),this.finishNode(t,"ExpressionStatement")}},{key:"parseBlock",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],s=this.startNode();return this.expect(o.braceL),e&&this.scope.enter(0),this.parseBlockBody(s,t,!1,o.braceR),e&&this.scope.exit(),this.finishNode(s,"BlockStatement")}},{key:"isValidDirective",value:function(t){return"ExpressionStatement"===t.type&&"StringLiteral"===t.expression.type&&!t.expression.extra.parenthesized}},{key:"parseBlockBody",value:function(t,e,s,i){var a=t.body=[],r=t.directives=[];this.parseBlockOrModuleBlockBody(a,e?r:void 0,s,i)}},{key:"parseBlockOrModuleBlockBody",value:function(t,e,s,i){for(var a,r,n=!1;!this.eat(i);){n||!this.state.containsOctal||r||(r=this.state.octalPosition);var o=this.parseStatement(null,s);if(e&&!n&&this.isValidDirective(o)){var h=this.stmtToDirective(o);e.push(h),void 0===a&&"use strict"===h.value.value&&(a=this.state.strict,this.setStrict(!0),r&&this.raise(r,"Octal literal in strict mode"))}else n=!0,t.push(o)}!1===a&&this.setStrict(!1)}},{key:"parseFor",value:function(t,e){var s=this;return t.init=e,this.expect(o.semi),t.test=this.match(o.semi)?null:this.parseExpression(),this.expect(o.semi),t.update=this.match(o.parenR)?null:this.parseExpression(),this.expect(o.parenR),t.body=this.withTopicForbiddingContext((function(){return s.parseStatement("for")})),this.scope.exit(),this.state.labels.pop(),this.finishNode(t,"ForStatement")}},{key:"parseForIn",value:function(t,e,s){var i=this,a=this.match(o._in);return this.next(),a?s>-1&&this.unexpected(s):t.await=s>-1,"VariableDeclaration"!==e.type||null==e.declarations[0].init||a&&!this.state.strict&&"var"===e.kind&&"Identifier"===e.declarations[0].id.type?"AssignmentPattern"===e.type&&this.raise(e.start,"Invalid left-hand side in for-loop"):this.raise(e.start,"".concat(a?"for-in":"for-of"," loop variable declaration may not have an initializer")),t.left=e,t.right=a?this.parseExpression():this.parseMaybeAssign(),this.expect(o.parenR),t.body=this.withTopicForbiddingContext((function(){return i.parseStatement("for")})),this.scope.exit(),this.state.labels.pop(),this.finishNode(t,a?"ForInStatement":"ForOfStatement")}},{key:"parseVar",value:function(t,e,s){var i=t.declarations=[],a=this.hasPlugin("typescript");for(t.kind=s;;){var r=this.startNode();if(this.parseVarId(r,s),this.eat(o.eq)?r.init=this.parseMaybeAssign(e):("const"!==s||this.match(o._in)||this.isContextual("of")?"Identifier"===r.id.type||e&&(this.match(o._in)||this.isContextual("of"))||this.raise(this.state.lastTokEnd,"Complex binding patterns require an initialization value"):a||this.unexpected(),r.init=null),i.push(this.finishNode(r,"VariableDeclarator")),!this.eat(o.comma))break}return t}},{key:"parseVarId",value:function(t,e){t.id=this.parseBindingAtom(),this.checkLVal(t.id,"var"===e?5:9,void 0,"variable declaration","var"!==e)}},{key:"parseFunction",value:function(t){var e=this,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=1&s,r=2&s,n=!(!a||4&s);this.initFunction(t,i),this.match(o.star)&&r&&this.raise(this.state.start,"Generators can only be declared at the top level or inside a block"),t.generator=this.eat(o.star),a&&(t.id=this.parseFunctionId(n));var h=this.state.maybeInArrowParameters,u=this.state.inClassProperty,l=this.state.yieldPos,c=this.state.awaitPos;return this.state.maybeInArrowParameters=!1,this.state.inClassProperty=!1,this.state.yieldPos=-1,this.state.awaitPos=-1,this.scope.enter(p(t.async,t.generator)),a||(t.id=this.parseFunctionId()),this.parseFunctionParams(t),this.withTopicForbiddingContext((function(){e.parseFunctionBodyAndFinish(t,a?"FunctionDeclaration":"FunctionExpression")})),this.scope.exit(),a&&!r&&this.registerFunctionStatementId(t),this.state.maybeInArrowParameters=h,this.state.inClassProperty=u,this.state.yieldPos=l,this.state.awaitPos=c,t}},{key:"parseFunctionId",value:function(t){return t||this.match(o.name)?this.parseIdentifier():null}},{key:"parseFunctionParams",value:function(t,e){var s=this.state.inParameters;this.state.inParameters=!0,this.expect(o.parenL),t.params=this.parseBindingList(o.parenR,41,!1,e),this.state.inParameters=s,this.checkYieldAwaitInDefaultParams()}},{key:"registerFunctionStatementId",value:function(t){t.id&&this.scope.declareName(t.id.name,this.state.strict||t.generator||t.async?this.scope.treatFunctionsAsVar?5:9:17,t.id.start)}},{key:"parseClass",value:function(t,e,s){this.next(),this.takeDecorators(t);var i=this.state.strict;return this.state.strict=!0,this.parseClassId(t,e,s),this.parseClassSuper(t),t.body=this.parseClassBody(!!t.superClass),this.state.strict=i,this.finishNode(t,e?"ClassDeclaration":"ClassExpression")}},{key:"isClassProperty",value:function(){return this.match(o.eq)||this.match(o.semi)||this.match(o.braceR)}},{key:"isClassMethod",value:function(){return this.match(o.parenL)}},{key:"isNonstaticConstructor",value:function(t){return!(t.computed||t.static||"constructor"!==t.key.name&&"constructor"!==t.key.value)}},{key:"parseClassBody",value:function(t){var e=this;this.state.classLevel++;var s={hadConstructor:!1},i=[],a=this.startNode();if(a.body=[],this.expect(o.braceL),this.withTopicForbiddingContext((function(){for(;!e.eat(o.braceR);)if(e.eat(o.semi)){if(i.length>0)throw e.raise(e.state.lastTokEnd,"Decorators must not be followed by a semicolon")}else if(e.match(o.at))i.push(e.parseDecorator());else{var r=e.startNode();i.length&&(r.decorators=i,e.resetStartLocationFromNode(r,i[0]),i=[]),e.parseClassMember(a,r,s,t),"constructor"===r.kind&&r.decorators&&r.decorators.length>0&&e.raise(r.start,"Decorators can't be used with a constructor. Did you mean '@dec class { ... }'?")}})),i.length)throw this.raise(this.state.start,"You have trailing decorators with no method");return this.state.classLevel--,this.finishNode(a,"ClassBody")}},{key:"parseClassMember",value:function(t,e,s,i){var a=!1,r=this.state.containsEsc;if(this.match(o.name)&&"static"===this.state.value){var n=this.parseIdentifier(!0);if(this.isClassMethod()){var h=e;return h.kind="method",h.computed=!1,h.key=n,h.static=!1,void this.pushClassMethod(t,h,!1,!1,!1,!1)}if(this.isClassProperty()){var u=e;return u.computed=!1,u.key=n,u.static=!1,void t.body.push(this.parseClassProperty(u))}if(r)throw this.unexpected();a=!0}this.parseClassMemberWithIsStatic(t,e,s,a,i)}},{key:"parseClassMemberWithIsStatic",value:function(t,e,s,i,a){var r=e,n=e,h=e,u=e,l=r,c=r;if(e.static=i,this.eat(o.star))return l.kind="method",this.parseClassPropertyName(l),"PrivateName"===l.key.type?void this.pushClassPrivateMethod(t,n,!0,!1):(this.isNonstaticConstructor(r)&&this.raise(r.key.start,"Constructor can't be a generator"),void this.pushClassMethod(t,r,!0,!1,!1,!1));var p=this.state.containsEsc,d=this.parseClassPropertyName(e),f="PrivateName"===d.type,m="Identifier"===d.type,y=this.state.start;if(this.parsePostMemberNameModifiers(c),this.isClassMethod()){if(l.kind="method",f)return void this.pushClassPrivateMethod(t,n,!1,!1);var D=this.isNonstaticConstructor(r),v=!1;D&&(r.kind="constructor",s.hadConstructor&&!this.hasPlugin("typescript")&&this.raise(d.start,"Duplicate constructor in the same class"),s.hadConstructor=!0,v=a),this.pushClassMethod(t,r,!1,!1,D,v)}else if(this.isClassProperty())f?this.pushClassPrivateProperty(t,u):this.pushClassProperty(t,h);else if(!m||"async"!==d.name||p||this.isLineTerminator())!m||"get"!==d.name&&"set"!==d.name||p||this.match(o.star)&&this.isLineTerminator()?this.isLineTerminator()?f?this.pushClassPrivateProperty(t,u):this.pushClassProperty(t,h):this.unexpected():(l.kind=d.name,this.parseClassPropertyName(r),"PrivateName"===l.key.type?this.pushClassPrivateMethod(t,n,!1,!1):(this.isNonstaticConstructor(r)&&this.raise(r.key.start,"Constructor can't have get/set modifier"),this.pushClassMethod(t,r,!1,!1,!1,!1)),this.checkGetterSetterParams(r));else{var x=this.eat(o.star);c.optional&&this.unexpected(y),l.kind="method",this.parseClassPropertyName(l),"PrivateName"===l.key.type?this.pushClassPrivateMethod(t,n,x,!0):(this.isNonstaticConstructor(r)&&this.raise(r.key.start,"Constructor can't be an async function"),this.pushClassMethod(t,r,x,!0,!1,!1))}}},{key:"parseClassPropertyName",value:function(t){var e=this.parsePropertyName(t);return t.computed||!t.static||"prototype"!==e.name&&"prototype"!==e.value||this.raise(e.start,"Classes may not have static property named prototype"),"PrivateName"===e.type&&"constructor"===e.id.name&&this.raise(e.start,"Classes may not have a private field named '#constructor'"),e}},{key:"pushClassProperty",value:function(t,e){e.computed||"constructor"!==e.key.name&&"constructor"!==e.key.value||this.raise(e.key.start,"Classes may not have a field named 'constructor'"),t.body.push(this.parseClassProperty(e))}},{key:"pushClassPrivateProperty",value:function(t,e){this.expectPlugin("classPrivateProperties",e.key.start),t.body.push(this.parseClassPrivateProperty(e))}},{key:"pushClassMethod",value:function(t,e,s,i,a,r){t.body.push(this.parseMethod(e,s,i,a,r,"ClassMethod",!0))}},{key:"pushClassPrivateMethod",value:function(t,e,s,i){this.expectPlugin("classPrivateMethods",e.key.start),t.body.push(this.parseMethod(e,s,i,!1,!1,"ClassPrivateMethod",!0))}},{key:"parsePostMemberNameModifiers",value:function(t){}},{key:"parseAccessModifier",value:function(){}},{key:"parseClassPrivateProperty",value:function(t){return this.state.inClassProperty=!0,this.scope.enter(320),t.value=this.eat(o.eq)?this.parseMaybeAssign():null,this.semicolon(),this.state.inClassProperty=!1,this.scope.exit(),this.finishNode(t,"ClassPrivateProperty")}},{key:"parseClassProperty",value:function(t){return t.typeAnnotation||this.expectPlugin("classProperties"),this.state.inClassProperty=!0,this.scope.enter(320),this.match(o.eq)?(this.expectPlugin("classProperties"),this.next(),t.value=this.parseMaybeAssign()):t.value=null,this.semicolon(),this.state.inClassProperty=!1,this.scope.exit(),this.finishNode(t,"ClassProperty")}},{key:"parseClassId",value:function(t,e,s){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:139;this.match(o.name)?(t.id=this.parseIdentifier(),e&&this.checkLVal(t.id,i,void 0,"class name")):s||!e?t.id=null:this.unexpected(null,"A class name is required")}},{key:"parseClassSuper",value:function(t){t.superClass=this.eat(o._extends)?this.parseExprSubscripts():null}},{key:"parseExport",value:function(t){var e=this.maybeParseExportDefaultSpecifier(t),s=!e||this.eat(o.comma),i=s&&this.eatExportStar(t),a=i&&this.maybeParseExportNamespaceSpecifier(t),r=s&&(!a||this.eat(o.comma)),n=e||i;if(i&&!a)return e&&this.unexpected(),this.parseExportFrom(t,!0),this.finishNode(t,"ExportAllDeclaration");var h,u=this.maybeParseExportNamedSpecifiers(t);if(e&&s&&!i&&!u||a&&r&&!u)throw this.unexpected(null,o.braceL);if(n||u?(h=!1,this.parseExportFrom(t,n)):h=this.maybeParseExportDeclaration(t),n||u||h)return this.checkExport(t,!0,!1,!!t.source),this.finishNode(t,"ExportNamedDeclaration");if(this.eat(o._default))return t.declaration=this.parseExportDefaultExpression(),this.checkExport(t,!0,!0),this.finishNode(t,"ExportDefaultDeclaration");throw this.unexpected(null,o.braceL)}},{key:"eatExportStar",value:function(t){return this.eat(o.star)}},{key:"maybeParseExportDefaultSpecifier",value:function(t){if(this.isExportDefaultSpecifier()){this.expectPlugin("exportDefaultFrom");var e=this.startNode();return e.exported=this.parseIdentifier(!0),t.specifiers=[this.finishNode(e,"ExportDefaultSpecifier")],!0}return!1}},{key:"maybeParseExportNamespaceSpecifier",value:function(t){if(this.isContextual("as")){t.specifiers||(t.specifiers=[]);var e=this.startNodeAt(this.state.lastTokStart,this.state.lastTokStartLoc);return this.next(),e.exported=this.parseIdentifier(!0),t.specifiers.push(this.finishNode(e,"ExportNamespaceSpecifier")),!0}return!1}},{key:"maybeParseExportNamedSpecifiers",value:function(t){var e;return!!this.match(o.braceL)&&(t.specifiers||(t.specifiers=[]),(e=t.specifiers).push.apply(e,b(this.parseExportSpecifiers())),t.source=null,t.declaration=null,!0)}},{key:"maybeParseExportDeclaration",value:function(t){if(this.shouldParseExportDeclaration()){if(this.isContextual("async")){var e=this.nextTokenStart();this.isUnparsedContextual(e,"function")||this.unexpected(e,'Unexpected token, expected "function"')}return t.specifiers=[],t.source=null,t.declaration=this.parseExportDeclaration(t),!0}return!1}},{key:"isAsyncFunction",value:function(){if(!this.isContextual("async"))return!1;var t=this.nextTokenStart();return!v.test(this.input.slice(this.state.pos,t))&&this.isUnparsedContextual(t,"function")}},{key:"parseExportDefaultExpression",value:function(){var t=this.startNode(),e=this.isAsyncFunction();if(this.match(o._function)||e)return this.next(),e&&this.next(),this.parseFunction(t,5,e);if(this.match(o._class))return this.parseClass(t,!0,!0);if(this.match(o.at))return this.hasPlugin("decorators")&&this.getPluginOption("decorators","decoratorsBeforeExport")&&this.raise(this.state.start,"Decorators must be placed *before* the 'export' keyword. You can set the 'decoratorsBeforeExport' option to false to use the 'export @decorator class {}' syntax"),this.parseDecorators(!1),this.parseClass(t,!0,!0);if(this.match(o._const)||this.match(o._var)||this.isLet())throw this.raise(this.state.start,"Only expressions, functions or classes are allowed as the `default` export.");var s=this.parseMaybeAssign();return this.semicolon(),s}},{key:"parseExportDeclaration",value:function(t){return this.parseStatement(null)}},{key:"isExportDefaultSpecifier",value:function(){if(this.match(o.name))return"async"!==this.state.value&&"let"!==this.state.value;if(!this.match(o._default))return!1;var t=this.nextTokenStart();return 44===this.input.charCodeAt(t)||this.isUnparsedContextual(t,"from")}},{key:"parseExportFrom",value:function(t,e){this.eatContextual("from")?(t.source=this.parseImportSource(),this.checkExport(t)):e?this.unexpected():t.source=null,this.semicolon()}},{key:"shouldParseExportDeclaration",value:function(){if(this.match(o.at)&&(this.expectOnePlugin(["decorators","decorators-legacy"]),this.hasPlugin("decorators"))){if(!this.getPluginOption("decorators","decoratorsBeforeExport"))return!0;this.unexpected(this.state.start,"Decorators must be placed *before* the 'export' keyword. You can set the 'decoratorsBeforeExport' option to false to use the 'export @decorator class {}' syntax")}return"var"===this.state.type.keyword||"const"===this.state.type.keyword||"function"===this.state.type.keyword||"class"===this.state.type.keyword||this.isLet()||this.isAsyncFunction()}},{key:"checkExport",value:function(t,e,s,i){if(e)if(s)this.checkDuplicateExports(t,"default");else if(t.specifiers&&t.specifiers.length)for(var a=0,r=t.specifiers;a<r.length;a++){var n=r[a];this.checkDuplicateExports(n,n.exported.name),!i&&n.local&&(this.checkReservedWord(n.local.name,n.local.start,!0,!1),this.scope.checkLocalExport(n.local))}else if(t.declaration)if("FunctionDeclaration"===t.declaration.type||"ClassDeclaration"===t.declaration.type){var o=t.declaration.id;if(!o)throw new Error("Assertion failure");this.checkDuplicateExports(t,o.name)}else if("VariableDeclaration"===t.declaration.type)for(var h=0,u=t.declaration.declarations;h<u.length;h++){var l=u[h];this.checkDeclaration(l.id)}if(this.state.decoratorStack[this.state.decoratorStack.length-1].length){var c=t.declaration&&("ClassDeclaration"===t.declaration.type||"ClassExpression"===t.declaration.type);if(!t.declaration||!c)throw this.raise(t.start,"You can only use decorators on an export when exporting a class");this.takeDecorators(t.declaration)}}},{key:"checkDeclaration",value:function(t){if("Identifier"===t.type)this.checkDuplicateExports(t,t.name);else if("ObjectPattern"===t.type)for(var e=0,s=t.properties;e<s.length;e++){var i=s[e];this.checkDeclaration(i)}else if("ArrayPattern"===t.type)for(var a=0,r=t.elements;a<r.length;a++){var n=r[a];n&&this.checkDeclaration(n)}else"ObjectProperty"===t.type?this.checkDeclaration(t.value):"RestElement"===t.type?this.checkDeclaration(t.argument):"AssignmentPattern"===t.type&&this.checkDeclaration(t.left)}},{key:"checkDuplicateExports",value:function(t,e){this.state.exportedIdentifiers.indexOf(e)>-1&&this.raise(t.start,"default"===e?"Only one default export allowed per module.":"`".concat(e,"` has already been exported. Exported identifiers must be unique.")),this.state.exportedIdentifiers.push(e)}},{key:"parseExportSpecifiers",value:function(){var t=[],e=!0;for(this.expect(o.braceL);!this.eat(o.braceR);){if(e)e=!1;else if(this.expect(o.comma),this.eat(o.braceR))break;var s=this.startNode();s.local=this.parseIdentifier(!0),s.exported=this.eatContextual("as")?this.parseIdentifier(!0):s.local.__clone(),t.push(this.finishNode(s,"ExportSpecifier"))}return t}},{key:"parseImport",value:function(t){if(t.specifiers=[],!this.match(o.string)){var e=!this.maybeParseDefaultImportSpecifier(t)||this.eat(o.comma),s=e&&this.maybeParseStarImportSpecifier(t);e&&!s&&this.parseNamedImportSpecifiers(t),this.expectContextual("from")}return t.source=this.parseImportSource(),this.semicolon(),this.finishNode(t,"ImportDeclaration")}},{key:"parseImportSource",value:function(){return this.match(o.string)||this.unexpected(),this.parseExprAtom()}},{key:"shouldParseDefaultImport",value:function(t){return this.match(o.name)}},{key:"parseImportSpecifierLocal",value:function(t,e,s,i){e.local=this.parseIdentifier(),this.checkLVal(e.local,9,void 0,i),t.specifiers.push(this.finishNode(e,s))}},{key:"maybeParseDefaultImportSpecifier",value:function(t){return!!this.shouldParseDefaultImport(t)&&(this.parseImportSpecifierLocal(t,this.startNode(),"ImportDefaultSpecifier","default import specifier"),!0)}},{key:"maybeParseStarImportSpecifier",value:function(t){if(this.match(o.star)){var e=this.startNode();return this.next(),this.expectContextual("as"),this.parseImportSpecifierLocal(t,e,"ImportNamespaceSpecifier","import namespace specifier"),!0}return!1}},{key:"parseNamedImportSpecifiers",value:function(t){var e=!0;for(this.expect(o.braceL);!this.eat(o.braceR);){if(e)e=!1;else{if(this.eat(o.colon))throw this.raise(this.state.start,"ES2015 named imports do not destructure. Use another statement for destructuring after the import.");if(this.expect(o.comma),this.eat(o.braceR))break}this.parseImportSpecifier(t)}}},{key:"parseImportSpecifier",value:function(t){var e=this.startNode();e.imported=this.parseIdentifier(!0),this.eatContextual("as")?e.local=this.parseIdentifier():(this.checkReservedWord(e.imported.name,e.start,!0,!0),e.local=e.imported.__clone()),this.checkLVal(e.local,9,void 0,"import specifier"),t.specifiers.push(this.finishNode(e,"ImportSpecifier"))}}]),e}(function(t){function e(){return d(this,e),g(this,D(e).apply(this,arguments))}return y(e,t),m(e,[{key:"checkDuplicatedProto",value:function(t,e){if(!("SpreadElement"===t.type||t.computed||t.kind||t.shorthand)){var s=t.key;"__proto__"===("Identifier"===s.type?s.name:String(s.value))&&(e.used&&!e.start&&(e.start=s.start),e.used=!0)}}},{key:"getExpression",value:function(){this.scope.enter(1),this.nextToken();var t=this.parseExpression();return this.match(o.eof)||this.unexpected(),t.comments=this.state.comments,t.errors=this.state.errors,t}},{key:"parseExpression",value:function(t,e){var s=this.state.start,i=this.state.startLoc,a=this.parseMaybeAssign(t,e);if(this.match(o.comma)){var r=this.startNodeAt(s,i);for(r.expressions=[a];this.eat(o.comma);)r.expressions.push(this.parseMaybeAssign(t,e));return this.toReferencedList(r.expressions),this.finishNode(r,"SequenceExpression")}return a}},{key:"parseMaybeAssign",value:function(t,e,s,i){var a,r=this.state.start,n=this.state.startLoc;if(this.isContextual("yield")){if(this.scope.inGenerator){var h=this.parseYield(t);return s&&(h=s.call(this,h,r,n)),h}this.state.exprAllowed=!1}e?a=!1:(e={start:0},a=!0),(this.match(o.parenL)||this.match(o.name))&&(this.state.potentialArrowAt=this.state.start);var u=this.parseMaybeConditional(t,e,i);if(s&&(u=s.call(this,u,r,n)),this.state.type.isAssign){var l=this.startNodeAt(r,n),c=this.state.value;l.operator=c,"??="===c&&(this.expectPlugin("nullishCoalescingOperator"),this.expectPlugin("logicalAssignment")),"||="!==c&&"&&="!==c||this.expectPlugin("logicalAssignment"),l.left=this.match(o.eq)?this.toAssignable(u,void 0,"assignment expression"):u,e.start>=l.left.start&&(e.start=0),this.checkLVal(u,void 0,void 0,"assignment expression");var p,d=function t(e){return"ParenthesizedExpression"===e.type?t(e.expression):e}(u);return"ObjectPattern"===d.type?p="`({a}) = 0` use `({a} = 0)`":"ArrayPattern"===d.type&&(p="`([a]) = 0` use `([a] = 0)`"),p&&(u.extra&&u.extra.parenthesized||"ParenthesizedExpression"===u.type)&&this.raise(d.start,"You're trying to assign to a parenthesized expression, eg. instead of ".concat(p)),this.next(),l.right=this.parseMaybeAssign(t),this.finishNode(l,"AssignmentExpression")}return a&&e.start&&this.unexpected(e.start),u}},{key:"parseMaybeConditional",value:function(t,e,s){var i=this.state.start,a=this.state.startLoc,r=this.state.potentialArrowAt,n=this.parseExprOps(t,e);return"ArrowFunctionExpression"===n.type&&n.start===r?n:e&&e.start?n:this.parseConditional(n,t,i,a,s)}},{key:"parseConditional",value:function(t,e,s,i,a){if(this.eat(o.question)){var r=this.startNodeAt(s,i);return r.test=t,r.consequent=this.parseMaybeAssign(),this.expect(o.colon),r.alternate=this.parseMaybeAssign(e),this.finishNode(r,"ConditionalExpression")}return t}},{key:"parseExprOps",value:function(t,e){var s=this.state.start,i=this.state.startLoc,a=this.state.potentialArrowAt,r=this.parseMaybeUnary(e);return"ArrowFunctionExpression"===r.type&&r.start===a?r:e&&e.start?r:this.parseExprOp(r,s,i,-1,t)}},{key:"parseExprOp",value:function(t,e,s,i,a){var r=this.state.type.binop;if(!(null==r||a&&this.match(o._in))&&r>i){var n=this.state.value;if("|>"===n&&this.state.inFSharpPipelineDirectBody)return t;var h=this.startNodeAt(e,s);h.left=t,h.operator=n,"**"!==n||"UnaryExpression"!==t.type||!this.options.createParenthesizedExpressions&&t.extra&&t.extra.parenthesized||this.raise(t.argument.start,"Illegal expression. Wrap left hand side or entire exponentiation in parentheses.");var u=this.state.type;if(u===o.pipeline?(this.expectPlugin("pipelineOperator"),this.state.inPipeline=!0,this.checkPipelineAtInfixOperator(t,e)):u===o.nullishCoalescing&&this.expectPlugin("nullishCoalescingOperator"),this.next(),u===o.pipeline&&"minimal"===this.getPluginOption("pipelineOperator","proposal")&&this.match(o.name)&&"await"===this.state.value&&this.scope.inAsync)throw this.raise(this.state.start,'Unexpected "await" after pipeline body; await must have parentheses in minimal proposal');if(h.right=this.parseExprOpRightExpr(u,r,a),u===o.nullishCoalescing){if(!("LogicalExpression"!==t.type||"??"===t.operator||t.extra&&t.extra.parenthesized))throw this.raise(t.start,"Nullish coalescing operator(??) requires parens when mixing with logical operators");if(!("LogicalExpression"!==h.right.type||"??"===h.right.operator||h.right.extra&&h.right.extra.parenthesized))throw this.raise(h.right.start,"Nullish coalescing operator(??) requires parens when mixing with logical operators")}return this.finishNode(h,u===o.logicalOR||u===o.logicalAND||u===o.nullishCoalescing?"LogicalExpression":"BinaryExpression"),this.parseExprOp(h,e,s,i,a)}return t}},{key:"parseExprOpRightExpr",value:function(t,e,s){var i=this,a=this.state.start,r=this.state.startLoc;switch(t){case o.pipeline:switch(this.getPluginOption("pipelineOperator","proposal")){case"smart":return this.withTopicPermittingContext((function(){return i.parseSmartPipelineBody(i.parseExprOpBaseRightExpr(t,e,s),a,r)}));case"fsharp":return this.withSoloAwaitPermittingContext((function(){return i.parseFSharpPipelineBody(e,s)}))}default:return this.parseExprOpBaseRightExpr(t,e,s)}}},{key:"parseExprOpBaseRightExpr",value:function(t,e,s){var i=this.state.start,a=this.state.startLoc;return this.parseExprOp(this.parseMaybeUnary(),i,a,t.rightAssociative?e-1:e,s)}},{key:"parseMaybeUnary",value:function(t){if(this.isContextual("await")&&this.isAwaitAllowed())return this.parseAwait();if(this.state.type.prefix){var e=this.startNode(),s=this.match(o.incDec);if(e.operator=this.state.value,e.prefix=!0,"throw"===e.operator&&this.expectPlugin("throwExpressions"),this.next(),e.argument=this.parseMaybeUnary(),t&&t.start&&this.unexpected(t.start),s)this.checkLVal(e.argument,void 0,void 0,"prefix operation");else if(this.state.strict&&"delete"===e.operator){var i=e.argument;"Identifier"===i.type?this.raise(e.start,"Deleting local variable in strict mode"):"MemberExpression"===i.type&&"PrivateName"===i.property.type&&this.raise(e.start,"Deleting a private field is not allowed")}return this.finishNode(e,s?"UpdateExpression":"UnaryExpression")}var a=this.state.start,r=this.state.startLoc,n=this.parseExprSubscripts(t);if(t&&t.start)return n;for(;this.state.type.postfix&&!this.canInsertSemicolon();){var h=this.startNodeAt(a,r);h.operator=this.state.value,h.prefix=!1,h.argument=n,this.checkLVal(n,void 0,void 0,"postfix operation"),this.next(),n=this.finishNode(h,"UpdateExpression")}return n}},{key:"parseExprSubscripts",value:function(t){var e=this.state.start,s=this.state.startLoc,i=this.state.potentialArrowAt,a=this.parseExprAtom(t);return"ArrowFunctionExpression"===a.type&&a.start===i?a:t&&t.start?a:this.parseSubscripts(a,e,s)}},{key:"parseSubscripts",value:function(t,e,s,i){var a={optionalChainMember:!1,maybeAsyncArrow:this.atPossibleAsync(t),stop:!1};do{t=this.parseSubscript(t,e,s,i,a),a.maybeAsyncArrow=!1}while(!a.stop);return t}},{key:"parseSubscript",value:function(t,e,s,i,a){if(!i&&this.eat(o.doubleColon)){var r=this.startNodeAt(e,s);return r.object=t,r.callee=this.parseNoCallExpr(),a.stop=!0,this.parseSubscripts(this.finishNode(r,"BindExpression"),e,s,i)}if(this.match(o.questionDot)){if(this.expectPlugin("optionalChaining"),a.optionalChainMember=!0,i&&40===this.lookaheadCharCode())return a.stop=!0,t;this.next();var n=this.startNodeAt(e,s);return this.eat(o.bracketL)?(n.object=t,n.property=this.parseExpression(),n.computed=!0,n.optional=!0,this.expect(o.bracketR),this.finishNode(n,"OptionalMemberExpression")):this.eat(o.parenL)?(n.callee=t,n.arguments=this.parseCallExpressionArguments(o.parenR,!1),n.optional=!0,this.finishCallExpression(n,!0)):(n.object=t,n.property=this.parseIdentifier(!0),n.computed=!1,n.optional=!0,this.finishNode(n,"OptionalMemberExpression"))}if(this.eat(o.dot)){var h=this.startNodeAt(e,s);return h.object=t,h.property=this.parseMaybePrivateName(),h.computed=!1,"PrivateName"===h.property.type&&"Super"===h.object.type&&this.raise(e,"Private fields can't be accessed on super"),a.optionalChainMember?(h.optional=!1,this.finishNode(h,"OptionalMemberExpression")):this.finishNode(h,"MemberExpression")}if(this.eat(o.bracketL)){var u=this.startNodeAt(e,s);return u.object=t,u.property=this.parseExpression(),u.computed=!0,this.expect(o.bracketR),a.optionalChainMember?(u.optional=!1,this.finishNode(u,"OptionalMemberExpression")):this.finishNode(u,"MemberExpression")}if(!i&&this.match(o.parenL)){var l=this.state.maybeInArrowParameters,c=this.state.yieldPos,p=this.state.awaitPos;this.state.maybeInArrowParameters=!0,this.state.yieldPos=-1,this.state.awaitPos=-1,this.next();var d=this.startNodeAt(e,s);return d.callee=t,d.arguments=this.parseCallExpressionArguments(o.parenR,a.maybeAsyncArrow,"Import"===t.type,"Super"!==t.type,d),this.finishCallExpression(d,a.optionalChainMember),a.maybeAsyncArrow&&this.shouldParseAsyncArrow()?(a.stop=!0,d=this.parseAsyncArrowFromCallExpression(this.startNodeAt(e,s),d),this.checkYieldAwaitInDefaultParams(),this.state.yieldPos=c,this.state.awaitPos=p):(this.toReferencedListDeep(d.arguments),-1!==c&&(this.state.yieldPos=c),(this.isAwaitAllowed()||l)&&-1===p||(this.state.awaitPos=p)),this.state.maybeInArrowParameters=l,d}return this.match(o.backQuote)?this.parseTaggedTemplateExpression(e,s,t,a):(a.stop=!0,t)}},{key:"parseTaggedTemplateExpression",value:function(t,e,s,i,a){var r=this.startNodeAt(t,e);return r.tag=s,r.quasi=this.parseTemplate(!0),a&&(r.typeParameters=a),i.optionalChainMember&&this.raise(t,"Tagged Template Literals are not allowed in optionalChain"),this.finishNode(r,"TaggedTemplateExpression")}},{key:"atPossibleAsync",value:function(t){return"Identifier"===t.type&&"async"===t.name&&this.state.lastTokEnd===t.end&&!this.canInsertSemicolon()&&"async"===this.input.slice(t.start,t.end)}},{key:"finishCallExpression",value:function(t,e){if("Import"===t.callee.type)if(1!==t.arguments.length)this.raise(t.start,"import() requires exactly one argument");else{var s=t.arguments[0];s&&"SpreadElement"===s.type&&this.raise(s.start,"... is not allowed in import()")}return this.finishNode(t,e?"OptionalCallExpression":"CallExpression")}},{key:"parseCallExpressionArguments",value:function(t,e,s,i,a){var r,n=[],h=!0,u=this.state.inFSharpPipelineDirectBody;for(this.state.inFSharpPipelineDirectBody=!1;!this.eat(t);){if(h)h=!1;else if(this.expect(o.comma),this.match(t)){s&&this.raise(this.state.lastTokStart,"Trailing comma is disallowed inside import(...) arguments"),a&&this.addExtra(a,"trailingComma",this.state.lastTokStart),this.next();break}this.match(o.parenL)&&!r&&(r=this.state.start),n.push(this.parseExprListItem(!1,e?{start:0}:void 0,e?{start:0}:void 0,i))}return e&&r&&this.shouldParseAsyncArrow()&&this.unexpected(),this.state.inFSharpPipelineDirectBody=u,n}},{key:"shouldParseAsyncArrow",value:function(){return this.match(o.arrow)&&!this.canInsertSemicolon()}},{key:"parseAsyncArrowFromCallExpression",value:function(t,e){var s;return this.expect(o.arrow),this.parseArrowExpression(t,e.arguments,!0,null===(s=e.extra)||void 0===s?void 0:s.trailingComma),t}},{key:"parseNoCallExpr",value:function(){var t=this.state.start,e=this.state.startLoc;return this.parseSubscripts(this.parseExprAtom(),t,e,!0)}},{key:"parseExprAtom",value:function(t){this.state.type===o.slash&&this.readRegexp();var e,s=this.state.potentialArrowAt===this.state.start;switch(this.state.type){case o._super:return e=this.startNode(),this.next(),!this.match(o.parenL)||this.scope.allowDirectSuper||this.options.allowSuperOutsideMethod?this.scope.allowSuper||this.options.allowSuperOutsideMethod||this.raise(e.start,"super is only allowed in object methods and classes"):this.raise(e.start,"super() is only valid inside a class constructor of a subclass. Maybe a typo in the method name ('constructor') or not extending another class?"),this.match(o.parenL)||this.match(o.bracketL)||this.match(o.dot)||this.raise(e.start,"super can only be used with function calls (i.e. super()) or in property accesses (i.e. super.prop or super[prop])"),this.finishNode(e,"Super");case o._import:return e=this.startNode(),this.next(),this.match(o.dot)?this.parseImportMetaProperty(e):(this.expectPlugin("dynamicImport",e.start),this.match(o.parenL)||this.unexpected(null,o.parenL),this.finishNode(e,"Import"));case o._this:return e=this.startNode(),this.next(),this.finishNode(e,"ThisExpression");case o.name:e=this.startNode();var i=this.state.containsEsc,a=this.parseIdentifier();if(!i&&"async"===a.name&&this.match(o._function)&&!this.canInsertSemicolon())return this.next(),this.parseFunction(e,void 0,!0);if(s&&!i&&"async"===a.name&&this.match(o.name)&&!this.canInsertSemicolon()){var r=[this.parseIdentifier()];return this.expect(o.arrow),this.parseArrowExpression(e,r,!0),e}return s&&this.match(o.arrow)&&!this.canInsertSemicolon()?(this.next(),this.parseArrowExpression(e,[a],!1),e):a;case o._do:this.expectPlugin("doExpressions");var n=this.startNode();this.next();var h=this.state.labels;return this.state.labels=[],n.body=this.parseBlock(),this.state.labels=h,this.finishNode(n,"DoExpression");case o.regexp:var u=this.state.value;return(e=this.parseLiteral(u.value,"RegExpLiteral")).pattern=u.pattern,e.flags=u.flags,e;case o.num:return this.parseLiteral(this.state.value,"NumericLiteral");case o.bigint:return this.parseLiteral(this.state.value,"BigIntLiteral");case o.string:return this.parseLiteral(this.state.value,"StringLiteral");case o._null:return e=this.startNode(),this.next(),this.finishNode(e,"NullLiteral");case o._true:case o._false:return this.parseBooleanLiteral();case o.parenL:return this.parseParenAndDistinguishExpression(s);case o.bracketL:var l=this.state.inFSharpPipelineDirectBody;return this.state.inFSharpPipelineDirectBody=!1,e=this.startNode(),this.next(),e.elements=this.parseExprList(o.bracketR,!0,t,e),this.state.maybeInArrowParameters||this.toReferencedList(e.elements),this.state.inFSharpPipelineDirectBody=l,this.finishNode(e,"ArrayExpression");case o.braceL:var c=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!1;var p=this.parseObj(!1,t);return this.state.inFSharpPipelineDirectBody=c,p;case o._function:return this.parseFunctionExpression();case o.at:this.parseDecorators();case o._class:return e=this.startNode(),this.takeDecorators(e),this.parseClass(e,!1);case o._new:return this.parseNew();case o.backQuote:return this.parseTemplate(!1);case o.doubleColon:e=this.startNode(),this.next(),e.object=null;var d=e.callee=this.parseNoCallExpr();if("MemberExpression"===d.type)return this.finishNode(e,"BindExpression");throw this.raise(d.start,"Binding should be performed on object property.");case o.hash:if(this.state.inPipeline)return e=this.startNode(),"smart"!==this.getPluginOption("pipelineOperator","proposal")&&this.raise(e.start,"Primary Topic Reference found but pipelineOperator not passed 'smart' for 'proposal' option."),this.next(),this.primaryTopicReferenceIsAllowedInCurrentTopicContext()||this.raise(e.start,"Topic reference was used in a lexical context without topic binding"),this.registerTopicReference(),this.finishNode(e,"PipelinePrimaryTopicReference");default:throw this.unexpected()}}},{key:"parseBooleanLiteral",value:function(){var t=this.startNode();return t.value=this.match(o._true),this.next(),this.finishNode(t,"BooleanLiteral")}},{key:"parseMaybePrivateName",value:function(){if(this.match(o.hash)){this.expectOnePlugin(["classPrivateProperties","classPrivateMethods"]);var t=this.startNode();return this.next(),this.assertNoSpace("Unexpected space between # and identifier"),t.id=this.parseIdentifier(!0),this.finishNode(t,"PrivateName")}return this.parseIdentifier(!0)}},{key:"parseFunctionExpression",value:function(){var t=this.startNode(),e=this.startNode();return this.next(),e=this.createIdentifier(e,"function"),this.scope.inGenerator&&this.eat(o.dot)?this.parseMetaProperty(t,e,"sent"):this.parseFunction(t)}},{key:"parseMetaProperty",value:function(t,e,s){t.meta=e,"function"===e.name&&"sent"===s&&(this.isContextual(s)?this.expectPlugin("functionSent"):this.hasPlugin("functionSent")||this.unexpected());var i=this.state.containsEsc;return t.property=this.parseIdentifier(!0),(t.property.name!==s||i)&&this.raise(t.property.start,"The only valid meta property for ".concat(e.name," is ").concat(e.name,".").concat(s)),this.finishNode(t,"MetaProperty")}},{key:"parseImportMetaProperty",value:function(t){var e=this.createIdentifier(this.startNodeAtNode(t),"import");return this.expect(o.dot),this.isContextual("meta")?(this.expectPlugin("importMeta"),this.inModule||this.raise(e.start,"import.meta may appear only with 'sourceType: \"module\"'",{code:"BABEL_PARSER_SOURCETYPE_MODULE_REQUIRED"}),this.sawUnambiguousESM=!0):this.hasPlugin("importMeta")||this.raise(e.start,"Dynamic imports require a parameter: import('a.js')"),this.parseMetaProperty(t,e,"meta")}},{key:"parseLiteral",value:function(t,e,s,i){s=s||this.state.start,i=i||this.state.startLoc;var a=this.startNodeAt(s,i);return this.addExtra(a,"rawValue",t),this.addExtra(a,"raw",this.input.slice(s,this.state.end)),a.value=t,this.next(),this.finishNode(a,e)}},{key:"parseParenAndDistinguishExpression",value:function(t){var e,s=this.state.start,i=this.state.startLoc;this.expect(o.parenL);var a=this.state.maybeInArrowParameters,r=this.state.yieldPos,n=this.state.awaitPos,h=this.state.inFSharpPipelineDirectBody;this.state.maybeInArrowParameters=!0,this.state.yieldPos=-1,this.state.awaitPos=-1,this.state.inFSharpPipelineDirectBody=!1;for(var u,l,c=this.state.start,p=this.state.startLoc,d=[],f={start:0},m={start:0},y=!0;!this.match(o.parenR);){if(y)y=!1;else if(this.expect(o.comma,m.start||null),this.match(o.parenR)){l=this.state.start;break}if(this.match(o.ellipsis)){var D=this.state.start,v=this.state.startLoc;u=this.state.start,d.push(this.parseParenItem(this.parseRestBinding(),D,v)),this.checkCommaAfterRest(41);break}d.push(this.parseMaybeAssign(!1,f,this.parseParenItem,m))}var x=this.state.start,g=this.state.startLoc;this.expect(o.parenR),this.state.maybeInArrowParameters=a,this.state.inFSharpPipelineDirectBody=h;var k=this.startNodeAt(s,i);if(t&&this.shouldParseArrow()&&(k=this.parseArrow(k))){this.checkYieldAwaitInDefaultParams(),this.state.yieldPos=r,this.state.awaitPos=n;for(var P=0;P<d.length;P++){var b=d[P];b.extra&&b.extra.parenthesized&&this.unexpected(b.extra.parenStart)}return this.parseArrowExpression(k,d,!1),k}if(-1!==r&&(this.state.yieldPos=r),-1!==n&&(this.state.awaitPos=n),d.length||this.unexpected(this.state.lastTokStart),l&&this.unexpected(l),u&&this.unexpected(u),f.start&&this.unexpected(f.start),m.start&&this.unexpected(m.start),this.toReferencedListDeep(d,!0),d.length>1?((e=this.startNodeAt(c,p)).expressions=d,this.finishNodeAt(e,"SequenceExpression",x,g)):e=d[0],!this.options.createParenthesizedExpressions)return this.addExtra(e,"parenthesized",!0),this.addExtra(e,"parenStart",s),e;var E=this.startNodeAt(s,i);return E.expression=e,this.finishNode(E,"ParenthesizedExpression"),E}},{key:"shouldParseArrow",value:function(){return!this.canInsertSemicolon()}},{key:"parseArrow",value:function(t){if(this.eat(o.arrow))return t}},{key:"parseParenItem",value:function(t,e,s){return t}},{key:"parseNew",value:function(){var t=this.startNode(),e=this.startNode();if(this.next(),e=this.createIdentifier(e,"new"),this.eat(o.dot)){var s=this.parseMetaProperty(t,e,"target");if(!this.scope.inNonArrowFunction&&!this.state.inClassProperty){var i="new.target can only be used in functions";this.hasPlugin("classProperties")&&(i+=" or class properties"),this.raise(s.start,i)}return s}return t.callee=this.parseNoCallExpr(),"Import"===t.callee.type?this.raise(t.callee.start,"Cannot use new with import(...)"):"OptionalMemberExpression"===t.callee.type||"OptionalCallExpression"===t.callee.type?this.raise(this.state.lastTokEnd,"constructors in/after an Optional Chain are not allowed"):this.eat(o.questionDot)&&this.raise(this.state.start,"constructors in/after an Optional Chain are not allowed"),this.parseNewArguments(t),this.finishNode(t,"NewExpression")}},{key:"parseNewArguments",value:function(t){if(this.eat(o.parenL)){var e=this.parseExprList(o.parenR);this.toReferencedList(e),t.arguments=e}else t.arguments=[]}},{key:"parseTemplateElement",value:function(t){var e=this.startNode();return null===this.state.value&&(t?this.state.invalidTemplateEscapePosition=null:this.raise(this.state.invalidTemplateEscapePosition||0,"Invalid escape sequence in template")),e.value={raw:this.input.slice(this.state.start,this.state.end).replace(/\r\n?/g,"\n"),cooked:this.state.value},this.next(),e.tail=this.match(o.backQuote),this.finishNode(e,"TemplateElement")}},{key:"parseTemplate",value:function(t){var e=this.startNode();this.next(),e.expressions=[];var s=this.parseTemplateElement(t);for(e.quasis=[s];!s.tail;)this.expect(o.dollarBraceL),e.expressions.push(this.parseExpression()),this.expect(o.braceR),e.quasis.push(s=this.parseTemplateElement(t));return this.next(),this.finishNode(e,"TemplateLiteral")}},{key:"parseObj",value:function(t,e){var s=Object.create(null),i=!0,a=this.startNode();for(a.properties=[],this.next();!this.eat(o.braceR);){if(i)i=!1;else if(this.expect(o.comma),this.match(o.braceR)){this.addExtra(a,"trailingComma",this.state.lastTokStart),this.next();break}var r=this.parseObjectMember(t,e);t||this.checkDuplicatedProto(r,s),r.shorthand&&this.addExtra(r,"shorthand",!0),a.properties.push(r)}return this.match(o.eq)||void 0===s.start||this.raise(s.start,"Redefinition of __proto__ property"),this.finishNode(a,t?"ObjectPattern":"ObjectExpression")}},{key:"isAsyncProp",value:function(t){return!t.computed&&"Identifier"===t.key.type&&"async"===t.key.name&&(this.match(o.name)||this.match(o.num)||this.match(o.string)||this.match(o.bracketL)||this.state.type.keyword||this.match(o.star))&&!this.hasPrecedingLineBreak()}},{key:"parseObjectMember",value:function(t,e){var s=[];if(this.match(o.at))for(this.hasPlugin("decorators")&&this.raise(this.state.start,"Stage 2 decorators disallow object literal property decorators");this.match(o.at);)s.push(this.parseDecorator());var i,a,r=this.startNode(),n=!1,h=!1;if(this.match(o.ellipsis))return s.length&&this.unexpected(),t?(this.next(),r.argument=this.parseIdentifier(),this.checkCommaAfterRest(125),this.finishNode(r,"RestElement")):this.parseSpread();s.length&&(r.decorators=s,s=[]),r.method=!1,(t||e)&&(i=this.state.start,a=this.state.startLoc),t||(n=this.eat(o.star));var u=this.state.containsEsc;return this.parsePropertyName(r),t||u||n||!this.isAsyncProp(r)?h=!1:(h=!0,n=this.eat(o.star),this.parsePropertyName(r)),this.parseObjPropValue(r,i,a,n,h,t,e,u),r}},{key:"isGetterOrSetterMethod",value:function(t,e){return!e&&!t.computed&&"Identifier"===t.key.type&&("get"===t.key.name||"set"===t.key.name)&&(this.match(o.string)||this.match(o.num)||this.match(o.bracketL)||this.match(o.name)||!!this.state.type.keyword)}},{key:"getGetterSetterExpectedParamCount",value:function(t){return"get"===t.kind?0:1}},{key:"checkGetterSetterParams",value:function(t){var e=this.getGetterSetterExpectedParamCount(t),s=t.start;t.params.length!==e&&("get"===t.kind?this.raise(s,"getter must not have any formal parameters"):this.raise(s,"setter must have exactly one formal parameter")),"set"===t.kind&&"RestElement"===t.params[t.params.length-1].type&&this.raise(s,"setter function argument must not be a rest parameter")}},{key:"parseObjectMethod",value:function(t,e,s,i,a){return s||e||this.match(o.parenL)?(i&&this.unexpected(),t.kind="method",t.method=!0,this.parseMethod(t,e,s,!1,!1,"ObjectMethod")):!a&&this.isGetterOrSetterMethod(t,i)?((e||s)&&this.unexpected(),t.kind=t.key.name,this.parsePropertyName(t),this.parseMethod(t,!1,!1,!1,!1,"ObjectMethod"),this.checkGetterSetterParams(t),t):void 0}},{key:"parseObjectProperty",value:function(t,e,s,i,a){return t.shorthand=!1,this.eat(o.colon)?(t.value=i?this.parseMaybeDefault(this.state.start,this.state.startLoc):this.parseMaybeAssign(!1,a),this.finishNode(t,"ObjectProperty")):t.computed||"Identifier"!==t.key.type?void 0:(this.checkReservedWord(t.key.name,t.key.start,!0,!0),i?t.value=this.parseMaybeDefault(e,s,t.key.__clone()):this.match(o.eq)&&a?(a.start||(a.start=this.state.start),t.value=this.parseMaybeDefault(e,s,t.key.__clone())):t.value=t.key.__clone(),t.shorthand=!0,this.finishNode(t,"ObjectProperty"))}},{key:"parseObjPropValue",value:function(t,e,s,i,a,r,n,o){var h=this.parseObjectMethod(t,i,a,r,o)||this.parseObjectProperty(t,e,s,r,n);return h||this.unexpected(),h}},{key:"parsePropertyName",value:function(t){if(this.eat(o.bracketL))t.computed=!0,t.key=this.parseMaybeAssign(),this.expect(o.bracketR);else{var e=this.state.inPropertyName;this.state.inPropertyName=!0,t.key=this.match(o.num)||this.match(o.string)?this.parseExprAtom():this.parseMaybePrivateName(),"PrivateName"!==t.key.type&&(t.computed=!1),this.state.inPropertyName=e}return t.key}},{key:"initFunction",value:function(t,e){t.id=null,t.generator=!1,t.async=!!e}},{key:"parseMethod",value:function(t,e,s,i,a,r){var n=arguments.length>6&&void 0!==arguments[6]&&arguments[6],o=this.state.yieldPos,h=this.state.awaitPos;this.state.yieldPos=-1,this.state.awaitPos=-1,this.initFunction(t,s),t.generator=!!e;var u=i;return this.scope.enter(64|p(s,t.generator)|(n?256:0)|(a?128:0)),this.parseFunctionParams(t,u),this.checkYieldAwaitInDefaultParams(),this.parseFunctionBodyAndFinish(t,r,!0),this.scope.exit(),this.state.yieldPos=o,this.state.awaitPos=h,t}},{key:"parseArrowExpression",value:function(t,e,s,i){this.scope.enter(16|p(s,!1)),this.initFunction(t,s);var a=this.state.maybeInArrowParameters,r=this.state.yieldPos,n=this.state.awaitPos;return this.state.maybeInArrowParameters=!1,this.state.yieldPos=-1,this.state.awaitPos=-1,e&&this.setArrowFunctionParameters(t,e,i),this.parseFunctionBody(t,!0),this.scope.exit(),this.state.maybeInArrowParameters=a,this.state.yieldPos=r,this.state.awaitPos=n,this.finishNode(t,"ArrowFunctionExpression")}},{key:"setArrowFunctionParameters",value:function(t,e,s){t.params=this.toAssignableList(e,!0,"arrow function parameters",s)}},{key:"isStrictBody",value:function(t){if("BlockStatement"===t.body.type&&t.body.directives.length)for(var e=0,s=t.body.directives;e<s.length;e++){if("use strict"===s[e].value.value)return!0}return!1}},{key:"parseFunctionBodyAndFinish",value:function(t,e){var s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.parseFunctionBody(t,!1,s),this.finishNode(t,e)}},{key:"parseFunctionBody",value:function(t,e){var s=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=e&&!this.match(o.braceL),a=this.state.strict,r=!1,n=this.state.inParameters;if(this.state.inParameters=!1,i)t.body=this.parseMaybeAssign(),this.checkParams(t,!1,e,!1);else{var h=!this.isSimpleParamList(t.params);if((!a||h)&&(r=this.strictDirective(this.state.end))&&h){var u="method"!==t.kind&&"constructor"!==t.kind||!t.key?t.start:t.key.end;this.raise(u,"Illegal 'use strict' directive in function with non-simple parameter list")}var l=this.state.labels;this.state.labels=[],r&&(this.state.strict=!0),this.checkParams(t,!(a||r||e||s||h),e,!a&&r),t.body=this.parseBlock(!0,!1),this.state.labels=l}this.state.inParameters=n,this.state.strict&&t.id&&this.checkLVal(t.id,65,void 0,"function name",void 0,!a&&r),this.state.strict=a}},{key:"isSimpleParamList",value:function(t){for(var e=0,s=t.length;e<s;e++)if("Identifier"!==t[e].type)return!1;return!0}},{key:"checkParams",value:function(t,e,s){for(var i=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],a=Object.create(null),r=0;r<t.params.length;r++)this.checkLVal(t.params[r],5,e?null:a,"function parameter list",void 0,i)}},{key:"parseExprList",value:function(t,e,s,i){for(var a=[],r=!0;!this.eat(t);){if(r)r=!1;else if(this.expect(o.comma),this.match(t)){i&&this.addExtra(i,"trailingComma",this.state.lastTokStart),this.next();break}a.push(this.parseExprListItem(e,s))}return a}},{key:"parseExprListItem",value:function(t,e,s,i){var a;if(t&&this.match(o.comma))a=null;else if(this.match(o.ellipsis)){var r=this.state.start,n=this.state.startLoc;a=this.parseParenItem(this.parseSpread(e,s),r,n)}else if(this.match(o.question)){this.expectPlugin("partialApplication"),i||this.raise(this.state.start,"Unexpected argument placeholder");var h=this.startNode();this.next(),a=this.finishNode(h,"ArgumentPlaceholder")}else a=this.parseMaybeAssign(!1,e,this.parseParenItem,s);return a}},{key:"parseIdentifier",value:function(t){var e=this.startNode(),s=this.parseIdentifierName(e.start,t);return this.createIdentifier(e,s)}},{key:"createIdentifier",value:function(t,e){return t.name=e,t.loc.identifierName=e,this.finishNode(t,"Identifier")}},{key:"parseIdentifierName",value:function(t,e){var s;if(this.match(o.name))s=this.state.value;else{if(!this.state.type.keyword)throw this.unexpected();"class"!==(s=this.state.type.keyword)&&"function"!==s||this.state.lastTokEnd===this.state.lastTokStart+1&&46===this.input.charCodeAt(this.state.lastTokStart)||this.state.context.pop()}return e?this.state.type=o.name:this.checkReservedWord(s,this.state.start,!!this.state.type.keyword,!1),this.next(),s}},{key:"checkReservedWord",value:function(t,e,s,i){if(this.scope.inGenerator&&"yield"===t)this.raise(e,"Can not use 'yield' as identifier inside a generator");else{if("await"===t){if(this.scope.inAsync)return void this.raise(e,"Can not use 'await' as identifier inside an async function");-1===this.state.awaitPos&&(this.state.maybeInArrowParameters||this.isAwaitAllowed())&&(this.state.awaitPos=this.state.start)}if(this.state.inClassProperty&&"arguments"===t)this.raise(e,"'arguments' is not allowed in class field initializer");else if(s&&function(t){return a.has(t)}(t))this.raise(e,"Unexpected keyword '".concat(t,"'"));else(this.state.strict?i?M:B:L)(t,this.inModule)&&(this.scope.inAsync||"await"!==t?this.raise(e,"Unexpected reserved word '".concat(t,"'")):this.raise(e,"Can not use keyword 'await' outside an async function"))}}},{key:"isAwaitAllowed",value:function(){return this.scope.inFunction?this.scope.inAsync:!!this.options.allowAwaitOutsideFunction||!!this.hasPlugin("topLevelAwait")&&this.inModule}},{key:"parseAwait",value:function(){var t=this.startNode();return this.next(),this.state.inParameters?this.raise(t.start,"await is not allowed in async function parameters"):-1===this.state.awaitPos&&(this.state.awaitPos=t.start),this.eat(o.star)&&this.raise(t.start,"await* has been removed from the async functions proposal. Use Promise.all() instead."),this.scope.inFunction||this.options.allowAwaitOutsideFunction||(this.hasPrecedingLineBreak()||this.match(o.plusMin)||this.match(o.parenL)||this.match(o.bracketL)||this.match(o.backQuote)||this.match(o.regexp)||this.match(o.slash)||this.hasPlugin("v8intrinsic")&&this.match(o.modulo)?this.ambiguousScriptDifferentAst=!0:this.sawUnambiguousESM=!0),this.state.soloAwait||(t.argument=this.parseMaybeUnary()),this.finishNode(t,"AwaitExpression")}},{key:"parseYield",value:function(t){var e=this.startNode();return this.state.inParameters?this.raise(e.start,"yield is not allowed in generator parameters"):-1===this.state.yieldPos&&(this.state.yieldPos=e.start),this.next(),this.match(o.semi)||!this.match(o.star)&&!this.state.type.startsExpr||this.hasPrecedingLineBreak()?(e.delegate=!1,e.argument=null):(e.delegate=this.eat(o.star),e.argument=this.parseMaybeAssign(t)),this.finishNode(e,"YieldExpression")}},{key:"checkPipelineAtInfixOperator",value:function(t,e){"smart"===this.getPluginOption("pipelineOperator","proposal")&&"SequenceExpression"===t.type&&this.raise(e,"Pipeline head should not be a comma-separated sequence expression")}},{key:"parseSmartPipelineBody",value:function(t,e,s){var i=this.checkSmartPipelineBodyStyle(t);return this.checkSmartPipelineBodyEarlyErrors(t,i,e),this.parseSmartPipelineBodyInStyle(t,i,e,s)}},{key:"checkSmartPipelineBodyEarlyErrors",value:function(t,e,s){if(this.match(o.arrow))throw this.raise(this.state.start,'Unexpected arrow "=>" after pipeline body; arrow function in pipeline body must be parenthesized');"PipelineTopicExpression"===e&&"SequenceExpression"===t.type&&this.raise(s,"Pipeline body may not be a comma-separated sequence expression")}},{key:"parseSmartPipelineBodyInStyle",value:function(t,e,s,i){var a=this.startNodeAt(s,i);switch(e){case"PipelineBareFunction":a.callee=t;break;case"PipelineBareConstructor":a.callee=t.callee;break;case"PipelineBareAwaitedFunction":a.callee=t.argument;break;case"PipelineTopicExpression":this.topicReferenceWasUsedInCurrentTopicContext()||this.raise(s,"Pipeline is in topic style but does not use topic reference"),a.expression=t;break;default:throw new Error("Internal @babel/parser error: Unknown pipeline style (".concat(e,")"))}return this.finishNode(a,e)}},{key:"checkSmartPipelineBodyStyle",value:function(t){return t.type,this.isSimpleReference(t)?"PipelineBareFunction":"PipelineTopicExpression"}},{key:"isSimpleReference",value:function(t){switch(t.type){case"MemberExpression":return!t.computed&&this.isSimpleReference(t.object);case"Identifier":return!0;default:return!1}}},{key:"withTopicPermittingContext",value:function(t){var e=this.state.topicContext;this.state.topicContext={maxNumOfResolvableTopics:1,maxTopicIndex:null};try{return t()}finally{this.state.topicContext=e}}},{key:"withTopicForbiddingContext",value:function(t){var e=this.state.topicContext;this.state.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null};try{return t()}finally{this.state.topicContext=e}}},{key:"withSoloAwaitPermittingContext",value:function(t){var e=this.state.soloAwait;this.state.soloAwait=!0;try{return t()}finally{this.state.soloAwait=e}}},{key:"registerTopicReference",value:function(){this.state.topicContext.maxTopicIndex=0}},{key:"primaryTopicReferenceIsAllowedInCurrentTopicContext",value:function(){return this.state.topicContext.maxNumOfResolvableTopics>=1}},{key:"topicReferenceWasUsedInCurrentTopicContext",value:function(){return null!=this.state.topicContext.maxTopicIndex&&this.state.topicContext.maxTopicIndex>=0}},{key:"parseFSharpPipelineBody",value:function(t,e){var s=this.state.start,i=this.state.startLoc;this.state.potentialArrowAt=this.state.start;var a=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!0;var r=this.parseExprOp(this.parseMaybeUnary(),s,i,t,e);return this.state.inFSharpPipelineDirectBody=a,r}}]),e}(function(t){function e(){return d(this,e),g(this,D(e).apply(this,arguments))}return y(e,t),m(e,[{key:"toAssignable",value:function(t,e,s){var i;if(t)switch(t.type){case"Identifier":case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":break;case"ObjectExpression":t.type="ObjectPattern";for(var a=0,r=t.properties.length,n=r-1;a<r;a++){var o,h=t.properties[a],u=a===n;this.toAssignableObjectExpressionProp(h,e,u),u&&"RestElement"===h.type&&(null===(o=t.extra)||void 0===o?void 0:o.trailingComma)&&this.raiseRestNotLast(t.extra.trailingComma)}break;case"ObjectProperty":this.toAssignable(t.value,e,s);break;case"SpreadElement":this.checkToRestConversion(t),t.type="RestElement";var l=t.argument;this.toAssignable(l,e,s);break;case"ArrayExpression":t.type="ArrayPattern",this.toAssignableList(t.elements,e,s,null===(i=t.extra)||void 0===i?void 0:i.trailingComma);break;case"AssignmentExpression":"="!==t.operator&&this.raise(t.left.end,"Only '=' operator can be used for specifying default value."),t.type="AssignmentPattern",delete t.operator,this.toAssignable(t.left,e,s);break;case"ParenthesizedExpression":t.expression=this.toAssignable(t.expression,e,s)}return t}},{key:"toAssignableObjectExpressionProp",value:function(t,e,s){if("ObjectMethod"===t.type){var i="get"===t.kind||"set"===t.kind?"Object pattern can't contain getter or setter":"Object pattern can't contain methods";this.raise(t.key.start,i)}else"SpreadElement"!==t.type||s?this.toAssignable(t,e,"object destructuring pattern"):this.raiseRestNotLast(t.start)}},{key:"toAssignableList",value:function(t,e,s,i){var a=t.length;if(a){var r=t[a-1];if(r&&"RestElement"===r.type)--a;else if(r&&"SpreadElement"===r.type){r.type="RestElement";var n=r.argument;this.toAssignable(n,e,s),"Identifier"!==n.type&&"MemberExpression"!==n.type&&"ArrayPattern"!==n.type&&"ObjectPattern"!==n.type&&this.unexpected(n.start),i&&this.raiseTrailingCommaAfterRest(i),--a}}for(var o=0;o<a;o++){var h=t[o];h&&(this.toAssignable(h,e,s),"RestElement"===h.type&&this.raiseRestNotLast(h.start))}return t}},{key:"toReferencedList",value:function(t,e){return t}},{key:"toReferencedListDeep",value:function(t,e){this.toReferencedList(t,e);for(var s=0;s<t.length;s++){var i=t[s];i&&"ArrayExpression"===i.type&&this.toReferencedListDeep(i.elements)}return t}},{key:"parseSpread",value:function(t,e){var s=this.startNode();return this.next(),s.argument=this.parseMaybeAssign(!1,t,void 0,e),this.finishNode(s,"SpreadElement")}},{key:"parseRestBinding",value:function(){var t=this.startNode();return this.next(),t.argument=this.parseBindingAtom(),this.finishNode(t,"RestElement")}},{key:"parseBindingAtom",value:function(){switch(this.state.type){case o.bracketL:var t=this.startNode();return this.next(),t.elements=this.parseBindingList(o.bracketR,93,!0),this.finishNode(t,"ArrayPattern");case o.braceL:return this.parseObj(!0)}return this.parseIdentifier()}},{key:"parseBindingList",value:function(t,e,s,i){for(var a=[],r=!0;!this.eat(t);)if(r?r=!1:this.expect(o.comma),s&&this.match(o.comma))a.push(null);else{if(this.eat(t))break;if(this.match(o.ellipsis)){a.push(this.parseAssignableListItemTypes(this.parseRestBinding())),this.checkCommaAfterRest(e),this.expect(t);break}var n=[];for(this.match(o.at)&&this.hasPlugin("decorators")&&this.raise(this.state.start,"Stage 2 decorators cannot be used to decorate parameters");this.match(o.at);)n.push(this.parseDecorator());a.push(this.parseAssignableListItem(i,n))}return a}},{key:"parseAssignableListItem",value:function(t,e){var s=this.parseMaybeDefault();this.parseAssignableListItemTypes(s);var i=this.parseMaybeDefault(s.start,s.loc.start,s);return e.length&&(s.decorators=e),i}},{key:"parseAssignableListItemTypes",value:function(t){return t}},{key:"parseMaybeDefault",value:function(t,e,s){if(e=e||this.state.startLoc,t=t||this.state.start,s=s||this.parseBindingAtom(),!this.eat(o.eq))return s;var i=this.startNodeAt(t,e);return i.left=s,i.right=this.parseMaybeAssign(),this.finishNode(i,"AssignmentPattern")}},{key:"checkLVal",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:64,s=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,a=arguments.length>4?arguments[4]:void 0,r=arguments.length>5&&void 0!==arguments[5]&&arguments[5];switch(t.type){case"Identifier":if(this.state.strict&&(r?M(t.name,this.inModule):O(t.name))&&this.raise(t.start,"".concat(64===e?"Assigning to":"Binding"," '").concat(t.name,"' in strict mode")),s){var n="_".concat(t.name);s[n]?this.raise(t.start,"Argument name clash"):s[n]=!0}a&&"let"===t.name&&this.raise(t.start,"'let' is not allowed to be used as a name in 'let' or 'const' declarations."),64&e||this.scope.declareName(t.name,e,t.start);break;case"MemberExpression":64!==e&&this.raise(t.start,"Binding member expression");break;case"ObjectPattern":for(var o=0,h=t.properties;o<h.length;o++){var u=h[o];if("ObjectProperty"===u.type)u=u.value;else if("ObjectMethod"===u.type)continue;this.checkLVal(u,e,s,"object destructuring pattern",a)}break;case"ArrayPattern":for(var l=0,c=t.elements;l<c.length;l++){var p=c[l];p&&this.checkLVal(p,e,s,"array destructuring pattern",a)}break;case"AssignmentPattern":this.checkLVal(t.left,e,s,"assignment pattern");break;case"RestElement":this.checkLVal(t.argument,e,s,"rest element");break;case"ParenthesizedExpression":this.checkLVal(t.expression,e,s,"parenthesized expression");break;default:var d=(64===e?"Invalid":"Binding invalid")+" left-hand side"+(i?" in "+i:"expression");this.raise(t.start,d)}}},{key:"checkToRestConversion",value:function(t){"Identifier"!==t.argument.type&&"MemberExpression"!==t.argument.type&&this.raise(t.argument.start,"Invalid rest operator's argument")}},{key:"checkCommaAfterRest",value:function(t){this.match(o.comma)&&(this.lookaheadCharCode()===t?this.raiseTrailingCommaAfterRest(this.state.start):this.raiseRestNotLast(this.state.start))}},{key:"raiseRestNotLast",value:function(t){throw this.raise(t,"Rest element must be last element")}},{key:"raiseTrailingCommaAfterRest",value:function(t){this.raise(t,"Unexpected trailing comma after rest element")}}]),e}(function(t){function e(){return d(this,e),g(this,D(e).apply(this,arguments))}return y(e,t),m(e,[{key:"startNode",value:function(){return new wt(this,this.state.start,this.state.startLoc)}},{key:"startNodeAt",value:function(t,e){return new wt(this,t,e)}},{key:"startNodeAtNode",value:function(t){return this.startNodeAt(t.start,t.loc.start)}},{key:"finishNode",value:function(t,e){return this.finishNodeAt(t,e,this.state.lastTokEnd,this.state.lastTokEndLoc)}},{key:"finishNodeAt",value:function(t,e,s,i){return t.type=e,t.end=s,t.loc.end=i,this.options.ranges&&(t.range[1]=s),this.processComment(t),t}},{key:"resetStartLocation",value:function(t,e,s){t.start=e,t.loc.start=s,this.options.ranges&&(t.range[0]=e)}},{key:"resetEndLocation",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state.lastTokEnd,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.state.lastTokEndLoc;t.end=e,t.loc.end=s,this.options.ranges&&(t.range[1]=e)}},{key:"resetStartLocationFromNode",value:function(t,e){this.resetStartLocation(t,e.start,e.loc.start)}}]),e}(At)))));function St(t,e){var s=Nt;return t&&t.plugins&&(!function(t){if(ut(t,"decorators")){if(ut(t,"decorators-legacy"))throw new Error("Cannot use the decorators and decorators-legacy plugin together");var e=lt(t,"decorators","decoratorsBeforeExport");if(null==e)throw new Error("The 'decorators' plugin requires a 'decoratorsBeforeExport' option, whose value must be a boolean. If you are migrating from Babylon/Babel 6 or want to use the old decorators proposal, you should use the 'decorators-legacy' plugin instead of 'decorators'.");if("boolean"!=typeof e)throw new Error("'decoratorsBeforeExport' must be a boolean.")}if(ut(t,"flow")&&ut(t,"typescript"))throw new Error("Cannot combine flow and typescript plugins.");if(ut(t,"placeholders")&&ut(t,"v8intrinsic"))throw new Error("Cannot combine placeholders and v8intrinsic plugins.");if(ut(t,"pipelineOperator")&&-1===ct.indexOf(lt(t,"pipelineOperator","proposal")))throw new Error("'pipelineOperator' requires 'proposal' option whose value should be one of: "+ct.map((function(t){return"'".concat(t,"'")})).join(", "))}(t.plugins),s=function(t){var e=dt.filter((function(e){return ut(t,e)})),s=e.join("/"),i=It[s];if(!i){i=Nt;for(var a=0;a<e.length;a++){var r=e[a];i=pt[r](i)}It[s]=i}return i}(t.plugins)),new s(t,e)}var It={};e.parse=function(t,e){if(!e||"unambiguous"!==e.sourceType)return St(e,t).parse();e=Object.assign({},e);try{e.sourceType="module";var s=St(e,t),i=s.parse();if(s.sawUnambiguousESM)return i;if(s.ambiguousScriptDifferentAst)try{return e.sourceType="script",St(e,t).parse()}catch(t){}else i.program.sourceType="script";return i}catch(s){try{return e.sourceType="script",St(e,t).parse()}catch(t){}throw s}},e.parseExpression=function(t,e){var s=St(e,t);return s.options.strictMode&&(s.state.strict=!0),s.getExpression()},e.tokTypes=o}));s(ot);ot.parse,ot.parseExpression,ot.tokTypes;var ht=u;function ut(t,e){return Object.assign({sourceType:"module",allowAwaitOutsideFunction:!0,allowImportExportEverywhere:!0,allowReturnOutsideFunction:!0,allowSuperOutsideMethod:!0,allowUndeclaredExports:!0,errorRecovery:!0,plugins:["jsx","doExpressions","objectRestSpread","classProperties","exportDefaultFrom","exportNamespaceFrom","asyncGenerators","functionBind","functionSent","dynamicImport","numericSeparator","importMeta","optionalCatchBinding","optionalChaining","classPrivateProperties",["pipelineOperator",{proposal:"minimal"}],"nullishCoalescingOperator","bigInt","throwExpressions","logicalAssignment","classPrivateMethods","v8intrinsic","partialApplication"].concat(e)},t)}function lt(t,s){return function(i,a,r){var n,o=ot,h=[ut({strictMode:!0},["decorators-legacy"].concat(s)),ut({strictMode:!1},["decorators-legacy"].concat(s)),ut({strictMode:!0},[["decorators",{decoratorsBeforeExport:!1}]].concat(s)),ut({strictMode:!1},[["decorators",{decoratorsBeforeExport:!1}]].concat(s))];try{n=function(t,e){for(var s,i=0;i<e.length;i++)try{return t(e[i])}catch(t){s||(s=t)}throw s}(o[t].bind(null,i),h)}catch(t){throw e(t.message.replace(/ \(.*\)/,""),{start:{line:t.loc.line,column:t.loc.column+1}})}return delete n.tokens,nt(n,Object.assign({},r,{originalText:i}))}}var ct=lt("parse",["flow"]),pt=lt("parse",[["flow",{all:!0,enums:!0}]]),dt=lt("parseExpression");function ft(t,s){switch(t.type){case"ArrayExpression":return t.elements.forEach(i);case"ObjectExpression":return t.properties.forEach(i);case"ObjectProperty":if(t.computed)throw a("computed");if(t.shorthand)throw a("shorthand");return[t.key,t.value].forEach(i);case"UnaryExpression":switch(t.operator){case"+":case"-":return i(t.argument);default:throw a("operator")}case"Identifier":if(s&&"ObjectProperty"===s.type&&s.key===t)return;throw a();case"NullLiteral":case"BooleanLiteral":case"NumericLiteral":case"StringLiteral":return;default:throw a()}function i(e){return ft(e,t)}function a(s){var i=s?"".concat(t.type," with ").concat(s,"=").concat(JSON.stringify(t[s])):t.type;return e("".concat(i," is not allowed in JSON."),{start:{line:t.loc.start.line,column:t.loc.start.column+1}})}}var mt=Object.assign({parse:ct,astFormat:"estree",hasPragma:ht},c),yt=Object.assign({},mt,{parse:pt}),Dt=Object.assign({},mt,{parse:dt}),vt={parsers:{babel:mt,"babel-flow":yt,babylon:mt,json:Object.assign({},Dt,{hasPragma:function(){return!0}}),json5:Dt,"json-stringify":Object.assign({parse:function(t,e,s){var i=dt(t,e,s);return i.comments.forEach(ft),ft(i),i},astFormat:"estree-json"},c),__js_expression:Dt,__vue_expression:Dt,__vue_event_binding:mt}},xt=vt.parsers;t.default=vt,t.parsers=xt,Object.defineProperty(t,"__esModule",{value:!0})}));
