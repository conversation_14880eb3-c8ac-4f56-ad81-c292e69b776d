/*
 * IMPORTANT!
 * This file has been automatically generated,
 * in order to update it's content execute "npm run update"
 */
'use strict'

module.exports = {
  rules: {
    'array-bracket-spacing': require('./rules/array-bracket-spacing'),
    'arrow-spacing': require('./rules/arrow-spacing'),
    'attribute-hyphenation': require('./rules/attribute-hyphenation'),
    'attributes-order': require('./rules/attributes-order'),
    'block-spacing': require('./rules/block-spacing'),
    'brace-style': require('./rules/brace-style'),
    'camelcase': require('./rules/camelcase'),
    'comma-dangle': require('./rules/comma-dangle'),
    'comment-directive': require('./rules/comment-directive'),
    'component-definition-name-casing': require('./rules/component-definition-name-casing'),
    'component-name-in-template-casing': require('./rules/component-name-in-template-casing'),
    'component-tags-order': require('./rules/component-tags-order'),
    'dot-location': require('./rules/dot-location'),
    'eqeqeq': require('./rules/eqeqeq'),
    'html-closing-bracket-newline': require('./rules/html-closing-bracket-newline'),
    'html-closing-bracket-spacing': require('./rules/html-closing-bracket-spacing'),
    'html-end-tags': require('./rules/html-end-tags'),
    'html-indent': require('./rules/html-indent'),
    'html-quotes': require('./rules/html-quotes'),
    'html-self-closing': require('./rules/html-self-closing'),
    'jsx-uses-vars': require('./rules/jsx-uses-vars'),
    'key-spacing': require('./rules/key-spacing'),
    'keyword-spacing': require('./rules/keyword-spacing'),
    'match-component-file-name': require('./rules/match-component-file-name'),
    'max-attributes-per-line': require('./rules/max-attributes-per-line'),
    'max-len': require('./rules/max-len'),
    'multiline-html-element-content-newline': require('./rules/multiline-html-element-content-newline'),
    'mustache-interpolation-spacing': require('./rules/mustache-interpolation-spacing'),
    'name-property-casing': require('./rules/name-property-casing'),
    'no-async-in-computed-properties': require('./rules/no-async-in-computed-properties'),
    'no-boolean-default': require('./rules/no-boolean-default'),
    'no-confusing-v-for-v-if': require('./rules/no-confusing-v-for-v-if'),
    'no-deprecated-scope-attribute': require('./rules/no-deprecated-scope-attribute'),
    'no-deprecated-slot-attribute': require('./rules/no-deprecated-slot-attribute'),
    'no-deprecated-slot-scope-attribute': require('./rules/no-deprecated-slot-scope-attribute'),
    'no-dupe-keys': require('./rules/no-dupe-keys'),
    'no-duplicate-attributes': require('./rules/no-duplicate-attributes'),
    'no-empty-pattern': require('./rules/no-empty-pattern'),
    'no-irregular-whitespace': require('./rules/no-irregular-whitespace'),
    'no-multi-spaces': require('./rules/no-multi-spaces'),
    'no-parsing-error': require('./rules/no-parsing-error'),
    'no-reserved-component-names': require('./rules/no-reserved-component-names'),
    'no-reserved-keys': require('./rules/no-reserved-keys'),
    'no-restricted-syntax': require('./rules/no-restricted-syntax'),
    'no-shared-component-data': require('./rules/no-shared-component-data'),
    'no-side-effects-in-computed-properties': require('./rules/no-side-effects-in-computed-properties'),
    'no-spaces-around-equal-signs-in-attribute': require('./rules/no-spaces-around-equal-signs-in-attribute'),
    'no-static-inline-styles': require('./rules/no-static-inline-styles'),
    'no-template-key': require('./rules/no-template-key'),
    'no-template-shadow': require('./rules/no-template-shadow'),
    'no-textarea-mustache': require('./rules/no-textarea-mustache'),
    'no-unsupported-features': require('./rules/no-unsupported-features'),
    'no-unused-components': require('./rules/no-unused-components'),
    'no-unused-vars': require('./rules/no-unused-vars'),
    'no-use-v-if-with-v-for': require('./rules/no-use-v-if-with-v-for'),
    'no-v-html': require('./rules/no-v-html'),
    'object-curly-spacing': require('./rules/object-curly-spacing'),
    'order-in-components': require('./rules/order-in-components'),
    'padding-line-between-blocks': require('./rules/padding-line-between-blocks'),
    'prop-name-casing': require('./rules/prop-name-casing'),
    'require-component-is': require('./rules/require-component-is'),
    'require-default-prop': require('./rules/require-default-prop'),
    'require-direct-export': require('./rules/require-direct-export'),
    'require-name-property': require('./rules/require-name-property'),
    'require-prop-type-constructor': require('./rules/require-prop-type-constructor'),
    'require-prop-types': require('./rules/require-prop-types'),
    'require-render-return': require('./rules/require-render-return'),
    'require-v-for-key': require('./rules/require-v-for-key'),
    'require-valid-default-prop': require('./rules/require-valid-default-prop'),
    'return-in-computed-property': require('./rules/return-in-computed-property'),
    'script-indent': require('./rules/script-indent'),
    'singleline-html-element-content-newline': require('./rules/singleline-html-element-content-newline'),
    'sort-keys': require('./rules/sort-keys'),
    'space-infix-ops': require('./rules/space-infix-ops'),
    'space-unary-ops': require('./rules/space-unary-ops'),
    'static-class-names-order': require('./rules/static-class-names-order'),
    'this-in-template': require('./rules/this-in-template'),
    'use-v-on-exact': require('./rules/use-v-on-exact'),
    'v-bind-style': require('./rules/v-bind-style'),
    'v-on-function-call': require('./rules/v-on-function-call'),
    'v-on-style': require('./rules/v-on-style'),
    'v-slot-style': require('./rules/v-slot-style'),
    'valid-template-root': require('./rules/valid-template-root'),
    'valid-v-bind-sync': require('./rules/valid-v-bind-sync'),
    'valid-v-bind': require('./rules/valid-v-bind'),
    'valid-v-cloak': require('./rules/valid-v-cloak'),
    'valid-v-else-if': require('./rules/valid-v-else-if'),
    'valid-v-else': require('./rules/valid-v-else'),
    'valid-v-for': require('./rules/valid-v-for'),
    'valid-v-html': require('./rules/valid-v-html'),
    'valid-v-if': require('./rules/valid-v-if'),
    'valid-v-model': require('./rules/valid-v-model'),
    'valid-v-on': require('./rules/valid-v-on'),
    'valid-v-once': require('./rules/valid-v-once'),
    'valid-v-pre': require('./rules/valid-v-pre'),
    'valid-v-show': require('./rules/valid-v-show'),
    'valid-v-slot': require('./rules/valid-v-slot'),
    'valid-v-text': require('./rules/valid-v-text')
  },
  configs: {
    'base': require('./configs/base'),
    'essential': require('./configs/essential'),
    'no-layout-rules': require('./configs/no-layout-rules'),
    'recommended': require('./configs/recommended'),
    'strongly-recommended': require('./configs/strongly-recommended')
  },
  processors: {
    '.vue': require('./processor')
  }
}
