{"name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "version": "4.0.1", "homepage": "https://github.com/jonschlinkert/clone-deep", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/clone-deep", "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=6"}, "scripts": {"test": "mocha"}, "dependencies": {"is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}, "devDependencies": {"gulp-format-md": "^2.0.0", "mocha": "^5.2.0"}, "keywords": ["array", "assign", "buffer", "clamped", "clone", "clone-array", "clone-array-deep", "clone-buffer", "clone-date", "clone-deep", "clone-map", "clone-object", "clone-object-deep", "clone-reg-exp", "clone-regex", "clone-regexp", "clone-set", "date", "deep", "extend", "mixin", "mixin-object", "object", "regex", "regexp", "shallow", "symbol"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-plain-object", "isobject", "kind-of", "shallow-clone"]}, "lint": {"reflinks": true}}}