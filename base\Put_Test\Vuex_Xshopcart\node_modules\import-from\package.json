{"name": "import-from", "version": "2.1.0", "description": "Import a module like with `require()` but from a given path", "license": "MIT", "repository": "sindresorhus/import-from", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["require", "resolve", "path", "module", "from", "like", "import", "path"], "dependencies": {"resolve-from": "^3.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}}