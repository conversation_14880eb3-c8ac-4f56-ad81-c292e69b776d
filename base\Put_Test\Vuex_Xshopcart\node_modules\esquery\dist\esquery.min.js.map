{"version": 3, "file": "esquery.min.js", "sources": ["../node_modules/estraverse/estraverse.js", "../parser.js", "../esquery.js"], "sourcesContent": ["/*\n  Copyright (C) 2012-2013 <PERSON><PERSON> <<EMAIL>>\n  Copyright (C) 2012 <PERSON>ya Hidayat <<EMAIL>>\n\n  Redistribution and use in source and binary forms, with or without\n  modification, are permitted provided that the following conditions are met:\n\n    * Redistributions of source code must retain the above copyright\n      notice, this list of conditions and the following disclaimer.\n    * Redistributions in binary form must reproduce the above copyright\n      notice, this list of conditions and the following disclaimer in the\n      documentation and/or other materials provided with the distribution.\n\n  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n  ARE DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\n  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n  THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n*/\n/*jslint vars:false, bitwise:true*/\n/*jshint indent:4*/\n/*global exports:true*/\n(function clone(exports) {\n    'use strict';\n\n    var Syntax,\n        VisitorOption,\n        VisitorKeys,\n        BREAK,\n        SKIP,\n        REMOVE;\n\n    function deepCopy(obj) {\n        var ret = {}, key, val;\n        for (key in obj) {\n            if (obj.hasOwnProperty(key)) {\n                val = obj[key];\n                if (typeof val === 'object' && val !== null) {\n                    ret[key] = deepCopy(val);\n                } else {\n                    ret[key] = val;\n                }\n            }\n        }\n        return ret;\n    }\n\n    // based on LLVM libc++ upper_bound / lower_bound\n    // MIT License\n\n    function upperBound(array, func) {\n        var diff, len, i, current;\n\n        len = array.length;\n        i = 0;\n\n        while (len) {\n            diff = len >>> 1;\n            current = i + diff;\n            if (func(array[current])) {\n                len = diff;\n            } else {\n                i = current + 1;\n                len -= diff + 1;\n            }\n        }\n        return i;\n    }\n\n    Syntax = {\n        AssignmentExpression: 'AssignmentExpression',\n        AssignmentPattern: 'AssignmentPattern',\n        ArrayExpression: 'ArrayExpression',\n        ArrayPattern: 'ArrayPattern',\n        ArrowFunctionExpression: 'ArrowFunctionExpression',\n        AwaitExpression: 'AwaitExpression', // CAUTION: It's deferred to ES7.\n        BlockStatement: 'BlockStatement',\n        BinaryExpression: 'BinaryExpression',\n        BreakStatement: 'BreakStatement',\n        CallExpression: 'CallExpression',\n        CatchClause: 'CatchClause',\n        ClassBody: 'ClassBody',\n        ClassDeclaration: 'ClassDeclaration',\n        ClassExpression: 'ClassExpression',\n        ComprehensionBlock: 'ComprehensionBlock',  // CAUTION: It's deferred to ES7.\n        ComprehensionExpression: 'ComprehensionExpression',  // CAUTION: It's deferred to ES7.\n        ConditionalExpression: 'ConditionalExpression',\n        ContinueStatement: 'ContinueStatement',\n        DebuggerStatement: 'DebuggerStatement',\n        DirectiveStatement: 'DirectiveStatement',\n        DoWhileStatement: 'DoWhileStatement',\n        EmptyStatement: 'EmptyStatement',\n        ExportAllDeclaration: 'ExportAllDeclaration',\n        ExportDefaultDeclaration: 'ExportDefaultDeclaration',\n        ExportNamedDeclaration: 'ExportNamedDeclaration',\n        ExportSpecifier: 'ExportSpecifier',\n        ExpressionStatement: 'ExpressionStatement',\n        ForStatement: 'ForStatement',\n        ForInStatement: 'ForInStatement',\n        ForOfStatement: 'ForOfStatement',\n        FunctionDeclaration: 'FunctionDeclaration',\n        FunctionExpression: 'FunctionExpression',\n        GeneratorExpression: 'GeneratorExpression',  // CAUTION: It's deferred to ES7.\n        Identifier: 'Identifier',\n        IfStatement: 'IfStatement',\n        ImportExpression: 'ImportExpression',\n        ImportDeclaration: 'ImportDeclaration',\n        ImportDefaultSpecifier: 'ImportDefaultSpecifier',\n        ImportNamespaceSpecifier: 'ImportNamespaceSpecifier',\n        ImportSpecifier: 'ImportSpecifier',\n        Literal: 'Literal',\n        LabeledStatement: 'LabeledStatement',\n        LogicalExpression: 'LogicalExpression',\n        MemberExpression: 'MemberExpression',\n        MetaProperty: 'MetaProperty',\n        MethodDefinition: 'MethodDefinition',\n        ModuleSpecifier: 'ModuleSpecifier',\n        NewExpression: 'NewExpression',\n        ObjectExpression: 'ObjectExpression',\n        ObjectPattern: 'ObjectPattern',\n        Program: 'Program',\n        Property: 'Property',\n        RestElement: 'RestElement',\n        ReturnStatement: 'ReturnStatement',\n        SequenceExpression: 'SequenceExpression',\n        SpreadElement: 'SpreadElement',\n        Super: 'Super',\n        SwitchStatement: 'SwitchStatement',\n        SwitchCase: 'SwitchCase',\n        TaggedTemplateExpression: 'TaggedTemplateExpression',\n        TemplateElement: 'TemplateElement',\n        TemplateLiteral: 'TemplateLiteral',\n        ThisExpression: 'ThisExpression',\n        ThrowStatement: 'ThrowStatement',\n        TryStatement: 'TryStatement',\n        UnaryExpression: 'UnaryExpression',\n        UpdateExpression: 'UpdateExpression',\n        VariableDeclaration: 'VariableDeclaration',\n        VariableDeclarator: 'VariableDeclarator',\n        WhileStatement: 'WhileStatement',\n        WithStatement: 'WithStatement',\n        YieldExpression: 'YieldExpression'\n    };\n\n    VisitorKeys = {\n        AssignmentExpression: ['left', 'right'],\n        AssignmentPattern: ['left', 'right'],\n        ArrayExpression: ['elements'],\n        ArrayPattern: ['elements'],\n        ArrowFunctionExpression: ['params', 'body'],\n        AwaitExpression: ['argument'], // CAUTION: It's deferred to ES7.\n        BlockStatement: ['body'],\n        BinaryExpression: ['left', 'right'],\n        BreakStatement: ['label'],\n        CallExpression: ['callee', 'arguments'],\n        CatchClause: ['param', 'body'],\n        ClassBody: ['body'],\n        ClassDeclaration: ['id', 'superClass', 'body'],\n        ClassExpression: ['id', 'superClass', 'body'],\n        ComprehensionBlock: ['left', 'right'],  // CAUTION: It's deferred to ES7.\n        ComprehensionExpression: ['blocks', 'filter', 'body'],  // CAUTION: It's deferred to ES7.\n        ConditionalExpression: ['test', 'consequent', 'alternate'],\n        ContinueStatement: ['label'],\n        DebuggerStatement: [],\n        DirectiveStatement: [],\n        DoWhileStatement: ['body', 'test'],\n        EmptyStatement: [],\n        ExportAllDeclaration: ['source'],\n        ExportDefaultDeclaration: ['declaration'],\n        ExportNamedDeclaration: ['declaration', 'specifiers', 'source'],\n        ExportSpecifier: ['exported', 'local'],\n        ExpressionStatement: ['expression'],\n        ForStatement: ['init', 'test', 'update', 'body'],\n        ForInStatement: ['left', 'right', 'body'],\n        ForOfStatement: ['left', 'right', 'body'],\n        FunctionDeclaration: ['id', 'params', 'body'],\n        FunctionExpression: ['id', 'params', 'body'],\n        GeneratorExpression: ['blocks', 'filter', 'body'],  // CAUTION: It's deferred to ES7.\n        Identifier: [],\n        IfStatement: ['test', 'consequent', 'alternate'],\n        ImportExpression: ['source'],\n        ImportDeclaration: ['specifiers', 'source'],\n        ImportDefaultSpecifier: ['local'],\n        ImportNamespaceSpecifier: ['local'],\n        ImportSpecifier: ['imported', 'local'],\n        Literal: [],\n        LabeledStatement: ['label', 'body'],\n        LogicalExpression: ['left', 'right'],\n        MemberExpression: ['object', 'property'],\n        MetaProperty: ['meta', 'property'],\n        MethodDefinition: ['key', 'value'],\n        ModuleSpecifier: [],\n        NewExpression: ['callee', 'arguments'],\n        ObjectExpression: ['properties'],\n        ObjectPattern: ['properties'],\n        Program: ['body'],\n        Property: ['key', 'value'],\n        RestElement: [ 'argument' ],\n        ReturnStatement: ['argument'],\n        SequenceExpression: ['expressions'],\n        SpreadElement: ['argument'],\n        Super: [],\n        SwitchStatement: ['discriminant', 'cases'],\n        SwitchCase: ['test', 'consequent'],\n        TaggedTemplateExpression: ['tag', 'quasi'],\n        TemplateElement: [],\n        TemplateLiteral: ['quasis', 'expressions'],\n        ThisExpression: [],\n        ThrowStatement: ['argument'],\n        TryStatement: ['block', 'handler', 'finalizer'],\n        UnaryExpression: ['argument'],\n        UpdateExpression: ['argument'],\n        VariableDeclaration: ['declarations'],\n        VariableDeclarator: ['id', 'init'],\n        WhileStatement: ['test', 'body'],\n        WithStatement: ['object', 'body'],\n        YieldExpression: ['argument']\n    };\n\n    // unique id\n    BREAK = {};\n    SKIP = {};\n    REMOVE = {};\n\n    VisitorOption = {\n        Break: BREAK,\n        Skip: SKIP,\n        Remove: REMOVE\n    };\n\n    function Reference(parent, key) {\n        this.parent = parent;\n        this.key = key;\n    }\n\n    Reference.prototype.replace = function replace(node) {\n        this.parent[this.key] = node;\n    };\n\n    Reference.prototype.remove = function remove() {\n        if (Array.isArray(this.parent)) {\n            this.parent.splice(this.key, 1);\n            return true;\n        } else {\n            this.replace(null);\n            return false;\n        }\n    };\n\n    function Element(node, path, wrap, ref) {\n        this.node = node;\n        this.path = path;\n        this.wrap = wrap;\n        this.ref = ref;\n    }\n\n    function Controller() { }\n\n    // API:\n    // return property path array from root to current node\n    Controller.prototype.path = function path() {\n        var i, iz, j, jz, result, element;\n\n        function addToPath(result, path) {\n            if (Array.isArray(path)) {\n                for (j = 0, jz = path.length; j < jz; ++j) {\n                    result.push(path[j]);\n                }\n            } else {\n                result.push(path);\n            }\n        }\n\n        // root node\n        if (!this.__current.path) {\n            return null;\n        }\n\n        // first node is sentinel, second node is root element\n        result = [];\n        for (i = 2, iz = this.__leavelist.length; i < iz; ++i) {\n            element = this.__leavelist[i];\n            addToPath(result, element.path);\n        }\n        addToPath(result, this.__current.path);\n        return result;\n    };\n\n    // API:\n    // return type of current node\n    Controller.prototype.type = function () {\n        var node = this.current();\n        return node.type || this.__current.wrap;\n    };\n\n    // API:\n    // return array of parent elements\n    Controller.prototype.parents = function parents() {\n        var i, iz, result;\n\n        // first node is sentinel\n        result = [];\n        for (i = 1, iz = this.__leavelist.length; i < iz; ++i) {\n            result.push(this.__leavelist[i].node);\n        }\n\n        return result;\n    };\n\n    // API:\n    // return current node\n    Controller.prototype.current = function current() {\n        return this.__current.node;\n    };\n\n    Controller.prototype.__execute = function __execute(callback, element) {\n        var previous, result;\n\n        result = undefined;\n\n        previous  = this.__current;\n        this.__current = element;\n        this.__state = null;\n        if (callback) {\n            result = callback.call(this, element.node, this.__leavelist[this.__leavelist.length - 1].node);\n        }\n        this.__current = previous;\n\n        return result;\n    };\n\n    // API:\n    // notify control skip / break\n    Controller.prototype.notify = function notify(flag) {\n        this.__state = flag;\n    };\n\n    // API:\n    // skip child nodes of current node\n    Controller.prototype.skip = function () {\n        this.notify(SKIP);\n    };\n\n    // API:\n    // break traversals\n    Controller.prototype['break'] = function () {\n        this.notify(BREAK);\n    };\n\n    // API:\n    // remove node\n    Controller.prototype.remove = function () {\n        this.notify(REMOVE);\n    };\n\n    Controller.prototype.__initialize = function(root, visitor) {\n        this.visitor = visitor;\n        this.root = root;\n        this.__worklist = [];\n        this.__leavelist = [];\n        this.__current = null;\n        this.__state = null;\n        this.__fallback = null;\n        if (visitor.fallback === 'iteration') {\n            this.__fallback = Object.keys;\n        } else if (typeof visitor.fallback === 'function') {\n            this.__fallback = visitor.fallback;\n        }\n\n        this.__keys = VisitorKeys;\n        if (visitor.keys) {\n            this.__keys = Object.assign(Object.create(this.__keys), visitor.keys);\n        }\n    };\n\n    function isNode(node) {\n        if (node == null) {\n            return false;\n        }\n        return typeof node === 'object' && typeof node.type === 'string';\n    }\n\n    function isProperty(nodeType, key) {\n        return (nodeType === Syntax.ObjectExpression || nodeType === Syntax.ObjectPattern) && 'properties' === key;\n    }\n  \n    function candidateExistsInLeaveList(leavelist, candidate) {\n        for (var i = leavelist.length - 1; i >= 0; --i) {\n            if (leavelist[i].node === candidate) {\n                return true;\n            }\n        }\n        return false;\n    }\n\n    Controller.prototype.traverse = function traverse(root, visitor) {\n        var worklist,\n            leavelist,\n            element,\n            node,\n            nodeType,\n            ret,\n            key,\n            current,\n            current2,\n            candidates,\n            candidate,\n            sentinel;\n\n        this.__initialize(root, visitor);\n\n        sentinel = {};\n\n        // reference\n        worklist = this.__worklist;\n        leavelist = this.__leavelist;\n\n        // initialize\n        worklist.push(new Element(root, null, null, null));\n        leavelist.push(new Element(null, null, null, null));\n\n        while (worklist.length) {\n            element = worklist.pop();\n\n            if (element === sentinel) {\n                element = leavelist.pop();\n\n                ret = this.__execute(visitor.leave, element);\n\n                if (this.__state === BREAK || ret === BREAK) {\n                    return;\n                }\n                continue;\n            }\n\n            if (element.node) {\n\n                ret = this.__execute(visitor.enter, element);\n\n                if (this.__state === BREAK || ret === BREAK) {\n                    return;\n                }\n\n                worklist.push(sentinel);\n                leavelist.push(element);\n\n                if (this.__state === SKIP || ret === SKIP) {\n                    continue;\n                }\n\n                node = element.node;\n                nodeType = node.type || element.wrap;\n                candidates = this.__keys[nodeType];\n                if (!candidates) {\n                    if (this.__fallback) {\n                        candidates = this.__fallback(node);\n                    } else {\n                        throw new Error('Unknown node type ' + nodeType + '.');\n                    }\n                }\n\n                current = candidates.length;\n                while ((current -= 1) >= 0) {\n                    key = candidates[current];\n                    candidate = node[key];\n                    if (!candidate) {\n                        continue;\n                    }\n\n                    if (Array.isArray(candidate)) {\n                        current2 = candidate.length;\n                        while ((current2 -= 1) >= 0) {\n                            if (!candidate[current2]) {\n                                continue;\n                            }\n\n                            if (candidateExistsInLeaveList(leavelist, candidate[current2])) {\n                              continue;\n                            }\n\n                            if (isProperty(nodeType, candidates[current])) {\n                                element = new Element(candidate[current2], [key, current2], 'Property', null);\n                            } else if (isNode(candidate[current2])) {\n                                element = new Element(candidate[current2], [key, current2], null, null);\n                            } else {\n                                continue;\n                            }\n                            worklist.push(element);\n                        }\n                    } else if (isNode(candidate)) {\n                        if (candidateExistsInLeaveList(leavelist, candidate)) {\n                          continue;\n                        }\n\n                        worklist.push(new Element(candidate, key, null, null));\n                    }\n                }\n            }\n        }\n    };\n\n    Controller.prototype.replace = function replace(root, visitor) {\n        var worklist,\n            leavelist,\n            node,\n            nodeType,\n            target,\n            element,\n            current,\n            current2,\n            candidates,\n            candidate,\n            sentinel,\n            outer,\n            key;\n\n        function removeElem(element) {\n            var i,\n                key,\n                nextElem,\n                parent;\n\n            if (element.ref.remove()) {\n                // When the reference is an element of an array.\n                key = element.ref.key;\n                parent = element.ref.parent;\n\n                // If removed from array, then decrease following items' keys.\n                i = worklist.length;\n                while (i--) {\n                    nextElem = worklist[i];\n                    if (nextElem.ref && nextElem.ref.parent === parent) {\n                        if  (nextElem.ref.key < key) {\n                            break;\n                        }\n                        --nextElem.ref.key;\n                    }\n                }\n            }\n        }\n\n        this.__initialize(root, visitor);\n\n        sentinel = {};\n\n        // reference\n        worklist = this.__worklist;\n        leavelist = this.__leavelist;\n\n        // initialize\n        outer = {\n            root: root\n        };\n        element = new Element(root, null, null, new Reference(outer, 'root'));\n        worklist.push(element);\n        leavelist.push(element);\n\n        while (worklist.length) {\n            element = worklist.pop();\n\n            if (element === sentinel) {\n                element = leavelist.pop();\n\n                target = this.__execute(visitor.leave, element);\n\n                // node may be replaced with null,\n                // so distinguish between undefined and null in this place\n                if (target !== undefined && target !== BREAK && target !== SKIP && target !== REMOVE) {\n                    // replace\n                    element.ref.replace(target);\n                }\n\n                if (this.__state === REMOVE || target === REMOVE) {\n                    removeElem(element);\n                }\n\n                if (this.__state === BREAK || target === BREAK) {\n                    return outer.root;\n                }\n                continue;\n            }\n\n            target = this.__execute(visitor.enter, element);\n\n            // node may be replaced with null,\n            // so distinguish between undefined and null in this place\n            if (target !== undefined && target !== BREAK && target !== SKIP && target !== REMOVE) {\n                // replace\n                element.ref.replace(target);\n                element.node = target;\n            }\n\n            if (this.__state === REMOVE || target === REMOVE) {\n                removeElem(element);\n                element.node = null;\n            }\n\n            if (this.__state === BREAK || target === BREAK) {\n                return outer.root;\n            }\n\n            // node may be null\n            node = element.node;\n            if (!node) {\n                continue;\n            }\n\n            worklist.push(sentinel);\n            leavelist.push(element);\n\n            if (this.__state === SKIP || target === SKIP) {\n                continue;\n            }\n\n            nodeType = node.type || element.wrap;\n            candidates = this.__keys[nodeType];\n            if (!candidates) {\n                if (this.__fallback) {\n                    candidates = this.__fallback(node);\n                } else {\n                    throw new Error('Unknown node type ' + nodeType + '.');\n                }\n            }\n\n            current = candidates.length;\n            while ((current -= 1) >= 0) {\n                key = candidates[current];\n                candidate = node[key];\n                if (!candidate) {\n                    continue;\n                }\n\n                if (Array.isArray(candidate)) {\n                    current2 = candidate.length;\n                    while ((current2 -= 1) >= 0) {\n                        if (!candidate[current2]) {\n                            continue;\n                        }\n                        if (isProperty(nodeType, candidates[current])) {\n                            element = new Element(candidate[current2], [key, current2], 'Property', new Reference(candidate, current2));\n                        } else if (isNode(candidate[current2])) {\n                            element = new Element(candidate[current2], [key, current2], null, new Reference(candidate, current2));\n                        } else {\n                            continue;\n                        }\n                        worklist.push(element);\n                    }\n                } else if (isNode(candidate)) {\n                    worklist.push(new Element(candidate, key, null, new Reference(node, key)));\n                }\n            }\n        }\n\n        return outer.root;\n    };\n\n    function traverse(root, visitor) {\n        var controller = new Controller();\n        return controller.traverse(root, visitor);\n    }\n\n    function replace(root, visitor) {\n        var controller = new Controller();\n        return controller.replace(root, visitor);\n    }\n\n    function extendCommentRange(comment, tokens) {\n        var target;\n\n        target = upperBound(tokens, function search(token) {\n            return token.range[0] > comment.range[0];\n        });\n\n        comment.extendedRange = [comment.range[0], comment.range[1]];\n\n        if (target !== tokens.length) {\n            comment.extendedRange[1] = tokens[target].range[0];\n        }\n\n        target -= 1;\n        if (target >= 0) {\n            comment.extendedRange[0] = tokens[target].range[1];\n        }\n\n        return comment;\n    }\n\n    function attachComments(tree, providedComments, tokens) {\n        // At first, we should calculate extended comment ranges.\n        var comments = [], comment, len, i, cursor;\n\n        if (!tree.range) {\n            throw new Error('attachComments needs range information');\n        }\n\n        // tokens array is empty, we attach comments to tree as 'leadingComments'\n        if (!tokens.length) {\n            if (providedComments.length) {\n                for (i = 0, len = providedComments.length; i < len; i += 1) {\n                    comment = deepCopy(providedComments[i]);\n                    comment.extendedRange = [0, tree.range[0]];\n                    comments.push(comment);\n                }\n                tree.leadingComments = comments;\n            }\n            return tree;\n        }\n\n        for (i = 0, len = providedComments.length; i < len; i += 1) {\n            comments.push(extendCommentRange(deepCopy(providedComments[i]), tokens));\n        }\n\n        // This is based on John Freeman's implementation.\n        cursor = 0;\n        traverse(tree, {\n            enter: function (node) {\n                var comment;\n\n                while (cursor < comments.length) {\n                    comment = comments[cursor];\n                    if (comment.extendedRange[1] > node.range[0]) {\n                        break;\n                    }\n\n                    if (comment.extendedRange[1] === node.range[0]) {\n                        if (!node.leadingComments) {\n                            node.leadingComments = [];\n                        }\n                        node.leadingComments.push(comment);\n                        comments.splice(cursor, 1);\n                    } else {\n                        cursor += 1;\n                    }\n                }\n\n                // already out of owned node\n                if (cursor === comments.length) {\n                    return VisitorOption.Break;\n                }\n\n                if (comments[cursor].extendedRange[0] > node.range[1]) {\n                    return VisitorOption.Skip;\n                }\n            }\n        });\n\n        cursor = 0;\n        traverse(tree, {\n            leave: function (node) {\n                var comment;\n\n                while (cursor < comments.length) {\n                    comment = comments[cursor];\n                    if (node.range[1] < comment.extendedRange[0]) {\n                        break;\n                    }\n\n                    if (node.range[1] === comment.extendedRange[0]) {\n                        if (!node.trailingComments) {\n                            node.trailingComments = [];\n                        }\n                        node.trailingComments.push(comment);\n                        comments.splice(cursor, 1);\n                    } else {\n                        cursor += 1;\n                    }\n                }\n\n                // already out of owned node\n                if (cursor === comments.length) {\n                    return VisitorOption.Break;\n                }\n\n                if (comments[cursor].extendedRange[0] > node.range[1]) {\n                    return VisitorOption.Skip;\n                }\n            }\n        });\n\n        return tree;\n    }\n\n    exports.Syntax = Syntax;\n    exports.traverse = traverse;\n    exports.replace = replace;\n    exports.attachComments = attachComments;\n    exports.VisitorKeys = VisitorKeys;\n    exports.VisitorOption = VisitorOption;\n    exports.Controller = Controller;\n    exports.cloneEnvironment = function () { return clone({}); };\n\n    return exports;\n}(exports));\n/* vim: set sw=4 ts=4 et tw=80 : */\n", "/*\n * Generated by PEG.js 0.10.0.\n *\n * http://pegjs.org/\n */\n(function(root, factory) {\n  if (typeof define === \"function\" && define.amd) {\n    define([], factory);\n  } else if (typeof module === \"object\" && module.exports) {\n    module.exports = factory();\n  }\n})(this, function() {\n  \"use strict\";\n\n  function peg$subclass(child, parent) {\n    function ctor() { this.constructor = child; }\n    ctor.prototype = parent.prototype;\n    child.prototype = new ctor();\n  }\n\n  function peg$SyntaxError(message, expected, found, location) {\n    this.message  = message;\n    this.expected = expected;\n    this.found    = found;\n    this.location = location;\n    this.name     = \"SyntaxError\";\n\n    if (typeof Error.captureStackTrace === \"function\") {\n      Error.captureStackTrace(this, peg$SyntaxError);\n    }\n  }\n\n  peg$subclass(peg$SyntaxError, Error);\n\n  peg$SyntaxError.buildMessage = function(expected, found) {\n    var DESCRIBE_EXPECTATION_FNS = {\n          literal: function(expectation) {\n            return \"\\\"\" + literalEscape(expectation.text) + \"\\\"\";\n          },\n\n          \"class\": function(expectation) {\n            var escapedParts = \"\",\n                i;\n\n            for (i = 0; i < expectation.parts.length; i++) {\n              escapedParts += expectation.parts[i] instanceof Array\n                ? classEscape(expectation.parts[i][0]) + \"-\" + classEscape(expectation.parts[i][1])\n                : classEscape(expectation.parts[i]);\n            }\n\n            return \"[\" + (expectation.inverted ? \"^\" : \"\") + escapedParts + \"]\";\n          },\n\n          any: function(expectation) {\n            return \"any character\";\n          },\n\n          end: function(expectation) {\n            return \"end of input\";\n          },\n\n          other: function(expectation) {\n            return expectation.description;\n          }\n        };\n\n    function hex(ch) {\n      return ch.charCodeAt(0).toString(16).toUpperCase();\n    }\n\n    function literalEscape(s) {\n      return s\n        .replace(/\\\\/g, '\\\\\\\\')\n        .replace(/\"/g,  '\\\\\"')\n        .replace(/\\0/g, '\\\\0')\n        .replace(/\\t/g, '\\\\t')\n        .replace(/\\n/g, '\\\\n')\n        .replace(/\\r/g, '\\\\r')\n        .replace(/[\\x00-\\x0F]/g,          function(ch) { return '\\\\x0' + hex(ch); })\n        .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return '\\\\x'  + hex(ch); });\n    }\n\n    function classEscape(s) {\n      return s\n        .replace(/\\\\/g, '\\\\\\\\')\n        .replace(/\\]/g, '\\\\]')\n        .replace(/\\^/g, '\\\\^')\n        .replace(/-/g,  '\\\\-')\n        .replace(/\\0/g, '\\\\0')\n        .replace(/\\t/g, '\\\\t')\n        .replace(/\\n/g, '\\\\n')\n        .replace(/\\r/g, '\\\\r')\n        .replace(/[\\x00-\\x0F]/g,          function(ch) { return '\\\\x0' + hex(ch); })\n        .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return '\\\\x'  + hex(ch); });\n    }\n\n    function describeExpectation(expectation) {\n      return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation);\n    }\n\n    function describeExpected(expected) {\n      var descriptions = new Array(expected.length),\n          i, j;\n\n      for (i = 0; i < expected.length; i++) {\n        descriptions[i] = describeExpectation(expected[i]);\n      }\n\n      descriptions.sort();\n\n      if (descriptions.length > 0) {\n        for (i = 1, j = 1; i < descriptions.length; i++) {\n          if (descriptions[i - 1] !== descriptions[i]) {\n            descriptions[j] = descriptions[i];\n            j++;\n          }\n        }\n        descriptions.length = j;\n      }\n\n      switch (descriptions.length) {\n        case 1:\n          return descriptions[0];\n\n        case 2:\n          return descriptions[0] + \" or \" + descriptions[1];\n\n        default:\n          return descriptions.slice(0, -1).join(\", \")\n            + \", or \"\n            + descriptions[descriptions.length - 1];\n      }\n    }\n\n    function describeFound(found) {\n      return found ? \"\\\"\" + literalEscape(found) + \"\\\"\" : \"end of input\";\n    }\n\n    return \"Expected \" + describeExpected(expected) + \" but \" + describeFound(found) + \" found.\";\n  };\n\n  function peg$parse(input, options) {\n    options = options !== void 0 ? options : {};\n\n    var peg$FAILED = {},\n\n        peg$startRuleFunctions = { start: peg$parsestart },\n        peg$startRuleFunction  = peg$parsestart,\n\n        peg$c0 = function(ss) {\n            return ss.length === 1 ? ss[0] : { type: 'matches', selectors: ss };\n          },\n        peg$c1 = function() { return void 0; },\n        peg$c2 = \" \",\n        peg$c3 = peg$literalExpectation(\" \", false),\n        peg$c4 = /^[^ [\\],():#!=><~+.]/,\n        peg$c5 = peg$classExpectation([\" \", \"[\", \"]\", \",\", \"(\", \")\", \":\", \"#\", \"!\", \"=\", \">\", \"<\", \"~\", \"+\", \".\"], true, false),\n        peg$c6 = function(i) { return i.join(''); },\n        peg$c7 = \">\",\n        peg$c8 = peg$literalExpectation(\">\", false),\n        peg$c9 = function() { return 'child'; },\n        peg$c10 = \"~\",\n        peg$c11 = peg$literalExpectation(\"~\", false),\n        peg$c12 = function() { return 'sibling'; },\n        peg$c13 = \"+\",\n        peg$c14 = peg$literalExpectation(\"+\", false),\n        peg$c15 = function() { return 'adjacent'; },\n        peg$c16 = function() { return 'descendant'; },\n        peg$c17 = \",\",\n        peg$c18 = peg$literalExpectation(\",\", false),\n        peg$c19 = function(s, ss) {\n          return [s].concat(ss.map(function (s) { return s[3]; }));\n        },\n        peg$c20 = function(a, ops) {\n            return ops.reduce(function (memo, rhs) {\n              return { type: rhs[0], left: memo, right: rhs[1] };\n            }, a);\n          },\n        peg$c21 = \"!\",\n        peg$c22 = peg$literalExpectation(\"!\", false),\n        peg$c23 = function(subject, as) {\n            const b = as.length === 1 ? as[0] : { type: 'compound', selectors: as };\n            if(subject) b.subject = true;\n            return b;\n          },\n        peg$c24 = \"*\",\n        peg$c25 = peg$literalExpectation(\"*\", false),\n        peg$c26 = function(a) { return { type: 'wildcard', value: a }; },\n        peg$c27 = \"#\",\n        peg$c28 = peg$literalExpectation(\"#\", false),\n        peg$c29 = function(i) { return { type: 'identifier', value: i }; },\n        peg$c30 = \"[\",\n        peg$c31 = peg$literalExpectation(\"[\", false),\n        peg$c32 = \"]\",\n        peg$c33 = peg$literalExpectation(\"]\", false),\n        peg$c34 = function(v) { return v; },\n        peg$c35 = /^[><!]/,\n        peg$c36 = peg$classExpectation([\">\", \"<\", \"!\"], false, false),\n        peg$c37 = \"=\",\n        peg$c38 = peg$literalExpectation(\"=\", false),\n        peg$c39 = function(a) { return (a || '') + '='; },\n        peg$c40 = /^[><]/,\n        peg$c41 = peg$classExpectation([\">\", \"<\"], false, false),\n        peg$c42 = \".\",\n        peg$c43 = peg$literalExpectation(\".\", false),\n        peg$c44 = function(name, op, value) {\n              return { type: 'attribute', name: name, operator: op, value: value };\n            },\n        peg$c45 = function(name) { return { type: 'attribute', name: name }; },\n        peg$c46 = \"\\\"\",\n        peg$c47 = peg$literalExpectation(\"\\\"\", false),\n        peg$c48 = /^[^\\\\\"]/,\n        peg$c49 = peg$classExpectation([\"\\\\\", \"\\\"\"], true, false),\n        peg$c50 = \"\\\\\",\n        peg$c51 = peg$literalExpectation(\"\\\\\", false),\n        peg$c52 = peg$anyExpectation(),\n        peg$c53 = function(a, b) { return a + b; },\n        peg$c54 = function(d) {\n                return { type: 'literal', value: strUnescape(d.join('')) };\n              },\n        peg$c55 = \"'\",\n        peg$c56 = peg$literalExpectation(\"'\", false),\n        peg$c57 = /^[^\\\\']/,\n        peg$c58 = peg$classExpectation([\"\\\\\", \"'\"], true, false),\n        peg$c59 = /^[0-9]/,\n        peg$c60 = peg$classExpectation([[\"0\", \"9\"]], false, false),\n        peg$c61 = function(a, b) {\n                // Can use `a.flat().join('')` once supported\n                const leadingDecimals = a ? [].concat.apply([], a).join('') : '';\n                return { type: 'literal', value: parseFloat(leadingDecimals + b.join('')) };\n              },\n        peg$c62 = function(i) { return { type: 'literal', value: i }; },\n        peg$c63 = \"type(\",\n        peg$c64 = peg$literalExpectation(\"type(\", false),\n        peg$c65 = /^[^ )]/,\n        peg$c66 = peg$classExpectation([\" \", \")\"], true, false),\n        peg$c67 = \")\",\n        peg$c68 = peg$literalExpectation(\")\", false),\n        peg$c69 = function(t) { return { type: 'type', value: t.join('') }; },\n        peg$c70 = /^[imsu]/,\n        peg$c71 = peg$classExpectation([\"i\", \"m\", \"s\", \"u\"], false, false),\n        peg$c72 = \"/\",\n        peg$c73 = peg$literalExpectation(\"/\", false),\n        peg$c74 = /^[^\\/]/,\n        peg$c75 = peg$classExpectation([\"/\"], true, false),\n        peg$c76 = function(d, flgs) { return {\n              type: 'regexp', value: new RegExp(d.join(''), flgs ? flgs.join('') : '') };\n            },\n        peg$c77 = function(i, is) {\n          return { type: 'field', name: is.reduce(function(memo, p){ return memo + p[0] + p[1]; }, i)};\n        },\n        peg$c78 = \":not(\",\n        peg$c79 = peg$literalExpectation(\":not(\", false),\n        peg$c80 = function(ss) { return { type: 'not', selectors: ss }; },\n        peg$c81 = \":matches(\",\n        peg$c82 = peg$literalExpectation(\":matches(\", false),\n        peg$c83 = function(ss) { return { type: 'matches', selectors: ss }; },\n        peg$c84 = \":has(\",\n        peg$c85 = peg$literalExpectation(\":has(\", false),\n        peg$c86 = function(ss) { return { type: 'has', selectors: ss }; },\n        peg$c87 = \":first-child\",\n        peg$c88 = peg$literalExpectation(\":first-child\", false),\n        peg$c89 = function() { return nth(1); },\n        peg$c90 = \":last-child\",\n        peg$c91 = peg$literalExpectation(\":last-child\", false),\n        peg$c92 = function() { return nthLast(1); },\n        peg$c93 = \":nth-child(\",\n        peg$c94 = peg$literalExpectation(\":nth-child(\", false),\n        peg$c95 = function(n) { return nth(parseInt(n.join(''), 10)); },\n        peg$c96 = \":nth-last-child(\",\n        peg$c97 = peg$literalExpectation(\":nth-last-child(\", false),\n        peg$c98 = function(n) { return nthLast(parseInt(n.join(''), 10)); },\n        peg$c99 = \":\",\n        peg$c100 = peg$literalExpectation(\":\", false),\n        peg$c101 = \"statement\",\n        peg$c102 = peg$literalExpectation(\"statement\", true),\n        peg$c103 = \"expression\",\n        peg$c104 = peg$literalExpectation(\"expression\", true),\n        peg$c105 = \"declaration\",\n        peg$c106 = peg$literalExpectation(\"declaration\", true),\n        peg$c107 = \"function\",\n        peg$c108 = peg$literalExpectation(\"function\", true),\n        peg$c109 = \"pattern\",\n        peg$c110 = peg$literalExpectation(\"pattern\", true),\n        peg$c111 = function(c) {\n          return { type: 'class', name: c };\n        },\n\n        peg$currPos          = 0,\n        peg$savedPos         = 0,\n        peg$posDetailsCache  = [{ line: 1, column: 1 }],\n        peg$maxFailPos       = 0,\n        peg$maxFailExpected  = [],\n        peg$silentFails      = 0,\n\n        peg$resultsCache = {},\n\n        peg$result;\n\n    if (\"startRule\" in options) {\n      if (!(options.startRule in peg$startRuleFunctions)) {\n        throw new Error(\"Can't start parsing from rule \\\"\" + options.startRule + \"\\\".\");\n      }\n\n      peg$startRuleFunction = peg$startRuleFunctions[options.startRule];\n    }\n\n    function text() {\n      return input.substring(peg$savedPos, peg$currPos);\n    }\n\n    function location() {\n      return peg$computeLocation(peg$savedPos, peg$currPos);\n    }\n\n    function expected(description, location) {\n      location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos)\n\n      throw peg$buildStructuredError(\n        [peg$otherExpectation(description)],\n        input.substring(peg$savedPos, peg$currPos),\n        location\n      );\n    }\n\n    function error(message, location) {\n      location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos)\n\n      throw peg$buildSimpleError(message, location);\n    }\n\n    function peg$literalExpectation(text, ignoreCase) {\n      return { type: \"literal\", text: text, ignoreCase: ignoreCase };\n    }\n\n    function peg$classExpectation(parts, inverted, ignoreCase) {\n      return { type: \"class\", parts: parts, inverted: inverted, ignoreCase: ignoreCase };\n    }\n\n    function peg$anyExpectation() {\n      return { type: \"any\" };\n    }\n\n    function peg$endExpectation() {\n      return { type: \"end\" };\n    }\n\n    function peg$otherExpectation(description) {\n      return { type: \"other\", description: description };\n    }\n\n    function peg$computePosDetails(pos) {\n      var details = peg$posDetailsCache[pos], p;\n\n      if (details) {\n        return details;\n      } else {\n        p = pos - 1;\n        while (!peg$posDetailsCache[p]) {\n          p--;\n        }\n\n        details = peg$posDetailsCache[p];\n        details = {\n          line:   details.line,\n          column: details.column\n        };\n\n        while (p < pos) {\n          if (input.charCodeAt(p) === 10) {\n            details.line++;\n            details.column = 1;\n          } else {\n            details.column++;\n          }\n\n          p++;\n        }\n\n        peg$posDetailsCache[pos] = details;\n        return details;\n      }\n    }\n\n    function peg$computeLocation(startPos, endPos) {\n      var startPosDetails = peg$computePosDetails(startPos),\n          endPosDetails   = peg$computePosDetails(endPos);\n\n      return {\n        start: {\n          offset: startPos,\n          line:   startPosDetails.line,\n          column: startPosDetails.column\n        },\n        end: {\n          offset: endPos,\n          line:   endPosDetails.line,\n          column: endPosDetails.column\n        }\n      };\n    }\n\n    function peg$fail(expected) {\n      if (peg$currPos < peg$maxFailPos) { return; }\n\n      if (peg$currPos > peg$maxFailPos) {\n        peg$maxFailPos = peg$currPos;\n        peg$maxFailExpected = [];\n      }\n\n      peg$maxFailExpected.push(expected);\n    }\n\n    function peg$buildSimpleError(message, location) {\n      return new peg$SyntaxError(message, null, null, location);\n    }\n\n    function peg$buildStructuredError(expected, found, location) {\n      return new peg$SyntaxError(\n        peg$SyntaxError.buildMessage(expected, found),\n        expected,\n        found,\n        location\n      );\n    }\n\n    function peg$parsestart() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 30 + 0,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parse_();\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseselectors();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parse_();\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c0(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parse_();\n        if (s1 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c1();\n        }\n        s0 = s1;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parse_() {\n      var s0, s1;\n\n      var key    = peg$currPos * 30 + 1,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = [];\n      if (input.charCodeAt(peg$currPos) === 32) {\n        s1 = peg$c2;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c3); }\n      }\n      while (s1 !== peg$FAILED) {\n        s0.push(s1);\n        if (input.charCodeAt(peg$currPos) === 32) {\n          s1 = peg$c2;\n          peg$currPos++;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c3); }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseidentifierName() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 30 + 2,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = [];\n      if (peg$c4.test(input.charAt(peg$currPos))) {\n        s2 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c5); }\n      }\n      if (s2 !== peg$FAILED) {\n        while (s2 !== peg$FAILED) {\n          s1.push(s2);\n          if (peg$c4.test(input.charAt(peg$currPos))) {\n            s2 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c5); }\n          }\n        }\n      } else {\n        s1 = peg$FAILED;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c6(s1);\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsebinaryOp() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 30 + 3,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parse_();\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 62) {\n          s2 = peg$c7;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c8); }\n        }\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parse_();\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c9();\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parse_();\n        if (s1 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 126) {\n            s2 = peg$c10;\n            peg$currPos++;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c11); }\n          }\n          if (s2 !== peg$FAILED) {\n            s3 = peg$parse_();\n            if (s3 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c12();\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n        if (s0 === peg$FAILED) {\n          s0 = peg$currPos;\n          s1 = peg$parse_();\n          if (s1 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 43) {\n              s2 = peg$c13;\n              peg$currPos++;\n            } else {\n              s2 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c14); }\n            }\n            if (s2 !== peg$FAILED) {\n              s3 = peg$parse_();\n              if (s3 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c15();\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n          if (s0 === peg$FAILED) {\n            s0 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 32) {\n              s1 = peg$c2;\n              peg$currPos++;\n            } else {\n              s1 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c3); }\n            }\n            if (s1 !== peg$FAILED) {\n              s2 = peg$parse_();\n              if (s2 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c16();\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseselectors() {\n      var s0, s1, s2, s3, s4, s5, s6, s7;\n\n      var key    = peg$currPos * 30 + 4,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseselector();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$currPos;\n        s4 = peg$parse_();\n        if (s4 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 44) {\n            s5 = peg$c17;\n            peg$currPos++;\n          } else {\n            s5 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c18); }\n          }\n          if (s5 !== peg$FAILED) {\n            s6 = peg$parse_();\n            if (s6 !== peg$FAILED) {\n              s7 = peg$parseselector();\n              if (s7 !== peg$FAILED) {\n                s4 = [s4, s5, s6, s7];\n                s3 = s4;\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$currPos;\n          s4 = peg$parse_();\n          if (s4 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 44) {\n              s5 = peg$c17;\n              peg$currPos++;\n            } else {\n              s5 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c18); }\n            }\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parse_();\n              if (s6 !== peg$FAILED) {\n                s7 = peg$parseselector();\n                if (s7 !== peg$FAILED) {\n                  s4 = [s4, s5, s6, s7];\n                  s3 = s4;\n                } else {\n                  peg$currPos = s3;\n                  s3 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c19(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseselector() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 5,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parsesequence();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$currPos;\n        s4 = peg$parsebinaryOp();\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsesequence();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$currPos;\n          s4 = peg$parsebinaryOp();\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsesequence();\n            if (s5 !== peg$FAILED) {\n              s4 = [s4, s5];\n              s3 = s4;\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c20(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsesequence() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 30 + 6,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 33) {\n        s1 = peg$c21;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c22); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$parseatom();\n        if (s3 !== peg$FAILED) {\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            s3 = peg$parseatom();\n          }\n        } else {\n          s2 = peg$FAILED;\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c23(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseatom() {\n      var s0;\n\n      var key    = peg$currPos * 30 + 7,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$parsewildcard();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parseidentifier();\n        if (s0 === peg$FAILED) {\n          s0 = peg$parseattr();\n          if (s0 === peg$FAILED) {\n            s0 = peg$parsefield();\n            if (s0 === peg$FAILED) {\n              s0 = peg$parsenegation();\n              if (s0 === peg$FAILED) {\n                s0 = peg$parsematches();\n                if (s0 === peg$FAILED) {\n                  s0 = peg$parsehas();\n                  if (s0 === peg$FAILED) {\n                    s0 = peg$parsefirstChild();\n                    if (s0 === peg$FAILED) {\n                      s0 = peg$parselastChild();\n                      if (s0 === peg$FAILED) {\n                        s0 = peg$parsenthChild();\n                        if (s0 === peg$FAILED) {\n                          s0 = peg$parsenthLastChild();\n                          if (s0 === peg$FAILED) {\n                            s0 = peg$parseclass();\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsewildcard() {\n      var s0, s1;\n\n      var key    = peg$currPos * 30 + 8,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 42) {\n        s1 = peg$c24;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c25); }\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c26(s1);\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseidentifier() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 30 + 9,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 35) {\n        s1 = peg$c27;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c28); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseidentifierName();\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c29(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattr() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 10,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 91) {\n        s1 = peg$c30;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c31); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseattrValue();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 93) {\n                s5 = peg$c32;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c33); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c34(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrOps() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 30 + 11,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (peg$c35.test(input.charAt(peg$currPos))) {\n        s1 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c36); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 61) {\n          s2 = peg$c37;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c38); }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c39(s1);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        if (peg$c40.test(input.charAt(peg$currPos))) {\n          s0 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s0 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c41); }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrEqOps() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 30 + 12,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 33) {\n        s1 = peg$c21;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c22); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 61) {\n          s2 = peg$c37;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c38); }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c39(s1);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrName() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 30 + 13,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = [];\n      s2 = peg$parseidentifierName();\n      if (s2 === peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 46) {\n          s2 = peg$c42;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c43); }\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        while (s2 !== peg$FAILED) {\n          s1.push(s2);\n          s2 = peg$parseidentifierName();\n          if (s2 === peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 46) {\n              s2 = peg$c42;\n              peg$currPos++;\n            } else {\n              s2 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c43); }\n            }\n          }\n        }\n      } else {\n        s1 = peg$FAILED;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c6(s1);\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrValue() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 14,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseattrName();\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseattrEqOps();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              s5 = peg$parsetype();\n              if (s5 === peg$FAILED) {\n                s5 = peg$parseregex();\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c44(s1, s3, s5);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parseattrName();\n        if (s1 !== peg$FAILED) {\n          s2 = peg$parse_();\n          if (s2 !== peg$FAILED) {\n            s3 = peg$parseattrOps();\n            if (s3 !== peg$FAILED) {\n              s4 = peg$parse_();\n              if (s4 !== peg$FAILED) {\n                s5 = peg$parsestring();\n                if (s5 === peg$FAILED) {\n                  s5 = peg$parsenumber();\n                  if (s5 === peg$FAILED) {\n                    s5 = peg$parsepath();\n                  }\n                }\n                if (s5 !== peg$FAILED) {\n                  peg$savedPos = s0;\n                  s1 = peg$c44(s1, s3, s5);\n                  s0 = s1;\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n        if (s0 === peg$FAILED) {\n          s0 = peg$currPos;\n          s1 = peg$parseattrName();\n          if (s1 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c45(s1);\n          }\n          s0 = s1;\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsestring() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 15,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 34) {\n        s1 = peg$c46;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c47); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        if (peg$c48.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c49); }\n        }\n        if (s3 === peg$FAILED) {\n          s3 = peg$currPos;\n          if (input.charCodeAt(peg$currPos) === 92) {\n            s4 = peg$c50;\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c51); }\n          }\n          if (s4 !== peg$FAILED) {\n            if (input.length > peg$currPos) {\n              s5 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s5 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c52); }\n            }\n            if (s5 !== peg$FAILED) {\n              peg$savedPos = s3;\n              s4 = peg$c53(s4, s5);\n              s3 = s4;\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          if (peg$c48.test(input.charAt(peg$currPos))) {\n            s3 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c49); }\n          }\n          if (s3 === peg$FAILED) {\n            s3 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 92) {\n              s4 = peg$c50;\n              peg$currPos++;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c51); }\n            }\n            if (s4 !== peg$FAILED) {\n              if (input.length > peg$currPos) {\n                s5 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c52); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s3;\n                s4 = peg$c53(s4, s5);\n                s3 = s4;\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 34) {\n            s3 = peg$c46;\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c47); }\n          }\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c54(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        if (input.charCodeAt(peg$currPos) === 39) {\n          s1 = peg$c55;\n          peg$currPos++;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c56); }\n        }\n        if (s1 !== peg$FAILED) {\n          s2 = [];\n          if (peg$c57.test(input.charAt(peg$currPos))) {\n            s3 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c58); }\n          }\n          if (s3 === peg$FAILED) {\n            s3 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 92) {\n              s4 = peg$c50;\n              peg$currPos++;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c51); }\n            }\n            if (s4 !== peg$FAILED) {\n              if (input.length > peg$currPos) {\n                s5 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c52); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s3;\n                s4 = peg$c53(s4, s5);\n                s3 = s4;\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          }\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            if (peg$c57.test(input.charAt(peg$currPos))) {\n              s3 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c58); }\n            }\n            if (s3 === peg$FAILED) {\n              s3 = peg$currPos;\n              if (input.charCodeAt(peg$currPos) === 92) {\n                s4 = peg$c50;\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c51); }\n              }\n              if (s4 !== peg$FAILED) {\n                if (input.length > peg$currPos) {\n                  s5 = input.charAt(peg$currPos);\n                  peg$currPos++;\n                } else {\n                  s5 = peg$FAILED;\n                  if (peg$silentFails === 0) { peg$fail(peg$c52); }\n                }\n                if (s5 !== peg$FAILED) {\n                  peg$savedPos = s3;\n                  s4 = peg$c53(s4, s5);\n                  s3 = s4;\n                } else {\n                  peg$currPos = s3;\n                  s3 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            }\n          }\n          if (s2 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 39) {\n              s3 = peg$c55;\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c56); }\n            }\n            if (s3 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c54(s2);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenumber() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 30 + 16,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      s2 = [];\n      if (peg$c59.test(input.charAt(peg$currPos))) {\n        s3 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s3 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c60); }\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        if (peg$c59.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c60); }\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 46) {\n          s3 = peg$c42;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c43); }\n        }\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        if (peg$c59.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c60); }\n        }\n        if (s3 !== peg$FAILED) {\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            if (peg$c59.test(input.charAt(peg$currPos))) {\n              s3 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c60); }\n            }\n          }\n        } else {\n          s2 = peg$FAILED;\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c61(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsepath() {\n      var s0, s1;\n\n      var key    = peg$currPos * 30 + 17,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseidentifierName();\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c62(s1);\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsetype() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 18,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 5) === peg$c63) {\n        s1 = peg$c63;\n        peg$currPos += 5;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c64); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          if (peg$c65.test(input.charAt(peg$currPos))) {\n            s4 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c66); }\n          }\n          if (s4 !== peg$FAILED) {\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              if (peg$c65.test(input.charAt(peg$currPos))) {\n                s4 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c66); }\n              }\n            }\n          } else {\n            s3 = peg$FAILED;\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c67;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c68); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c69(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseflags() {\n      var s0, s1;\n\n      var key    = peg$currPos * 30 + 19,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = [];\n      if (peg$c70.test(input.charAt(peg$currPos))) {\n        s1 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c71); }\n      }\n      if (s1 !== peg$FAILED) {\n        while (s1 !== peg$FAILED) {\n          s0.push(s1);\n          if (peg$c70.test(input.charAt(peg$currPos))) {\n            s1 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s1 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c71); }\n          }\n        }\n      } else {\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseregex() {\n      var s0, s1, s2, s3, s4;\n\n      var key    = peg$currPos * 30 + 20,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 47) {\n        s1 = peg$c72;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c73); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        if (peg$c74.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c75); }\n        }\n        if (s3 !== peg$FAILED) {\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            if (peg$c74.test(input.charAt(peg$currPos))) {\n              s3 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c75); }\n            }\n          }\n        } else {\n          s2 = peg$FAILED;\n        }\n        if (s2 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 47) {\n            s3 = peg$c72;\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c73); }\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parseflags();\n            if (s4 === peg$FAILED) {\n              s4 = null;\n            }\n            if (s4 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c76(s2, s4);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsefield() {\n      var s0, s1, s2, s3, s4, s5, s6;\n\n      var key    = peg$currPos * 30 + 21,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 46) {\n        s1 = peg$c42;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c43); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseidentifierName();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          s4 = peg$currPos;\n          if (input.charCodeAt(peg$currPos) === 46) {\n            s5 = peg$c42;\n            peg$currPos++;\n          } else {\n            s5 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c43); }\n          }\n          if (s5 !== peg$FAILED) {\n            s6 = peg$parseidentifierName();\n            if (s6 !== peg$FAILED) {\n              s5 = [s5, s6];\n              s4 = s5;\n            } else {\n              peg$currPos = s4;\n              s4 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s4;\n            s4 = peg$FAILED;\n          }\n          while (s4 !== peg$FAILED) {\n            s3.push(s4);\n            s4 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 46) {\n              s5 = peg$c42;\n              peg$currPos++;\n            } else {\n              s5 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c43); }\n            }\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parseidentifierName();\n              if (s6 !== peg$FAILED) {\n                s5 = [s5, s6];\n                s4 = s5;\n              } else {\n                peg$currPos = s4;\n                s4 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s4;\n              s4 = peg$FAILED;\n            }\n          }\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c77(s2, s3);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenegation() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 22,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 5) === peg$c78) {\n        s1 = peg$c78;\n        peg$currPos += 5;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c79); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseselectors();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c67;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c68); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c80(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsematches() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 23,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 9) === peg$c81) {\n        s1 = peg$c81;\n        peg$currPos += 9;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c82); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseselectors();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c67;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c68); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c83(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsehas() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 24,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 5) === peg$c84) {\n        s1 = peg$c84;\n        peg$currPos += 5;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c85); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseselectors();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c67;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c68); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c86(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsefirstChild() {\n      var s0, s1;\n\n      var key    = peg$currPos * 30 + 25,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 12) === peg$c87) {\n        s1 = peg$c87;\n        peg$currPos += 12;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c88); }\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c89();\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parselastChild() {\n      var s0, s1;\n\n      var key    = peg$currPos * 30 + 26,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 11) === peg$c90) {\n        s1 = peg$c90;\n        peg$currPos += 11;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c91); }\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c92();\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenthChild() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 27,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 11) === peg$c93) {\n        s1 = peg$c93;\n        peg$currPos += 11;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c94); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          if (peg$c59.test(input.charAt(peg$currPos))) {\n            s4 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c60); }\n          }\n          if (s4 !== peg$FAILED) {\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              if (peg$c59.test(input.charAt(peg$currPos))) {\n                s4 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c60); }\n              }\n            }\n          } else {\n            s3 = peg$FAILED;\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c67;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c68); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c95(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenthLastChild() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 28,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 16) === peg$c96) {\n        s1 = peg$c96;\n        peg$currPos += 16;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c97); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          if (peg$c59.test(input.charAt(peg$currPos))) {\n            s4 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c60); }\n          }\n          if (s4 !== peg$FAILED) {\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              if (peg$c59.test(input.charAt(peg$currPos))) {\n                s4 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c60); }\n              }\n            }\n          } else {\n            s3 = peg$FAILED;\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c67;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c68); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c98(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseclass() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 30 + 29,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 58) {\n        s1 = peg$c99;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c100); }\n      }\n      if (s1 !== peg$FAILED) {\n        if (input.substr(peg$currPos, 9).toLowerCase() === peg$c101) {\n          s2 = input.substr(peg$currPos, 9);\n          peg$currPos += 9;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c102); }\n        }\n        if (s2 === peg$FAILED) {\n          if (input.substr(peg$currPos, 10).toLowerCase() === peg$c103) {\n            s2 = input.substr(peg$currPos, 10);\n            peg$currPos += 10;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c104); }\n          }\n          if (s2 === peg$FAILED) {\n            if (input.substr(peg$currPos, 11).toLowerCase() === peg$c105) {\n              s2 = input.substr(peg$currPos, 11);\n              peg$currPos += 11;\n            } else {\n              s2 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c106); }\n            }\n            if (s2 === peg$FAILED) {\n              if (input.substr(peg$currPos, 8).toLowerCase() === peg$c107) {\n                s2 = input.substr(peg$currPos, 8);\n                peg$currPos += 8;\n              } else {\n                s2 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c108); }\n              }\n              if (s2 === peg$FAILED) {\n                if (input.substr(peg$currPos, 7).toLowerCase() === peg$c109) {\n                  s2 = input.substr(peg$currPos, 7);\n                  peg$currPos += 7;\n                } else {\n                  s2 = peg$FAILED;\n                  if (peg$silentFails === 0) { peg$fail(peg$c110); }\n                }\n              }\n            }\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c111(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n\n      function nth(n) { return { type: 'nth-child', index: { type: 'literal', value: n } }; }\n      function nthLast(n) { return { type: 'nth-last-child', index: { type: 'literal', value: n } }; }\n      function strUnescape(s) {\n        return s.replace(/\\\\(.)/g, function(match, ch) {\n          switch(ch) {\n            case 'b': return '\\b';\n            case 'f': return '\\f';\n            case 'n': return '\\n';\n            case 'r': return '\\r';\n            case 't': return '\\t';\n            case 'v': return '\\v';\n            default: return ch;\n          }\n        });\n      }\n\n\n    peg$result = peg$startRuleFunction();\n\n    if (peg$result !== peg$FAILED && peg$currPos === input.length) {\n      return peg$result;\n    } else {\n      if (peg$result !== peg$FAILED && peg$currPos < input.length) {\n        peg$fail(peg$endExpectation());\n      }\n\n      throw peg$buildStructuredError(\n        peg$maxFailExpected,\n        peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null,\n        peg$maxFailPos < input.length\n          ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1)\n          : peg$computeLocation(peg$maxFailPos, peg$maxFailPos)\n      );\n    }\n  }\n\n  return {\n    SyntaxError: peg$SyntaxError,\n    parse:       peg$parse\n  };\n});\n", "/* vim: set sw=4 sts=4 : */\nimport estraverse from 'estraverse';\nimport parser from './parser.js';\n\n/**\n* @typedef {\"LEFT_SIDE\"|\"RIGHT_SIDE\"} Side\n*/\n\nconst LEFT_SIDE = 'LEFT_SIDE';\nconst RIGHT_SIDE = 'RIGHT_SIDE';\n\n/**\n * @external AST\n * @see https://esprima.readthedocs.io/en/latest/syntax-tree-format.html\n */\n\n/**\n * One of the rules of `grammar.pegjs`\n * @typedef {PlainObject} SelectorAST\n * @see grammar.pegjs\n*/\n\n/**\n * The `sequence` production of `grammar.pegjs`\n * @typedef {PlainObject} SelectorSequenceAST\n*/\n\n/**\n * Get the value of a property which may be multiple levels down\n * in the object.\n * @param {?PlainObject} obj\n * @param {string} key\n * @returns {undefined|boolean|string|number|external:AST}\n */\nfunction getPath(obj, key) {\n    const keys = key.split('.');\n    for (let i = 0; i < keys.length; i++) {\n        if (obj == null) { return obj; }\n        obj = obj[keys[i]];\n    }\n    return obj;\n}\n\n/**\n * Determine whether `node` can be reached by following `path`,\n * starting at `ancestor`.\n * @param {?external:AST} node\n * @param {?external:AST} ancestor\n * @param {string[]} path\n * @returns {boolean}\n */\nfunction inPath(node, ancestor, path) {\n    if (path.length === 0) { return node === ancestor; }\n    if (ancestor == null) { return false; }\n    const field = ancestor[path[0]];\n    const remainingPath = path.slice(1);\n    if (Array.isArray(field)) {\n        for (let i = 0, l = field.length; i < l; ++i) {\n            if (inPath(node, field[i], remainingPath)) { return true; }\n        }\n        return false;\n    } else {\n        return inPath(node, field, remainingPath);\n    }\n}\n\n/**\n * Given a `node` and its ancestors, determine if `node` is matched\n * by `selector`.\n * @param {?external:AST} node\n * @param {?SelectorAST} selector\n * @param {external:AST[]} [ancestry=[]]\n * @throws {Error} Unknowns (operator, class name, selector type, or\n * selector value type)\n * @returns {boolean}\n */\nfunction matches(node, selector, ancestry) {\n    if (!selector) { return true; }\n    if (!node) { return false; }\n    if (!ancestry) { ancestry = []; }\n\n    switch(selector.type) {\n        case 'wildcard':\n            return true;\n\n        case 'identifier':\n            return selector.value.toLowerCase() === node.type.toLowerCase();\n\n        case 'field': {\n            const path = selector.name.split('.');\n            const ancestor = ancestry[path.length - 1];\n            return inPath(node, ancestor, path);\n\n        }\n        case 'matches':\n            for (let i = 0, l = selector.selectors.length; i < l; ++i) {\n                if (matches(node, selector.selectors[i], ancestry)) { return true; }\n            }\n            return false;\n\n        case 'compound':\n            for (let i = 0, l = selector.selectors.length; i < l; ++i) {\n                if (!matches(node, selector.selectors[i], ancestry)) { return false; }\n            }\n            return true;\n\n        case 'not':\n            for (let i = 0, l = selector.selectors.length; i < l; ++i) {\n                if (matches(node, selector.selectors[i], ancestry)) { return false; }\n            }\n            return true;\n\n        case 'has': {\n            const collector = [];\n            for (let i = 0, l = selector.selectors.length; i < l; ++i) {\n                const a = [];\n                estraverse.traverse(node, {\n                    enter (node, parent) {\n                        if (parent != null) { a.unshift(parent); }\n                        if (matches(node, selector.selectors[i], a)) {\n                            collector.push(node);\n                        }\n                    },\n                    leave () { a.shift(); },\n                    fallback: 'iteration'\n                });\n            }\n            return collector.length !== 0;\n\n        }\n        case 'child':\n            if (matches(node, selector.right, ancestry)) {\n                return matches(ancestry[0], selector.left, ancestry.slice(1));\n            }\n            return false;\n\n        case 'descendant':\n            if (matches(node, selector.right, ancestry)) {\n                for (let i = 0, l = ancestry.length; i < l; ++i) {\n                    if (matches(ancestry[i], selector.left, ancestry.slice(i + 1))) {\n                        return true;\n                    }\n                }\n            }\n            return false;\n\n        case 'attribute': {\n            const p = getPath(node, selector.name);\n            switch (selector.operator) {\n                case void 0:\n                    return p != null;\n                case '=':\n                    switch (selector.value.type) {\n                        case 'regexp': return typeof p === 'string' && selector.value.value.test(p);\n                        case 'literal': return `${selector.value.value}` === `${p}`;\n                        case 'type': return selector.value.value === typeof p;\n                    }\n                    throw new Error(`Unknown selector value type: ${selector.value.type}`);\n                case '!=':\n                    switch (selector.value.type) {\n                        case 'regexp': return !selector.value.value.test(p);\n                        case 'literal': return `${selector.value.value}` !== `${p}`;\n                        case 'type': return selector.value.value !== typeof p;\n                    }\n                    throw new Error(`Unknown selector value type: ${selector.value.type}`);\n                case '<=': return p <= selector.value.value;\n                case '<': return p < selector.value.value;\n                case '>': return p > selector.value.value;\n                case '>=': return p >= selector.value.value;\n            }\n            throw new Error(`Unknown operator: ${selector.operator}`);\n        }\n        case 'sibling':\n            return matches(node, selector.right, ancestry) &&\n                sibling(node, selector.left, ancestry, LEFT_SIDE) ||\n                selector.left.subject &&\n                matches(node, selector.left, ancestry) &&\n                sibling(node, selector.right, ancestry, RIGHT_SIDE);\n        case 'adjacent':\n            return matches(node, selector.right, ancestry) &&\n                adjacent(node, selector.left, ancestry, LEFT_SIDE) ||\n                selector.right.subject &&\n                matches(node, selector.left, ancestry) &&\n                adjacent(node, selector.right, ancestry, RIGHT_SIDE);\n\n        case 'nth-child':\n            return matches(node, selector.right, ancestry) &&\n                nthChild(node, ancestry, function () {\n                    return selector.index.value - 1;\n                });\n\n        case 'nth-last-child':\n            return matches(node, selector.right, ancestry) &&\n                nthChild(node, ancestry, function (length) {\n                    return length - selector.index.value;\n                });\n\n        case 'class':\n            switch(selector.name.toLowerCase()){\n                case 'statement':\n                    if(node.type.slice(-9) === 'Statement') return true;\n                    // fallthrough: interface Declaration <: Statement { }\n                case 'declaration':\n                    return node.type.slice(-11) === 'Declaration';\n                case 'pattern':\n                    if(node.type.slice(-7) === 'Pattern') return true;\n                    // fallthrough: interface Expression <: Node, Pattern { }\n                case 'expression':\n                    return node.type.slice(-10) === 'Expression' ||\n                        node.type.slice(-7) === 'Literal' ||\n                        (\n                            node.type === 'Identifier' &&\n                            (ancestry.length === 0 || ancestry[0].type !== 'MetaProperty')\n                        ) ||\n                        node.type === 'MetaProperty';\n                case 'function':\n                    return node.type === 'FunctionDeclaration' ||\n                        node.type === 'FunctionExpression' ||\n                        node.type === 'ArrowFunctionExpression';\n            }\n            throw new Error(`Unknown class name: ${selector.name}`);\n    }\n\n    throw new Error(`Unknown selector type: ${selector.type}`);\n}\n\n/**\n * Determines if the given node has a sibling that matches the\n * given selector.\n * @param {external:AST} node\n * @param {SelectorSequenceAST} selector\n * @param {external:AST[]} ancestry\n * @param {Side} side\n * @returns {boolean}\n */\nfunction sibling(node, selector, ancestry, side) {\n    const [parent] = ancestry;\n    if (!parent) { return false; }\n    const keys = estraverse.VisitorKeys[parent.type];\n    for (let i = 0, l = keys.length; i < l; ++i) {\n        const listProp = parent[keys[i]];\n        if (Array.isArray(listProp)) {\n            const startIndex = listProp.indexOf(node);\n            if (startIndex < 0) { continue; }\n            let lowerBound, upperBound;\n            if (side === LEFT_SIDE) {\n                lowerBound = 0;\n                upperBound = startIndex;\n            } else {\n                lowerBound = startIndex + 1;\n                upperBound = listProp.length;\n            }\n            for (let k = lowerBound; k < upperBound; ++k) {\n                if (matches(listProp[k], selector, ancestry)) {\n                    return true;\n                }\n            }\n        }\n    }\n    return false;\n}\n\n/**\n * Determines if the given node has an adjacent sibling that matches\n * the given selector.\n * @param {external:AST} node\n * @param {SelectorSequenceAST} selector\n * @param {external:AST[]} ancestry\n * @param {Side} side\n * @returns {boolean}\n */\nfunction adjacent(node, selector, ancestry, side) {\n    const [parent] = ancestry;\n    if (!parent) { return false; }\n    const keys = estraverse.VisitorKeys[parent.type];\n    for (let i = 0, l = keys.length; i < l; ++i) {\n        const listProp = parent[keys[i]];\n        if (Array.isArray(listProp)) {\n            const idx = listProp.indexOf(node);\n            if (idx < 0) { continue; }\n            if (side === LEFT_SIDE && idx > 0 && matches(listProp[idx - 1], selector, ancestry)) {\n                return true;\n            }\n            if (side === RIGHT_SIDE && idx < listProp.length - 1 && matches(listProp[idx + 1], selector, ancestry)) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n\n/**\n* @callback IndexFunction\n* @param {Integer} len Containing list's length\n* @returns {Integer}\n*/\n\n/**\n * Determines if the given node is the nth child, determined by\n * `idxFn`, which is given the containing list's length.\n * @param {external:AST} node\n * @param {external:AST[]} ancestry\n * @param {IndexFunction} idxFn\n * @returns {boolean}\n */\nfunction nthChild(node, ancestry, idxFn) {\n    const [parent] = ancestry;\n    if (!parent) { return false; }\n    const keys = estraverse.VisitorKeys[parent.type];\n    for (let i = 0, l = keys.length; i < l; ++i) {\n        const listProp = parent[keys[i]];\n        if (Array.isArray(listProp)) {\n            const idx = listProp.indexOf(node);\n            if (idx >= 0 && idx === idxFn(listProp.length)) { return true; }\n        }\n    }\n    return false;\n}\n\n/**\n * For each selector node marked as a subject, find the portion of the\n * selector that the subject must match.\n * @param {SelectorAST} selector\n * @param {SelectorAST} [ancestor] Defaults to `selector`\n * @returns {SelectorAST[]}\n */\nfunction subjects(selector, ancestor) {\n    if (selector == null || typeof selector != 'object') { return []; }\n    if (ancestor == null) { ancestor = selector; }\n    const results = selector.subject ? [ancestor] : [];\n    for (const [p, sel] of Object.entries(selector)) {\n        results.push(...subjects(sel, p === 'left' ? sel : ancestor));\n    }\n    return results;\n}\n\n/**\n* @callback TraverseVisitor\n* @param {?external:AST} node\n* @param {?external:AST} parent\n* @param {external:AST[]} ancestry\n*/\n\n/**\n * From a JS AST and a selector AST, collect all JS AST nodes that\n * match the selector.\n * @param {external:AST} ast\n * @param {?SelectorAST} selector\n * @param {TraverseVisitor} visitor\n * @returns {external:AST[]}\n */\nfunction traverse(ast, selector, visitor) {\n    if (!selector) { return; }\n    const ancestry = [];\n    const altSubjects = subjects(selector);\n    estraverse.traverse(ast, {\n        enter (node, parent) {\n            if (parent != null) { ancestry.unshift(parent); }\n            if (matches(node, selector, ancestry)) {\n                if (altSubjects.length) {\n                    for (let i = 0, l = altSubjects.length; i < l; ++i) {\n                        if (matches(node, altSubjects[i], ancestry)) {\n                            visitor(node, parent, ancestry);\n                        }\n                        for (let k = 0, m = ancestry.length; k < m; ++k) {\n                            const succeedingAncestry = ancestry.slice(k + 1);\n                            if (matches(ancestry[k], altSubjects[i], succeedingAncestry)) {\n                                visitor(ancestry[k], parent, succeedingAncestry);\n                            }\n                        }\n                    }\n                } else {\n                    visitor(node, parent, ancestry);\n                }\n            }\n        },\n        leave () { ancestry.shift(); },\n        fallback: 'iteration'\n    });\n}\n\n\n/**\n * From a JS AST and a selector AST, collect all JS AST nodes that\n * match the selector.\n * @param {external:AST} ast\n * @param {?SelectorAST} selector\n * @returns {external:AST[]}\n */\nfunction match(ast, selector) {\n    const results = [];\n    traverse(ast, selector, function (node) {\n        results.push(node);\n    });\n    return results;\n}\n\n/**\n * Parse a selector string and return its AST.\n * @param {string} selector\n * @returns {SelectorAST}\n */\nfunction parse(selector) {\n    return parser.parse(selector);\n}\n\n/**\n * Query the code AST using the selector string.\n * @param {external:AST} ast\n * @param {string} selector\n * @returns {external:AST[]}\n */\nfunction query(ast, selector) {\n    return match(ast, parse(selector));\n}\n\nquery.parse = parse;\nquery.match = match;\nquery.traverse = traverse;\nquery.matches = matches;\nquery.query = query;\n\nexport default query;\n"], "names": ["clone", "exports", "Syntax", "VisitorOption", "VisitorKeys", "BREAK", "SKIP", "REMOVE", "deepCopy", "obj", "key", "val", "ret", "hasOwnProperty", "Reference", "parent", "Element", "node", "path", "wrap", "ref", "Controller", "isNode", "type", "isProperty", "nodeType", "ObjectExpression", "ObjectPattern", "candidateExistsInLeaveList", "leavelist", "candidate", "i", "length", "traverse", "root", "visitor", "extendCommentRange", "comment", "tokens", "target", "array", "func", "diff", "len", "current", "upperBound", "token", "range", "extendedRange", "AssignmentExpression", "AssignmentPattern", "ArrayExpression", "ArrayPattern", "ArrowFunctionExpression", "AwaitExpression", "BlockStatement", "BinaryExpression", "BreakStatement", "CallExpression", "CatchClause", "ClassBody", "ClassDeclaration", "ClassExpression", "ComprehensionBlock", "ComprehensionExpression", "ConditionalExpression", "ContinueStatement", "DebuggerStatement", "DirectiveStatement", "DoWhileStatement", "EmptyStatement", "ExportAllDeclaration", "ExportDefaultDeclaration", "ExportNamedDeclaration", "ExportSpecifier", "ExpressionStatement", "ForStatement", "ForInStatement", "ForOfStatement", "FunctionDeclaration", "FunctionExpression", "GeneratorExpression", "Identifier", "IfStatement", "ImportExpression", "ImportDeclaration", "ImportDefaultSpecifier", "ImportNamespaceSpecifier", "ImportSpecifier", "Literal", "LabeledStatement", "LogicalExpression", "MemberExpression", "MetaProperty", "MethodDefinition", "ModuleSpecifier", "NewExpression", "Program", "Property", "RestElement", "ReturnStatement", "SequenceExpression", "SpreadElement", "Super", "SwitchStatement", "SwitchCase", "TaggedTemplateExpression", "TemplateElement", "TemplateLiteral", "ThisExpression", "ThrowStatement", "TryStatement", "UnaryExpression", "UpdateExpression", "VariableDeclaration", "VariableDeclarator", "WhileStatement", "WithStatement", "YieldExpression", "Break", "<PERSON><PERSON>", "Remove", "prototype", "replace", "this", "remove", "Array", "isArray", "splice", "iz", "j", "jz", "result", "addToPath", "push", "__current", "__leavelist", "parents", "__execute", "callback", "element", "previous", "undefined", "__state", "call", "notify", "flag", "skip", "__initialize", "__worklist", "__fallback", "fallback", "Object", "keys", "__keys", "assign", "create", "worklist", "current2", "candidates", "sentinel", "pop", "enter", "Error", "leave", "outer", "removeElem", "nextElem", "attachComments", "tree", "providedComments", "cursor", "comments", "leadingComments", "trailingComments", "cloneEnvironment", "module", "peg$SyntaxError", "message", "expected", "found", "location", "name", "captureStackTrace", "child", "ctor", "constructor", "peg$subclass", "buildMessage", "DESCRIBE_EXPECTATION_FNS", "literal", "expectation", "literalEscape", "text", "escapedParts", "parts", "classEscape", "inverted", "any", "end", "other", "description", "hex", "ch", "charCodeAt", "toString", "toUpperCase", "s", "descriptions", "sort", "slice", "join", "describeExpected", "describeFound", "SyntaxError", "parse", "input", "options", "peg$result", "peg$FAILED", "peg$startRuleFunctions", "start", "peg$parsestart", "peg$startRuleFunction", "peg$c3", "peg$literalExpectation", "peg$c4", "peg$c5", "peg$classExpectation", "peg$c6", "peg$c8", "peg$c11", "peg$c14", "peg$c18", "peg$c22", "peg$c25", "peg$c28", "peg$c31", "peg$c33", "peg$c35", "peg$c36", "peg$c38", "peg$c39", "a", "peg$c40", "peg$c41", "peg$c43", "peg$c44", "op", "value", "operator", "peg$c47", "peg$c48", "peg$c49", "peg$c51", "peg$c52", "peg$c53", "b", "peg$c54", "d", "match", "peg$c56", "peg$c57", "peg$c58", "peg$c59", "peg$c60", "peg$c64", "peg$c65", "peg$c66", "peg$c68", "peg$c70", "peg$c71", "peg$c73", "peg$c74", "peg$c75", "peg$c79", "peg$c82", "peg$c85", "peg$c88", "peg$c91", "peg$c94", "peg$c97", "peg$c100", "peg$c102", "peg$c104", "peg$c106", "peg$c108", "peg$c110", "peg$currPos", "peg$posDetailsCache", "line", "column", "peg$maxFailPos", "peg$maxFailExpected", "peg$silentFails", "startRule", "ignoreCase", "peg$computePosDetails", "pos", "p", "details", "peg$computeLocation", "startPos", "endPos", "startPosDetails", "endPosDetails", "offset", "peg$fail", "s0", "s1", "s2", "ss", "cached", "peg$resultsCache", "nextPos", "peg$parse_", "peg$parseselectors", "selectors", "peg$c1", "peg$parseidentifierName", "test", "char<PERSON>t", "peg$parsebinaryOp", "s3", "s4", "s5", "s6", "s7", "peg$parseselector", "concat", "map", "peg$parsesequence", "reduce", "memo", "rhs", "left", "right", "subject", "as", "peg$parseatom", "peg$parsewildcard", "peg$parseidentifier", "peg$parseattrName", "peg$parseattrEqOps", "substr", "peg$parsetype", "flgs", "peg$parseflags", "RegExp", "peg$parseregex", "peg$parseattrOps", "peg$parsestring", "leadingDecimals", "apply", "parseFloat", "peg$parsenumber", "peg$parsepath", "peg$parseattrValue", "peg$parseattr", "peg$parsefield", "peg$parsenegation", "peg$parsematches", "peg$parsehas", "nth", "peg$parsefirstChild", "nthLast", "peg$parselastChild", "parseInt", "peg$parsenthChild", "peg$parsenthLastChild", "toLowerCase", "peg$parseclass", "n", "index", "factory", "matches", "selector", "ancestry", "split", "ancestor", "inPath", "field", "remainingPath", "l", "collector", "estraverse", "unshift", "shift", "<PERSON><PERSON><PERSON>", "sibling", "adjacent", "nthChild", "side", "listProp", "startIndex", "indexOf", "lowerBound", "k", "idx", "idxFn", "subjects", "_typeof", "results", "_objectEntries", "sel", "ast", "altSubjects", "m", "succeedingAncestry", "parser", "query"], "mappings": "m6DA2BUA,EAAMC,OAGRC,EACAC,EACAC,EACAC,EACAC,EACAC,WAEKC,EAASC,OACAC,EAAKC,EAAfC,EAAM,OACLF,KAAOD,EACJA,EAAII,eAAeH,KACnBC,EAAMF,EAAIC,GAENE,EAAIF,GADW,iBAARC,GAA4B,OAARA,EAChBH,EAASG,GAETA,UAIhBC,WA0LFE,EAAUC,EAAQL,QAClBK,OAASA,OACTL,IAAMA,WAiBNM,EAAQC,EAAMC,EAAMC,EAAMC,QAC1BH,KAAOA,OACPC,KAAOA,OACPC,KAAOA,OACPC,IAAMA,WAGNC,cAuHAC,EAAOL,UACA,MAARA,IAGmB,iBAATA,GAA0C,iBAAdA,EAAKM,eAG1CC,EAAWC,EAAUf,UAClBe,IAAavB,EAAOwB,kBAAoBD,IAAavB,EAAOyB,gBAAkB,eAAiBjB,WAGlGkB,EAA2BC,EAAWC,OACtC,IAAIC,EAAIF,EAAUG,OAAS,EAAGD,GAAK,IAAKA,KACrCF,EAAUE,GAAGd,OAASa,SACf,SAGR,WAwQFG,EAASC,EAAMC,UACH,IAAId,GACHY,SAASC,EAAMC,YAQ5BC,EAAmBC,EAASC,OAC7BC,SAEJA,WA3mBgBC,EAAOC,OACnBC,EAAMC,EAAKZ,EAAGa,MAElBD,EAAMH,EAAMR,OACZD,EAAI,EAEGY,GAGCF,EAAKD,EADTI,EAAUb,GADVW,EAAOC,IAAQ,KAGXA,EAAMD,GAENX,EAAIa,EAAU,EACdD,GAAOD,EAAO,UAGfX,EA2lBEc,CAAWP,GAAQ,SAAgBQ,UACjCA,EAAMC,MAAM,GAAKV,EAAQU,MAAM,MAG1CV,EAAQW,cAAgB,CAACX,EAAQU,MAAM,GAAIV,EAAQU,MAAM,IAErDR,IAAWD,EAAON,SAClBK,EAAQW,cAAc,GAAKV,EAAOC,GAAQQ,MAAM,KAGpDR,GAAU,IACI,IACVF,EAAQW,cAAc,GAAKV,EAAOC,GAAQQ,MAAM,IAG7CV,SAvmBXnC,EAAS,CACL+C,qBAAsB,uBACtBC,kBAAmB,oBACnBC,gBAAiB,kBACjBC,aAAc,eACdC,wBAAyB,0BACzBC,gBAAiB,kBACjBC,eAAgB,iBAChBC,iBAAkB,mBAClBC,eAAgB,iBAChBC,eAAgB,iBAChBC,YAAa,cACbC,UAAW,YACXC,iBAAkB,mBAClBC,gBAAiB,kBACjBC,mBAAoB,qBACpBC,wBAAyB,0BACzBC,sBAAuB,wBACvBC,kBAAmB,oBACnBC,kBAAmB,oBACnBC,mBAAoB,qBACpBC,iBAAkB,mBAClBC,eAAgB,iBAChBC,qBAAsB,uBACtBC,yBAA0B,2BAC1BC,uBAAwB,yBACxBC,gBAAiB,kBACjBC,oBAAqB,sBACrBC,aAAc,eACdC,eAAgB,iBAChBC,eAAgB,iBAChBC,oBAAqB,sBACrBC,mBAAoB,qBACpBC,oBAAqB,sBACrBC,WAAY,aACZC,YAAa,cACbC,iBAAkB,mBAClBC,kBAAmB,oBACnBC,uBAAwB,yBACxBC,yBAA0B,2BAC1BC,gBAAiB,kBACjBC,QAAS,UACTC,iBAAkB,mBAClBC,kBAAmB,oBACnBC,iBAAkB,mBAClBC,aAAc,eACdC,iBAAkB,mBAClBC,gBAAiB,kBACjBC,cAAe,gBACftE,iBAAkB,mBAClBC,cAAe,gBACfsE,QAAS,UACTC,SAAU,WACVC,YAAa,cACbC,gBAAiB,kBACjBC,mBAAoB,qBACpBC,cAAe,gBACfC,MAAO,QACPC,gBAAiB,kBACjBC,WAAY,aACZC,yBAA0B,2BAC1BC,gBAAiB,kBACjBC,gBAAiB,kBACjBC,eAAgB,iBAChBC,eAAgB,iBAChBC,aAAc,eACdC,gBAAiB,kBACjBC,iBAAkB,mBAClBC,oBAAqB,sBACrBC,mBAAoB,qBACpBC,eAAgB,iBAChBC,cAAe,gBACfC,gBAAiB,mBAGrBlH,EAAc,CACV6C,qBAAsB,CAAC,OAAQ,SAC/BC,kBAAmB,CAAC,OAAQ,SAC5BC,gBAAiB,CAAC,YAClBC,aAAc,CAAC,YACfC,wBAAyB,CAAC,SAAU,QACpCC,gBAAiB,CAAC,YAClBC,eAAgB,CAAC,QACjBC,iBAAkB,CAAC,OAAQ,SAC3BC,eAAgB,CAAC,SACjBC,eAAgB,CAAC,SAAU,aAC3BC,YAAa,CAAC,QAAS,QACvBC,UAAW,CAAC,QACZC,iBAAkB,CAAC,KAAM,aAAc,QACvCC,gBAAiB,CAAC,KAAM,aAAc,QACtCC,mBAAoB,CAAC,OAAQ,SAC7BC,wBAAyB,CAAC,SAAU,SAAU,QAC9CC,sBAAuB,CAAC,OAAQ,aAAc,aAC9CC,kBAAmB,CAAC,SACpBC,kBAAmB,GACnBC,mBAAoB,GACpBC,iBAAkB,CAAC,OAAQ,QAC3BC,eAAgB,GAChBC,qBAAsB,CAAC,UACvBC,yBAA0B,CAAC,eAC3BC,uBAAwB,CAAC,cAAe,aAAc,UACtDC,gBAAiB,CAAC,WAAY,SAC9BC,oBAAqB,CAAC,cACtBC,aAAc,CAAC,OAAQ,OAAQ,SAAU,QACzCC,eAAgB,CAAC,OAAQ,QAAS,QAClCC,eAAgB,CAAC,OAAQ,QAAS,QAClCC,oBAAqB,CAAC,KAAM,SAAU,QACtCC,mBAAoB,CAAC,KAAM,SAAU,QACrCC,oBAAqB,CAAC,SAAU,SAAU,QAC1CC,WAAY,GACZC,YAAa,CAAC,OAAQ,aAAc,aACpCC,iBAAkB,CAAC,UACnBC,kBAAmB,CAAC,aAAc,UAClCC,uBAAwB,CAAC,SACzBC,yBAA0B,CAAC,SAC3BC,gBAAiB,CAAC,WAAY,SAC9BC,QAAS,GACTC,iBAAkB,CAAC,QAAS,QAC5BC,kBAAmB,CAAC,OAAQ,SAC5BC,iBAAkB,CAAC,SAAU,YAC7BC,aAAc,CAAC,OAAQ,YACvBC,iBAAkB,CAAC,MAAO,SAC1BC,gBAAiB,GACjBC,cAAe,CAAC,SAAU,aAC1BtE,iBAAkB,CAAC,cACnBC,cAAe,CAAC,cAChBsE,QAAS,CAAC,QACVC,SAAU,CAAC,MAAO,SAClBC,YAAa,CAAE,YACfC,gBAAiB,CAAC,YAClBC,mBAAoB,CAAC,eACrBC,cAAe,CAAC,YAChBC,MAAO,GACPC,gBAAiB,CAAC,eAAgB,SAClCC,WAAY,CAAC,OAAQ,cACrBC,yBAA0B,CAAC,MAAO,SAClCC,gBAAiB,GACjBC,gBAAiB,CAAC,SAAU,eAC5BC,eAAgB,GAChBC,eAAgB,CAAC,YACjBC,aAAc,CAAC,QAAS,UAAW,aACnCC,gBAAiB,CAAC,YAClBC,iBAAkB,CAAC,YACnBC,oBAAqB,CAAC,gBACtBC,mBAAoB,CAAC,KAAM,QAC3BC,eAAgB,CAAC,OAAQ,QACzBC,cAAe,CAAC,SAAU,QAC1BC,gBAAiB,CAAC,aAQtBnH,EAAgB,CACZoH,MALJlH,EAAQ,GAMJmH,KALJlH,EAAO,GAMHmH,OALJlH,EAAS,IAaTO,EAAU4G,UAAUC,QAAU,SAAiB1G,QACtCF,OAAO6G,KAAKlH,KAAOO,GAG5BH,EAAU4G,UAAUG,OAAS,kBACrBC,MAAMC,QAAQH,KAAK7G,cACdA,OAAOiH,OAAOJ,KAAKlH,IAAK,IACtB,SAEFiH,QAAQ,OACN,IAeftG,EAAWqG,UAAUxG,KAAO,eACpBa,EAAGkG,EAAIC,EAAGC,EAAIC,WAETC,EAAUD,EAAQlH,MACnB4G,MAAMC,QAAQ7G,OACTgH,EAAI,EAAGC,EAAKjH,EAAKc,OAAQkG,EAAIC,IAAMD,EACpCE,EAAOE,KAAKpH,EAAKgH,SAGrBE,EAAOE,KAAKpH,OAKf0G,KAAKW,UAAUrH,YACT,SAIXkH,EAAS,GACJrG,EAAI,EAAGkG,EAAKL,KAAKY,YAAYxG,OAAQD,EAAIkG,IAAMlG,EAEhDsG,EAAUD,EADAR,KAAKY,YAAYzG,GACDb,aAE9BmH,EAAUD,EAAQR,KAAKW,UAAUrH,MAC1BkH,GAKX/G,EAAWqG,UAAUnG,KAAO,kBACbqG,KAAKhF,UACJrB,MAAQqG,KAAKW,UAAUpH,MAKvCE,EAAWqG,UAAUe,QAAU,eACvB1G,EAAGkG,EAAIG,MAGXA,EAAS,GACJrG,EAAI,EAAGkG,EAAKL,KAAKY,YAAYxG,OAAQD,EAAIkG,IAAMlG,EAChDqG,EAAOE,KAAKV,KAAKY,YAAYzG,GAAGd,aAG7BmH,GAKX/G,EAAWqG,UAAU9E,QAAU,kBACpBgF,KAAKW,UAAUtH,MAG1BI,EAAWqG,UAAUgB,UAAY,SAAmBC,EAAUC,OACtDC,EAAUT,SAEdA,OAASU,EAETD,EAAYjB,KAAKW,eACZA,UAAYK,OACZG,QAAU,KACXJ,IACAP,EAASO,EAASK,KAAKpB,KAAMgB,EAAQ3H,KAAM2G,KAAKY,YAAYZ,KAAKY,YAAYxG,OAAS,GAAGf,YAExFsH,UAAYM,EAEVT,GAKX/G,EAAWqG,UAAUuB,OAAS,SAAgBC,QACrCH,QAAUG,GAKnB7H,EAAWqG,UAAUyB,KAAO,gBACnBF,OAAO3I,IAKhBe,EAAWqG,UAAX,MAAgC,gBACvBuB,OAAO5I,IAKhBgB,EAAWqG,UAAUG,OAAS,gBACrBoB,OAAO1I,IAGhBc,EAAWqG,UAAU0B,aAAe,SAASlH,EAAMC,QAC1CA,QAAUA,OACVD,KAAOA,OACPmH,WAAa,QACbb,YAAc,QACdD,UAAY,UACZQ,QAAU,UACVO,WAAa,KACO,cAArBnH,EAAQoH,cACHD,WAAaE,OAAOC,KACU,mBAArBtH,EAAQoH,gBACjBD,WAAanH,EAAQoH,eAGzBG,OAAStJ,EACV+B,EAAQsH,YACHC,OAASF,OAAOG,OAAOH,OAAOI,OAAOhC,KAAK8B,QAASvH,EAAQsH,QAwBxEpI,EAAWqG,UAAUzF,SAAW,SAAkBC,EAAMC,OAChD0H,EACAhI,EACA+G,EACA3H,EACAQ,EACAb,EACAF,EACAkC,EACAkH,EACAC,EACAjI,EACAkI,WAECZ,aAAalH,EAAMC,GAExB6H,EAAW,GAGXH,EAAWjC,KAAKyB,WAChBxH,EAAY+F,KAAKY,YAGjBqB,EAASvB,KAAK,IAAItH,EAAQkB,EAAM,KAAM,KAAM,OAC5CL,EAAUyG,KAAK,IAAItH,EAAQ,KAAM,KAAM,KAAM,OAEtC6I,EAAS7H,YACZ4G,EAAUiB,EAASI,SAEHD,MAWZpB,EAAQ3H,KAAM,IAEdL,EAAMgH,KAAKc,UAAUvG,EAAQ+H,MAAOtB,GAEhChB,KAAKmB,UAAY1I,GAASO,IAAQP,YAItCwJ,EAASvB,KAAK0B,GACdnI,EAAUyG,KAAKM,GAEXhB,KAAKmB,UAAYzI,GAAQM,IAAQN,cAKrCmB,GADAR,EAAO2H,EAAQ3H,MACCM,MAAQqH,EAAQzH,OAChC4I,EAAanC,KAAK8B,OAAOjI,IACR,KACTmG,KAAK0B,iBAGC,IAAIa,MAAM,qBAAuB1I,EAAW,KAFlDsI,EAAanC,KAAK0B,WAAWrI,OAMrC2B,EAAUmH,EAAW/H,QACbY,GAAW,IAAM,MAErBd,EAAYb,EADZP,EAAMqJ,EAAWnH,OAMbkF,MAAMC,QAAQjG,QACdgI,EAAWhI,EAAUE,QACb8H,GAAY,IAAM,MACjBhI,EAAUgI,KAIXlI,EAA2BC,EAAWC,EAAUgI,QAIhDtI,EAAWC,EAAUsI,EAAWnH,IAChCgG,EAAU,IAAI5H,EAAQc,EAAUgI,GAAW,CAACpJ,EAAKoJ,GAAW,WAAY,UACrE,CAAA,IAAIxI,EAAOQ,EAAUgI,aACxBlB,EAAU,IAAI5H,EAAQc,EAAUgI,GAAW,CAACpJ,EAAKoJ,GAAW,KAAM,MAItED,EAASvB,KAAKM,SAEf,GAAItH,EAAOQ,GAAY,IACtBF,EAA2BC,EAAWC,YAI1C+H,EAASvB,KAAK,IAAItH,EAAQc,EAAWpB,EAAK,KAAM,iBArExDkI,EAAU/G,EAAUoI,MAEpBrJ,EAAMgH,KAAKc,UAAUvG,EAAQiI,MAAOxB,GAEhChB,KAAKmB,UAAY1I,GAASO,IAAQP,UAwElDgB,EAAWqG,UAAUC,QAAU,SAAiBzF,EAAMC,OAC9C0H,EACAhI,EACAZ,EACAQ,EACAc,EACAqG,EACAhG,EACAkH,EACAC,EACAjI,EACAkI,EACAK,EACA3J,WAEK4J,EAAW1B,OACZ7G,EACArB,EACA6J,EACAxJ,KAEA6H,EAAQxH,IAAIyG,aAEZnH,EAAMkI,EAAQxH,IAAIV,IAClBK,EAAS6H,EAAQxH,IAAIL,OAGrBgB,EAAI8H,EAAS7H,OACND,SACHwI,EAAWV,EAAS9H,IACPX,KAAOmJ,EAASnJ,IAAIL,SAAWA,EAAQ,IAC3CwJ,EAASnJ,IAAIV,IAAMA,UAGtB6J,EAASnJ,IAAIV,cAM1B0I,aAAalH,EAAMC,GAExB6H,EAAW,GAGXH,EAAWjC,KAAKyB,WAChBxH,EAAY+F,KAAKY,YAMjBI,EAAU,IAAI5H,EAAQkB,EAAM,KAAM,KAAM,IAAIpB,EAH5CuJ,EAAQ,CACJnI,KAAMA,GAEmD,SAC7D2H,EAASvB,KAAKM,GACd/G,EAAUyG,KAAKM,GAERiB,EAAS7H,YACZ4G,EAAUiB,EAASI,SAEHD,WA0BDlB,KAJfvG,EAASqF,KAAKc,UAAUvG,EAAQ+H,MAAOtB,KAIXrG,IAAWlC,GAASkC,IAAWjC,GAAQiC,IAAWhC,IAE1EqI,EAAQxH,IAAIuG,QAAQpF,GACpBqG,EAAQ3H,KAAOsB,GAGfqF,KAAKmB,UAAYxI,GAAUgC,IAAWhC,IACtC+J,EAAW1B,GACXA,EAAQ3H,KAAO,MAGf2G,KAAKmB,UAAY1I,GAASkC,IAAWlC,SAC9BgK,EAAMnI,SAIjBjB,EAAO2H,EAAQ3H,QAKf4I,EAASvB,KAAK0B,GACdnI,EAAUyG,KAAKM,GAEXhB,KAAKmB,UAAYzI,GAAQiC,IAAWjC,OAIxCmB,EAAWR,EAAKM,MAAQqH,EAAQzH,OAChC4I,EAAanC,KAAK8B,OAAOjI,IACR,KACTmG,KAAK0B,iBAGC,IAAIa,MAAM,qBAAuB1I,EAAW,KAFlDsI,EAAanC,KAAK0B,WAAWrI,OAMrC2B,EAAUmH,EAAW/H,QACbY,GAAW,IAAM,MAErBd,EAAYb,EADZP,EAAMqJ,EAAWnH,OAMbkF,MAAMC,QAAQjG,QACdgI,EAAWhI,EAAUE,QACb8H,GAAY,IAAM,MACjBhI,EAAUgI,OAGXtI,EAAWC,EAAUsI,EAAWnH,IAChCgG,EAAU,IAAI5H,EAAQc,EAAUgI,GAAW,CAACpJ,EAAKoJ,GAAW,WAAY,IAAIhJ,EAAUgB,EAAWgI,QAC9F,CAAA,IAAIxI,EAAOQ,EAAUgI,aACxBlB,EAAU,IAAI5H,EAAQc,EAAUgI,GAAW,CAACpJ,EAAKoJ,GAAW,KAAM,IAAIhJ,EAAUgB,EAAWgI,IAI/FD,EAASvB,KAAKM,SAEXtH,EAAOQ,IACd+H,EAASvB,KAAK,IAAItH,EAAQc,EAAWpB,EAAK,KAAM,IAAII,EAAUG,EAAMP,cAvFxEkI,EAAU/G,EAAUoI,WAMLnB,KAJfvG,EAASqF,KAAKc,UAAUvG,EAAQiI,MAAOxB,KAIXrG,IAAWlC,GAASkC,IAAWjC,GAAQiC,IAAWhC,GAE1EqI,EAAQxH,IAAIuG,QAAQpF,GAGpBqF,KAAKmB,UAAYxI,GAAUgC,IAAWhC,GACtC+J,EAAW1B,GAGXhB,KAAKmB,UAAY1I,GAASkC,IAAWlC,SAC9BgK,EAAMnI,YA4ElBmI,EAAMnI,MAiIjBjC,EAAQC,OAASA,EACjBD,EAAQgC,SAAWA,EACnBhC,EAAQ0H,iBA3HSzF,EAAMC,UACF,IAAId,GACHsG,QAAQzF,EAAMC,IA0HpClC,EAAQuK,wBAlGgBC,EAAMC,EAAkBpI,OAEzBD,EAASM,EAAKZ,EAAG4I,EAAhCC,EAAW,OAEVH,EAAK1H,YACA,IAAIoH,MAAM,8CAIf7H,EAAON,OAAQ,IACZ0I,EAAiB1I,OAAQ,KACpBD,EAAI,EAAGY,EAAM+H,EAAiB1I,OAAQD,EAAIY,EAAKZ,GAAK,GACrDM,EAAU7B,EAASkK,EAAiB3I,KAC5BiB,cAAgB,CAAC,EAAGyH,EAAK1H,MAAM,IACvC6H,EAAStC,KAAKjG,GAElBoI,EAAKI,gBAAkBD,SAEpBH,MAGN1I,EAAI,EAAGY,EAAM+H,EAAiB1I,OAAQD,EAAIY,EAAKZ,GAAK,EACrD6I,EAAStC,KAAKlG,EAAmB5B,EAASkK,EAAiB3I,IAAKO,WAIpEqI,EAAS,EACT1I,EAASwI,EAAM,CACXP,MAAO,SAAUjJ,WACToB,EAEGsI,EAASC,EAAS5I,WACrBK,EAAUuI,EAASD,IACP3H,cAAc,GAAK/B,EAAK8B,MAAM,KAItCV,EAAQW,cAAc,KAAO/B,EAAK8B,MAAM,IACnC9B,EAAK4J,kBACN5J,EAAK4J,gBAAkB,IAE3B5J,EAAK4J,gBAAgBvC,KAAKjG,GAC1BuI,EAAS5C,OAAO2C,EAAQ,IAExBA,GAAU,SAKdA,IAAWC,EAAS5I,OACb7B,EAAcoH,MAGrBqD,EAASD,GAAQ3H,cAAc,GAAK/B,EAAK8B,MAAM,GACxC5C,EAAcqH,eAKjCmD,EAAS,EACT1I,EAASwI,EAAM,CACXL,MAAO,SAAUnJ,WACToB,EAEGsI,EAASC,EAAS5I,SACrBK,EAAUuI,EAASD,KACf1J,EAAK8B,MAAM,GAAKV,EAAQW,cAAc,MAItC/B,EAAK8B,MAAM,KAAOV,EAAQW,cAAc,IACnC/B,EAAK6J,mBACN7J,EAAK6J,iBAAmB,IAE5B7J,EAAK6J,iBAAiBxC,KAAKjG,GAC3BuI,EAAS5C,OAAO2C,EAAQ,IAExBA,GAAU,SAKdA,IAAWC,EAAS5I,OACb7B,EAAcoH,MAGrBqD,EAASD,GAAQ3H,cAAc,GAAK/B,EAAK8B,MAAM,GACxC5C,EAAcqH,eAK1BiD,GAOXxK,EAAQG,YAAcA,EACtBH,EAAQE,cAAgBA,EACxBF,EAAQoB,WAAaA,EACrBpB,EAAQ8K,iBAAmB,kBAAqB/K,EAAM,KAE/CC,GACTA,uBCrxByC+K,EAAO/K,UAC9C+K,UAEK,oBASEC,EAAgBC,EAASC,EAAUC,EAAOC,QAC5CH,QAAWA,OACXC,SAAWA,OACXC,MAAWA,OACXC,SAAWA,OACXC,KAAW,cAEuB,mBAA5BnB,MAAMoB,mBACfpB,MAAMoB,kBAAkB3D,KAAMqD,mBAdZO,EAAOzK,YAClB0K,SAAcC,YAAcF,EACrCC,EAAK/D,UAAY3G,EAAO2G,UACxB8D,EAAM9D,UAAY,IAAI+D,EAexBE,CAAaV,EAAiBd,OAE9Bc,EAAgBW,aAAe,SAAST,EAAUC,OAC5CS,EAA2B,CACzBC,QAAS,SAASC,SACT,IAAOC,EAAcD,EAAYE,MAAQ,WAGzC,SAASF,OAEZhK,EADAmK,EAAe,OAGdnK,EAAI,EAAGA,EAAIgK,EAAYI,MAAMnK,OAAQD,IACxCmK,GAAgBH,EAAYI,MAAMpK,aAAc+F,MAC5CsE,EAAYL,EAAYI,MAAMpK,GAAG,IAAM,IAAMqK,EAAYL,EAAYI,MAAMpK,GAAG,IAC9EqK,EAAYL,EAAYI,MAAMpK,UAG7B,KAAOgK,EAAYM,SAAW,IAAM,IAAMH,EAAe,KAGlEI,IAAK,SAASP,SACL,iBAGTQ,IAAK,SAASR,SACL,gBAGTS,MAAO,SAAST,UACPA,EAAYU,uBAIlBC,EAAIC,UACJA,EAAGC,WAAW,GAAGC,SAAS,IAAIC,uBAG9Bd,EAAce,UACdA,EACJpF,QAAQ,MAAO,QACfA,QAAQ,KAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,gBAAyB,SAASgF,SAAa,OAASD,EAAIC,MACpEhF,QAAQ,yBAAyB,SAASgF,SAAa,MAASD,EAAIC,eAGhEP,EAAYW,UACZA,EACJpF,QAAQ,MAAO,QACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,KAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,gBAAyB,SAASgF,SAAa,OAASD,EAAIC,MACpEhF,QAAQ,yBAAyB,SAASgF,SAAa,MAASD,EAAIC,YA6ClE,qBAtCmBxB,OAEpBpJ,EAAGmG,EANoB6D,EAKvBiB,EAAe,IAAIlF,MAAMqD,EAASnJ,YAGjCD,EAAI,EAAGA,EAAIoJ,EAASnJ,OAAQD,IAC/BiL,EAAajL,IATYgK,EASaZ,EAASpJ,GAR1C8J,EAAyBE,EAAYxK,MAAMwK,OAWlDiB,EAAaC,OAETD,EAAahL,OAAS,EAAG,KACtBD,EAAI,EAAGmG,EAAI,EAAGnG,EAAIiL,EAAahL,OAAQD,IACtCiL,EAAajL,EAAI,KAAOiL,EAAajL,KACvCiL,EAAa9E,GAAK8E,EAAajL,GAC/BmG,KAGJ8E,EAAahL,OAASkG,SAGhB8E,EAAahL,aACd,SACIgL,EAAa,QAEjB,SACIA,EAAa,GAAK,OAASA,EAAa,kBAGxCA,EAAaE,MAAM,GAAI,GAAGC,KAAK,MAClC,QACAH,EAAaA,EAAahL,OAAS,IAQxBoL,CAAiBjC,GAAY,iBAJ3BC,UACdA,EAAQ,IAAOY,EAAcZ,GAAS,IAAO,eAGMiC,CAAcjC,GAAS,WA63E9E,CACLkC,YAAarC,EACbsC,eA53EiBC,EAAOC,GACxBA,OAAsB,IAAZA,EAAqBA,EAAU,OA2JrCC,EAwH8BvC,EAAUC,EAAOC,EAjR/CsC,EAAa,GAEbC,EAAyB,CAAEC,MAAOC,IAClCC,EAAyBD,GAOzBE,EAASC,GAAuB,KAAK,GACrCC,EAAS,uBACTC,EAASC,GAAqB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,MAAM,GAAM,GACjHC,EAAS,SAAStM,UAAYA,EAAEoL,KAAK,KAErCmB,EAASL,GAAuB,KAAK,GAGrCM,EAAUN,GAAuB,KAAK,GAGtCO,EAAUP,GAAuB,KAAK,GAItCQ,EAAUR,GAAuB,KAAK,GAUtCS,EAAUT,GAAuB,KAAK,GAOtCU,EAAUV,GAAuB,KAAK,GAGtCW,EAAUX,GAAuB,KAAK,GAGtCY,EAAUZ,GAAuB,KAAK,GAEtCa,EAAUb,GAAuB,KAAK,GAEtCc,EAAU,SACVC,EAAUZ,GAAqB,CAAC,IAAK,IAAK,MAAM,GAAO,GAEvDa,EAAUhB,GAAuB,KAAK,GACtCiB,EAAU,SAASC,UAAaA,GAAK,IAAM,KAC3CC,EAAU,QACVC,EAAUjB,GAAqB,CAAC,IAAK,MAAM,GAAO,GAElDkB,EAAUrB,GAAuB,KAAK,GACtCsB,EAAU,SAASjE,EAAMkE,EAAIC,SAChB,CAAElO,KAAM,YAAa+J,KAAMA,EAAMoE,SAAUF,EAAIC,MAAOA,IAInEE,EAAU1B,GAAuB,KAAM,GACvC2B,EAAU,UACVC,EAAUzB,GAAqB,CAAC,KAAM,MAAO,GAAM,GAEnD0B,EAAU7B,GAAuB,MAAM,GACvC8B,EA6HK,CAAExO,KAAM,OA5HbyO,EAAU,SAASb,EAAGc,UAAYd,EAAIc,GACtCC,EAAU,SAASC,SACJ,CAAE5O,KAAM,UAAWkO,OA2wEf1C,EA3wEkCoD,EAAEhD,KAAK,IA4wErDJ,EAAEpF,QAAQ,UAAU,SAASyI,EAAOzD,UAClCA,OACA,UAAY,SACZ,UAAY,SACZ,UAAY,SACZ,UAAY,SACZ,UAAY,SACZ,UAAY,oBACDA,YATDI,GAxwEnBsD,EAAUpC,GAAuB,KAAK,GACtCqC,EAAU,UACVC,EAAUnC,GAAqB,CAAC,KAAM,MAAM,GAAM,GAClDoC,EAAU,SACVC,EAAUrC,GAAqB,CAAC,CAAC,IAAK,OAAO,GAAO,GAQpDsC,EAAUzC,GAAuB,SAAS,GAC1C0C,EAAU,SACVC,EAAUxC,GAAqB,CAAC,IAAK,MAAM,GAAM,GAEjDyC,EAAU5C,GAAuB,KAAK,GAEtC6C,EAAU,UACVC,EAAU3C,GAAqB,CAAC,IAAK,IAAK,IAAK,MAAM,GAAO,GAE5D4C,EAAU/C,GAAuB,KAAK,GACtCgD,EAAU,SACVC,EAAU9C,GAAqB,CAAC,MAAM,GAAM,GAQ5C+C,EAAUlD,GAAuB,SAAS,GAG1CmD,EAAUnD,GAAuB,aAAa,GAG9CoD,GAAUpD,GAAuB,SAAS,GAG1CqD,GAAUrD,GAAuB,gBAAgB,GAGjDsD,GAAUtD,GAAuB,eAAe,GAGhDuD,GAAUvD,GAAuB,eAAe,GAGhDwD,GAAUxD,GAAuB,oBAAoB,GAGrDyD,GAAWzD,GAAuB,KAAK,GAEvC0D,GAAW1D,GAAuB,aAAa,GAE/C2D,GAAW3D,GAAuB,cAAc,GAEhD4D,GAAW5D,GAAuB,eAAe,GAEjD6D,GAAW7D,GAAuB,YAAY,GAE9C8D,GAAW9D,GAAuB,WAAW,GAK7C+D,GAAuB,EAEvBC,GAAuB,CAAC,CAAEC,KAAM,EAAGC,OAAQ,IAC3CC,GAAuB,EACvBC,GAAuB,GACvBC,GAEmB,MAInB,cAAe7E,EAAS,MACpBA,EAAQ8E,aAAa3E,SACnB,IAAIzD,MAAM,mCAAqCsD,EAAQ8E,UAAY,MAG3ExE,EAAwBH,EAAuBH,EAAQ8E,oBA2BhDtE,GAAuBhC,EAAMuG,SAC7B,CAAEjR,KAAM,UAAW0K,KAAMA,EAAMuG,WAAYA,YAG3CpE,GAAqBjC,EAAOE,EAAUmG,SACtC,CAAEjR,KAAM,QAAS4K,MAAOA,EAAOE,SAAUA,EAAUmG,WAAYA,YAe/DC,GAAsBC,OACWC,EAApCC,EAAUX,GAAoBS,MAE9BE,SACKA,MAEPD,EAAID,EAAM,GACFT,GAAoBU,IAC1BA,QAIFC,EAAU,CACRV,MAFFU,EAAUX,GAAoBU,IAEZT,KAChBC,OAAQS,EAAQT,QAGXQ,EAAID,GACmB,KAAxBlF,EAAMZ,WAAW+F,IACnBC,EAAQV,OACRU,EAAQT,OAAS,GAEjBS,EAAQT,SAGVQ,WAGFV,GAAoBS,GAAOE,EACpBA,WAIFC,GAAoBC,EAAUC,OACjCC,EAAkBP,GAAsBK,GACxCG,EAAkBR,GAAsBM,SAErC,CACLlF,MAAO,CACLqF,OAAQJ,EACRZ,KAAQc,EAAgBd,KACxBC,OAAQa,EAAgBb,QAE1B5F,IAAK,CACH2G,OAAQH,EACRb,KAAQe,EAAcf,KACtBC,OAAQc,EAAcd,kBAKnBgB,GAAShI,GACZ6G,GAAcI,KAEdJ,GAAcI,KAChBA,GAAiBJ,GACjBK,GAAsB,IAGxBA,GAAoB/J,KAAK6C,aAgBlB2C,SACHsF,EAAIC,EAAIC,EAtRQC,EAwRhB7S,EAAuB,GAAdsR,GAAmB,EAC5BwB,EAASC,GAAiB/S,UAE1B8S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,SAGhBgL,EAAKpB,IACLqB,EAAKM,QACMhG,IACT2F,EAAKM,QACMjG,GACJgG,OACMhG,EAGTyF,EADAC,EAxSqB,KADPE,EAySFD,GAxSFtR,OAAeuR,EAAG,GAAK,CAAEhS,KAAM,UAAWsS,UAAWN,IAmTnEvB,GAAcoB,EACdA,EAAKzF,GAEHyF,IAAOzF,IACTyF,EAAKpB,IACLqB,EAAKM,QACMhG,IAET0F,OAAKS,GAEPV,EAAKC,GAGPI,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,YAGAO,SACHP,EAAIC,EAEJ3S,EAAuB,GAAdsR,GAAmB,EAC5BwB,EAASC,GAAiB/S,MAE1B8S,SACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,WAGhBgL,EAAK,GACiC,KAAlC5F,EAAMZ,WAAWoF,KACnBqB,EAhVS,IAiVTrB,OAEAqB,EAAK1F,EACwBwF,GAASnF,IAEjCqF,IAAO1F,GACZyF,EAAG9K,KAAK+K,GAC8B,KAAlC7F,EAAMZ,WAAWoF,KACnBqB,EAzVO,IA0VPrB,OAEAqB,EAAK1F,EACwBwF,GAASnF,WAI1CyF,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,WAGAW,SACHX,EAAIC,EAAIC,EAER5S,EAAuB,GAAdsR,GAAmB,EAC5BwB,EAASC,GAAiB/S,MAE1B8S,SACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,UAIhBiL,EAAK,GACDnF,EAAO8F,KAAKxG,EAAMyG,OAAOjC,MAC3BsB,EAAK9F,EAAMyG,OAAOjC,IAClBA,OAEAsB,EAAK3F,EACwBwF,GAAShF,IAEpCmF,IAAO3F,OACF2F,IAAO3F,GACZ0F,EAAG/K,KAAKgL,GACJpF,EAAO8F,KAAKxG,EAAMyG,OAAOjC,MAC3BsB,EAAK9F,EAAMyG,OAAOjC,IAClBA,OAEAsB,EAAK3F,EACwBwF,GAAShF,SAI1CkF,EAAK1F,SAEH0F,IAAO1F,IAET0F,EAAKhF,EAAOgF,IAEdD,EAAKC,EAELI,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,WAGAc,SACHd,EAAIC,EAAIC,EAER5S,EAAuB,GAAdsR,GAAmB,EAC5BwB,EAASC,GAAiB/S,UAE1B8S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,SAGhBgL,EAAKpB,IACLqB,EAAKM,QACMhG,GAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBsB,EA/ZO,IAgaPtB,OAEAsB,EAAK3F,EACwBwF,GAAS7E,IAEpCgF,IAAO3F,GACJgG,OACMhG,EAGTyF,EADAC,EAvayB,SA8a3BrB,GAAcoB,EACdA,EAAKzF,KAGPqE,GAAcoB,EACdA,EAAKzF,GAEHyF,IAAOzF,IACTyF,EAAKpB,IACLqB,EAAKM,QACMhG,GAC6B,MAAlCH,EAAMZ,WAAWoF,KACnBsB,EAzbM,IA0bNtB,OAEAsB,EAAK3F,EACwBwF,GAAS5E,IAEpC+E,IAAO3F,GACJgG,OACMhG,EAGTyF,EADAC,EAjcwB,WAwc1BrB,GAAcoB,EACdA,EAAKzF,KAGPqE,GAAcoB,EACdA,EAAKzF,GAEHyF,IAAOzF,IACTyF,EAAKpB,IACLqB,EAAKM,QACMhG,GAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBsB,EAndI,IAodJtB,OAEAsB,EAAK3F,EACwBwF,GAAS3E,IAEpC8E,IAAO3F,GACJgG,OACMhG,EAGTyF,EADAC,EA3dsB,YAkexBrB,GAAcoB,EACdA,EAAKzF,KAGPqE,GAAcoB,EACdA,EAAKzF,GAEHyF,IAAOzF,IACTyF,EAAKpB,GACiC,KAAlCxE,EAAMZ,WAAWoF,KACnBqB,EAzfG,IA0fHrB,OAEAqB,EAAK1F,EACwBwF,GAASnF,IAEpCqF,IAAO1F,IACT2F,EAAKK,QACMhG,EAGTyF,EADAC,EArfsB,cA4fxBrB,GAAcoB,EACdA,EAAKzF,MAMb8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,YAGAQ,SACHR,EAAIC,EAAIC,EAAIa,EAAIC,EAAIC,EAAIC,EAAIC,EAE5B7T,EAAuB,GAAdsR,GAAmB,EAC5BwB,EAASC,GAAiB/S,MAE1B8S,SACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,UAGhBgL,EAAKpB,IACLqB,EAAKmB,QACM7G,EAAY,KACrB2F,EAAK,GACLa,EAAKnC,IACLoC,EAAKT,QACMhG,GAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBqC,EA3hBM,IA4hBNrC,OAEAqC,EAAK1G,EACwBwF,GAAS1E,IAEpC4F,IAAO1G,IACT2G,EAAKX,QACMhG,IACT4G,EAAKC,QACM7G,EAETwG,EADAC,EAAK,CAACA,EAAIC,EAAIC,EAAIC,IAWtBvC,GAAcmC,EACdA,EAAKxG,KAGPqE,GAAcmC,EACdA,EAAKxG,GAEAwG,IAAOxG,GACZ2F,EAAGhL,KAAK6L,GACRA,EAAKnC,IACLoC,EAAKT,QACMhG,GAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBqC,EA9jBI,IA+jBJrC,OAEAqC,EAAK1G,EACwBwF,GAAS1E,IAEpC4F,IAAO1G,IACT2G,EAAKX,QACMhG,IACT4G,EAAKC,QACM7G,EAETwG,EADAC,EAAK,CAACA,EAAIC,EAAIC,EAAIC,IAWtBvC,GAAcmC,EACdA,EAAKxG,KAGPqE,GAAcmC,EACdA,EAAKxG,GAGL2F,IAAO3F,EAGTyF,EADAC,EA3lBO,CA2lBMA,GA3lBFoB,OA2lBMnB,EA3lBIoB,KAAI,SAAU3H,UAAYA,EAAE,QA8lBjDiF,GAAcoB,EACdA,EAAKzF,QAGPqE,GAAcoB,EACdA,EAAKzF,SAGP8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,WAGAoB,SACHpB,EAAIC,EAAIC,EAAIa,EAAIC,EAAIC,EA1mBHlF,EA4mBjBzO,EAAuB,GAAdsR,GAAmB,EAC5BwB,EAASC,GAAiB/S,MAE1B8S,SACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,UAGhBgL,EAAKpB,IACLqB,EAAKsB,QACMhH,EAAY,KACrB2F,EAAK,GACLa,EAAKnC,IACLoC,EAAKF,QACMvG,IACT0G,EAAKM,QACMhH,EAETwG,EADAC,EAAK,CAACA,EAAIC,IAOZrC,GAAcmC,EACdA,EAAKxG,GAEAwG,IAAOxG,GACZ2F,EAAGhL,KAAK6L,GACRA,EAAKnC,IACLoC,EAAKF,QACMvG,IACT0G,EAAKM,QACMhH,EAETwG,EADAC,EAAK,CAACA,EAAIC,IAOZrC,GAAcmC,EACdA,EAAKxG,GAGL2F,IAAO3F,GA1pBQwB,EA4pBJkE,EACbD,EADAC,EAAiBC,EA3pBJsB,QAAO,SAAUC,EAAMC,SACzB,CAAEvT,KAAMuT,EAAI,GAAIC,KAAMF,EAAMG,MAAOF,EAAI,MAC7C3F,KA4pBL6C,GAAcoB,EACdA,EAAKzF,QAGPqE,GAAcoB,EACdA,EAAKzF,SAGP8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,WAGAuB,SACHvB,EAAIC,EAAIC,EAAIa,EAtqBKc,EAASC,EAClBjF,EAuqBRvP,EAAuB,GAAdsR,GAAmB,EAC5BwB,EAASC,GAAiB/S,MAE1B8S,SACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,UAGhBgL,EAAKpB,GACiC,KAAlCxE,EAAMZ,WAAWoF,KACnBqB,EArrBU,IAsrBVrB,OAEAqB,EAAK1F,EACwBwF,GAASzE,IAEpC2E,IAAO1F,IACT0F,EAAK,MAEHA,IAAO1F,EAAY,IACrB2F,EAAK,IACLa,EAAKgB,QACMxH,OACFwG,IAAOxG,GACZ2F,EAAGhL,KAAK6L,GACRA,EAAKgB,UAGP7B,EAAK3F,EAEH2F,IAAO3F,GAvsBQsH,EAysBJ5B,EAxsBLpD,EAAkB,KADAiF,EAysBT5B,GAxsBFtR,OAAekT,EAAG,GAAK,CAAE3T,KAAM,WAAYsS,UAAWqB,GAChED,IAAShF,EAAEgF,SAAU,GAwsB1B7B,EADAC,EAtsBSpD,IAysBT+B,GAAcoB,EACdA,EAAKzF,QAGPqE,GAAcoB,EACdA,EAAKzF,SAGP8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,WAGA+B,SACH/B,EAEA1S,EAAuB,GAAdsR,GAAmB,EAC5BwB,EAASC,GAAiB/S,UAE1B8S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,UAGhBgL,iBAyCIA,EAAIC,EAEJ3S,EAAuB,GAAdsR,GAAmB,EAC5BwB,EAASC,GAAiB/S,UAE1B8S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,SAIsB,KAAlCoF,EAAMZ,WAAWoF,KACnBqB,EAtxBU,IAuxBVrB,OAEAqB,EAAK1F,EACwBwF,GAASxE,IAEpC0E,IAAO1F,IAET0F,EA5xB+B,CAAE9R,KAAM,WAAYkO,MA4xBtC4D,IAEfD,EAAKC,EAELI,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,GApEFgC,MACMzH,IACTyF,iBAsEEA,EAAIC,EAAIC,EAER5S,EAAuB,GAAdsR,GAAmB,EAC5BwB,EAASC,GAAiB/S,UAE1B8S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,SAGhBgL,EAAKpB,GACiC,KAAlCxE,EAAMZ,WAAWoF,KACnBqB,EAlzBU,IAmzBVrB,OAEAqB,EAAK1F,EACwBwF,GAASvE,IAEpCyE,IAAO1F,IACT0F,EAAK,MAEHA,IAAO1F,IACT2F,EAAKS,QACMpG,EAGTyF,EADAC,EA7zB6B,CAAE9R,KAAM,aAAckO,MA6zBtC6D,IAOftB,GAAcoB,EACdA,EAAKzF,GAGP8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,GA7GAiC,MACM1H,IACTyF,iBA+GAA,EAAIC,EAAQc,EAAQE,EAEpB3T,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,UAE1B8S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,SAGhBgL,EAAKpB,GACiC,KAAlCxE,EAAMZ,WAAWoF,KACnBqB,EA11BU,IA21BVrB,OAEAqB,EAAK1F,EACwBwF,GAAStE,IAEpCwE,IAAO1F,GACJgG,OACMhG,IACTwG,iBAyMAf,EAAIC,EAAQc,EAAQE,EAEpB3T,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,UAE1B8S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,SAGhBgL,EAAKpB,IACLqB,EAAKiC,QACM3H,GACJgG,OACMhG,IACTwG,iBArHAf,EAAIC,EAAIC,EAER5S,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,UAE1B8S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,SAGhBgL,EAAKpB,GACiC,KAAlCxE,EAAMZ,WAAWoF,KACnBqB,EAj+BU,IAk+BVrB,OAEAqB,EAAK1F,EACwBwF,GAASzE,IAEpC2E,IAAO1F,IACT0F,EAAK,MAEHA,IAAO1F,GAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBsB,EAx9BQ,IAy9BRtB,OAEAsB,EAAK3F,EACwBwF,GAASlE,IAEpCqE,IAAO3F,GAET0F,EAAKnE,EAAQmE,GACbD,EAAKC,IAELrB,GAAcoB,EACdA,EAAKzF,KAGPqE,GAAcoB,EACdA,EAAKzF,GAGP8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,GAwEEmC,MACM5H,GACJgG,OACMhG,IACT0G,iBAgcJjB,EAAIC,EAAQc,EAAIC,EAAIC,EAEpB3T,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,MAE1B8S,SACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,UAGhBgL,EAAKpB,GAl+CO,UAm+CRxE,EAAMgI,OAAOxD,GAAa,IAC5BqB,EAp+CU,QAq+CVrB,IAAe,IAEfqB,EAAK1F,EACwBwF,GAASzC,IAEpC2C,IAAO1F,KACJgG,OACMhG,EAAY,IACrBwG,EAAK,GACDxD,EAAQqD,KAAKxG,EAAMyG,OAAOjC,MAC5BoC,EAAK5G,EAAMyG,OAAOjC,IAClBA,OAEAoC,EAAKzG,EACwBwF,GAASvC,IAEpCwD,IAAOzG,OACFyG,IAAOzG,GACZwG,EAAG7L,KAAK8L,GACJzD,EAAQqD,KAAKxG,EAAMyG,OAAOjC,MAC5BoC,EAAK5G,EAAMyG,OAAOjC,IAClBA,OAEAoC,EAAKzG,EACwBwF,GAASvC,SAI1CuD,EAAKxG,EAEHwG,IAAOxG,IACTyG,EAAKT,QACMhG,GAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBqC,EAngDE,IAogDFrC,OAEAqC,EAAK1G,EACwBwF,GAAStC,IAEpCwD,IAAO1G,GAET0F,EAzgDuB,CAAE9R,KAAM,OAAQkO,MAygD1B0E,EAzgDmChH,KAAK,KA0gDrDiG,EAAKC,IAELrB,GAAcoB,EACdA,EAAKzF,KAOTqE,GAAcoB,EACdA,EAAKzF,QAGPqE,GAAcoB,EACdA,EAAKzF,OAGPqE,GAAcoB,EACdA,EAAKzF,SAGP8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,EAjhBMqC,MACM9H,IACT0G,iBA2jBNjB,EAAIC,EAAIC,EAAIa,EAAIC,EAvkDIsB,EAykDpBhV,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,MAE1B8S,SACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,UAGhBgL,EAAKpB,GACiC,KAAlCxE,EAAMZ,WAAWoF,KACnBqB,EAxlDU,IAylDVrB,OAEAqB,EAAK1F,EACwBwF,GAASnC,IAEpCqC,IAAO1F,EAAY,IACrB2F,EAAK,GACDrC,EAAQ+C,KAAKxG,EAAMyG,OAAOjC,MAC5BmC,EAAK3G,EAAMyG,OAAOjC,IAClBA,OAEAmC,EAAKxG,EACwBwF,GAASjC,IAEpCiD,IAAOxG,OACFwG,IAAOxG,GACZ2F,EAAGhL,KAAK6L,GACJlD,EAAQ+C,KAAKxG,EAAMyG,OAAOjC,MAC5BmC,EAAK3G,EAAMyG,OAAOjC,IAClBA,OAEAmC,EAAKxG,EACwBwF,GAASjC,SAI1CoC,EAAK3F,EAEH2F,IAAO3F,GAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBmC,EAvnDM,IAwnDNnC,OAEAmC,EAAKxG,EACwBwF,GAASnC,IAEpCmD,IAAOxG,IACTyG,iBA3FFhB,EAAIC,EAEJ3S,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,MAE1B8S,SACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,UAGhBgL,EAAK,GACDtC,EAAQkD,KAAKxG,EAAMyG,OAAOjC,MAC5BqB,EAAK7F,EAAMyG,OAAOjC,IAClBA,OAEAqB,EAAK1F,EACwBwF,GAASpC,IAEpCsC,IAAO1F,OACF0F,IAAO1F,GACZyF,EAAG9K,KAAK+K,GACJvC,EAAQkD,KAAKxG,EAAMyG,OAAOjC,MAC5BqB,EAAK7F,EAAMyG,OAAOjC,IAClBA,OAEAqB,EAAK1F,EACwBwF,GAASpC,SAI1CqC,EAAKzF,SAGP8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,EAuDIuC,MACMhI,IACTyG,EAAK,MAEHA,IAAOzG,GA9nDO+H,EAgoDCtB,EAAjBf,EAhoD+B,CAC/B9R,KAAM,SAAUkO,MAAO,IAAImG,OA+nDdtC,EA/nDuBnG,KAAK,IAAKuI,EAAOA,EAAKvI,KAAK,IAAM,KAgoDrEiG,EAAKC,IAELrB,GAAcoB,EACdA,EAAKzF,KAGPqE,GAAcoB,EACdA,EAAKzF,KAGPqE,GAAcoB,EACdA,EAAKzF,QAGPqE,GAAcoB,EACdA,EAAKzF,SAGP8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,EAzoBQyC,IAEHxB,IAAO1G,GAET0F,EAAK9D,EAAQ8D,EAAIc,EAAIE,GACrBjB,EAAKC,IAELrB,GAAcoB,EACdA,EAAKzF,KAebqE,GAAcoB,EACdA,EAAKzF,GAEHyF,IAAOzF,IACTyF,EAAKpB,IACLqB,EAAKiC,QACM3H,GACJgG,OACMhG,IACTwG,iBArNFf,EAAIC,EAAIC,EAER5S,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,UAE1B8S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,SAGhBgL,EAAKpB,GACDjD,EAAQiF,KAAKxG,EAAMyG,OAAOjC,MAC5BqB,EAAK7F,EAAMyG,OAAOjC,IAClBA,OAEAqB,EAAK1F,EACwBwF,GAASnE,IAEpCqE,IAAO1F,IACT0F,EAAK,MAEHA,IAAO1F,GAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBsB,EA95BQ,IA+5BRtB,OAEAsB,EAAK3F,EACwBwF,GAASlE,IAEpCqE,IAAO3F,GAET0F,EAAKnE,EAAQmE,GACbD,EAAKC,IAELrB,GAAcoB,EACdA,EAAKzF,KAGPqE,GAAcoB,EACdA,EAAKzF,GAEHyF,IAAOzF,IACLyB,EAAQ4E,KAAKxG,EAAMyG,OAAOjC,MAC5BoB,EAAK5F,EAAMyG,OAAOjC,IAClBA,OAEAoB,EAAKzF,EACwBwF,GAAS9D,KAI1CoE,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,GA+JI0C,MACMnI,GACJgG,OACMhG,IACT0G,iBAgDNjB,EAAIC,EAAIC,EAAIa,EAAIC,EAAIC,EAEpB3T,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,MAE1B8S,SACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,UAGhBgL,EAAKpB,GACiC,KAAlCxE,EAAMZ,WAAWoF,KACnBqB,EAjpCU,IAkpCVrB,OAEAqB,EAAK1F,EACwBwF,GAASxD,IAEpC0D,IAAO1F,EAAY,KACrB2F,EAAK,GACD1D,EAAQoE,KAAKxG,EAAMyG,OAAOjC,MAC5BmC,EAAK3G,EAAMyG,OAAOjC,IAClBA,OAEAmC,EAAKxG,EACwBwF,GAAStD,IAEpCsE,IAAOxG,IACTwG,EAAKnC,GACiC,KAAlCxE,EAAMZ,WAAWoF,KACnBoC,EA/pCM,KAgqCNpC,OAEAoC,EAAKzG,EACwBwF,GAASrD,IAEpCsE,IAAOzG,GACLH,EAAMxL,OAASgQ,IACjBqC,EAAK7G,EAAMyG,OAAOjC,IAClBA,OAEAqC,EAAK1G,EACwBwF,GAASpD,IAEpCsE,IAAO1G,GAETyG,EAAKpE,EAAQoE,EAAIC,GACjBF,EAAKC,IAELpC,GAAcmC,EACdA,EAAKxG,KAGPqE,GAAcmC,EACdA,EAAKxG,IAGFwG,IAAOxG,GACZ2F,EAAGhL,KAAK6L,GACJvE,EAAQoE,KAAKxG,EAAMyG,OAAOjC,MAC5BmC,EAAK3G,EAAMyG,OAAOjC,IAClBA,OAEAmC,EAAKxG,EACwBwF,GAAStD,IAEpCsE,IAAOxG,IACTwG,EAAKnC,GACiC,KAAlCxE,EAAMZ,WAAWoF,KACnBoC,EAtsCI,KAusCJpC,OAEAoC,EAAKzG,EACwBwF,GAASrD,IAEpCsE,IAAOzG,GACLH,EAAMxL,OAASgQ,IACjBqC,EAAK7G,EAAMyG,OAAOjC,IAClBA,OAEAqC,EAAK1G,EACwBwF,GAASpD,IAEpCsE,IAAO1G,GAETyG,EAAKpE,EAAQoE,EAAIC,GACjBF,EAAKC,IAELpC,GAAcmC,EACdA,EAAKxG,KAGPqE,GAAcmC,EACdA,EAAKxG,IAIP2F,IAAO3F,GAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBmC,EAxuCM,IAyuCNnC,OAEAmC,EAAKxG,EACwBwF,GAASxD,IAEpCwE,IAAOxG,GAET0F,EAAKnD,EAAQoD,GACbF,EAAKC,IAELrB,GAAcoB,EACdA,EAAKzF,KAGPqE,GAAcoB,EACdA,EAAKzF,QAGPqE,GAAcoB,EACdA,EAAKzF,KAEHyF,IAAOzF,KACTyF,EAAKpB,GACiC,KAAlCxE,EAAMZ,WAAWoF,KACnBqB,EAtvCQ,IAuvCRrB,OAEAqB,EAAK1F,EACwBwF,GAAS9C,IAEpCgD,IAAO1F,EAAY,KACrB2F,EAAK,GACDhD,EAAQ0D,KAAKxG,EAAMyG,OAAOjC,MAC5BmC,EAAK3G,EAAMyG,OAAOjC,IAClBA,OAEAmC,EAAKxG,EACwBwF,GAAS5C,IAEpC4D,IAAOxG,IACTwG,EAAKnC,GACiC,KAAlCxE,EAAMZ,WAAWoF,KACnBoC,EA/wCI,KAgxCJpC,OAEAoC,EAAKzG,EACwBwF,GAASrD,IAEpCsE,IAAOzG,GACLH,EAAMxL,OAASgQ,IACjBqC,EAAK7G,EAAMyG,OAAOjC,IAClBA,OAEAqC,EAAK1G,EACwBwF,GAASpD,IAEpCsE,IAAO1G,GAETyG,EAAKpE,EAAQoE,EAAIC,GACjBF,EAAKC,IAELpC,GAAcmC,EACdA,EAAKxG,KAGPqE,GAAcmC,EACdA,EAAKxG,IAGFwG,IAAOxG,GACZ2F,EAAGhL,KAAK6L,GACJ7D,EAAQ0D,KAAKxG,EAAMyG,OAAOjC,MAC5BmC,EAAK3G,EAAMyG,OAAOjC,IAClBA,OAEAmC,EAAKxG,EACwBwF,GAAS5C,IAEpC4D,IAAOxG,IACTwG,EAAKnC,GACiC,KAAlCxE,EAAMZ,WAAWoF,KACnBoC,EAtzCE,KAuzCFpC,OAEAoC,EAAKzG,EACwBwF,GAASrD,IAEpCsE,IAAOzG,GACLH,EAAMxL,OAASgQ,IACjBqC,EAAK7G,EAAMyG,OAAOjC,IAClBA,OAEAqC,EAAK1G,EACwBwF,GAASpD,IAEpCsE,IAAO1G,GAETyG,EAAKpE,EAAQoE,EAAIC,GACjBF,EAAKC,IAELpC,GAAcmC,EACdA,EAAKxG,KAGPqE,GAAcmC,EACdA,EAAKxG,IAIP2F,IAAO3F,GAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBmC,EA70CI,IA80CJnC,OAEAmC,EAAKxG,EACwBwF,GAAS9C,IAEpC8D,IAAOxG,GAET0F,EAAKnD,EAAQoD,GACbF,EAAKC,IAELrB,GAAcoB,EACdA,EAAKzF,KAGPqE,GAAcoB,EACdA,EAAKzF,QAGPqE,GAAcoB,EACdA,EAAKzF,SAIT8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,EA9RQ2C,MACMpI,IACT0G,iBAgSRjB,EAAIC,EAAIC,EAAIa,EAr2CKhF,EAAGc,EAER+F,EAq2CZtV,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,MAE1B8S,SACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,WAGhBgL,EAAKpB,GACLqB,EAAKrB,GACLsB,EAAK,GACD9C,EAAQwD,KAAKxG,EAAMyG,OAAOjC,MAC5BmC,EAAK3G,EAAMyG,OAAOjC,IAClBA,OAEAmC,EAAKxG,EACwBwF,GAAS1C,IAEjC0D,IAAOxG,GACZ2F,EAAGhL,KAAK6L,GACJ3D,EAAQwD,KAAKxG,EAAMyG,OAAOjC,MAC5BmC,EAAK3G,EAAMyG,OAAOjC,IAClBA,OAEAmC,EAAKxG,EACwBwF,GAAS1C,OAGtC6C,IAAO3F,GAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBmC,EA75CQ,IA85CRnC,OAEAmC,EAAKxG,EACwBwF,GAAS7D,IAEpC6E,IAAOxG,EAET0F,EADAC,EAAK,CAACA,EAAIa,IAGVnC,GAAcqB,EACdA,EAAK1F,KAGPqE,GAAcqB,EACdA,EAAK1F,GAEH0F,IAAO1F,IACT0F,EAAK,MAEHA,IAAO1F,EAAY,IACrB2F,EAAK,GACD9C,EAAQwD,KAAKxG,EAAMyG,OAAOjC,MAC5BmC,EAAK3G,EAAMyG,OAAOjC,IAClBA,OAEAmC,EAAKxG,EACwBwF,GAAS1C,IAEpC0D,IAAOxG,OACFwG,IAAOxG,GACZ2F,EAAGhL,KAAK6L,GACJ3D,EAAQwD,KAAKxG,EAAMyG,OAAOjC,MAC5BmC,EAAK3G,EAAMyG,OAAOjC,IAClBA,OAEAmC,EAAKxG,EACwBwF,GAAS1C,SAI1C6C,EAAK3F,EAEH2F,IAAO3F,GAj7CWsC,EAm7CHqD,EAj7CL0C,GAFK7G,EAm7CJkE,GAj7CqB,GAAGoB,OAAOwB,MAAM,GAAI9G,GAAGhC,KAAK,IAAM,GAi7CpEkG,EAh7Ca,CAAE9R,KAAM,UAAWkO,MAAOyG,WAAWF,EAAkB/F,EAAE9C,KAAK,MAi7C3EiG,EAAKC,IAELrB,GAAcoB,EACdA,EAAKzF,QAGPqE,GAAcoB,EACdA,EAAKzF,SAGP8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,EA3XU+C,MACMxI,IACT0G,iBA6XVjB,EAAIC,EAEJ3S,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,UAE1B8S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,UAIhBiL,EAAKU,QACMpG,IAET0F,EA98C+B,CAAE9R,KAAM,UAAWkO,MA88CrC4D,IAEfD,EAAKC,EAELI,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,GAlZYgD,IAGL/B,IAAO1G,GAET0F,EAAK9D,EAAQ8D,EAAIc,EAAIE,GACrBjB,EAAKC,IAELrB,GAAcoB,EACdA,EAAKzF,KAebqE,GAAcoB,EACdA,EAAKzF,GAEHyF,IAAOzF,IACTyF,EAAKpB,IACLqB,EAAKiC,QACM3H,IAET0F,EAznC8B,CAAE9R,KAAM,YAAa+J,KAynCtC+H,IAEfD,EAAKC,IAITI,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,GA/SEiD,MACM1I,GACJgG,OACMhG,GAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBqC,EAt2BE,IAu2BFrC,OAEAqC,EAAK1G,EACwBwF,GAASrE,IAEpCuF,IAAO1G,EAGTyF,EADAC,EAAac,GAGbnC,GAAcoB,EACdA,EAAKzF,KAebqE,GAAcoB,EACdA,EAAKzF,GAGP8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,GA3KEkD,MACM3I,IACTyF,iBA++BFA,EAAIC,EAAIC,EAAIa,EAAIC,EAAIC,EAAIC,EAtpDPvS,EAwpDjBrB,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,MAE1B8S,SACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,UAGhBgL,EAAKpB,GACiC,KAAlCxE,EAAMZ,WAAWoF,KACnBqB,EAhtDU,IAitDVrB,OAEAqB,EAAK1F,EACwBwF,GAAS7D,IAEpC+D,IAAO1F,MACT2F,EAAKS,QACMpG,EAAY,KACrBwG,EAAK,GACLC,EAAKpC,GACiC,KAAlCxE,EAAMZ,WAAWoF,KACnBqC,EA5tDM,IA6tDNrC,OAEAqC,EAAK1G,EACwBwF,GAAS7D,IAEpC+E,IAAO1G,IACT2G,EAAKP,QACMpG,EAETyG,EADAC,EAAK,CAACA,EAAIC,IAOZtC,GAAcoC,EACdA,EAAKzG,GAEAyG,IAAOzG,GACZwG,EAAG7L,KAAK8L,GACRA,EAAKpC,GACiC,KAAlCxE,EAAMZ,WAAWoF,KACnBqC,EAnvDI,IAovDJrC,OAEAqC,EAAK1G,EACwBwF,GAAS7D,IAEpC+E,IAAO1G,IACT2G,EAAKP,QACMpG,EAETyG,EADAC,EAAK,CAACA,EAAIC,IAOZtC,GAAcoC,EACdA,EAAKzG,GAGLwG,IAAOxG,GA1tDM5L,EA4tDFuR,EAAbD,EA3tDK,CAAE9R,KAAM,QAAS+J,KA2tDL6I,EA3tDcS,QAAO,SAASC,EAAMlC,UAAWkC,EAAOlC,EAAE,GAAKA,EAAE,KAAO5Q,IA4tDvFqR,EAAKC,IAELrB,GAAcoB,EACdA,EAAKzF,QAGPqE,GAAcoB,EACdA,EAAKzF,OAGPqE,GAAcoB,EACdA,EAAKzF,SAGP8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,EAtkCImD,MACM5I,IACTyF,iBAwkCJA,EAAIC,EAAQc,EAAQE,EAEpB3T,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,UAE1B8S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,SAGhBgL,EAAKpB,GAzvDO,UA0vDRxE,EAAMgI,OAAOxD,GAAa,IAC5BqB,EA3vDU,QA4vDVrB,IAAe,IAEfqB,EAAK1F,EACwBwF,GAAShC,IAEpCkC,IAAO1F,GACJgG,OACMhG,IACTwG,EAAKP,QACMjG,GACJgG,OACMhG,GAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBqC,EAxxDE,IAyxDFrC,OAEAqC,EAAK1G,EACwBwF,GAAStC,IAEpCwD,IAAO1G,EAGTyF,EADAC,EA/wDwB,CAAE9R,KAAM,MAAOsS,UA+wD1BM,IAGbnC,GAAcoB,EACdA,EAAKzF,KAebqE,GAAcoB,EACdA,EAAKzF,GAGP8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,GApoCMoD,MACM7I,IACTyF,iBAsoCNA,EAAIC,EAAQc,EAAQE,EAEpB3T,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,UAE1B8S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,SAGhBgL,EAAKpB,GAtzDO,cAuzDRxE,EAAMgI,OAAOxD,GAAa,IAC5BqB,EAxzDU,YAyzDVrB,IAAe,IAEfqB,EAAK1F,EACwBwF,GAAS/B,IAEpCiC,IAAO1F,GACJgG,OACMhG,IACTwG,EAAKP,QACMjG,GACJgG,OACMhG,GAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBqC,EAx1DE,IAy1DFrC,OAEAqC,EAAK1G,EACwBwF,GAAStC,IAEpCwD,IAAO1G,EAGTyF,EADAC,EA50DwB,CAAE9R,KAAM,UAAWsS,UA40D9BM,IAGbnC,GAAcoB,EACdA,EAAKzF,KAebqE,GAAcoB,EACdA,EAAKzF,GAGP8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,GAlsCQqD,MACM9I,IACTyF,iBAosCRA,EAAIC,EAAQc,EAAQE,EAEpB3T,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,UAE1B8S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,SAGhBgL,EAAKpB,GAn3DO,UAo3DRxE,EAAMgI,OAAOxD,GAAa,IAC5BqB,EAr3DU,QAs3DVrB,IAAe,IAEfqB,EAAK1F,EACwBwF,GAAS9B,KAEpCgC,IAAO1F,GACJgG,OACMhG,IACTwG,EAAKP,QACMjG,GACJgG,OACMhG,GAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBqC,EAx5DE,IAy5DFrC,OAEAqC,EAAK1G,EACwBwF,GAAStC,IAEpCwD,IAAO1G,EAGTyF,EADAC,EAz4DwB,CAAE9R,KAAM,MAAOsS,UAy4D1BM,IAGbnC,GAAcoB,EACdA,EAAKzF,KAebqE,GAAcoB,EACdA,EAAKzF,GAGP8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,GAhwCUsD,MACM/I,IACTyF,iBAkwCVA,EAAIC,EAEJ3S,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,UAE1B8S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,SA76DJ,iBAi7DRoF,EAAMgI,OAAOxD,GAAa,KAC5BqB,EAl7DU,eAm7DVrB,IAAe,KAEfqB,EAAK1F,EACwBwF,GAAS7B,KAEpC+B,IAAO1F,IAET0F,EAx7D8BsD,GAAI,IA07DpCvD,EAAKC,EAELI,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,GA7xCYwD,MACMjJ,IACTyF,iBA+xCZA,EAAIC,EAEJ3S,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,UAE1B8S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,SAz8DJ,gBA68DRoF,EAAMgI,OAAOxD,GAAa,KAC5BqB,EA98DU,cA+8DVrB,IAAe,KAEfqB,EAAK1F,EACwBwF,GAAS5B,KAEpC8B,IAAO1F,IAET0F,EAp9D8BwD,GAAQ,IAs9DxCzD,EAAKC,EAELI,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,GA1zCc0D,MACMnJ,IACTyF,iBA4zCdA,EAAIC,EAAQc,EAAIC,EAAIC,EAEpB3T,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,MAE1B8S,SACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,UAGhBgL,EAAKpB,GAx+DO,gBAy+DRxE,EAAMgI,OAAOxD,GAAa,KAC5BqB,EA1+DU,cA2+DVrB,IAAe,KAEfqB,EAAK1F,EACwBwF,GAAS3B,KAEpC6B,IAAO1F,KACJgG,OACMhG,EAAY,IACrBwG,EAAK,GACD3D,EAAQwD,KAAKxG,EAAMyG,OAAOjC,MAC5BoC,EAAK5G,EAAMyG,OAAOjC,IAClBA,OAEAoC,EAAKzG,EACwBwF,GAAS1C,IAEpC2D,IAAOzG,OACFyG,IAAOzG,GACZwG,EAAG7L,KAAK8L,GACJ5D,EAAQwD,KAAKxG,EAAMyG,OAAOjC,MAC5BoC,EAAK5G,EAAMyG,OAAOjC,IAClBA,OAEAoC,EAAKzG,EACwBwF,GAAS1C,SAI1C0D,EAAKxG,EAEHwG,IAAOxG,IACTyG,EAAKT,QACMhG,GAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBqC,EA3iEE,IA4iEFrC,OAEAqC,EAAK1G,EACwBwF,GAAStC,IAEpCwD,IAAO1G,GAET0F,EAnhEuBsD,GAAII,SAmhEd5C,EAnhEyBhH,KAAK,IAAK,KAohEhDiG,EAAKC,IAELrB,GAAcoB,EACdA,EAAKzF,KAOTqE,GAAcoB,EACdA,EAAKzF,QAGPqE,GAAcoB,EACdA,EAAKzF,OAGPqE,GAAcoB,EACdA,EAAKzF,SAGP8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,EA74CgB4D,MACMrJ,IACTyF,iBA+4ChBA,EAAIC,EAAQc,EAAIC,EAAIC,EAEpB3T,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,MAE1B8S,SACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,UAGhBgL,EAAKpB,GA1jEO,qBA2jERxE,EAAMgI,OAAOxD,GAAa,KAC5BqB,EA5jEU,mBA6jEVrB,IAAe,KAEfqB,EAAK1F,EACwBwF,GAAS1B,KAEpC4B,IAAO1F,KACJgG,OACMhG,EAAY,IACrBwG,EAAK,GACD3D,EAAQwD,KAAKxG,EAAMyG,OAAOjC,MAC5BoC,EAAK5G,EAAMyG,OAAOjC,IAClBA,OAEAoC,EAAKzG,EACwBwF,GAAS1C,IAEpC2D,IAAOzG,OACFyG,IAAOzG,GACZwG,EAAG7L,KAAK8L,GACJ5D,EAAQwD,KAAKxG,EAAMyG,OAAOjC,MAC5BoC,EAAK5G,EAAMyG,OAAOjC,IAClBA,OAEAoC,EAAKzG,EACwBwF,GAAS1C,SAI1C0D,EAAKxG,EAEHwG,IAAOxG,IACTyG,EAAKT,QACMhG,GAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBqC,EAhoEE,IAioEFrC,OAEAqC,EAAK1G,EACwBwF,GAAStC,IAEpCwD,IAAO1G,GAET0F,EArmEuBwD,GAAQE,SAqmElB5C,EArmE6BhH,KAAK,IAAK,KAsmEpDiG,EAAKC,IAELrB,GAAcoB,EACdA,EAAKzF,KAOTqE,GAAcoB,EACdA,EAAKzF,QAGPqE,GAAcoB,EACdA,EAAKzF,OAGPqE,GAAcoB,EACdA,EAAKzF,SAGP8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,EAh+CkB6D,MACMtJ,IACTyF,iBAk+ClBA,EAAIC,EAAIC,EAER5S,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,UAE1B8S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,SAGhBgL,EAAKpB,GACiC,KAAlCxE,EAAMZ,WAAWoF,KACnBqB,EA9oEU,IA+oEVrB,OAEAqB,EAAK1F,EACwBwF,GAASzB,KAEpC2B,IAAO1F,GAlpEE,cAmpEPH,EAAMgI,OAAOxD,GAAa,GAAGkF,eAC/B5D,EAAK9F,EAAMgI,OAAOxD,GAAa,GAC/BA,IAAe,IAEfsB,EAAK3F,EACwBwF,GAASxB,KAEpC2B,IAAO3F,IAxpEA,eAypELH,EAAMgI,OAAOxD,GAAa,IAAIkF,eAChC5D,EAAK9F,EAAMgI,OAAOxD,GAAa,IAC/BA,IAAe,KAEfsB,EAAK3F,EACwBwF,GAASvB,KAEpC0B,IAAO3F,IA9pEF,gBA+pEHH,EAAMgI,OAAOxD,GAAa,IAAIkF,eAChC5D,EAAK9F,EAAMgI,OAAOxD,GAAa,IAC/BA,IAAe,KAEfsB,EAAK3F,EACwBwF,GAAStB,KAEpCyB,IAAO3F,IApqEJ,aAqqEDH,EAAMgI,OAAOxD,GAAa,GAAGkF,eAC/B5D,EAAK9F,EAAMgI,OAAOxD,GAAa,GAC/BA,IAAe,IAEfsB,EAAK3F,EACwBwF,GAASrB,KAEpCwB,IAAO3F,IA1qEN,YA2qECH,EAAMgI,OAAOxD,GAAa,GAAGkF,eAC/B5D,EAAK9F,EAAMgI,OAAOxD,GAAa,GAC/BA,IAAe,IAEfsB,EAAK3F,EACwBwF,GAASpB,SAM5CuB,IAAO3F,EAGTyF,EADAC,EArrEO,CAAE9R,KAAM,QAAS+J,KAqrEVgI,IAGdtB,GAAcoB,EACdA,EAAKzF,KAGPqE,GAAcoB,EACdA,EAAKzF,GAGP8F,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,GAhjDoB+D,IAa3B1D,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,YAwPAkC,SACHlC,EAAIC,EAAIC,EAER5S,EAAuB,GAAdsR,GAAmB,GAC5BwB,EAASC,GAAiB/S,MAE1B8S,SACFxB,GAAcwB,EAAOE,QAEdF,EAAOpL,UAIhBiL,EAAK,IACLC,EAAKS,QACMpG,IAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBsB,EA5/BQ,IA6/BRtB,OAEAsB,EAAK3F,EACwBwF,GAAS7D,KAGtCgE,IAAO3F,OACF2F,IAAO3F,GACZ0F,EAAG/K,KAAKgL,IACRA,EAAKS,QACMpG,IAC6B,KAAlCH,EAAMZ,WAAWoF,KACnBsB,EAzgCI,IA0gCJtB,OAEAsB,EAAK3F,EACwBwF,GAAS7D,UAK5C+D,EAAK1F,SAEH0F,IAAO1F,IAET0F,EAAKhF,EAAOgF,IAEdD,EAAKC,EAELI,GAAiB/S,GAAO,CAAEgT,QAAS1B,GAAa5J,OAAQgL,GAEjDA,WA4vCEuD,GAAIS,SAAY,CAAE7V,KAAM,YAAa8V,MAAO,CAAE9V,KAAM,UAAWkO,MAAO2H,aACtEP,GAAQO,SAAY,CAAE7V,KAAM,iBAAkB8V,MAAO,CAAE9V,KAAM,UAAWkO,MAAO2H,QAgB1F1J,EAAaK,OAEMJ,GAAcqE,KAAgBxE,EAAMxL,cAC9C0L,QAEHA,IAAeC,GAAcqE,GAAcxE,EAAMxL,QACnDmR,GAlqEK,CAAE5R,KAAM,QAyEiB4J,EA6lE9BkH,GA7lEwCjH,EA8lExCgH,GAAiB5E,EAAMxL,OAASwL,EAAMyG,OAAO7B,IAAkB,KA9lEhB/G,EA+lE/C+G,GAAiB5E,EAAMxL,OACnB6Q,GAAoBT,GAAgBA,GAAiB,GACrDS,GAAoBT,GAAgBA,IAhmEnC,IAAInH,EACTA,EAAgBW,aAAaT,EAAUC,GACvCD,EACAC,EACAC,KA7ZaiM,OCmErB,SAASC,EAAQtW,EAAMuW,EAAUC,OACxBD,SAAmB,MACnBvW,SAAe,SACfwW,IAAYA,EAAW,IAErBD,EAASjW,UACP,kBACM,MAEN,oBACMiW,EAAS/H,MAAMyH,gBAAkBjW,EAAKM,KAAK2V,kBAEjD,YACKhW,EAAOsW,EAASlM,KAAKoM,MAAM,KAC3BC,EAAWF,EAASvW,EAAKc,OAAS,UAvCpD,SAAS4V,EAAO3W,EAAM0W,EAAUzW,MACR,IAAhBA,EAAKc,cAAuBf,IAAS0W,KACzB,MAAZA,SAA2B,MACzBE,EAAQF,EAASzW,EAAK,IACtB4W,EAAgB5W,EAAKgM,MAAM,MAC7BpF,MAAMC,QAAQ8P,GAAQ,KACjB,IAAI9V,EAAI,EAAGgW,EAAIF,EAAM7V,OAAQD,EAAIgW,IAAKhW,KACnC6V,EAAO3W,EAAM4W,EAAM9V,GAAI+V,UAAyB,SAEjD,SAEAF,EAAO3W,EAAM4W,EAAOC,GA6BhBF,CAAO3W,EAAM0W,EAAUzW,OAG7B,cACI,IAAIa,EAAI,EAAGgW,EAAIP,EAAS3D,UAAU7R,OAAQD,EAAIgW,IAAKhW,KAChDwV,EAAQtW,EAAMuW,EAAS3D,UAAU9R,GAAI0V,UAAoB,SAE1D,MAEN,eACI,IAAI1V,EAAI,EAAGgW,EAAIP,EAAS3D,UAAU7R,OAAQD,EAAIgW,IAAKhW,MAC/CwV,EAAQtW,EAAMuW,EAAS3D,UAAU9R,GAAI0V,UAAoB,SAE3D,MAEN,UACI,IAAI1V,EAAI,EAAGgW,EAAIP,EAAS3D,UAAU7R,OAAQD,EAAIgW,IAAKhW,KAChDwV,EAAQtW,EAAMuW,EAAS3D,UAAU9R,GAAI0V,UAAoB,SAE1D,MAEN,+BACKO,EAAY,cACTjW,EAAOgW,OACN5I,EAAI,GACV8I,EAAWhW,SAAShB,EAAM,CACtBiJ,eAAOjJ,EAAMF,GACK,MAAVA,GAAkBoO,EAAE+I,QAAQnX,GAC5BwW,EAAQtW,EAAMuW,EAAS3D,UAAU9R,GAAIoN,IACrC6I,EAAU1P,KAAKrH,IAGvBmJ,iBAAW+E,EAAEgJ,SACb5O,SAAU,eAVTxH,EAAI,EAAGgW,EAAIP,EAAS3D,UAAU7R,OAAQD,EAAIgW,IAAKhW,IAA/CA,YAamB,IAArBiW,EAAUhW,6CAGhB,gBACGuV,EAAQtW,EAAMuW,EAASxC,MAAOyC,IACvBF,EAAQE,EAAS,GAAID,EAASzC,KAAM0C,EAASvK,MAAM,QAI7D,gBACGqK,EAAQtW,EAAMuW,EAASxC,MAAOyC,OACzB,IAAI1V,EAAI,EAAGgW,EAAIN,EAASzV,OAAQD,EAAIgW,IAAKhW,KACtCwV,EAAQE,EAAS1V,GAAIyV,EAASzC,KAAM0C,EAASvK,MAAMnL,EAAI,WAChD,SAIZ,MAEN,gBACK4Q,EAjHlB,SAAiBlS,EAAKC,WACZ+I,EAAO/I,EAAIgX,MAAM,KACd3V,EAAI,EAAGA,EAAI0H,EAAKzH,OAAQD,IAAK,IACvB,MAAPtB,SAAsBA,EAC1BA,EAAMA,EAAIgJ,EAAK1H,WAEZtB,EA2GW2X,CAAQnX,EAAMuW,EAASlM,aACzBkM,EAAS9H,oBACR,SACW,MAALiD,MACN,WACO6E,EAAS/H,MAAMlO,UACd,eAA8B,iBAANoR,GAAkB6E,EAAS/H,MAAMA,MAAMuE,KAAKrB,OACpE,gBAAkB,UAAG6E,EAAS/H,MAAMA,mBAAekD,OACnD,cAAe6E,EAAS/H,MAAMA,UAAiBkD,SAElD,IAAIxI,6CAAsCqN,EAAS/H,MAAMlO,WAC9D,YACOiW,EAAS/H,MAAMlO,UACd,gBAAkBiW,EAAS/H,MAAMA,MAAMuE,KAAKrB,OAC5C,gBAAkB,UAAG6E,EAAS/H,MAAMA,mBAAekD,OACnD,cAAe6E,EAAS/H,MAAMA,UAAiBkD,SAElD,IAAIxI,6CAAsCqN,EAAS/H,MAAMlO,WAC9D,YAAaoR,GAAK6E,EAAS/H,MAAMA,UACjC,WAAYkD,EAAI6E,EAAS/H,MAAMA,UAC/B,WAAYkD,EAAI6E,EAAS/H,MAAMA,UAC/B,YAAakD,GAAK6E,EAAS/H,MAAMA,YAEpC,IAAItF,kCAA2BqN,EAAS9H,eAE7C,iBACM6H,EAAQtW,EAAMuW,EAASxC,MAAOyC,IACjCY,EAAQpX,EAAMuW,EAASzC,KAAM0C,EAtK3B,cAuKFD,EAASzC,KAAKE,SACdsC,EAAQtW,EAAMuW,EAASzC,KAAM0C,IAC7BY,EAAQpX,EAAMuW,EAASxC,MAAOyC,EAxK3B,kBAyKN,kBACMF,EAAQtW,EAAMuW,EAASxC,MAAOyC,IACjCa,EAASrX,EAAMuW,EAASzC,KAAM0C,EA5K5B,cA6KFD,EAASxC,MAAMC,SACfsC,EAAQtW,EAAMuW,EAASzC,KAAM0C,IAC7Ba,EAASrX,EAAMuW,EAASxC,MAAOyC,EA9K5B,kBAgLN,mBACMF,EAAQtW,EAAMuW,EAASxC,MAAOyC,IACjCc,EAAStX,EAAMwW,GAAU,kBACdD,EAASH,MAAM5H,MAAQ,SAGrC,wBACM8H,EAAQtW,EAAMuW,EAASxC,MAAOyC,IACjCc,EAAStX,EAAMwW,GAAU,SAAUzV,UACxBA,EAASwV,EAASH,MAAM5H,aAGtC,eACM+H,EAASlM,KAAK4L,mBACZ,eAC0B,cAAxBjW,EAAKM,KAAK2L,OAAO,GAAoB,OAAO,MAE9C,oBAC+B,gBAAzBjM,EAAKM,KAAK2L,OAAO,QACvB,aAC0B,YAAxBjM,EAAKM,KAAK2L,OAAO,GAAkB,OAAO,MAE5C,mBAC+B,eAAzBjM,EAAKM,KAAK2L,OAAO,KACI,YAAxBjM,EAAKM,KAAK2L,OAAO,IAEC,eAAdjM,EAAKM,OACgB,IAApBkW,EAASzV,QAAqC,iBAArByV,EAAS,GAAGlW,OAE5B,iBAAdN,EAAKM,SACR,iBACoB,wBAAdN,EAAKM,MACM,uBAAdN,EAAKM,MACS,4BAAdN,EAAKM,WAEX,IAAI4I,oCAA6BqN,EAASlM,aAGlD,IAAInB,uCAAgCqN,EAASjW,OAYvD,SAAS8W,EAAQpX,EAAMuW,EAAUC,EAAUe,OAChCzX,IAAU0W,YACZ1W,SAAiB,UAChB0I,EAAOwO,EAAW7X,YAAYW,EAAOQ,MAClCQ,EAAI,EAAGgW,EAAItO,EAAKzH,OAAQD,EAAIgW,IAAKhW,EAAG,KACnC0W,EAAW1X,EAAO0I,EAAK1H,OACzB+F,MAAMC,QAAQ0Q,GAAW,KACnBC,EAAaD,EAASE,QAAQ1X,MAChCyX,EAAa,eACbE,SAAY/V,SA5OV,cA6OF2V,GACAI,EAAa,EACb/V,EAAa6V,IAEbE,EAAaF,EAAa,EAC1B7V,EAAa4V,EAASzW,YAErB,IAAI6W,EAAID,EAAYC,EAAIhW,IAAcgW,KACnCtB,EAAQkB,EAASI,GAAIrB,EAAUC,UACxB,UAKhB,EAYX,SAASa,EAASrX,EAAMuW,EAAUC,EAAUe,OACjCzX,IAAU0W,YACZ1W,SAAiB,UAChB0I,EAAOwO,EAAW7X,YAAYW,EAAOQ,MAClCQ,EAAI,EAAGgW,EAAItO,EAAKzH,OAAQD,EAAIgW,IAAKhW,EAAG,KACnC0W,EAAW1X,EAAO0I,EAAK1H,OACzB+F,MAAMC,QAAQ0Q,GAAW,KACnBK,EAAML,EAASE,QAAQ1X,MACzB6X,EAAM,cA/QJ,cAgRFN,GAAsBM,EAAM,GAAKvB,EAAQkB,EAASK,EAAM,GAAItB,EAAUC,UAC/D,KAhRJ,eAkRHe,GAAuBM,EAAML,EAASzW,OAAS,GAAKuV,EAAQkB,EAASK,EAAM,GAAItB,EAAUC,UAClF,UAIZ,EAiBX,SAASc,EAAStX,EAAMwW,EAAUsB,OACvBhY,IAAU0W,YACZ1W,SAAiB,UAChB0I,EAAOwO,EAAW7X,YAAYW,EAAOQ,MAClCQ,EAAI,EAAGgW,EAAItO,EAAKzH,OAAQD,EAAIgW,IAAKhW,EAAG,KACnC0W,EAAW1X,EAAO0I,EAAK1H,OACzB+F,MAAMC,QAAQ0Q,GAAW,KACnBK,EAAML,EAASE,QAAQ1X,MACzB6X,GAAO,GAAKA,IAAQC,EAAMN,EAASzW,eAAkB,UAG1D,EAUX,SAASgX,EAASxB,EAAUG,MACR,MAAZH,GAAuC,UAAnByB,EAAOzB,SAA+B,GAC9C,MAAZG,IAAoBA,EAAWH,WAC7B0B,EAAU1B,EAASvC,QAAU,CAAC0C,GAAY,sGACzBwB,CAAe3B,kBAAW,iBAArC7E,OAAGyG,OACXF,EAAQ5Q,WAAR4Q,IAAgBF,EAASI,EAAW,SAANzG,EAAeyG,EAAMzB,YAEhDuB,EAkBX,SAASjX,EAASoX,EAAK7B,EAAUrV,MACxBqV,OACCC,EAAW,GACX6B,EAAcN,EAASxB,GAC7BS,EAAWhW,SAASoX,EAAK,CACrBnP,eAAOjJ,EAAMF,MACK,MAAVA,GAAkB0W,EAASS,QAAQnX,GACnCwW,EAAQtW,EAAMuW,EAAUC,MACpB6B,EAAYtX,WACP,IAAID,EAAI,EAAGgW,EAAIuB,EAAYtX,OAAQD,EAAIgW,IAAKhW,EAAG,CAC5CwV,EAAQtW,EAAMqY,EAAYvX,GAAI0V,IAC9BtV,EAAQlB,EAAMF,EAAQ0W,OAErB,IAAIoB,EAAI,EAAGU,EAAI9B,EAASzV,OAAQ6W,EAAIU,IAAKV,EAAG,KACvCW,EAAqB/B,EAASvK,MAAM2L,EAAI,GAC1CtB,EAAQE,EAASoB,GAAIS,EAAYvX,GAAIyX,IACrCrX,EAAQsV,EAASoB,GAAI9X,EAAQyY,SAKzCrX,EAAQlB,EAAMF,EAAQ0W,IAIlCrN,iBAAWqN,EAASU,SACpB5O,SAAU,eAYlB,SAAS6G,EAAMiJ,EAAK7B,OACV0B,EAAU,UAChBjX,EAASoX,EAAK7B,GAAU,SAAUvW,GAC9BiY,EAAQ5Q,KAAKrH,MAEViY,EAQX,SAAS3L,EAAMiK,UACJiC,EAAOlM,MAAMiK,GASxB,SAASkC,EAAML,EAAK7B,UACTpH,EAAMiJ,EAAK9L,EAAMiK,WAG5BkC,EAAMnM,MAAQA,EACdmM,EAAMtJ,MAAQA,EACdsJ,EAAMzX,SAAWA,EACjByX,EAAMnC,QAAUA,EAChBmC,EAAMA,MAAQA"}