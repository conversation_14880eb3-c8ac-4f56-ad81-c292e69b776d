{"name": "default-gateway", "version": "5.0.5", "description": "Get the default network gateway, cross-platform.", "author": "silverwind <<EMAIL>>", "repository": "silverwind/default-gateway", "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "eslint *.js && node --pending-deprecation --trace-deprecation --throw-deprecation --trace-warnings test.js"}, "engines": {"node": "^8.12.0 || >=9.7.0"}, "dependencies": {"execa": "^3.3.0"}, "devDependencies": {"eslint": "6.6.0", "eslint-config-silverwind": "5.0.0", "updates": "9.0.1", "ver": "6.0.2"}, "files": ["index.js", "android.js", "darwin.js", "freebsd.js", "linux.js", "openbsd.js", "sunos.js", "win32.js", "ibmi.js"], "keywords": ["default gateway", "network", "default", "gateway", "routing", "route"]}