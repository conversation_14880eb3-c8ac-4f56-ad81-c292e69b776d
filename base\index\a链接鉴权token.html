<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <title>a 标签触发 ajax</title>
    <script src="https://code.jquery.com/jquery-3.1.1.min.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>

<body> <a href="#" class="a_post">发起 POST 请求</a>
    <script>         
        //方式 1        
        $(".a_post").on("click",function(event){        event.preventDefault();
            //使 a 自带的方法失效，即无法调整到 href 中的 URL        
            var url='http://localhost:8050/file/export/snapEventVO';   
            //请求的 URl            
            var xhr = new XMLHttpRequest();     
            //定义 http 请求对象            
            xhr.open("POST", url, true);                    
            xhr.setRequestHeader("Content-type","application/x-www-form-urlencoded");            
            xhr.send();            
            xhr.responseType = "blob";  
            // 返回类型 blob            
            xhr.onload = function() {   
                // 定义请求完成的处理函数，请求前也可以增加加载框/禁用下载按钮逻辑                
                if (this.status===200) {                    
                    var blob = this.response;                    
                    //alert(this.readyState);                    
                    //alert(xhr.getAllResponseHeaders());                    
                    console.log(xhr.getResponseHeader("content-disposition"))                    
                    let temp = xhr.getResponseHeader("content-disposition").split(";")[1].split("filename=")[1]; 
                    var fileName = decodeURIComponent(temp);                    
                    //var hh = xhh.getResponseHeader("fileName");                    
                    //var fileName = this.response.headers["content-disposition"].split(";")[1].split("filename=")[1];                    //console.log("fileName="+fileName)                    //console.log(xhr.getResponseHeader("content-disposition"))                    var reader = new FileReader();                    reader.readAsDataURL(blob);  // 转换为 base64，可以直接放入 a 标签 href                    reader.onload=function (e) {                        console.log(e);         //查看有没有接收到数据流                        // 转换完成，创建一个 a 标签用于下载                        var a = document.createElement('a');                        a.download=fileName+".xlsx";            //自定义下载文件名称                        a.href = e.target.result;                        $("body").append(a);    // 修复 firefox 中无法触发 click                        a.click();                        //$(a).remove();                    }                }                else{                    alert("出现了未知的错误!");                }            }        });        //方式 2        /*$(".a_post").on("click",function(event){            event.preventDefault();//使 a 自带的方法失效，即无法调整到 href 中的 URL            axios({                method: 'post',                url: "http://localhost:8050/file/export/snapEventVO",                responseType: 'blob'            }).then((res) => {                const link = document.createElement('a')                let blob = new Blob([res.data],{type: 'application/vnd.ms-excel'});            //获取 heads 中的 filename 文件名            var aa = res.headers["content-disposition"]            let temp = res.headers["content-disposition"].split(";")[1].split("filename=")[1];            var fileName = decodeURIComponent(temp);            console.log(fileName)            link.style.display = 'none'            link.href = URL.createObjectURL(blob);            link.setAttribute('download', fileName)            document.body.appendChild(link)            link.click()            document.body.removeChild(link)        }).catch(error => {                console.log(error)            })        });*/     </script>
</body>