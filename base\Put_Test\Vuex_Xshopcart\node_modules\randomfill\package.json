{"name": "randomfill", "version": "1.0.4", "description": "random fill from browserify stand alone", "main": "index.js", "scripts": {"test": "standard && node test.js | tspec", "phantom": "zuul --phantom -- test.js", "local": "zuul --local --no-coverage -- test.js"}, "repository": {"type": "git", "url": "https://github.com/crypto-browserify/randomfill.git"}, "keywords": ["crypto", "random"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/randomfill/issues"}, "homepage": "https://github.com/crypto-browserify/randomfill", "browser": "browser.js", "devDependencies": {"phantomjs": "^1.9.9", "standard": "^10.0.2", "tap-spec": "^2.1.2", "tape": "^4.6.3", "zuul": "^3.7.2"}, "dependencies": {"randombytes": "^2.0.5", "safe-buffer": "^5.1.0"}}