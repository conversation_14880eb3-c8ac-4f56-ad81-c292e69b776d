{"name": "@types/serve-static", "version": "1.13.5", "description": "TypeScript definitions for serve-static", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/urossmolnik", "githubUsername": "urossmolnik"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LinusU", "githubUsername": "LinusU"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/serve-static"}, "scripts": {}, "dependencies": {"@types/express-serve-static-core": "*", "@types/mime": "*"}, "typesPublisherContentHash": "be710541b956835e6ef2e8e82d2d69fe6c61944dd361fec322155ed3ea7344e2", "typeScriptVersion": "3.0"}