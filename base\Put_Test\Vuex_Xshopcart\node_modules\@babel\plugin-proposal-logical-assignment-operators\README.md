# @babel/plugin-proposal-logical-assignment-operators

> Transforms logical assignment operators into short-circuited assignments

See our website [@babel/plugin-proposal-logical-assignment-operators](https://babeljs.io/docs/en/next/babel-plugin-proposal-logical-assignment-operators.html) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-proposal-logical-assignment-operators
```

or using yarn:

```sh
yarn add @babel/plugin-proposal-logical-assignment-operators --dev
```
