{"name": "extglob", "description": "Extended glob support for JavaScript. Adds (almost) the expressive power of regular expressions to glob patterns.", "version": "2.0.4", "homepage": "https://github.com/micromatch/extglob", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON> (https://twitter.com/doowb)", "<PERSON> (http://badassjs.com)", "<PERSON><PERSON> (https://www.isiahmeadows.com)", "<PERSON> (http://twitter.com/jonschlink<PERSON>)", "<PERSON> (http://mattbierner.com)", "<PERSON><PERSON><PERSON> (https://shinnn.github.io)"], "repository": "micromatch/extglob", "bugs": {"url": "https://github.com/micromatch/extglob/issues"}, "license": "MIT", "files": ["index.js", "lib"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-eslint": "^4.0.0", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.1", "helper-changelog": "^0.3.0", "is-windows": "^1.0.1", "micromatch": "^3.0.4", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.0", "multimatch": "^2.1.0"}, "keywords": ["bash", "extended", "extglob", "glob", "globbing", "ksh", "match", "pattern", "patterns", "regex", "test", "wildcard"], "lintDeps": {"devDependencies": {"files": {"options": {"ignore": ["benchmark/**/*.js"]}}}}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "related": {"list": ["braces", "expand-brackets", "expand-range", "fill-range", "micromatch"]}, "helpers": ["helper-changelog"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}}