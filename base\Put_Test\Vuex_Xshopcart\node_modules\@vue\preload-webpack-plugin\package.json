{"name": "@vue/preload-webpack-plugin", "version": "1.1.2", "description": "A webpack plugin for injecting <link rel='preload|prefecth'> into HtmlWebpackPlugin pages, with async chunk support", "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/addyosmani)", "license": "Apache-2.0", "bugs": {"url": "https://github.com/vuejs/preload-webpack-plugin/issues"}, "homepage": "https://github.com/vuejs/preload-webpack-plugin", "repository": {"type": "git", "url": "https://github.com/vuejs/preload-webpack-plugin.git"}, "keywords": ["webpack", "plugin", "html-webpack-plugin", "script", "preload", "resource hints"], "engines": {"node": ">=6.0.0"}, "scripts": {"lint": "eslint --format=codeframe .", "test": "npm run lint && jasmine test/unit/* test/e2e/index.js", "coverage": "nyc npm run test", "prepublishOnly": "npm run test"}, "main": "src/index.js", "files": ["src"], "devDependencies": {"eslint": "^4.19.1", "eslint-plugin-vue-libs": "^2.1.0", "html-webpack-plugin": "^3.2.0", "jasmine": "^3.1.0", "jsdom": "^11.9.0", "memory-fs": "^0.4.1", "nyc": "^11.7.1", "rimraf": "^2.6.2", "webpack": "^4.0.0"}, "peerDependencies": {"html-webpack-plugin": ">=2.26.0", "webpack": ">=4.0.0"}}