{"name": "regexp.prototype.flags", "version": "1.3.0", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "posttest": "npx aud", "tests-only": "es-shim-api --bound && node --harmony --es-staging test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "eslint .", "eccheck": "eclint check *.js **/*.js > /dev/null"}, "repository": {"type": "git", "url": "git://github.com/es-shims/RegExp.prototype.flags.git"}, "keywords": ["RegExp.prototype.flags", "regex", "regular expression", "ES6", "shim", "flag", "flags", "regexp", "RegExp#flags", "polyfill", "es-shim API"], "dependencies": {"define-properties": "^1.1.3", "es-abstract": "^1.17.0-next.1"}, "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^15.0.2", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^6.7.2", "has": "^1.0.3", "tape": "^4.11.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}}