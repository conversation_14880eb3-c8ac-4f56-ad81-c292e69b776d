{"name": "no-case", "version": "2.3.2", "description": "Remove case from a string", "main": "no-case.js", "typings": "no-case.d.ts", "files": ["no-case.js", "no-case.d.ts", "vendor", "LICENSE"], "scripts": {"lint": "standard", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec --bail", "test": "npm run lint && npm run test-cov", "build": "node build.js"}, "standard": {"ignore": ["coverage/**"]}, "repository": {"type": "git", "url": "git://github.com/blakeembrey/no-case.git"}, "keywords": ["no", "case", "space", "lower", "trim"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "bugs": {"url": "https://github.com/blakeembrey/no-case/issues"}, "homepage": "https://github.com/blakeembrey/no-case", "devDependencies": {"chai": "^4.0.2", "istanbul": "^0.4.3", "jsesc": "^2.2.0", "mocha": "^3.0.0", "standard": "^10.0.2", "xregexp": "^3.1.1"}, "dependencies": {"lower-case": "^1.1.1"}}