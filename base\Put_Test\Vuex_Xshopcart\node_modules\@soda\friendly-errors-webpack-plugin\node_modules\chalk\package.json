{"name": "chalk", "version": "1.1.3", "description": "Terminal string styling done right. Much color.", "license": "MIT", "repository": "chalk/chalk", "maintainers": ["Sindre Sorhus <<EMAIL>> (sindresorhus.com)", "<PERSON> <<EMAIL>> (jbnicolai.com)", "<PERSON><PERSON> <<EMAIL>> (github.com/qix-)"], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && mocha", "bench": "matcha benchmark.js", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}, "devDependencies": {"coveralls": "^2.11.2", "matcha": "^0.6.0", "mocha": "*", "nyc": "^3.0.0", "require-uncached": "^1.0.2", "resolve-from": "^1.0.0", "semver": "^4.3.3", "xo": "*"}, "xo": {"envs": ["node", "mocha"]}}