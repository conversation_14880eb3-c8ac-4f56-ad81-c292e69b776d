{"name": "@babel/helper-wrap-function", "version": "7.10.4", "description": "Helper to wrap functions inside a function call.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-wrap-function"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-function-name": "^7.10.4", "@babel/template": "^7.10.4", "@babel/traverse": "^7.10.4", "@babel/types": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df"}