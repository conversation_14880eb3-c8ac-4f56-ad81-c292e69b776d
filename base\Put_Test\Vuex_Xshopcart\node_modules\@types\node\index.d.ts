// Type definitions for non-npm package Node.js 14.10
// Project: http://nodejs.org/
// Definitions by: Microsoft TypeScript <https://github.com/Microsoft>
//                 DefinitelyTyped <https://github.com/DefinitelyTyped>
//                 <PERSON> <https://github.com/jkomyno>
//                 <PERSON>. <https://github.com/a-tarasyuk>
//                 Alvis HT Tang <https://github.com/alvis>
//                 <PERSON> <https://github.com/r3nya>
//                 <PERSON> <https://github.com/btoueg>
//                 <PERSON> <https://github.com/brunoscheufler>
//                 Chigozirim C. <https://github.com/smac89>
//                 <PERSON> <https://github.com/touffy>
//                 Deividas Bakanas <https://github.com/DeividasBakanas>
//                 <PERSON> <https://github.com/eyqs>
//                 Flarna <https://github.com/Flarna>
//                 <PERSON><PERSON> <https://github.com/<PERSON><PERSON>-<PERSON>-<PERSON>>
//                 Hoàng Văn <PERSON> <https://github.com/KSXGitHub>
//                 Huw <https://github.com/hoo29>
//                 Kelvin Jin <https://github.com/kjin>
//                 Klaus Meinhardt <https://github.com/ajafff>
//                 Lishude <https://github.com/islishude>
//                 Mariusz Wiktorczyk <https://github.com/mwiktorczyk>
//                 Mohsen Azimi <https://github.com/mohsen1>
//                 Nicolas Even <https://github.com/n-e>
//                 Nikita Galkin <https://github.com/galkin>
//                 Parambir Singh <https://github.com/parambirs>
//                 Sebastian Silbermann <https://github.com/eps1lon>
//                 Simon Schick <https://github.com/SimonSchick>
//                 Thomas den Hollander <https://github.com/ThomasdenH>
//                 Wilco Bakker <https://github.com/WilcoBakker>
//                 wwwy3y3 <https://github.com/wwwy3y3>
//                 Samuel Ainsworth <https://github.com/samuela>
//                 Kyle Uehlein <https://github.com/kuehlein>
//                 Jordi Oliveras Rovira <https://github.com/j-oliveras>
//                 Thanik Bhongbhibhat <https://github.com/bhongy>
//                 Marcin Kopacz <https://github.com/chyzwar>
//                 Trivikram Kamat <https://github.com/trivikr>
//                 Minh Son Nguyen <https://github.com/nguymin4>
//                 Junxiao Shi <https://github.com/yoursunny>
//                 Ilia Baryshnikov <https://github.com/qwelias>
//                 ExE Boss <https://github.com/ExE-Boss>
//                 Surasak Chaisurin <https://github.com/Ryan-Willpower>
//                 Piotr Błażejewicz <https://github.com/peterblazejewicz>
//                 Anna Henningsen <https://github.com/addaleax>
//                 Jason Kwok <https://github.com/JasonHK>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped

// NOTE: These definitions support NodeJS and TypeScript 3.7.
// Typically type modifications should be made in base.d.ts instead of here

/// <reference path="base.d.ts" />

// NOTE: TypeScript version-specific augmentations can be found in the following paths:
//          - ~/base.d.ts         - Shared definitions common to all TypeScript versions
//          - ~/index.d.ts        - Definitions specific to TypeScript 2.8
//          - ~/ts3.5/index.d.ts  - Definitions specific to TypeScript 3.5

// NOTE: Augmentations for TypeScript 3.5 and later should use individual files for overrides
//       within the respective ~/ts3.5 (or later) folder. However, this is disallowed for versions
//       prior to TypeScript 3.5, so the older definitions will be found here.
