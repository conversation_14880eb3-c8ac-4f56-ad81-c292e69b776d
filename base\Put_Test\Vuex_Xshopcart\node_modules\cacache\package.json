{"name": "cacache", "publishConfig": {"tag": "legacy"}, "version": "12.0.4", "cache-version": {"content": "2", "index": "5"}, "description": "Fast, fault-tolerant, cross-platform, disk-based, data-agnostic, content-addressable cache.", "main": "index.js", "files": ["*.js", "lib", "locales"], "scripts": {"benchmarks": "node test/benchmarks", "prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "pretest": "standard", "release": "standard-version -s", "test": "cross-env CACACHE_UPDATE_LOCALE_FILES=true tap --coverage --nyc-arg=--all -J test/*.js", "test-docker": "docker run -it --rm --name pacotest -v \"$PWD\":/tmp -w /tmp node:latest npm test"}, "repository": "https://github.com/npm/cacache", "keywords": ["cache", "caching", "content-addressable", "sri", "sri hash", "subresource integrity", "cache", "storage", "store", "file store", "filesystem", "disk cache", "disk storage"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "twitter": "maybekatz"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "twitter": "char<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>", "twitter": "ReBeccaOrg"}], "license": "ISC", "dependencies": {"bluebird": "^3.5.5", "chownr": "^1.1.1", "figgy-pudding": "^3.5.1", "glob": "^7.1.4", "graceful-fs": "^4.1.15", "infer-owner": "^1.0.3", "lru-cache": "^5.1.1", "mississippi": "^3.0.0", "mkdirp": "^0.5.1", "move-concurrently": "^1.0.1", "promise-inflight": "^1.0.1", "rimraf": "^2.6.3", "ssri": "^6.0.1", "unique-filename": "^1.1.1", "y18n": "^4.0.0"}, "devDependencies": {"benchmark": "^2.1.4", "chalk": "^2.4.2", "cross-env": "^5.1.4", "require-inject": "^1.4.4", "standard": "^12.0.1", "standard-version": "^6.0.1", "tacks": "^1.3.0", "tap": "^12.7.0"}, "config": {"nyc": {"exclude": ["node_modules/**", "test/**"]}}}