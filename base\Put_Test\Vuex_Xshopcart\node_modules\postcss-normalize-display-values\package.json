{"name": "postcss-normalize-display-values", "version": "4.0.2", "description": "Normalize multiple value display syntaxes into single values.", "main": "dist/index.js", "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "files": ["LICENSE-MIT", "dist"], "license": "MIT", "dependencies": {"cssnano-util-get-match": "^4.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "homepage": "https://github.com/cssnano/cssnano", "engines": {"node": ">=6.9.0"}}