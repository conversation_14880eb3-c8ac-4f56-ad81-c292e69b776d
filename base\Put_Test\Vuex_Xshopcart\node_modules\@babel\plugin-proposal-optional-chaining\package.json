{"name": "@babel/plugin-proposal-optional-chaining", "version": "7.11.0", "description": "Transform optional chaining operators into a series of nil checks", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-optional-chaining"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-skip-transparent-expression-wrappers": "^7.11.0", "@babel/plugin-syntax-optional-chaining": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.11.0", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-transform-block-scoping": "^7.10.4"}}