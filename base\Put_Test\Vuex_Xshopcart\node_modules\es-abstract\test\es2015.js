'use strict';

var ES = require('../').ES2015;
var boundES = require('./helpers/createBoundESNamespace')(ES);

var ops = require('../operations/2015');

var expectedMissing = [
	'abs',
	'AddRestrictedFunctionProperties',
	'AllocateArrayBuffer',
	'AllocateTypedArray',
	'BoundFunctionCreate',
	'Canonicalize',
	'CharacterRange',
	'CharacterSetMatcher',
	'CloneArrayBuffer',
	'Completion',
	'Construct',
	'CopyDataBlockBytes',
	'CreateArrayFromList',
	'CreateBuiltinFunction',
	'CreateByteDataBlock',
	'CreateDynamicFunction',
	'CreateIntrinsics',
	'CreateListIterator',
	'CreateMapIterator',
	'CreateMappedArgumentsObject',
	'CreatePerIterationEnvironment',
	'CreateRealm',
	'CreateSetIterator',
	'CreateUnmappedArgumentsObject',
	'Decode',
	'DetachArrayBuffer',
	'Encode',
	'EnqueueJob',
	'EscapeRegExpPattern',
	'EvalDeclarationInstantiation',
	'EvaluateCall',
	'EvaluateDirectCall',
	'EvaluateNew',
	'floor',
	'ForBodyEvaluation',
	'ForIn/OfBodyEvaluation',
	'ForIn/OfHeadEvaluation',
	'FulfillPromise',
	'FunctionAllocate',
	'FunctionCreate',
	'FunctionInitialize',
	'GeneratorFunctionCreate',
	'GeneratorResume',
	'GeneratorResumeAbrupt',
	'GeneratorStart',
	'GeneratorValidate',
	'GeneratorYield',
	'GetBase',
	'GetFunctionRealm',
	'GetGlobalObject',
	'GetIdentifierReference',
	'GetModuleNamespace',
	'GetNewTarget',
	'GetReferencedName',
	'GetSuperConstructor',
	'GetTemplateObject',
	'GetThisEnvironment',
	'GetThisValue',
	'GetValue',
	'GetValueFromBuffer',
	'GetViewValue',
	'HasPrimitiveBase',
	'HostResolveImportedModule',
	'ImportedLocalNames',
	'InitializeHostDefinedRealm',
	'InitializeReferencedBinding',
	'IntegerIndexedElementGet',
	'IntegerIndexedElementSet',
	'IntegerIndexedObjectCreate',
	'InternalizeJSONProperty',
	'IsAnonymousFunctionDefinition',
	'IsCompatiblePropertyDescriptor',
	'IsDetachedBuffer',
	'IsInTailPosition',
	'IsLabelledFunction',
	'IsPropertyReference',
	'IsStrictReference',
	'IsSuperReference',
	'IsUnresolvableReference',
	'IsWordChar',
	'LocalTime',
	'LoopContinues',
	'MakeArgGetter',
	'MakeArgSetter',
	'MakeClassConstructor',
	'MakeConstructor',
	'MakeMethod',
	'MakeSuperPropertyReference',
	'max',
	'min',
	'ModuleNamespaceCreate',
	'msPerDay',
	'NewDeclarativeEnvironment',
	'NewFunctionEnvironment',
	'NewGlobalEnvironment',
	'NewModuleEnvironment',
	'NewObjectEnvironment',
	'NewPromiseCapability',
	'NormalCompletion',
	'ObjectDefineProperties',
	'OrdinaryCallBindThis',
	'OrdinaryCallEvaluateBody',
	'OrdinaryCreateFromConstructor',
	'ParseModule',
	'PerformEval',
	'PerformPromiseAll',
	'PerformPromiseRace',
	'PerformPromiseThen',
	'PrepareForOrdinaryCall',
	'PrepareForTailCall',
	'ProxyCreate',
	'PutValue',
	'QuoteJSONString',
	'RegExpAlloc',
	'RegExpBuiltinExec',
	'RegExpCreate',
	'RegExpInitialize',
	'RejectPromise',
	'RepeatMatcher',
	'ResolveBinding',
	'ResolveThisBinding',
	'SerializeJSONArray',
	'SerializeJSONObject',
	'SerializeJSONProperty',
	'SetDefaultGlobalBindings',
	'SetRealmGlobalObject',
	'SetValueInBuffer',
	'SetViewValue',
	'sign',
	'SortCompare',
	'SplitMatch',
	'StringCreate',
	'StringGetIndexProperty',
	'TriggerPromiseReactions',
	'TypedArrayFrom',
	'UpdateEmpty',
	'UTC'
];

require('./tests').es2015(boundES, ops, expectedMissing);

require('./helpers/runManifestTest')(require('tape'), ES, 2015);
