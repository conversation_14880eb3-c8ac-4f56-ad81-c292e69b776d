module.exports={A:{A:{"2":"L H G E A B jB"},B:{"1":"8","2":"C D e K I N","130":"J"},C:{"1":"1 2 3 9 KB JB CB DB EB O GB HB IB","2":"0 4 5 7 gB BB F L H G E A B C D e K I N J P Q R S T U V W X Y Z a b c d f g h i j k l m n o M q r s t u v w x y z aB ZB"},D:{"1":"8 JB CB DB EB O GB HB IB TB PB OB mB MB QB RB","2":"0 1 2 3 4 5 7 9 F L H G E A B C D e K I N J P Q R S T U V W X Y Z a b c d f g h i j k l m n o M q r s t u v w x y z KB"},E:{"2":"4 6 F L H G E A B C SB LB UB VB WB XB YB p","16":"D bB"},F:{"1":"0 1 2 3 t u v w x y z","2":"5 6 7 E B C K I N J P Q R S T U V W X Y Z a b c d f g h i j k l m n o M q r s cB dB eB fB p AB hB"},G:{"2":"G D LB iB FB kB lB NB nB oB pB qB rB sB tB uB vB"},H:{"2":"wB"},I:{"1":"O","2":"BB F xB yB zB 0B FB 1B 2B"},J:{"2":"H A"},K:{"2":"6 A B C M p AB"},L:{"1":"MB"},M:{"1":"O"},N:{"2":"A B"},O:{"2":"3B"},P:{"2":"F 4B 5B 6B 7B 8B"},Q:{"2":"9B"},R:{"2":"AC"},S:{"2":"BC"}},B:6,C:"Intl.PluralRules API"};
