{"name": "portfinder", "description": "A simple tool to find an open port on the current machine", "version": "1.0.28", "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "**************:http-party/node-portfinder.git"}, "keywords": ["http", "ports", "utilities"], "files": ["lib"], "dependencies": {"async": "^2.6.2", "debug": "^3.1.1", "mkdirp": "^0.5.5"}, "devDependencies": {"glob": "^7.1.4", "vows": "^0.8.2"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.12.0"}, "license": "MIT"}