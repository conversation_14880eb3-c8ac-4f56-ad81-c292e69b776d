{"name": "object.getownpropertydescriptors", "version": "2.1.0", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ES2017 spec-compliant shim for `Object.getOwnPropertyDescriptors` that works in ES5.", "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run --silent lint && es-shim-api --bound", "test": "npm run --silent tests-only", "posttest": "npx aud", "tests-only": "npm run --silent test:shimmed && npm run --silent test:module", "test:shimmed": "node test/shimmed", "test:module": "node test", "coverage": "covert test/*.js", "coverage:quiet": "covert test/*.js --quiet", "lint": "eslint ."}, "repository": {"type": "git", "url": "git://github.com/es-shims/object.getownpropertydescriptors.git"}, "keywords": ["Object.getOwnPropertyDescriptors", "descriptor", "property descriptor", "ES8", "ES2017", "shim", "polyfill", "getOwnPropertyDescriptor", "es-shim API"], "dependencies": {"define-properties": "^1.1.3", "es-abstract": "^1.17.0-next.1"}, "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^15.0.2", "covert": "^1.1.1", "eslint": "^6.7.2", "functions-have-names": "^1.2.0", "replace": "^1.1.1", "safe-publish-latest": "^1.1.4", "semver": "^6.3.0", "tape": "^4.11.0"}, "testling": {"files": ["test/index.js", "test/shimmed.js"], "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/5.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/12.0..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.8"}}