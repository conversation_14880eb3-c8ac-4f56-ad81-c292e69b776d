{"name": "hsla-regex", "description": "Regex for matching HSLA colors.", "author": "<PERSON>", "version": "1.0.0", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "mocha test"}, "repository": {"type": "git", "url": "https://github.com/regexps/hsla-regex.git"}, "keywords": ["hsla", "regex", "regexp", "color", "css"], "license": "MIT", "bugs": {"url": "https://github.com/regexps/hsla-regex/issues"}, "homepage": "https://github.com/regexps/hsla-regex", "dependencies": {}, "devDependencies": {"mocha": "*"}}