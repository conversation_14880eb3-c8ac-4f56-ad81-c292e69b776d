{"name": "regexpu-core", "version": "4.7.0", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "homepage": "https://mths.be/regexpu", "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regexpu-core.git"}, "bugs": "https://github.com/mathiasbynens/regexpu-core/issues", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/character-class-escape-sets.js", "data/iu-mappings.js"], "scripts": {"build": "node scripts/iu-mappings.js && node scripts/character-class-escape-sets.js", "test": "mocha tests", "cover": "istanbul cover --report html node_modules/.bin/_mocha tests -- -u exports -R spec"}, "dependencies": {"regenerate": "^1.4.0", "regenerate-unicode-properties": "^8.2.0", "regjsgen": "^0.5.1", "regjsparser": "^0.6.4", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.2.0"}, "devDependencies": {"codecov": "^3.6.5", "istanbul": "^0.4.5", "jsesc": "^2.5.2", "lodash": "^4.17.15", "mocha": "^7.1.0", "regexpu-fixtures": "^2.1.3", "unicode-13.0.0": "^0.8.0"}}