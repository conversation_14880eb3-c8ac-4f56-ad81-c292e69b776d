{"name": "aproba", "version": "1.2.0", "description": "A ridiculously light-weight argument validator (now browser friendly)", "main": "index.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"standard": "^10.0.3", "tap": "^10.0.2"}, "files": ["index.js"], "scripts": {"test": "standard && tap -j3 test/*.js"}, "repository": {"type": "git", "url": "https://github.com/iarna/aproba"}, "keywords": ["argument", "validate"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/iarna/aproba/issues"}, "homepage": "https://github.com/iarna/aproba"}