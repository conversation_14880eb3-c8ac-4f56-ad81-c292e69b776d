{"name": "flat-cache", "version": "2.0.1", "description": "A stupidly simple key/value storage using files to persist some data", "repository": "royriojas/flat-cache", "license": "MIT", "author": {"name": "<PERSON>", "url": "http://royriojas.com"}, "main": "cache.js", "files": ["cache.js", "utils.js", "del.js"], "engines": {"node": ">=4"}, "precommit": ["npm run verify --silent"], "prepush": ["npm run verify --silent"], "scripts": {"beautify": "esbeautifier 'cache.js' 'utils.js' 'del.js' 'test/specs/**/*.js'", "beautify-check": "npm run beautify -- -k", "eslint": "eslinter 'cache.js' 'utils.js' 'del.js' 'specs/**/*.js'", "eslint-fix": "npm run eslint -- --fix", "autofix": "npm run beautify && npm run eslint-fix", "check": "npm run beautify-check && npm run eslint", "verify": "npm run check && npm run test:cache", "install-hooks": "prepush install && changelogx install-hook && precommit install", "changelog": "changelogx -f markdown -o ./changelog.md", "do-changelog": "npm run changelog && git add ./changelog.md && git commit -m 'DOC: Generate changelog' --no-verify", "pre-v": "npm run verify", "post-v": "npm run do-changelog && git push --no-verify && git push --tags --no-verify", "bump-major": "npm run pre-v && npm version major -m 'BLD: Release v%s' && npm run post-v", "bump-minor": "npm run pre-v && npm version minor -m 'BLD: Release v%s' && npm run post-v", "bump-patch": "npm run pre-v && npm version patch -m 'BLD: Release v%s' && npm run post-v", "test:cache": "mocha -R spec test/specs", "test": "npm run verify --silent", "cover": "istanbul cover test/runner.js html text-summary", "watch": "watch-run -i -p 'test/specs/**/*.js' istanbul cover test/runner.js html text-summary"}, "keywords": ["json cache", "simple cache", "file cache", "key par", "key value", "cache"], "changelogx": {"ignoreRegExp": ["BLD: Release", "DOC: Generate Changelog", "Generated Changelog"], "issueIDRegExp": "#(\\d+)", "commitURL": "https://github.com/royriojas/flat-cache/commit/{0}", "authorURL": "https://github.com/{0}", "issueIDURL": "https://github.com/royriojas/flat-cache/issues/{0}", "projectName": "flat-cache"}, "devDependencies": {"chai": "^3.2.0", "changelogx": "3.0.0", "esbeautifier": "10.1.1", "eslinter": "^3.2.1", "glob-expand": "0.2.1", "istanbul": "0.4.5", "mocha": "5.2.0", "precommit": "^1.1.5", "prepush": "^3.1.4", "proxyquire": "^1.7.2", "sinon": "^1.16.1", "sinon-chai": "^2.8.0", "watch-run": "^1.2.2"}, "dependencies": {"flatted": "^2.0.0", "rimraf": "2.6.3", "write": "1.0.3"}}