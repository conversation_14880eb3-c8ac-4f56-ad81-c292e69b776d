{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../src/cli.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,wCAA2B;AAC3B,yCAA4B;AAE5B,6BAA+B;AAC/B,iCAAuE;AACvE,iCAA+B;AAE/B,KAAK;KACA,MAAM,CAAC,OAAO,EAAE;IACb,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,CAAC;IACR,WAAW,EAAE,oCAAoC;CACpD,CAAC;KACD,KAAK,CACF,CAAC,EAAE,EAAE,mCAAmC,EAAE,EAAE,EAAE,wDAAwD,CAAC,CAAC,IAAI,CACxG,IAAI,CACP,CACJ;KACA,MAAM,CAAC,UAAU,EAAE;IAChB,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,CAAC;IACR,WAAW,EAAE,mEAAmE;CACnF,CAAC;KACD,OAAO,EAAE;KACT,IAAI,CAAC,MAAM,CAAC;KACZ,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC;KAClB,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,CAAA;AAO1B,IAAM,IAAI,GAAc,KAAK,CAAC,IAAI,CAAA;AAElC,IAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AAEtB,IAAI,WAA4B,CAAA;AAChC,IAAI,CAAC,IAAI,IAAI,CAAE,OAAO,CAAC,KAAwB,CAAC,KAAK,EAAE;IACnD,mBAAmB;IACnB,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;IACjC,IAAI,MAAI,GAAG,EAAE,CAAA;IACb,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE;QACzB,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;QAClC,IAAI,KAAK,KAAK,IAAI,EAAE;YAChB,MAAI,IAAI,KAAK,CAAA;SAChB;IACL,CAAC,CAAC,CAAA;IACF,WAAW,GAAG,IAAI,OAAO,CAAC,UAAA,OAAO;QAC7B,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE;YACpB,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;YAClC,IAAI,KAAK,KAAK,IAAI,EAAE;gBAChB,MAAI,IAAI,KAAK,CAAA;aAChB;YACD,OAAO,CAAC,MAAI,CAAC,CAAA;QACjB,CAAC,CAAC,CAAA;IACN,CAAC,CAAC,CAAA;CACL;KAAM,IAAI,IAAI,EAAE;IACb,YAAY;IACZ,WAAW,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;CAC3C;KAAM;IACH,KAAK,CAAC,QAAQ,EAAE,CAAA;IAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACf,MAAM,IAAI,KAAK,EAAE,CAAA;CACpB;AAED,OAAO,CAAC,GAAG,CAA6B,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;KAC3G,IAAI,CAAC,UAAC,EAAa;QAAZ,YAAI,EAAE,aAAK;IACf,IAAM,OAAO,GAAqB;QAC9B,cAAc,EAAE,IAAI;QACpB,KAAK,EAAE,CAAC,KAAK,IAAI,aAAK,CAAC,KAAK,CAAC,CAAC,IAAI,SAAS;KAC9C,CAAA;IACD,IAAI,IAAI,EAAE;QACN,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QACxC,IAAI,GAAG,IAAI,wBAAgB,CAAC,GAAG,CAAC,EAAE;YAC9B,OAAO,CAAC,QAAQ,GAAG,GAAG,CAAA;SACzB;KACJ;IACD,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;IAChC,OAAO,IAAI,OAAO,CAAO,UAAC,OAAO,EAAE,MAAM;QACrC,OAAA,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAS,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,UAAC,GAAQ,IAAK,OAAA,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,EAA/B,CAA+B,CAAC;IAA7F,CAA6F,CAChG,CAAA;AACL,CAAC,CAAC;KACD,IAAI,CAAC;IACF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACnB,CAAC,CAAC;KACD,KAAK,CAAC,UAAC,GAAQ;IACZ,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAClB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACnB,CAAC,CAAC,CAAA"}