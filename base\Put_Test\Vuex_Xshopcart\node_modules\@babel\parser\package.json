{"name": "@babel/parser", "version": "7.11.5", "description": "A JavaScript parser", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "keywords": ["babel", "javascript", "parser", "tc39", "ecmascript", "@babel/parser"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-parser"}, "main": "lib/index.js", "types": "typings/babel-parser.d.ts", "files": ["bin", "lib", "typings"], "engines": {"node": ">=6.0.0"}, "devDependencies": {"@babel/code-frame": "^7.10.4", "@babel/helper-fixtures": "^7.10.5", "@babel/helper-validator-identifier": "^7.10.4", "charcodes": "^0.2.0"}, "bin": "./bin/babel-parser.js", "gitHead": "af64ccb2b00bc7574943674996c2f0507cdbfb6f"}