{"name": "postcss-reduce-initial", "version": "4.0.3", "description": "Reduce initial definitions to the actual initial value, where possible.", "main": "dist/index.js", "files": ["data", "dist/index.js", "LICENSE-MIT"], "scripts": {"acquire": "babel-node ./src/acquire.js", "prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/,src/acquire.js"}, "keywords": ["css", "postcss", "postcss-plugin"], "license": "MIT", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0", "got": "^8.0.0", "html2plaintext": "^2.0.0", "write-file": "^1.0.0"}, "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "dependencies": {"browserslist": "^4.0.0", "caniuse-api": "^3.0.0", "has": "^1.0.0", "postcss": "^7.0.0"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": ">=6.9.0"}}