<div class="apiDetail">
<div>
	<h2><span>Function(treeId, treeNode)</span><span class="path">setting.view.</span>addDiyDom</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>This function used to display the custom control on the node.</p>
			<p class="highlight_red">1. If you have huge node data, please note: this function will affect the initialization performance. If not required, it is recommended not to use this function.</p>
			<p class="highlight_red">2. This function is an advanced application, please make sure that a better understanding of zTree before you use it.</p>
			<p>Default: null</p>
		</div>
	</div>
	<h3>Function Parameter Descriptions</h3>
	<div class="desc">
	<h4><b>treeId</b><span>String</span></h4>
	<p>zTree unique identifier: <b class="highlight_red">treeId</b>, easy for users to control.</p>
	<h4 class="topLine"><b>treeNode</b><span>JSON</span></h4>
	<p>JSON data object of the node which display the custom control.</p>
	</div>
	<h3>Examples of setting & function</h3>
	<h4>1. Display button in all nodes.</h4>
	<pre xmlns=""><code>var setting = {
	view: {
		addDiyDom: addDiyDom
	}
};
function addDiyDom(treeId, treeNode) {
	var aObj = $("#" + treeNode.tId + "_a");
	if ($("#diyBtn_"+treeNode.id).length>0) return;
	var editStr = "&lt;span id='diyBtn_space_" +treeNode.id+ "' &gt; &lt;/span&gt;"
		+ "&lt;button type='button' class='diyBtn1' id='diyBtn_" + treeNode.id
		+ "' title='"+treeNode.name+"' onfocus='this.blur();'&gt;&lt;/button&gt;";
	aObj.append(editStr);
	var btn = $("#diyBtn_"+treeNode.id);
	if (btn) btn.bind("click", function(){alert("diy Button for " + treeNode.name);});
};
......</code></pre>
</div>
</div>