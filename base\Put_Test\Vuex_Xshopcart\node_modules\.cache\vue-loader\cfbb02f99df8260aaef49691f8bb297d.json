{"remainingRequest": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\components\\HelloWorld.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\components\\HelloWorld.vue", "mtime": 1658035355533}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1657986293299}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1657986298727}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1657986293299}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1657986322488}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["HelloWorld.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "HelloWorld.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\n  <div class=\"hello\">\n    <ul class=\"shop_container\">\n      <li class=\"shop_container_li\" v-for=\"(item, index) in goodsObj\" :key=\"item.index\">\n        <div class=\"shop_img\">\n          <img width=\"100%\" height=\"100%\" :src=\"item.img\" />\n        </div>\n        <div class=\"shop_detail\">\n          <p>{{ item.name }}</p>\n          <p>{{ item.hint }}</p>\n          <p>￥{{ item.price }}</p>\n          <p>\n            <span class=\"shop_reduce\" @click=\"handleReduce({ index })\">-</span>\n            <span class=\"shop_num\">{{ item.num }}</span>\n            <span class=\"shop_add\" @click=\"handleAdd({ index })\">+</span>\n          </p>\n        </div>\n      </li>\n    </ul>\n    <div class=\"foot\">\n      <div class=\"total_price\">\n        <span>合计：{{ totalNum }}</span>\n      </div>\n      <div class=\"total_num\">\n        <span>去结账：￥{{ totalPrice }}</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  mapState,\n  mapGetters,\n  // mapMutations,\n  // mapActions,\n} from \"vuex\";\nexport default {\n  name: \"HelloWorld\",\n  data() {\n    return {};\n  },\n  created() {\n    // console.log(this.$store.getters.getById(1));\n    // 根据id获取商品值\n    if(this.$store.state.goods){\n      console.log(this.$store.state.goods.filter((item) => item.id - 0 === 1));\n    }\n    console.log('this.$store.state.cart :>> ', this.$store.state.cart );\n  },\n  computed: {\n    // ...mapState(['goods', 'totalNum', 'totalPrice']),\n    ...mapState({\n      goods: (state) => state.cart.goods, \n      totalNum: (state) => state.cart.totalNum,\n      totalPrice: (state) => state.cart.totalPrice,\n    }),\n    ...mapGetters([\"goodsObj\"]),\n    // ...mapState({\n    //   goods: state => state.goods\n    // }),\n    // goods() {\n    //   return this.$store.state.goods\n    // }\n    // goodsObj() {\n    //   return this.$store.getters.goodsObj\n    // }\n  },\n  methods: {\n    handleAdd({ index }) {\n      // console.log(\"%c [ index ]\", \"font-size:13px; background:#00ffff; color:red;\", index)\n      // this.$store.commit('add', index)\n      // this.$store.commit({\n      //   type: 'ADD',\n      //   index\n      // })\n      // this.$store.dispatch('increment', {\n      //   index\n      // })\n      this.$store.dispatch({\n        type: \"increment\",\n        index, \n      });\n    },\n    handleReduce({ index }) {\n      // this.$store.dispatch({\n      //   type: \"reduce\",\n      //   index,\n      // });\n      this.$store.commit({\n        type:'REDUCE',\n        index\n      })\n    },\n  },\n//   答：如果请求的数据是多个组件共享的，为了⽅便只写⼀份，就写vuex⾥⾯，如果是组件独⽤的就写在当前组件⾥⾯。\n// 如果请求来的数据不是要被其他组件公⽤，仅仅在请求的组件内使⽤，就不需要放⼊ vuex 的 state ⾥\n// 如果被其他地⽅复⽤，请将请求放⼊ action ⾥，⽅便复⽤，并包装成 promise 返回\n\n};\n</script>\n\n<!-- Add \"scoped\" attribute to limit CSS to this component only -->\n\n<style scoped>\nbody,\nli,\nul,\np {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.hello {\n  position: relative;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n}\n\n.shop_container {\n  width: 100%;\n}\n\n.shop_container_li {\n  background-color: #f5f5f5;\n  height: 130px;\n  margin-bottom: 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.shop_img {\n  flex: 1;\n  padding: 10px;\n  height: 110px;\n}\n\n.shop_detail {\n  flex: 2;\n}\n\n.shop_detail p {\n  font-size: 14px;\n  line-height: 25px;\n  height: 25px;\n}\n\n.shop_reduce,\n.shop_add {\n  font-size: 18px;\n  font-weight: 700;\n  display: inline-block;\n  text-align: center;\n  width: 20px;\n  height: 20px;\n  border: 1px solid #f5f5f5;\n  background-color: #ffffff;\n}\n\n.shop_num {\n  margin: 0 5px;\n}\n\n.foot {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 40px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.total_price {\n  background-color: #4cd964;\n  flex: 2;\n  height: 100%;\n  line-height: 40px;\n}\n\n.total_num {\n  flex: 1;\n  background-color: #666;\n  height: 100%;\n  line-height: 40px;\n}\n\n.payment {\n  background-color: #3b95e9;\n}\n</style>\n"]}]}