{"name": "@babel/generator", "version": "7.11.6", "description": "Turns an AST into code.", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-generator"}, "main": "lib/index.js", "files": ["lib"], "dependencies": {"@babel/types": "^7.11.5", "jsesc": "^2.5.1", "source-map": "^0.5.0"}, "devDependencies": {"@babel/helper-fixtures": "^7.10.5", "@babel/parser": "^7.11.5"}, "gitHead": "e51139d7fd850e7f5b8cd6aafb17cc88b7010218"}