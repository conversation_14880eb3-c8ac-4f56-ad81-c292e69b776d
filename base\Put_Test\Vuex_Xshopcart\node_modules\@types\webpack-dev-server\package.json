{"name": "@types/webpack-dev-server", "version": "3.11.0", "description": "TypeScript definitions for webpack-dev-server", "license": "MIT", "contributors": [{"name": "ma<PERSON><PERSON>h", "url": "https://github.com/maestroh", "githubUsername": "ma<PERSON><PERSON>h"}, {"name": "<PERSON>", "url": "https://github.com/daveparslow", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Zheyang<PERSON>ong", "githubUsername": "ZheyangSong"}, {"name": "<PERSON>", "url": "https://github.com/alan-agius4", "githubUsername": "alan-agius4"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/arturovt", "githubUsername": "arturovt"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/dobogo", "githubUsername": "dobogo"}, {"name": "<PERSON>", "url": "https://github.com/billy-le", "githubUsername": "billy-le"}, {"name": "<PERSON>", "url": "https://github.com/chrispaterson", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wwmoraes", "githubUsername": "ww<PERSON><PERSON>s"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/webpack-dev-server"}, "scripts": {}, "dependencies": {"@types/connect-history-api-fallback": "*", "@types/express": "*", "@types/http-proxy-middleware": "*", "@types/serve-static": "*", "@types/webpack": "*"}, "typesPublisherContentHash": "cc762a7776a8681e401c671cb40514bb6516ba15b86163d62df96d3490cc2dfd", "typeScriptVersion": "3.0"}