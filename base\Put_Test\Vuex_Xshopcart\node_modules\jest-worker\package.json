{"name": "jest-worker", "version": "25.5.0", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/jest-worker"}, "license": "MIT", "main": "build/index.js", "types": "build/index.d.ts", "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "dependencies": {"merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "devDependencies": {"@types/merge-stream": "^1.1.2", "@types/node": "*", "@types/supports-color": "^5.3.0", "get-stream": "^5.1.0", "worker-farm": "^1.6.0"}, "engines": {"node": ">= 8.3"}, "publishConfig": {"access": "public"}, "gitHead": "ddd73d18adfb982b9b0d94bad7d41c9f78567ca7"}