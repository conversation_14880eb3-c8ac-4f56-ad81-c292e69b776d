<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>JS分页+全选</title>
<style>
.pagesize{
    position: fixed;
    top:350px;
    right: 20px;
}
</style>

</head>
<body onLoad="goPage(1,10);">
  <table id="idData" width="70%">
    <tr >   
        <td>      
            <label for="check0">全选:&nbsp;&nbsp;
            <input type="checkbox" id="check0" value="000" onclick="checkBox(value)">
            </label>
            <br/>
            <br/>
        </td> 
    </tr>
    <tr><td> 
        <label for="check1">单选1:
            <input type="checkbox" id="check1" value="001" onclick="checkBox(value)">
        </label>
        <br/></td> 
    </tr>
    <tr><td> 
        <label for="check2">单选2:
            <input type="checkbox" id="check2" value="003" onclick="checkBox(value)">
        </label>
        <br/></td> 
    </tr>
    <tr><td> 
        <label for="check3">单选3:
            <input type="checkbox" id="check3" value="003" onclick="checkBox(value)">
        </label>
        <br/></td> 
    </tr>
    <tr><td> 
        <label for="check4">单选4:
            <input type="checkbox" id="check4" value="004" onclick="checkBox(value)">
        </label>
        <br/></td> 
    </tr>    
    <tr><td> 
        <label for="check5">单选5:
            <input type="checkbox" id="check5" value="005" onclick="checkBox(value)">
        </label>
        <br/></td> 
    </tr>
    <tr><td> 
        <label for="check6">单选6:
            <input type="checkbox" id="check6" value="006" onclick="checkBox(value)">
        </label>
        <br/></td> 
    </tr>
    <tr><td> 
        <label for="check7">单选7:
            <input type="checkbox" id="check7" value="007" onclick="checkBox(value)">
        </label>
        <br/></td> 
    </tr>
    <tr><td> 
        <label for="check8">单选8:
            <input type="checkbox" id="check8" value="008" onclick="checkBox(value)">
        </label>
        <br/></td> 
    </tr>
    <tr><td> 
        <label for="check9">单选9:
            <input type="checkbox" id="check9" value="009" onclick="checkBox(value)">
        </label>
        <br/></td> 
    </tr>
    <tr><td> 
        <label for="check10">单选10:
            <input type="checkbox" id="check10" value="010" onclick="checkBox(value)">
        </label>
        <br/></td> 
    </tr>
    <tr><td> 
        <label for="check11">单选11:
            <input type="checkbox" id="check11" value="011" onclick="checkBox(value)">
        </label>
        <br/></td> 
    </tr>
    <tr><td> 
        <label for="check12">单选12:
            <input type="checkbox" id="check12" value="012" onclick="checkBox(value)">
        </label>
        <br/></td> 
    </tr>
    <tr> 
        <td> 
            <label for="check13">单选13:
                <input type="checkbox" id="check13" value="013" onclick="checkBox(value)">
            </label>
            <br/>
        </td> 
    </tr>
    <tr>
        <td> 
            <label for="check14">单选14:
                <input type="checkbox" id="check14" value="014" onclick="checkBox(value)">
            </label>
            <br/>
        </td>
    </tr>
  </table>
  <table width="60%" align="right" class="pagesize">
    <tr><td><div id="barcon" name="barcon"></div></td></tr>
  </table>
  <script>
    /**
     * 分页函数
     * pno--页数
     * psize--每页显示记录数
     * 分页部分是从真实数据行开始，因而存在加减某个常数，以确定真正的记录数
     * 纯js分页实质是数据行全部加载，通过是否显示属性完成分页功能
     **/
    function goPage(pno,psize){
      var itable = document.getElementById("idData");
      var num = itable.rows.length;//表格所有行数(所有记录数)
      var totalPage = 0;//总页数
      var pageSize = psize;//每页显示行数
      //总共分几页
      if(num/pageSize > parseInt(num/pageSize)){
          totalPage=parseInt(num/pageSize)+1;
      }else{
          totalPage=parseInt(num/pageSize);
      }
      var currentPage = pno;//当前页数
      var startRow = (currentPage - 1) * pageSize+1;//开始显示的行 31
      var endRow = currentPage * pageSize;//结束显示的行  40
        endRow = (endRow > num)? num : endRow;  //40
        //遍历显示数据实现分页
      for(var i=1;i<(num+1);i++){
        var irow = itable.rows[i-1];
        if(i>=startRow && i<=endRow){
          irow.style.display = "block";
        }else{
          irow.style.display = "none";
        }
      }
      var tempStr = "共"+num+"条记录 分"+totalPage+"页 当前第"+currentPage+"页";
      if(currentPage>1){
        tempStr += "<a href=\"#\" onClick=\"goPage("+(1)+","+psize+")\">首页</a>";
        tempStr += "<a href=\"#\" onClick=\"goPage("+(currentPage-1)+","+psize+")\"><上一页</a>"
      }else{
        tempStr += "首页";
        tempStr += "<上一页";
      }
      if(currentPage<totalPage){
        tempStr += "<a href=\"#\" onClick=\"goPage("+(currentPage+1)+","+psize+")\">下一页></a>";
        tempStr += "<a href=\"#\" onClick=\"goPage("+(totalPage)+","+psize+")\">尾页</a>";
      }else{
        tempStr += "下一页>";
        tempStr += "尾页";
      }
      document.getElementById("barcon").innerHTML = tempStr;
    };

    function checkBox(item){
        console.log('item :>> ', item);

    }




    </script>
</body>
</html>