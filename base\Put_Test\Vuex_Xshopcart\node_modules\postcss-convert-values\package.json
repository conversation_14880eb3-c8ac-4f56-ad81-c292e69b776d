{"name": "postcss-convert-values", "version": "4.0.1", "description": "Convert values with PostCSS (e.g. ms -> s)", "main": "dist/index.js", "files": ["LICENSE-MIT", "dist"], "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "keywords": ["css", "optimisation", "postcss", "postcss-plugin"], "license": "MIT", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0"}, "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "dependencies": {"postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": ">=6.9.0"}}