{"name": "hsl-regex", "description": "Regex for matching HSL colors.", "author": "<PERSON>", "version": "1.0.0", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "mocha test"}, "repository": {"type": "git", "url": "https://github.com/regexps/hsl-regex.git"}, "keywords": ["hsl", "regex", "regexp", "color", "css"], "license": "MIT", "bugs": {"url": "https://github.com/regexps/hsl-regex/issues"}, "homepage": "https://github.com/regexps/hsl-regex", "dependencies": {}, "devDependencies": {"mocha": "*"}}