{"name": "@babel/plugin-transform-regenerator", "author": "<PERSON> <<EMAIL>>", "description": "Explode async and generator functions into a state machine.", "version": "7.10.4", "homepage": "https://babeljs.io/", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-regenerator"}, "main": "lib/index.js", "dependencies": {"regenerator-transform": "^0.14.2"}, "license": "MIT", "publishConfig": {"access": "public"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df"}