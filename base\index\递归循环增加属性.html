<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>递归循环增加属性</title>
</head>
<body>
    <script>
    // function sum(n){ //经典递归 计算从1+到100总和为 5050
    //     if(n === 1) return 1    
    //     let sum2 =  sum(n - 1) + n
    //     console.log("%c [ sum2 ]", "font-size:13px; background:#00ffff; color:red;", sum2)
    //     return sum2
    // }
    // sum(100)
    
    const tableData = [
        {
            name: '1',
            id: '1',
            children:[
                { 
                    name: '1-1',
                    id: '1-1',
                    children:[
                        {    
                            name: '1-1-1',
                            id: '1-1-1'
                        }
                    ]
                }
            ]
        }, {
            name: '2',
            id: '2'
        }, {
            name: '3',
            id: '3'
        }
    ]
    // let newArr = []
    // tableData.map((item,index)=>{
    //     newArr.push(Object.assign({}, item, { level: 1+1 }))
    // })
    // console.log("%c [ newArr ]", "font-size:13px; background:#00ffff; color:red;", newArr)

    // function slotVal(arr, index){
    //     index ++
    //     arr.forEach(item=>{
    //         item.isSlotVal = index < 2 ? true : false
    //         if(item.children){
    //             slotVal(item.children, index)
    //         }
    //     })
    // }
    // let index = 0
    // slotVal(tableData, index)
    // console.log("%c [ tableData ]", "font-size:13px; background:#00ffff; color:red;", tableData)


    // const treeData = [
    //     {
    //         name: "XXX医疗设备",
    //         children: [
    //             {
    //                 name: "销售部",
    //                 children: [
    //                     {
    //                         name: "销售1队",
    //                         children:[
    //                             {
    //                                 name: "yq 10.00万",
    //                                 value: 10.00 ,
    //                             },
    //                             {
    //                                 name: "旺仔1 2.40万",
    //                                 value: 2.40 ,
    //                             },
    //                         ]
    //                     } 
    //                 ]
    //             }
    //         ]
    //     },
    // ]

    // function walk(list, callback, deep = 0) {
    //     return list.map(it => {
    //         const result = callback({ ...it }, deep);
    //         if (it.children) {
    //             result.children = walk(it.children, callback, deep + 1);
    //         }
    //         return result;
    //     });
    // }

    // const colors = ['#000', '#111', '#222', '#333', '#444', '#555'];
    // const data = walk(treeData, (data, deep) => ({
    //     ...data, style: {
    //         color: colors[deep]
    //     }
    // }), 0);
    // console.log("%c [ data ]", "font-size:13px; background:#00ffff; color:red;", data)

    var imgs=[[['#fff','#eee'],['#117A65','#E74C3C'],['#F39C12','#AF7AC5']],[['#7B241C  ','#eee'],['#117A65','#909497  ']],[['#17A589','#626567']],[['#5499C7','#eee'],['#117A65','#884EA0']]];

    const flattened = imgs.flat(2);//数组扁平化处理
    // console.log("%c [ flattened ]", "font-size:13px; background:#00ffff; color:red;", flattened)

    for (let i = 0; i < flattened.length; i++) {
        console.log(flattened[i]);
    }




    </script>
</body>
</html>