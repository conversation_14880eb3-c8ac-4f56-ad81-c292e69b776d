{"name": "ora", "version": "3.4.0", "description": "Elegant terminal spinner", "license": "MIT", "repository": "sindresorhus/ora", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle"], "dependencies": {"chalk": "^2.4.2", "cli-cursor": "^2.1.0", "cli-spinners": "^2.0.0", "log-symbols": "^2.2.0", "strip-ansi": "^5.2.0", "wcwidth": "^1.0.1"}, "devDependencies": {"@types/node": "^11.13.0", "ava": "^1.4.1", "get-stream": "^4.1.0", "tsd": "^0.7.2", "xo": "^0.24.0"}}