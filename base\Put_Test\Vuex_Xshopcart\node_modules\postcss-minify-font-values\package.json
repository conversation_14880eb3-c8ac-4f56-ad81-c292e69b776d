{"name": "postcss-minify-font-values", "version": "4.0.2", "description": "Minify font declarations with PostCSS", "main": "dist/index.js", "files": ["dist"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "keywords": ["css", "font", "font-family", "font-weight", "optimise", "postcss-plugin"], "dependencies": {"postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "repository": "cssnano/cssnano", "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "homepage": "https://github.com/cssnano/cssnano", "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0"}, "engines": {"node": ">=6.9.0"}}