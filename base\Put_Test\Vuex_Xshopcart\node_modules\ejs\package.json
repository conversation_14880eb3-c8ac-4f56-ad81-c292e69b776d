{"name": "ejs", "description": "Embedded JavaScript templates", "keywords": ["template", "engine", "ejs"], "version": "2.7.4", "author": "<PERSON> <<EMAIL>> (http://fleegix.org)", "license": "Apache-2.0", "main": "./lib/ejs.js", "repository": {"type": "git", "url": "git://github.com/mde/ejs.git"}, "bugs": "https://github.com/mde/ejs/issues", "homepage": "https://github.com/mde/ejs", "dependencies": {}, "devDependencies": {"browserify": "^13.1.1", "eslint": "^4.14.0", "git-directory-deploy": "^1.5.1", "jake": "^10.3.1", "jsdoc": "^3.4.0", "lru-cache": "^4.0.1", "mocha": "^5.0.5", "uglify-js": "^3.3.16"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "postinstall": "node ./postinstall.js"}}