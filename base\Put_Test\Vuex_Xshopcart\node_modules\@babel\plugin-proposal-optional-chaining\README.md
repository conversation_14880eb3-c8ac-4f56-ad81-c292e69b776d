# @babel/plugin-proposal-optional-chaining

> Transform optional chaining operators into a series of nil checks

See our website [@babel/plugin-proposal-optional-chaining](https://babeljs.io/docs/en/next/babel-plugin-proposal-optional-chaining.html) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-proposal-optional-chaining
```

or using yarn:

```sh
yarn add @babel/plugin-proposal-optional-chaining --dev
```
