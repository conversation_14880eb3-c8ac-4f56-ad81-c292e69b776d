verbose: false
instrumentation:
    root: .
    extensions:
        - .js
        - .jsx
    default-excludes: true
    excludes: []
    variable: __coverage__
    compact: true
    preserve-comments: false
    complete-copy: false
    save-baseline: false
    baseline-file: ./coverage/coverage-baseline.raw.json
    include-all-sources: false
    include-pid: false
    es-modules: false
    auto-wrap: false
reporting:
    print: summary
    reports:
        - html
    dir: ./coverage
    summarizer: pkg
    report-config: {}
    watermarks:
        statements: [50, 80]
        functions: [50, 80]
        branches: [50, 80]
        lines: [50, 80]
hooks:
    hook-run-in-context: false
    post-require-hook: null
    handle-sigint: false
check:
    global:
        statements: 100
        lines: 100
        branches: 100
        functions: 100
        excludes: []
    each:
        statements: 100
        lines: 100
        branches: 100
        functions: 100
        excludes: []
