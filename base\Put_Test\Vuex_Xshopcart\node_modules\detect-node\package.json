{"name": "detect-node", "version": "2.0.4", "description": "Detect Node.JS (as opposite to browser environment) (reliable)", "main": "index.js", "browser": "browser.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/iliakan/detect-node"}, "keywords": ["detect", "node"], "author": "<PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/iliakan/detect-node/issues"}, "homepage": "https://github.com/iliakan/detect-node"}