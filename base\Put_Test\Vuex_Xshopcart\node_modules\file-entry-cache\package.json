{"name": "file-entry-cache", "version": "5.0.1", "description": "Super simple cache for file metadata, useful for process that work o a given series of files and that only need to repeat the job on the changed ones since the previous run of the process", "repository": "royriojas/file-entry-cache", "license": "MIT", "author": {"name": "<PERSON>", "url": "http://royriojas.com"}, "main": "cache.js", "files": ["cache.js"], "engines": {"node": ">=4"}, "scripts": {"beautify": "esbeautifier 'cache.js' 'test/**/*.js' 'perf.js'", "beautify-check": "npm run beautify -- -k", "eslint": "eslinter 'cache.js' 'specs/**/*.js' 'perf.js'", "lint": "npm run beautify && npm run eslint", "verify": "npm run beautify-check && npm run eslint", "install-hooks": "prepush install && changelogx install-hook && precommit install", "changelog": "changelogx -f markdown -o ./changelog.md", "do-changelog": "npm run changelog && git add ./changelog.md && git commit -m 'DOC: Generate changelog' --no-verify", "pre-v": "npm run test", "post-v": "npm run do-changelog && git push --no-verify && git push --tags --no-verify", "bump-major": "npm run pre-v && npm version major -m 'BLD: Release v%s' && npm run post-v", "bump-minor": "npm run pre-v && npm version minor -m 'BLD: Release v%s' && npm run post-v", "bump-patch": "npm run pre-v && npm version patch -m 'BLD: Release v%s' && npm run post-v", "test": "npm run verify --silent && mocha -R spec test/specs", "perf": "node perf.js", "cover": "istanbul cover test/runner.js html text-summary", "watch": "watch-run -i -p 'test/specs/**/*.js' istanbul cover test/runner.js html text-summary"}, "prepush": ["npm run verify"], "precommit": ["npm run verify"], "keywords": ["file cache", "task cache files", "file cache", "key par", "key value", "cache"], "changelogx": {"ignoreRegExp": ["BLD: Release", "DOC: Generate Changelog", "Generated Changelog"], "issueIDRegExp": "#(\\d+)", "commitURL": "https://github.com/royriojas/file-entry-cache/commit/{0}", "authorURL": "https://github.com/{0}", "issueIDURL": "https://github.com/royriojas/file-entry-cache/issues/{0}", "projectName": "file-entry-cache"}, "devDependencies": {"chai": "^3.2.0", "changelogx": "3.0.0", "commander": "^2.6.0", "del": "^2.0.2", "esbeautifier": "^4.2.11", "eslinter": "^2.3.3", "glob-expand": "^0.1.0", "istanbul": "^0.3.6", "mocha": "^2.1.0", "precommit": "^1.1.5", "prepush": "^3.1.4", "proxyquire": "^1.3.1", "sinon": "^1.12.2", "sinon-chai": "^2.7.0", "watch-run": "^1.2.1", "write": "^0.3.1"}, "dependencies": {"flat-cache": "^2.0.1"}}