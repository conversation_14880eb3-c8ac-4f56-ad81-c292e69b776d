{"version": 3, "file": "forkJoin.js", "sources": ["../../src/internal/observable/forkJoin.ts"], "names": [], "mappings": ";;AAAA,4CAA2C;AAE3C,2CAA0C;AAC1C,wCAAuC;AACvC,6CAA4C;AAE5C,+BAA8B;AAsI9B,SAAgB,QAAQ;IACtB,iBAAiB;SAAjB,UAAiB,EAAjB,qBAAiB,EAAjB,IAAiB;QAAjB,4BAAiB;;IAEjB,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QACxB,IAAM,OAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACzB,IAAI,iBAAO,CAAC,OAAK,CAAC,EAAE;YAClB,OAAO,gBAAgB,CAAC,OAAK,EAAE,IAAI,CAAC,CAAC;SACtC;QAED,IAAI,mBAAQ,CAAC,OAAK,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,OAAK,CAAC,KAAK,MAAM,CAAC,SAAS,EAAE;YACxE,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAK,CAAC,CAAC;YAChC,OAAO,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,OAAK,CAAC,GAAG,CAAC,EAAV,CAAU,CAAC,EAAE,IAAI,CAAC,CAAC;SAC5D;KACF;IAGD,IAAI,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;QACrD,IAAM,gBAAc,GAAG,OAAO,CAAC,GAAG,EAAc,CAAC;QACjD,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,iBAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QAC/E,OAAO,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,IAAI,CACzC,SAAG,CAAC,UAAC,IAAW,IAAK,OAAA,gBAAc,eAAI,IAAI,GAAtB,CAAuB,CAAC,CAC9C,CAAC;KACH;IAED,OAAO,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACzC,CAAC;AAzBD,4BAyBC;AAED,SAAS,gBAAgB,CAAC,OAA+B,EAAE,IAAqB;IAC9E,OAAO,IAAI,uBAAU,CAAC,UAAA,UAAU;QAC9B,IAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;QAC3B,IAAI,GAAG,KAAK,CAAC,EAAE;YACb,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,OAAO;SACR;QACD,IAAM,MAAM,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,OAAO,GAAG,CAAC,CAAC;gCACP,CAAC;YACR,IAAM,MAAM,GAAG,WAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;gBAC9B,IAAI,EAAE,UAAA,KAAK;oBACT,IAAI,CAAC,QAAQ,EAAE;wBACb,QAAQ,GAAG,IAAI,CAAC;wBAChB,OAAO,EAAE,CAAC;qBACX;oBACD,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;gBACpB,CAAC;gBACD,KAAK,EAAE,UAAA,GAAG,IAAI,OAAA,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,EAArB,CAAqB;gBACnC,QAAQ,EAAE;oBACR,SAAS,EAAE,CAAC;oBACZ,IAAI,SAAS,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE;wBAClC,IAAI,OAAO,KAAK,GAAG,EAAE;4BACnB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gCACpB,IAAI,CAAC,MAAM,CAAC,UAAC,MAAM,EAAE,GAAG,EAAE,CAAC,IAAK,OAAA,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAjC,CAAiC,EAAE,EAAE,CAAC,CAAC,CAAC;gCACxE,MAAM,CAAC,CAAC;yBACX;wBACD,UAAU,CAAC,QAAQ,EAAE,CAAC;qBACvB;gBACH,CAAC;aACF,CAAC,CAAC,CAAC;QACN,CAAC;QAxBD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;oBAAnB,CAAC;SAwBT;IACH,CAAC,CAAC,CAAC;AACL,CAAC"}