<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <style>
        
    </style>
</head>

<body>
    <div id="app">
    </div>
    <!-- <script src="https://cdn.staticfile.org/vue/2.2.2/vue.min.js"></script> -->
    <script>
        //  promise async await基础知识
        // 事件机制: 如果有new Promise 先走微任务(promise),再走主线程,再走then(promise的回调),然后,再宏任务(setTimeout)
        // async返回了一个promise函数, await处理了一个promise, 相当于返回了.then(){}
        
            // setTimeout(function() {
            //     console.log('我是定时器！');
            // })
            // new Promise(function(resolve) {
            //     console.log('我是promise！');
            //     resolve();
            // }).then(function() {
            //     console.log('我是then！');
            // })
            // console.log('我是主线程！');

            // 我是promise！
            // 我是主线程！
            // 我是then！
            // 我是定时器！

        
        // (function test(){

        //     setTimeout(function(){
        //         console.log('我是定时器！');
        //     },1000)

        //     new Promise(function(resolve,reject){
        //         console.log(1);
        //         for(var i=0;i<3;i++){
        //             resolve()
        //         }
        //     }).then(function(){
        //         console.log(2);
        //     })
        //     console.log(3)

        // })()       // 执行顺序: 1 3 2 我是定时器！




        // new Promise((resolve, reject) => {
        //     setTimeout(() => {
        //         resolve(2500)
                
        //     }, 1000)

        //     console.log(1111)
        // }).then(res => {
        //     console.log('promise.then ==> res',res) // 这个地方会打印捕捉到的2000
        //     console.log('1500 :>> ', 1500);
        //     return res + 1000 // 这个函数的返回值，返回的就是这个promise对象捕捉到的成功的值
        // }).then(res => {
        //     console.log('promise.then.then ==> res: res(2500) + 1000',res) //这个地方打印的就是上一个promise对象return的值  1000+2500 =3500
        // }).catch(err=>{
        //     console.log('err :>> ', err);
        // })    

        //  1111 2500 1500 3500


        // new Promise((resove,reject)=>{
        //     reject(500)
        //     console.log(1111)
        // }).then(res=>{}).catch(err=>{
        //     console.log("%c [ err ]", "font-size:13px; background:#00ffff; color:red;", err)
        // })


      

        // new Promise((resolve,reject)=>{
        //     console.log('小A开始执行了')
        //     resolve(22)
        // }).then(res=>{
        //     console.log("%c [ res 小A开始执行了]", "font-size:13px; background:#00ffff; color:red;", res)
        //     new Promise((resolve,reject)=>{
        //         console.log('小B开始执行了')
        //         resolve()
        //     }).then(res=>{
        //         new Promise((resolve,reject)=>{
        //             console.log('小C开始执行了')
        //             resolve()
        //         }).then(res=>{
        //             console.log('全都执行完了！')
        //         })
        //     })
        // })

        // let getInfoA = new Promise((resolve, reject) => {
        //     console.log('小A开始执行了')
        //     resolve()
        // })
        // let getInfoB = new Promise((resolve, reject) => {
        //     console.log('小B开始执行了')
        //     resolve()
        // })
        // let getInfoC = new Promise((resolve, reject) => {
        //     console.log('小C开始执行了')
        //     resolve()
        // })
        // Promise.all(['getInfoA','getInfoB','getInfoC']).then(res=>console.log('全都执行完了！'))
        // // 接收一个Promise对象组成的数组作为参数，当这个数组所有的Promise对象状态都变成resolved或者rejected的时候，它才会去调用then方法。


        // function* doSomething() {  //Generator函数
        //     yield '吃饭'
        //     return '睡觉'
        // }

        // let newDoSomething = doSomething() // 自己执行不了，需要指向一个状态机

        // console.log(newDoSomething.next()) // {value: "吃饭", done: false}
        // console.log(newDoSomething.next()) // {value: "睡觉", done: true}


        // function f(){
        //     return new Promise(resolve=>{
        //         resolve('ffff')
        //     })
        // }
        // async function doSomething1(){
        //     let x = await f()
        //     console.log("%c [ x ]", "font-size:13px; background:#00ffff; color:red;", x)
        // }

        // doSomething1()


        // async function gotoplay(){
        //     let h = await 'xx'
        //     return h
        // }
        // console.log('gotoplay() :>> ', gotoplay());

        // gotoplay().then(res=>{
        //     console.log("%c [ res ]", "font-size:13px; background:#00ffff; color:red;", res)
        // })

        // async function gotoplay(){
        //     let h = await 'xx'
        //     reject('err')
        //     return h
        // }
        // console.log('gotoplay() :>> ', gotoplay());

        // gotoplay().catch(err=>{
        //     console.log("%c [ err ]", "font-size:13px; background:#00ffff; color:red;", err)
        // })

        function sleep(time){
            return new Promise((resolve,reject)=>{
                 setTimeout(()=>{
                    resolve()
                },time)
            })
        }

        // for(let i=0; i < 5; i++){
        //     sleep(1000 * i).then(data=>{
        //         console.log('i :>> ', i);
                 // // 延迟5秒打印i 0 1 2 3 4
        //     })
        // }

        // for(var i=0; i < 5; i++){
        //     setTimeout(()=>{
        //         console.log('i :>> ', i); // 5 次 5
        //     },1000)
        // }


        // (async ()=>{
        //     for(let i=0; i<5; i++){
        //         await sleep(1000)
        //         console.log('i :>> ', i); //*
        //     }
        // })()



        async function go(){ // 打印 hello word
            await new Promise(resolve=>{
                 setTimeout(()=>{
                    console.log('hello')
                    resolve()
                }, 200)
            })
             console.log('word')
        }
        
        go()
         




        
    </script>

    <script>
        
        // let const var 基础知识

        // const a = true
        // a = 990
        // console.log("%c [ a ]", "font-size:13px; background:#00ffff; color:red;", a)  //err

        // const person = {
        //     name:'小明'
        // }
        // console.log("%c [ person ]", "font-size:13px; background:#00ffff; color:red;", person)

        // person.name = '小红'
        // person.age = 19
        // console.log("%c [ person 2]", "font-size:13px; background:#00ffff; color:red;", person)//const 声明的常量，允许在不重新赋值的情况下修改它的值

        // let sex = '男'
        // let sex = '女' 

        // let sex = '男'
        // sex = '女'        //let可以改值,但是不能重复声明   

        // var sex = '男'    //var可以重复声明
        // var sex = '女'



        // 相当于
        // var a;  
        // console.log(a);  // undefined
        // a = 1;
        // console.log(a);  // 1

        // console.log(a);  undefined
        // var a = 1;

        // console.log(a);  // const let 必须先声明再使用
        // const a = 1; 
        // console.log(a)
        // console.log(a);
        // let a = 1; 
        // console.log(a)

        // let a = 2;
        // function func() {
        //     console.log(a);
        //     // var a = 1;  //undefined 
        //     let a = 1;  //Cannot access 'a' before initialization
        //     // const a = 1;  //Cannot access 'a' before initialization
        // }
        // func();

        // var/function
        // var age = 18;
        // function add() {}
        // console.log(window.age);   //18
        // console.log(window.add === add);   //true

        // let/const
        // let age = 18;
        // const add = function () {};
        // console.log(window.age);   //undefined
        // console.log(window.add === add); //false

        // function func(){
        //     for (let i = 0; i < 3; i++) {
        //         console.log(i);  //0,1,2
        //     }  
        //     console.log(i);    //报错
        // }
        
        // func()
      

        //出了块级作用域就被销毁了，在全局范围内找不到了


        // for (var i = 0; i < 3; i++) {
        //     console.log(i);   //0,1,2
        // }
        //     console.log(i); //3

        




    </script>
</body>

</html>