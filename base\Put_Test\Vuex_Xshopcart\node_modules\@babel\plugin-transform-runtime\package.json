{"name": "@babel/plugin-transform-runtime", "version": "7.11.5", "description": "Externalise references to helpers and builtins, automatically polyfilling your code without polluting globals", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-runtime"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "browser": {"./lib/get-runtime-path/index.js": "./lib/get-runtime-path/browser.js", "./src/get-runtime-path/index.js": "./src/get-runtime-path/browser.js"}, "dependencies": {"@babel/helper-module-imports": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4", "resolve": "^1.8.1", "semver": "^5.5.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.11.5", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/helpers": "^7.10.4", "@babel/plugin-transform-typeof-symbol": "^7.10.4", "@babel/preset-env": "^7.11.5", "@babel/runtime": "^7.11.0", "@babel/runtime-corejs3": "^7.11.0", "@babel/template": "^7.10.4", "@babel/types": "^7.11.5", "make-dir": "^2.1.0"}, "gitHead": "af64ccb2b00bc7574943674996c2f0507cdbfb6f"}