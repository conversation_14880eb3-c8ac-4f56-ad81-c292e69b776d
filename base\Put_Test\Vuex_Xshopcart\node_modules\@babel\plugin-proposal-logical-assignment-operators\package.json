{"name": "@babel/plugin-proposal-logical-assignment-operators", "version": "7.11.0", "description": "Transforms logical assignment operators into short-circuited assignments", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-logical-assignment-operators"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.11.0", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.0"}, "gitHead": "38dda069eeac2e31bce3f56290998d30bee1ed6b"}