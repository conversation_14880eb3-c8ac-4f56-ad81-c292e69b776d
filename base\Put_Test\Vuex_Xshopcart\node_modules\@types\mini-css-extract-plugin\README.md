# Installation
> `npm install --save @types/mini-css-extract-plugin`

# Summary
This package contains type definitions for mini-css-extract-plugin (https://github.com/webpack-contrib/mini-css-extract-plugin).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mini-css-extract-plugin.

### Additional Details
 * Last updated: Thu, 20 Feb 2020 19:06:34 GMT
 * Dependencies: [@types/webpack](https://npmjs.com/package/@types/webpack)
 * Global values: none

# Credits
These definitions were written by [<PERSON><PERSON><PERSON><PERSON>](https://github.com/JounQin), [<PERSON><PERSON><PERSON>](https://github.com/dobogo), [<PERSON>](https://github.com/skovy), and [<PERSON><PERSON><PERSON>](https://github.com/peter<PERSON><PERSON><PERSON><PERSON>).
