module.exports={A:{A:{"2":"I F E D A B kB"},B:{"2":"C O P","194":"M V N WB KB","257":"H J K L"},C:{"2":"0 1 2 3 4 5 6 7 8 9 tB RB G W I F E D A B C O P H J K L X Y Z a b c d e f g h i j k l m n o p q r s t u v w x Q z AB YB CB JB EB FB GB HB DB BB y T S LB MB NB OB PB QB IB SB M V jB rB","16":"N iB"},D:{"2":"G W I F E D A B C O P H J K L X Y Z a b c d e f g h i j k l m n o p q","16":"0 1 2 3 4 5 6 7 8 9 r s t u v w x Q z","194":"AB YB CB JB EB FB GB HB DB BB y T S LB MB NB OB PB QB IB SB M V N WB KB TB uB aB bB"},E:{"2":"G W I F E cB UB eB fB gB","16":"D A B C O P hB VB R U lB mB"},F:{"2":"D B C H J K L X Y Z a b c d e f g h nB oB pB qB R XB sB U","16":"0 1 2 3 4 5 6 7 8 9 i j k l m n o p q r s t u v w x Q z AB CB EB FB GB HB DB BB y T S"},G:{"2":"E UB TC ZB vB wB xB yB","16":"zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC"},H:{"16":"CC"},I:{"2":"RB G DC EC FC GC ZB HC IC","16":"N"},J:{"2":"F A"},K:{"2":"A B C R XB U","16":"Q"},L:{"16":"TB"},M:{"16":"M"},N:{"2":"A","16":"B"},O:{"16":"JC"},P:{"16":"G KC LC MC NC OC VB PC QC"},Q:{"16":"RC"},R:{"16":"SC"},S:{"2":"dB"}},B:6,C:"Token Binding"};
