{"name": "@types/json-schema", "version": "7.0.6", "description": "TypeScript definitions for json-schema 4.0, 6.0 and", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/bcherny", "githubUsername": "b<PERSON>ny"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/cyrilletuzi", "githubUsername": "cyr<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/lucianbuzzo", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/rolandjitsu", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/json-schema"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "bb4b22e70ee94bf7b787307444fcee2f57a8869f012a460ed8f15b5b6985ed70", "typeScriptVersion": "3.1"}