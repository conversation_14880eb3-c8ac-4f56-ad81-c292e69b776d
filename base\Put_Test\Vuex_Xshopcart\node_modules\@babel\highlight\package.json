{"name": "@babel/highlight", "version": "7.10.4", "description": "Syntax highlight JavaScript strings for output in terminals.", "author": "suchipi <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-highlight"}, "main": "lib/index.js", "dependencies": {"@babel/helper-validator-identifier": "^7.10.4", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}, "devDependencies": {"strip-ansi": "^4.0.0"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df"}