# [postcss][postcss]-normalize-positions

> Normalize positions with PostCSS.

## Install

With [npm](https://npmjs.org/package/postcss-normalize-positions) do:

```
npm install postcss-normalize-positions --save
```

## Example

### Input

```css
div {
    background-position: bottom left;
}
```

### Output

```css
div {
    background-position:0 100%;
}
``` 

## Usage

See the [PostCSS documentation](https://github.com/postcss/postcss#usage) for
examples for your environment.

## Contributors

See [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).

## License

MIT © [<PERSON>](http://beneb.info)

[postcss]: https://github.com/postcss/postcss
