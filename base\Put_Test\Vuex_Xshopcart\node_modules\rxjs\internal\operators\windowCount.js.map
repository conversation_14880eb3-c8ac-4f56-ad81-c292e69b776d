{"version": 3, "file": "windowCount.js", "sources": ["../../src/internal/operators/windowCount.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,4CAA2C;AAE3C,sCAAqC;AAkErC,SAAgB,WAAW,CAAI,UAAkB,EAClB,gBAA4B;IAA5B,iCAAA,EAAA,oBAA4B;IACzD,OAAO,SAAS,2BAA2B,CAAC,MAAqB;QAC/D,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,mBAAmB,CAAI,UAAU,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAC/E,CAAC,CAAC;AACJ,CAAC;AALD,kCAKC;AAED;IAEE,6BAAoB,UAAkB,EAClB,gBAAwB;QADxB,eAAU,GAAV,UAAU,CAAQ;QAClB,qBAAgB,GAAhB,gBAAgB,CAAQ;IAC5C,CAAC;IAED,kCAAI,GAAJ,UAAK,UAAqC,EAAE,MAAW;QACrD,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,qBAAqB,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACzG,CAAC;IACH,0BAAC;AAAD,CAAC,AATD,IASC;AAOD;IAAuC,yCAAa;IAIlD,+BAAsB,WAAsC,EACxC,UAAkB,EAClB,gBAAwB;QAF5C,YAGE,kBAAM,WAAW,CAAC,SAEnB;QALqB,iBAAW,GAAX,WAAW,CAA2B;QACxC,gBAAU,GAAV,UAAU,CAAQ;QAClB,sBAAgB,GAAhB,gBAAgB,CAAQ;QALpC,aAAO,GAAiB,CAAE,IAAI,iBAAO,EAAK,CAAE,CAAC;QAC7C,WAAK,GAAW,CAAC,CAAC;QAMxB,WAAW,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;;IACpC,CAAC;IAES,qCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;QAC/F,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;QAE3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACxB;QACD,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,UAAU,GAAG,CAAC,CAAC;QACtC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,gBAAgB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACxD,OAAO,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC;SAC5B;QACD,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,gBAAgB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACzD,IAAM,QAAM,GAAG,IAAI,iBAAO,EAAK,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,QAAM,CAAC,CAAC;YACrB,WAAW,CAAC,IAAI,CAAC,QAAM,CAAC,CAAC;SAC1B;IACH,CAAC;IAES,sCAAM,GAAhB,UAAiB,GAAQ;QACvB,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAI,OAAO,EAAE;YACX,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBACzC,OAAO,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aAC5B;SACF;QACD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAES,yCAAS,GAAnB;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAI,OAAO,EAAE;YACX,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBACzC,OAAO,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC;aAC5B;SACF;QACD,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;IAES,4CAAY,GAAtB;QACE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;IACH,4BAAC;AAAD,CAAC,AAxDD,CAAuC,uBAAU,GAwDhD"}