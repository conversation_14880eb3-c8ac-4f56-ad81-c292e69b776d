{"name": "import-cwd", "version": "2.1.0", "description": "Import a module like with `require()` but from the current working directory", "license": "MIT", "repository": "sindresorhus/import-cwd", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["require", "resolve", "path", "module", "from", "like", "cwd", "current", "working", "directory", "import"], "dependencies": {"import-from": "^2.1.0"}, "devDependencies": {"ava": "*", "xo": "*"}}