{"name": "@babel/plugin-proposal-unicode-property-regex", "version": "7.10.4", "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "homepage": "https://babeljs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "engines": {"node": ">=4"}, "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-unicode-property-regex"}, "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df"}