{"name": "promise-inflight", "version": "1.0.1", "description": "One promise for multiple requests in flight to avoid async duplication", "main": "inflight.js", "files": ["inflight.js"], "license": "ISC", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "<PERSON> <<EMAIL>> (http://re-becca.org/)", "devDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/iarna/promise-inflight.git"}, "bugs": {"url": "https://github.com/iarna/promise-inflight/issues"}, "homepage": "https://github.com/iarna/promise-inflight#readme"}