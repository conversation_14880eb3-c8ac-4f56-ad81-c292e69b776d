{"name": "object-inspect", "version": "1.8.0", "description": "string representations of objects in node and the browser", "main": "index.js", "devDependencies": {"@ljharb/eslint-config": "^17.1.0", "aud": "^1.1.2", "core-js": "^2.6.11", "eslint": "^7.1.0", "for-each": "^0.3.3", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "string.prototype.repeat": "^1.0.0", "tape": "^5.0.1"}, "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint .", "test": "npm run tests-only", "pretests-only": "node test-core-js", "tests-only": "tape test/*.js", "posttest": "npx aud --production", "coverage": "nyc npm run tests-only"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/object-inspect.git"}, "homepage": "https://github.com/inspect-js/object-inspect", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "browser": {"./util.inspect.js": false}, "greenkeeper": {"ignore": ["nyc", "core-js"]}}