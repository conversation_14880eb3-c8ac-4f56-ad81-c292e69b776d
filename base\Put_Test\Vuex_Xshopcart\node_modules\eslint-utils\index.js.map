{"version": 3, "file": "index.js", "sources": ["src/get-innermost-scope.js", "src/find-variable.js", "src/token-predicate.js", "src/get-function-head-location.js", "src/get-static-value.js", "src/get-string-if-constant.js", "src/get-property-name.js", "src/get-function-name-with-kind.js", "src/has-side-effect.js", "src/is-parenthesized.js", "src/pattern-matcher.js", "src/reference-tracker.js", "src/index.js"], "sourcesContent": ["/**\n * Get the innermost scope which contains a given location.\n * @param {Scope} initialScope The initial scope to search.\n * @param {Node} node The location to search.\n * @returns {Scope} The innermost scope.\n */\nexport function getInnermostScope(initialScope, node) {\n    const location = node.range[0]\n\n    let scope = initialScope\n    let found = false\n    do {\n        found = false\n        for (const childScope of scope.childScopes) {\n            const range = childScope.block.range\n\n            if (range[0] <= location && location < range[1]) {\n                scope = childScope\n                found = true\n                break\n            }\n        }\n    } while (found)\n\n    return scope\n}\n", "import { getInnermostScope } from \"./get-innermost-scope\"\n\n/**\n * Find the variable of a given name.\n * @param {Scope} initialScope The scope to start finding.\n * @param {string|Node} nameOrNode The variable name to find. If this is a Node object then it should be an Identifier node.\n * @returns {Variable|null} The found variable or null.\n */\nexport function findVariable(initialScope, nameOrNode) {\n    let name = \"\"\n    let scope = initialScope\n\n    if (typeof nameOrNode === \"string\") {\n        name = nameOrNode\n    } else {\n        name = nameOrNode.name\n        scope = getInnermostScope(scope, nameOrNode)\n    }\n\n    while (scope != null) {\n        const variable = scope.set.get(name)\n        if (variable != null) {\n            return variable\n        }\n        scope = scope.upper\n    }\n\n    return null\n}\n", "/**\n * Negate the result of `this` calling.\n * @param {Token} token The token to check.\n * @returns {boolean} `true` if the result of `this(token)` is `false`.\n */\nfunction negate0(token) {\n    return !this(token) //eslint-disable-line no-invalid-this\n}\n\n/**\n * Creates the negate function of the given function.\n * @param {function(Token):boolean} f - The function to negate.\n * @returns {function(Token):boolean} Negated function.\n */\nfunction negate(f) {\n    return negate0.bind(f)\n}\n\n/**\n * Checks if the given token is an arrow token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is an arrow token.\n */\nexport function isArrowToken(token) {\n    return token.value === \"=>\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is a comma token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a comma token.\n */\nexport function isCommaToken(token) {\n    return token.value === \",\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is a semicolon token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a semicolon token.\n */\nexport function isSemicolonToken(token) {\n    return token.value === \";\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is a colon token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a colon token.\n */\nexport function isColonToken(token) {\n    return token.value === \":\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is an opening parenthesis token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is an opening parenthesis token.\n */\nexport function isOpeningParenToken(token) {\n    return token.value === \"(\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is a closing parenthesis token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a closing parenthesis token.\n */\nexport function isClosingParenToken(token) {\n    return token.value === \")\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is an opening square bracket token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is an opening square bracket token.\n */\nexport function isOpeningBracketToken(token) {\n    return token.value === \"[\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is a closing square bracket token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a closing square bracket token.\n */\nexport function isClosingBracketToken(token) {\n    return token.value === \"]\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is an opening brace token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is an opening brace token.\n */\nexport function isOpeningBraceToken(token) {\n    return token.value === \"{\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is a closing brace token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a closing brace token.\n */\nexport function isClosingBraceToken(token) {\n    return token.value === \"}\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is a comment token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a comment token.\n */\nexport function isCommentToken(token) {\n    return (\n        token.type === \"Line\" ||\n        token.type === \"Block\" ||\n        token.type === \"Shebang\"\n    )\n}\n\nexport const isNotArrowToken = negate(isArrowToken)\nexport const isNotCommaToken = negate(isCommaToken)\nexport const isNotSemicolonToken = negate(isSemicolonToken)\nexport const isNotColonToken = negate(isColonToken)\nexport const isNotOpeningParenToken = negate(isOpeningParenToken)\nexport const isNotClosingParenToken = negate(isClosingParenToken)\nexport const isNotOpeningBracketToken = negate(isOpeningBracketToken)\nexport const isNotClosingBracketToken = negate(isClosingBracketToken)\nexport const isNotOpeningBraceToken = negate(isOpeningBraceToken)\nexport const isNotClosingBraceToken = negate(isClosingBraceToken)\nexport const isNotCommentToken = negate(isCommentToken)\n", "import { isArrowToken, isOpeningParenToken } from \"./token-predicate\"\n\n/**\n * Get the `(` token of the given function node.\n * @param {Node} node - The function node to get.\n * @param {SourceCode} sourceCode - The source code object to get tokens.\n * @returns {Token} `(` token.\n */\nfunction getOpeningParenOfParams(node, sourceCode) {\n    return node.id\n        ? sourceCode.getTokenAfter(node.id, isOpeningParenToken)\n        : sourceCode.getFirstToken(node, isOpeningParenToken)\n}\n\n/**\n * Get the location of the given function node for reporting.\n * @param {Node} node - The function node to get.\n * @param {SourceCode} sourceCode - The source code object to get tokens.\n * @returns {string} The location of the function node for reporting.\n */\nexport function getFunctionHeadLocation(node, sourceCode) {\n    const parent = node.parent\n    let start = null\n    let end = null\n\n    if (node.type === \"ArrowFunctionExpression\") {\n        const arrowToken = sourceCode.getTokenBefore(node.body, isArrowToken)\n\n        start = arrowToken.loc.start\n        end = arrowToken.loc.end\n    } else if (\n        parent.type === \"Property\" ||\n        parent.type === \"MethodDefinition\"\n    ) {\n        start = parent.loc.start\n        end = getOpeningParenOfParams(node, sourceCode).loc.start\n    } else {\n        start = node.loc.start\n        end = getOpeningParenOfParams(node, sourceCode).loc.start\n    }\n\n    return {\n        start: Object.assign({}, start),\n        end: Object.assign({}, end),\n    }\n}\n", "/* globals BigInt, globalThis, global, self, window */\n\nimport { findVariable } from \"./find-variable\"\n\nconst globalObject =\n    typeof globalThis !== \"undefined\"\n        ? globalThis\n        : typeof self !== \"undefined\"\n        ? self\n        : typeof window !== \"undefined\"\n        ? window\n        : typeof global !== \"undefined\"\n        ? global\n        : {}\n\nconst builtinNames = Object.freeze(\n    new Set([\n        \"Array\",\n        \"ArrayBuffer\",\n        \"BigInt\",\n        \"BigInt64Array\",\n        \"BigUint64Array\",\n        \"Boolean\",\n        \"DataView\",\n        \"Date\",\n        \"decodeURI\",\n        \"decodeURIComponent\",\n        \"encodeURI\",\n        \"encodeURIComponent\",\n        \"escape\",\n        \"Float32Array\",\n        \"Float64Array\",\n        \"Function\",\n        \"Infinity\",\n        \"Int16Array\",\n        \"Int32Array\",\n        \"Int8Array\",\n        \"isFinite\",\n        \"isNaN\",\n        \"isPrototypeOf\",\n        \"JSON\",\n        \"Map\",\n        \"Math\",\n        \"NaN\",\n        \"Number\",\n        \"Object\",\n        \"parseFloat\",\n        \"parseInt\",\n        \"Promise\",\n        \"Proxy\",\n        \"Reflect\",\n        \"RegExp\",\n        \"Set\",\n        \"String\",\n        \"Symbol\",\n        \"Uint16Array\",\n        \"Uint32Array\",\n        \"Uint8Array\",\n        \"Uint8ClampedArray\",\n        \"undefined\",\n        \"unescape\",\n        \"WeakMap\",\n        \"WeakSet\",\n    ])\n)\nconst callAllowed = new Set(\n    [\n        Array.isArray,\n        typeof BigInt === \"function\" ? BigInt : undefined,\n        Boolean,\n        Date,\n        Date.parse,\n        decodeURI,\n        decodeURIComponent,\n        encodeURI,\n        encodeURIComponent,\n        escape,\n        isFinite,\n        isNaN,\n        isPrototypeOf,\n        ...Object.getOwnPropertyNames(Math)\n            .map(k => Math[k])\n            .filter(f => typeof f === \"function\"),\n        Number,\n        Number.isFinite,\n        Number.isNaN,\n        Number.parseFloat,\n        Number.parseInt,\n        Object,\n        Object.entries,\n        Object.is,\n        Object.isExtensible,\n        Object.isFrozen,\n        Object.isSealed,\n        Object.keys,\n        Object.values,\n        parseFloat,\n        parseInt,\n        RegExp,\n        String,\n        String.fromCharCode,\n        String.fromCodePoint,\n        String.raw,\n        Symbol,\n        Symbol.for,\n        Symbol.keyFor,\n        unescape,\n    ].filter(f => typeof f === \"function\")\n)\nconst callPassThrough = new Set([\n    Object.freeze,\n    Object.preventExtensions,\n    Object.seal,\n])\n\n/**\n * Get the property descriptor.\n * @param {object} object The object to get.\n * @param {string|number|symbol} name The property name to get.\n */\nfunction getPropertyDescriptor(object, name) {\n    let x = object\n    while ((typeof x === \"object\" || typeof x === \"function\") && x !== null) {\n        const d = Object.getOwnPropertyDescriptor(x, name)\n        if (d) {\n            return d\n        }\n        x = Object.getPrototypeOf(x)\n    }\n    return null\n}\n\n/**\n * Check if a property is getter or not.\n * @param {object} object The object to check.\n * @param {string|number|symbol} name The property name to check.\n */\nfunction isGetter(object, name) {\n    const d = getPropertyDescriptor(object, name)\n    return d != null && d.get != null\n}\n\n/**\n * Get the element values of a given node list.\n * @param {Node[]} nodeList The node list to get values.\n * @param {Scope|undefined} initialScope The initial scope to find variables.\n * @returns {any[]|null} The value list if all nodes are constant. Otherwise, null.\n */\nfunction getElementValues(nodeList, initialScope) {\n    const valueList = []\n\n    for (let i = 0; i < nodeList.length; ++i) {\n        const elementNode = nodeList[i]\n\n        if (elementNode == null) {\n            valueList.length = i + 1\n        } else if (elementNode.type === \"SpreadElement\") {\n            const argument = getStaticValueR(elementNode.argument, initialScope)\n            if (argument == null) {\n                return null\n            }\n            valueList.push(...argument.value)\n        } else {\n            const element = getStaticValueR(elementNode, initialScope)\n            if (element == null) {\n                return null\n            }\n            valueList.push(element.value)\n        }\n    }\n\n    return valueList\n}\n\nconst operations = Object.freeze({\n    ArrayExpression(node, initialScope) {\n        const elements = getElementValues(node.elements, initialScope)\n        return elements != null ? { value: elements } : null\n    },\n\n    AssignmentExpression(node, initialScope) {\n        if (node.operator === \"=\") {\n            return getStaticValueR(node.right, initialScope)\n        }\n        return null\n    },\n\n    //eslint-disable-next-line complexity\n    BinaryExpression(node, initialScope) {\n        if (node.operator === \"in\" || node.operator === \"instanceof\") {\n            // Not supported.\n            return null\n        }\n\n        const left = getStaticValueR(node.left, initialScope)\n        const right = getStaticValueR(node.right, initialScope)\n        if (left != null && right != null) {\n            switch (node.operator) {\n                case \"==\":\n                    return { value: left.value == right.value } //eslint-disable-line eqeqeq\n                case \"!=\":\n                    return { value: left.value != right.value } //eslint-disable-line eqeqeq\n                case \"===\":\n                    return { value: left.value === right.value }\n                case \"!==\":\n                    return { value: left.value !== right.value }\n                case \"<\":\n                    return { value: left.value < right.value }\n                case \"<=\":\n                    return { value: left.value <= right.value }\n                case \">\":\n                    return { value: left.value > right.value }\n                case \">=\":\n                    return { value: left.value >= right.value }\n                case \"<<\":\n                    return { value: left.value << right.value }\n                case \">>\":\n                    return { value: left.value >> right.value }\n                case \">>>\":\n                    return { value: left.value >>> right.value }\n                case \"+\":\n                    return { value: left.value + right.value }\n                case \"-\":\n                    return { value: left.value - right.value }\n                case \"*\":\n                    return { value: left.value * right.value }\n                case \"/\":\n                    return { value: left.value / right.value }\n                case \"%\":\n                    return { value: left.value % right.value }\n                case \"**\":\n                    return { value: Math.pow(left.value, right.value) }\n                case \"|\":\n                    return { value: left.value | right.value }\n                case \"^\":\n                    return { value: left.value ^ right.value }\n                case \"&\":\n                    return { value: left.value & right.value }\n\n                // no default\n            }\n        }\n\n        return null\n    },\n\n    CallExpression(node, initialScope) {\n        const calleeNode = node.callee\n        const args = getElementValues(node.arguments, initialScope)\n\n        if (args != null) {\n            if (calleeNode.type === \"MemberExpression\") {\n                const object = getStaticValueR(calleeNode.object, initialScope)\n                const property = calleeNode.computed\n                    ? getStaticValueR(calleeNode.property, initialScope)\n                    : { value: calleeNode.property.name }\n\n                if (object != null && property != null) {\n                    const receiver = object.value\n                    const methodName = property.value\n                    if (callAllowed.has(receiver[methodName])) {\n                        return { value: receiver[methodName](...args) }\n                    }\n                    if (callPassThrough.has(receiver[methodName])) {\n                        return { value: args[0] }\n                    }\n                }\n            } else {\n                const callee = getStaticValueR(calleeNode, initialScope)\n                if (callee != null) {\n                    const func = callee.value\n                    if (callAllowed.has(func)) {\n                        return { value: func(...args) }\n                    }\n                    if (callPassThrough.has(func)) {\n                        return { value: args[0] }\n                    }\n                }\n            }\n        }\n\n        return null\n    },\n\n    ConditionalExpression(node, initialScope) {\n        const test = getStaticValueR(node.test, initialScope)\n        if (test != null) {\n            return test.value\n                ? getStaticValueR(node.consequent, initialScope)\n                : getStaticValueR(node.alternate, initialScope)\n        }\n        return null\n    },\n\n    ExpressionStatement(node, initialScope) {\n        return getStaticValueR(node.expression, initialScope)\n    },\n\n    Identifier(node, initialScope) {\n        if (initialScope != null) {\n            const variable = findVariable(initialScope, node)\n\n            // Built-in globals.\n            if (\n                variable != null &&\n                variable.defs.length === 0 &&\n                builtinNames.has(variable.name) &&\n                variable.name in globalObject\n            ) {\n                return { value: globalObject[variable.name] }\n            }\n\n            // Constants.\n            if (variable != null && variable.defs.length === 1) {\n                const def = variable.defs[0]\n                if (\n                    def.parent &&\n                    def.parent.kind === \"const\" &&\n                    // TODO(mysticatea): don't support destructuring here.\n                    def.node.id.type === \"Identifier\"\n                ) {\n                    return getStaticValueR(def.node.init, initialScope)\n                }\n            }\n        }\n        return null\n    },\n\n    Literal(node) {\n        //istanbul ignore if : this is implementation-specific behavior.\n        if ((node.regex != null || node.bigint != null) && node.value == null) {\n            // It was a RegExp/BigInt literal, but Node.js didn't support it.\n            return null\n        }\n        return { value: node.value }\n    },\n\n    LogicalExpression(node, initialScope) {\n        const left = getStaticValueR(node.left, initialScope)\n        if (left != null) {\n            if (\n                (node.operator === \"||\" && Boolean(left.value) === true) ||\n                (node.operator === \"&&\" && Boolean(left.value) === false)\n            ) {\n                return left\n            }\n\n            const right = getStaticValueR(node.right, initialScope)\n            if (right != null) {\n                return right\n            }\n        }\n\n        return null\n    },\n\n    MemberExpression(node, initialScope) {\n        const object = getStaticValueR(node.object, initialScope)\n        const property = node.computed\n            ? getStaticValueR(node.property, initialScope)\n            : { value: node.property.name }\n\n        if (\n            object != null &&\n            property != null &&\n            !isGetter(object.value, property.value)\n        ) {\n            return { value: object.value[property.value] }\n        }\n        return null\n    },\n\n    NewExpression(node, initialScope) {\n        const callee = getStaticValueR(node.callee, initialScope)\n        const args = getElementValues(node.arguments, initialScope)\n\n        if (callee != null && args != null) {\n            const Func = callee.value\n            if (callAllowed.has(Func)) {\n                return { value: new Func(...args) }\n            }\n        }\n\n        return null\n    },\n\n    ObjectExpression(node, initialScope) {\n        const object = {}\n\n        for (const propertyNode of node.properties) {\n            if (propertyNode.type === \"Property\") {\n                if (propertyNode.kind !== \"init\") {\n                    return null\n                }\n                const key = propertyNode.computed\n                    ? getStaticValueR(propertyNode.key, initialScope)\n                    : { value: propertyNode.key.name }\n                const value = getStaticValueR(propertyNode.value, initialScope)\n                if (key == null || value == null) {\n                    return null\n                }\n                object[key.value] = value.value\n            } else if (\n                propertyNode.type === \"SpreadElement\" ||\n                propertyNode.type === \"ExperimentalSpreadProperty\"\n            ) {\n                const argument = getStaticValueR(\n                    propertyNode.argument,\n                    initialScope\n                )\n                if (argument == null) {\n                    return null\n                }\n                Object.assign(object, argument.value)\n            } else {\n                return null\n            }\n        }\n\n        return { value: object }\n    },\n\n    SequenceExpression(node, initialScope) {\n        const last = node.expressions[node.expressions.length - 1]\n        return getStaticValueR(last, initialScope)\n    },\n\n    TaggedTemplateExpression(node, initialScope) {\n        const tag = getStaticValueR(node.tag, initialScope)\n        const expressions = getElementValues(\n            node.quasi.expressions,\n            initialScope\n        )\n\n        if (tag != null && expressions != null) {\n            const func = tag.value\n            const strings = node.quasi.quasis.map(q => q.value.cooked)\n            strings.raw = node.quasi.quasis.map(q => q.value.raw)\n\n            if (func === String.raw) {\n                return { value: func(strings, ...expressions) }\n            }\n        }\n\n        return null\n    },\n\n    TemplateLiteral(node, initialScope) {\n        const expressions = getElementValues(node.expressions, initialScope)\n        if (expressions != null) {\n            let value = node.quasis[0].value.cooked\n            for (let i = 0; i < expressions.length; ++i) {\n                value += expressions[i]\n                value += node.quasis[i + 1].value.cooked\n            }\n            return { value }\n        }\n        return null\n    },\n\n    UnaryExpression(node, initialScope) {\n        if (node.operator === \"delete\") {\n            // Not supported.\n            return null\n        }\n        if (node.operator === \"void\") {\n            return { value: undefined }\n        }\n\n        const arg = getStaticValueR(node.argument, initialScope)\n        if (arg != null) {\n            switch (node.operator) {\n                case \"-\":\n                    return { value: -arg.value }\n                case \"+\":\n                    return { value: +arg.value } //eslint-disable-line no-implicit-coercion\n                case \"!\":\n                    return { value: !arg.value }\n                case \"~\":\n                    return { value: ~arg.value }\n                case \"typeof\":\n                    return { value: typeof arg.value }\n\n                // no default\n            }\n        }\n\n        return null\n    },\n})\n\n/**\n * Get the value of a given node if it's a static value.\n * @param {Node} node The node to get.\n * @param {Scope|undefined} initialScope The scope to start finding variable.\n * @returns {{value:any}|null} The static value of the node, or `null`.\n */\nfunction getStaticValueR(node, initialScope) {\n    if (node != null && Object.hasOwnProperty.call(operations, node.type)) {\n        return operations[node.type](node, initialScope)\n    }\n    return null\n}\n\n/**\n * Get the value of a given node if it's a static value.\n * @param {Node} node The node to get.\n * @param {Scope} [initialScope] The scope to start finding variable. Optional. If this scope was given, this tries to resolve identifier references which are in the given node as much as possible.\n * @returns {{value:any}|null} The static value of the node, or `null`.\n */\nexport function getStaticValue(node, initialScope = null) {\n    try {\n        return getStaticValueR(node, initialScope)\n    } catch (_error) {\n        return null\n    }\n}\n", "import { getStaticValue } from \"./get-static-value\"\n\n/**\n * Get the value of a given node if it's a literal or a template literal.\n * @param {Node} node The node to get.\n * @param {Scope} [initialScope] The scope to start finding variable. Optional. If the node is an Identifier node and this scope was given, this checks the variable of the identifier, and returns the value of it if the variable is a constant.\n * @returns {string|null} The value of the node, or `null`.\n */\nexport function getStringIfConstant(node, initialScope = null) {\n    // Handle the literals that the platform doesn't support natively.\n    if (node && node.type === \"Literal\" && node.value === null) {\n        if (node.regex) {\n            return `/${node.regex.pattern}/${node.regex.flags}`\n        }\n        if (node.bigint) {\n            return node.bigint\n        }\n    }\n\n    const evaluated = getStaticValue(node, initialScope)\n    return evaluated && String(evaluated.value)\n}\n", "import { getStringIfConstant } from \"./get-string-if-constant\"\n\n/**\n * Get the property name from a MemberExpression node or a Property node.\n * @param {Node} node The node to get.\n * @param {Scope} [initialScope] The scope to start finding variable. Optional. If the node is a computed property node and this scope was given, this checks the computed property name by the `getStringIfConstant` function with the scope, and returns the value of it.\n * @returns {string|null} The property name of the node.\n */\nexport function getPropertyName(node, initialScope) {\n    switch (node.type) {\n        case \"MemberExpression\":\n            if (node.computed) {\n                return getStringIfConstant(node.property, initialScope)\n            }\n            return node.property.name\n\n        case \"Property\":\n        case \"MethodDefinition\":\n            if (node.computed) {\n                return getStringIfConstant(node.key, initialScope)\n            }\n            if (node.key.type === \"Literal\") {\n                return String(node.key.value)\n            }\n            return node.key.name\n\n        // no default\n    }\n\n    return null\n}\n", "import { getPropertyName } from \"./get-property-name\"\n\n/**\n * Get the name and kind of the given function node.\n * @param {ASTNode} node - The function node to get.\n * @returns {string} The name and kind of the function node.\n */\nexport function getFunctionNameWithKind(node) {\n    const parent = node.parent\n    const tokens = []\n\n    if (parent.type === \"MethodDefinition\" && parent.static) {\n        tokens.push(\"static\")\n    }\n    if (node.async) {\n        tokens.push(\"async\")\n    }\n    if (node.generator) {\n        tokens.push(\"generator\")\n    }\n\n    if (node.type === \"ArrowFunctionExpression\") {\n        tokens.push(\"arrow\", \"function\")\n    } else if (\n        parent.type === \"Property\" ||\n        parent.type === \"MethodDefinition\"\n    ) {\n        if (parent.kind === \"constructor\") {\n            return \"constructor\"\n        }\n        if (parent.kind === \"get\") {\n            tokens.push(\"getter\")\n        } else if (parent.kind === \"set\") {\n            tokens.push(\"setter\")\n        } else {\n            tokens.push(\"method\")\n        }\n    } else {\n        tokens.push(\"function\")\n    }\n\n    if (node.id) {\n        tokens.push(`'${node.id.name}'`)\n    } else {\n        const name = getPropertyName(parent)\n\n        if (name) {\n            tokens.push(`'${name}'`)\n        }\n    }\n\n    return tokens.join(\" \")\n}\n", "import evk from \"eslint-visitor-keys\"\n\nconst typeConversionBinaryOps = Object.freeze(\n    new Set([\n        \"==\",\n        \"!=\",\n        \"<\",\n        \"<=\",\n        \">\",\n        \">=\",\n        \"<<\",\n        \">>\",\n        \">>>\",\n        \"+\",\n        \"-\",\n        \"*\",\n        \"/\",\n        \"%\",\n        \"|\",\n        \"^\",\n        \"&\",\n        \"in\",\n    ])\n)\nconst typeConversionUnaryOps = Object.freeze(new Set([\"-\", \"+\", \"!\", \"~\"]))\nconst visitor = Object.freeze(\n    Object.assign(Object.create(null), {\n        $visit(node, options, visitorKeys) {\n            const { type } = node\n\n            if (typeof this[type] === \"function\") {\n                return this[type](node, options, visitorKeys)\n            }\n\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n\n        $visitChildren(node, options, visitorKeys) {\n            const { type } = node\n\n            for (const key of visitorKeys[type] || evk.getKeys(node)) {\n                const value = node[key]\n\n                if (Array.isArray(value)) {\n                    for (const element of value) {\n                        if (\n                            element &&\n                            this.$visit(element, options, visitorKeys)\n                        ) {\n                            return true\n                        }\n                    }\n                } else if (value && this.$visit(value, options, visitorKeys)) {\n                    return true\n                }\n            }\n\n            return false\n        },\n\n        ArrowFunctionExpression() {\n            return false\n        },\n        AssignmentExpression() {\n            return true\n        },\n        AwaitExpression() {\n            return true\n        },\n        BinaryExpression(node, options, visitorKeys) {\n            if (\n                options.considerImplicitTypeConversion &&\n                typeConversionBinaryOps.has(node.operator) &&\n                (node.left.type !== \"Literal\" || node.right.type !== \"Literal\")\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        CallExpression() {\n            return true\n        },\n        FunctionExpression() {\n            return false\n        },\n        ImportExpression() {\n            return true\n        },\n        MemberExpression(node, options, visitorKeys) {\n            if (options.considerGetters) {\n                return true\n            }\n            if (\n                options.considerImplicitTypeConversion &&\n                node.computed &&\n                node.property.type !== \"Literal\"\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        MethodDefinition(node, options, visitorKeys) {\n            if (\n                options.considerImplicitTypeConversion &&\n                node.computed &&\n                node.key.type !== \"Literal\"\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        NewExpression() {\n            return true\n        },\n        Property(node, options, visitorKeys) {\n            if (\n                options.considerImplicitTypeConversion &&\n                node.computed &&\n                node.key.type !== \"Literal\"\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        UnaryExpression(node, options, visitorKeys) {\n            if (node.operator === \"delete\") {\n                return true\n            }\n            if (\n                options.considerImplicitTypeConversion &&\n                typeConversionUnaryOps.has(node.operator) &&\n                node.argument.type !== \"Literal\"\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        UpdateExpression() {\n            return true\n        },\n        YieldExpression() {\n            return true\n        },\n    })\n)\n\n/**\n * Check whether a given node has any side effect or not.\n * @param {Node} node The node to get.\n * @param {SourceCode} sourceCode The source code object.\n * @param {object} [options] The option object.\n * @param {boolean} [options.considerGetters=false] If `true` then it considers member accesses as the node which has side effects.\n * @param {boolean} [options.considerImplicitTypeConversion=false] If `true` then it considers implicit type conversion as the node which has side effects.\n * @param {object} [options.visitorKeys=evk.KEYS] The keys to traverse nodes. Use `context.getSourceCode().visitorKeys`.\n * @returns {boolean} `true` if the node has a certain side effect.\n */\nexport function hasSideEffect(\n    node,\n    sourceCode,\n    { considerGetters = false, considerImplicitTypeConversion = false } = {}\n) {\n    return visitor.$visit(\n        node,\n        { considerGetters, considerImplicitTypeConversion },\n        sourceCode.visitorKeys || evk.KEYS\n    )\n}\n", "import { isClosingParenToken, isOpeningParenToken } from \"./token-predicate\"\n\n/**\n * Get the left parenthesis of the parent node syntax if it exists.\n * E.g., `if (a) {}` then the `(`.\n * @param {Node} node The AST node to check.\n * @param {SourceCode} sourceCode The source code object to get tokens.\n * @returns {Token|null} The left parenthesis of the parent node syntax\n */\nfunction getParentSyntaxParen(node, sourceCode) {\n    const parent = node.parent\n\n    switch (parent.type) {\n        case \"CallExpression\":\n        case \"NewExpression\":\n            if (parent.arguments.length === 1 && parent.arguments[0] === node) {\n                return sourceCode.getTokenAfter(\n                    parent.callee,\n                    isOpeningParenToken\n                )\n            }\n            return null\n\n        case \"DoWhileStatement\":\n            if (parent.test === node) {\n                return sourceCode.getTokenAfter(\n                    parent.body,\n                    isOpeningParenToken\n                )\n            }\n            return null\n\n        case \"IfStatement\":\n        case \"WhileStatement\":\n            if (parent.test === node) {\n                return sourceCode.getFirstToken(parent, 1)\n            }\n            return null\n\n        case \"ImportExpression\":\n            if (parent.source === node) {\n                return sourceCode.getFirstToken(parent, 1)\n            }\n            return null\n\n        case \"SwitchStatement\":\n            if (parent.discriminant === node) {\n                return sourceCode.getFirstToken(parent, 1)\n            }\n            return null\n\n        case \"WithStatement\":\n            if (parent.object === node) {\n                return sourceCode.getFirstToken(parent, 1)\n            }\n            return null\n\n        default:\n            return null\n    }\n}\n\n/**\n * Check whether a given node is parenthesized or not.\n * @param {number} times The number of parantheses.\n * @param {Node} node The AST node to check.\n * @param {SourceCode} sourceCode The source code object to get tokens.\n * @returns {boolean} `true` if the node is parenthesized the given times.\n */\n/**\n * Check whether a given node is parenthesized or not.\n * @param {Node} node The AST node to check.\n * @param {SourceCode} sourceCode The source code object to get tokens.\n * @returns {boolean} `true` if the node is parenthesized.\n */\nexport function isParenthesized(\n    timesOrNode,\n    nodeOrSourceCode,\n    optionalSourceCode\n) {\n    let times, node, sourceCode, maybeLeftParen, maybeRightParen\n    if (typeof timesOrNode === \"number\") {\n        times = timesOrNode | 0\n        node = nodeOrSourceCode\n        sourceCode = optionalSourceCode\n        if (!(times >= 1)) {\n            throw new TypeError(\"'times' should be a positive integer.\")\n        }\n    } else {\n        times = 1\n        node = timesOrNode\n        sourceCode = nodeOrSourceCode\n    }\n\n    if (node == null) {\n        return false\n    }\n\n    maybeLeftParen = maybeRightParen = node\n    do {\n        maybeLeftParen = sourceCode.getTokenBefore(maybeLeftParen)\n        maybeRightParen = sourceCode.getTokenAfter(maybeRightParen)\n    } while (\n        maybeLeftParen != null &&\n        maybeRightParen != null &&\n        isOpeningParenToken(maybeLeftParen) &&\n        isClosingParenToken(maybeRightParen) &&\n        // Avoid false positive such as `if (a) {}`\n        maybeLeftParen !== getParentSyntaxParen(node, sourceCode) &&\n        --times > 0\n    )\n\n    return times === 0\n}\n", "/**\n * <AUTHOR> <https://github.com/mysticatea>\n * See LICENSE file in root directory for full license.\n */\n\nconst placeholder = /\\$(?:[$&`']|[1-9][0-9]?)/gu\n\n/** @type {WeakMap<PatternMatcher, {pattern:RegExp,escaped:boolean}>} */\nconst internal = new WeakMap()\n\n/**\n * Check whether a given character is escaped or not.\n * @param {string} str The string to check.\n * @param {number} index The location of the character to check.\n * @returns {boolean} `true` if the character is escaped.\n */\nfunction isEscaped(str, index) {\n    let escaped = false\n    for (let i = index - 1; i >= 0 && str.charCodeAt(i) === 0x5c; --i) {\n        escaped = !escaped\n    }\n    return escaped\n}\n\n/**\n * Replace a given string by a given matcher.\n * @param {PatternMatcher} matcher The pattern matcher.\n * @param {string} str The string to be replaced.\n * @param {string} replacement The new substring to replace each matched part.\n * @returns {string} The replaced string.\n */\nfunction replaceS(matcher, str, replacement) {\n    const chunks = []\n    let index = 0\n\n    /** @type {RegExpExecArray} */\n    let match = null\n\n    /**\n     * @param {string} key The placeholder.\n     * @returns {string} The replaced string.\n     */\n    function replacer(key) {\n        switch (key) {\n            case \"$$\":\n                return \"$\"\n            case \"$&\":\n                return match[0]\n            case \"$`\":\n                return str.slice(0, match.index)\n            case \"$'\":\n                return str.slice(match.index + match[0].length)\n            default: {\n                const i = key.slice(1)\n                if (i in match) {\n                    return match[i]\n                }\n                return key\n            }\n        }\n    }\n\n    for (match of matcher.execAll(str)) {\n        chunks.push(str.slice(index, match.index))\n        chunks.push(replacement.replace(placeholder, replacer))\n        index = match.index + match[0].length\n    }\n    chunks.push(str.slice(index))\n\n    return chunks.join(\"\")\n}\n\n/**\n * Replace a given string by a given matcher.\n * @param {PatternMatcher} matcher The pattern matcher.\n * @param {string} str The string to be replaced.\n * @param {(...strs[])=>string} replace The function to replace each matched part.\n * @returns {string} The replaced string.\n */\nfunction replaceF(matcher, str, replace) {\n    const chunks = []\n    let index = 0\n\n    for (const match of matcher.execAll(str)) {\n        chunks.push(str.slice(index, match.index))\n        chunks.push(String(replace(...match, match.index, match.input)))\n        index = match.index + match[0].length\n    }\n    chunks.push(str.slice(index))\n\n    return chunks.join(\"\")\n}\n\n/**\n * The class to find patterns as considering escape sequences.\n */\nexport class PatternMatcher {\n    /**\n     * Initialize this matcher.\n     * @param {RegExp} pattern The pattern to match.\n     * @param {{escaped:boolean}} options The options.\n     */\n    constructor(pattern, { escaped = false } = {}) {\n        if (!(pattern instanceof RegExp)) {\n            throw new TypeError(\"'pattern' should be a RegExp instance.\")\n        }\n        if (!pattern.flags.includes(\"g\")) {\n            throw new Error(\"'pattern' should contains 'g' flag.\")\n        }\n\n        internal.set(this, {\n            pattern: new RegExp(pattern.source, pattern.flags),\n            escaped: Boolean(escaped),\n        })\n    }\n\n    /**\n     * Find the pattern in a given string.\n     * @param {string} str The string to find.\n     * @returns {IterableIterator<RegExpExecArray>} The iterator which iterate the matched information.\n     */\n    *execAll(str) {\n        const { pattern, escaped } = internal.get(this)\n        let match = null\n        let lastIndex = 0\n\n        pattern.lastIndex = 0\n        while ((match = pattern.exec(str)) != null) {\n            if (escaped || !isEscaped(str, match.index)) {\n                lastIndex = pattern.lastIndex\n                yield match\n                pattern.lastIndex = lastIndex\n            }\n        }\n    }\n\n    /**\n     * Check whether the pattern is found in a given string.\n     * @param {string} str The string to check.\n     * @returns {boolean} `true` if the pattern was found in the string.\n     */\n    test(str) {\n        const it = this.execAll(str)\n        const ret = it.next()\n        return !ret.done\n    }\n\n    /**\n     * Replace a given string.\n     * @param {string} str The string to be replaced.\n     * @param {(string|((...strs:string[])=>string))} replacer The string or function to replace. This is the same as the 2nd argument of `String.prototype.replace`.\n     * @returns {string} The replaced string.\n     */\n    [Symbol.replace](str, replacer) {\n        return typeof replacer === \"function\"\n            ? replaceF(this, String(str), replacer)\n            : replaceS(this, String(str), String(replacer))\n    }\n}\n", "import { findVariable } from \"./find-variable\"\nimport { getPropertyName } from \"./get-property-name\"\nimport { getStringIfConstant } from \"./get-string-if-constant\"\n\nconst IMPORT_TYPE = /^(?:Import|Export(?:All|Default|Named))Declaration$/u\nconst has = Function.call.bind(Object.hasOwnProperty)\n\nexport const READ = Symbol(\"read\")\nexport const CALL = Symbol(\"call\")\nexport const CONSTRUCT = Symbol(\"construct\")\nexport const ESM = Symbol(\"esm\")\n\nconst requireCall = { require: { [CALL]: true } }\n\n/**\n * Check whether a given variable is modified or not.\n * @param {Variable} variable The variable to check.\n * @returns {boolean} `true` if the variable is modified.\n */\nfunction isModifiedGlobal(variable) {\n    return (\n        variable == null ||\n        variable.defs.length !== 0 ||\n        variable.references.some(r => r.isWrite())\n    )\n}\n\n/**\n * Check if the value of a given node is passed through to the parent syntax as-is.\n * For example, `a` and `b` in (`a || b` and `c ? a : b`) are passed through.\n * @param {Node} node A node to check.\n * @returns {boolean} `true` if the node is passed through.\n */\nfunction isPassThrough(node) {\n    const parent = node.parent\n\n    switch (parent && parent.type) {\n        case \"ConditionalExpression\":\n            return parent.consequent === node || parent.alternate === node\n        case \"LogicalExpression\":\n            return true\n        case \"SequenceExpression\":\n            return parent.expressions[parent.expressions.length - 1] === node\n\n        default:\n            return false\n    }\n}\n\n/**\n * The reference tracker.\n */\nexport class ReferenceTracker {\n    /**\n     * Initialize this tracker.\n     * @param {Scope} globalScope The global scope.\n     * @param {object} [options] The options.\n     * @param {\"legacy\"|\"strict\"} [options.mode=\"strict\"] The mode to determine the ImportDeclaration's behavior for CJS modules.\n     * @param {string[]} [options.globalObjectNames=[\"global\",\"self\",\"window\"]] The variable names for Global Object.\n     */\n    constructor(\n        globalScope,\n        {\n            mode = \"strict\",\n            globalObjectNames = [\"global\", \"self\", \"window\"],\n        } = {}\n    ) {\n        this.variableStack = []\n        this.globalScope = globalScope\n        this.mode = mode\n        this.globalObjectNames = globalObjectNames.slice(0)\n    }\n\n    /**\n     * Iterate the references of global variables.\n     * @param {object} traceMap The trace map.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    *iterateGlobalReferences(traceMap) {\n        for (const key of Object.keys(traceMap)) {\n            const nextTraceMap = traceMap[key]\n            const path = [key]\n            const variable = this.globalScope.set.get(key)\n\n            if (isModifiedGlobal(variable)) {\n                continue\n            }\n\n            yield* this._iterateVariableReferences(\n                variable,\n                path,\n                nextTraceMap,\n                true\n            )\n        }\n\n        for (const key of this.globalObjectNames) {\n            const path = []\n            const variable = this.globalScope.set.get(key)\n\n            if (isModifiedGlobal(variable)) {\n                continue\n            }\n\n            yield* this._iterateVariableReferences(\n                variable,\n                path,\n                traceMap,\n                false\n            )\n        }\n    }\n\n    /**\n     * Iterate the references of CommonJS modules.\n     * @param {object} traceMap The trace map.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    *iterateCjsReferences(traceMap) {\n        for (const { node } of this.iterateGlobalReferences(requireCall)) {\n            const key = getStringIfConstant(node.arguments[0])\n            if (key == null || !has(traceMap, key)) {\n                continue\n            }\n\n            const nextTraceMap = traceMap[key]\n            const path = [key]\n\n            if (nextTraceMap[READ]) {\n                yield {\n                    node,\n                    path,\n                    type: READ,\n                    info: nextTraceMap[READ],\n                }\n            }\n            yield* this._iteratePropertyReferences(node, path, nextTraceMap)\n        }\n    }\n\n    /**\n     * Iterate the references of ES modules.\n     * @param {object} traceMap The trace map.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    *iterateEsmReferences(traceMap) {\n        const programNode = this.globalScope.block\n\n        for (const node of programNode.body) {\n            if (!IMPORT_TYPE.test(node.type) || node.source == null) {\n                continue\n            }\n            const moduleId = node.source.value\n\n            if (!has(traceMap, moduleId)) {\n                continue\n            }\n            const nextTraceMap = traceMap[moduleId]\n            const path = [moduleId]\n\n            if (nextTraceMap[READ]) {\n                yield { node, path, type: READ, info: nextTraceMap[READ] }\n            }\n\n            if (node.type === \"ExportAllDeclaration\") {\n                for (const key of Object.keys(nextTraceMap)) {\n                    const exportTraceMap = nextTraceMap[key]\n                    if (exportTraceMap[READ]) {\n                        yield {\n                            node,\n                            path: path.concat(key),\n                            type: READ,\n                            info: exportTraceMap[READ],\n                        }\n                    }\n                }\n            } else {\n                for (const specifier of node.specifiers) {\n                    const esm = has(nextTraceMap, ESM)\n                    const it = this._iterateImportReferences(\n                        specifier,\n                        path,\n                        esm\n                            ? nextTraceMap\n                            : this.mode === \"legacy\"\n                            ? Object.assign(\n                                  { default: nextTraceMap },\n                                  nextTraceMap\n                              )\n                            : { default: nextTraceMap }\n                    )\n\n                    if (esm) {\n                        yield* it\n                    } else {\n                        for (const report of it) {\n                            report.path = report.path.filter(exceptDefault)\n                            if (\n                                report.path.length >= 2 ||\n                                report.type !== READ\n                            ) {\n                                yield report\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    /**\n     * Iterate the references for a given variable.\n     * @param {Variable} variable The variable to iterate that references.\n     * @param {string[]} path The current path.\n     * @param {object} traceMap The trace map.\n     * @param {boolean} shouldReport = The flag to report those references.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    *_iterateVariableReferences(variable, path, traceMap, shouldReport) {\n        if (this.variableStack.includes(variable)) {\n            return\n        }\n        this.variableStack.push(variable)\n        try {\n            for (const reference of variable.references) {\n                if (!reference.isRead()) {\n                    continue\n                }\n                const node = reference.identifier\n\n                if (shouldReport && traceMap[READ]) {\n                    yield { node, path, type: READ, info: traceMap[READ] }\n                }\n                yield* this._iteratePropertyReferences(node, path, traceMap)\n            }\n        } finally {\n            this.variableStack.pop()\n        }\n    }\n\n    /**\n     * Iterate the references for a given AST node.\n     * @param rootNode The AST node to iterate references.\n     * @param {string[]} path The current path.\n     * @param {object} traceMap The trace map.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    //eslint-disable-next-line complexity\n    *_iteratePropertyReferences(rootNode, path, traceMap) {\n        let node = rootNode\n        while (isPassThrough(node)) {\n            node = node.parent\n        }\n\n        const parent = node.parent\n        if (parent.type === \"MemberExpression\") {\n            if (parent.object === node) {\n                const key = getPropertyName(parent)\n                if (key == null || !has(traceMap, key)) {\n                    return\n                }\n\n                path = path.concat(key) //eslint-disable-line no-param-reassign\n                const nextTraceMap = traceMap[key]\n                if (nextTraceMap[READ]) {\n                    yield {\n                        node: parent,\n                        path,\n                        type: READ,\n                        info: nextTraceMap[READ],\n                    }\n                }\n                yield* this._iteratePropertyReferences(\n                    parent,\n                    path,\n                    nextTraceMap\n                )\n            }\n            return\n        }\n        if (parent.type === \"CallExpression\") {\n            if (parent.callee === node && traceMap[CALL]) {\n                yield { node: parent, path, type: CALL, info: traceMap[CALL] }\n            }\n            return\n        }\n        if (parent.type === \"NewExpression\") {\n            if (parent.callee === node && traceMap[CONSTRUCT]) {\n                yield {\n                    node: parent,\n                    path,\n                    type: CONSTRUCT,\n                    info: traceMap[CONSTRUCT],\n                }\n            }\n            return\n        }\n        if (parent.type === \"AssignmentExpression\") {\n            if (parent.right === node) {\n                yield* this._iterateLhsReferences(parent.left, path, traceMap)\n                yield* this._iteratePropertyReferences(parent, path, traceMap)\n            }\n            return\n        }\n        if (parent.type === \"AssignmentPattern\") {\n            if (parent.right === node) {\n                yield* this._iterateLhsReferences(parent.left, path, traceMap)\n            }\n            return\n        }\n        if (parent.type === \"VariableDeclarator\") {\n            if (parent.init === node) {\n                yield* this._iterateLhsReferences(parent.id, path, traceMap)\n            }\n        }\n    }\n\n    /**\n     * Iterate the references for a given Pattern node.\n     * @param {Node} patternNode The Pattern node to iterate references.\n     * @param {string[]} path The current path.\n     * @param {object} traceMap The trace map.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    *_iterateLhsReferences(patternNode, path, traceMap) {\n        if (patternNode.type === \"Identifier\") {\n            const variable = findVariable(this.globalScope, patternNode)\n            if (variable != null) {\n                yield* this._iterateVariableReferences(\n                    variable,\n                    path,\n                    traceMap,\n                    false\n                )\n            }\n            return\n        }\n        if (patternNode.type === \"ObjectPattern\") {\n            for (const property of patternNode.properties) {\n                const key = getPropertyName(property)\n\n                if (key == null || !has(traceMap, key)) {\n                    continue\n                }\n\n                const nextPath = path.concat(key)\n                const nextTraceMap = traceMap[key]\n                if (nextTraceMap[READ]) {\n                    yield {\n                        node: property,\n                        path: nextPath,\n                        type: READ,\n                        info: nextTraceMap[READ],\n                    }\n                }\n                yield* this._iterateLhsReferences(\n                    property.value,\n                    nextPath,\n                    nextTraceMap\n                )\n            }\n            return\n        }\n        if (patternNode.type === \"AssignmentPattern\") {\n            yield* this._iterateLhsReferences(patternNode.left, path, traceMap)\n        }\n    }\n\n    /**\n     * Iterate the references for a given ModuleSpecifier node.\n     * @param {Node} specifierNode The ModuleSpecifier node to iterate references.\n     * @param {string[]} path The current path.\n     * @param {object} traceMap The trace map.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    *_iterateImportReferences(specifierNode, path, traceMap) {\n        const type = specifierNode.type\n\n        if (type === \"ImportSpecifier\" || type === \"ImportDefaultSpecifier\") {\n            const key =\n                type === \"ImportDefaultSpecifier\"\n                    ? \"default\"\n                    : specifierNode.imported.name\n            if (!has(traceMap, key)) {\n                return\n            }\n\n            path = path.concat(key) //eslint-disable-line no-param-reassign\n            const nextTraceMap = traceMap[key]\n            if (nextTraceMap[READ]) {\n                yield {\n                    node: specifierNode,\n                    path,\n                    type: READ,\n                    info: nextTraceMap[READ],\n                }\n            }\n            yield* this._iterateVariableReferences(\n                findVariable(this.globalScope, specifierNode.local),\n                path,\n                nextTraceMap,\n                false\n            )\n\n            return\n        }\n\n        if (type === \"ImportNamespaceSpecifier\") {\n            yield* this._iterateVariableReferences(\n                findVariable(this.globalScope, specifierNode.local),\n                path,\n                traceMap,\n                false\n            )\n            return\n        }\n\n        if (type === \"ExportSpecifier\") {\n            const key = specifierNode.local.name\n            if (!has(traceMap, key)) {\n                return\n            }\n\n            path = path.concat(key) //eslint-disable-line no-param-reassign\n            const nextTraceMap = traceMap[key]\n            if (nextTraceMap[READ]) {\n                yield {\n                    node: specifierNode,\n                    path,\n                    type: READ,\n                    info: nextTraceMap[READ],\n                }\n            }\n        }\n    }\n}\n\nReferenceTracker.READ = READ\nReferenceTracker.CALL = CALL\nReferenceTracker.CONSTRUCT = CONSTRUCT\nReferenceTracker.ESM = ESM\n\n/**\n * This is a predicate function for Array#filter.\n * @param {string} name A name part.\n * @param {number} index The index of the name.\n * @returns {boolean} `false` if it's default.\n */\nfunction exceptDefault(name, index) {\n    return !(index === 1 && name === \"default\")\n}\n", "import { findVariable } from \"./find-variable\"\nimport { getFunctionHeadLocation } from \"./get-function-head-location\"\nimport { getFunctionNameWithKind } from \"./get-function-name-with-kind\"\nimport { getInnermostScope } from \"./get-innermost-scope\"\nimport { getPropertyName } from \"./get-property-name\"\nimport { getStaticValue } from \"./get-static-value\"\nimport { getStringIfConstant } from \"./get-string-if-constant\"\nimport { hasSideEffect } from \"./has-side-effect\"\nimport { isParenthesized } from \"./is-parenthesized\"\nimport { PatternMatcher } from \"./pattern-matcher\"\nimport {\n    CALL,\n    CONSTRUCT,\n    ESM,\n    READ,\n    ReferenceTracker,\n} from \"./reference-tracker\"\nimport {\n    isArrowToken,\n    isClosingBraceToken,\n    isClosingBracketToken,\n    isClosingParenToken,\n    isColonToken,\n    isCommaToken,\n    isCommentToken,\n    isNotArrowToken,\n    isNotClosingBraceToken,\n    isNotClosingBracketToken,\n    isNotClosing<PERSON>arenToken,\n    isNotColonToken,\n    isNotCommaToken,\n    isNotCommentToken,\n    isNotOpeningBraceToken,\n    isNotOpeningBracketToken,\n    isNotOpeningParenToken,\n    isNotSemicolonToken,\n    isOpeningBraceToken,\n    isOpeningBracketToken,\n    isOpeningParenToken,\n    isSemicolonToken,\n} from \"./token-predicate\"\n\nexport default {\n    CALL,\n    CONSTRUCT,\n    ESM,\n    findVariable,\n    getFunctionHeadLocation,\n    getFunctionNameWithKind,\n    getInnermostScope,\n    getPropertyName,\n    getStaticValue,\n    getStringIfConstant,\n    hasSideEffect,\n    isArrowToken,\n    isClosingBraceToken,\n    isClosingBracketToken,\n    isClosingParenToken,\n    isColonToken,\n    isCommaToken,\n    isCommentToken,\n    isNotArrowToken,\n    isNotClosingBraceToken,\n    isNotClosingBracketToken,\n    isNotClosingParenToken,\n    isNotColonToken,\n    isNotCommaToken,\n    isNotCommentToken,\n    isNotOpeningBraceToken,\n    isNotOpeningBracketToken,\n    isNotOpeningParenToken,\n    isNotSemicolonToken,\n    isOpeningBraceToken,\n    isOpeningBracketToken,\n    isOpeningParenToken,\n    isParenthesized,\n    isSemicolonToken,\n    PatternMatcher,\n    READ,\n    ReferenceTracker,\n}\nexport {\n    CALL,\n    CONSTRUCT,\n    ESM,\n    findVariable,\n    getFunctionHeadLocation,\n    getFunctionNameWithKind,\n    getInnermostScope,\n    getPropertyName,\n    getStaticValue,\n    getStringIfConstant,\n    hasSideEffect,\n    isArrowToken,\n    isClosingBraceToken,\n    isClosingBracketToken,\n    isClosingParenToken,\n    isColonToken,\n    isCommaToken,\n    isCommentToken,\n    isNotArrowToken,\n    isNotClosingBraceToken,\n    isNotClosingBracketToken,\n    isNotClosingParenToken,\n    isNotColonToken,\n    isNotCommaToken,\n    isNotCommentToken,\n    isNotOpeningBraceToken,\n    isNotOpeningBracketToken,\n    isNotOpeningParenToken,\n    isNotSemicolonToken,\n    isOpeningBraceToken,\n    isOpeningBracketToken,\n    isOpeningParenToken,\n    isParenthesized,\n    isSemicolonToken,\n    PatternMatcher,\n    READ,\n    ReferenceTracker,\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;AAMA,AAAO,SAAS,iBAAiB,CAAC,YAAY,EAAE,IAAI,EAAE;IAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAC;;IAE9B,IAAI,KAAK,GAAG,aAAY;IACxB,IAAI,KAAK,GAAG,MAAK;IACjB,GAAG;QACC,KAAK,GAAG,MAAK;QACb,KAAK,MAAM,UAAU,IAAI,KAAK,CAAC,WAAW,EAAE;YACxC,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,MAAK;;YAEpC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE;gBAC7C,KAAK,GAAG,WAAU;gBAClB,KAAK,GAAG,KAAI;gBACZ,KAAK;aACR;SACJ;KACJ,QAAQ,KAAK,CAAC;;IAEf,OAAO,KAAK;CACf;;ACvBD;;;;;;AAMA,AAAO,SAAS,YAAY,CAAC,YAAY,EAAE,UAAU,EAAE;IACnD,IAAI,IAAI,GAAG,GAAE;IACb,IAAI,KAAK,GAAG,aAAY;;IAExB,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;QAChC,IAAI,GAAG,WAAU;KACpB,MAAM;QACH,IAAI,GAAG,UAAU,CAAC,KAAI;QACtB,KAAK,GAAG,iBAAiB,CAAC,KAAK,EAAE,UAAU,EAAC;KAC/C;;IAED,OAAO,KAAK,IAAI,IAAI,EAAE;QAClB,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAC;QACpC,IAAI,QAAQ,IAAI,IAAI,EAAE;YAClB,OAAO,QAAQ;SAClB;QACD,KAAK,GAAG,KAAK,CAAC,MAAK;KACtB;;IAED,OAAO,IAAI;CACd;;AC5BD;;;;;AAKA,SAAS,OAAO,CAAC,KAAK,EAAE;IACpB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;CACtB;;;;;;;AAOD,SAAS,MAAM,CAAC,CAAC,EAAE;IACf,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;CACzB;;;;;;;AAOD,AAAO,SAAS,YAAY,CAAC,KAAK,EAAE;IAChC,OAAO,KAAK,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;CAC7D;;;;;;;AAOD,AAAO,SAAS,YAAY,CAAC,KAAK,EAAE;IAChC,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;CAC5D;;;;;;;AAOD,AAAO,SAAS,gBAAgB,CAAC,KAAK,EAAE;IACpC,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;CAC5D;;;;;;;AAOD,AAAO,SAAS,YAAY,CAAC,KAAK,EAAE;IAChC,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;CAC5D;;;;;;;AAOD,AAAO,SAAS,mBAAmB,CAAC,KAAK,EAAE;IACvC,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;CAC5D;;;;;;;AAOD,AAAO,SAAS,mBAAmB,CAAC,KAAK,EAAE;IACvC,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;CAC5D;;;;;;;AAOD,AAAO,SAAS,qBAAqB,CAAC,KAAK,EAAE;IACzC,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;CAC5D;;;;;;;AAOD,AAAO,SAAS,qBAAqB,CAAC,KAAK,EAAE;IACzC,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;CAC5D;;;;;;;AAOD,AAAO,SAAS,mBAAmB,CAAC,KAAK,EAAE;IACvC,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;CAC5D;;;;;;;AAOD,AAAO,SAAS,mBAAmB,CAAC,KAAK,EAAE;IACvC,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;CAC5D;;;;;;;AAOD,AAAO,SAAS,cAAc,CAAC,KAAK,EAAE;IAClC;QACI,KAAK,CAAC,IAAI,KAAK,MAAM;QACrB,KAAK,CAAC,IAAI,KAAK,OAAO;QACtB,KAAK,CAAC,IAAI,KAAK,SAAS;KAC3B;CACJ;;AAED,AAAY,MAAC,eAAe,GAAG,MAAM,CAAC,YAAY,EAAC;AACnD,AAAY,MAAC,eAAe,GAAG,MAAM,CAAC,YAAY,EAAC;AACnD,AAAY,MAAC,mBAAmB,GAAG,MAAM,CAAC,gBAAgB,EAAC;AAC3D,AAAY,MAAC,eAAe,GAAG,MAAM,CAAC,YAAY,EAAC;AACnD,AAAY,MAAC,sBAAsB,GAAG,MAAM,CAAC,mBAAmB,EAAC;AACjE,AAAY,MAAC,sBAAsB,GAAG,MAAM,CAAC,mBAAmB,EAAC;AACjE,AAAY,MAAC,wBAAwB,GAAG,MAAM,CAAC,qBAAqB,EAAC;AACrE,AAAY,MAAC,wBAAwB,GAAG,MAAM,CAAC,qBAAqB,EAAC;AACrE,AAAY,MAAC,sBAAsB,GAAG,MAAM,CAAC,mBAAmB,EAAC;AACjE,AAAY,MAAC,sBAAsB,GAAG,MAAM,CAAC,mBAAmB,EAAC;AACjE,AAAY,MAAC,iBAAiB,GAAG,MAAM,CAAC,cAAc,CAAC;;ACjIvD;;;;;;AAMA,SAAS,uBAAuB,CAAC,IAAI,EAAE,UAAU,EAAE;IAC/C,OAAO,IAAI,CAAC,EAAE;UACR,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,mBAAmB,CAAC;UACtD,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,mBAAmB,CAAC;CAC5D;;;;;;;;AAQD,AAAO,SAAS,uBAAuB,CAAC,IAAI,EAAE,UAAU,EAAE;IACtD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAM;IAC1B,IAAI,KAAK,GAAG,KAAI;IAChB,IAAI,GAAG,GAAG,KAAI;;IAEd,IAAI,IAAI,CAAC,IAAI,KAAK,yBAAyB,EAAE;QACzC,MAAM,UAAU,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,EAAC;;QAErE,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,MAAK;QAC5B,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,IAAG;KAC3B,MAAM;QACH,MAAM,CAAC,IAAI,KAAK,UAAU;QAC1B,MAAM,CAAC,IAAI,KAAK,kBAAkB;MACpC;QACE,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,MAAK;QACxB,GAAG,GAAG,uBAAuB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,MAAK;KAC5D,MAAM;QACH,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAK;QACtB,GAAG,GAAG,uBAAuB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,MAAK;KAC5D;;IAED,OAAO;QACH,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC;QAC/B,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC;KAC9B;CACJ;;AC7CD;AACA,AAEA;AACA,MAAM,YAAY;IACd,OAAO,UAAU,KAAK,WAAW;UAC3B,UAAU;UACV,OAAO,IAAI,KAAK,WAAW;UAC3B,IAAI;UACJ,OAAO,MAAM,KAAK,WAAW;UAC7B,MAAM;UACN,OAAO,MAAM,KAAK,WAAW;UAC7B,MAAM;UACN,GAAE;;AAEZ,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM;IAC9B,IAAI,GAAG,CAAC;QACJ,OAAO;QACP,aAAa;QACb,QAAQ;QACR,eAAe;QACf,gBAAgB;QAChB,SAAS;QACT,UAAU;QACV,MAAM;QACN,WAAW;QACX,oBAAoB;QACpB,WAAW;QACX,oBAAoB;QACpB,QAAQ;QACR,cAAc;QACd,cAAc;QACd,UAAU;QACV,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,WAAW;QACX,UAAU;QACV,OAAO;QACP,eAAe;QACf,MAAM;QACN,KAAK;QACL,MAAM;QACN,KAAK;QACL,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,SAAS;QACT,OAAO;QACP,SAAS;QACT,QAAQ;QACR,KAAK;QACL,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,aAAa;QACb,YAAY;QACZ,mBAAmB;QACnB,WAAW;QACX,UAAU;QACV,SAAS;QACT,SAAS;KACZ,CAAC;EACL;AACD,MAAM,WAAW,GAAG,IAAI,GAAG;IACvB;QACI,KAAK,CAAC,OAAO;QACb,OAAO,MAAM,KAAK,UAAU,GAAG,MAAM,GAAG,SAAS;QACjD,OAAO;QACP,IAAI;QACJ,IAAI,CAAC,KAAK;QACV,SAAS;QACT,kBAAkB;QAClB,SAAS;QACT,kBAAkB;QAClB,MAAM;QACN,QAAQ;QACR,KAAK;QACL,aAAa;QACb,GAAG,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC;aAC9B,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;aACjB,MAAM,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,UAAU,CAAC;QACzC,MAAM;QACN,MAAM,CAAC,QAAQ;QACf,MAAM,CAAC,KAAK;QACZ,MAAM,CAAC,UAAU;QACjB,MAAM,CAAC,QAAQ;QACf,MAAM;QACN,MAAM,CAAC,OAAO;QACd,MAAM,CAAC,EAAE;QACT,MAAM,CAAC,YAAY;QACnB,MAAM,CAAC,QAAQ;QACf,MAAM,CAAC,QAAQ;QACf,MAAM,CAAC,IAAI;QACX,MAAM,CAAC,MAAM;QACb,UAAU;QACV,QAAQ;QACR,MAAM;QACN,MAAM;QACN,MAAM,CAAC,YAAY;QACnB,MAAM,CAAC,aAAa;QACpB,MAAM,CAAC,GAAG;QACV,MAAM;QACN,MAAM,CAAC,GAAG;QACV,MAAM,CAAC,MAAM;QACb,QAAQ;KACX,CAAC,MAAM,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,UAAU,CAAC;EACzC;AACD,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC;IAC5B,MAAM,CAAC,MAAM;IACb,MAAM,CAAC,iBAAiB;IACxB,MAAM,CAAC,IAAI;CACd,EAAC;;;;;;;AAOF,SAAS,qBAAqB,CAAC,MAAM,EAAE,IAAI,EAAE;IACzC,IAAI,CAAC,GAAG,OAAM;IACd,OAAO,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,UAAU,KAAK,CAAC,KAAK,IAAI,EAAE;QACrE,MAAM,CAAC,GAAG,MAAM,CAAC,wBAAwB,CAAC,CAAC,EAAE,IAAI,EAAC;QAClD,IAAI,CAAC,EAAE;YACH,OAAO,CAAC;SACX;QACD,CAAC,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC,EAAC;KAC/B;IACD,OAAO,IAAI;CACd;;;;;;;AAOD,SAAS,QAAQ,CAAC,MAAM,EAAE,IAAI,EAAE;IAC5B,MAAM,CAAC,GAAG,qBAAqB,CAAC,MAAM,EAAE,IAAI,EAAC;IAC7C,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI;CACpC;;;;;;;;AAQD,SAAS,gBAAgB,CAAC,QAAQ,EAAE,YAAY,EAAE;IAC9C,MAAM,SAAS,GAAG,GAAE;;IAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACtC,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,EAAC;;QAE/B,IAAI,WAAW,IAAI,IAAI,EAAE;YACrB,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,EAAC;SAC3B,MAAM,IAAI,WAAW,CAAC,IAAI,KAAK,eAAe,EAAE;YAC7C,MAAM,QAAQ,GAAG,eAAe,CAAC,WAAW,CAAC,QAAQ,EAAE,YAAY,EAAC;YACpE,IAAI,QAAQ,IAAI,IAAI,EAAE;gBAClB,OAAO,IAAI;aACd;YACD,SAAS,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,KAAK,EAAC;SACpC,MAAM;YACH,MAAM,OAAO,GAAG,eAAe,CAAC,WAAW,EAAE,YAAY,EAAC;YAC1D,IAAI,OAAO,IAAI,IAAI,EAAE;gBACjB,OAAO,IAAI;aACd;YACD,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAC;SAChC;KACJ;;IAED,OAAO,SAAS;CACnB;;AAED,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;IAC7B,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE;QAChC,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,EAAC;QAC9D,OAAO,QAAQ,IAAI,IAAI,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI;KACvD;;IAED,oBAAoB,CAAC,IAAI,EAAE,YAAY,EAAE;QACrC,IAAI,IAAI,CAAC,QAAQ,KAAK,GAAG,EAAE;YACvB,OAAO,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;SACnD;QACD,OAAO,IAAI;KACd;;;IAGD,gBAAgB,CAAC,IAAI,EAAE,YAAY,EAAE;QACjC,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,YAAY,EAAE;;YAE1D,OAAO,IAAI;SACd;;QAED,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,EAAC;QACrD,MAAM,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,EAAC;QACvD,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;YAC/B,QAAQ,IAAI,CAAC,QAAQ;gBACjB,KAAK,IAAI;oBACL,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;gBAC/C,KAAK,IAAI;oBACL,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;gBAC/C,KAAK,KAAK;oBACN,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE;gBAChD,KAAK,KAAK;oBACN,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE;gBAChD,KAAK,GAAG;oBACJ,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;gBAC9C,KAAK,IAAI;oBACL,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;gBAC/C,KAAK,GAAG;oBACJ,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;gBAC9C,KAAK,IAAI;oBACL,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;gBAC/C,KAAK,IAAI;oBACL,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;gBAC/C,KAAK,IAAI;oBACL,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;gBAC/C,KAAK,KAAK;oBACN,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE;gBAChD,KAAK,GAAG;oBACJ,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;gBAC9C,KAAK,GAAG;oBACJ,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;gBAC9C,KAAK,GAAG;oBACJ,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;gBAC9C,KAAK,GAAG;oBACJ,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;gBAC9C,KAAK,GAAG;oBACJ,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;gBAC9C,KAAK,IAAI;oBACL,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE;gBACvD,KAAK,GAAG;oBACJ,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;gBAC9C,KAAK,GAAG;oBACJ,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;gBAC9C,KAAK,GAAG;oBACJ,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;;;aAGjD;SACJ;;QAED,OAAO,IAAI;KACd;;IAED,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE;QAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,OAAM;QAC9B,MAAM,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,EAAC;;QAE3D,IAAI,IAAI,IAAI,IAAI,EAAE;YACd,IAAI,UAAU,CAAC,IAAI,KAAK,kBAAkB,EAAE;gBACxC,MAAM,MAAM,GAAG,eAAe,CAAC,UAAU,CAAC,MAAM,EAAE,YAAY,EAAC;gBAC/D,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;sBAC9B,eAAe,CAAC,UAAU,CAAC,QAAQ,EAAE,YAAY,CAAC;sBAClD,EAAE,KAAK,EAAE,UAAU,CAAC,QAAQ,CAAC,IAAI,GAAE;;gBAEzC,IAAI,MAAM,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,EAAE;oBACpC,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAK;oBAC7B,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAK;oBACjC,IAAI,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE;wBACvC,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE;qBAClD;oBACD,IAAI,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE;wBAC3C,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE;qBAC5B;iBACJ;aACJ,MAAM;gBACH,MAAM,MAAM,GAAG,eAAe,CAAC,UAAU,EAAE,YAAY,EAAC;gBACxD,IAAI,MAAM,IAAI,IAAI,EAAE;oBAChB,MAAM,IAAI,GAAG,MAAM,CAAC,MAAK;oBACzB,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;wBACvB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE;qBAClC;oBACD,IAAI,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;wBAC3B,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE;qBAC5B;iBACJ;aACJ;SACJ;;QAED,OAAO,IAAI;KACd;;IAED,qBAAqB,CAAC,IAAI,EAAE,YAAY,EAAE;QACtC,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,EAAC;QACrD,IAAI,IAAI,IAAI,IAAI,EAAE;YACd,OAAO,IAAI,CAAC,KAAK;kBACX,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC;kBAC9C,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;SACtD;QACD,OAAO,IAAI;KACd;;IAED,mBAAmB,CAAC,IAAI,EAAE,YAAY,EAAE;QACpC,OAAO,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC;KACxD;;IAED,UAAU,CAAC,IAAI,EAAE,YAAY,EAAE;QAC3B,IAAI,YAAY,IAAI,IAAI,EAAE;YACtB,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,EAAE,IAAI,EAAC;;;YAGjD;gBACI,QAAQ,IAAI,IAAI;gBAChB,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;gBAC1B,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAC/B,QAAQ,CAAC,IAAI,IAAI,YAAY;cAC/B;gBACE,OAAO,EAAE,KAAK,EAAE,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;aAChD;;;YAGD,IAAI,QAAQ,IAAI,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBAChD,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAC;gBAC5B;oBACI,GAAG,CAAC,MAAM;oBACV,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,OAAO;;oBAE3B,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,YAAY;kBACnC;oBACE,OAAO,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC;iBACtD;aACJ;SACJ;QACD,OAAO,IAAI;KACd;;IAED,OAAO,CAAC,IAAI,EAAE;;QAEV,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;;YAEnE,OAAO,IAAI;SACd;QACD,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;KAC/B;;IAED,iBAAiB,CAAC,IAAI,EAAE,YAAY,EAAE;QAClC,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,EAAC;QACrD,IAAI,IAAI,IAAI,IAAI,EAAE;YACd;gBACI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI;iBACtD,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;cAC3D;gBACE,OAAO,IAAI;aACd;;YAED,MAAM,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,EAAC;YACvD,IAAI,KAAK,IAAI,IAAI,EAAE;gBACf,OAAO,KAAK;aACf;SACJ;;QAED,OAAO,IAAI;KACd;;IAED,gBAAgB,CAAC,IAAI,EAAE,YAAY,EAAE;QACjC,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,EAAC;QACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;cACxB,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC;cAC5C,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAE;;QAEnC;YACI,MAAM,IAAI,IAAI;YACd,QAAQ,IAAI,IAAI;YAChB,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC;UACzC;YACE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;SACjD;QACD,OAAO,IAAI;KACd;;IAED,aAAa,CAAC,IAAI,EAAE,YAAY,EAAE;QAC9B,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,EAAC;QACzD,MAAM,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,EAAC;;QAE3D,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;YAChC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAK;YACzB,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACvB,OAAO,EAAE,KAAK,EAAE,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE;aACtC;SACJ;;QAED,OAAO,IAAI;KACd;;IAED,gBAAgB,CAAC,IAAI,EAAE,YAAY,EAAE;QACjC,MAAM,MAAM,GAAG,GAAE;;QAEjB,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,UAAU,EAAE;YACxC,IAAI,YAAY,CAAC,IAAI,KAAK,UAAU,EAAE;gBAClC,IAAI,YAAY,CAAC,IAAI,KAAK,MAAM,EAAE;oBAC9B,OAAO,IAAI;iBACd;gBACD,MAAM,GAAG,GAAG,YAAY,CAAC,QAAQ;sBAC3B,eAAe,CAAC,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC;sBAC/C,EAAE,KAAK,EAAE,YAAY,CAAC,GAAG,CAAC,IAAI,GAAE;gBACtC,MAAM,KAAK,GAAG,eAAe,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,EAAC;gBAC/D,IAAI,GAAG,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;oBAC9B,OAAO,IAAI;iBACd;gBACD,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAK;aAClC,MAAM;gBACH,YAAY,CAAC,IAAI,KAAK,eAAe;gBACrC,YAAY,CAAC,IAAI,KAAK,4BAA4B;cACpD;gBACE,MAAM,QAAQ,GAAG,eAAe;oBAC5B,YAAY,CAAC,QAAQ;oBACrB,YAAY;kBACf;gBACD,IAAI,QAAQ,IAAI,IAAI,EAAE;oBAClB,OAAO,IAAI;iBACd;gBACD,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,EAAC;aACxC,MAAM;gBACH,OAAO,IAAI;aACd;SACJ;;QAED,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE;KAC3B;;IAED,kBAAkB,CAAC,IAAI,EAAE,YAAY,EAAE;QACnC,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAC;QAC1D,OAAO,eAAe,CAAC,IAAI,EAAE,YAAY,CAAC;KAC7C;;IAED,wBAAwB,CAAC,IAAI,EAAE,YAAY,EAAE;QACzC,MAAM,GAAG,GAAG,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,YAAY,EAAC;QACnD,MAAM,WAAW,GAAG,gBAAgB;YAChC,IAAI,CAAC,KAAK,CAAC,WAAW;YACtB,YAAY;UACf;;QAED,IAAI,GAAG,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI,EAAE;YACpC,MAAM,IAAI,GAAG,GAAG,CAAC,MAAK;YACtB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,EAAC;YAC1D,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,EAAC;;YAErD,IAAI,IAAI,KAAK,MAAM,CAAC,GAAG,EAAE;gBACrB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,WAAW,CAAC,EAAE;aAClD;SACJ;;QAED,OAAO,IAAI;KACd;;IAED,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE;QAChC,MAAM,WAAW,GAAG,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,EAAC;QACpE,IAAI,WAAW,IAAI,IAAI,EAAE;YACrB,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAM;YACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACzC,KAAK,IAAI,WAAW,CAAC,CAAC,EAAC;gBACvB,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,OAAM;aAC3C;YACD,OAAO,EAAE,KAAK,EAAE;SACnB;QACD,OAAO,IAAI;KACd;;IAED,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE;QAChC,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;;YAE5B,OAAO,IAAI;SACd;QACD,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,EAAE;YAC1B,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;SAC9B;;QAED,MAAM,GAAG,GAAG,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,EAAC;QACxD,IAAI,GAAG,IAAI,IAAI,EAAE;YACb,QAAQ,IAAI,CAAC,QAAQ;gBACjB,KAAK,GAAG;oBACJ,OAAO,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE;gBAChC,KAAK,GAAG;oBACJ,OAAO,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE;gBAChC,KAAK,GAAG;oBACJ,OAAO,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE;gBAChC,KAAK,GAAG;oBACJ,OAAO,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE;gBAChC,KAAK,QAAQ;oBACT,OAAO,EAAE,KAAK,EAAE,OAAO,GAAG,CAAC,KAAK,EAAE;;;aAGzC;SACJ;;QAED,OAAO,IAAI;KACd;CACJ,EAAC;;;;;;;;AAQF,SAAS,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE;IACzC,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;QACnE,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,YAAY,CAAC;KACnD;IACD,OAAO,IAAI;CACd;;;;;;;;AAQD,AAAO,SAAS,cAAc,CAAC,IAAI,EAAE,YAAY,GAAG,IAAI,EAAE;IACtD,IAAI;QACA,OAAO,eAAe,CAAC,IAAI,EAAE,YAAY,CAAC;KAC7C,CAAC,OAAO,MAAM,EAAE;QACb,OAAO,IAAI;KACd;CACJ;;AClgBD;;;;;;AAMA,AAAO,SAAS,mBAAmB,CAAC,IAAI,EAAE,YAAY,GAAG,IAAI,EAAE;;IAE3D,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;QACxD,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACtD;QACD,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,OAAO,IAAI,CAAC,MAAM;SACrB;KACJ;;IAED,MAAM,SAAS,GAAG,cAAc,CAAC,IAAI,EAAE,YAAY,EAAC;IACpD,OAAO,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;CAC9C;;ACnBD;;;;;;AAMA,AAAO,SAAS,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE;IAChD,QAAQ,IAAI,CAAC,IAAI;QACb,KAAK,kBAAkB;YACnB,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,OAAO,mBAAmB,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC;aAC1D;YACD,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI;;QAE7B,KAAK,UAAU,CAAC;QAChB,KAAK,kBAAkB;YACnB,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,OAAO,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE,YAAY,CAAC;aACrD;YACD,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS,EAAE;gBAC7B,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;aAChC;YACD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI;;;KAG3B;;IAED,OAAO,IAAI;CACd;;AC5BD;;;;;AAKA,AAAO,SAAS,uBAAuB,CAAC,IAAI,EAAE;IAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAM;IAC1B,MAAM,MAAM,GAAG,GAAE;;IAEjB,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,IAAI,MAAM,CAAC,MAAM,EAAE;QACrD,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAC;KACxB;IACD,IAAI,IAAI,CAAC,KAAK,EAAE;QACZ,MAAM,CAAC,IAAI,CAAC,OAAO,EAAC;KACvB;IACD,IAAI,IAAI,CAAC,SAAS,EAAE;QAChB,MAAM,CAAC,IAAI,CAAC,WAAW,EAAC;KAC3B;;IAED,IAAI,IAAI,CAAC,IAAI,KAAK,yBAAyB,EAAE;QACzC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,EAAC;KACnC,MAAM;QACH,MAAM,CAAC,IAAI,KAAK,UAAU;QAC1B,MAAM,CAAC,IAAI,KAAK,kBAAkB;MACpC;QACE,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE;YAC/B,OAAO,aAAa;SACvB;QACD,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE;YACvB,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAC;SACxB,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE;YAC9B,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAC;SACxB,MAAM;YACH,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAC;SACxB;KACJ,MAAM;QACH,MAAM,CAAC,IAAI,CAAC,UAAU,EAAC;KAC1B;;IAED,IAAI,IAAI,CAAC,EAAE,EAAE;QACT,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC;KACnC,MAAM;QACH,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,EAAC;;QAEpC,IAAI,IAAI,EAAE;YACN,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAC;SAC3B;KACJ;;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;CAC1B;;AClDD,MAAM,uBAAuB,GAAG,MAAM,CAAC,MAAM;IACzC,IAAI,GAAG,CAAC;QACJ,IAAI;QACJ,IAAI;QACJ,GAAG;QACH,IAAI;QACJ,GAAG;QACH,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,KAAK;QACL,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,IAAI;KACP,CAAC;EACL;AACD,MAAM,sBAAsB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAC;AAC3E,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM;IACzB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;QAC/B,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;YAC/B,MAAM,EAAE,IAAI,EAAE,GAAG,KAAI;;YAErB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,UAAU,EAAE;gBAClC,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;aAChD;;YAED,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;SACzD;;QAED,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;YACvC,MAAM,EAAE,IAAI,EAAE,GAAG,KAAI;;YAErB,KAAK,MAAM,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACtD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAC;;gBAEvB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBACtB,KAAK,MAAM,OAAO,IAAI,KAAK,EAAE;wBACzB;4BACI,OAAO;4BACP,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC;0BAC5C;4BACE,OAAO,IAAI;yBACd;qBACJ;iBACJ,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC,EAAE;oBAC1D,OAAO,IAAI;iBACd;aACJ;;YAED,OAAO,KAAK;SACf;;QAED,uBAAuB,GAAG;YACtB,OAAO,KAAK;SACf;QACD,oBAAoB,GAAG;YACnB,OAAO,IAAI;SACd;QACD,eAAe,GAAG;YACd,OAAO,IAAI;SACd;QACD,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;YACzC;gBACI,OAAO,CAAC,8BAA8B;gBACtC,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;iBACzC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC;cACjE;gBACE,OAAO,IAAI;aACd;YACD,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;SACzD;QACD,cAAc,GAAG;YACb,OAAO,IAAI;SACd;QACD,kBAAkB,GAAG;YACjB,OAAO,KAAK;SACf;QACD,gBAAgB,GAAG;YACf,OAAO,IAAI;SACd;QACD,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;YACzC,IAAI,OAAO,CAAC,eAAe,EAAE;gBACzB,OAAO,IAAI;aACd;YACD;gBACI,OAAO,CAAC,8BAA8B;gBACtC,IAAI,CAAC,QAAQ;gBACb,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS;cAClC;gBACE,OAAO,IAAI;aACd;YACD,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;SACzD;QACD,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;YACzC;gBACI,OAAO,CAAC,8BAA8B;gBACtC,IAAI,CAAC,QAAQ;gBACb,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS;cAC7B;gBACE,OAAO,IAAI;aACd;YACD,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;SACzD;QACD,aAAa,GAAG;YACZ,OAAO,IAAI;SACd;QACD,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;YACjC;gBACI,OAAO,CAAC,8BAA8B;gBACtC,IAAI,CAAC,QAAQ;gBACb,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS;cAC7B;gBACE,OAAO,IAAI;aACd;YACD,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;SACzD;QACD,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;YACxC,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;gBAC5B,OAAO,IAAI;aACd;YACD;gBACI,OAAO,CAAC,8BAA8B;gBACtC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACzC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS;cAClC;gBACE,OAAO,IAAI;aACd;YACD,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;SACzD;QACD,gBAAgB,GAAG;YACf,OAAO,IAAI;SACd;QACD,eAAe,GAAG;YACd,OAAO,IAAI;SACd;KACJ,CAAC;EACL;;;;;;;;;;;;AAYD,AAAO,SAAS,aAAa;IACzB,IAAI;IACJ,UAAU;IACV,EAAE,eAAe,GAAG,KAAK,EAAE,8BAA8B,GAAG,KAAK,EAAE,GAAG,EAAE;EAC1E;IACE,OAAO,OAAO,CAAC,MAAM;QACjB,IAAI;QACJ,EAAE,eAAe,EAAE,8BAA8B,EAAE;QACnD,UAAU,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI;KACrC;CACJ;;ACpKD;;;;;;;AAOA,SAAS,oBAAoB,CAAC,IAAI,EAAE,UAAU,EAAE;IAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAM;;IAE1B,QAAQ,MAAM,CAAC,IAAI;QACf,KAAK,gBAAgB,CAAC;QACtB,KAAK,eAAe;YAChB,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;gBAC/D,OAAO,UAAU,CAAC,aAAa;oBAC3B,MAAM,CAAC,MAAM;oBACb,mBAAmB;iBACtB;aACJ;YACD,OAAO,IAAI;;QAEf,KAAK,kBAAkB;YACnB,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE;gBACtB,OAAO,UAAU,CAAC,aAAa;oBAC3B,MAAM,CAAC,IAAI;oBACX,mBAAmB;iBACtB;aACJ;YACD,OAAO,IAAI;;QAEf,KAAK,aAAa,CAAC;QACnB,KAAK,gBAAgB;YACjB,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE;gBACtB,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;aAC7C;YACD,OAAO,IAAI;;QAEf,KAAK,kBAAkB;YACnB,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;gBACxB,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;aAC7C;YACD,OAAO,IAAI;;QAEf,KAAK,iBAAiB;YAClB,IAAI,MAAM,CAAC,YAAY,KAAK,IAAI,EAAE;gBAC9B,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;aAC7C;YACD,OAAO,IAAI;;QAEf,KAAK,eAAe;YAChB,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;gBACxB,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;aAC7C;YACD,OAAO,IAAI;;QAEf;YACI,OAAO,IAAI;KAClB;CACJ;;;;;;;;;;;;;;;AAeD,AAAO,SAAS,eAAe;IAC3B,WAAW;IACX,gBAAgB;IAChB,kBAAkB;EACpB;IACE,IAAI,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,gBAAe;IAC5D,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;QACjC,KAAK,GAAG,WAAW,GAAG,EAAC;QACvB,IAAI,GAAG,iBAAgB;QACvB,UAAU,GAAG,mBAAkB;QAC/B,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE;YACf,MAAM,IAAI,SAAS,CAAC,uCAAuC,CAAC;SAC/D;KACJ,MAAM;QACH,KAAK,GAAG,EAAC;QACT,IAAI,GAAG,YAAW;QAClB,UAAU,GAAG,iBAAgB;KAChC;;IAED,IAAI,IAAI,IAAI,IAAI,EAAE;QACd,OAAO,KAAK;KACf;;IAED,cAAc,GAAG,eAAe,GAAG,KAAI;IACvC,GAAG;QACC,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC,cAAc,EAAC;QAC1D,eAAe,GAAG,UAAU,CAAC,aAAa,CAAC,eAAe,EAAC;KAC9D;QACG,cAAc,IAAI,IAAI;QACtB,eAAe,IAAI,IAAI;QACvB,mBAAmB,CAAC,cAAc,CAAC;QACnC,mBAAmB,CAAC,eAAe,CAAC;;QAEpC,cAAc,KAAK,oBAAoB,CAAC,IAAI,EAAE,UAAU,CAAC;QACzD,EAAE,KAAK,GAAG,CAAC;KACd;;IAED,OAAO,KAAK,KAAK,CAAC;CACrB;;ACjHD;;;;;AAKA,MAAM,WAAW,GAAG,6BAA4B;;;AAGhD,MAAM,QAAQ,GAAG,IAAI,OAAO,GAAE;;;;;;;;AAQ9B,SAAS,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE;IAC3B,IAAI,OAAO,GAAG,MAAK;IACnB,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,EAAE,CAAC,EAAE;QAC/D,OAAO,GAAG,CAAC,QAAO;KACrB;IACD,OAAO,OAAO;CACjB;;;;;;;;;AASD,SAAS,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,WAAW,EAAE;IACzC,MAAM,MAAM,GAAG,GAAE;IACjB,IAAI,KAAK,GAAG,EAAC;;;IAGb,IAAI,KAAK,GAAG,KAAI;;;;;;IAMhB,SAAS,QAAQ,CAAC,GAAG,EAAE;QACnB,QAAQ,GAAG;YACP,KAAK,IAAI;gBACL,OAAO,GAAG;YACd,KAAK,IAAI;gBACL,OAAO,KAAK,CAAC,CAAC,CAAC;YACnB,KAAK,IAAI;gBACL,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC;YACpC,KAAK,IAAI;gBACL,OAAO,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YACnD,SAAS;gBACL,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAC;gBACtB,IAAI,CAAC,IAAI,KAAK,EAAE;oBACZ,OAAO,KAAK,CAAC,CAAC,CAAC;iBAClB;gBACD,OAAO,GAAG;aACb;SACJ;KACJ;;IAED,KAAK,KAAK,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAChC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,EAAC;QAC1C,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAC;QACvD,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAM;KACxC;IACD,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,EAAC;;IAE7B,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;CACzB;;;;;;;;;AASD,SAAS,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE;IACrC,MAAM,MAAM,GAAG,GAAE;IACjB,IAAI,KAAK,GAAG,EAAC;;IAEb,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACtC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,EAAC;QAC1C,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAC;QAChE,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAM;KACxC;IACD,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,EAAC;;IAE7B,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;CACzB;;;;;AAKD,AAAO,MAAM,cAAc,CAAC;;;;;;IAMxB,WAAW,CAAC,OAAO,EAAE,EAAE,OAAO,GAAG,KAAK,EAAE,GAAG,EAAE,EAAE;QAC3C,IAAI,EAAE,OAAO,YAAY,MAAM,CAAC,EAAE;YAC9B,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC;SAChE;QACD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YAC9B,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC;SACzD;;QAED,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE;YACf,OAAO,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC;YAClD,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC;SAC5B,EAAC;KACL;;;;;;;IAOD,CAAC,OAAO,CAAC,GAAG,EAAE;QACV,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAC;QAC/C,IAAI,KAAK,GAAG,KAAI;QAChB,IAAI,SAAS,GAAG,EAAC;;QAEjB,OAAO,CAAC,SAAS,GAAG,EAAC;QACrB,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;YACxC,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE;gBACzC,SAAS,GAAG,OAAO,CAAC,UAAS;gBAC7B,MAAM,MAAK;gBACX,OAAO,CAAC,SAAS,GAAG,UAAS;aAChC;SACJ;KACJ;;;;;;;IAOD,IAAI,CAAC,GAAG,EAAE;QACN,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAC;QAC5B,MAAM,GAAG,GAAG,EAAE,CAAC,IAAI,GAAE;QACrB,OAAO,CAAC,GAAG,CAAC,IAAI;KACnB;;;;;;;;IAQD,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE;QAC5B,OAAO,OAAO,QAAQ,KAAK,UAAU;cAC/B,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC;cACrC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;KACtD;CACJ;;AC1JD,MAAM,WAAW,GAAG,uDAAsD;AAC1E,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAC;;AAErD,AAAY,MAAC,IAAI,GAAG,MAAM,CAAC,MAAM,EAAC;AAClC,AAAY,MAAC,IAAI,GAAG,MAAM,CAAC,MAAM,EAAC;AAClC,AAAY,MAAC,SAAS,GAAG,MAAM,CAAC,WAAW,EAAC;AAC5C,AAAY,MAAC,GAAG,GAAG,MAAM,CAAC,KAAK,EAAC;;AAEhC,MAAM,WAAW,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,IAAI,GAAG,IAAI,EAAE,GAAE;;;;;;;AAOjD,SAAS,gBAAgB,CAAC,QAAQ,EAAE;IAChC;QACI,QAAQ,IAAI,IAAI;QAChB,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;QAC1B,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;KAC7C;CACJ;;;;;;;;AAQD,SAAS,aAAa,CAAC,IAAI,EAAE;IACzB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAM;;IAE1B,QAAQ,MAAM,IAAI,MAAM,CAAC,IAAI;QACzB,KAAK,uBAAuB;YACxB,OAAO,MAAM,CAAC,UAAU,KAAK,IAAI,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI;QAClE,KAAK,mBAAmB;YACpB,OAAO,IAAI;QACf,KAAK,oBAAoB;YACrB,OAAO,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI;;QAErE;YACI,OAAO,KAAK;KACnB;CACJ;;;;;AAKD,AAAO,MAAM,gBAAgB,CAAC;;;;;;;;IAQ1B,WAAW;QACP,WAAW;QACX;YACI,IAAI,GAAG,QAAQ;YACf,iBAAiB,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;SACnD,GAAG,EAAE;MACR;QACE,IAAI,CAAC,aAAa,GAAG,GAAE;QACvB,IAAI,CAAC,WAAW,GAAG,YAAW;QAC9B,IAAI,CAAC,IAAI,GAAG,KAAI;QAChB,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAC;KACtD;;;;;;;IAOD,CAAC,uBAAuB,CAAC,QAAQ,EAAE;QAC/B,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACrC,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAC;YAClC,MAAM,IAAI,GAAG,CAAC,GAAG,EAAC;YAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAC;;YAE9C,IAAI,gBAAgB,CAAC,QAAQ,CAAC,EAAE;gBAC5B,QAAQ;aACX;;YAED,OAAO,IAAI,CAAC,0BAA0B;gBAClC,QAAQ;gBACR,IAAI;gBACJ,YAAY;gBACZ,IAAI;cACP;SACJ;;QAED,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACtC,MAAM,IAAI,GAAG,GAAE;YACf,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAC;;YAE9C,IAAI,gBAAgB,CAAC,QAAQ,CAAC,EAAE;gBAC5B,QAAQ;aACX;;YAED,OAAO,IAAI,CAAC,0BAA0B;gBAClC,QAAQ;gBACR,IAAI;gBACJ,QAAQ;gBACR,KAAK;cACR;SACJ;KACJ;;;;;;;IAOD,CAAC,oBAAoB,CAAC,QAAQ,EAAE;QAC5B,KAAK,MAAM,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YAC9D,MAAM,GAAG,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAC;YAClD,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;gBACpC,QAAQ;aACX;;YAED,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAC;YAClC,MAAM,IAAI,GAAG,CAAC,GAAG,EAAC;;YAElB,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;gBACpB,MAAM;oBACF,IAAI;oBACJ,IAAI;oBACJ,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC;kBAC3B;aACJ;YACD,OAAO,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAC;SACnE;KACJ;;;;;;;IAOD,CAAC,oBAAoB,CAAC,QAAQ,EAAE;QAC5B,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAK;;QAE1C,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE;YACjC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;gBACrD,QAAQ;aACX;YACD,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,MAAK;;YAElC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;gBAC1B,QAAQ;aACX;YACD,MAAM,YAAY,GAAG,QAAQ,CAAC,QAAQ,EAAC;YACvC,MAAM,IAAI,GAAG,CAAC,QAAQ,EAAC;;YAEvB,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;gBACpB,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC,GAAE;aAC7D;;YAED,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAsB,EAAE;gBACtC,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;oBACzC,MAAM,cAAc,GAAG,YAAY,CAAC,GAAG,EAAC;oBACxC,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE;wBACtB,MAAM;4BACF,IAAI;4BACJ,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;4BACtB,IAAI,EAAE,IAAI;4BACV,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC;0BAC7B;qBACJ;iBACJ;aACJ,MAAM;gBACH,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;oBACrC,MAAM,GAAG,GAAG,GAAG,CAAC,YAAY,EAAE,GAAG,EAAC;oBAClC,MAAM,EAAE,GAAG,IAAI,CAAC,wBAAwB;wBACpC,SAAS;wBACT,IAAI;wBACJ,GAAG;8BACG,YAAY;8BACZ,IAAI,CAAC,IAAI,KAAK,QAAQ;8BACtB,MAAM,CAAC,MAAM;kCACT,EAAE,OAAO,EAAE,YAAY,EAAE;kCACzB,YAAY;+BACf;8BACD,EAAE,OAAO,EAAE,YAAY,EAAE;sBAClC;;oBAED,IAAI,GAAG,EAAE;wBACL,OAAO,GAAE;qBACZ,MAAM;wBACH,KAAK,MAAM,MAAM,IAAI,EAAE,EAAE;4BACrB,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAC;4BAC/C;gCACI,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;gCACvB,MAAM,CAAC,IAAI,KAAK,IAAI;8BACtB;gCACE,MAAM,OAAM;6BACf;yBACJ;qBACJ;iBACJ;aACJ;SACJ;KACJ;;;;;;;;;;IAUD,CAAC,0BAA0B,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE;QAChE,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACvC,MAAM;SACT;QACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAC;QACjC,IAAI;YACA,KAAK,MAAM,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE;gBACzC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE;oBACrB,QAAQ;iBACX;gBACD,MAAM,IAAI,GAAG,SAAS,CAAC,WAAU;;gBAEjC,IAAI,YAAY,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;oBAChC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAE;iBACzD;gBACD,OAAO,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAC;aAC/D;SACJ,SAAS;YACN,IAAI,CAAC,aAAa,CAAC,GAAG,GAAE;SAC3B;KACJ;;;;;;;;;;IAUD,CAAC,0BAA0B,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE;QAClD,IAAI,IAAI,GAAG,SAAQ;QACnB,OAAO,aAAa,CAAC,IAAI,CAAC,EAAE;YACxB,IAAI,GAAG,IAAI,CAAC,OAAM;SACrB;;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAM;QAC1B,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE;YACpC,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;gBACxB,MAAM,GAAG,GAAG,eAAe,CAAC,MAAM,EAAC;gBACnC,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;oBACpC,MAAM;iBACT;;gBAED,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAC;gBACvB,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAC;gBAClC,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;oBACpB,MAAM;wBACF,IAAI,EAAE,MAAM;wBACZ,IAAI;wBACJ,IAAI,EAAE,IAAI;wBACV,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC;sBAC3B;iBACJ;gBACD,OAAO,IAAI,CAAC,0BAA0B;oBAClC,MAAM;oBACN,IAAI;oBACJ,YAAY;kBACf;aACJ;YACD,MAAM;SACT;QACD,IAAI,MAAM,CAAC,IAAI,KAAK,gBAAgB,EAAE;YAClC,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;gBAC1C,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAE;aACjE;YACD,MAAM;SACT;QACD,IAAI,MAAM,CAAC,IAAI,KAAK,eAAe,EAAE;YACjC,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;gBAC/C,MAAM;oBACF,IAAI,EAAE,MAAM;oBACZ,IAAI;oBACJ,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,QAAQ,CAAC,SAAS,CAAC;kBAC5B;aACJ;YACD,MAAM;SACT;QACD,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAsB,EAAE;YACxC,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE;gBACvB,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAC;gBAC9D,OAAO,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAC;aACjE;YACD,MAAM;SACT;QACD,IAAI,MAAM,CAAC,IAAI,KAAK,mBAAmB,EAAE;YACrC,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE;gBACvB,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAC;aACjE;YACD,MAAM;SACT;QACD,IAAI,MAAM,CAAC,IAAI,KAAK,oBAAoB,EAAE;YACtC,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE;gBACtB,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAC;aAC/D;SACJ;KACJ;;;;;;;;;IASD,CAAC,qBAAqB,CAAC,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE;QAChD,IAAI,WAAW,CAAC,IAAI,KAAK,YAAY,EAAE;YACnC,MAAM,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,EAAC;YAC5D,IAAI,QAAQ,IAAI,IAAI,EAAE;gBAClB,OAAO,IAAI,CAAC,0BAA0B;oBAClC,QAAQ;oBACR,IAAI;oBACJ,QAAQ;oBACR,KAAK;kBACR;aACJ;YACD,MAAM;SACT;QACD,IAAI,WAAW,CAAC,IAAI,KAAK,eAAe,EAAE;YACtC,KAAK,MAAM,QAAQ,IAAI,WAAW,CAAC,UAAU,EAAE;gBAC3C,MAAM,GAAG,GAAG,eAAe,CAAC,QAAQ,EAAC;;gBAErC,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;oBACpC,QAAQ;iBACX;;gBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAC;gBACjC,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAC;gBAClC,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;oBACpB,MAAM;wBACF,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,IAAI;wBACV,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC;sBAC3B;iBACJ;gBACD,OAAO,IAAI,CAAC,qBAAqB;oBAC7B,QAAQ,CAAC,KAAK;oBACd,QAAQ;oBACR,YAAY;kBACf;aACJ;YACD,MAAM;SACT;QACD,IAAI,WAAW,CAAC,IAAI,KAAK,mBAAmB,EAAE;YAC1C,OAAO,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAC;SACtE;KACJ;;;;;;;;;IASD,CAAC,wBAAwB,CAAC,aAAa,EAAE,IAAI,EAAE,QAAQ,EAAE;QACrD,MAAM,IAAI,GAAG,aAAa,CAAC,KAAI;;QAE/B,IAAI,IAAI,KAAK,iBAAiB,IAAI,IAAI,KAAK,wBAAwB,EAAE;YACjE,MAAM,GAAG;gBACL,IAAI,KAAK,wBAAwB;sBAC3B,SAAS;sBACT,aAAa,CAAC,QAAQ,CAAC,KAAI;YACrC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;gBACrB,MAAM;aACT;;YAED,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAC;YACvB,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAC;YAClC,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;gBACpB,MAAM;oBACF,IAAI,EAAE,aAAa;oBACnB,IAAI;oBACJ,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC;kBAC3B;aACJ;YACD,OAAO,IAAI,CAAC,0BAA0B;gBAClC,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,KAAK,CAAC;gBACnD,IAAI;gBACJ,YAAY;gBACZ,KAAK;cACR;;YAED,MAAM;SACT;;QAED,IAAI,IAAI,KAAK,0BAA0B,EAAE;YACrC,OAAO,IAAI,CAAC,0BAA0B;gBAClC,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,KAAK,CAAC;gBACnD,IAAI;gBACJ,QAAQ;gBACR,KAAK;cACR;YACD,MAAM;SACT;;QAED,IAAI,IAAI,KAAK,iBAAiB,EAAE;YAC5B,MAAM,GAAG,GAAG,aAAa,CAAC,KAAK,CAAC,KAAI;YACpC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;gBACrB,MAAM;aACT;;YAED,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAC;YACvB,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAC;YAClC,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;gBACpB,MAAM;oBACF,IAAI,EAAE,aAAa;oBACnB,IAAI;oBACJ,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC;kBAC3B;aACJ;SACJ;KACJ;CACJ;;AAED,gBAAgB,CAAC,IAAI,GAAG,KAAI;AAC5B,gBAAgB,CAAC,IAAI,GAAG,KAAI;AAC5B,gBAAgB,CAAC,SAAS,GAAG,UAAS;AACtC,gBAAgB,CAAC,GAAG,GAAG,IAAG;;;;;;;;AAQ1B,SAAS,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE;IAChC,OAAO,EAAE,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,SAAS,CAAC;CAC9C;;ACxZD,YAAe;IACX,IAAI;IACJ,SAAS;IACT,GAAG;IACH,YAAY;IACZ,uBAAuB;IACvB,uBAAuB;IACvB,iBAAiB;IACjB,eAAe;IACf,cAAc;IACd,mBAAmB;IACnB,aAAa;IACb,YAAY;IACZ,mBAAmB;IACnB,qBAAqB;IACrB,mBAAmB;IACnB,YAAY;IACZ,YAAY;IACZ,cAAc;IACd,eAAe;IACf,sBAAsB;IACtB,wBAAwB;IACxB,sBAAsB;IACtB,eAAe;IACf,eAAe;IACf,iBAAiB;IACjB,sBAAsB;IACtB,wBAAwB;IACxB,sBAAsB;IACtB,mBAAmB;IACnB,mBAAmB;IACnB,qBAAqB;IACrB,mBAAmB;IACnB,eAAe;IACf,gBAAgB;IAChB,cAAc;IACd,IAAI;IACJ,gBAAgB;CACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}