{"name": "@babel/compat-data", "version": "7.11.0", "author": "The Babel Team (https://babeljs.io/team)", "license": "MIT", "description": "", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-compat-data"}, "publishConfig": {"access": "public"}, "exports": {"./plugins": "./data/plugins.json", "./native-modules": "./data/native-modules.json", "./corejs2-built-ins": "./data/corejs2-built-ins.json", "./corejs3-shipped-proposals": "./data/corejs3-shipped-proposals.json", "./overlapping-plugins": "./data/overlapping-plugins.json", "./plugin-bugfixes": "./data/plugin-bugfixes.json"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "keywords": ["babel", "compat-table", "compat-data"], "dependencies": {"browserslist": "^4.12.0", "invariant": "^2.2.4", "semver": "^5.5.0"}, "devDependencies": {"@babel/helper-compilation-targets": "^7.10.4", "electron-to-chromium": "1.3.513", "lodash": "^4.17.19", "mdn-browser-compat-data": "1.0.31"}, "gitHead": "38dda069eeac2e31bce3f56290998d30bee1ed6b"}