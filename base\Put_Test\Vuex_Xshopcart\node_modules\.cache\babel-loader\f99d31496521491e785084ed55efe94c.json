{"remainingRequest": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\store\\cart\\actions.js", "dependencies": [{"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\store\\cart\\actions.js", "mtime": 1658035355533}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1657986293299}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1657986298727}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\eslint-loader\\index.js", "mtime": 1657986295398}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgQURELCBSRURVQ0UgfSBmcm9tICcuL211dGF0aW9ucy10eXBlJzsKZXhwb3J0IGRlZmF1bHQgewogIGluY3JlbWVudDogZnVuY3Rpb24gaW5jcmVtZW50KF9yZWYsIHBheWxvYWQpIHsKICAgIHZhciBjb21taXQgPSBfcmVmLmNvbW1pdDsKICAgIC8vIGNvbnNvbGUubG9nKCIlYyBbIHBheWxvYWQgXSIsICJmb250LXNpemU6MTNweDsgYmFja2dyb3VuZDojMDBmZmZmOyBjb2xvcjpyZWQ7IiwgcGF5bG9hZCkgLy97dHlwZTogImluY3JlbWVudCIsIGluZGV4OiAwfQogICAgY29tbWl0KEFERCwgcGF5bG9hZCk7CiAgfSwKICByZWR1Y2U6IGZ1bmN0aW9uIHJlZHVjZShfcmVmMiwgcGF5bG9hZCkgewogICAgdmFyIGNvbW1pdCA9IF9yZWYyLmNvbW1pdDsKICAgIGNvbW1pdChSRURVQ0UsIHBheWxvYWQpOwogIH0KfTs="}, {"version": 3, "sources": ["D:/No.1 folder/No.2 folder/dev/dev2/vue/base/Vuex_Xshopcart/src/store/cart/actions.js"], "names": ["ADD", "REDUCE", "increment", "payload", "commit", "reduce"], "mappings": "AAAA,SAASA,GAAT,EAAcC,MAAd,QAA4B,kBAA5B;AACA,eAAe;AACbC,EAAAA,SADa,2BAGVC,OAHU,EAGD;AAAA,QADVC,MACU,QADVA,MACU;AACV;AACAA,IAAAA,MAAM,CAACJ,GAAD,EAAMG,OAAN,CAAN;AACD,GANY;AAObE,EAAAA,MAPa,yBASVF,OATU,EASD;AAAA,QADVC,MACU,SADVA,MACU;AACVA,IAAAA,MAAM,CAACH,MAAD,EAASE,OAAT,CAAN;AACD;AAXY,CAAf", "sourcesContent": ["import { ADD, REDUCE } from './mutations-type'\nexport default {\n  increment ({\n    commit\n  }, payload) {\n    // console.log(\"%c [ payload ]\", \"font-size:13px; background:#00ffff; color:red;\", payload) //{type: \"increment\", index: 0}\n    commit(ADD, payload)\n  },\n  reduce ({\n    commit\n  }, payload) {\n    commit(REDUCE, payload)\n  }\n}"]}]}