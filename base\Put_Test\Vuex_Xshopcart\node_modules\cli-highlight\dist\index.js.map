{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,iDAAoC;AACpC,6CAAgC;AAChC,oGAAgE;AAEhE,iCAAqD;AAErD,SAAS,YAAY,CAAC,IAAsB,EAAE,KAAiB,EAAE,OAAgB;IAAnC,sBAAA,EAAA,UAAiB;IAC3D,QAAQ,IAAI,CAAC,IAAI,EAAE;QACf,KAAK,MAAM,CAAC,CAAC;YACT,IAAM,IAAI,GAAI,IAA6B,CAAC,IAAI,CAAA;YAChD,IAAI,OAAO,KAAK,SAAS,EAAE;gBACvB,OAAO,CAAC,KAAK,CAAC,OAAO,IAAI,qBAAa,CAAC,OAAO,IAAI,aAAK,CAAC,CAAC,IAAI,CAAC,CAAA;aACjE;iBAAM;gBACH,OAAO,IAAI,CAAA;aACd;SACJ;QACD,KAAK,KAAK,CAAC,CAAC;YACR,IAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAE,IAA4B,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;YAChF,IAAI,SAAS,EAAE;gBACX,IAAM,OAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;gBAC1B,IAAM,QAAQ,GAAI,IAA4B,CAAC,UAAU;qBACpD,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,OAAK,CAAC,EAAhC,CAAgC,CAAC;qBAC7C,IAAI,CAAC,EAAE,CAAC,CAAA;gBACb,OAAO,CAAE,KAAa,CAAC,OAAK,CAAC,IAAK,qBAAqB,CAAC,OAAK,CAAC,IAAI,aAAK,CAAC,CAAC,QAAQ,CAAC,CAAA;aACrF;YAED,8FAA8F;YAC9F,gFAAgF;YAChF,OAAQ,IAA4B,CAAC,UAAU,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,EAAzB,CAAyB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;SAClG;KACJ;IACD,MAAM,IAAI,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC,IAAI,CAAC,CAAA;AACrD,CAAC;AAED,SAAS,QAAQ,CAAC,IAAY,EAAE,KAAiB;IAAjB,sBAAA,EAAA,UAAiB;IAC7C,IAAM,QAAQ,GAAG,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE;QACxC,WAAW,EAAE,yCAAkB;KAClC,CAAiC,CAAA;IAClC,OAAO,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,EAAzB,CAAyB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;AAC9E,CAAC;AAqCD;;;;;;;;;;;;;;;;GAgBG;AACH,SAAgB,SAAS,CAAC,IAAY,EAAE,OAA8B;IAA9B,wBAAA,EAAA,YAA8B;IAClE,IAAI,IAAY,CAAA;IAChB,IAAI,OAAO,CAAC,QAAQ,EAAE;QAClB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,CAAA;KACpG;SAAM;QACH,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC,KAAK,CAAA;KAChE;IACD,OAAO,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;AACxC,CAAC;AARD,8BAQC;AAED;;GAEG;AACH,SAAgB,aAAa;IACzB,OAAO,IAAI,CAAC,aAAa,EAAE,CAAA;AAC/B,CAAC;AAFD,sCAEC;AAED;;;GAGG;AACH,SAAgB,gBAAgB,CAAC,IAAY;IACzC,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;AACnC,CAAC;AAFD,4CAEC;AAED,kBAAe,SAAS,CAAA;AACxB,6BAAuB"}