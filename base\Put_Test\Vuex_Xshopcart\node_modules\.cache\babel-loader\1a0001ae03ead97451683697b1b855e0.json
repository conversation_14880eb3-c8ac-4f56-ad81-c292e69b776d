{"remainingRequest": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\App.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\App.vue", "mtime": 1649424514352}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1657986293299}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1657986298727}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1657986293299}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1657986322488}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KaW1wb3J0IEhlbGxvV29ybGQgZnJvbSAnLi9jb21wb25lbnRzL0hlbGxvV29ybGQudnVlJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdBcHAnLAogIGNvbXBvbmVudHM6IHsKICAgIEhlbGxvV29ybGQ6IEhlbGxvV29ybGQKICB9Cn07"}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";;;;;;AAOA,OAAA,UAAA,MAAA,6BAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,KADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,UAAA,EAAA;AADA;AAFA,CAAA", "sourcesContent": ["<template>\n<div id=\"app\">\n  <HelloWorld msg=\"Welcome to Your Vue.js App\" />\n</div>\n</template>\n\n<script>\nimport HelloWorld from './components/HelloWorld.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    HelloWorld\n  }\n}\n</script>\n\n<style lang=\"scss\">\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-align: center;\n  color: #2c3e50;\n  margin-top: 60px;\n}\n</style>\n"], "sourceRoot": "src"}]}