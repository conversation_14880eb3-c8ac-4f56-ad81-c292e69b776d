{"name": "rgba-regex", "description": "Regex for matching RGBA color strings.", "author": "<PERSON>", "version": "1.0.0", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "mocha test"}, "repository": {"type": "git", "url": "https://github.com/johnotander/rgba-regex.git"}, "keywords": ["css", "regex", "regexp", "regexps", "rgba", "color", "regular", "expression"], "license": "MIT", "bugs": {"url": "https://github.com/johnotander/rgba-regex/issues"}, "homepage": "https://github.com/johnotander/rgba-regex", "dependencies": {}, "devDependencies": {"mocha": "*"}}