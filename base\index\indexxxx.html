<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="UTF-8">
    <title>iframe标签</title>
</head>
<body>
<!--  iframe 标签可以在页面上开辟一个小区域显示一个单独的页面
        iframe和a标签组合使用的步骤：
            1.在iframe标签中使用name属性定义一个名称
            2.在a标签的target属性上设置iframe的name的属性值
-->
  <!-- <iframe src="https://img-blog.csdnimg.cn/d097bbd70e5e4442b67386316736f716.png" width="500" height="400" name="点击跳转目标"></iframe> -->
  <!-- <a href="https://img-blog.csdnimg.cn/d097bbd70e5e4442b67386316736f716.png" target="点击跳转目标"  >超连接.html</a> -->
  <script>
    window.open('./ThreeJS.html', '_blank')
  </script>

</body>
</html>
