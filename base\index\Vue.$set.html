<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>vue $set</title>
</head>
<body>
<div id="app">
  <ul>
    <li>{{obj.name}}</li>
    <li>{{obj.age}}</li>
    <li>{{obj.sex}}</li>
    <li>{{obj.info.content}}</li>
    </li>
  </ul>
  <button @click="showSex">显示性别</button>
</div>
<script src="https://cdn.staticfile.org/vue/2.2.2/vue.min.js"></script>
<script>
let vm = new Vue({
  el:'#app',
  data() {
    return {
      obj:{
        name: "lemo",
        age: '3',
        info: {
          content: 'my name is lemo'
        }
      }
    }
  },
  methods:{
    showSex(){
    //   Vue.set(this.obj,'sex', '女')
      this.$set(this.obj,'sex','🚹')
    }
  },
  mounted(){
    this.$set(this.obj.info,'content', 'who is my girlfriend?');
  }
});
</script>
</body>
</html>
