# is-docker [![Build Status](https://travis-ci.com/sindresorhus/is-docker.svg?branch=master)](https://travis-ci.com/github/sindresorhus/is-docker)

> Check if the process is running inside a Docker container

## Install

```
$ npm install is-docker
```

## Usage

```js
const isDocker = require('is-docker');

if (isDocker()) {
	console.log('Running inside a Docker container');
}
```

## CLI

```
$ is-docker
```

Exits with code 0 if inside a Docker container and 2 if not.
