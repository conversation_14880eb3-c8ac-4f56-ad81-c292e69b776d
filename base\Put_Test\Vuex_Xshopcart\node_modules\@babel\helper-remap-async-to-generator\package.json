{"name": "@babel/helper-remap-async-to-generator", "version": "7.11.4", "description": "Helper function to remap async functions to generators", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-remap-async-to-generator"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/helper-annotate-as-pure": "^7.10.4", "@babel/helper-wrap-function": "^7.10.4", "@babel/template": "^7.10.4", "@babel/types": "^7.10.4"}, "devDependencies": {"@babel/traverse": "^7.10.4"}, "gitHead": "90b198956995195ea00e7ac9912c2260e44d8746"}