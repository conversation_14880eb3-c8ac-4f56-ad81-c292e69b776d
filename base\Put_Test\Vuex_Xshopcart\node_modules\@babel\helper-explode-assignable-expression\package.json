{"name": "@babel/helper-explode-assignable-expression", "version": "7.11.4", "description": "Helper function to explode an assignable expression", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-explode-assignable-expression"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "dependencies": {"@babel/types": "^7.10.4"}, "devDependencies": {"@babel/traverse": "^7.10.4"}, "gitHead": "90b198956995195ea00e7ac9912c2260e44d8746"}