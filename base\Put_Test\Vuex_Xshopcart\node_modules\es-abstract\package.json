{"name": "es-abstract", "version": "1.17.6", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "description": "ECMAScript spec abstract operations.", "license": "MIT", "main": "index.js", "scripts": {"prespackle": "git ls-files | xargs git check-attr spackled | grep -v 'unspecified$' | cut -d: -f1 | xargs rm || true", "spackle": "node operations/spackle 1", "postspackle": "git ls-files | xargs git check-attr spackled | grep -v 'unspecified$' | cut -d: -f1 | xargs git add", "prepublish": "safe-publish-latest && (not-in-publish || npm run spackle)", "pretest": "npm run lint", "test": "npm run tests-only", "posttest": "aud --production", "tests-only": "node test", "coverage": "nyc npm run tests-only >/dev/null", "postcoverage": "nyc report", "lint": "eslint .", "eccheck": "eclint check *.js **/*.js > /dev/null"}, "repository": {"type": "git", "url": "git://github.com/ljharb/es-abstract.git"}, "keywords": ["ECMAScript", "ES", "abstract", "operation", "abstract operation", "JavaScript", "ES5", "ES6", "ES7"], "dependencies": {"es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1", "is-callable": "^1.2.0", "is-regex": "^1.1.0", "object-inspect": "^1.7.0", "object-keys": "^1.1.1", "object.assign": "^4.1.0", "string.prototype.trimend": "^1.0.1", "string.prototype.trimstart": "^1.0.1"}, "devDependencies": {"@ljharb/eslint-config": "^17.1.0", "array.prototype.indexof": "^1.0.0", "aud": "^1.1.2", "cheerio": "^1.0.0-rc.3", "diff": "^4.0.2", "eclint": "^2.8.1", "eslint": "^7.2.0", "foreach": "^2.0.5", "functions-have-names": "^1.2.1", "has-strict-mode": "^1.0.0", "in-publish": "^2.0.1", "make-arrow-function": "^1.2.0", "nyc": "^10.3.2", "object-is": "^1.1.2", "object.fromentries": "^2.0.2", "safe-publish-latest": "^1.1.4", "tape": "^5.0.1"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "greenkeeper": {"//": "nyc is ignored because it requires node 4+, and we support older than that", "ignore": ["nyc"]}}