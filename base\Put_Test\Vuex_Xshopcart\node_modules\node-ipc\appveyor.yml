# http://www.appveyor.com/docs/appveyor-yml

# version format
version: 8.9.3.{build}

# Test against these versions of Io.js and Node.js.
environment:
  matrix:
  # node.js
    - nodejs_version: "6"
    - nodejs_version: "7"
    - nodejs_version: "8"

# Install scripts. (runs after repo cloning)
install:
  # Get the latest stable version of Node 0.STABLE.latest
  - ps: Install-Product node $env:nodejs_version
  - npm -g install npm
  - npm install

# Post-install test scripts.
test_script:
  # Output useful info for debugging.
  - node --version
  - npm --version
  # run tests
  - npm run test-windows

# Don't actually build.
build: off
