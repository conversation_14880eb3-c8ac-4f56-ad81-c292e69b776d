{"name": "import-local", "version": "2.0.0", "description": "Let a globally installed package use a locally installed version of itself if available", "license": "MIT", "repository": "sindresorhus/import-local", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "fixtures/cli.js"], "keywords": ["import", "local", "require", "resolve", "global", "version", "prefer", "cli"], "dependencies": {"pkg-dir": "^3.0.0", "resolve-cwd": "^2.0.0"}, "devDependencies": {"ava": "*", "cpy": "^7.0.1", "del": "^3.0.0", "execa": "^0.11.0", "xo": "*"}, "xo": {"ignores": ["fixtures"]}}