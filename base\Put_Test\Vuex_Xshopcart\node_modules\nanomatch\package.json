{"name": "nanomatch", "description": "Fast, minimal glob matcher for node.js. Similar to micromatch, minimatch and multimatch, but complete Bash 4.3 wildcard support only (no support for exglobs, posix brackets or braces)", "version": "1.2.13", "homepage": "https://github.com/micromatch/nanomatch", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON> (http://badassjs.com)", "<PERSON> (http://twitter.com/jonschlink<PERSON>)"], "repository": "micromatch/nanomatch", "bugs": {"url": "https://github.com/micromatch/nanomatch/issues"}, "license": "MIT", "files": ["index.js", "lib"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.3", "gulp-mocha": "^5.0.0", "helper-changelog": "^0.3.0", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.3", "multimatch": "^2.1.0"}, "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "nanomatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "lintDeps": {"dependencies": {"options": {"lock": {"snapdragon": "^0.8.1"}}}, "devDependencies": {"files": {"options": {"ignore": ["benchmark/**"]}}}}, "verb": {"toc": "collapsible", "layout": "default", "tasks": ["readme"], "helpers": ["helper-changelog"], "plugins": ["gulp-format-md"], "related": {"list": ["extglob", "is-extglob", "is-glob", "micromatch"]}, "reflinks": ["expand-brackets", "expand-tilde", "glob-object", "micromatch", "minimatch", "options", "snapdragon"], "lint": {"reflinks": true}}}