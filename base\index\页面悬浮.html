<!DOCTYPE html> 
<html> 
    <head>
    <title>页面banner悬浮</title>
        <style type="text/css">
        .div1 {
            height:2000px;
        }
        .div2 {
            width:100%;
            height:35px;
            background-color:#3399FF;
            margin-top:100px;
            text-align: center;
        }
        .div2_1{
            position:fixed;
            width:100%;
            height:35px;
            z-index:999;
            text-align: center;
            background-color:#3399FF;
            top:0px;
            _position:absolute; 
             /* position加短下划线_，从而将绝对定位的参照改成了body */
            _bottom:auto;  
            /* _position和_top是对IE6的hack */
            _top:expression(eval(document.documentElement.scrollTop));
        }
        </style>
    </head>
    <body>
        <div class="div1">
            <div id="div2" class="div2">--88--</div>
        </div>
        <script type="text/javascript">
            window.onscroll=function(){     
                var t = document.documentElement.scrollTop||document.body.scrollTop;  
                var div2 = document.getElementById("div2"); 
                console.log('expression:>> ', (eval(document.documentElement.scrollTop)));
                if(t>= 100){ 
                    div2.className = "div2_1";
                }else{
                    div2.className = "div2";
                } 
            }
        </script>
    </body>
</html>