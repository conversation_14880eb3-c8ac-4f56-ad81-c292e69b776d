{
	"root": true,

	"extends": "@ljharb",

	"env": {
		"es6": true,
	},

	"rules": {
		"array-bracket-newline": 0,
		"array-element-newline": 0,
		"complexity": 0,
		"eqeqeq": [2, "allow-null"],
		"func-name-matching": 0,
		"id-length": [2, { "min": 1, "max": 40 }],
		"max-params": [2, 4],
		"max-statements-per-line": [2, { "max": 2 }],
		"multiline-comment-style": 0,
		"no-magic-numbers": 0,
		"new-cap": 0,
		"no-extra-parens": 1,
		"operator-linebreak": [2, "before"],
		"sort-keys": 0,
	},

	"overrides": [
		{
			"files": "GetIntrinsic.js",
			"rules": {
				"max-statements": 0,
			}
		},
		{
			"files": "operations/*",
			"rules": {
				"max-lines": 0,
			},
		},
		{
			"files": "operations/*.js",
			"parserOptions": {
				"ecmaVersion": 2020,
			},
			"rules": {
				"no-console": 0,
				"no-multi-str": 0,
			},
		},
		{
			"files": "operations/getOps.js",
			"rules": {
				"no-console": 0,
				"no-process-exit": 0,
			},
		},
		{
			"files": "test/**",
			"rules": {
				"id-length": 0,
				"max-lines": 0,
				"max-lines-per-function": 0,
				"max-statements-per-line": [2, { "max": 3 }],
				"max-statements": 0,
				"no-implicit-coercion": 0,
				"no-invalid-this": 1,
				"object-curly-newline": 0,
				"prefer-regex-literals": 0,
			},
		},
	],
}
