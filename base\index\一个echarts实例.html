<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>一个ECharts 示例</title>
    <!-- 引入 echarts.js -->
    <script src="https://cdn.bootcdn.net/ajax/libs/echarts/4.8.0/echarts-en.common.js" rel="external nofollow" ></script>
    <style>
        /* #main {
            color:red;
        } */
    </style>
</head>
<body>
    <!-- 为ECharts准备一个具备大小（宽高）的Dom -->
    <div id="main" style="width: 600px;height:400px;"></div>
    <script type="text/javascript">
        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById('main'));
        // 指定图表的配置项和数据
        var option = {
            title: {
                text: 'ECharts 示例',
                textStyle:{  //标题样式
                    color:'chartreuse',
                    fontSize:'16',
                    fontFamily:'fangsong',
                    fontWeight:'600',
                    fontStyle:'normal',
                },
            },
            tooltip: {  //鼠标悬浮的数据
                trigger: 'axis', 
                // item  主要在散点图，饼图等无类目轴的图表中使用。
                // axis  坐标轴触发，主要在柱状图，折线图等会使用类目轴的图表中使用
                // none   什么都不触发
                axisPointer: {  // 坐标轴指示器配置项
                    type: 'line'
                    // 'line'   直线指示器
                    // 'shadow' 阴影指示器
                    // 'none' 无指示器
                    // 'cross' 十字准星指示器。其实是种简写，表示启用两个正交的轴的 axisPointer。
                },
                formatter: function (params) {  
                    // params 数组，包括一个树状图对应的数据，data,value（这俩值一样），
                    // name（xAxis.data(i)的 name名称），color（柱状图的颜色），marker（柱状图的样式，可更改），dataIndex（柱状图的下标）
                    console.log('params :>> ', params);
                    // return params[0].data + '元' 
                    return params[1].data + '%' 
                }
            },
            legend: {  //legend（头部标题） series（底部数据）相互对应，
                data:['销量7'],
                // data:['比例'],
                formatter:function(name){
                    console.log('name :>> ', name);
                    return name + '(说明)'
                }
            },
            xAxis: {
                name:'x轴名称',
                position:'botttom', //x轴的位置，top, botttom (默认)
                data: ["衬衫","羊毛衫","雪纺衫","裤子","高跟鞋","袜子","内衣"],
            },
            yAxis: {
                name:'y轴名称',
                position:'left',  //默认
                axisLabel: {
                    formatter: '{value} 个'
                }
            },
            series: [
                {
                    name: '销量7',
                    type: 'bar',  // line 折线图  bar 柱状图  pie 饼图  scatter 散点图  effectScatter 带有涟漪的散点图  radar 雷达图
                    data: [5, 20, 36, 10, 10, 20, 66]
                },
                {
                    name: '比例',
                    type: 'line',
                    data: [15, 25, 35, 45, 25, 35,55]
                }
            ]
        }; 
        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
    </script>
</body>
</html>
