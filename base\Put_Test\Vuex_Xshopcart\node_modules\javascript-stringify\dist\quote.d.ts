import { Next } from "./types";
/**
 * Quote a string.
 */
export declare function quoteString(str: string): string;
/**
 * Test for valid JavaScript identifier.
 */
export declare const IS_VALID_IDENTIFIER: RegExp;
/**
 * Check if a variable name is valid.
 */
export declare function isValidVariableName(name: PropertyK<PERSON>): name is string;
/**
 * Quote JavaScript key access.
 */
export declare function quoteKey(key: PropertyKey, next: Next): string | undefined;
/**
 * Serialize the path to a string.
 */
export declare function stringifyPath(path: PropertyKey[], next: Next): string;
