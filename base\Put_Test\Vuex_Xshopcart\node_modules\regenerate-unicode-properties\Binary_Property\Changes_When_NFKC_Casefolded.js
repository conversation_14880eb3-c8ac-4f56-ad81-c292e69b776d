const set = require('regenerate')(0xA0, 0xA8, 0xAA, 0xAD, 0xAF, 0x100, 0x102, 0x104, 0x106, 0x108, 0x10A, 0x10C, 0x10E, 0x110, 0x112, 0x114, 0x116, 0x118, 0x11A, 0x11C, 0x11E, 0x120, 0x122, 0x124, 0x126, 0x128, 0x12A, 0x12C, 0x12E, 0x130, 0x136, 0x139, 0x13B, 0x13D, 0x143, 0x145, 0x147, 0x14C, 0x14E, 0x150, 0x152, 0x154, 0x156, 0x158, 0x15A, 0x15C, 0x15E, 0x160, 0x162, 0x164, 0x166, 0x168, 0x16A, 0x16C, 0x16E, 0x170, 0x172, 0x174, 0x176, 0x17B, 0x17D, 0x17F, 0x184, 0x1A2, 0x1A4, 0x1A9, 0x1AC, 0x1B5, 0x1BC, 0x1CF, 0x1D1, 0x1D3, 0x1D5, 0x1D7, 0x1D9, 0x1DB, 0x1DE, 0x1E0, 0x1E2, 0x1E4, 0x1E6, 0x1E8, 0x1EA, 0x1EC, 0x1EE, 0x1FA, 0x1FC, 0x1FE, 0x200, 0x202, 0x204, 0x206, 0x208, 0x20A, 0x20C, 0x20E, 0x210, 0x212, 0x214, 0x216, 0x218, 0x21A, 0x21C, 0x21E, 0x220, 0x222, 0x224, 0x226, 0x228, 0x22A, 0x22C, 0x22E, 0x230, 0x232, 0x241, 0x248, 0x24A, 0x24C, 0x24E, 0x34F, 0x370, 0x372, 0x374, 0x376, 0x37A, 0x38C, 0x3C2, 0x3D8, 0x3DA, 0x3DC, 0x3DE, 0x3E0, 0x3E2, 0x3E4, 0x3E6, 0x3E8, 0x3EA, 0x3EC, 0x3EE, 0x3F7, 0x460, 0x462, 0x464, 0x466, 0x468, 0x46A, 0x46C, 0x46E, 0x470, 0x472, 0x474, 0x476, 0x478, 0x47A, 0x47C, 0x47E, 0x480, 0x48A, 0x48C, 0x48E, 0x490, 0x492, 0x494, 0x496, 0x498, 0x49A, 0x49C, 0x49E, 0x4A0, 0x4A2, 0x4A4, 0x4A6, 0x4A8, 0x4AA, 0x4AC, 0x4AE, 0x4B0, 0x4B2, 0x4B4, 0x4B6, 0x4B8, 0x4BA, 0x4BC, 0x4BE, 0x4C3, 0x4C5, 0x4C7, 0x4C9, 0x4CB, 0x4CD, 0x4D0, 0x4D2, 0x4D4, 0x4D6, 0x4D8, 0x4DA, 0x4DC, 0x4DE, 0x4E0, 0x4E2, 0x4E4, 0x4E6, 0x4E8, 0x4EA, 0x4EC, 0x4EE, 0x4F0, 0x4F2, 0x4F4, 0x4F6, 0x4F8, 0x4FA, 0x4FC, 0x4FE, 0x500, 0x502, 0x504, 0x506, 0x508, 0x50A, 0x50C, 0x50E, 0x510, 0x512, 0x514, 0x516, 0x518, 0x51A, 0x51C, 0x51E, 0x520, 0x522, 0x524, 0x526, 0x528, 0x52A, 0x52C, 0x52E, 0x587, 0x61C, 0x9DF, 0xA33, 0xA36, 0xA5E, 0xE33, 0xEB3, 0xF0C, 0xF43, 0xF4D, 0xF52, 0xF57, 0xF5C, 0xF69, 0xF73, 0xF81, 0xF93, 0xF9D, 0xFA2, 0xFA7, 0xFAC, 0xFB9, 0x10C7, 0x10CD, 0x10FC, 0x1D78, 0x1E00, 0x1E02, 0x1E04, 0x1E06, 0x1E08, 0x1E0A, 0x1E0C, 0x1E0E, 0x1E10, 0x1E12, 0x1E14, 0x1E16, 0x1E18, 0x1E1A, 0x1E1C, 0x1E1E, 0x1E20, 0x1E22, 0x1E24, 0x1E26, 0x1E28, 0x1E2A, 0x1E2C, 0x1E2E, 0x1E30, 0x1E32, 0x1E34, 0x1E36, 0x1E38, 0x1E3A, 0x1E3C, 0x1E3E, 0x1E40, 0x1E42, 0x1E44, 0x1E46, 0x1E48, 0x1E4A, 0x1E4C, 0x1E4E, 0x1E50, 0x1E52, 0x1E54, 0x1E56, 0x1E58, 0x1E5A, 0x1E5C, 0x1E5E, 0x1E60, 0x1E62, 0x1E64, 0x1E66, 0x1E68, 0x1E6A, 0x1E6C, 0x1E6E, 0x1E70, 0x1E72, 0x1E74, 0x1E76, 0x1E78, 0x1E7A, 0x1E7C, 0x1E7E, 0x1E80, 0x1E82, 0x1E84, 0x1E86, 0x1E88, 0x1E8A, 0x1E8C, 0x1E8E, 0x1E90, 0x1E92, 0x1E94, 0x1E9E, 0x1EA0, 0x1EA2, 0x1EA4, 0x1EA6, 0x1EA8, 0x1EAA, 0x1EAC, 0x1EAE, 0x1EB0, 0x1EB2, 0x1EB4, 0x1EB6, 0x1EB8, 0x1EBA, 0x1EBC, 0x1EBE, 0x1EC0, 0x1EC2, 0x1EC4, 0x1EC6, 0x1EC8, 0x1ECA, 0x1ECC, 0x1ECE, 0x1ED0, 0x1ED2, 0x1ED4, 0x1ED6, 0x1ED8, 0x1EDA, 0x1EDC, 0x1EDE, 0x1EE0, 0x1EE2, 0x1EE4, 0x1EE6, 0x1EE8, 0x1EEA, 0x1EEC, 0x1EEE, 0x1EF0, 0x1EF2, 0x1EF4, 0x1EF6, 0x1EF8, 0x1EFA, 0x1EFC, 0x1EFE, 0x1F59, 0x1F5B, 0x1F5D, 0x1F5F, 0x1F71, 0x1F73, 0x1F75, 0x1F77, 0x1F79, 0x1F7B, 0x1F7D, 0x1FD3, 0x1FE3, 0x2011, 0x2017, 0x203C, 0x203E, 0x2057, 0x20A8, 0x2124, 0x2126, 0x2128, 0x2183, 0x2189, 0x2A0C, 0x2ADC, 0x2C60, 0x2C67, 0x2C69, 0x2C6B, 0x2C72, 0x2C75, 0x2C82, 0x2C84, 0x2C86, 0x2C88, 0x2C8A, 0x2C8C, 0x2C8E, 0x2C90, 0x2C92, 0x2C94, 0x2C96, 0x2C98, 0x2C9A, 0x2C9C, 0x2C9E, 0x2CA0, 0x2CA2, 0x2CA4, 0x2CA6, 0x2CA8, 0x2CAA, 0x2CAC, 0x2CAE, 0x2CB0, 0x2CB2, 0x2CB4, 0x2CB6, 0x2CB8, 0x2CBA, 0x2CBC, 0x2CBE, 0x2CC0, 0x2CC2, 0x2CC4, 0x2CC6, 0x2CC8, 0x2CCA, 0x2CCC, 0x2CCE, 0x2CD0, 0x2CD2, 0x2CD4, 0x2CD6, 0x2CD8, 0x2CDA, 0x2CDC, 0x2CDE, 0x2CE0, 0x2CE2, 0x2CEB, 0x2CED, 0x2CF2, 0x2D6F, 0x2E9F, 0x2EF3, 0x3000, 0x3036, 0x309F, 0x30FF, 0xA640, 0xA642, 0xA644, 0xA646, 0xA648, 0xA64A, 0xA64C, 0xA64E, 0xA650, 0xA652, 0xA654, 0xA656, 0xA658, 0xA65A, 0xA65C, 0xA65E, 0xA660, 0xA662, 0xA664, 0xA666, 0xA668, 0xA66A, 0xA66C, 0xA680, 0xA682, 0xA684, 0xA686, 0xA688, 0xA68A, 0xA68C, 0xA68E, 0xA690, 0xA692, 0xA694, 0xA696, 0xA698, 0xA69A, 0xA722, 0xA724, 0xA726, 0xA728, 0xA72A, 0xA72C, 0xA72E, 0xA732, 0xA734, 0xA736, 0xA738, 0xA73A, 0xA73C, 0xA73E, 0xA740, 0xA742, 0xA744, 0xA746, 0xA748, 0xA74A, 0xA74C, 0xA74E, 0xA750, 0xA752, 0xA754, 0xA756, 0xA758, 0xA75A, 0xA75C, 0xA75E, 0xA760, 0xA762, 0xA764, 0xA766, 0xA768, 0xA76A, 0xA76C, 0xA76E, 0xA770, 0xA779, 0xA77B, 0xA780, 0xA782, 0xA784, 0xA786, 0xA78B, 0xA78D, 0xA790, 0xA792, 0xA796, 0xA798, 0xA79A, 0xA79C, 0xA79E, 0xA7A0, 0xA7A2, 0xA7A4, 0xA7A6, 0xA7A8, 0xA7B6, 0xA7B8, 0xA7BA, 0xA7BC, 0xA7BE, 0xA7C2, 0xA7C9, 0xA7F5, 0xAB69, 0xFA10, 0xFA12, 0xFA20, 0xFA22, 0xFB1D, 0xFB3E, 0xFE74, 0xFEFF, 0x1D4A2, 0x1D4BB, 0x1D546, 0x1EE24, 0x1EE27, 0x1EE39, 0x1EE3B, 0x1EE42, 0x1EE47, 0x1EE49, 0x1EE4B, 0x1EE54, 0x1EE57, 0x1EE59, 0x1EE5B, 0x1EE5D, 0x1EE5F, 0x1EE64, 0x1EE7E, 0x1F190);
set.addRange(0x41, 0x5A).addRange(0xB2, 0xB5).addRange(0xB8, 0xBA).addRange(0xBC, 0xBE).addRange(0xC0, 0xD6).addRange(0xD8, 0xDF).addRange(0x132, 0x134).addRange(0x13F, 0x141).addRange(0x149, 0x14A).addRange(0x178, 0x179).addRange(0x181, 0x182).addRange(0x186, 0x187).addRange(0x189, 0x18B).addRange(0x18E, 0x191).addRange(0x193, 0x194).addRange(0x196, 0x198).addRange(0x19C, 0x19D).addRange(0x19F, 0x1A0).addRange(0x1A6, 0x1A7).addRange(0x1AE, 0x1AF).addRange(0x1B1, 0x1B3).addRange(0x1B7, 0x1B8).addRange(0x1C4, 0x1CD).addRange(0x1F1, 0x1F4).addRange(0x1F6, 0x1F8).addRange(0x23A, 0x23B).addRange(0x23D, 0x23E).addRange(0x243, 0x246).addRange(0x2B0, 0x2B8).addRange(0x2D8, 0x2DD).addRange(0x2E0, 0x2E4).addRange(0x340, 0x341).addRange(0x343, 0x345).addRange(0x37E, 0x37F).addRange(0x384, 0x38A).addRange(0x38E, 0x38F).addRange(0x391, 0x3A1).addRange(0x3A3, 0x3AB).addRange(0x3CF, 0x3D6).addRange(0x3F0, 0x3F2).addRange(0x3F4, 0x3F5).addRange(0x3F9, 0x3FA).addRange(0x3FD, 0x42F).addRange(0x4C0, 0x4C1).addRange(0x531, 0x556).addRange(0x675, 0x678).addRange(0x958, 0x95F).addRange(0x9DC, 0x9DD).addRange(0xA59, 0xA5B).addRange(0xB5C, 0xB5D).addRange(0xEDC, 0xEDD);
set.addRange(0xF75, 0xF79).addRange(0x10A0, 0x10C5).addRange(0x115F, 0x1160).addRange(0x13F8, 0x13FD).addRange(0x17B4, 0x17B5).addRange(0x180B, 0x180E).addRange(0x1C80, 0x1C88).addRange(0x1C90, 0x1CBA).addRange(0x1CBD, 0x1CBF).addRange(0x1D2C, 0x1D2E).addRange(0x1D30, 0x1D3A).addRange(0x1D3C, 0x1D4D).addRange(0x1D4F, 0x1D6A).addRange(0x1D9B, 0x1DBF).addRange(0x1E9A, 0x1E9B).addRange(0x1F08, 0x1F0F).addRange(0x1F18, 0x1F1D).addRange(0x1F28, 0x1F2F).addRange(0x1F38, 0x1F3F).addRange(0x1F48, 0x1F4D).addRange(0x1F68, 0x1F6F).addRange(0x1F80, 0x1FAF).addRange(0x1FB2, 0x1FB4).addRange(0x1FB7, 0x1FC4).addRange(0x1FC7, 0x1FCF).addRange(0x1FD8, 0x1FDB).addRange(0x1FDD, 0x1FDF).addRange(0x1FE8, 0x1FEF).addRange(0x1FF2, 0x1FF4).addRange(0x1FF7, 0x1FFE).addRange(0x2000, 0x200F).addRange(0x2024, 0x2026).addRange(0x202A, 0x202F).addRange(0x2033, 0x2034).addRange(0x2036, 0x2037).addRange(0x2047, 0x2049).addRange(0x205F, 0x2071).addRange(0x2074, 0x208E).addRange(0x2090, 0x209C).addRange(0x2100, 0x2103).addRange(0x2105, 0x2107).addRange(0x2109, 0x2113).addRange(0x2115, 0x2116).addRange(0x2119, 0x211D).addRange(0x2120, 0x2122).addRange(0x212A, 0x212D).addRange(0x212F, 0x2139).addRange(0x213B, 0x2140).addRange(0x2145, 0x2149).addRange(0x2150, 0x217F).addRange(0x222C, 0x222D);
set.addRange(0x222F, 0x2230).addRange(0x2329, 0x232A).addRange(0x2460, 0x24EA).addRange(0x2A74, 0x2A76).addRange(0x2C00, 0x2C2E).addRange(0x2C62, 0x2C64).addRange(0x2C6D, 0x2C70).addRange(0x2C7C, 0x2C80).addRange(0x2F00, 0x2FD5).addRange(0x3038, 0x303A).addRange(0x309B, 0x309C).addRange(0x3131, 0x318E).addRange(0x3192, 0x319F).addRange(0x3200, 0x321E).addRange(0x3220, 0x3247).addRange(0x3250, 0x327E).addRange(0x3280, 0x33FF).addRange(0xA69C, 0xA69D).addRange(0xA77D, 0xA77E).addRange(0xA7AA, 0xA7AE).addRange(0xA7B0, 0xA7B4).addRange(0xA7C4, 0xA7C7).addRange(0xA7F8, 0xA7F9).addRange(0xAB5C, 0xAB5F).addRange(0xAB70, 0xABBF).addRange(0xF900, 0xFA0D).addRange(0xFA15, 0xFA1E).addRange(0xFA25, 0xFA26).addRange(0xFA2A, 0xFA6D).addRange(0xFA70, 0xFAD9).addRange(0xFB00, 0xFB06).addRange(0xFB13, 0xFB17).addRange(0xFB1F, 0xFB36).addRange(0xFB38, 0xFB3C).addRange(0xFB40, 0xFB41).addRange(0xFB43, 0xFB44).addRange(0xFB46, 0xFBB1).addRange(0xFBD3, 0xFD3D).addRange(0xFD50, 0xFD8F).addRange(0xFD92, 0xFDC7).addRange(0xFDF0, 0xFDFC).addRange(0xFE00, 0xFE19).addRange(0xFE30, 0xFE44).addRange(0xFE47, 0xFE52).addRange(0xFE54, 0xFE66).addRange(0xFE68, 0xFE6B).addRange(0xFE70, 0xFE72).addRange(0xFE76, 0xFEFC).addRange(0xFF01, 0xFFBE).addRange(0xFFC2, 0xFFC7).addRange(0xFFCA, 0xFFCF);
set.addRange(0xFFD2, 0xFFD7).addRange(0xFFDA, 0xFFDC).addRange(0xFFE0, 0xFFE6).addRange(0xFFE8, 0xFFEE).addRange(0xFFF0, 0xFFF8).addRange(0x10400, 0x10427).addRange(0x104B0, 0x104D3).addRange(0x10C80, 0x10CB2).addRange(0x118A0, 0x118BF).addRange(0x16E40, 0x16E5F).addRange(0x1BCA0, 0x1BCA3).addRange(0x1D15E, 0x1D164).addRange(0x1D173, 0x1D17A).addRange(0x1D1BB, 0x1D1C0).addRange(0x1D400, 0x1D454).addRange(0x1D456, 0x1D49C).addRange(0x1D49E, 0x1D49F).addRange(0x1D4A5, 0x1D4A6).addRange(0x1D4A9, 0x1D4AC).addRange(0x1D4AE, 0x1D4B9).addRange(0x1D4BD, 0x1D4C3).addRange(0x1D4C5, 0x1D505).addRange(0x1D507, 0x1D50A).addRange(0x1D50D, 0x1D514).addRange(0x1D516, 0x1D51C).addRange(0x1D51E, 0x1D539).addRange(0x1D53B, 0x1D53E).addRange(0x1D540, 0x1D544).addRange(0x1D54A, 0x1D550).addRange(0x1D552, 0x1D6A5).addRange(0x1D6A8, 0x1D7CB).addRange(0x1D7CE, 0x1D7FF).addRange(0x1E900, 0x1E921).addRange(0x1EE00, 0x1EE03).addRange(0x1EE05, 0x1EE1F).addRange(0x1EE21, 0x1EE22).addRange(0x1EE29, 0x1EE32).addRange(0x1EE34, 0x1EE37).addRange(0x1EE4D, 0x1EE4F).addRange(0x1EE51, 0x1EE52).addRange(0x1EE61, 0x1EE62).addRange(0x1EE67, 0x1EE6A).addRange(0x1EE6C, 0x1EE72).addRange(0x1EE74, 0x1EE77).addRange(0x1EE79, 0x1EE7C).addRange(0x1EE80, 0x1EE89).addRange(0x1EE8B, 0x1EE9B).addRange(0x1EEA1, 0x1EEA3).addRange(0x1EEA5, 0x1EEA9).addRange(0x1EEAB, 0x1EEBB).addRange(0x1F100, 0x1F10A);
set.addRange(0x1F110, 0x1F12E).addRange(0x1F130, 0x1F14F).addRange(0x1F16A, 0x1F16C).addRange(0x1F200, 0x1F202).addRange(0x1F210, 0x1F23B).addRange(0x1F240, 0x1F248).addRange(0x1F250, 0x1F251).addRange(0x1FBF0, 0x1FBF9).addRange(0x2F800, 0x2FA1D).addRange(0xE0000, 0xE0FFF);
module.exports = set;
