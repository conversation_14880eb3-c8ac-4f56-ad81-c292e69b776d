<div class="apiDetail">
<div>
	<h2><span>Boolean</span><span class="path">treeNode.</span>isHover</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.exedit</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Used to record the hover status of node's DOM. For 'setting.view.addHoverDom / removeHoverDom'.</p>
			<p class="highlight_red">Do not initialize or modify it, it is an internal argument.</p>
			<p>Default: false</p>
		</div>
	</div>
	<h3>Boolean Format</h3>
	<div class="desc">
	<p>true means: the node's DOM is in hover.</p>
	<p>false means: the node's DOM is not in hover.</p>
	</div>
</div>
</div>