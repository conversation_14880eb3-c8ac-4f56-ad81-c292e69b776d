module.exports={A:{A:{"2":"I F E D kB","129":"A B"},B:{"1":"M V N WB KB","129":"C O","1025":"P H J K L"},C:{"2":"tB RB G W I F E D A B C O P H J K L X Y Z a b c d e f g jB rB","513":"0 1 2 3 4 5 6 7 8 9 h i j k l m n o p q r s t u v w x Q z AB YB CB JB EB FB GB HB DB BB y T S LB MB NB OB PB QB IB SB M V N iB"},D:{"1":"0 1 2 3 4 5 6 7 8 9 I F E D A B C O P H J K L X Y Z a b c d e f g h i j k l m n o p q r s t u v w x Q z AB YB CB JB EB FB GB HB DB BB y T S LB MB NB OB PB QB IB SB M V N WB KB TB uB aB bB","2":"G W"},E:{"1":"W I F E D A B C O P eB fB gB hB VB R U lB mB","2":"G cB UB"},F:{"1":"0 1 2 3 4 5 6 7 8 9 D B C H J K L X Y Z a b c d e f g h i j k l m n o p q r s t u v w x Q z AB CB EB FB GB HB DB BB y T S nB oB pB qB R XB sB U"},G:{"388":"E UB TC ZB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC"},H:{"2":"CC"},I:{"2":"RB DC EC FC","388":"G N GC ZB HC IC"},J:{"2":"F","388":"A"},K:{"1":"A B C R XB U","388":"Q"},L:{"388":"TB"},M:{"641":"M"},N:{"388":"A B"},O:{"388":"JC"},P:{"388":"G KC LC MC NC OC VB PC QC"},Q:{"388":"RC"},R:{"388":"SC"},S:{"513":"dB"}},B:1,C:"Number input type"};
