<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>vue_zujian_XtoX</title>
    <style>
     .name{
        color:red;
      }
    </style>
</head>

<body>
    <div id="app">
      <msg></msg>
      <bmsg></bmsg>
    </div>
    <script src="https://cdn.staticfile.org/vue/2.2.2/vue.min.js"></script>
    <script>
        const bus = new Vue()

        Vue.component('msg',{
            template:`
                <div class="name" @click="send">子组件1：{{zmsg}}</div>
            `,
            data() {
               return {
                zmsg: '🐂'
               }
           },
            methods:{
                send(){
                    bus.$emit('busmsg','子组件1发送的信息==> 🐂')
                }
            }
        })

        Vue.component('bmsg',{
            template:`
                <div class="name" >子组件2：{{zmsg}}</div>
            `,
            data() {
               return {
                zmsg: '📕'
               }
           },
            created(){
                bus.$on('busmsg', res=>{
                    console.log('子组件2：',res)
                    this.zmsg = res
                })
            }
        })


        const vm = new Vue({
           el:'#app',
           data() {
               return {
                  name: '張三'
               }
           },
           methods:{
               clickName() {
                  console.log('name :>> ', this.name);
               }
            ,
           }
        });
    </script>
</body>

</html>