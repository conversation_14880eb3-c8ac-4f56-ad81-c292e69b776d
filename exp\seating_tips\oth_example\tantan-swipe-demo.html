<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TanTan_Swipe_Demo</title>
    <!-- Bootstrap 5.3.0 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .card-container {
            position: relative;
            width: 90%;
            max-width: 400px;
            height: 550px;
            margin: 20px auto;
        }

        .profile-card {
            position: absolute;
            width: 100%;
            height: 100%;
            background-color: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .profile-card .profile-img {
            width: 100%;
            height: 80%;
            object-fit: cover;
        }

        .profile-info {
            padding: 15px;
        }

        .profile-name {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .profile-bio {
            color: #666;
            font-size: 16px;
        }

        .swipe-indicators {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            display: flex;
            justify-content: center;
            gap: 8px;
        }

        .swipe-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.5);
        }

        .swipe-indicator.active {
            background-color: white;
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
        }

        .action-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-dislike {
            background-color: white;
            color: #ff5a5f;
        }

        .btn-like {
            background-color: white;
            color: #00b489;
        }

        .btn-superlike {
            background-color: white;
            color: #007AFF;
        }

        .swipe-notification {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-30deg);
            font-size: 40px;
            font-weight: bold;
            padding: 10px 20px;
            border: 4px solid;
            border-radius: 8px;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }

        .swipe-notification.dislike {
            color: #ff5a5f;
            border-color: #ff5a5f;
        }

        .swipe-notification.like {
            color: #00b489;
            border-color: #00b489;
        }

        .match-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .match-content {
            background-color: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            width: 90%;
            max-width: 400px;
        }

        .match-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            margin: 0 auto 20px;
            border: 4px solid #00b489;
        }

        .match-text {
            font-size: 22px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .match-subtext {
            color: #666;
            margin-bottom: 20px;
        }

        .match-buttons {
            display: flex;
            gap: 15px;
        }

        .match-btn {
            flex: 1;
            padding: 10px;
            border-radius: 30px;
            border: none;
            font-weight: 600;
            cursor: pointer;
        }

        .match-btn.chat {
            background-color: #00b489;
            color: white;
        }

        .match-btn.keep-swiping {
            background-color: white;
            color: #00b489;
            border: 2px solid #00b489;
        }
    </style>
</head>

<body>
    <div class="container mt-4 mb-5">

        <div class="card-container" id="cardContainer">
            <!-- 卡片将通过JS动态生成 -->
        </div>

        <div class="action-buttons">
            <button class="action-btn btn-dislike" id="dislikeBtn">
                <i class="fas fa-times fa-lg"></i>
            </button>
            <!-- <button class="action-btn btn-superlike" id="superlikeBtn">
                <i class="fas fa-star fa-lg"></i>
            </button> -->
            <button class="action-btn btn-like" id="likeBtn">
                <i class="fas fa-heart fa-lg"></i>
            </button>
        </div>
    </div>

    <!-- 匹配成功弹窗 -->
    <div class="match-modal" id="matchModal">
        <div class="match-content">
            <h2 class="text-success"><i class="fas fa-heart"></i> 匹配成功！</h2>
            <img src="" alt="匹配用户头像" class="match-avatar" id="matchAvatar">
            <p class="match-text" id="matchName"></p>
            <p class="match-subtext">你们彼此喜欢，可以开始聊天了！</p>
            <div class="match-buttons">
                <button class="match-btn chat">开始聊天</button>
                <button class="match-btn keep-swiping" id="keepSwipingBtn">继续滑动</button>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5.3.0 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 模拟用户数据
        const profiles = [
            {
                id: 1,
                name: "李梦，26",
                bio: "喜欢旅行和摄影，寻找有趣的灵魂",
                image: "https://picsum.photos/id/64/600/800",
                match: true
            },
            {
                id: 2,
                name: "王浩，28",
                bio: "程序员，热爱健身和美食",
                image: "https://picsum.photos/id/91/600/800",
                match: false
            },
            {
                id: 3,
                name: "张婷，24",
                bio: "音乐老师，喜欢小动物和看电影",
                image: "https://picsum.photos/id/26/600/800",
                match: true
            },
            {
                id: 4,
                name: "刘阳，30",
                bio: "设计师，咖啡爱好者，喜欢徒步",
                image: "https://picsum.photos/id/177/600/800",
                match: false
            },
            {
                id: 5,
                name: "陈雪，25",
                bio: "瑜伽教练，热爱生活，喜欢尝试新事物",
                image: "https://picsum.photos/id/65/600/800",
                match: true
            },
            {
                id: 6,
                name: "李梦，26",
                bio: "喜欢旅行和摄影，寻找有趣的灵魂",
                image: "https://picsum.photos/id/64/600/800",
                match: true
            },
            {
                id: 7,
                name: "王浩，28",
                bio: "程序员，热爱健身和美食",
                image: "https://picsum.photos/id/91/600/800",
                match: false
            },
            {
                id: 8,
                name: "张婷，24",
                bio: "音乐老师，喜欢小动物和看电影",
                image: "https://picsum.photos/id/26/600/800",
                match: true
            },
            {
                id: 9,
                name: "刘阳，30",
                bio: "设计师，咖啡爱好者，喜欢徒步",
                image: "https://picsum.photos/id/177/600/800",
                match: false
            },
            {
                id: 10,
                name: "陈雪，25",
                bio: "瑜伽教练，热爱生活，喜欢尝试新事物",
                image: "https://picsum.photos/id/65/600/800",
                match: true
            },
            {
                id: 11,
                name: "李梦，26",
                bio: "喜欢旅行和摄影，寻找有趣的灵魂",
                image: "https://picsum.photos/id/64/600/800",
                match: true
            },
            {
                id: 12,
                name: "王浩，28",
                bio: "程序员，热爱健身和美食",
                image: "https://picsum.photos/id/91/600/800",
                match: false
            },
            {
                id: 13,
                name: "张婷，24",
                bio: "音乐老师，喜欢小动物和看电影",
                image: "https://picsum.photos/id/26/600/800",
                match: true
            },
            {
                id: 14,
                name: "刘阳，30",
                bio: "设计师，咖啡爱好者，喜欢徒步",
                image: "https://picsum.photos/id/177/600/800",
                match: false
            },
            {
                id: 15,
                name: "陈雪，25",
                bio: "瑜伽教练，热爱生活，喜欢尝试新事物",
                image: "https://picsum.photos/id/65/600/800",
                match: true
            },
        ];

        let currentIndex = 0;
        const cardContainer = document.getElementById('cardContainer');
        const dislikeBtn = document.getElementById('dislikeBtn');
        const likeBtn = document.getElementById('likeBtn');
        // const superlikeBtn = document.getElementById('superlikeBtn');
        const matchModal = document.getElementById('matchModal');
        const keepSwipingBtn = document.getElementById('keepSwipingBtn');
        const matchAvatar = document.getElementById('matchAvatar');
        const matchName = document.getElementById('matchName');

        // 初始化卡片
        function initCards() {
            // 清空容器
            cardContainer.innerHTML = '';

            // 创建当前卡片和下一张卡片（用于堆叠效果）
            for (let i = 0; i < 2 && currentIndex + i < profiles.length; i++) {
                createCard(profiles[currentIndex + i], i);
            }

            // 更新指示器
            updateIndicators();
        }

        // 创建卡片
        function createCard(profile, index) {
            const card = document.createElement('div');
            card.className = 'profile-card';
            card.dataset.id = profile.id;

            // 堆叠效果 - 后面的卡片稍微小一点并靠后
            if (index > 0) {
                card.style.transform = 'scale(0.95) translateY(10px)';
                card.style.opacity = '0.7';
                card.style.zIndex = 10 - index;
            } else {
                card.style.zIndex = 10;
            }

            card.innerHTML = `
                <img src="${profile.image}" alt="${profile.name}的照片" class="profile-img">
                <div class="swipe-notification dislike">不喜欢</div>
                <div class="swipe-notification like">喜欢</div>
                <div class="profile-info">
                    <div class="profile-name">${profile.name}</div>
                    <div class="profile-bio">${profile.bio}</div>
                </div>
            `;

            cardContainer.appendChild(card);

            // 如果是当前卡片（第一张），添加拖拽事件
            if (index === 0) {
                addSwipeEvents(card, profile);
            }
        }

        // 更新指示器
        function updateIndicators() {
            // 先移除旧的指示器
            const oldIndicators = document.querySelector('.swipe-indicators');
            if (oldIndicators) {
                oldIndicators.remove();
            }

            // 创建新的指示器容器
            const indicators = document.createElement('div');
            indicators.className = 'swipe-indicators';

            // 添加指示器
            profiles.forEach((_, i) => {
                const indicator = document.createElement('div');
                indicator.className = `swipe-indicator ${i === currentIndex ? 'active' : ''}`;
                indicators.appendChild(indicator);
            });

            // 添加到第一个卡片
            const firstCard = document.querySelector('.profile-card');
            if (firstCard) {
                firstCard.appendChild(indicators);
            }
        }

        // 添加滑动事件
        function addSwipeEvents(card, profile) {
            let startX, startY;
            let currentX = 0;
            let currentY = 0;
            let isDragging = false;
            const threshold = 100; // 滑动阈值

            // 鼠标/触摸开始
            function startDrag(e) {
                isDragging = true;
                // 处理鼠标和触摸事件
                if (e.type === 'mousedown') {
                    startX = e.clientX;
                    startY = e.clientY;
                } else {
                    startX = e.touches[0].clientX;
                    startY = e.touches[0].clientY;
                }
                card.style.transition = 'none';
            }

            // 鼠标/触摸移动
            function drag(e) {
                if (!isDragging) return;

                let clientX, clientY;
                if (e.type === 'mousemove') {
                    clientX = e.clientX;
                    clientY = e.clientY;
                } else {
                    clientX = e.touches[0].clientX;
                    clientY = e.touches[0].clientY;
                }

                currentX = clientX - startX;
                currentY = clientY - startY;

                // 计算旋转角度 (最大15度)
                const rotate = (currentX / card.offsetWidth) * 15;

                // 应用变换
                card.style.transform = `translateX(${currentX}px) translateY(${currentY}px) rotate(${rotate}deg)`;

                // 显示滑动提示
                const dislikeNotice = card.querySelector('.swipe-notification.dislike');
                const likeNotice = card.querySelector('.swipe-notification.like');

                if (currentX > 50) {
                    // 向右滑动 - 喜欢
                    likeNotice.style.opacity = Math.min(currentX / threshold, 1);
                    dislikeNotice.style.opacity = 0;
                } else if (currentX < -50) {
                    // 向左滑动 - 不喜欢
                    dislikeNotice.style.opacity = Math.min(Math.abs(currentX) / threshold, 1);
                    likeNotice.style.opacity = 0;
                } else {
                    // 在中间，不显示提示
                    dislikeNotice.style.opacity = 0;
                    likeNotice.style.opacity = 0;
                }
            }

            // 鼠标/触摸结束
            function endDrag() {
                if (!isDragging) return;
                isDragging = false;

                card.style.transition = 'all 0.3s ease';

                // 判断是否超过阈值
                if (currentX > threshold) {
                    // 向右滑动 - 喜欢
                    swipeRight(card, profile);
                } else if (currentX < -threshold) {
                    // 向左滑动 - 不喜欢
                    swipeLeft(card);
                } else {
                    // 未超过阈值，回到原位
                    card.style.transform = 'translateX(0) translateY(0) rotate(0)';
                    // 隐藏提示
                    card.querySelector('.swipe-notification.dislike').style.opacity = 0;
                    card.querySelector('.swipe-notification.like').style.opacity = 0;
                }
            }

            // 绑定事件
            card.addEventListener('mousedown', startDrag);
            card.addEventListener('mousemove', drag);
            card.addEventListener('mouseup', endDrag);
            card.addEventListener('mouseleave', endDrag);

            // 触摸事件
            card.addEventListener('touchstart', startDrag);
            card.addEventListener('touchmove', drag);
            card.addEventListener('touchend', endDrag);
        }

        // 向左滑动（不喜欢）
        function swipeLeft(card) {
            card.style.transform = `translateX(-100%) rotate(-30deg)`;
            card.querySelector('.swipe-notification.dislike').style.opacity = 1;

            // 动画结束后移除卡片并加载新卡片
            setTimeout(() => {
                card.remove();
                currentIndex++;
                if (currentIndex < profiles.length) {
                    initCards();
                } else {
                    currentIndex = 0;
                    initCards();
                    // 没有更多卡片
                    // cardContainer.innerHTML = '<div class="text-center mt-5"><p class="text-muted">没有更多用户了</p></div>';
                }
            }, 300);
        }

        // 向右滑动（喜欢）
        function swipeRight(card, profile) {
            card.style.transform = `translateX(100%) rotate(30deg)`;
            card.querySelector('.swipe-notification.like').style.opacity = 1;

            // 动画结束后移除卡片并加载新卡片
            setTimeout(() => {
                card.remove();
                currentIndex++;

                // 检查是否匹配
                // if (profile.match) {
                //     showMatchModal(profile);
                // } else {
                if (currentIndex < profiles.length) {
                    initCards();
                } else {
                    currentIndex = 0;
                    initCards();
                    // 没有更多卡片
                    // cardContainer.innerHTML = '<div class="text-center mt-5"><p class="text-muted">没有更多用户了</p></div>';
                }
                // }
            }, 300);
        }

        // 显示匹配成功弹窗
        function showMatchModal(profile) {
            matchAvatar.src = profile.image;
            matchAvatar.alt = profile.name;
            matchName.textContent = `你和${profile.name.split('，')[0]}匹配成功！`;
            matchModal.style.display = 'flex';
        }

        // 按钮事件
        dislikeBtn.addEventListener('click', () => {
            const currentCard = document.querySelector('.profile-card');
            if (currentCard) {
                swipeLeft(currentCard);
            }
        });

        likeBtn.addEventListener('click', () => {
            const currentCard = document.querySelector('.profile-card');
            if (currentCard) {
                const profileId = parseInt(currentCard.dataset.id);
                const profile = profiles.find(p => p.id === profileId);
                swipeRight(currentCard, profile);
            }
        });

        // superlikeBtn.addEventListener('click', () => {
        //     alert('超级喜欢功能 - 在实际应用中可以有特殊效果');
        // });

        // 继续滑动按钮
        keepSwipingBtn.addEventListener('click', () => {
            matchModal.style.display = 'none';
            if (currentIndex < profiles.length) {
                initCards();
            } else {
                currentIndex = 0;
                initCards();
                // cardContainer.innerHTML = '<div class="text-center mt-5"><p class="text-muted">没有更多用户了</p></div>';
            }
        });

        // 初始化
        initCards();
    </script>
</body>

</html>