{"version": 3, "file": "groupBy.js", "sources": ["../../src/internal/operators/groupBy.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,4CAA2C;AAC3C,gDAA+C;AAC/C,4CAA2C;AAE3C,sCAAqC;AAoGrC,SAAgB,OAAO,CAAU,WAA4B,EAC5B,eAA0C,EAC1C,gBAAwE,EACxE,eAAkC;IACjE,OAAO,UAAC,MAAqB;QAC3B,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,WAAW,EAAE,eAAe,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC;IAAjG,CAAiG,CAAC;AACtG,CAAC;AAND,0BAMC;AASD;IACE,yBAAoB,WAA4B,EAC5B,eAA0C,EAC1C,gBAAwE,EACxE,eAAkC;QAHlC,gBAAW,GAAX,WAAW,CAAiB;QAC5B,oBAAe,GAAf,eAAe,CAA2B;QAC1C,qBAAgB,GAAhB,gBAAgB,CAAwD;QACxE,oBAAe,GAAf,eAAe,CAAmB;IACtD,CAAC;IAED,8BAAI,GAAJ,UAAK,UAA+C,EAAE,MAAW;QAC/D,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,iBAAiB,CAC3C,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAChG,CAAC,CAAC;IACL,CAAC;IACH,sBAAC;AAAD,CAAC,AAZD,IAYC;AAOD;IAAyC,qCAAa;IAKpD,2BAAY,WAAgD,EACxC,WAA4B,EAC5B,eAA0C,EAC1C,gBAAwE,EACxE,eAAkC;QAJtD,YAKE,kBAAM,WAAW,CAAC,SACnB;QALmB,iBAAW,GAAX,WAAW,CAAiB;QAC5B,qBAAe,GAAf,eAAe,CAA2B;QAC1C,sBAAgB,GAAhB,gBAAgB,CAAwD;QACxE,qBAAe,GAAf,eAAe,CAAmB;QAR9C,YAAM,GAA2B,IAAI,CAAC;QACvC,4BAAsB,GAAY,KAAK,CAAC;QACxC,WAAK,GAAW,CAAC,CAAC;;IAQzB,CAAC;IAES,iCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,GAAM,CAAC;QACX,IAAI;YACF,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAC/B;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAChB,OAAO;SACR;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC1B,CAAC;IAEO,kCAAM,GAAd,UAAe,KAAQ,EAAE,GAAM;QAC7B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAEzB,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,EAAqB,CAAC;SACrD;QAED,IAAI,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE5B,IAAI,OAAU,CAAC;QACf,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI;gBACF,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;aACvC;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aACjB;SACF;aAAM;YACL,OAAO,GAAQ,KAAK,CAAC;SACtB;QAED,IAAI,CAAC,KAAK,EAAE;YACV,KAAK,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,IAAI,iBAAO,EAAK,CAAmB,CAAC;YAC7F,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACvB,IAAM,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAClE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACzC,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACzB,IAAI,QAAQ,SAAK,CAAC;gBAClB,IAAI;oBACF,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,iBAAiB,CAAO,GAAG,EAAc,KAAK,CAAC,CAAC,CAAC;iBACvF;gBAAC,OAAO,GAAG,EAAE;oBACZ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAChB,OAAO;iBACR;gBACD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,uBAAuB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;aAC7E;SACF;QAED,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACjB,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACrB;IACH,CAAC;IAES,kCAAM,GAAhB,UAAiB,GAAQ;QACvB,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,GAAG;gBACxB,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,EAAE,CAAC;SAChB;QACD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAES,qCAAS,GAAnB;QACE,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,GAAG;gBACxB,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,EAAE,CAAC;SAChB;QACD,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;IAED,uCAAW,GAAX,UAAY,GAAM;QAChB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED,uCAAW,GAAX;QACE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;YACnC,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE;gBACpB,iBAAM,WAAW,WAAE,CAAC;aACrB;SACF;IACH,CAAC;IACH,wBAAC;AAAD,CAAC,AAvGD,CAAyC,uBAAU,GAuGlD;AAOD;IAA4C,2CAAa;IACvD,iCAAoB,GAAM,EACN,KAAiB,EACjB,MAA0C;QAF9D,YAGE,kBAAM,KAAK,CAAC,SACb;QAJmB,SAAG,GAAH,GAAG,CAAG;QACN,WAAK,GAAL,KAAK,CAAY;QACjB,YAAM,GAAN,MAAM,CAAoC;;IAE9D,CAAC;IAES,uCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAGD,8CAAY,GAAZ;QACQ,IAAA,SAAsB,EAApB,kBAAM,EAAE,YAAG,CAAU;QAC7B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QAC9B,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;SACzB;IACH,CAAC;IACH,8BAAC;AAAD,CAAC,AAnBD,CAA4C,uBAAU,GAmBrD;AAUD;IAA6C,qCAAa;IAExD,2BAAmB,GAAM,EACL,YAAwB,EACxB,oBAA2C;QAF/D,YAGE,iBAAO,SACR;QAJkB,SAAG,GAAH,GAAG,CAAG;QACL,kBAAY,GAAZ,YAAY,CAAY;QACxB,0BAAoB,GAApB,oBAAoB,CAAuB;;IAE/D,CAAC;IAGD,sCAAU,GAAV,UAAW,UAAyB;QAClC,IAAM,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;QAClC,IAAA,SAA6C,EAA3C,8CAAoB,EAAE,8BAAY,CAAU;QACpD,IAAI,oBAAoB,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE;YACxD,YAAY,CAAC,GAAG,CAAC,IAAI,yBAAyB,CAAC,oBAAoB,CAAC,CAAC,CAAC;SACvE;QACD,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;QACrD,OAAO,YAAY,CAAC;IACtB,CAAC;IACH,wBAAC;AAAD,CAAC,AAlBD,CAA6C,uBAAU,GAkBtD;AAlBY,8CAAiB;AAyB9B;IAAwC,6CAAY;IAClD,mCAAoB,MAA4B;QAAhD,YACE,iBAAO,SAER;QAHmB,YAAM,GAAN,MAAM,CAAsB;QAE9C,MAAM,CAAC,KAAK,EAAE,CAAC;;IACjB,CAAC;IAED,+CAAW,GAAX;QACE,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAClC,iBAAM,WAAW,WAAE,CAAC;YACpB,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC;YAClB,IAAI,MAAM,CAAC,KAAK,KAAK,CAAC,IAAI,MAAM,CAAC,sBAAsB,EAAE;gBACvD,MAAM,CAAC,WAAW,EAAE,CAAC;aACtB;SACF;IACH,CAAC;IACH,gCAAC;AAAD,CAAC,AAhBD,CAAwC,2BAAY,GAgBnD"}