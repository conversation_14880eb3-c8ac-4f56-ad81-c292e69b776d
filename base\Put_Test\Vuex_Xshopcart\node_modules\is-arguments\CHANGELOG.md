1.0.4 / 2018-11-05
==================
  * [Fix] Fix errors about `in` operator (#22)

1.0.3 / 2018-11-02
==================
  * [Fix] add awareness of Symbol.toStringTag (#20)
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`, `jscs`, `nsp`
  * [Tests] up to `node` `v11.1`, `v10.13`, `v9.11`, `v8.12`, `v7.10`, `v6.14`, `v5.11`, `v4.8`; use `nvm install-latest-npm`; pin included builds to LTS.

1.0.2 / 2015-09-21
==================
  * [Docs] Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG.
  * [Enhancement] In modern engines, only export the "is standard arguments" check.
  * [Fix] `toString` as a variable name breaks in some older browsers.
  * [<PERSON>] update `covert`, `jscs`, `eslint`
  * [Tests] up to `io.js` `v3.3`, `node` `v4.1`

1.0.1 / 2015-04-29
==================
  * [Docs] clean up README; add badges
  * [Dev Deps] update `tape`, `covert`
  * [Tests] add `npm run lint`

1.0.0 / 2014-01-14
==================
  * Bump to v1.0

0.1.0 / 2014-01-14
==================
  * Initial release.

