# lodash._reinterpolate v3.0.0

The [modern build](https://github.com/lodash/lodash/wiki/Build-Differences) of [lodash’s](https://lodash.com/) internal `reInterpolate` exported as a [Node.js](http://nodejs.org/)/[io.js](https://iojs.org/) module.

## Installation

Using npm:

```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash._reinterpolate
```

In Node.js/io.js:

```js
var reInterpolate = require('lodash._reinterpolate');
```

See the [package source](https://github.com/lodash/lodash/blob/3.0.0-npm-packages/lodash._reinterpolate) for more details.
