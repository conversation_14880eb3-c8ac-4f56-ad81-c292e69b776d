{"name": "deep-equal", "version": "1.1.1", "description": "node's assert.deepEqual algorithm", "main": "index.js", "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint .", "tests-only": "tape test/*", "test": "npm run tests-only", "posttest": "npx aud", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "dependencies": {"is-arguments": "^1.0.4", "is-date-object": "^1.0.1", "is-regex": "^1.0.4", "object-is": "^1.0.1", "object-keys": "^1.1.1", "regexp.prototype.flags": "^1.2.0"}, "devDependencies": {"@ljharb/eslint-config": "^15.0.1", "auto-changelog": "^1.16.2", "eslint": "^6.6.0", "safe-publish-latest": "^1.1.4", "tape": "^4.11.0"}, "repository": {"type": "git", "url": "http://github.com/substack/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}}