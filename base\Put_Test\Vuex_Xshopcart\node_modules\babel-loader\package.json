{"name": "babel-loader", "version": "8.1.0", "description": "babel module loader for webpack", "files": ["lib"], "main": "lib/index.js", "engines": {"node": ">= 6.9"}, "dependencies": {"find-cache-dir": "^2.1.0", "loader-utils": "^1.4.0", "mkdirp": "^0.5.3", "pify": "^4.0.1", "schema-utils": "^2.6.5"}, "peerDependencies": {"@babel/core": "^7.0.0", "webpack": ">=2"}, "devDependencies": {"@babel/cli": "^7.2.0", "@babel/core": "^7.2.0", "@babel/preset-env": "^7.2.0", "ava": "^2.4.0", "babel-eslint": "^10.0.1", "babel-plugin-istanbul": "^5.1.0", "babel-plugin-react-intl": "^4.1.19", "cross-env": "^6.0.0", "eslint": "^6.5.1", "eslint-config-babel": "^9.0.0", "eslint-config-prettier": "^6.3.0", "eslint-plugin-flowtype": "^4.3.0", "eslint-plugin-prettier": "^3.0.0", "husky": "^3.0.7", "lint-staged": "^9.4.1", "nyc": "^14.1.1", "prettier": "^1.15.3", "react": "^16.0.0", "react-intl": "^3.3.2", "react-intl-webpack-plugin": "^0.3.0", "rimraf": "^3.0.0", "webpack": "^4.0.0"}, "scripts": {"clean": "rimraf lib/", "build": "babel src/ --out-dir lib/ --copy-files", "format": "prettier --write --trailing-comma all 'src/**/*.js' 'test/**/*.test.js' 'test/helpers/*.js' && prettier --write --trailing-comma es5 'scripts/*.js'", "lint": "eslint src test", "precommit": "lint-staged", "prepublish": "yarn run clean && yarn run build", "preversion": "yarn run test", "test": "yarn run lint && cross-env BABEL_ENV=test yarn run build && yarn run test-only", "test-only": "nyc ava"}, "repository": {"type": "git", "url": "https://github.com/babel/babel-loader.git"}, "keywords": ["webpack", "loader", "babel", "es6", "transpiler", "module"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/babel/babel-loader/issues"}, "homepage": "https://github.com/babel/babel-loader", "nyc": {"all": true, "include": ["src/**/*.js"], "reporter": ["text", "json"], "sourceMap": false, "instrument": false}, "ava": {"files": ["test/**/*.test.js", "!test/fixtures/**/*", "!test/helpers/**/*"], "helpers": ["**/helpers/**/*"], "sources": ["src/**/*.js"]}, "lint-staged": {"scripts/*.js": ["prettier --trailing-comma es5 --write", "git add"], "src/**/*.js": ["prettier --trailing-comma all --write", "git add"], "test/**/*.test.js": ["prettier --trailing-comma all --write", "git add"], "test/helpers/*.js": ["prettier --trailing-comma all --write", "git add"], "package.json": ["node ./scripts/yarn-install.js", "git add yarn.lock"]}}