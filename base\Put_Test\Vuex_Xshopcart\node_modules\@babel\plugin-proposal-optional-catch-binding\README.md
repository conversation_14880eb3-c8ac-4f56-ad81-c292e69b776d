# @babel/plugin-proposal-optional-catch-binding

> Compile optional catch bindings

See our website [@babel/plugin-proposal-optional-catch-binding](https://babeljs.io/docs/en/next/babel-plugin-proposal-optional-catch-binding.html) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-proposal-optional-catch-binding
```

or using yarn:

```sh
yarn add @babel/plugin-proposal-optional-catch-binding --dev
```
