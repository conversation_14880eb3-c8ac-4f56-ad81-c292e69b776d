{"name": "register-service-worker", "sideEffects": false, "version": "1.7.1", "description": "<PERSON><PERSON>t for registering service worker, with hooks", "main": "index.js", "scripts": {"build": "node scripts/build.js", "prepare": "npm run build", "prepublishOnly": "conventional-changelog -p angular -r 2 -i CHANGELOG.md -s"}, "repository": {"type": "git", "url": "git+https://github.com/yyx990803/register-service-worker.git"}, "keywords": ["service-worker"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/yyx990803/register-service-worker/issues"}, "homepage": "https://github.com/yyx990803/register-service-worker#readme", "devDependencies": {"buble": "^0.18.0", "conventional-changelog-cli": "^2.0.1"}}