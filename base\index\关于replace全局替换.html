<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <style>
     .name{
        color:red;
      }
    </style>
</head>

<body>
    <div id="app">
       <div class="name" @click="clickName">{{name}}</div>
    </div>
    <script src="https://cdn.staticfile.org/vue/2.2.2/vue.min.js"></script>
    <script>
        const vm = new Vue({
           el:'#app',
           data() {
               return {
                  name: '張三'
               }
           },
           methods:{
               clickName() {
                  console.log('name :>> ', this.name);
               }
            ,
           }
        });
    </script>
    <script>
        var str = "abcabcabcabc"
        console.log("%c [ str1 ]", "font-size:13px; background:#00ffff; color:red;", str)
        // var newStr = str.replace(/a/g,"f")
        // console.log("%c [ Str2 ]", "font-size:13px; background:#00ffff; color:red;", newStr)
        var str2 = str.split("b").join("f")
        console.log("%c [ str2 ]", "font-size:13px; background:#00ffff; color:red;", str2)

        var reg = 11 && /^((13|14|15|16|17|18|19)[0-9]{1}\d{8})$/ 
        var phone = '13355303221'
        var phoneTest = reg.test(phone)
        console.log("%c [ phoneTest ]", "font-size:13px; background:#00ffff; color:red;", phoneTest)
    </script>
</body>

</html>