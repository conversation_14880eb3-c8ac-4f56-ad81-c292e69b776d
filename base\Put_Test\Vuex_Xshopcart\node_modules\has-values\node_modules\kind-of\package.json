{"name": "kind-of", "description": "Get the native type of a value.", "version": "4.0.0", "homepage": "https://github.com/jonschlinkert/kind-of", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON> (https://dtothefp.github.io/me)", "<PERSON> (http://twitter.com/jonschlink<PERSON>)", "<PERSON> (kensheedlo.com)", "laggingreflex (https://github.com/laggingreflex)", "<PERSON> (https://miguelmota.com)", "<PERSON> (http://about.me/peterdehaan)"], "repository": "jonschlinkert/kind-of", "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "dependencies": {"is-buffer": "^1.1.5"}, "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^1.1.1", "browserify": "^14.3.0", "glob": "^7.1.1", "gulp-format-md": "^0.1.12", "mocha": "^3.4.1", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "verb": {"related": {"list": ["is-glob", "is-number", "is-primitive"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}}