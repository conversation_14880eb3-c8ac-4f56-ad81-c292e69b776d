{"name": "faye-websocket", "description": "Standards-compliant WebSocket server and client", "homepage": "http://github.com/faye/faye-websocket-node", "author": "<PERSON> <<EMAIL>> (http://jcoglan.com/)", "keywords": ["websocket", "eventsource"], "license": "MIT", "version": "0.10.0", "engines": {"node": ">=0.4.0"}, "main": "./lib/faye/websocket", "dependencies": {"websocket-driver": ">=0.5.1"}, "devDependencies": {"jstest": "", "pace": "", "permessage-deflate": ""}, "scripts": {"test": "jstest spec/runner.js"}, "repository": {"type": "git", "url": "git://github.com/faye/faye-websocket-node.git"}, "bugs": "http://github.com/faye/faye-websocket-node/issues"}