# query-string [![Build Status](https://travis-ci.org/sindresorhus/query-string.svg?branch=master)](https://travis-ci.org/sindresorhus/query-string)

> Parse and stringify URL [query strings](http://en.wikipedia.org/wiki/Query_string)

---

<p align="center"><b>🔥 Want to strengthen your core JavaScript skills and master ES6?</b><br>I would personally recommend this awesome <a href="https://ES6.io/friend/AWESOME">ES6 course</a> by <PERSON>. You might also like his <a href="https://ReactForBeginners.com/friend/AWESOME">React course</a>.</p>

---


## Install

```
$ npm install --save query-string
```


## Usage

```js
const queryString = require('query-string');

console.log(location.search);
//=> '?foo=bar'

const parsed = queryString.parse(location.search);
console.log(parsed);
//=> {foo: 'bar'}

console.log(location.hash);
//=> '#token=bada55cafe'

const parsedHash = queryString.parse(location.hash);
console.log(parsedHash);
//=> {token: 'bada55cafe'}

parsed.foo = 'unicorn';
parsed.ilike = 'pizza';

const stringified = queryString.stringify(parsed);
//=> 'foo=unicorn&ilike=pizza'

location.search = stringified;
// note that `location.search` automatically prepends a question mark
console.log(location.search);
//=> '?foo=unicorn&ilike=pizza'
```


## API

### .parse(*string*, *[options]*)

Parse a query string into an object. Leading `?` or `#` are ignored, so you can pass `location.search` or `location.hash` directly.

The returned object is created with [`Object.create(null)`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/create) and thus does not have a `prototype`.

#### arrayFormat

Type: `string`<br>
Default: `'none'`

Supports both `index` for an indexed array representation or `bracket` for a *bracketed* array representation.

- `bracket`: stands for parsing correctly arrays with bracket representation on the query string, such as:

```js
queryString.parse('foo[]=1&foo[]=2&foo[]=3', {arrayFormat: 'bracket'});
//=> foo: [1,2,3]
```

- `index`: stands for parsing taking the index into account, such as:

```js
queryString.parse('foo[0]=1&foo[1]=2&foo[3]=3', {arrayFormat: 'index'});
//=> foo: [1,2,3]
```

- `none`: is the **default** option and removes any bracket representation, such as:

```js
queryString.parse('foo=1&foo=2&foo=3');
//=> foo: [1,2,3]
```

### .stringify(*object*, *[options]*)

Stringify an object into a query string, sorting the keys.

#### strict

Type: `boolean`<br>
Default: `true`

Strictly encode URI components with [strict-uri-encode](https://github.com/kevva/strict-uri-encode). It uses [encodeURIComponent](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/encodeURIComponent)
if set to false. You probably [don't care](https://github.com/sindresorhus/query-string/issues/42) about this option.

#### encode

Type: `boolean`<br>
Default: `true`

[URL encode](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/encodeURIComponent) the keys and values.

#### arrayFormat

Type: `string`<br>
Default: `'none'`

Supports both `index` for an indexed array representation or `bracket` for a *bracketed* array representation.

- `bracket`: stands for parsing correctly arrays with bracket representation on the query string, such as:

```js
queryString.stringify({foo: [1,2,3]}, {arrayFormat: 'bracket'});
// => foo[]=1&foo[]=2&foo[]=3
```

- `index`: stands for parsing taking the index into account, such as:

```js
queryString.stringify({foo: [1,2,3]}, {arrayFormat: 'index'});
// => foo[0]=1&foo[1]=2&foo[3]=3
```

- `none`: is the __default__ option and removes any bracket representation, such as:

```js
queryString.stringify({foo: [1,2,3]});
// => foo=1&foo=2&foo=3
```

### .extract(*string*)

Extract a query string from a URL that can be passed into `.parse()`.


## Nesting

This module intentionally doesn't support nesting as it's not spec'd and varies between implementations, which causes a lot of [edge cases](https://github.com/visionmedia/node-querystring/issues).

You're much better off just converting the object to a JSON string:

```js
queryString.stringify({
	foo: 'bar',
	nested: JSON.stringify({
		unicorn: 'cake'
	})
});
//=> 'foo=bar&nested=%7B%22unicorn%22%3A%22cake%22%7D'
```

However, there is support for multiple instances of the same key:

```js
queryString.parse('likes=cake&name=bob&likes=icecream');
//=> {likes: ['cake', 'icecream'], name: 'bob'}

queryString.stringify({color: ['taupe', 'chartreuse'], id: '515'});
//=> 'color=chartreuse&color=taupe&id=515'
```


## Falsy values

Sometimes you want to unset a key, or maybe just make it present without assigning a value to it. Here is how falsy values are stringified:

```js
queryString.stringify({foo: false});
//=> 'foo=false'

queryString.stringify({foo: null});
//=> 'foo'

queryString.stringify({foo: undefined});
//=> ''
```


## License

MIT © [Sindre Sorhus](https://sindresorhus.com)
