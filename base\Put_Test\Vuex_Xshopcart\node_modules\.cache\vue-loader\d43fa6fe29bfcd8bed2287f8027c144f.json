{"remainingRequest": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\components\\HelloWorld.vue?vue&type=style&index=0&id=469af010&scoped=true&lang=css&", "dependencies": [{"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\src\\components\\HelloWorld.vue", "mtime": 1658035355533}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1657986309260}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1657986331183}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1657986317252}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1657986293299}, {"path": "D:\\No.1 folder\\No.2 folder\\dev\\dev2\\vue\\base\\Vuex_Xshopcart\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1657986322488}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKYm9keSwKbGksCnVsLApwIHsKICBsaXN0LXN0eWxlOiBub25lOwogIHBhZGRpbmc6IDA7CiAgbWFyZ2luOiAwOwp9CgouaGVsbG8gewogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICB0b3A6IDA7CiAgYm90dG9tOiAwOwogIGxlZnQ6IDA7CiAgcmlnaHQ6IDA7Cn0KCi5zaG9wX2NvbnRhaW5lciB7CiAgd2lkdGg6IDEwMCU7Cn0KCi5zaG9wX2NvbnRhaW5lcl9saSB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTsKICBoZWlnaHQ6IDEzMHB4OwogIG1hcmdpbi1ib3R0b206IDE1cHg7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKfQoKLnNob3BfaW1nIHsKICBmbGV4OiAxOwogIHBhZGRpbmc6IDEwcHg7CiAgaGVpZ2h0OiAxMTBweDsKfQoKLnNob3BfZGV0YWlsIHsKICBmbGV4OiAyOwp9Cgouc2hvcF9kZXRhaWwgcCB7CiAgZm9udC1zaXplOiAxNHB4OwogIGxpbmUtaGVpZ2h0OiAyNXB4OwogIGhlaWdodDogMjVweDsKfQoKLnNob3BfcmVkdWNlLAouc2hvcF9hZGQgewogIGZvbnQtc2l6ZTogMThweDsKICBmb250LXdlaWdodDogNzAwOwogIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgd2lkdGg6IDIwcHg7CiAgaGVpZ2h0OiAyMHB4OwogIGJvcmRlcjogMXB4IHNvbGlkICNmNWY1ZjU7CiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjsKfQoKLnNob3BfbnVtIHsKICBtYXJnaW46IDAgNXB4Owp9CgouZm9vdCB7CiAgcG9zaXRpb246IGZpeGVkOwogIGJvdHRvbTogMDsKICBsZWZ0OiAwOwogIHdpZHRoOiAxMDAlOwogIGhlaWdodDogNDBweDsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwp9CgoudG90YWxfcHJpY2UgewogIGJhY2tncm91bmQtY29sb3I6ICM0Y2Q5NjQ7CiAgZmxleDogMjsKICBoZWlnaHQ6IDEwMCU7CiAgbGluZS1oZWlnaHQ6IDQwcHg7Cn0KCi50b3RhbF9udW0gewogIGZsZXg6IDE7CiAgYmFja2dyb3VuZC1jb2xvcjogIzY2NjsKICBoZWlnaHQ6IDEwMCU7CiAgbGluZS1oZWlnaHQ6IDQwcHg7Cn0KCi5wYXltZW50IHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjM2I5NWU5Owp9Cg=="}, {"version": 3, "sources": ["HelloWorld.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "HelloWorld.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\n  <div class=\"hello\">\n    <ul class=\"shop_container\">\n      <li class=\"shop_container_li\" v-for=\"(item, index) in goodsObj\" :key=\"item.index\">\n        <div class=\"shop_img\">\n          <img width=\"100%\" height=\"100%\" :src=\"item.img\" />\n        </div>\n        <div class=\"shop_detail\">\n          <p>{{ item.name }}</p>\n          <p>{{ item.hint }}</p>\n          <p>￥{{ item.price }}</p>\n          <p>\n            <span class=\"shop_reduce\" @click=\"handleReduce({ index })\">-</span>\n            <span class=\"shop_num\">{{ item.num }}</span>\n            <span class=\"shop_add\" @click=\"handleAdd({ index })\">+</span>\n          </p>\n        </div>\n      </li>\n    </ul>\n    <div class=\"foot\">\n      <div class=\"total_price\">\n        <span>合计：{{ totalNum }}</span>\n      </div>\n      <div class=\"total_num\">\n        <span>去结账：￥{{ totalPrice }}</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  mapState,\n  mapGetters,\n  // mapMutations,\n  // mapActions,\n} from \"vuex\";\nexport default {\n  name: \"HelloWorld\",\n  data() {\n    return {};\n  },\n  created() {\n    // console.log(this.$store.getters.getById(1));\n    // 根据id获取商品值\n    if(this.$store.state.goods){\n      console.log(this.$store.state.goods.filter((item) => item.id - 0 === 1));\n    }\n    console.log('this.$store.state.cart :>> ', this.$store.state.cart );\n  },\n  computed: {\n    // ...mapState(['goods', 'totalNum', 'totalPrice']),\n    ...mapState({\n      goods: (state) => state.cart.goods, \n      totalNum: (state) => state.cart.totalNum,\n      totalPrice: (state) => state.cart.totalPrice,\n    }),\n    ...mapGetters([\"goodsObj\"]),\n    // ...mapState({\n    //   goods: state => state.goods\n    // }),\n    // goods() {\n    //   return this.$store.state.goods\n    // }\n    // goodsObj() {\n    //   return this.$store.getters.goodsObj\n    // }\n  },\n  methods: {\n    handleAdd({ index }) {\n      // console.log(\"%c [ index ]\", \"font-size:13px; background:#00ffff; color:red;\", index)\n      // this.$store.commit('add', index)\n      // this.$store.commit({\n      //   type: 'ADD',\n      //   index\n      // })\n      // this.$store.dispatch('increment', {\n      //   index\n      // })\n      this.$store.dispatch({\n        type: \"increment\",\n        index, \n      });\n    },\n    handleReduce({ index }) {\n      // this.$store.dispatch({\n      //   type: \"reduce\",\n      //   index,\n      // });\n      this.$store.commit({\n        type:'REDUCE',\n        index\n      })\n    },\n  },\n//   答：如果请求的数据是多个组件共享的，为了⽅便只写⼀份，就写vuex⾥⾯，如果是组件独⽤的就写在当前组件⾥⾯。\n// 如果请求来的数据不是要被其他组件公⽤，仅仅在请求的组件内使⽤，就不需要放⼊ vuex 的 state ⾥\n// 如果被其他地⽅复⽤，请将请求放⼊ action ⾥，⽅便复⽤，并包装成 promise 返回\n\n};\n</script>\n\n<!-- Add \"scoped\" attribute to limit CSS to this component only -->\n\n<style scoped>\nbody,\nli,\nul,\np {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.hello {\n  position: relative;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n}\n\n.shop_container {\n  width: 100%;\n}\n\n.shop_container_li {\n  background-color: #f5f5f5;\n  height: 130px;\n  margin-bottom: 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.shop_img {\n  flex: 1;\n  padding: 10px;\n  height: 110px;\n}\n\n.shop_detail {\n  flex: 2;\n}\n\n.shop_detail p {\n  font-size: 14px;\n  line-height: 25px;\n  height: 25px;\n}\n\n.shop_reduce,\n.shop_add {\n  font-size: 18px;\n  font-weight: 700;\n  display: inline-block;\n  text-align: center;\n  width: 20px;\n  height: 20px;\n  border: 1px solid #f5f5f5;\n  background-color: #ffffff;\n}\n\n.shop_num {\n  margin: 0 5px;\n}\n\n.foot {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 40px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.total_price {\n  background-color: #4cd964;\n  flex: 2;\n  height: 100%;\n  line-height: 40px;\n}\n\n.total_num {\n  flex: 1;\n  background-color: #666;\n  height: 100%;\n  line-height: 40px;\n}\n\n.payment {\n  background-color: #3b95e9;\n}\n</style>\n"]}]}