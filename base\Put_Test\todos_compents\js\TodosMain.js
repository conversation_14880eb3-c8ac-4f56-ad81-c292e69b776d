Vue.component('todos-main', {
	template: `
		<section class="main">
			<input id="toggle-all" class="toggle-all" type="checkbox">
			<label for="toggle-all">Mark all as complete</label>
			<ul class="todo-list">
				<li :class="{completed: item.done}" v-for="item in zList" :key="item.id">
					<div class="view">
						<input class="toggle" type="checkbox" v-model="item.done">
						<label>{{item.name}}</label>
						<button class="destroy" @click=del(item.id)></button>
					</div>
					<input class="edit" value="Create a TodoMVC template">
				</li>
			</ul>
		</section>
	`,
	// 第三步：子组件接收父组件传递过来的数据
	props: ['zList'],
	methods: {
		del(id) {
			this.$emit('z_del', id)
		}
	}
});
