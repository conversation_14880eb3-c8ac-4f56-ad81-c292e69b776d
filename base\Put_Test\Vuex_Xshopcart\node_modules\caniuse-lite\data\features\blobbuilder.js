module.exports={A:{A:{"1":"A B","2":"I F E D kB"},B:{"1":"C O P H J K L M V N WB KB"},C:{"1":"0 1 2 3 4 5 6 7 8 9 O P H J K L X Y Z a b c d e f g h i j k l m n o p q r s t u v w x Q z AB YB CB JB EB FB GB HB DB BB y T S LB MB NB OB PB QB IB SB M V N iB","2":"tB RB G W jB rB","36":"I F E D A B C"},D:{"1":"0 1 2 3 4 5 6 7 8 9 Y Z a b c d e f g h i j k l m n o p q r s t u v w x Q z AB YB CB JB EB FB GB HB DB BB y T S LB MB NB OB PB QB IB SB M V N WB KB TB uB aB bB","2":"G W I F","36":"E D A B C O P H J K L X"},E:{"1":"I F E D A B C O P fB gB hB VB R U lB mB","2":"G W cB UB eB"},F:{"1":"0 1 2 3 4 5 6 7 8 9 H J K L X Y Z a b c d e f g h i j k l m n o p q r s t u v w x Q z AB CB EB FB GB HB DB BB y T S U","2":"D B C nB oB pB qB R XB sB"},G:{"1":"E wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC","2":"UB TC ZB vB"},H:{"2":"CC"},I:{"1":"N","2":"DC EC FC","36":"RB G GC ZB HC IC"},J:{"1":"A","2":"F"},K:{"1":"Q U","2":"A B C R XB"},L:{"1":"TB"},M:{"1":"M"},N:{"1":"A B"},O:{"1":"JC"},P:{"1":"G KC LC MC NC OC VB PC QC"},Q:{"1":"RC"},R:{"1":"SC"},S:{"1":"dB"}},B:5,C:"Blob constructing"};
